<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>前端路由调试</title>
</head>
<body>
    <h1>前端路由调试页面</h1>
    <p>当前URL: <span id="currentUrl"></span></p>
    <p>路由参数: <span id="routeParams"></span></p>
    
    <h2>API测试</h2>
    <button onclick="testApi()">测试编辑历史API</button>
    <div id="apiResult"></div>
    
    <script>
        document.getElementById("currentUrl").textContent = window.location.href;
        document.getElementById("routeParams").textContent = window.location.search;

        function testApi() {
            const robotId = new URLSearchParams(window.location.search).get("robot_id") || 7;
            const apiUrl = `/adminapi/kb.robot_edit_log/lists?robot_id=${robotId}`;

            console.log("🔍 开始测试API:", apiUrl);

            fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
                .then(response => {
                    console.log("📡 API响应状态:", response.status);
                    console.log("📡 API响应头:", response.headers);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.log("✅ API响应数据:", data);
                    document.getElementById("apiResult").innerHTML =
                        "<h3>✅ API响应成功:</h3><pre>" + JSON.stringify(data, null, 2) + "</pre>";
                })
                .catch(error => {
                    console.error("❌ API请求失败:", error);
                    document.getElementById("apiResult").innerHTML =
                        "<h3>❌ API错误:</h3><pre>" + error.message + "</pre>";
                });
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            console.log("🚀 页面加载完成，开始自动测试");
            setTimeout(testApi, 1000);
        });

        // 检查Vue应用是否加载
        setTimeout(function() {
            if (window.Vue) {
                console.log("✅ Vue已加载");
            } else {
                console.log("❌ Vue未加载");
            }

            if (window.app) {
                console.log("✅ Vue应用实例存在");
            } else {
                console.log("❌ Vue应用实例不存在");
            }
        }, 2000);
    </script>
</body>
</html>