/**
 * UniApp移动端PC新功能移植测试验证脚本
 * 验证新增功能的正确性和兼容性
 */

console.log("=== UniApp移动端PC新功能移植测试验证 ===\n");

// 1. 检查文件是否存在
const fs = require('fs');
const path = require('path');

const targetFile = 'uniapp/src/packages/pages/robot_info/component/robot-setting/search-config.vue';

console.log("1. 文件存在性检查");
console.log("=====================================");

if (fs.existsSync(targetFile)) {
    console.log("✅ search-config.vue 文件存在");
} else {
    console.log("❌ search-config.vue 文件不存在");
    process.exit(1);
}

// 2. 检查新增功能的代码
console.log("\n2. 新增功能代码检查");
console.log("=====================================");

const fileContent = fs.readFileSync(targetFile, 'utf8');

// P0级功能检查
const p0Features = [
    {
        name: "多种检索模式选择",
        patterns: [
            'search_mode',
            'u-radio-group',
            'name="similar"',
            'name="full"',
            'name="mix"',
            '语义检索',
            '全文检索',
            '混合检索'
        ]
    },
    {
        name: "引用上限配置",
        patterns: [
            'search_tokens',
            'app-slider',
            ':min="100"',
            ':max="30000"',
            'tokens'
        ]
    }
];

// P1级功能检查
const p1Features = [
    {
        name: "问题优化功能",
        patterns: [
            'optimize_ask',
            'u-switch',
            'optimize_m_id',
            'optimize_s_id',
            '问题优化',
            'chatModels'
        ]
    }
];

// 检查P0级功能
console.log("P0级功能检查：");
p0Features.forEach(feature => {
    console.log(`\n检查功能：${feature.name}`);
    let allFound = true;
    feature.patterns.forEach(pattern => {
        if (fileContent.includes(pattern)) {
            console.log(`  ✅ ${pattern} - 找到`);
        } else {
            console.log(`  ❌ ${pattern} - 未找到`);
            allFound = false;
        }
    });
    console.log(`  ${allFound ? '✅' : '❌'} ${feature.name} - ${allFound ? '实现完成' : '实现不完整'}`);
});

// 检查P1级功能
console.log("\nP1级功能检查：");
p1Features.forEach(feature => {
    console.log(`\n检查功能：${feature.name}`);
    let allFound = true;
    feature.patterns.forEach(pattern => {
        if (fileContent.includes(pattern)) {
            console.log(`  ✅ ${pattern} - 找到`);
        } else {
            console.log(`  ❌ ${pattern} - 未找到`);
            allFound = false;
        }
    });
    console.log(`  ${allFound ? '✅' : '❌'} ${feature.name} - ${allFound ? '实现完成' : '实现不完整'}`);
});

// 3. 检查默认值设置
console.log("\n3. 默认值设置检查");
console.log("=====================================");

const defaultValueChecks = [
    { field: 'search_mode', defaultValue: 'similar', description: '默认语义检索' },
    { field: 'search_tokens', defaultValue: '8000', description: '默认8000 tokens' },
    { field: 'optimize_ask', defaultValue: '0', description: '默认关闭问题优化' }
];

defaultValueChecks.forEach(check => {
    if (fileContent.includes(`${check.field}`) && fileContent.includes(`${check.defaultValue}`)) {
        console.log(`✅ ${check.field} - ${check.description}`);
    } else {
        console.log(`⚠️ ${check.field} - 可能缺少默认值设置`);
    }
});

// 4. 检查UI组件使用
console.log("\n4. UI组件使用检查");
console.log("=====================================");

const uiComponents = [
    { name: 'u-radio-group', description: '单选组件组' },
    { name: 'u-radio', description: '单选组件' },
    { name: 'app-slider', description: '滑块组件' },
    { name: 'u-switch', description: '开关组件' },
    { name: 'model-picker', description: '模型选择器' },
    { name: 'u-form-item', description: '表单项' }
];

uiComponents.forEach(component => {
    if (fileContent.includes(component.name)) {
        console.log(`✅ ${component.name} - ${component.description} 已使用`);
    } else {
        console.log(`❌ ${component.name} - ${component.description} 未使用`);
    }
});

// 5. 检查样式优化
console.log("\n5. 样式优化检查");
console.log("=====================================");

const styleChecks = [
    'text-muted',
    ':deep(.u-radio-group)',
    ':deep(.app-slider)',
    ':deep(.u-form-item)',
    'margin-bottom',
    'font-size',
    'border-radius'
];

let styleCount = 0;
styleChecks.forEach(style => {
    if (fileContent.includes(style)) {
        console.log(`✅ ${style} - 样式已添加`);
        styleCount++;
    }
});

console.log(`\n样式优化完成度: ${styleCount}/${styleChecks.length} (${Math.round(styleCount/styleChecks.length*100)}%)`);

// 6. 检查数据结构兼容性
console.log("\n6. 数据结构兼容性检查");
console.log("=====================================");

const dataFields = [
    'search_similarity', // 原有字段
    'search_limits', // 原有字段
    'search_mode', // 新增字段
    'search_tokens', // 新增字段
    'optimize_ask', // 新增字段
    'optimize_m_id', // 新增字段
    'optimize_s_id' // 新增字段
];

console.log("数据字段检查：");
dataFields.forEach(field => {
    if (fileContent.includes(field)) {
        console.log(`✅ ${field} - 字段已定义`);
    } else {
        console.log(`❌ ${field} - 字段缺失`);
    }
});

// 7. 生成测试报告
console.log("\n7. 测试报告生成");
console.log("=====================================");

const reportData = {
    timestamp: new Date().toISOString(),
    file: targetFile,
    p0Features: p0Features.length,
    p1Features: p1Features.length,
    uiComponents: uiComponents.length,
    styleOptimizations: styleCount,
    dataFields: dataFields.length
};

console.log("测试报告：");
console.log(`- 测试时间: ${reportData.timestamp}`);
console.log(`- 测试文件: ${reportData.file}`);
console.log(`- P0级功能: ${reportData.p0Features}个`);
console.log(`- P1级功能: ${reportData.p1Features}个`);
console.log(`- UI组件: ${reportData.uiComponents}个`);
console.log(`- 样式优化: ${reportData.styleOptimizations}项`);
console.log(`- 数据字段: ${reportData.dataFields}个`);

// 8. 总体评估
console.log("\n8. 总体评估");
console.log("=====================================");

let totalScore = 0;
let maxScore = 0;

// P0功能评分 (40分)
let p0Score = 0;
p0Features.forEach(feature => {
    let featureScore = 0;
    feature.patterns.forEach(pattern => {
        if (fileContent.includes(pattern)) featureScore++;
    });
    p0Score += (featureScore / feature.patterns.length) * 20;
});
totalScore += p0Score;
maxScore += 40;

// P1功能评分 (20分)
let p1Score = 0;
p1Features.forEach(feature => {
    let featureScore = 0;
    feature.patterns.forEach(pattern => {
        if (fileContent.includes(pattern)) featureScore++;
    });
    p1Score += (featureScore / feature.patterns.length) * 20;
});
totalScore += p1Score;
maxScore += 20;

// UI组件评分 (20分)
let uiScore = uiComponents.filter(comp => fileContent.includes(comp.name)).length;
totalScore += (uiScore / uiComponents.length) * 20;
maxScore += 20;

// 样式优化评分 (20分)
totalScore += (styleCount / styleChecks.length) * 20;
maxScore += 20;

const finalScore = Math.round((totalScore / maxScore) * 100);

console.log(`总体评分: ${finalScore}/100`);
console.log(`实现质量: ${finalScore >= 90 ? '优秀' : finalScore >= 80 ? '良好' : finalScore >= 70 ? '合格' : '需要改进'}`);

if (finalScore >= 80) {
    console.log("🎉 功能移植成功！");
} else {
    console.log("⚠️ 功能移植需要进一步完善");
}

console.log("\n=== 测试验证完成 ===");
