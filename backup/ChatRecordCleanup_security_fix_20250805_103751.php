<?php
// +----------------------------------------------------------------------
// | 对话记录清理定时任务
// +----------------------------------------------------------------------

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\common\model\chat\ChatRecord;
use app\common\model\kb\KbRobotRecord;
use app\common\service\ConfigService;
use Exception;

/**
 * AI对话和智能体对话记录清理定时任务
 * 专门清理半年前的对话记录，不影响其他功能
 */
class ChatRecordCleanup extends Command
{
    // 清理配置
    private array $chatTables = [
        'cm_chat_record' => 'AI对话记录',
        'cm_kb_robot_record' => '智能体对话记录'
    ];
    
    // 默认保留180天（半年）
    private int $defaultRetentionDays = 180;
    
    // 日志文件路径
    private string $logFile = '';
    
    // 日志文件最大大小 (5MB)
    private int $maxLogSize = 5 * 1024 * 1024;

    protected function configure(): void
    {
        $this->setName('chat:cleanup')
            ->addOption('days', 'd', \think\console\input\Option::VALUE_OPTIONAL, '保留天数', 180)
            ->addOption('dry-run', null, \think\console\input\Option::VALUE_NONE, '预览模式，不实际删除')
            ->setDescription('AI对话记录清理命令');
    }

    /**
     * 执行清理任务
     */
    protected function execute(Input $input, Output $output)
    {
        // 初始化日志文件和轮转机制
        $this->initLogFile();
        
        $days = (int)$input->getOption('days');
        $isDryRun = $input->getOption('dry-run');
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $output->writeln("=== AI对话记录清理任务开始 ===");
        $output->writeln("保留期限: {$days}天");
        
        if ($isDryRun) {
            $output->writeln("【预览模式】- 仅显示统计信息，不实际删除数据");
        }
        
        try {
            // 清理AI对话记录
            if ($isDryRun) {
                $chatCount = Db::name('chat_record')
                    ->where('create_time', '<', $cutoffDate)
                    ->where('delete_time', 0)
                    ->count();
            } else {
                $chatCount = Db::name('chat_record')
                    ->where('create_time', '<', $cutoffDate)
                    ->where('delete_time', 0)
                    ->update([
                        'delete_time' => time(),
                        'is_show' => 0
                    ]);
            }
            
            // 清理智能体对话记录  
            if ($isDryRun) {
                $kbCount = Db::name('kb_robot_record')
                    ->where('create_time', '<', $cutoffDate)
                    ->where('delete_time', 0)
                    ->count();
            } else {
                $kbCount = Db::name('kb_robot_record')
                    ->where('create_time', '<', $cutoffDate)
                    ->where('delete_time', 0)
                    ->update([
                        'delete_time' => time(),
                        'is_show' => 0
                    ]);
            }
            
            $output->writeln("📋 AI对话记录: {$chatCount} 条");
            $output->writeln("📋 智能体对话记录: {$kbCount} 条");
            
            if ($isDryRun) {
                $output->writeln("✨ 预览完成，使用 --dry-run=false 执行实际清理");
            } else {
                $output->writeln("✨ 清理完成");
            }
            
        } catch (\Exception $e) {
            $output->writeln("❌ 清理失败: " . $e->getMessage());
        }
        
        $output->writeln("=== 清理任务完成 ===");
    }

    /**
     * 清理指定对话表的数据
     */
    private function cleanupChatTable(string $tableName, int $cutoffTimestamp, int $batchSize, bool $dryRun, Output $output): int
    {
        // 检查表是否存在
        if (!$this->tableExists($tableName)) {
            $output->writeln("<comment>⚠️  表 {$tableName} 不存在，跳过清理</comment>");
            return 0;
        }
        
        $totalCleaned = 0;
        
        // 首先获取总数
        $countSql = "SELECT COUNT(*) as total FROM `{$tableName}` 
                     WHERE create_time < {$cutoffTimestamp} 
                     AND (delete_time IS NULL OR delete_time = 0)";
        
        $result = Db::query($countSql);
        $totalCount = $result[0]['total'] ?? 0;
        
        if ($totalCount == 0) {
            $output->writeln("<comment>  ✅ 没有需要清理的记录</comment>");
            return 0;
        }
        
        if ($dryRun) {
            $output->writeln("<comment>  📊 发现 {$totalCount} 条可清理记录</comment>");
            return $totalCount;
        }
        
        $output->writeln("<info>  📊 发现 {$totalCount} 条记录需要清理，开始分批处理...</info>");
        
        // 分批清理
        $processed = 0;
        while ($processed < $totalCount) {
            $currentBatchSize = min($batchSize, $totalCount - $processed);
            
            // 执行批量软删除
            $updateSql = "UPDATE `{$tableName}` 
                         SET delete_time = " . time() . ", is_show = 0 
                         WHERE create_time < {$cutoffTimestamp} 
                         AND (delete_time IS NULL OR delete_time = 0)
                         LIMIT {$currentBatchSize}";
            
            $affected = Db::execute($updateSql);
            $processed += $affected;
            $totalCleaned += $affected;
            
            $output->writeln("<info>  ⏳ 已处理 {$processed}/{$totalCount} 条记录</info>");
            
            if ($affected == 0) {
                break; // 没有更多记录可处理
            }
            
            // 休息一下，避免给数据库造成压力
            usleep(100000); // 0.1秒
        }
        
        $output->writeln("<info>  ✅ 完成清理，共处理 {$totalCleaned} 条记录</info>");
        
        return $totalCleaned;
    }

    /**
     * 清理孤立的对话收藏记录
     */
    private function cleanupOrphanedCollections(bool $dryRun, Output $output): int
    {
        $output->writeln("<info>🔗 检查孤立的收藏记录</info>");
        
        $collectTables = [
            'cm_chat_record_collect' => 'cm_chat_record',
            'cm_kb_robot_record_collect' => 'cm_kb_robot_record'
        ];
        
        $totalCleaned = 0;
        
        foreach ($collectTables as $collectTable => $mainTable) {
            if (!$this->tableExists($collectTable) || !$this->tableExists($mainTable)) {
                continue;
            }
            
            if ($dryRun) {
                $sql = "SELECT COUNT(*) as count FROM `{$collectTable}` c
                       LEFT JOIN `{$mainTable}` m ON c.records_id = m.id
                       WHERE m.id IS NULL OR m.delete_time > 0";
                
                $result = Db::query($sql);
                $count = $result[0]['count'] ?? 0;
                
                if ($count > 0) {
                    $output->writeln("<comment>  📊 {$collectTable}: 发现 {$count} 条孤立收藏记录</comment>");
                    $totalCleaned += $count;
                }
            } else {
                $sql = "DELETE c FROM `{$collectTable}` c
                       LEFT JOIN `{$mainTable}` m ON c.records_id = m.id
                       WHERE m.id IS NULL OR m.delete_time > 0";
                
                $affected = Db::execute($sql);
                if ($affected > 0) {
                    $output->writeln("<info>  ✅ {$collectTable}: 清理了 {$affected} 条孤立收藏记录</info>");
                    $totalCleaned += $affected;
                }
            }
        }
        
        if ($totalCleaned == 0) {
            $output->writeln("<comment>  ✅ 没有发现孤立的收藏记录</comment>");
        }
        
        return $totalCleaned;
    }

    /**
     * 检查表是否存在
     */
    private function tableExists(string $tableName): bool
    {
        try {
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 输出清理总结
     */
    private function outputSummary(array $results, int $total, int $days, bool $dryRun, Output $output): void
    {
        $output->writeln("<info>=== 清理总结 ===</info>");
        
        if (empty($results) && $total == 0) {
            $output->writeln("<comment>✨ 没有发现需要清理的对话记录</comment>");
            return;
        }
        
        foreach ($results as $table => $info) {
            $output->writeln("<info>📋 {$info['description']}: {$info['cleaned_count']} 条记录</info>");
        }
        
        $action = $dryRun ? '发现' : '清理';
        $output->writeln("<info>🎯 总计{$action}: {$total} 条对话记录（保留{$days}天内）</info>");
        
        if ($dryRun) {
            $output->writeln("<comment>💡 要执行实际清理，请移除 --dry-run 参数</comment>");
        } else {
            $output->writeln("<info>💾 已释放数据库存储空间，提升查询性能</info>");
        }
    }

    /**
     * 建议数据库优化
     */
    private function suggestDatabaseOptimization(Output $output): void
    {
        $output->writeln("");
        $output->writeln("<comment>💡 建议执行数据库优化以回收存储空间：</comment>");
        $output->writeln("<comment>   OPTIMIZE TABLE cm_chat_record, cm_kb_robot_record;</comment>");
    }

    /**
     * 记录清理结果到日志文件
     */
    private function logCleanupResult(int $days, array $results, int $total): void
    {
        $logData = [
            'cleanup_date' => date('Y-m-d H:i:s'),
            'retention_days' => $days,
            'cleanup_results' => $results,
            'total_cleaned' => $total,
            'execution_type' => 'chat_record_cleanup'
        ];
        
        $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 记录错误日志
     */
    private function logError(Exception $e): void
    {
        $errorData = [
            'error_date' => date('Y-m-d H:i:s'),
            'error_message' => $e->getMessage(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'execution_type' => 'chat_record_cleanup_error'
        ];
        
        $logContent = json_encode($errorData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 初始化日志文件
     */
    private function initLogFile(): void
    {
        $logDir = runtime_path('log');
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFile = $logDir . '/chat_cleanup.log';
        
        // 检查日志文件大小，如果超过限制则轮转
        $this->rotateLogIfNeeded();
    }

    /**
     * 日志轮转机制
     */
    private function rotateLogIfNeeded(): void
    {
        if (!file_exists($this->logFile)) {
            return;
        }
        
        $fileSize = filesize($this->logFile);
        
        // 如果文件大小超过限制，进行轮转
        if ($fileSize > $this->maxLogSize) {
            $backupFile = $this->logFile . '.' . date('Y-m-d_H-i-s');
            
            // 备份当前日志文件
            if (rename($this->logFile, $backupFile)) {
                // 压缩旧日志文件（如果系统支持gzip）
                if (function_exists('gzencode') && is_writable(dirname($backupFile))) {
                    $content = file_get_contents($backupFile);
                    $gzipFile = $backupFile . '.gz';
                    
                    if (file_put_contents($gzipFile, gzencode($content))) {
                        unlink($backupFile); // 删除未压缩的备份文件
                    }
                }
                
                // 清理旧的备份文件（保留最近10个）
                $this->cleanOldLogBackups();
            }
        }
    }

    /**
     * 清理旧的日志备份文件
     */
    private function cleanOldLogBackups(): void
    {
        $logDir = dirname($this->logFile);
        $logBaseName = basename($this->logFile);
        
        // 查找所有备份文件
        $backupFiles = glob($logDir . '/' . $logBaseName . '.*');
        
        if (count($backupFiles) > 10) {
            // 按修改时间排序
            usort($backupFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的文件，只保留最新的10个
            $filesToDelete = array_slice($backupFiles, 0, count($backupFiles) - 10);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }

    /**
     * 写入日志
     */
    private function writeLog(string $message): void
    {
        $logEntry = '[' . date('Y-m-d H:i:s') . '] ' . $message . PHP_EOL;
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
} 