<?php

namespace app\adminapi\controller\kb;

use app\adminapi\controller\BaseAdminController;
use app\adminapi\logic\kb\KbRobotEditLogLogic;
use app\adminapi\validate\IDMustValidate;
use think\response\Json;

/**
 * 智能体编辑日志管理控制器
 */
class RobotEditLogController extends BaseAdminController
{
    /**
     * @notes 编辑日志列表
     * @return Json
     * <AUTHOR> Assistant
     */
    public function lists(): <PERSON><PERSON>
    {
        $params = $this->request->get();
        $result = KbRobotEditLogLogic::lists($params);
        return $this->data($result);
    }
    
    /**
     * @notes 编辑日志详情
     * @return Json
     * <AUTHOR> Assistant
     */
    public function detail(): Json
    {
        (new IDMustValidate())->goCheck();
        $id = intval($this->request->get('id'));
        
        $result = KbRobotEditLogLogic::detail($id);
        return $this->data($result);
    }
    
    /**
     * @notes 编辑日志统计
     * @return Json
     * <AUTHOR> Assistant
     */
    public function statistics(): Json
    {
        $params = $this->request->get();
        $result = KbRobotEditLogLogic::statistics($params);
        return $this->data($result);
    }
}
