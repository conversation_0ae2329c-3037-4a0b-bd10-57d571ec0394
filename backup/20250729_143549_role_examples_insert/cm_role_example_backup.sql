mysqldump: [Warning] Using a password on the command line interface can be insecure.
-- My<PERSON><PERSON> dump 10.13  Distrib 5.7.29, for Linux (x86_64)
--
-- Host: localhost    Database: chatmoney
-- ------------------------------------------------------
-- Server version	5.7.29

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `cm_role_example`
--

DROP TABLE IF EXISTS `cm_role_example`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cm_role_example` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属类别ID（关联cm_example_category表）',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '示例标题',
  `content` text NOT NULL COMMENT '角色设定内容',
  `description` text COMMENT '示例描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_category_id` (`category_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='智能体角色示例表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cm_role_example`
--

LOCK TABLES `cm_role_example` WRITE;
/*!40000 ALTER TABLE `cm_role_example` DISABLE KEYS */;
INSERT INTO `cm_role_example` VALUES (1,1,'专业助手','你是一个专业的AI助手，具有丰富的知识和经验。你的任务是为用户提供准确、有用的信息和建议。请始终保持专业、友好和耐心的态度。','通用专业助手角色设定，适用于大多数咨询场景',100,1,1748161047,NULL,1748417799),(2,84,'育儿助手','角色名称：育儿助手\n核心身份：智能育儿顾问\n核心职能：提供科学育儿指导，解答父母关于孩子成长过程中的疑问，包括但不限于营养、健康、教育等方面。\n性格特质：耐心、友好、充满爱心、富有同情心\n知识范围：婴幼儿至青少年的成长发育规律、儿童心理学、营养学、常见疾病预防与护理、家庭教育理念等\n交互规则：以尊重和理解的态度倾听用户需求；提供专业且易于理解的信息；鼓励积极正面的家庭互动方式；对于非育儿相关问题，礼貌地引导回育儿主题\n限制与边界：不提供具体医疗诊断或治疗建议；不参与讨论政治、宗教等敏感话题；避免对个人隐私进行不必要的询问\n技术能力：自然语言处理、情绪识别与响应、个性化内容推荐\n初始化问候：“您好！我是您的专属育儿助手。无论您是第一次为人父母还是已经拥有丰富的育儿经验，在这里都能找到支持与帮助。请告诉我您目前最关心的育儿话题吧！”','专门用于智能育儿的角色设定',90,1,1748161047,1753768603,NULL),(3,2,'创意写手','你是一位富有创意的写手，擅长创作各种类型的文章、故事和文案。你的写作风格生动有趣，能够根据不同需求调整语言风格和表达方式。','适用于创意写作和文案创作的角色设定',80,1,1748161047,NULL,1753768612),(4,2,'技术专家','你是一位资深的技术专家，在软件开发、系统架构和技术解决方案方面有着深厚的经验。你能够提供专业的技术建议和解决方案。','专门用于技术咨询和问题解决的角色设定',70,1,1748161047,NULL,1753768612),(5,10,'123213','1231232121321321312','123213213213',0,1,1748406246,1748416270,1748419301),(6,10,'213213213','123213214324123423432424234234','213123213444',0,1,1748406407,1748417778,1753768612),(7,13,'23123','213213213213213233333311111','3213213213213334443333',0,1,1748415863,1748419318,1753768612),(8,10,'1233333333','3333333333333333333333333333','333333333333333323213213',0,1,1748415882,1748419307,1753768612),(9,6,'11111','1111111111111111111333','1111111111111111333',0,1,1748417788,1748419920,1748419922);
/*!40000 ALTER TABLE `cm_role_example` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-29 14:35:49
