<template>
    <popup
        ref="popupRef"
        :title="`${robotName} - 编辑历史详情`"
        width="1200px"
        :show-footer="false"
        @close="$emit('close')"
    >
        <div v-loading="loading">
            <!-- 统计信息 -->
            <div class="statistics-bar mb-4">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-statistic title="总编辑次数" :value="statistics.total_count" />
                    </el-col>
                    <el-col :span="6">
                        <el-statistic title="智能体编辑" :value="statistics.robot_edit_count" />
                    </el-col>
                    <el-col :span="6">
                        <el-statistic title="知识库编辑" :value="statistics.knowledge_edit_count" />
                    </el-col>
                    <el-col :span="6">
                        <el-statistic title="自动下架次数" :value="statistics.auto_offline_count" />
                    </el-col>
                </el-row>
            </div>

            <!-- 筛选条件 -->
            <div class="filter-bar mb-4">
                <el-form :model="queryParams" inline>
                    <el-form-item label="编辑类型">
                        <el-select v-model="queryParams.edit_type" placeholder="全部类型" clearable>
                            <el-option label="智能体编辑" :value="1" />
                            <el-option label="知识库编辑触发" :value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否自动下架">
                        <el-select v-model="queryParams.is_auto_offline" placeholder="全部" clearable>
                            <el-option label="是" :value="1" />
                            <el-option label="否" :value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getLists">查询</el-button>
                        <el-button @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 编辑历史列表 -->
            <div class="history-list">
                <el-table :data="lists" stripe>
                    <el-table-column label="编辑时间" prop="create_time_text" width="180" />
                    <el-table-column label="编辑类型" width="120">
                        <template #default="{ row }">
                            <el-tag :type="row.edit_type === 1 ? 'primary' : 'success'">
                                {{ row.edit_type_text }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作用户" width="150">
                        <template #default="{ row }">
                            <div class="flex items-center">
                                <el-avatar :src="row.user_avatar" :size="24" class="mr-2">
                                    <el-icon><User /></el-icon>
                                </el-avatar>
                                <span>{{ row.user_nickname || row.user_account }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否自动下架" width="120" align="center">
                        <template #default="{ row }">
                            <el-tag :type="row.is_auto_offline ? 'danger' : 'success'">
                                {{ row.is_auto_offline_text }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="下架原因" prop="offline_reason" min-width="200">
                        <template #default="{ row }">
                            <span v-if="row.offline_reason" class="text-red-500">
                                {{ row.offline_reason }}
                            </span>
                            <span v-else class="text-gray-400">-</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" fixed="right">
                        <template #default="{ row }">
                            <el-button
                                type="primary"
                                link
                                @click="viewDetail(row)"
                            >
                                查看详情
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 分页 -->
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </div>

        <!-- 详情弹窗 -->
        <edit-detail-popup
            ref="editDetailPopupRef"
            @close="editDetailPopupRef?.close()"
        />
    </popup>
</template>

<script lang="ts" setup>
import { User } from '@element-plus/icons-vue'
import { ElStatistic } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import Pagination from '@/components/pagination/index.vue'
import EditDetailPopup from './edit-detail-popup.vue'
import { getRobotEditLogLists, getRobotEditLogStatistics } from '@/api/knowledge_base/robot_edit_log'

const emit = defineEmits(['close'])
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const editDetailPopupRef = shallowRef<InstanceType<typeof EditDetailPopup>>()

const loading = ref(false)
const robotId = ref<number>(0)
const robotName = ref<string>('')

// 查询参数
const queryParams = reactive({
    edit_type: '',
    is_auto_offline: ''
})

// 分页参数
const pager = reactive({
    page_no: 1,
    page_size: 15,
    count: 0
})

// 列表数据
const lists = ref<any[]>([])

// 统计数据
const statistics = ref({
    total_count: 0,
    robot_edit_count: 0,
    knowledge_edit_count: 0,
    auto_offline_count: 0
})

// 获取列表数据
const getLists = async () => {
    if (!robotId.value) return
    
    try {
        loading.value = true
        console.log('🔍 [编辑历史弹窗] 获取编辑历史列表')
        
        const params = {
            robot_id: robotId.value,
            page_no: pager.page_no,
            page_size: pager.page_size,
            ...queryParams
        }
        
        const response = await getRobotEditLogLists(params)
        
        lists.value = response.lists || []
        pager.count = response.count || 0
        
        console.log('📊 [编辑历史弹窗] 列表数据:', lists.value)
    } catch (error) {
        console.error('❌ [编辑历史弹窗] 获取列表失败:', error)
    } finally {
        loading.value = false
    }
}

// 获取统计数据
const getStatistics = async () => {
    if (!robotId.value) return
    
    try {
        const response = await getRobotEditLogStatistics({
            robot_id: robotId.value
        })
        
        statistics.value = response || {
            total_count: 0,
            robot_edit_count: 0,
            knowledge_edit_count: 0,
            auto_offline_count: 0
        }
        
        console.log('📈 [编辑历史弹窗] 统计数据:', statistics.value)
    } catch (error) {
        console.error('❌ [编辑历史弹窗] 获取统计失败:', error)
    }
}

// 重置查询
const resetQuery = () => {
    queryParams.edit_type = ''
    queryParams.is_auto_offline = ''
    pager.page_no = 1
    getLists()
}

// 查看详情
const viewDetail = (row: any) => {
    editDetailPopupRef.value?.open(row.id)
}

// 打开弹窗
const open = (id: number, name: string = '') => {
    robotId.value = id
    robotName.value = name || `智能体 ${id}`
    
    // 重置参数
    queryParams.edit_type = ''
    queryParams.is_auto_offline = ''
    pager.page_no = 1
    
    popupRef.value?.open()
    
    // 获取数据
    getLists()
    getStatistics()
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.statistics-bar {
    .el-statistic {
        text-align: center;
        
        :deep(.el-statistic__content) {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
        }
    }
}

.filter-bar {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.history-list {
    .el-table {
        border: 1px solid #e4e7ed;
    }
}
</style>
