<template>
    <div>
        <popup
            ref="popupRef"
            title="编辑日志详情"
            width="800px"
            :show-footer="false"
            @close="$emit('close')"
        >
            <div v-loading="loading">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="日志ID">
                        {{ detail.id }}
                    </el-descriptions-item>
                    <el-descriptions-item label="编辑时间">
                        {{ detail.create_time_text }}
                    </el-descriptions-item>
                    <el-descriptions-item label="智能体">
                        <div class="flex items-center">
                            <el-avatar
                                v-if="detail.robot_image"
                                :src="detail.robot_image"
                                :size="32"
                                class="mr-2"
                            />
                            <div>
                                <div class="font-medium">{{ detail.robot_name || '已删除' }}</div>
                                <div class="text-xs text-gray-500">ID: {{ detail.robot_id }}</div>
                            </div>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="操作用户">
                        <div class="flex items-center">
                            <el-avatar
                                v-if="detail.user_avatar"
                                :src="detail.user_avatar"
                                :size="32"
                                class="mr-2"
                            />
                            <div>
                                <div class="font-medium">{{ detail.user_nickname || detail.user_account }}</div>
                                <div class="text-xs text-gray-500">{{ detail.user_account }}</div>
                            </div>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="编辑类型">
                        <el-tag>{{ detail.edit_type_text }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="是否自动下架">
                        <el-tag :type="detail.is_auto_offline ? 'danger' : 'success'">
                            {{ detail.is_auto_offline_text }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="下架原因" :span="2">
                        {{ detail.offline_reason || '无' }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 编辑前后数据对比 -->
                <div class="mt-6">
                    <h3 class="text-lg font-medium mb-4">编辑数据对比</h3>
                    
                    <el-row :gutter="20">
                        <!-- 编辑前数据 -->
                        <el-col :span="12">
                            <el-card shadow="never" class="h-full">
                                <template #header>
                                    <div class="card-header">
                                        <span>编辑前数据</span>
                                    </div>
                                </template>
                                <div v-if="detail.before_data">
                                    <pre class="data-content">{{ formatJsonData(detail.before_data) }}</pre>
                                </div>
                                <div v-else class="text-gray-500 text-center py-8">
                                    无编辑前数据
                                </div>
                            </el-card>
                        </el-col>
                        
                        <!-- 编辑后数据 -->
                        <el-col :span="12">
                            <el-card shadow="never" class="h-full">
                                <template #header>
                                    <div class="card-header">
                                        <span>编辑后数据</span>
                                    </div>
                                </template>
                                <div v-if="detail.after_data">
                                    <pre class="data-content">{{ formatJsonData(detail.after_data) }}</pre>
                                </div>
                                <div v-else class="text-gray-500 text-center py-8">
                                    无编辑后数据
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>

                <!-- 智能体简介 -->
                <div v-if="detail.robot_intro" class="mt-6">
                    <h3 class="text-lg font-medium mb-4">智能体简介</h3>
                    <el-card shadow="never">
                        <p class="text-gray-700 leading-relaxed">{{ detail.robot_intro }}</p>
                    </el-card>
                </div>
            </div>
        </popup>
    </div>
</template>

<script lang="ts" setup>
import Popup from '@/components/popup/index.vue'
import { getRobotEditLogDetail } from '@/api/knowledge_base/robot_edit_log'

const emit = defineEmits(['success', 'close'])

const popupRef = shallowRef<InstanceType<typeof Popup>>()
const loading = ref(false)

const detail = ref<any>({})

// 格式化JSON数据显示
const formatJsonData = (data: any) => {
    if (!data) return ''
    try {
        return JSON.stringify(data, null, 2)
    } catch (error) {
        return String(data)
    }
}

// 获取详情
const getDetail = async (id: number) => {
    try {
        loading.value = true
        const data = await getRobotEditLogDetail({ id })
        detail.value = data
    } catch (error) {
        console.error('获取详情失败:', error)
    } finally {
        loading.value = false
    }
}

// 打开弹窗
const open = (row: any) => {
    detail.value = {}
    popupRef.value?.open()
    getDetail(row.id)
}

// 关闭弹窗
const close = () => {
    popupRef.value?.close()
}

defineExpose({
    open,
    close
})
</script>

<style lang="scss" scoped>
.data-content {
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px;
    font-size: 12px;
    line-height: 1.5;
    color: #606266;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 300px;
    overflow-y: auto;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
