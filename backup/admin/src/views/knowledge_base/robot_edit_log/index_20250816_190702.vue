<template>
    <div>
        <el-card class="!border-none" shadow="never">
            <div class="flex justify-between">
                <div>
                    <el-button type="primary" @click="resetPage">
                        <template #icon>
                            <icon name="el-icon-Refresh" />
                        </template>
                        刷新
                    </el-button>
                </div>
                <div>
                    <el-button @click="showStatistics = !showStatistics">
                        {{ showStatistics ? '隐藏统计' : '显示统计' }}
                    </el-button>
                </div>
            </div>
        </el-card>

        <!-- 统计面板 -->
        <el-card v-if="showStatistics" class="!border-none mt-4" shadow="never">
            <template #header>
                <div class="card-header">
                    <span>编辑日志统计</span>
                </div>
            </template>
            <el-row :gutter="20">
                <el-col :span="6">
                    <div class="stat-item">
                        <div class="stat-value">{{ statistics.total_edits }}</div>
                        <div class="stat-label">总编辑次数</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-item">
                        <div class="stat-value">{{ statistics.auto_offline_count }}</div>
                        <div class="stat-label">自动下架次数</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-item">
                        <div class="stat-value">{{ statistics.today_edits }}</div>
                        <div class="stat-label">今日编辑</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-item">
                        <div class="stat-value">{{ statistics.week_edits }}</div>
                        <div class="stat-label">本周编辑</div>
                    </div>
                </el-col>
            </el-row>
        </el-card>

        <el-card class="!border-none mt-4" shadow="never">
            <div class="mb-4">
                <el-form :model="queryParams" :inline="true">
                    <el-form-item label="智能体名称">
                        <el-input
                            v-model="queryParams.robot_keyword"
                            placeholder="请输入智能体名称"
                            clearable
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="用户">
                        <el-input
                            v-model="queryParams.user_keyword"
                            placeholder="请输入用户昵称/账号"
                            clearable
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="编辑类型">
                        <el-select v-model="queryParams.edit_type" placeholder="请选择" clearable>
                            <el-option label="智能体编辑" :value="1" />
                            <el-option label="知识库编辑触发" :value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否自动下架">
                        <el-select v-model="queryParams.is_auto_offline" placeholder="请选择" clearable>
                            <el-option label="是" :value="1" />
                            <el-option label="否" :value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="时间范围">
                        <el-date-picker
                            v-model="dateRange"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            @change="handleDateChange"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="resetPage">查询</el-button>
                        <el-button @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <div>
                <el-table
                    v-loading="pager.loading"
                    :data="pager.lists"
                    size="large"
                >
                    <el-table-column label="ID" prop="id" width="80" />
                    <el-table-column label="智能体" min-width="200">
                        <template #default="{ row }">
                            <div class="flex items-center">
                                <el-avatar
                                    v-if="row.robot_image"
                                    :src="row.robot_image"
                                    :size="40"
                                    class="mr-2"
                                />
                                <div>
                                    <div class="font-medium">{{ row.robot_name || '已删除' }}</div>
                                    <div class="text-xs text-gray-500">ID: {{ row.robot_id }}</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作用户" min-width="150">
                        <template #default="{ row }">
                            <div class="flex items-center">
                                <el-avatar
                                    v-if="row.user_avatar"
                                    :src="row.user_avatar"
                                    :size="32"
                                    class="mr-2"
                                />
                                <div>
                                    <div class="font-medium">{{ row.user_nickname || row.user_account }}</div>
                                    <div class="text-xs text-gray-500">{{ row.user_account }}</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="编辑类型" prop="edit_type_text" width="120" />
                    <el-table-column label="自动下架" width="100">
                        <template #default="{ row }">
                            <el-tag :type="row.is_auto_offline ? 'danger' : 'success'">
                                {{ row.is_auto_offline_text }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="下架原因" prop="offline_reason" min-width="150" />
                    <el-table-column label="编辑时间" prop="create_time_text" width="160" />
                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
                            <el-button
                                type="primary"
                                link
                                @click="handleDetail(row)"
                            >
                                查看详情
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>

        <!-- 详情弹窗 -->
        <detail-popup
            ref="detailPopupRef"
            @success="getLists"
            @close="getLists"
        />
    </div>
</template>

<script lang="ts" setup>
import { getRobotEditLogLists, getRobotEditLogStatistics } from '@/api/knowledge_base/robot_edit_log'
import DetailPopup from './detail.vue'

defineOptions({
    name: 'RobotEditLogIndex'
})

const detailPopupRef = ref<InstanceType<typeof DetailPopup>>()
const showStatistics = ref(false)

// 查询参数
const queryParams = reactive({
    robot_keyword: '',
    user_keyword: '',
    edit_type: '',
    is_auto_offline: '',
    start_time: '',
    end_time: ''
})

const dateRange = ref<string[]>([])

// 分页数据
const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: getRobotEditLogLists,
    params: queryParams
})

// 统计数据
const statistics = ref({
    total_edits: 0,
    auto_offline_count: 0,
    today_edits: 0,
    week_edits: 0,
    edit_type_stats: []
})

// 获取统计数据
const getStatistics = async () => {
    try {
        const data = await getRobotEditLogStatistics()
        statistics.value = data
    } catch (error) {
        console.error('获取统计数据失败:', error)
    }
}

// 处理时间范围变化
const handleDateChange = (dates: string[]) => {
    if (dates && dates.length === 2) {
        queryParams.start_time = dates[0]
        queryParams.end_time = dates[1]
    } else {
        queryParams.start_time = ''
        queryParams.end_time = ''
    }
}

// 重置查询
const resetQuery = () => {
    Object.assign(queryParams, {
        robot_keyword: '',
        user_keyword: '',
        edit_type: '',
        is_auto_offline: '',
        start_time: '',
        end_time: ''
    })
    dateRange.value = []
    resetPage()
}

// 查看详情
const handleDetail = (row: any) => {
    detailPopupRef.value?.open(row)
}

onMounted(() => {
    getLists()
    getStatistics()
})

watch(showStatistics, (val) => {
    if (val) {
        getStatistics()
    }
})
</script>

<style lang="scss" scoped>
.stat-item {
    text-align: center;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
    }
    
    .stat-label {
        font-size: 14px;
        color: #606266;
    }
}
</style>
