<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\model\kb;

use app\common\model\BaseModel;

/**
 * 智能体分成收益记录模型
 */
class KbRobotRevenueLog extends BaseModel
{
    protected $table = 'cm_kb_robot_revenue_log';

    /**
     * @notes 创建分成收益记录
     * @param array $data
     * @return mixed
     */
    public static function createRevenue(array $data)
    {
        $data['create_time'] = time();
        $data['update_time'] = time();
        return self::create($data);
    }

    /**
     * @notes 获取用户分成收益统计
     * @param int $userId
     * @param string $timeRange 可选：today, week, month, all
     * @return array
     */
    public static function getUserRevenueStats(int $userId, string $timeRange = 'all'): array
    {
        $where = ['sharer_id' => $userId, 'settle_status' => 1];
        
        switch ($timeRange) {
            case 'today':
                $where[] = ['create_time', '>=', strtotime('today')];
                break;
            case 'week':
                $where[] = ['create_time', '>=', strtotime('-7 days')];
                break;
            case 'month':
                $where[] = ['create_time', '>=', strtotime('-30 days')];
                break;
            default:
                // 全部时间
                break;
        }
        
        $stats = self::where($where)->field([
            'COUNT(*) as count',
            'SUM(share_amount) as total_revenue',
            'AVG(share_amount) as avg_revenue'
        ])->find();
        
        return [
            'count' => $stats['count'] ?? 0,
            'total_revenue' => $stats['total_revenue'] ?? 0,
            'avg_revenue' => $stats['avg_revenue'] ?? 0
        ];
    }

    /**
     * @notes 获取智能体分成收益统计
     * @param int $robotId
     * @return array
     */
    public static function getRobotRevenueStats(int $robotId): array
    {
        $stats = self::where(['robot_id' => $robotId, 'settle_status' => 1])
            ->field([
                'COUNT(*) as use_count',
                'SUM(total_cost) as total_cost',
                'SUM(share_amount) as total_share_amount',
                'SUM(platform_amount) as total_platform_amount'
            ])->find();
        
        return [
            'use_count' => $stats['use_count'] ?? 0,
            'total_cost' => $stats['total_cost'] ?? 0,
            'total_share_amount' => $stats['total_share_amount'] ?? 0,
            'total_platform_amount' => $stats['total_platform_amount'] ?? 0
        ];
    }

    /**
     * @notes 批量结算收益
     * @param array $ids
     * @return bool
     */
    public static function batchSettle(array $ids): bool
    {
        return self::whereIn('id', $ids)
            ->where(['settle_status' => 0])
            ->update([
                'settle_status' => 1,
                'settle_time' => time(),
                'update_time' => time()
            ]) !== false;
    }
} 