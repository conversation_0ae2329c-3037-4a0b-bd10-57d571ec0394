<?php

namespace app\common\service;

use app\common\model\kb\KbRobotRecord;
use app\common\model\kb\KbRobotRevenueConfig;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\model\kb\KbRobotSquare;
use app\common\model\user\User;
use app\common\model\user\UserAccountLog;
use app\common\enum\user\AccountLogEnum;
use think\facade\Db;
use think\facade\Log;

/**
 * 简化的智能体分成服务（内存优化版本）
 * 
 * 设计原则：
 * 1. 简单可靠 - 避免复杂逻辑
 * 2. 防御性编程 - 安全的数组访问
 * 3. 清晰日志 - 便于调试
 * 4. 内存优化 - Docker环境内存管理
 */
class SimpleRevenueService
{
    // 内存管理相关属性
    private static ?int $memoryStartCheckpoint = null;
    private static int $processedCount = 0;
    private static array $memoryStats = [];
    private static array $configCache = [];
    private static int $cacheSize = 0;
    private static int $maxCacheSize = 100;
    
    // Docker环境内存阈值（适配128MB限制）
    private const MEMORY_WARNING_THRESHOLD = 50 * 1024 * 1024; // 50MB
    private const MEMORY_CRITICAL_THRESHOLD = 80 * 1024 * 1024; // 80MB
    private const GC_INTERVAL = 100; // 每100条记录执行垃圾回收
    
    /**
     * 内存监控：设置检查点
     */
    private static function setMemoryCheckpoint(string $label): void
    {
        $currentMemory = memory_get_usage(true);
        self::$memoryStats[$label] = [
            'memory' => $currentMemory,
            'time' => microtime(true),
            'processed' => self::$processedCount
        ];
        
        // 内存预警
        if ($currentMemory > self::MEMORY_WARNING_THRESHOLD) {
            Log::warning('[内存监控] 内存使用警告', [
                'label' => $label,
                'memory_mb' => round($currentMemory / 1024 / 1024, 2),
                'threshold_mb' => round(self::MEMORY_WARNING_THRESHOLD / 1024 / 1024, 2)
            ]);
            
            if ($currentMemory > self::MEMORY_CRITICAL_THRESHOLD) {
                self::emergencyMemoryCleanup();
            }
        }
    }
    
    /**
     * 智能垃圾回收
     */
    private static function smartGarbageCollection(): int
    {
        $beforeMemory = memory_get_usage(true);
        $cycles = gc_collect_cycles();
        $afterMemory = memory_get_usage(true);
        
        if ($cycles > 0) {
            $freedMemory = $beforeMemory - $afterMemory;
            Log::info('[内存优化] 垃圾回收完成', [
                'cycles' => $cycles,
                'freed_mb' => round($freedMemory / 1024 / 1024, 2),
                'current_mb' => round($afterMemory / 1024 / 1024, 2)
            ]);
        }
        
        return $cycles;
    }
    
    /**
     * 紧急内存清理
     */
    private static function emergencyMemoryCleanup(): void
    {
        Log::warning('[内存优化] 执行紧急内存清理');
        
        // 清理配置缓存
        self::$configCache = [];
        self::$cacheSize = 0;
        
        // 强制垃圾回收
        self::smartGarbageCollection();
        
        // 记录清理效果
        $currentMemory = memory_get_usage(true);
        Log::info('[内存优化] 紧急清理完成', [
            'current_mb' => round($currentMemory / 1024 / 1024, 2)
        ]);
    }
    /**
     * 处理单条记录的分成（内存优化版本）
     * 
     * @param array $record 对话记录数据
     * @return bool 是否处理成功
     */
    public static function processRecord(array $record): bool
    {
        try {
            // 内存监控：设置处理开始检查点
            self::setMemoryCheckpoint('process_start');
            
            Log::info('[分成处理] 开始处理记录', [
                'record_id' => $record['id'] ?? 0,
                'user_id' => $record['user_id'] ?? 0,
                'robot_id' => $record['robot_id'] ?? 0,
                'square_id' => $record['square_id'] ?? 0,
                'tokens' => $record['tokens'] ?? 0
            ]);

            // 1. 基础验证
            if (!self::validateRecord($record)) {
                return false;
            }

            // 2. 检查是否已处理
            if (($record['is_revenue_shared'] ?? 0) == 1) {
                Log::info('[分成处理] 记录已处理，跳过', ['record_id' => $record['id']]);
                return true;
            }

            // 3. 获取分成配置
            $config = self::getConfig();
            if (!$config || !($config['is_enable'] ?? 0)) {
                Log::info('[分成处理] 分成功能未开启', ['config' => $config]);
                return false;
            }

            // 4. 获取分享者信息
            $sharer = self::getSharer($record['square_id'] ?? 0);
            if (!$sharer) {
                Log::warning('[分成处理] 未找到分享者', ['square_id' => $record['square_id']]);
                return false;
            }

            // 5. 检查自分成（不能给自己分成）
            if (($sharer['user_id'] ?? 0) == ($record['user_id'] ?? 0)) {
                Log::info('[分成处理] 自分成，跳过', [
                    'user_id' => $record['user_id'],
                    'sharer_id' => $sharer['user_id']
                ]);
                return self::markAsProcessed($record['id'] ?? 0);
            }

            // 6. 计算分成金额
            $cost = self::calculateCost($record);
            if ($cost <= 0) {
                Log::info('[分成处理] 费用为0，跳过', ['cost' => $cost]);
                return self::markAsProcessed($record['id'] ?? 0);
            }

            $shareAmount = round($cost * ($config['share_ratio'] ?? 0) / 100, 4);
            $platformAmount = round($cost * ($config['platform_ratio'] ?? 0) / 100, 4);

            // 检查最小分成金额
            if ($shareAmount < ($config['min_revenue'] ?? 0.01)) {
                Log::info('[分成处理] 分成金额低于最小值，跳过', [
                    'share_amount' => $shareAmount,
                    'min_revenue' => $config['min_revenue']
                ]);
                return self::markAsProcessed($record['id'] ?? 0);
            }

            // 7. 执行分成
            $result = self::executeRevenue($record, $sharer, $cost, $shareAmount, $platformAmount, $config);
            
            // 内存优化：处理完成后清理和统计
            self::$processedCount++;
            
            // 主动释放变量
            unset($record, $sharer, $config, $cost, $shareAmount, $platformAmount);
            
            // 定期垃圾回收
            if (self::$processedCount % self::GC_INTERVAL === 0) {
                self::smartGarbageCollection();
            }
            
            // 设置处理完成检查点
            self::setMemoryCheckpoint('process_end');
            
            return $result;

        } catch (\Throwable $e) {
            Log::error('[分成处理] 处理异常', [
                'record_id' => $record['id'] ?? 0,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            // 异常时也要清理内存
            self::emergencyMemoryCleanup();
            return false;
        }
    }

    /**
     * 验证记录的有效性
     */
    private static function validateRecord(array $record): bool
    {
        $requiredFields = ['id', 'user_id', 'robot_id', 'square_id'];
        
        foreach ($requiredFields as $field) {
            if (!isset($record[$field]) || $record[$field] <= 0) {
                Log::warning('[分成处理] 记录验证失败', [
                    'missing_field' => $field,
                    'record_id' => $record['id'] ?? 0
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * 获取分成配置（内存优化缓存版本）
     */
    public static function getConfig(): ?array
    {
        try {
            $cacheKey = 'revenue_config';
            
            // 检查内存缓存
            if (isset(self::$configCache[$cacheKey])) {
                return self::$configCache[$cacheKey];
            }
            
            $config = KbRobotRevenueConfig::order('id desc')->findOrEmpty();
            
            if ($config->isEmpty()) {
                // 自动创建默认配置
                Log::info('[分成处理] 创建默认分成配置');
                $defaultConfig = [
                    'is_enable' => 1,
                    'share_ratio' => 15.00,
                    'platform_ratio' => 85.00,
                    'min_revenue' => 0.01,
                    'settle_type' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ];
                
                $config = KbRobotRevenueConfig::create($defaultConfig);
            }

            $result = $config->toArray();
            
            // 内存优化：限制缓存大小
            if (self::$cacheSize >= self::$maxCacheSize) {
                // LRU淘汰：移除最旧的缓存项
                $oldestKey = array_key_first(self::$configCache);
                unset(self::$configCache[$oldestKey]);
                self::$cacheSize--;
            }
            
            // 添加到缓存
            self::$configCache[$cacheKey] = $result;
            self::$cacheSize++;
            
            return $result;
        } catch (\Throwable $e) {
            Log::error('[分成处理] 获取配置失败', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 获取分享者信息
     */
    private static function getSharer(int $squareId): ?array
    {
        try {
            if ($squareId <= 0) {
                return null;
            }

            $square = KbRobotSquare::where('id', $squareId)->findOrEmpty();
            if ($square->isEmpty()) {
                Log::warning('[分成处理] 广场记录不存在', ['square_id' => $squareId]);
                return null;
            }

            $sharer = User::where('id', $square['user_id'])->findOrEmpty();
            if ($sharer->isEmpty()) {
                Log::warning('[分成处理] 分享者不存在', [
                    'square_id' => $squareId,
                    'user_id' => $square['user_id']
                ]);
                return null;
            }

            return [
                'square_id' => $square['id'],
                'user_id' => $sharer['id'],
                'robot_id' => $square['robot_id'],
                'nickname' => $sharer['nickname'] ?? '',
                'balance' => $sharer['balance'] ?? 0
            ];
        } catch (\Throwable $e) {
            Log::error('[分成处理] 获取分享者失败', [
                'square_id' => $squareId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 计算分成费用（基于实际消耗的电力值）
     */
    private static function calculateCost(array $record): float
    {
        try {
            $totalCost = 0;
            
            // 获取字符长度用于计算电力值（从flows字段中提取）
            $flows = json_decode($record['flows'] ?? '[]', true);
            if (!$flows) {
                Log::warning('[分成处理] 无法获取flows数据', ['record_id' => $record['id']]);
                return 0;
            }

            foreach ($flows as $flow) {
                $flowName = $flow['name'] ?? '';
                $totalPrice = floatval($flow['total_price'] ?? 0);
                
                Log::info('[分成处理] 计算费用详情', [
                    'record_id' => $record['id'],
                    'flow_name' => $flowName,
                    'total_price' => $totalPrice
                ]);
                
                $totalCost += $totalPrice;
            }

            Log::info('[分成处理] 费用计算完成', [
                'record_id' => $record['id'],
                'total_cost' => $totalCost
            ]);

            return round($totalCost, 4);
            
        } catch (\Throwable $e) {
            Log::error('[分成处理] 费用计算异常', [
                'record_id' => $record['id'] ?? 0,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 执行分成操作
     */
    private static function executeRevenue(
        array $record, 
        array $sharer, 
        float $cost, 
        float $shareAmount, 
        float $platformAmount, 
        array $config
    ): bool {
        Db::startTrans();
        
        try {
            // 根据结算方式决定是否立即结算
            $settleType = intval($config['settle_type'] ?? 1);
            $isRealTimeSettle = ($settleType == 1); // 1-实时结算, 2-每日结算
            
            // 1. 创建分成记录
            $revenueLog = KbRobotRevenueLog::create([
                'user_id' => $record['user_id'],
                'sharer_id' => $sharer['user_id'],
                'robot_id' => $record['robot_id'],
                'square_id' => $record['square_id'],
                'record_id' => $record['id'],
                'total_cost' => $cost,
                'share_amount' => $shareAmount,
                'platform_amount' => $platformAmount,
                'share_ratio' => $config['share_ratio'] ?? 0,
                'settle_status' => $isRealTimeSettle ? 1 : 0, // 实时结算=1(已结算), 每日结算=0(待结算)
                'settle_time' => $isRealTimeSettle ? time() : 0, // 实时结算记录结算时间
                'create_time' => time(),
                'update_time' => time()
            ]);

            // 2. 如果是实时结算，立即更新余额和记录日志
            if ($isRealTimeSettle) {
                // 更新分享者余额
                User::where('id', $sharer['user_id'])->inc('balance', $shareAmount);

                // 记录分享者余额变动日志
                UserAccountLog::add(
                    $sharer['user_id'],
                    AccountLogEnum::UM_INC_ROBOT_REVENUE,
                    AccountLogEnum::INC,
                    $shareAmount,
                    '',
                    '智能体分成收益',
                    [
                        'robot_id' => $record['robot_id'],
                        'square_id' => $record['square_id'],
                        'record_id' => $record['id'],
                        'revenue_log_id' => $revenueLog['id']
                    ]
                );
            }

            // 3. 标记对话记录已处理
            KbRobotRecord::where('id', $record['id'])->update([
                'is_revenue_shared' => 1,
                'revenue_log_id' => $revenueLog['id'],
                'update_time' => time()
            ]);

            Db::commit();

            Log::info('[分成处理] 分成执行成功', [
                'record_id' => $record['id'],
                'sharer_id' => $sharer['user_id'],
                'share_amount' => $shareAmount,
                'revenue_log_id' => $revenueLog['id'],
                'settle_type' => $settleType,
                'is_real_time_settle' => $isRealTimeSettle
            ]);

            return true;

        } catch (\Throwable $e) {
            Db::rollback();
            
            Log::error('[分成处理] 分成执行失败', [
                'record_id' => $record['id'],
                'sharer_id' => $sharer['user_id'] ?? 0,
                'cost' => $cost,
                'share_amount' => $shareAmount,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return false;
        }
    }

    /**
     * 标记记录为已处理（无需分成）
     */
    private static function markAsProcessed(int $recordId): bool
    {
        try {
            KbRobotRecord::where('id', $recordId)->update([
                'is_revenue_shared' => 1,
                'revenue_log_id' => 0,
                'update_time' => time()
            ]);

            Log::info('[分成处理] 标记为已处理', ['record_id' => $recordId]);
            return true;
        } catch (\Throwable $e) {
            Log::error('[分成处理] 标记失败', [
                'record_id' => $recordId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 批量处理未处理的记录（内存优化版本）
     */
    public static function batchProcess(int $limit = 50): array
    {
        $result = [
            'total' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'memory_stats' => []
        ];

        try {
            // 内存监控：批处理开始
            self::setMemoryCheckpoint('batch_start');
            
            // 查找未处理的记录
            $records = KbRobotRecord::where('is_revenue_shared', 0)
                ->where('square_id', '>', 0)
                ->limit($limit)
                ->select()
                ->toArray();

            $result['total'] = count($records);
            
            // 内存优化：分批处理，避免一次性加载所有数据
            $processedCount = 0;
            $batchSize = min(50, count($records)); // 每批最多50条
            
            for ($i = 0; $i < count($records); $i += $batchSize) {
                $batch = array_slice($records, $i, $batchSize);
                
                foreach ($batch as $record) {
                    if (self::processRecord($record)) {
                        $result['success']++;
                    } else {
                        $result['failed']++;
                    }
                    $processedCount++;
                }
                
                // 主动释放批次数据
                unset($batch);
                
                // 每处理完一批后执行内存检查
                if ($processedCount % 100 === 0) {
                    self::setMemoryCheckpoint("batch_progress_{$processedCount}");
                    self::smartGarbageCollection();
                }
            }
            
            // 最终内存清理
            unset($records);
            self::smartGarbageCollection();
            
            // 内存监控：批处理结束
            self::setMemoryCheckpoint('batch_end');
            
            // 添加内存统计信息
            $result['memory_stats'] = [
                'start_mb' => round((self::$memoryStats['batch_start']['memory'] ?? 0) / 1024 / 1024, 2),
                'end_mb' => round((self::$memoryStats['batch_end']['memory'] ?? 0) / 1024 / 1024, 2),
                'peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'processed_count' => self::$processedCount
            ];

            Log::info('[分成处理] 批量处理完成', $result);

        } catch (\Throwable $e) {
            Log::error('[分成处理] 批量处理异常', [
                'error' => $e->getMessage(),
                'result' => $result
            ]);
            
            // 异常时执行紧急内存清理
            self::emergencyMemoryCleanup();
        }

        return $result;
    }

    /**
     * 定时任务批量结算待结算记录（优化版，支持大量数据处理）
     */
    public static function batchSettlePending(int $limit = 200): array
    {
        $result = [
            'success' => false,
            'total_processed' => 0,
            'total_success' => 0,
            'total_failed' => 0,
            'total_amount' => 0,
            'batch_count' => 0,
            'errors' => [],
            'message' => '',
            'execution_time' => 0,
            'memory_usage' => 0
        ];

        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        try {
            Log::info('[定时结算] 开始执行智能体分成收益批量结算');
            
            // 检查是否有待结算记录
            $pendingCount = KbRobotRevenueLog::where(['settle_status' => 0])->count();
            if ($pendingCount == 0) {
                $result['success'] = true;
                $result['message'] = '没有待结算记录';
                Log::info('[定时结算] 没有待结算记录，跳过执行');
                return $result;
            }
            
            Log::info("[定时结算] 发现 {$pendingCount} 条待结算记录，开始分批处理");
            
            // 🚀 优化：动态调整批次大小，大量数据时增加批次
            $dynamicLimit = self::calculateOptimalBatchSize($pendingCount, $limit);
            Log::info("[定时结算] 动态批次大小：{$dynamicLimit} 条/批");
            
            do {
                $result['batch_count']++;
                
                // 分批获取待结算记录 - 优化：添加索引排序
                $pendingLogs = KbRobotRevenueLog::where(['settle_status' => 0])
                    ->order('id ASC') // 按ID升序处理，利用主键索引
                    ->limit($dynamicLimit)
                    ->select();

                if ($pendingLogs->isEmpty()) {
                    break;
                }
                
                // 🚀 优化：使用批量更新和事务批量处理
                $batchResult = self::processBatchSettlement($pendingLogs, $result['batch_count']);
                
                $result['total_processed'] += $batchResult['processed'];
                $result['total_success'] += $batchResult['success'];
                $result['total_failed'] += $batchResult['failed'];
                $result['total_amount'] += $batchResult['amount'];
                $result['errors'] = array_merge($result['errors'], $batchResult['errors']);
                
                // 🚀 优化：内存管理，释放已处理数据
                unset($pendingLogs, $batchResult);
                
                // 检查内存使用情况
                $currentMemory = memory_get_usage();
                if ($currentMemory > $startMemory + 100 * 1024 * 1024) { // 超过100MB增长时强制GC
                    gc_collect_cycles();
                    Log::info('[定时结算] 执行内存垃圾回收', [
                        'batch' => $result['batch_count'],
                        'memory_before' => round($currentMemory / 1024 / 1024, 2) . 'MB'
                    ]);
                }
                
                // 检查是否还有待处理记录
                $remainingCount = KbRobotRevenueLog::where(['settle_status' => 0])->count();
                if ($remainingCount == 0) {
                    break;
                }
                
                // 🚀 优化：处理大量数据时适当休息，避免数据库压力过大
                if ($pendingCount > 10000 && $result['batch_count'] % 50 == 0) {
                    usleep(100000); // 休息0.1秒
                    Log::info('[定时结算] 大数据处理休息', ['processed_batches' => $result['batch_count']]);
                }
                
            } while (true);
            
            $result['success'] = true;
            $result['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
            $result['memory_usage'] = round((memory_get_peak_usage() - $startMemory) / 1024 / 1024, 2);
            $result['message'] = "批量结算完成，成功: {$result['total_success']}, 失败: {$result['total_failed']}, 总金额: {$result['total_amount']}, 执行时间: {$result['execution_time']}ms, 内存使用: {$result['memory_usage']}MB";
            
            Log::info('[定时结算] 智能体分成收益批量结算完成', $result);
            
        } catch (\Throwable $e) {
            $result['success'] = false;
            $result['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
            $result['memory_usage'] = round((memory_get_peak_usage() - $startMemory) / 1024 / 1024, 2);
            $result['message'] = "批量结算异常: {$e->getMessage()}";
            $result['errors'][] = $e->getMessage();
            
            Log::error('[定时结算] 智能体分成收益批量结算异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'result' => $result
            ]);
        }
        
        return $result;
    }

    /**
     * 🚀 新增：计算最优批次大小
     */
    private static function calculateOptimalBatchSize(int $totalCount, int $defaultLimit): int
    {
        // 根据待处理记录数量动态调整批次大小
        if ($totalCount < 1000) {
            return min($defaultLimit, 100); // 小量数据：100条/批
        } elseif ($totalCount < 10000) {
            return min($defaultLimit * 2, 500); // 中量数据：500条/批
        } elseif ($totalCount < 100000) {
            return min($defaultLimit * 5, 1000); // 大量数据：1000条/批
        } else {
            return min($defaultLimit * 10, 2000); // 超大量数据：2000条/批
        }
    }

    /**
     * 🚀 新增：批量结算处理（优化版）
     */
    private static function processBatchSettlement($pendingLogs, int $batchNumber): array
    {
        $batchResult = [
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'amount' => 0,
            'errors' => []
        ];

        // 准备批量数据
        $userUpdates = []; // 用户余额更新
        $accountLogs = []; // 账户日志
        $revenueUpdates = []; // 分成记录更新
        $successIds = []; // 成功处理的ID

        foreach ($pendingLogs as $log) {
            $batchResult['processed']++;
            
            try {
                // 准备用户余额更新数据
                if (!isset($userUpdates[$log['sharer_id']])) {
                    $userUpdates[$log['sharer_id']] = 0;
                }
                $userUpdates[$log['sharer_id']] += $log['share_amount'];
                
                // 准备账户日志数据
                $accountLogs[] = [
                    'user_id' => $log['sharer_id'],
                    'change_type' => AccountLogEnum::UM_INC_ROBOT_REVENUE,
                    'action' => AccountLogEnum::INC,
                    'change_amount' => $log['share_amount'],
                    'left_amount' => 0, // 临时值，后续更新
                    'source_sn' => '',
                    'remark' => '智能体分成收益',
                    'extra' => json_encode([
                        'robot_id' => $log['robot_id'],
                        'square_id' => $log['square_id'],
                        'record_id' => $log['record_id'],
                        'revenue_log_id' => $log['id']
                    ]),
                    'create_time' => time(),
                    'update_time' => time()
                ];
                
                // 准备分成记录更新
                $revenueUpdates[] = [
                    'id' => $log['id'],
                    'settle_status' => 1,
                    'settle_time' => time(),
                    'update_time' => time()
                ];
                
                $successIds[] = $log['id'];
                $batchResult['amount'] += $log['share_amount'];
                
            } catch (\Throwable $e) {
                $batchResult['failed']++;
                $batchResult['errors'][] = "记录ID {$log['id']} 预处理失败: {$e->getMessage()}";
            }
        }

        // 🚀 优化：使用单一事务批量执行所有操作
        try {
            Db::startTrans();
            
            // 1. 批量更新用户余额 - 使用原生SQL确保更新成功
            foreach ($userUpdates as $userId => $amount) {
                $updateResult = Db::execute(
                    'UPDATE cm_user SET balance = balance + ?, update_time = ? WHERE id = ?',
                    [$amount, time(), $userId]
                );
                
                if (!$updateResult) {
                    throw new \Exception("用户ID {$userId} 余额更新失败");
                }
                
                Log::info("[定时结算] 用户余额更新", [
                    'user_id' => $userId,
                    'amount' => $amount,
                    'update_result' => $updateResult
                ]);
            }
            
            // 2. 获取更新后的用户余额用于账户日志
            if (!empty($accountLogs)) {
                $updatedUsers = User::whereIn('id', array_keys($userUpdates))->column('balance', 'id');
                foreach ($accountLogs as &$logData) {
                    $logData['left_amount'] = $updatedUsers[$logData['user_id']] ?? 0;
                }
                
                // 批量插入账户日志
                Db::name('user_account_log')->insertAll($accountLogs);
            }
            
            // 3. 批量更新分成记录状态
            if (!empty($successIds)) {
                KbRobotRevenueLog::whereIn('id', $successIds)->update([
                    'settle_status' => 1,
                    'settle_time' => time(),
                    'update_time' => time()
                ]);
            }
            
            Db::commit();
            
            $batchResult['success'] = count($successIds);
            
            Log::info("[定时结算] 批次 {$batchNumber} 处理成功", [
                'processed' => $batchResult['processed'],
                'success' => $batchResult['success'],
                'amount' => $batchResult['amount'],
                'user_count' => count($userUpdates),
                'user_updates' => $userUpdates
            ]);
            
        } catch (\Throwable $e) {
            Db::rollback();
            
            // 如果批量处理失败，回退到单条处理
            Log::warning("[定时结算] 批次 {$batchNumber} 批量处理失败，回退到单条处理", [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return self::processBatchSettlementFallback($pendingLogs, $batchNumber);
        }

        return $batchResult;
    }

    /**
     * 🚀 新增：批量处理失败时的单条处理回退方案
     */
    private static function processBatchSettlementFallback($pendingLogs, int $batchNumber): array
    {
        $batchResult = [
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'amount' => 0,
            'errors' => []
        ];

        foreach ($pendingLogs as $log) {
            $batchResult['processed']++;
            
            try {
                Db::startTrans();
                
                // 1. 更新分享者余额 - 使用原生SQL确保更新成功
                $updateResult = Db::execute(
                    'UPDATE cm_user SET balance = balance + ?, update_time = ? WHERE id = ?',
                    [$log['share_amount'], time(), $log['sharer_id']]
                );
                
                if (!$updateResult) {
                    throw new \Exception("用户ID {$log['sharer_id']} 余额更新失败");
                }
                
                // 2. 记录分享者余额变动日志
                UserAccountLog::add(
                    $log['sharer_id'],
                    AccountLogEnum::UM_INC_ROBOT_REVENUE,
                    AccountLogEnum::INC,
                    $log['share_amount'],
                    '',
                    '智能体分成收益',
                    [
                        'robot_id' => $log['robot_id'],
                        'square_id' => $log['square_id'],
                        'record_id' => $log['record_id'],
                        'revenue_log_id' => $log['id']
                    ]
                );
                
                // 3. 更新分成记录状态
                KbRobotRevenueLog::where('id', $log['id'])->update([
                    'settle_status' => 1,
                    'settle_time' => time(),
                    'update_time' => time()
                ]);
                
                Db::commit();
                
                $batchResult['success']++;
                $batchResult['amount'] += $log['share_amount'];
                
                Log::info("[定时结算] 单条记录结算成功", [
                    'log_id' => $log['id'],
                    'sharer_id' => $log['sharer_id'],
                    'share_amount' => $log['share_amount'],
                    'update_result' => $updateResult
                ]);
                
            } catch (\Throwable $e) {
                Db::rollback();
                
                $batchResult['failed']++;
                $error = "记录ID {$log['id']} 结算失败: {$e->getMessage()}";
                $batchResult['errors'][] = $error;
                
                Log::error("[定时结算] 批次 {$batchNumber} 单条记录结算失败", [
                    'log_id' => $log['id'],
                    'sharer_id' => $log['sharer_id'],
                    'share_amount' => $log['share_amount'],
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
        }

        return $batchResult;
    }

    /**
     * 获取统计信息
     */
    public static function getStats(): array
    {
        try {
            return [
                'total_records' => KbRobotRecord::where('square_id', '>', 0)->count(),
                'processed_records' => KbRobotRecord::where('square_id', '>', 0)->where('is_revenue_shared', 1)->count(),
                'pending_records' => KbRobotRecord::where('square_id', '>', 0)->where('is_revenue_shared', 0)->count(),
                'total_revenue_logs' => KbRobotRevenueLog::count(),
                'pending_settle_logs' => KbRobotRevenueLog::where('settle_status', 0)->count(),
                'settled_logs' => KbRobotRevenueLog::where('settle_status', 1)->count(),
                'config_enabled' => (bool)(self::getConfig()['is_enable'] ?? 0)
            ];
        } catch (\Throwable $e) {
            Log::error('[分成处理] 获取统计信息失败', ['error' => $e->getMessage()]);
            return [];
        }
    }
} 