<?php

namespace app\command;

use app\common\enum\RevenueStatusEnum;
use app\common\model\kb\KbRobotRecord;
use app\common\model\kb\KbRobotRevenueConfig;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\model\kb\KbRobotSquare;
use app\common\model\user\User;
use app\common\model\user\UserAccountLog;
use app\common\enum\user\AccountLogEnum;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 优化版智能体分成定时任务处理命令
 * 采用批量事务优化，显著提升处理性能
 */
class OptimizedRevenueSettle extends Command
{
    protected function configure()
    {
        $this->setName('optimized_revenue_settle')
            ->setDescription('优化版智能体分成定时任务处理')
            ->addArgument('limit', Argument::OPTIONAL, '单次处理记录数量', 1000)
            ->addOption('debug', 'd', Option::VALUE_NONE, '调试模式')
            ->addOption('stats', 's', Option::VALUE_NONE, '显示统计信息')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制执行')
            ->addOption('batch-size', 'b', Option::VALUE_REQUIRED, '批处理大小', 25)
            ->addOption('max-retry', 'r', Option::VALUE_REQUIRED, '最大重试次数', 3)
            ->addOption('use-cache', 'c', Option::VALUE_NONE, '启用缓存优化')
            ->addOption('benchmark', null, Option::VALUE_NONE, '性能基准测试模式');
    }

    /**
     * 执行命令
     */
    protected function execute(Input $input, Output $output)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        $limit = (int)($input->getArgument('limit') ?: 1000);
        $isDebug = $input->hasOption('debug');
        $showStats = $input->hasOption('stats');
        $isForce = $input->hasOption('force');
        $batchSize = (int)($input->getOption('batch-size') ?: 25);
        $maxRetry = (int)($input->getOption('max-retry') ?: 3);
        $useCache = $input->hasOption('use-cache');
        $isBenchmark = $input->hasOption('benchmark');

        $output->writeln('<info>🚀 优化版智能体分成定时任务开始执行</info>');
        
        if ($isDebug) {
            $output->writeln("<comment>调试模式已启用</comment>");
            $output->writeln("<comment>处理限制: {$limit} 条</comment>");
            $output->writeln("<comment>批处理大小: {$batchSize} 条</comment>");
            $output->writeln("<comment>最大重试: {$maxRetry} 次</comment>");
            $output->writeln("<comment>缓存优化: " . ($useCache ? '启用' : '禁用') . "</comment>");
        }

        try {
            // 1. 检查分成配置
            if (!$isForce) {
                $config = $this->getRevenueConfig();
                if (!$config || !$config['is_enable']) {
                    $output->writeln('<comment>⚠️ 智能体分成功能未开启，跳过执行</comment>');
                    return 0;
                }
            }

            // 2. 显示统计信息
            if ($showStats) {
                $stats = $this->getStatistics();
                $output->writeln('<comment>📊 当前统计信息:</comment>');
                $output->writeln("  - 待分成记录: {$stats['pending']} 条");
                $output->writeln("  - 分成失败记录: {$stats['failed']} 条");
                $output->writeln("  - 今日已处理: {$stats['today_processed']} 条");
                $output->writeln("  - 今日分成金额: {$stats['today_amount']} 元");
            }

            // 3. 执行优化版批量分成处理
            $result = $this->batchProcessOptimized($limit, $batchSize, $maxRetry, $useCache, $isBenchmark);
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $memoryUsage = round((memory_get_peak_usage(true) - $startMemory) / 1024 / 1024, 2);
            
            if ($result['success']) {
                $output->writeln('<info>✅ 优化版智能体分成定时任务执行成功</info>');
                $output->writeln("<comment>📊 执行统计:</comment>");
                $output->writeln("  - 处理记录数: {$result['total_processed']} 条");
                $output->writeln("  - 分成成功: {$result['total_success']} 条");
                $output->writeln("  - 分成失败: {$result['total_failed']} 条");
                $output->writeln("  - 跳过处理: {$result['total_skipped']} 条");
                $output->writeln("  - 分成金额: {$result['total_amount']} 元");
                $output->writeln("  - 批次数量: {$result['batch_count']} 批");
                $output->writeln("  - 事务数量: {$result['transaction_count']} 个");
                $output->writeln("  - 执行时间: {$executionTime}ms");
                $output->writeln("  - 内存使用: {$memoryUsage}MB");
                
                // 性能统计
                if ($result['total_processed'] > 0) {
                    $avgTimePerRecord = round($executionTime / $result['total_processed'], 2);
                    $recordsPerSec = round($result['total_processed'] / ($executionTime / 1000), 0);
                    $output->writeln("  - 平均处理时间: {$avgTimePerRecord}ms/条");
                    $output->writeln("  - 处理速度: {$recordsPerSec} 条/秒");
                    
                    // 性能对比
                    $oldSpeed = 13.3; // 原有处理速度
                    $improvement = round(($recordsPerSec / $oldSpeed - 1) * 100, 1);
                    $output->writeln("  - 性能提升: {$improvement}%");
                }
                
                // 缓存统计
                if ($useCache && isset($result['cache_stats'])) {
                    $cacheHitRate = round($result['cache_stats']['hit_rate'] * 100, 1);
                    $output->writeln("  - 缓存命中率: {$cacheHitRate}%");
                    $output->writeln("  - 缓存节省查询: {$result['cache_stats']['saved_queries']} 次");
                }
                
                if (!empty($result['errors']) && $isDebug) {
                    $output->writeln('<comment>⚠️ 错误信息:</comment>');
                    foreach ($result['errors'] as $error) {
                        $output->writeln("  - {$error}");
                    }
                }
                
                Log::info('[优化分成] 智能体分成定时任务执行成功', [
                    'execution_time' => $executionTime . 'ms',
                    'memory_usage' => $memoryUsage . 'MB',
                    'limit' => $limit,
                    'batch_size' => $batchSize,
                    'use_cache' => $useCache,
                    'result' => $result
                ]);
                
                return 0;
                
            } else {
                $output->writeln("<error>❌ 优化版智能体分成定时任务执行失败</error>");
                if (!empty($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        $output->writeln("<error>  - {$error}</error>");
                    }
                }
                
                Log::error('[优化分成] 智能体分成定时任务执行失败', [
                    'execution_time' => $executionTime . 'ms',
                    'errors' => $result['errors']
                ]);
                
                return 1;
            }
            
        } catch (\Throwable $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            $output->writeln("<error>❌ 优化版智能体分成定时任务执行异常: {$e->getMessage()}</error>");
            
            Log::error('[优化分成] 智能体分成定时任务异常', [
                'error' => $e->getMessage(),
                'execution_time' => $executionTime . 'ms',
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }

    /**
     * 优化版批量处理待分成记录
     */
    private function batchProcessOptimized(int $limit, int $batchSize, int $maxRetry, bool $useCache, bool $isBenchmark): array
    {
        $result = [
            'success' => false,
            'total_processed' => 0,
            'total_success' => 0,
            'total_failed' => 0,
            'total_skipped' => 0,
            'total_amount' => 0,
            'batch_count' => 0,
            'transaction_count' => 0,
            'errors' => [],
            'cache_stats' => [
                'hit_rate' => 0,
                'saved_queries' => 0
            ]
        ];
        
        try {
            // 1. 获取待分成记录
            $records = $this->getPendingRevenueRecords($limit, $maxRetry);
            if (empty($records)) {
                $result['success'] = true;
                return $result;
            }
            
            // 2. 预处理：按分享者分组并缓存信息
            $groupedData = $this->preProcessRecords($records, $useCache);
            $result['cache_stats'] = $groupedData['cache_stats'];
            
            // 3. 批量处理每个分享者的记录
            foreach ($groupedData['groups'] as $sharerId => $group) {
                $batchResult = $this->processSharerRecordsOptimized(
                    $group['records'], 
                    $group['sharer'], 
                    $batchSize,
                    $isBenchmark
                );
                
                $result['total_processed'] += $batchResult['processed'];
                $result['total_success'] += $batchResult['success'];
                $result['total_failed'] += $batchResult['failed'];
                $result['total_skipped'] += $batchResult['skipped'];
                $result['total_amount'] += $batchResult['amount'];
                $result['batch_count'] += $batchResult['batches'];
                $result['transaction_count'] += $batchResult['transactions'];
                
                if (!empty($batchResult['errors'])) {
                    $result['errors'] = array_merge($result['errors'], $batchResult['errors']);
                }
            }
            
            $result['success'] = true;
            
        } catch (\Throwable $e) {
            $result['errors'][] = $e->getMessage();
            Log::error('[优化分成] 批量处理异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        
        return $result;
    }

    /**
     * 预处理记录：按分享者分组并缓存信息
     */
    private function preProcessRecords(array $records, bool $useCache): array
    {
        $cacheStats = ['hit_rate' => 0, 'saved_queries' => 0];
        
        // 1. 提取所有分享者ID
        $sharerIds = array_unique(array_column($records, 'square_id'));
        
        // 2. 批量获取分享者信息（使用缓存优化）
        $sharers = [];
        $cacheHits = 0;
        
        if ($useCache) {
            // 尝试从缓存获取
            foreach ($sharerIds as $sharerId) {
                $cacheKey = "revenue:sharer:{$sharerId}";
                $cached = Cache::get($cacheKey);
                
                if ($cached) {
                    $sharers[$sharerId] = $cached;
                    $cacheHits++;
                } else {
                    $sharerInfo = KbRobotSquare::where('id', $sharerId)->find();
                    if ($sharerInfo) {
                        $sharers[$sharerId] = $sharerInfo->toArray();
                        Cache::set($cacheKey, $sharers[$sharerId], 3600); // 缓存1小时
                    }
                }
            }
            
            $cacheStats['hit_rate'] = count($sharerIds) > 0 ? $cacheHits / count($sharerIds) : 0;
            $cacheStats['saved_queries'] = $cacheHits;
        } else {
            // 批量查询所有分享者信息
            $sharerList = KbRobotSquare::whereIn('id', $sharerIds)->select();
            foreach ($sharerList as $sharer) {
                $sharers[$sharer['id']] = $sharer->toArray();
            }
        }
        
        // 3. 按分享者分组记录
        $groups = [];
        foreach ($records as $record) {
            $sharerId = $record['square_id'];
            if (isset($sharers[$sharerId])) {
                if (!isset($groups[$sharerId])) {
                    $groups[$sharerId] = [
                        'sharer' => $sharers[$sharerId],
                        'records' => []
                    ];
                }
                $groups[$sharerId]['records'][] = $record;
            }
        }
        
        return [
            'groups' => $groups,
            'cache_stats' => $cacheStats
        ];
    }

    /**
     * 优化版处理单个分享者的记录
     */
    private function processSharerRecordsOptimized(array $records, array $sharerInfo, int $batchSize, bool $isBenchmark): array
    {
        $result = [
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'amount' => 0,
            'batches' => 0,
            'transactions' => 0,
            'errors' => []
        ];

        // 按批次处理记录
        $batches = array_chunk($records, $batchSize);
        $result['batches'] = count($batches);

        foreach ($batches as $batchIndex => $batch) {
            try {
                $batchResult = $this->processBatchOptimized($batch, $sharerInfo, $batchIndex + 1, $isBenchmark);

                $result['processed'] += $batchResult['processed'];
                $result['success'] += $batchResult['success'];
                $result['failed'] += $batchResult['failed'];
                $result['skipped'] += $batchResult['skipped'];
                $result['amount'] += $batchResult['amount'];
                $result['transactions'] += $batchResult['transactions'];

                if (!empty($batchResult['errors'])) {
                    $result['errors'] = array_merge($result['errors'], $batchResult['errors']);
                }

            } catch (\Exception $e) {
                $result['errors'][] = "批次 " . ($batchIndex + 1) . " 处理异常: " . $e->getMessage();

                // 批次失败时，标记该批次所有记录为重试
                foreach ($batch as $record) {
                    $this->markRecordForRetry($record['id'], $e->getMessage());
                    $result['failed']++;
                }
            }
        }

        return $result;
    }

    /**
     * 优化版批次处理 - 使用单个大事务
     */
    private function processBatchOptimized(array $records, array $sharerInfo, int $batchIndex, bool $isBenchmark): array
    {
        $result = [
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'amount' => 0,
            'transactions' => 1,
            'errors' => []
        ];

        if ($isBenchmark) {
            $batchStartTime = microtime(true);
        }

        // 开启大事务
        Db::startTrans();

        try {
            // 1. 预处理：计算所有分成数据
            $revenueData = [];
            $accountLogs = [];
            $recordUpdates = [];
            $totalShareAmount = 0;
            $config = $this->getRevenueConfig();

            foreach ($records as $record) {
                $result['processed']++;

                // 计算分成金额
                $shareRatio = floatval($config['share_ratio'] ?? 15) / 100;
                $shareAmount = round($record['revenue_base_cost'] * $shareRatio, 4);

                // 检查最小分成金额
                if ($shareAmount < floatval($config['min_revenue'] ?? 0.01)) {
                    $result['skipped']++;
                    $recordUpdates[] = [
                        'id' => $record['id'],
                        'status' => RevenueStatusEnum::SKIPPED,
                        'error' => '分成金额低于最小值'
                    ];
                    continue;
                }

                $totalShareAmount += $shareAmount;
                $platformAmount = round($record['revenue_base_cost'] * (1 - $shareRatio), 4);

                // 准备分成记录数据
                $revenueData[] = [
                    'user_id' => $record['user_id'],
                    'sharer_id' => $sharerInfo['user_id'],
                    'robot_id' => $record['robot_id'],
                    'square_id' => $record['square_id'],
                    'record_id' => $record['id'],
                    'total_cost' => $record['revenue_base_cost'],
                    'share_amount' => $shareAmount,
                    'platform_amount' => $platformAmount,
                    'share_ratio' => $config['share_ratio'],
                    'settle_status' => 1,
                    'settle_time' => time(),
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 准备账户日志数据
                $accountLogs[] = [
                    'user_id' => $sharerInfo['user_id'],
                    'action' => AccountLogEnum::UM_INC_ROBOT_REVENUE,
                    'change_type' => AccountLogEnum::INC,
                    'change_amount' => $shareAmount,
                    'left_amount' => 0, // 将在批量更新后计算
                    'source_sn' => '',
                    'remark' => '智能体分成收益',
                    'extra' => json_encode([
                        'robot_id' => $record['robot_id'],
                        'square_id' => $record['square_id'],
                        'record_id' => $record['id']
                    ]),
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 准备记录更新数据
                $recordUpdates[] = [
                    'id' => $record['id'],
                    'status' => RevenueStatusEnum::SUCCESS,
                    'error' => null
                ];

                $result['success']++;
                $result['amount'] += $shareAmount;
            }

            // 2. 批量执行数据库操作
            if (!empty($revenueData)) {
                // 批量插入分成记录
                Db::table('cm_kb_robot_revenue_log')->insertAll($revenueData);

                // 更新用户余额
                if ($totalShareAmount > 0) {
                    User::where('id', $sharerInfo['user_id'])
                        ->inc('balance', $totalShareAmount)
                        ->update(['update_time' => time()]);
                }

                // 批量插入账户日志
                if (!empty($accountLogs)) {
                    // 获取更新后的用户余额
                    $userBalance = User::where('id', $sharerInfo['user_id'])->value('balance');
                    foreach ($accountLogs as &$log) {
                        $log['left_amount'] = $userBalance;
                    }
                    Db::table('cm_user_account_log')->insertAll($accountLogs);
                }

                // 批量更新记录状态
                foreach ($recordUpdates as $update) {
                    KbRobotRecord::where('id', $update['id'])
                        ->update([
                            'is_revenue_shared' => $update['status'],
                            'revenue_process_time' => time(),
                            'revenue_error' => $update['error']
                        ]);
                }
            }

            // 提交事务
            Db::commit();

            if ($isBenchmark) {
                $batchTime = round((microtime(true) - $batchStartTime) * 1000, 2);
                Log::info("[优化分成] 批次 {$batchIndex} 处理完成", [
                    'batch_size' => count($records),
                    'processing_time' => $batchTime . 'ms',
                    'success' => $result['success'],
                    'skipped' => $result['skipped'],
                    'amount' => $result['amount']
                ]);
            }

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            $result['errors'][] = "批次 {$batchIndex} 事务异常: " . $e->getMessage();
            $result['failed'] = $result['processed'];
            $result['success'] = 0;
            $result['amount'] = 0;

            Log::error("[优化分成] 批次 {$batchIndex} 处理失败", [
                'error' => $e->getMessage(),
                'batch_size' => count($records),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            throw $e;
        }

        return $result;
    }

    /**
     * 标记记录为重试
     */
    private function markRecordForRetry(int $recordId, string $error): void
    {
        try {
            KbRobotRecord::where('id', $recordId)
                ->inc('revenue_retry_count', 1)
                ->update([
                    'revenue_error' => $error,
                    'update_time' => time()
                ]);
        } catch (\Exception $e) {
            Log::error('[优化分成] 标记重试失败', [
                'record_id' => $recordId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取待分成记录
     */
    private function getPendingRevenueRecords(int $limit, int $maxRetry): array
    {
        return KbRobotRecord::where([
                ['is_revenue_shared', '=', RevenueStatusEnum::PENDING],
                ['square_id', '>', 0],
                ['revenue_base_cost', '>', 0]
            ])
            ->where('revenue_retry_count', '<', $maxRetry)
            ->order('create_time ASC')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 获取分成配置
     */
    private function getRevenueConfig(): ?array
    {
        try {
            $config = KbRobotRevenueConfig::order('id desc')->find();
            return $config ? $config->toArray() : null;
        } catch (\Exception $e) {
            Log::error('[优化分成] 获取分成配置异常', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取统计信息
     */
    private function getStatistics(): array
    {
        try {
            $today = date('Y-m-d');
            $todayStart = strtotime($today . ' 00:00:00');
            $todayEnd = strtotime($today . ' 23:59:59');

            return [
                'pending' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::PENDING)
                    ->where('square_id', '>', 0)
                    ->count(),
                'failed' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::FAILED)
                    ->count(),
                'today_processed' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::SUCCESS)
                    ->where('revenue_process_time', '>=', $todayStart)
                    ->where('revenue_process_time', '<=', $todayEnd)
                    ->count(),
                'today_amount' => KbRobotRevenueLog::where('create_time', '>=', $todayStart)
                    ->where('create_time', '<=', $todayEnd)
                    ->sum('share_amount')
            ];
        } catch (\Exception $e) {
            Log::error('[优化分成] 获取统计信息异常', [
                'error' => $e->getMessage()
            ]);
            return [
                'pending' => 0,
                'failed' => 0,
                'today_processed' => 0,
                'today_amount' => 0
            ];
        }
    }
}
