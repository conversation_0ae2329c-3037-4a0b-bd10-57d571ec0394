<template>
    <view class="h-full pt-[20rpx]">
        <view class="h-full bg-white flex flex-col py-[20rpx]">
            <scroll-view class="flex mb-[20rpx] px-[20rpx] whitespace-nowrap" scroll-x>
                <view class="bg-page mr-8 p-[8rpx] inline-block rounded-[8rpx]">
                    <view
                        v-for="item in tabState.list"
                        :key="item.type"
                        class="px-[14rpx] inline-block py-[9rpx] rounded-[8rpx]"
                        :class="{
                            'tab-active': item.type == tabState.current
                        }"
                        @click="tabsChange(item.type)"
                    >
                        {{ item.name }}
                    </view>
                </view>
            </scroll-view>
            <view class="flex-1 min-h-0 relative z-10">
                <scroll-view class="h-full" scroll-y :scroll-top="scrollTop">
                    <view class="px-[20rpx]">
                        <u-form
                            :model="robotInfo"
                            ref="uFormRef"
                            :rules="rules"
                            label-position="top"
                            :border-bottom="false"
                        >
                            <BaseConfig
                                v-model="robotInfo"
                                v-show="tabState.current == 'base'"
                            />
                            <SearchConfig
                                v-model="robotInfo"
                                v-show="tabState.current == 'search'"
                            />
                            <InterfaceConfig
                                v-model="robotInfo"
                                v-if="tabState.current == 'interface'"
                            />
                            <DigitalConfig
                                v-model="robotInfo"
                                v-show="tabState.current == 'digital'"
                            />
                            <FlowConfig
                                v-model="robotInfo"
                                v-show="tabState.current == 'flow'"
                            />
                        </u-form>
                    </view>
                </scroll-view>
            </view>
            <view class="px-[20rpx] pt-[20rpx]">
                <view class="flex-1">
                    <u-button
                        type="primary"
                        :custom-style="{ 'z-index': 0 }"
                        @click="handelSave"
                    >
                        保存
                    </u-button>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, shallowRef } from 'vue'
import BaseConfig from './base-config.vue'
import SearchConfig from './search-config.vue'
import InterfaceConfig from './interface-config.vue'
import DigitalConfig from './digital-config.vue'
import FlowConfig from './flow-config.vue'
import { putRobot } from '@/api/robot'
import { onMounted } from 'vue'
import { useRobot } from '../../useRobot'
import { useRouter } from 'uniapp-router-next'

const router = useRouter()
const tabState = reactive({
    list: [
        {
            name: '基本设置',
            type: 'base'
        },
        {
            name: 'AI模型/搜索配置',
            type: 'search'
        },
        {
            name: '界面设置',
            type: 'interface'
        },
        {
            name: '形象设置',
            type: 'digital'
        },
        {
            name: '工作流设置',
            type: 'flow'
        },
    ],
    current: 'base'
})
const scrollTop = ref(0)
const tabsChange = (type: string) => {
    tabState.current = type
    scrollTop.value = 10
    nextTick(() => {
        scrollTop.value = 0
    })
}
const uFormRef = shallowRef()
const { robotInfo, getRobotInfo } = useRobot()
const rules = {
    image: [
        {
            required: true,
            message: '请选择智能体图标'
        }
    ],
    name: [
        {
            required: true,
            message: '请输入智能体名称',
            trigger: ['change']
        }
    ],
    model_sub_id: [
        {
            required: true,
            message: '请选择AI模型'
        }
    ],
    cate_id: [
        {
            required: true,
            message: '请选择智能体分类'
        }
    ],
    digital_id: [
        {
            required: true,
            message: '请选择形象',
            validator: (rule: any, value: any, callback: any) => {
                if (value > 0) {
                    return true
                } else {
                    return false
                }
            }
        }
    ]
}

const handelSave = () => {
    uFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
            try {
                await putRobot(robotInfo.value)
                getRobotInfo()
                setTimeout(() => {
                    router.navigateBack()
                }, 1000)
            } catch (apiError: any) {
                // 🔧 H5端调试日志 - 开始
                console.log('🚨 [H5端] 捕获到API错误:', apiError);
                console.log('📝 [H5端] 错误对象类型:', typeof apiError);
                console.log('📝 [H5端] 错误对象结构:', JSON.stringify(apiError, null, 2));
                console.log('📝 [H5端] 错误消息:', apiError.msg);
                console.log('📝 [H5端] 错误消息类型:', typeof apiError.msg);
                console.log('📝 [H5端] 错误数据:', apiError.data);
                console.log('🔍 [H5端] 是否包含msg属性:', 'msg' in apiError);
                console.log('🔍 [H5端] msg属性值:', apiError.msg);
                console.log('🔍 [H5端] 字符串比较结果:', apiError.msg === 'ROBOT_ONLINE_NEED_CONFIRM');
                console.log('🔍 [H5端] 字符串长度比较:', apiError.msg?.length, 'vs', 'ROBOT_ONLINE_NEED_CONFIRM'.length);

                // 检查是否是智能体已上架需要确认的错误
                if (apiError.msg === 'ROBOT_ONLINE_NEED_CONFIRM') {
                    console.log('✅ [H5端] 检测到ROBOT_ONLINE_NEED_CONFIRM错误');
                    console.log('🔔 [H5端] 准备显示确认弹窗');

                    const squareInfo = apiError.data
                    console.log('📊 [H5端] 智能体状态信息:', squareInfo);

                    try {
                        console.log('📱 [H5端] 开始显示uni.showModal确认弹窗');

                        // 显示确认对话框
                        uni.showModal({
                            title: '智能体编辑确认',
                            content: '此智能体已在智能体广场上架，编辑后需要重新提交审核。确认编辑将自动下架此智能体，是否继续？',
                            confirmText: '确认编辑',
                            cancelText: '取消',
                            success: async (res) => {
                                console.log('🎯 [H5端] 用户选择结果:', res);

                                if (res.confirm) {
                                    console.log('✅ [H5端] 用户确认编辑，准备重新提交');

                                    try {
                                        // 用户确认，带上确认标识重新提交
                                        const confirmData = { ...robotInfo.value, confirm_offline: true }
                                        console.log('📝 [H5端] 重新提交数据:', confirmData);

                                        await putRobot(confirmData)
                                        console.log('✅ [H5端] 重新提交成功');

                                        uni.showToast({
                                            title: '智能体已更新并从广场下架，如需重新上架请重新提交审核',
                                            icon: 'none',
                                            duration: 3000
                                        })
                                        getRobotInfo()
                                        setTimeout(() => {
                                            router.navigateBack()
                                        }, 1000)
                                    } catch (error) {
                                        console.error('❌ [H5端] 重新提交失败:', error)
                                    }
                                } else {
                                    console.log('❌ [H5端] 用户取消编辑');
                                }
                            },
                            fail: (err) => {
                                console.error('❌ [H5端] 显示确认弹窗失败:', err);
                            }
                        })
                    } catch (modalError) {
                        console.error('❌ [H5端] 确认弹窗处理失败:', modalError);
                    }
                } else {
                    console.log('❌ [H5端] 不是ROBOT_ONLINE_NEED_CONFIRM错误');
                    console.log('🔍 [H5端] 实际错误消息:', JSON.stringify(apiError.msg));
                    console.log('🔍 [H5端] 期望错误消息:', 'ROBOT_ONLINE_NEED_CONFIRM');

                    // 其他错误正常处理
                    console.error('保存失败:', apiError)
                }
                // 🔧 H5端调试日志 - 结束
            }
        }
    })
}

onMounted(() => {
    setTimeout(() => {
        uFormRef.value.setRules(rules)
    }, 10)
})
</script>
<style lang="scss">
.tab-active {
    background: linear-gradient(
        90deg,
        var(--color-minor) 0%,
        var(--color-primary) 100%
    );
    @apply text-white;
}
</style>
