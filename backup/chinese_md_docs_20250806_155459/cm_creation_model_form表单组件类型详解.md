# cm_creation_model 表单组件类型详解

## 概述

在 `cm_creation_model` 表的 `form` 字段中，支持5种不同类型的表单组件，每种组件都有其特定的配置格式和用途。

## 支持的组件类型

根据前端表单设计器的实现，系统支持以下5种组件类型（按排序号）：

1. **WidgetInput** - 单行文本 (sort: 1)
2. **WidgetTextarea** - 多行文本 (sort: 2)
3. **WidgetSelect** - 下拉选项 (sort: 3)
4. **WidgetRadio** - 单选 (sort: 4)
5. **WidgetCheckbox** - 多选 (sort: 5)

## 各组件类型详细配置

### 1. WidgetInput (单行文本)

**用途**: 用于收集简短的文本信息，如姓名、标题、关键词等。

**配置格式**:
```json
{
  "name": "WidgetInput",
  "title": "单行文本",
  "id": "unique_id",
  "props": {
    "field": "field_name",           // 字段名，用于变量替换
    "title": "字段标题",              // 在表单中显示的标题
    "defaultValue": "",              // 默认值（通常为空）
    "placeholder": "示例文字",        // 输入提示文字
    "maxlength": 200,                // 最大输入长度限制
    "isRequired": true               // 是否必填
  }
}
```

**实际案例**:
```json
{
  "name": "WidgetInput",
  "title": "单行文本",
  "id": "lm5rj8y3",
  "props": {
    "field": "ljju8wlo",
    "title": "职位名称",
    "defaultValue": "",
    "placeholder": "新媒体运营",
    "maxlength": 200,
    "isRequired": true
  }
}
```

**在content中的使用**: `${ljju8wlo}`

### 2. WidgetTextarea (多行文本)

**用途**: 用于收集较长的文本内容，如文章内容、详细描述、要求说明等。

**配置格式**:
```json
{
  "name": "WidgetTextarea",
  "title": "多行文本",
  "id": "unique_id",
  "props": {
    "field": "field_name",           // 字段名，用于变量替换
    "title": "字段标题",              // 在表单中显示的标题
    "placeholder": "示例文字",        // 输入提示文字
    "rows": 4,                       // 默认显示行数
    "defaultValue": "",              // 默认值（通常为空）
    "maxlength": 500,                // 最大输入长度限制
    "autosize": false,               // 是否自动调整高度
    "isRequired": true               // 是否必填
  }
}
```

**实际案例**:
```json
{
  "name": "WidgetTextarea",
  "title": "多行文本",
  "id": "lm5rj8y5",
  "props": {
    "field": "ljczht8s",
    "title": "工作内容",
    "placeholder": "1.剪辑抖音视频8个 2.拍摄试镜2个 3.拍摄产品细节视频",
    "rows": 4,
    "defaultValue": "",
    "maxlength": 200,
    "autosize": false,
    "isRequired": true
  }
}
```

**在content中的使用**: `${ljczht8s}`

### 3. WidgetSelect (下拉选项)

**用途**: 用于从预定义的选项中选择一个值，适合选项较多或界面空间有限的场景。

**配置格式**:
```json
{
  "name": "WidgetSelect",
  "title": "下拉选项",
  "id": "unique_id",
  "props": {
    "field": "field_name",           // 字段名，用于变量替换
    "title": "字段标题",              // 在表单中显示的标题
    "options": ["选项1", "选项2", "选项3"],  // 下拉选项列表
    "defaultValue": "",              // 默认选中值
    "isRequired": true               // 是否必填
  }
}
```

**实际案例**:
```json
{
  "name": "WidgetSelect",
  "title": "下拉选项",
  "id": "lm5rj8zf",
  "props": {
    "field": "ljw9oucw",
    "title": "文档类型",
    "options": ["技术文档", "用户手册", "API文档"],
    "defaultValue": "",
    "isRequired": false
  }
}
```

**在content中的使用**: `${ljw9oucw}`

### 4. WidgetRadio (单选)

**用途**: 用于从几个选项中选择一个，选项相对较少且需要直观展示所有选项。

**配置格式**:
```json
{
  "name": "WidgetRadio",
  "title": "单选",
  "id": "unique_id",
  "props": {
    "field": "field_name",           // 字段名，用于变量替换
    "title": "字段标题",              // 在表单中显示的标题
    "options": ["选项1", "选项2", "选项3"],  // 单选选项列表
    "defaultValue": "选项1",         // 默认选中值
    "isRequired": true               // 是否必填
  }
}
```

**实际案例**:
```json
{
  "name": "WidgetRadio",
  "title": "单选",
  "id": "lm5rj8y7",
  "props": {
    "field": "lja6u9f7",
    "title": "生成类型",
    "options": ["日报", "周报", "月报", "汇总"],
    "defaultValue": "日报",
    "isRequired": true
  }
}
```

**在content中的使用**: `${lja6u9f7}`

### 5. WidgetCheckbox (多选)

**用途**: 用于从多个选项中选择一个或多个值，适合需要多选的场景。

**配置格式**:
```json
{
  "name": "WidgetCheckbox",
  "title": "多选",
  "id": "unique_id",
  "props": {
    "field": "field_name",           // 字段名，用于变量替换
    "title": "字段标题",              // 在表单中显示的标题
    "options": ["选项1", "选项2", "选项3"],  // 多选选项列表
    "defaultValue": [],              // 默认选中值（数组格式）
    "isRequired": true               // 是否必填
  }
}
```

**示例配置**:
```json
{
  "name": "WidgetCheckbox",
  "title": "多选",
  "id": "lm5rj8z8",
  "props": {
    "field": "ljk9slw2",
    "title": "技能要求",
    "options": ["HTML", "CSS", "JavaScript", "Vue", "React"],
    "defaultValue": [],
    "isRequired": false
  }
}
```

**在content中的使用**: `${ljk9slw2}`

**数据处理**: 多选数据在后端会被处理为用"、"连接的字符串
```php
if(is_array($form)){
    $form = implode('、',$form);
}
```

## 组件配置规则

### 1. 通用配置项

所有组件都包含以下通用配置：

- **name**: 组件类型标识，必须是以上5种之一
- **title**: 组件显示名称，用于后台管理界面
- **id**: 唯一标识符，系统自动生成
- **props**: 组件属性配置对象

### 2. props通用属性

所有组件的props都包含：

- **field**: 字段名，用于与content中的变量对应，格式为8位随机字符
- **title**: 字段标题，在用户表单中显示
- **isRequired**: 是否必填，布尔值

### 3. 字段名规则

- **格式**: 8位随机字符，如 `ljju8wlo`、`lja6u9f7`
- **唯一性**: 同一个创作模型内字段名不能重复
- **字符集**: 包含小写字母和数字
- **命名规范**: 避免使用保留字和特殊字符

### 4. 选项类型组件规则

对于包含选项的组件（WidgetSelect、WidgetRadio、WidgetCheckbox）：

- **options**: 数组格式，每个元素是一个选项
- **选项内容**: 每个选项最多50个字符
- **选项数量**: 最多50个选项
- **默认值**: 必须是options中的有效值

## 完整示例

### 综合表单配置示例

```json
[
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "lm5rj8y1",
    "props": {
      "field": "ljk8mn45",
      "title": "项目名称",
      "defaultValue": "",
      "placeholder": "请输入项目名称",
      "maxlength": 100,
      "isRequired": true
    }
  },
  {
    "name": "WidgetTextarea",
    "title": "多行文本",
    "id": "lm5rj8y2",
    "props": {
      "field": "ljk8mn46",
      "title": "项目描述",
      "placeholder": "请详细描述项目内容和目标",
      "rows": 5,
      "defaultValue": "",
      "maxlength": 1000,
      "autosize": true,
      "isRequired": true
    }
  },
  {
    "name": "WidgetSelect",
    "title": "下拉选项",
    "id": "lm5rj8y3",
    "props": {
      "field": "ljk8mn47",
      "title": "项目类型",
      "options": ["Web应用", "移动应用", "桌面应用", "API服务"],
      "defaultValue": "Web应用",
      "isRequired": true
    }
  },
  {
    "name": "WidgetRadio",
    "title": "单选",
    "id": "lm5rj8y4",
    "props": {
      "field": "ljk8mn48",
      "title": "优先级",
      "options": ["高", "中", "低"],
      "defaultValue": "中",
      "isRequired": true
    }
  },
  {
    "name": "WidgetCheckbox",
    "title": "多选",
    "id": "lm5rj8y5",
    "props": {
      "field": "ljk8mn49",
      "title": "技术栈",
      "options": ["Vue.js", "React", "Angular", "Node.js", "Python", "Java"],
      "defaultValue": ["Vue.js"],
      "isRequired": false
    }
  }
]
```

### 对应的content模板

```text
请根据以下要求为项目"${ljk8mn45}"制定开发计划：

项目描述：${ljk8mn46}

项目类型：${ljk8mn47}
优先级：${ljk8mn48}
技术栈：${ljk8mn49}

请制定详细的开发计划，包括时间安排、技术选型说明和风险评估。
```

## 数据流程

### 1. 用户填写表单
```javascript
// 用户提交的数据
{
  "ljk8mn45": "在线商城系统",
  "ljk8mn46": "一个支持多商户的电商平台，包含商品管理、订单处理、支付集成等功能",
  "ljk8mn47": "Web应用",
  "ljk8mn48": "高",
  "ljk8mn49": ["Vue.js", "Node.js", "MySQL"]
}
```

### 2. 后端变量替换
```php
// PHP处理逻辑
$this->question = $this->modelContent['content'];
foreach ($this->modelContent['form'] as $formVal) {
    $field = $formVal['props']['field'];
    $form = $this->form[$field] ?? '';
    
    // 处理多选数据
    if(is_array($form)){
        $form = implode('、',$form);
    }
    
    $replaceStr = '${'.$field.'}';
    $this->question = str_replace($replaceStr, $form, $this->question);
}
```

### 3. 最终生成的提示词
```text
请根据以下要求为项目"在线商城系统"制定开发计划：

项目描述：一个支持多商户的电商平台，包含商品管理、订单处理、支付集成等功能

项目类型：Web应用
优先级：高
技术栈：Vue.js、Node.js、MySQL

请制定详细的开发计划，包括时间安排、技术选型说明和风险评估。
```

## 最佳实践

### 1. 组件选择建议

- **短文本（<50字符）**: 使用 WidgetInput
- **长文本（>50字符）**: 使用 WidgetTextarea
- **固定选项（2-5个）**: 使用 WidgetRadio
- **固定选项（>5个）**: 使用 WidgetSelect
- **多项选择**: 使用 WidgetCheckbox

### 2. 字段命名建议

- 使用描述性的title，让用户清楚字段用途
- placeholder提供具体的示例
- 合理设置maxlength，避免过长或过短
- 根据实际需求设置isRequired

### 3. 选项设计建议

- 选项名称简洁明了
- 避免歧义的选项
- 提供合理的默认值
- 选项数量适中，避免过多造成选择困难

### 4. 验证规则

- 必填字段必须有明确的提示
- 长度限制要合理
- 选项的默认值必须在options范围内
- 多选的defaultValue必须是数组格式

## 系统实现细节

### 1. 前端表单设计器

**文件位置**: `admin/src/components/form-designer/`

- **material/**: 各组件类型定义
- **setters/**: 属性设置器组件
- **container.vue**: 主容器组件
- **popup.vue**: 弹窗组件

### 2. 表单渲染

前端根据form配置动态渲染用户界面：

```vue
<template>
  <div v-for="(item, index) in formData.form" :key="index">
    <component 
      :is="item.name"
      v-model="formValues[item.props.field]"
      v-bind="item.props"
    />
  </div>
</template>
```

### 3. 后端处理

**文件位置**: `server/app/api/logic/chat/ChatDialogLogic.php`

变量替换的核心逻辑确保用户输入正确转换为AI提示词。

## 总结

cm_creation_model的form字段通过5种组件类型实现了灵活的表单配置系统：

1. **灵活性**: 支持多种输入类型，满足不同场景需求
2. **扩展性**: 易于添加新的组件类型
3. **一致性**: 统一的配置格式和处理逻辑
4. **用户友好**: 直观的界面设计和交互体验

这个设计为AI创作提供了强大的模板化能力，是现代AI应用的优秀实践案例。 