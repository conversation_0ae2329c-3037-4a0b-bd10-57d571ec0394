# 数据库分析与升级管理指南

## 系统环境
- **部署方式**：Docker环境
- **数据库版本**：MySQL 5.7
- **PHP版本**：8.0.30.3
- **Redis版本**：7.4
- **技术栈**：ThinkPHP 8 + Vue 3 + Nuxt 3

---

# 数据库基础配置表分析

## 分析概述
**分析时间**：2025-01-27  
**分析目标**：对数据库中与基础配置相关的表进行系统性分析和整理

## 核心发现

### 📊 基础配置表统计
通过分析数据库结构，共发现**23个**与基础配置相关的核心表，按功能模块分类如下：

#### 🔧 系统配置模块 (3个表)

##### 1. cm_config - 系统配置表 (53条记录)
**功能**: 存储系统的核心配置参数
**字段结构**:
- `id`: 主键
- `type`: 配置类型
- `name`: 配置名称  
- `value`: 配置值
- `create_time/update_time`: 时间戳

**配置类型分布**:
- `chat` (16项): 聊天相关配置
- `content_censor` (9项): 内容审核配置
- `distribution` (6项): 分销配置
- `login` (6项): 登录配置
- `manual_kf` (6项): 人工客服配置
- `online_kf` (3项): 在线客服配置
- `robot_award` (5项): 机器人奖励配置
- `sms` (2项): 短信配置

##### 2. cm_notice_setting - 通知设置表 (7条记录)
**功能**: 管理系统通知相关配置

##### 3. cm_dev_pay_config - 开发支付配置表 (2条记录)
**功能**: 支付接口相关配置

#### 👥 用户管理模块 (11个表)

##### 4. cm_user - 用户表 (3条记录)
**功能**: 存储用户基础信息

##### 5. cm_user_auth - 用户认证表 (0条记录)
**功能**: 用户第三方认证信息

##### 6. cm_user_account_log - 用户账户日志表 (370条记录)
**功能**: 记录用户账户变动记录

##### 7. cm_user_session - 用户会话表 (34条记录)
**功能**: 管理用户登录会话

##### 8. cm_user_group - 用户组表 (0条记录)
**功能**: 用户分组管理

##### 9. cm_user_member - 用户会员表 (4条记录)
**功能**: 用户会员信息管理

##### 10. cm_user_gift_config - 用户赠送配置表 (0条记录)
**功能**: 用户间赠送功能配置

##### 11. cm_user_gift_log - 用户赠送日志表 (23条记录)
**功能**: 记录用户间赠送操作

#### 🛡️ 权限管理模块 (7个表)

##### 12. cm_admin - 管理员表 (0条记录)
**功能**: 管理员账户信息

##### 13. cm_admin_dept - 管理员部门表 (0条记录)
**功能**: 管理员部门组织结构

##### 14. cm_admin_jobs - 管理员岗位表 (0条记录)
**功能**: 管理员岗位管理

##### 15. cm_admin_role - 管理员角色表 (0条记录)
**功能**: 管理员角色分配

##### 16. cm_admin_session - 管理员会话表 (1条记录)
**功能**: 管理员登录会话管理

##### 17. cm_system_role - 系统角色表 (0条记录)
**功能**: 系统角色定义

##### 18. cm_system_role_menu - 系统角色菜单关联表 (11条记录)
**功能**: 角色与菜单权限关联

#### 📋 菜单与字典模块 (3个表)

##### 19. cm_system_menu - 系统菜单表 (483条记录)
**功能**: 系统菜单结构管理
**字段结构**:
- `id`: 主键
- `pid`: 父级菜单ID
- `type`: 菜单类型
- `name`: 菜单名称
- `icon`: 菜单图标
- `sort`: 排序
- `perms`: 权限标识
- `paths`: 路径
- `component`: 组件
- `params`: 参数
- `is_cache/is_show/is_disable`: 状态控制

##### 20. cm_dict_type - 字典类型表 (0条记录)
**功能**: 数据字典类型定义

##### 21. cm_dict_data - 字典数据表 (0条记录)
**功能**: 数据字典具体数据

#### 🤖 业务配置模块 (2个表)

##### 22. cm_role_example - 角色示例表 (9条记录)
**功能**: AI角色示例配置

##### 23. cm_kb_robot_revenue_config - 知识库机器人收益配置表 (1条记录)
**功能**: 知识库机器人分成配置

### 🎯 重要性分析

#### 🔴 核心配置表 (高优先级)
- **cm_config**: 系统核心配置，影响整个系统运行
- **cm_system_menu**: 系统菜单结构，影响用户界面和权限
- **cm_user**: 用户基础信息，影响用户功能

#### 🟡 业务配置表 (中优先级)
- **cm_user_account_log**: 账户变动记录，影响财务统计
- **cm_notice_setting**: 通知配置，影响用户体验
- **cm_role_example**: AI角色配置，影响AI功能

#### 🟢 辅助配置表 (低优先级)
- **cm_dict_type/cm_dict_data**: 数据字典，便于数据管理
- **cm_admin系列**: 管理员相关，影响后台管理

### 📝 配置管理建议

#### 1. 数据完整性检查
- 部分核心表记录为0，需要检查是否正常
- 建议定期备份配置表数据

#### 2. 权限管理优化
- 完善管理员权限体系
- 建立完整的角色权限关联

#### 3. 配置管理规范
- 建立配置项变更审计机制
- 实现配置的版本管理

#### 4. 性能优化
- 对高频访问的配置表建立适当索引
- 考虑配置缓存机制

---

# NewAI功能融合 - 数据库修改影响分析

## 修改概述

在NewAI功能融合过程中，对数据库进行了以下修改：

### 1. 数据库结构修改

#### 1.1 用户表（cm_user）新增字段
```sql
ALTER TABLE `cm_user` ADD COLUMN `censor_text_status` tinyint(1) NULL DEFAULT 0 COMMENT '审核状态  0-未审核 1-已审核';
ALTER TABLE `cm_user` ADD COLUMN `censor_text_result` text NULL COMMENT '审核结果';
ALTER TABLE `cm_user` ADD COLUMN `censor_image_status` tinyint(1) NULL DEFAULT 0 COMMENT '审核状态  0-未审核 1-已审核';
ALTER TABLE `cm_user` ADD COLUMN `censor_image_result` text NULL COMMENT '审核结果';
```

**影响分析：**
- ✅ **数据安全**：新增字段不影响现有数据
- ✅ **向后兼容**：现有用户记录保持完整
- ✅ **默认值设置**：新字段有合理的默认值（0表示未审核）

#### 1.2 知识库机器人记录表（cm_kb_robot_record）字段修改
```sql
ALTER TABLE `cm_kb_robot_record` MODIFY COLUMN `ask` longtext NULL COMMENT '提问';
```

**影响分析：**
- ✅ **数据保留**：从text改为longtext，数据完全保留
- ✅ **功能增强**：支持更长的问题文本（最大4GB vs 64KB）
- ✅ **无风险**：字段类型扩展，不会丢失数据

#### 1.3 视频记录表（cm_video_record）新增字段
```sql
ALTER TABLE `cm_video_record` ADD COLUMN `complex_params` text NULL COMMENT '高级参数';
```

**影响分析：**
- ✅ **数据安全**：新增字段不影响现有记录
- ✅ **功能扩展**：为视频功能提供高级参数支持
- ✅ **可选字段**：NULL默认值，不强制要求

### 2. 数据修改

#### 2.1 定时任务新增
```sql
INSERT INTO `cm_dev_crontab` VALUES ('用户信息审核', 1, 0, '审核用户头像及昵称', 'user_info_censor', '', 1, '*/30 * * * *', ...);
```

**影响分析：**
- ✅ **功能新增**：添加用户信息审核定时任务
- ✅ **无冲突**：独立的新记录，不影响现有定时任务
- ✅ **可控制**：可以通过管理后台启用/禁用

## 实际数据状态检查

### 用户表审核字段状态
**检查结果：**
```
用户ID: 1, 昵称: 11111, 文本审核状态: 3, 图片审核状态: 3
用户ID: 2, 昵称: 222,   文本审核状态: 3, 图片审核状态: 3  
用户ID: 3, 昵称: 333,   文本审核状态: 3, 图片审核状态: 3
```

**状态说明：**
- 状态3 = 审核失败（CENSOR_STATUS_FAIL）
- 审核失败原因：`{"error_code":14,"error_msg":"IAM Certification failed"}`
- **原因分析**：百度AI审核配置未正确设置，导致API调用失败

### 风险评估

#### 低风险项（✅ 已验证安全）
1. **数据保留**：所有现有数据100%保留
2. **字段扩展**：longtext扩展不会丢失数据
3. **新增字段**：有合理默认值，不破坏现有逻辑
4. **定时任务**：独立功能，不影响现有业务

#### 需要关注的项（⚠️ 需要配置）
1. **审核API配置**：需要配置正确的百度AI参数
2. **审核状态处理**：当前用户审核状态为"失败"，需要重置或修复
3. **定时任务执行**：需要确保命令正确注册

## 修复建议

### 1. 审核功能配置
```sql
-- 重置用户审核状态为未审核
UPDATE cm_user SET 
    censor_text_status = 0, 
    censor_image_status = 0,
    censor_text_result = NULL,
    censor_image_result = NULL 
WHERE censor_text_status = 3 OR censor_image_status = 3;
```

### 2. 百度AI配置
在管理后台配置正确的百度AI参数：
- APP_ID：从百度AI控制台获取
- API_KEY：从百度AI控制台获取  
- SECRET_KEY：从百度AI控制台获取

### 3. 定时任务验证
确保`user_info_censor`命令已在`server/config/console.php`中正确注册。

## 总结

### 数据安全性 ✅
- **零数据丢失**：所有现有数据完整保留
- **向后兼容**：现有功能完全不受影响
- **安全升级**：仅新增和扩展，不删除或破坏

### 功能完整性 ✅  
- **核心功能**：AI对话、绘画、音乐等功能正常
- **增强功能**：知识库支持更长文本，视频支持高级参数
- **新增功能**：用户信息审核系统已安装（需配置激活）

### 系统稳定性 ✅
- **数据库结构**：升级成功，无错误
- **业务逻辑**：现有逻辑不受影响
- **新功能隔离**：审核功能独立，失败不影响主业务

**结论：NewAI功能融合对现有数据和系统的影响是安全的，所有现有数据完整保留，现有功能正常运行。新增的审核功能需要进一步配置才能正常工作。**

---

# 数据库升级标准指南

## 数据库结构升级

请在数据库中执行以下SQL语句：

```sql
SET FOREIGN_KEY_CHECKS=0;

-- 修改知识库机器人记录表的ask字段类型
ALTER TABLE `cw_kb_robot_record` MODIFY COLUMN `ask` longtext NULL COMMENT '提问' AFTER `emb_model_id`;

-- 为用户表添加审核相关字段
ALTER TABLE `cw_user` ADD COLUMN `censor_text_status` tinyint(1) NULL DEFAULT 0 COMMENT '审核状态  0-未审核 1-已审核' AFTER `delete_time`;
ALTER TABLE `cw_user` ADD COLUMN `censor_text_result` text NULL COMMENT '审核结果' AFTER `censor_text_status`;
ALTER TABLE `cw_user` ADD COLUMN `censor_image_status` tinyint(1) NULL DEFAULT 0 COMMENT '审核状态  0-未审核 1-已审核' AFTER `censor_text_result`;
ALTER TABLE `cw_user` ADD COLUMN `censor_image_result` text NULL COMMENT '审核结果' AFTER `censor_image_status`;

-- 为视频记录表添加高级参数字段
ALTER TABLE `cw_video_record` ADD COLUMN `complex_params` text NULL COMMENT '高级参数' AFTER `api_version`;

SET FOREIGN_KEY_CHECKS=1;
```

## 定时任务数据升级

请在数据库中执行以下SQL语句添加用户信息审核定时任务：

```sql
SET FOREIGN_KEY_CHECKS=0;

INSERT INTO `cw_dev_crontab` (`name`, `type`, `system`, `remark`, `command`, `params`, `status`, `expression`, `error`, `last_time`, `time`, `max_time`, `create_time`, `update_time`, `delete_time`) 
VALUES ('用户信息审核', 1, 0, '审核用户头像及昵称', 'user_info_censor', '', 1, '*/30 * * * *', NULL, 1750042321, '0','0', 1750042321, 1750042321, NULL);

SET FOREIGN_KEY_CHECKS=1;
```

## 执行说明

1. 请根据您的实际数据库配置，通过数据库管理工具或命令行执行上述SQL语句
2. 数据库升级文件已保存在主目录：
   - `database_upgrade_structure.sql` - 结构升级脚本
   - `database_upgrade_data.sql` - 数据升级脚本

## 升级后功能

- **用户信息审核**：自动审核用户头像和昵称，违规内容将被替换
- **字段类型优化**：知识库问题字段支持更长的文本内容
- **视频功能增强**：支持高级参数配置
- **定时任务**：每30分钟自动执行用户信息审核

---

# 数据库升级参考指南

## 升级指南概述
**整理时间**：2025-06-28  
**数据来源**：34个历史记录文件，约50MB数据  
**时间跨度**：2025-06-04至2025-06-28

## 🗂️ 新增表结构

### 1. 用户赠送功能相关表

#### 1.1 用户赠送日志表 (cm_user_gift_log)
```sql
CREATE TABLE `cm_user_gift_log` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gift_sn` varchar(32) NOT NULL DEFAULT '' COMMENT '赠送流水号',
  `from_user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '赠送者用户ID',
  `to_user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '接收者用户ID',
  `gift_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '赠送类型: 1=灵感值, 2=会员时长, 3=对话次数',
  `gift_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '赠送金额',
  `gift_message` varchar(500) NOT NULL DEFAULT '' COMMENT '赠送留言',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '状态: [1=成功, 2=失败, 3=已撤回]',
  `remark` varchar(300) NOT NULL DEFAULT '' COMMENT '备注信息',
  `ip` varchar(32) NOT NULL DEFAULT '' COMMENT '来源IP',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `gift_sn` (`gift_sn`),
  KEY `idx_from_user` (`from_user_id`),
  KEY `idx_to_user` (`to_user_id`),
  KEY `idx_gift_type` (`gift_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`),
  KEY `idx_user_time` (`from_user_id`, `create_time`),
  KEY `idx_receive_time` (`to_user_id`, `create_time`),
  KEY `idx_status_time` (`status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户赠送记录表';
```

#### 1.2 用户赠送配置表 (cm_user_gift_config)
```sql
CREATE TABLE `cm_user_gift_config` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '配置名称',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否启用赠送功能',
  `gift_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '赠送类型: 1=灵感值, 2=会员时长, 3=对话次数',
  `min_gift_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '1.0000000' COMMENT '最小赠送金额',
  `max_gift_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '1000.0000000' COMMENT '最大赠送金额',
  `daily_gift_limit` decimal(15,7) UNSIGNED NOT NULL DEFAULT '100.0000000' COMMENT '每日赠送限额',
  `daily_receive_limit` decimal(15,7) UNSIGNED NOT NULL DEFAULT '500.0000000' COMMENT '每日接收限额',
  `gift_times_limit` int(10) UNSIGNED NOT NULL DEFAULT '10' COMMENT '每日赠送次数限制',
  `receive_times_limit` int(10) UNSIGNED NOT NULL DEFAULT '20' COMMENT '每日接收次数限制',
  `friend_only` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否仅限好友间赠送',
  `need_verify` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否需要人工审核',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_gift_type` (`gift_type`),
  KEY `idx_is_enable` (`is_enable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户赠送配置表';
```

### 2. 知识库机器人收益相关表

#### 2.1 知识库机器人收益配置表 (cm_kb_robot_revenue_config)
```sql
CREATE TABLE `cm_kb_robot_revenue_config` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `is_enable` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否启用分成',
  `revenue_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '收益类型: 1=按比例分成, 2=固定金额',
  `share_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '分成比例(%)',
  `platform_ratio` decimal(5,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '平台比例(%)',
  `revenue_rate` decimal(5,4) UNSIGNED NOT NULL DEFAULT '0.0000' COMMENT '分成比例',
  `fixed_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '固定金额',
  `min_revenue` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '最小收益',
  `max_revenue` decimal(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '最大收益',
  `settle_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '结算类型: 1=实时, 2=批量',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_robot_id` (`robot_id`),
  KEY `idx_revenue_type` (`revenue_type`),
  KEY `idx_is_enable` (`is_enable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库机器人收益配置表';
```

#### 2.2 知识库机器人收益日志表 (cm_kb_robot_revenue_log)
```sql
CREATE TABLE `cm_kb_robot_revenue_log` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `robot_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机器人ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
  `sharer_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '分享者ID',
  `session_id` varchar(64) NOT NULL DEFAULT '' COMMENT '会话ID',
  `revenue_type` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '收益类型: 1=按比例分成, 2=固定金额',
  `total_cost` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '总消费金额',
  `share_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '分成金额',
  `platform_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '平台金额',
  `revenue_amount` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '收益金额',
  `settle_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '结算状态: 0=未结算, 1=已结算',
  `settle_time` int(10) UNSIGNED DEFAULT NULL COMMENT '结算时间',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_robot_id` (`robot_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_sharer_id` (`sharer_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_settle_status` (`settle_status`),
  KEY `idx_settle_sharer_time` (`settle_status`, `sharer_id`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库机器人收益日志表';
```

### 3. 角色示例表 (cm_role_example)
```sql
CREATE TABLE `cm_role_example` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '角色名称',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '角色头像',
  `description` text COMMENT '角色描述',
  `prompt` text NOT NULL COMMENT '角色提示词',
  `category` varchar(50) NOT NULL DEFAULT '' COMMENT '角色分类',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否显示: 1=是, 0=否',
  `use_count` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '使用次数',
  `create_time` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_is_show` (`is_show`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色示例表';
```

### 4. 会员模型限制视图 (v_member_model_limits)
```sql
CREATE OR REPLACE VIEW `v_member_model_limits` AS
SELECT 
    um.user_id,
    um.package_id,
    mp.name as package_name,
    mp.model_limits,
    um.member_end_time,
    um.is_perpetual,
    CASE 
        WHEN um.is_perpetual = 1 THEN 1
        WHEN um.member_end_time > UNIX_TIMESTAMP() THEN 1
        ELSE 0
    END as is_valid
FROM cm_user_member um
LEFT JOIN cm_member_package mp ON um.package_id = mp.id
WHERE um.delete_time IS NULL;
```

## 🔧 新增字段

### 1. 用户表 (cm_user) 新增字段
```sql
ALTER TABLE `cm_user` 
ADD COLUMN IF NOT EXISTS `user_gift_config` text COMMENT '用户赠送配置' AFTER `total_amount`;
```

### 2. 知识库机器人表 (cm_kb_robot) 新增字段
```sql
ALTER TABLE `cm_kb_robot` 
ADD COLUMN IF NOT EXISTS `revenue_config_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '收益配置ID' AFTER `is_show`,
ADD COLUMN IF NOT EXISTS `revenue_enabled` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否启用收益: 1=是, 0=否' AFTER `revenue_config_id`;
```

### 3. 聊天记录表 (cm_chat_record) 新增字段
```sql
ALTER TABLE `cm_chat_record` 
ADD COLUMN IF NOT EXISTS `session_id` varchar(64) NOT NULL DEFAULT '' COMMENT '会话ID' AFTER `robot_id`,
ADD COLUMN IF NOT EXISTS `role` varchar(20) NOT NULL DEFAULT 'user' COMMENT '角色: user=用户, assistant=助手' AFTER `session_id`;
```

### 4. 用户会员表 (cm_user_member) 新增字段
```sql
ALTER TABLE `cm_user_member` 
ADD COLUMN IF NOT EXISTS `gift_from_user_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '赠送来源用户ID' AFTER `is_clear`,
ADD COLUMN IF NOT EXISTS `gift_log_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '赠送日志ID' AFTER `gift_from_user_id`;
```

## 📊 新增索引

### 1. 用户表 (cm_user) 新增索引
```sql
ALTER TABLE `cm_user` 
ADD INDEX IF NOT EXISTS `idx_inviter_id` (`inviter_id`),
ADD INDEX IF NOT EXISTS `idx_first_leader` (`first_leader`),
ADD INDEX IF NOT EXISTS `idx_second_leader` (`second_leader`);
```

### 2. 聊天记录表 (cm_chat_record) 新增索引
```sql
ALTER TABLE `cm_chat_record` 
ADD INDEX IF NOT EXISTS `idx_session_id` (`session_id`),
ADD INDEX IF NOT EXISTS `idx_role` (`role`);
```

### 3. 知识库机器人表 (cm_kb_robot) 新增索引
```sql
ALTER TABLE `cm_kb_robot` 
ADD INDEX IF NOT EXISTS `idx_revenue_config_id` (`revenue_config_id`),
ADD INDEX IF NOT EXISTS `idx_revenue_enabled` (`revenue_enabled`);
```

### 4. 用户会员表 (cm_user_member) 新增索引
```sql
ALTER TABLE `cm_user_member` 
ADD INDEX IF NOT EXISTS `idx_gift_from_user_id` (`gift_from_user_id`),
ADD INDEX IF NOT EXISTS `idx_gift_log_id` (`gift_log_id`);
```

## 📝 初始化数据

### 1. 用户赠送配置初始化
```sql
INSERT IGNORE INTO `cm_user_gift_config` (`id`, `name`, `gift_type`, `min_amount`, `max_amount`, `daily_limit`, `is_enable`, `remark`, `create_time`, `update_time`) VALUES
(1, '灵感值赠送', 1, 1.00, 1000.00, 10, 1, '用户间灵感值赠送配置', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '会员时长赠送', 2, 1.00, 365.00, 5, 1, '用户间会员时长赠送配置', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '对话次数赠送', 3, 1.00, 1000.00, 20, 1, '用户间对话次数赠送配置', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
```

### 2. 角色示例数据初始化
```sql
INSERT IGNORE INTO `cm_role_example` (`id`, `name`, `avatar`, `description`, `prompt`, `category`, `sort`, `is_show`, `use_count`, `create_time`, `update_time`) VALUES
(1, '智能助手', '/uploads/avatars/assistant.png', '专业的AI助手，可以回答各种问题', '你是一个专业的AI助手，请详细回答用户的问题。', '通用', 1, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '编程专家', '/uploads/avatars/programmer.png', '专业的编程助手，擅长各种编程语言', '你是一个编程专家，请帮助用户解决编程问题。', '技术', 2, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '文案写手', '/uploads/avatars/writer.png', '专业的文案创作者，擅长各种文体写作', '你是一个专业的文案写手，请帮助用户创作优质文案。', '创作', 3, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
```

## 升级标准流程

### 1. 升级前准备
```bash
# 数据库备份
mysqldump -u root -p chatmoney > backup_$(date +%Y%m%d_%H%M%S).sql

# 权限检查
SHOW GRANTS FOR CURRENT_USER;

# 磁盘空间检查
df -h /var/lib/mysql
```

### 2. 安全升级脚本
```sql
-- 事务开始
START TRANSACTION;

-- 创建表（安全模式）
CREATE TABLE IF NOT EXISTS cm_user_gift_log (
    id int(11) NOT NULL AUTO_INCREMENT,
    user_id int(11) NOT NULL COMMENT '赠送用户ID',
    target_user_id int(11) NOT NULL COMMENT '接收用户ID',
    amount decimal(10,2) NOT NULL COMMENT '赠送金额',
    create_time int(11) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_target_user_id (target_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户赠送日志表';

-- 添加字段（安全模式）
ALTER TABLE cm_user 
ADD COLUMN IF NOT EXISTS user_gift_config text COMMENT '用户赠送配置';

-- 创建索引（安全模式）
ALTER TABLE cm_user 
ADD INDEX IF NOT EXISTS idx_inviter_id (inviter_id);

-- 提交事务
COMMIT;
```

### 3. 升级验证
```sql
-- 验证表结构
DESCRIBE cm_user_gift_log;

-- 验证字段添加
SHOW COLUMNS FROM cm_user LIKE 'user_gift_config';

-- 验证索引创建
SHOW INDEX FROM cm_user WHERE Key_name = 'idx_inviter_id';
```

## 性能优化策略

### 1. 分区策略
```sql
-- 大表分区优化（按年份）
CREATE TABLE cm_kb_robot_revenue_log_partitioned (
    id int(11) NOT NULL AUTO_INCREMENT,
    -- 其他字段...
    create_time int(11) NOT NULL,
    PRIMARY KEY (id, create_time)
) ENGINE=InnoDB
PARTITION BY RANGE (YEAR(FROM_UNIXTIME(create_time))) (
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 2. 索引优化
```sql
-- 复合索引优化
ALTER TABLE cm_user_account_log 
ADD INDEX idx_user_type_time (user_id, change_type, create_time);

-- 覆盖索引优化
ALTER TABLE cm_chat_records 
ADD INDEX idx_session_role_time (session_id, role, create_time);
```

### 3. 查询优化
```sql
-- 慢查询优化示例
SELECT SQL_NO_CACHE 
    u.id, u.nickname, SUM(l.change_amount) as total_amount
FROM cm_user u 
INNER JOIN cm_user_account_log l ON u.id = l.user_id 
WHERE l.create_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY))
GROUP BY u.id, u.nickname
ORDER BY total_amount DESC 
LIMIT 100;
```

## 数据库监控与维护

### 1. 性能监控查询
```sql
-- 表空间使用情况
SELECT 
    table_schema,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'DB Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'chatmoney'
ORDER BY (data_length + index_length) DESC;

-- 索引使用统计
SELECT 
    OBJECT_SCHEMA,
    OBJECT_NAME,
    COUNT_STAR,
    COUNT_READ,
    COUNT_WRITE
FROM performance_schema.table_io_waits_summary_by_table 
WHERE OBJECT_SCHEMA = 'chatmoney'
ORDER BY COUNT_STAR DESC;
```

### 2. 定期维护任务
```sql
-- 表优化（定期执行）
OPTIMIZE TABLE cm_user_account_log;
OPTIMIZE TABLE cm_chat_records;

-- 索引重建
ALTER TABLE cm_user_account_log DROP INDEX idx_user_type_time;
ALTER TABLE cm_user_account_log ADD INDEX idx_user_type_time (user_id, change_type, create_time);
```

## 版本控制与回滚

### 1. 版本标记
```sql
-- 版本记录表
CREATE TABLE IF NOT EXISTS cm_database_version (
    id int(11) NOT NULL AUTO_INCREMENT,
    version varchar(50) NOT NULL COMMENT '版本号',
    description text COMMENT '版本描述',
    upgrade_sql text COMMENT '升级SQL',
    rollback_sql text COMMENT '回滚SQL',
    create_time int(11) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 记录版本信息
INSERT INTO cm_database_version (version, description, create_time) 
VALUES ('v2.1.0', '用户赠送功能和智能体分成功能', UNIX_TIMESTAMP());
```

### 2. 回滚脚本
```sql
-- 回滚脚本模板
START TRANSACTION;

-- 删除新增表
DROP TABLE IF EXISTS cm_user_gift_log;
DROP TABLE IF EXISTS cm_user_gift_config;

-- 删除新增字段
ALTER TABLE cm_user DROP COLUMN IF EXISTS user_gift_config;

-- 删除新增索引
ALTER TABLE cm_user DROP INDEX IF EXISTS idx_inviter_id;

COMMIT;
```

## 技术规范与最佳实践

### 1. 命名规范
- **表名**：统一使用`cm_`前缀，小写字母+下划线
- **字段名**：描述性命名，避免缩写
- **索引名**：`idx_`前缀+字段名组合

### 2. 数据类型规范
- **主键**：统一使用`int(11) AUTO_INCREMENT`
- **时间戳**：统一使用`int(11)`存储UNIX时间戳
- **金额**：使用`decimal(10,2)`确保精度
- **文本**：中文内容使用`utf8mb4`字符集

### 3. 安全规范
- **权限控制**：最小权限原则
- **备份策略**：每日自动备份+重大操作前手动备份
- **操作审计**：重要操作记录到审计日志

---

## 升级检查清单

### 升级前检查 ✓
- [ ] 数据库完整备份
- [ ] 权限验证
- [ ] 磁盘空间检查
- [ ] 应用服务停止

### 升级执行 ✓
- [ ] 执行升级脚本
- [ ] 验证表结构
- [ ] 验证数据完整性
- [ ] 性能测试

### 升级后验证 ✓
- [ ] 功能测试
- [ ] 性能监控
- [ ] 错误日志检查
- [ ] 用户验收测试

---

# 数据库升级脚本实战开发记录

## 升级脚本开发历程

### 第一阶段：数据库升级脚本完善 (2025-06-28)

#### 会话目标
完善chatmoney1数据库升级脚本，确保包含所有最新的数据库表和功能模块，实现chatmoney数据库向chatmoney1数据库的完整同步。

#### 核心成果

##### ✅ 数据库结构分析与补全
- **识别缺失表**：发现并添加了`cm_example_category`、`cm_example_content`、`cm_draw_prompt_example`三个重要表
- **结构查询**：通过Docker MySQL命令获取完整的表结构定义
- **字段映射**：确保所有字段类型、约束和索引完全匹配

##### ✅ 升级脚本优化
**新增11个核心功能表**：
- **用户赠送系统**（配置表、日志表）
- **智能体收益分成**（配置表、日志表、归档表）
- **示例系统**（分类表、内容表）
- **绘画提示词示例表**
- **角色示例表**
- **聊天记录表**

##### ✅ 功能模块集成
- **用户赠送功能**：完整的赠送配置、日志记录、菜单权限
- **智能体分成系统**：收益配置、日志记录、数据归档
- **示例内容管理**：分类管理、Q&A示例、绘画提示词
- **定时任务配置**：数据清理、收益结算、内容审核

##### ✅ 数据初始化
- **配置数据**：3类赠送配置（灵感值、会员时长、对话次数）
- **示例数据**：5个角色示例，6个示例分类，6个示例内容
- **提示词数据**：5个绘画提示词示例
- **定时任务**：5个自动化处理任务

#### 技术决策
1. **增量升级策略**：使用`CREATE TABLE IF NOT EXISTS`确保安全升级
2. **数据完整性**：使用`INSERT IGNORE`避免重复数据插入
3. **事务安全**：整个升级过程在事务中执行，确保原子性
4. **验证机制**：包含升级后的表和字段验证查询

#### 升级内容统计
- **新增表数量**：11个核心功能表
- **新增字段数量**：8个扩展字段
- **新增索引数量**：12个性能索引
- **新增菜单项**：7个管理界面
- **初始化数据**：22条基础数据
- **定时任务**：5个自动化任务

### 第二阶段：数据库升级脚本修正 (2025-06-28)

#### 会话目标
修正数据库升级脚本中的重复创建问题，删除chatmoney1数据库中已存在的表，确保升级脚本的准确性和安全性。

#### 核心发现

##### ✅ 表结构重复检查
- **数据库对比分析**：详细比较chatmoney和chatmoney1数据库中的example相关表差异
- **重复表识别**：发现`cm_draw_prompt_example`表在两个数据库中都存在
- **差异表确认**：确认只有`cm_example_category`和`cm_example_content`需要新增到chatmoney1

##### ✅ 升级脚本修正
- **删除重复创建**：移除`cm_draw_prompt_example`表的创建语句（chatmoney1已存在）
- **删除重复数据**：移除绘画提示词示例的初始化数据插入语句
- **调整序号**：重新调整表创建部分的序号

##### ✅ 统计信息更新
- **新增表数量**：从11个修正为10个
- **初始化数据**：从22条修正为17条（删除5条绘画提示词示例）
- **保持准确性**：确保统计信息与实际修改内容一致

#### 数据库差异分析
**chatmoney数据库特有**：
- ❌ `cm_example_category` (仅chatmoney有)
- ❌ `cm_example_content` (仅chatmoney有)
- ❌ `cm_role_example` (仅chatmoney有)

**chatmoney1数据库特有**：
- ❌ `cm_kb_example` (仅chatmoney1有)
- ❌ `cm_kb_example_category` (仅chatmoney1有)

**两数据库共有**：
- ✅ `cm_draw_prompt_example` (两数据库都有)

#### 修正策略
1. **安全原则**：不创建已存在的表，避免SQL执行错误
2. **增量更新**：只同步chatmoney中新增的表和数据到chatmoney1
3. **保持完整**：确保所有必需的新表和功能都包含在升级脚本中

### 第三阶段：发现遗漏表并完善升级脚本 (2025-06-28)

#### 会话目标
通过全面对比两个数据库的所有表结构，发现并补充了之前遗漏的重要表，确保升级脚本的完整性和准确性。

#### 核心发现

##### ✅ 全面数据库表对比
- **完整表列表对比**：获取并对比chatmoney和chatmoney1数据库的所有表
- **差异识别**：使用diff和comm命令精确识别表差异
- **数据量检查**：验证遗漏表的数据重要性

##### ✅ 发现重要遗漏表
1. **`cm_decorate_page_backup`** - 装饰页面备份表 (1条数据)
   - 页面配置备份功能的核心表
   - 存储装饰页面的备份数据

2. **`cm_template`** - 模板表 (5条数据)
   - 模板管理功能的核心表
   - 关联示例分类，提供模板下载功能

3. **`cm_user_chat_log`** - 用户聊天日志表 (100条数据)
   - 用户聊天记录的备份表
   - 包含用户消息和AI回复的完整日志

##### ✅ 完善升级脚本
- **添加遗漏表结构**：根据数据库字段信息重建表结构
- **优化索引设计**：为新增表添加合适的索引
- **更新统计信息**：表数量从10个修正为13个

#### 完整数据库同步策略
1. **完整性检查**：确保所有chatmoney有的表都同步到chatmoney1
2. **数据价值评估**：验证遗漏表的数据重要性和功能价值
3. **结构完整重建**：基于字段信息准确重建表结构

### 第四阶段：智能体分成系统功能补充

#### 会话目标
补充升级脚本，添加智能体广场商业化功能的缺失字段。

#### 核心发现

##### ✅ 字段差异分析
发现`cm_kb_robot_square`表在chatmoney和chatmoney1之间存在重要字段差异：
- **chatmoney版本**：包含`total_revenue`和`use_count`字段（商业化功能）
- **chatmoney1版本**：缺少这两个关键字段

##### ✅ 补充升级脚本
**新增字段（2.5节）**：
```sql
-- 2.5 为cm_kb_robot_square表添加新字段（智能体广场商业化功能）
ALTER TABLE `cm_kb_robot_square` 
ADD COLUMN IF NOT EXISTS `total_revenue` decimal(15,7) UNSIGNED NOT NULL DEFAULT '0.0000000' COMMENT '总收益金额' AFTER `verify_result`,
ADD COLUMN IF NOT EXISTS `use_count` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '使用次数' AFTER `total_revenue`;
```

**新增索引（3.5节）**：
```sql
-- 3.5 为cm_kb_robot_square表添加索引（智能体广场商业化功能）
ALTER TABLE `cm_kb_robot_square` 
ADD INDEX IF NOT EXISTS `idx_total_revenue` (`total_revenue`),
ADD INDEX IF NOT EXISTS `idx_use_count` (`use_count`),
ADD INDEX IF NOT EXISTS `idx_revenue_count` (`total_revenue`, `use_count`);
```

##### ✅ 统计信息更新
- **新增字段数量**：从8个更新为10个（含智能体广场商业化字段）
- **新增索引数量**：从15个更新为18个（含智能体广场商业化索引）

#### 业务价值
1. **商业化支持**：支持智能体创作者的收益分成计算
2. **数据分析**：提供使用热度分析基础数据
3. **运营决策**：为平台运营提供关键指标

### 第五阶段：会员套餐子模型功能补充

#### 会话目标
比较`cm_member_package_apply`表在两个数据库中的异同，并补充缺失字段。

#### 核心发现

##### ⚠️ 重要表结构差异
**chatmoney比chatmoney1功能更完善**：

| 数据库 | 字段数量 | 缺失字段 | 影响功能 |
|--------|----------|----------|----------|
| **chatmoney** | **8个字段** | 完整 | 支持子模型绑定 |
| **chatmoney1** | **7个字段** | `sub_model_id` | 仅支持通用套餐 |

**缺失字段详情**：
- **字段名**：`sub_model_id`
- **类型**：`int(10) DEFAULT '0'`
- **注释**：`子模型ID(0=通用模型,>0=指定子模型)`

##### ✅ 补充升级脚本
**新增字段（2.6节）**：
```sql
-- 2.6 为cm_member_package_apply表添加新字段（会员套餐子模型功能）
ALTER TABLE `cm_member_package_apply` 
ADD COLUMN IF NOT EXISTS `sub_model_id` int(10) DEFAULT '0' COMMENT '子模型ID(0表示大类限制)' AFTER `channel`;
```

**新增复合索引（3.6节）**：
```sql
-- 3.6 为cm_member_package_apply表添加复合索引（会员套餐子模型功能）
CREATE INDEX IF NOT EXISTS `idx_package_type_submodel` ON `cm_member_package_apply` (`package_id`, `type`, `sub_model_id`);
CREATE INDEX IF NOT EXISTS `idx_type_channel_submodel` ON `cm_member_package_apply` (`type`, `channel`, `sub_model_id`);
```

##### ✅ 最终统计信息
- **新增字段数量**：从10个更新为**11个**（含会员套餐子模型字段）
- **新增索引数量**：从18个更新为**20个**（含会员套餐子模型索引）

#### 业务功能差异

##### chatmoney（新版）功能特征
- **精细化权限控制**：支持会员套餐与特定AI子模型绑定
- **灵活配置**：可为不同子模型设置不同的权限和限额
- **查询优化**：完善的索引支持高效的子模型查询
- **扩展性强**：为AI模型差异化定价奠定基础

##### chatmoney1（旧版）功能特征
- **通用权限控制**：仅支持通用的会员套餐配置
- **功能相对简单**：缺乏子模型级别的精细化管理
- **查询性能一般**：缺少关键索引影响查询效率

## 升级脚本最终版本内容

### 📊 升级脚本统计信息
- **新增表数量**：13个核心功能表
- **新增字段数量**：11个扩展字段（含商业化和子模型功能）
- **新增索引数量**：20个性能索引（含商业化和子模型索引）
- **新增菜单项**：48个管理界面
- **配置更新**：5个新增配置项
- **初始数据**：17条基础数据
- **定时任务**：5个自动化任务
- **验证机制**：完整的升级验证
- **商业化功能**：智能体广场收益统计支持
- **子模型功能**：会员套餐精细化权限控制

### 🚀 标准执行流程
```bash
# 1. 备份现有数据库
docker exec chatmoney-mysql mysqldump -u root -p123456Abcd chatmoney1 > backup/chatmoney1_backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行升级脚本
docker exec -i chatmoney-mysql mysql -uroot -p123456Abcd chatmoney1 < upgrade_chatmoney1_to_latest.sql

# 3. 验证升级结果
docker exec chatmoney-mysql mysql -u root -p123456Abcd -e "SELECT COUNT(*) FROM cm_user_gift_config;" chatmoney1
```

### 🛡️ 安全保障措施
- **事务安全**：整个升级过程在事务中执行
- **条件创建**：使用`IF NOT EXISTS`避免重复执行错误
- **验证机制**：包含完整的升级后验证查询
- **回滚支持**：支持完整的升级回滚操作

### 💡 升级脚本最佳实践
1. **完整性验证**：始终对比源数据库和目标数据库的结构差异
2. **安全升级**：使用条件创建语句避免重复执行错误
3. **性能考虑**：为频繁查询的字段添加适当索引
4. **用户体验**：提供完整的管理界面和示例数据
5. **自动化运维**：配置定时任务实现数据自动清理和维护

---

# 知识库录入敏感词审核方案

## 📋 问题分析

### 当前状态
- ❌ **知识库录入无审核**: 用户可以录入敏感内容到知识库
- ⚠️ **安全风险**: 敏感内容通过知识库引用传播到对话中
- 🔍 **影响范围**: 所有使用该知识库的智能体对话

### 录入场景分析
1. **单条录入**: `KbTeachLogic::insert()` - 手动录入问答对
2. **批量导入**: `KbTeachLogic::import()` - 文件导入、CSV导入、QA拆分
3. **对话录入**: `KbChatLogic::dataRevise()` - 从对话中录入

## 🎯 推荐方案

### 方案一：录入时同步审核（推荐）

#### 优点
- ✅ **源头控制**: 从录入环节就阻止敏感内容
- ✅ **用户体验**: 立即反馈，用户可及时修改
- ✅ **系统效率**: 避免重复审核，节省资源
- ✅ **数据质量**: 确保知识库内容的合规性

#### 实现方式
```php
// 在录入前进行审核
public static function insert(array $post, int $userId): bool
{
    try {
        $question = $post['question'] ?? '';
        $answer = $post['answer'] ?? '';
        
        // 敏感词审核
        WordsService::sensitive($question . ' ' . $answer);
        
        // 百度内容审核（可选）
        WordsService::askCensor($question . ' ' . $answer);
        
        // 继续原有录入逻辑...
    } catch (Exception $e) {
        self::setError($e->getMessage());
        return false;
    }
}
```

### 方案二：异步队列审核

#### 优点
- ✅ **性能优化**: 不阻塞录入流程
- ✅ **批量处理**: 适合大量数据导入
- ✅ **错误恢复**: 审核失败可重试

#### 实现方式
```php
// 录入时先保存，标记为待审核
$modelKbEmbedding->insert([
    // ... 其他字段
    'status' => KnowEnum::AUDIT_WAIT,  // 新增审核状态
]);

// 推送到审核队列
BaseQueue::pushAudit(['uuid' => $uuid]);
```

## 📊 性能影响分析

### 敏感词检测性能

#### 单次检测耗时
- **内置敏感词库**: 1075个词汇
- **DFA算法复杂度**: O(n) - n为文本长度
- **预估耗时**: 
  - 100字符文本: ~1-3ms
  - 1000字符文本: ~5-15ms
  - 10000字符文本: ~50-150ms

#### 百度内容审核耗时
- **API调用**: ~200-500ms
- **网络延迟**: 取决于网络状况
- **并发限制**: 需要考虑QPS限制

### 批量导入影响

#### 场景分析
```
批量导入1000条记录：
- 同步审核: 1000 × 10ms = 10秒 (敏感词) + 1000 × 300ms = 5分钟 (百度审核)
- 异步审核: 录入1秒 + 后台处理5分钟
```

#### 优化策略
1. **分批处理**: 每批50-100条记录
2. **并发审核**: 多线程处理审核任务
3. **缓存优化**: 相同内容避免重复审核
4. **智能跳过**: 纯数字、英文等低风险内容快速通过

## 🔧 具体实现方案

### 方案一实现（推荐）

#### 1. 修改录入逻辑
```php
// server/app/api/logic/kb/KbTeachLogic.php

public static function insert(array $post, int $userId): bool
{
    try {
        $question = trim($post['question'] ?? '');
        $answer = trim($post['answer'] ?? '');
        
        // 内容验证
        if (!$question || !$answer) {
            throw new Exception('问题和答案不能为空');
        }
        
        // 敏感词审核
        self::auditContent($question . ' ' . $answer);
        
        // 继续原有逻辑...
    } catch (Exception $e) {
        self::setError($e->getMessage());
        return false;
    }
}

private static function auditContent(string $content): void
{
    // 敏感词检测
    WordsService::sensitive($content);
    
    // 百度内容审核（可配置开关）
    $enableBaiduAudit = ConfigService::get('kb_audit', 'enable_baidu', 0);
    if ($enableBaiduAudit) {
        WordsService::askCensor($content);
    }
}
```

## ⚙️ 配置管理

### 新增配置项
```php
// 知识库审核配置
'kb_audit' => [
    'enable_sensitive' => 1,    // 启用敏感词审核
    'enable_baidu' => 0,        // 启用百度内容审核
    'batch_size' => 50,         // 批量处理大小
    'async_threshold' => 100,   // 异步处理阈值
]
```

## 💡 最终建议

**推荐采用方案一（录入时同步审核）**，理由：

1. **安全优先**: 从源头控制敏感内容
2. **用户体验**: 立即反馈，便于修改
3. **实现简单**: 改动最小，风险最低
4. **性能可控**: 敏感词检测耗时很短，可接受

---

# 5万条敏感词库性能影响分析报告

## 📊 测试概述

本报告基于实际性能测试，分析了从1000条到50000条敏感词对系统性能的影响，包括构建时间、检测时间、内存使用和并发能力等关键指标。

## 🔍 测试环境

- **服务器配置**: Linux 5.10.134-19.al8.x86_64
- **PHP版本**: 8.0.30.3
- **测试算法**: DFA (Deterministic Finite Automaton)
- **测试规模**: 1000, 5000, 10000, 20000, 50000 条敏感词
- **测试文本**: 短文本(13字符)、中等文本(280字符)、长文本(2200字符)、超长文本(6500字符)

## 📈 关键性能指标

### 1. 敏感词树构建时间

| 敏感词数量 | 构建时间 | 增长倍数 | 性能评估 |
|-----------|---------|---------|---------|
| 1,000     | 2.5 ms  | 1x      | ✅ 优秀 |
| 5,000     | 13.7 ms | 5.5x    | ✅ 良好 |
| 10,000    | 30.4 ms | 12.2x   | ✅ 可接受 |
| 20,000    | 71.8 ms | 28.9x   | ⚠️ 需注意 |
| 50,000    | 231.3 ms| 93.2x   | ⚠️ 需优化 |

**关键发现**：
- 构建时间与敏感词数量呈**非线性增长**
- 50000条敏感词构建时间约231ms，仍在可接受范围内
- 构建时间主要影响系统启动和敏感词库更新

### 2. 敏感词检测时间

| 文本类型 | 1000词 | 5000词 | 10000词 | 20000词 | 50000词 | 性能影响 |
|---------|-------|-------|--------|--------|--------|---------|
| 短文本   | 0.017ms| 0.015ms| 0.011ms | 0.008ms | 0.009ms | ✅ 无影响 |
| 中等文本 | 0.277ms| 0.247ms| 0.240ms | 0.303ms | 0.238ms | ✅ 无影响 |
| 长文本   | 10.5ms | 10.5ms | 10.9ms  | 10.4ms  | 10.3ms  | ✅ 稳定 |
| 超长文本 | 85.5ms | 84.5ms | 84.2ms  | 95.9ms  | 85.4ms  | ✅ 稳定 |

**关键发现**：
- 检测时间主要取决于**文本长度**，与敏感词数量关系不大
- DFA算法的O(n)时间复杂度特性得到验证
- 即使50000条敏感词，检测性能依然稳定

### 3. 内存使用情况

| 敏感词数量 | 构建内存 | 总内存占用 | 内存效率 |
|-----------|---------|-----------|---------|
| 1,000     | 776 KB  | 1.3 MB    | ✅ 优秀 |
| 5,000     | 3.1 MB  | 4.7 MB    | ✅ 良好 |
| 10,000    | 3.8 MB  | 9.0 MB    | ✅ 可接受 |
| 20,000    | 7.7 MB  | 17.4 MB   | ✅ 可接受 |
| 50,000    | 22.8 MB | 42.1 MB   | ⚠️ 需监控 |

**关键发现**：
- 内存使用与敏感词数量呈**线性增长**
- 50000条敏感词约占用42MB内存，对现代服务器影响较小
- 平均每1000条敏感词约占用0.8MB内存

## 🏁 结论

基于详细的性能测试和分析，**5万条敏感词库对系统性能的影响是可控和可接受的**：

### ✅ 优势
1. **检测性能稳定**: 检测时间主要取决于文本长度，与敏感词数量无关
2. **内存使用合理**: 42MB内存占用对现代服务器影响较小
3. **算法效率高**: DFA算法保证了O(n)的时间复杂度
4. **扩展性良好**: 可通过优化策略进一步提升性能

### 🎯 最终建议
**推荐部署5万条敏感词库**，同时实施相应的优化措施，可以在保证系统性能的前提下，显著提升内容安全防护能力。

---

# 模型删除保护机制实施指南

## 🎯 实施目标

建立完善的模型删除保护机制，确保在删除AI模型时不会影响现有的会员套餐配置，避免数据不一致和业务中断。

## 📋 实施步骤

### 第一阶段：后端保护逻辑

#### 1.1 创建检查服务类
```bash
# 创建模型删除检查服务
cp model_member_package_check_solution.php server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php
```

#### 1.2 修改现有删除逻辑
```php
// 在 server/app/adminapi/logic/setting/ai/AiModelsLogic.php 中修改 del 方法
public static function del(int $id): bool
{
    try {
        // 1. 检查模型是否存在
        $model = new Models();
        $detail = $model->where(['id'=>$id])->findOrEmpty()->toArray();

        if (!$detail) {
            throw new Exception('模型已不存在!');
        }

        // 2. 检查会员套餐限制 - 新增检查
        $checkResult = ModelDeletionChecker::checkMainModelDeletion($id);
        
        if (!$checkResult['can_delete']) {
            throw new Exception($checkResult['message']);
        }

        // 3. 检查其他使用情况（保留原有逻辑）
        // ... 原有的 checkModelCanDel 逻辑

        // 4. 执行删除
        Models::destroy($id);

        return true;
    } catch (Exception $e) {
        self::setError($e->getMessage());
        return false;
    }
}
```

### 第二阶段：前端交互优化

#### 2.1 创建删除确认组件
```bash
# 创建增强的删除确认对话框
cp model_deletion_frontend_enhancement.vue admin/src/components/ModelDeletionDialog.vue
```

#### 2.2 修改模型管理页面
```vue
<!-- 在模型列表中使用新的删除对话框 -->
<template>
  <!-- 原有的模型列表 -->
  <el-table>
    <!-- ... -->
    <el-table-column label="操作">
      <template #default="{ row }">
        <el-button 
          type="danger" 
          size="small"
          @click="showDeleteDialog(row)"
        >
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!-- 使用新的删除确认对话框 -->
  <ModelDeletionDialog
    v-model="deleteDialogVisible"
    :model="selectedModel"
    @deleted="handleModelDeleted"
  />
</template>
```

### 第三阶段：数据库优化（可选）

如果要实现具体子模型限制，需要执行数据库迁移：

```sql
-- 添加子模型字段
ALTER TABLE `cm_member_package_apply` 
ADD COLUMN `sub_model_id` int(10) DEFAULT 0 COMMENT '子模型ID(0表示大类限制)' AFTER `channel`;

-- 添加索引
ALTER TABLE `cm_member_package_apply` 
ADD INDEX `idx_package_type_submodel` (`package_id`, `type`, `sub_model_id`);
```

## 🧪 测试方案

### 测试用例1：模型未被使用
```bash
# 测试场景：删除未被任何会员套餐使用的模型
# 期望结果：可以正常删除，显示"该模型可以安全删除"
```

### 测试用例2：模型被会员套餐使用
```bash
# 测试场景：删除被会员套餐限制的模型
# 期望结果：阻止删除，显示具体的套餐信息和操作建议
```

## 📈 效果评估

### 成功指标
- ✅ 零数据不一致事件
- ✅ 管理员操作满意度提升
- ✅ 删除操作错误率降低
- ✅ 系统稳定性提升

这个实施方案确保了模型删除操作的安全性和用户体验，同时保持了系统的稳定性和数据一致性。

---

# NewAI功能融合报告

## 融合概述

本次融合将`newai`目录中的最新升级功能安全地集成到现有系统中，主要新增了**用户信息审核系统**和相关功能。

## 主要新增功能

### 1. 用户信息审核系统
- **功能描述**：自动审核用户头像和昵称，违规内容将被替换
- **审核频率**：每30分钟执行一次定时任务
- **技术实现**：基于百度AI内容审核API

### 2. 多种内容审核支持
- **问题审核**：对用户提问进行预审核
- **内容审核**：对生成内容进行后审核
- **提示词审核**：对绘画提示词进行审核
- **图片审核**：对绘画生成图片进行审核
- **用户信息审核**：对用户头像和昵称进行审核
- **上传图片审核**：对用户上传的图片进行审核

### 3. 管理后台功能
- **内容审核配置页面**：统一管理所有审核开关和配置
- **百度AI配置**：支持配置百度AI的APPID、API Key、Secret Key

## 融合详情

### 数据库升级

#### 结构变更
```sql
-- 为用户表添加审核相关字段
ALTER TABLE `cw_user` ADD COLUMN `censor_text_status` tinyint(1) NULL DEFAULT 0 COMMENT '审核状态  0-未审核 1-已审核';
ALTER TABLE `cw_user` ADD COLUMN `censor_text_result` text NULL COMMENT '审核结果';
ALTER TABLE `cw_user` ADD COLUMN `censor_image_status` tinyint(1) NULL DEFAULT 0 COMMENT '审核状态  0-未审核 1-已审核';
ALTER TABLE `cw_user` ADD COLUMN `censor_image_result` text NULL COMMENT '审核结果';

-- 修改知识库机器人记录表的ask字段类型
ALTER TABLE `cw_kb_robot_record` MODIFY COLUMN `ask` longtext NULL COMMENT '提问';

-- 为视频记录表添加高级参数字段
ALTER TABLE `cw_video_record` ADD COLUMN `complex_params` text NULL COMMENT '高级参数';
```

#### 数据更新
```sql
-- 添加用户信息审核定时任务
INSERT INTO `cw_dev_crontab` VALUES ('用户信息审核', 1, 0, '审核用户头像及昵称', 'user_info_censor', '', 1, '*/30 * * * *', ...);
```

### 后端功能融合

#### 新增文件
- ✅ `server/app/common/command/UserInfoCensor.php` - 用户信息审核命令
- ✅ `server/app/common/command/QueryVideo.php` - 查询视频命令（更新）

#### 修改文件
- ✅ `server/app/common/service/UserService.php` - 添加用户信息审核方法
- ✅ `server/app/common/enum/user/UserEnum.php` - 添加审核状态常量
- ✅ `server/app/common/service/UploadService.php` - 添加上传图片审核功能
- ✅ `server/app/adminapi/logic/setting/ContentCensorLogic.php` - 添加新审核配置项

### 前端功能融合

#### 管理后台
- ✅ `admin/src/views/ai_setting/examine/index.vue` - 内容审核配置页面
- ✅ `admin/src/api/ai_setting/examine.ts` - 内容审核API接口（已存在）

#### 配置界面包含
- 问题审核开关
- 内容审核开关
- 提示词审核开关
- 图片审核开关
- **用户信息审核开关**（新增）
- **上传图片审核开关**（新增）
- 百度AI配置参数

### 技术特性

#### 审核状态定义
```php
const CENSOR_STATUS_WAIT = 0;           // 待审核
const CENSOR_STATUS_PASS = 1;           // 审核通过
const CENSOR_STATUS_NON_COMPLIANCE = 2; // 不合规
const CENSOR_STATUS_FAIL = 3;           // 审核失败
```

#### 审核流程
1. **用户信息审核**：
   - 定时任务每30分钟执行
   - 审核待审核或失败的用户信息
   - 违规昵称替换为"用户XXXXX"
   - 违规头像替换为默认头像

2. **上传图片审核**：
   - 用户上传图片时实时审核
   - 违规图片直接阻止上传
   - 审核失败记录日志

## 安全性考虑

### 数据安全
- ✅ 使用ALTER语句添加字段，不影响现有数据
- ✅ 新增字段均有默认值，兼容现有记录
- ✅ 保留原有业务逻辑，仅添加新功能

### 功能安全
- ✅ 审核功能可单独开关，不影响主业务
- ✅ 审核失败时有降级处理，不阻断业务流程
- ✅ 详细的错误日志记录，便于问题排查

### 兼容性
- ✅ 向后兼容，不破坏现有二次开发内容
- ✅ 新增功能独立封装，不影响原有模块
- ✅ 数据库变更向下兼容

## 使用指南

### 开启审核功能
1. 登录管理后台
2. 进入 **AI设置** > **内容审核**
3. 配置百度AI参数（APPID、API Key、Secret Key）
4. 开启所需的审核功能开关
5. 保存配置

### 审核配置说明
- **问题审核**：开启后，用户提问会预先审核
- **内容审核**：开启后，AI生成内容会后审核
- **提示词审核**：开启后，绘画提示词会实时审核
- **图片审核**：开启后，生成图片会后审核
- **用户信息审核**：开启后，用户头像和昵称会定时审核
- **上传图片审核**：开启后，用户上传图片会实时审核

## 融合状态总结

| 功能模块 | 状态 | 备注 |
|---------|------|------|
| 数据库结构升级 | ✅ 完成 | 需手动执行SQL脚本 |
| 用户信息审核命令 | ✅ 完成 | 每30分钟执行一次 |
| 上传图片审核 | ✅ 完成 | 实时审核用户上传 |
| 管理后台配置页面 | ✅ 完成 | 支持全部审核开关 |
| 后端API接口 | ✅ 完成 | 配置读取和保存 |
| 用户服务增强 | ✅ 完成 | 添加审核方法 |
| 枚举类型定义 | ✅ 完成 | 审核状态常量 |

## 风险评估

### 低风险项
- ✅ 数据库字段添加（不影响现有数据）
- ✅ 新增独立功能模块
- ✅ 可选功能开关

### 注意事项
1. **百度AI费用**：开启审核功能会产生API调用费用
2. **审核延迟**：定时审核有最多30分钟的延迟
3. **网络依赖**：审核功能依赖百度AI服务可用性

---

*文档最后更新时间：2025-01-27*  
*维护团队：AI系统开发组*  
*技术状态：✅ 数据库管理体系完善，升级流程标准化* 