# 智能体分成记录清理功能实施指南

## 🎯 功能概述

基于现有的对话记录清理机制，为智能体分成记录实现定期清理功能，解决长期运行后分成记录数据过多的问题。

## 📋 功能特性

### ✅ **清理策略**
- **默认保留期**：365天（一年）
- **清理模式**：支持删除和归档两种模式
- **选择性清理**：可选择只清理未结算记录，保留已结算记录
- **批量处理**：支持大数据量分批处理，避免数据库压力

### ✅ **安全保障**
- **预览模式**：支持dry-run，先预览后执行
- **数据归档**：可选择归档到历史表而非直接删除
- **详细日志**：完整的操作日志记录
- **异常处理**：完善的错误处理和回滚机制

## 🚀 实施步骤

### 1. 部署清理命令文件

```bash
# 将命令文件放到正确位置
cp revenue_cleanup_command.php server/app/common/command/RevenueRecordCleanup.php
```

### 2. 注册命令到系统

编辑 `server/config/console.php` 文件，添加命令注册：

```php
<?php
return [
    'commands' => [
        // ... 现有命令 ...
        'revenue:cleanup' => app\common\command\RevenueRecordCleanup::class,
    ]
];
```

### 3. 执行定时任务配置

```bash
# 连接MySQL并执行配置
docker exec chatmoney-mysql mysql -u root -p123456Abcd chatmoney < revenue_cleanup_crontab.sql
```

### 4. 验证部署结果

```bash
# 检查命令是否注册成功
docker exec chatmoney-php php think list | grep revenue

# 检查定时任务是否配置成功
docker exec chatmoney-mysql mysql -u root -p123456Abcd -e "SELECT name, command, params, expression, status FROM cm_dev_crontab WHERE command = 'revenue:cleanup';" chatmoney
```

## 🧪 功能测试

### 1. 预览模式测试

```bash
# 预览将要清理的记录数量（不实际执行）
docker exec chatmoney-php php think revenue:cleanup --dry-run

# 预览清理365天以上的记录
docker exec chatmoney-php php think revenue:cleanup --days=365 --dry-run

# 预览只清理未结算记录
docker exec chatmoney-php php think revenue:cleanup --days=365 --keep-settled --dry-run
```

### 2. 小批量测试

```bash
# 先测试清理少量旧记录
docker exec chatmoney-php php think revenue:cleanup --days=730 --batch-size=10

# 检查清理结果
docker exec chatmoney-mysql mysql -u root -p123456Abcd -e "SELECT COUNT(*) as total FROM cm_kb_robot_revenue_log WHERE create_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 730 DAY));" chatmoney
```

### 3. 归档模式测试

```bash
# 测试归档功能（推荐用于重要数据）
docker exec chatmoney-php php think revenue:cleanup --days=365 --archive --keep-settled --batch-size=100

# 检查归档表数据
docker exec chatmoney-mysql mysql -u root -p123456Abcd -e "SELECT COUNT(*) FROM cm_kb_robot_revenue_log_archive;" chatmoney
```

## ⚙️ 配置选项详解

### 命令参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--days` | 365 | 保留天数，超过此天数的记录将被清理 |
| `--dry-run` | false | 预览模式，只显示将要删除的数量 |
| `--batch-size` | 1000 | 批处理大小，避免单次处理过多数据 |
| `--archive` | false | 归档模式，数据复制到历史表 |
| `--keep-settled` | false | 只清理未结算记录，保留已结算记录 |

### 定时任务配置

```sql
-- 推荐配置：每月清理，保留一年，只清理未结算
'0 3 1-7 * 0'  -- 每月第一个周日凌晨3点
--days=365 --keep-settled

-- 保守配置：归档模式，保留重要数据
'0 3 1-7 * 0'  -- 每月第一个周日凌晨3点  
--days=365 --archive --keep-settled

-- 激进配置：清理所有旧记录
'0 2 * * 0'    -- 每周日凌晨2点
--days=180
```

## 📊 监控和维护

### 1. 查看清理日志

```bash
# 查看清理日志
docker exec chatmoney-php cat /www/wwwroot/ai/server/runtime/log/revenue_cleanup.log

# 查看最近的清理记录
docker exec chatmoney-php tail -n 10 /www/wwwroot/ai/server/runtime/log/revenue_cleanup.log
```

### 2. 监控数据量变化

```sql
-- 查看分成记录总量
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN settle_status = 1 THEN 1 END) as settled_records,
    COUNT(CASE WHEN settle_status = 0 THEN 1 END) as unsettled_records,
    MIN(FROM_UNIXTIME(create_time)) as earliest_record,
    MAX(FROM_UNIXTIME(create_time)) as latest_record
FROM cm_kb_robot_revenue_log;

-- 查看归档表数据量
SELECT COUNT(*) as archived_records FROM cm_kb_robot_revenue_log_archive;

-- 分析数据分布
SELECT 
    DATE(FROM_UNIXTIME(create_time)) as date,
    COUNT(*) as daily_count,
    SUM(share_amount) as daily_amount
FROM cm_kb_robot_revenue_log 
WHERE create_time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY))
GROUP BY DATE(FROM_UNIXTIME(create_time))
ORDER BY date DESC;
```

### 3. 性能优化建议

```sql
-- 为清理操作添加索引
CREATE INDEX idx_revenue_cleanup ON cm_kb_robot_revenue_log (create_time, settle_status);

-- 检查索引使用情况
EXPLAIN SELECT * FROM cm_kb_robot_revenue_log 
WHERE create_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 365 DAY)) 
AND settle_status = 0 
LIMIT 1000;
```

## 🔧 故障排除

### 常见问题解决

1. **命令未找到错误**
   ```bash
   # 检查命令注册
   docker exec chatmoney-php php think list | grep revenue
   
   # 如果没有找到，检查console.php配置
   ```

2. **内存不足错误**
   ```bash
   # 减少批处理大小
   docker exec chatmoney-php php think revenue:cleanup --batch-size=100
   ```

3. **数据库锁定错误**
   ```bash
   # 检查是否有其他进程在操作分成记录表
   docker exec chatmoney-mysql mysql -e "SHOW PROCESSLIST;" chatmoney
   ```

4. **归档表创建失败**
   ```sql
   -- 手动创建归档表
   CREATE TABLE cm_kb_robot_revenue_log_archive LIKE cm_kb_robot_revenue_log;
   ALTER TABLE cm_kb_robot_revenue_log_archive ADD COLUMN archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
   ```

## 📈 效果评估

### 清理前后对比

```sql
-- 执行清理前
SELECT 
    '清理前' as status,
    COUNT(*) as total_records,
    ROUND(SUM(LENGTH(CONCAT_WS('',id,user_id,sharer_id,robot_id,square_id,record_id,total_cost,share_amount,platform_amount,share_ratio,settle_status,settle_time,create_time,update_time)))/1024/1024, 2) as size_mb
FROM cm_kb_robot_revenue_log;

-- 执行清理后
SELECT 
    '清理后' as status,
    COUNT(*) as total_records,
    ROUND(SUM(LENGTH(CONCAT_WS('',id,user_id,sharer_id,robot_id,square_id,record_id,total_cost,share_amount,platform_amount,share_ratio,settle_status,settle_time,create_time,update_time)))/1024/1024, 2) as size_mb
FROM cm_kb_robot_revenue_log;
```

## 💡 最佳实践建议

### 1. **渐进式清理策略**
- 首次使用建议用归档模式
- 运行稳定后可切换到删除模式
- 定期备份重要的已结算记录

### 2. **数据保留原则**
- **已结算记录**：建议保留较长时间（1-2年）
- **未结算记录**：可适当缩短保留期（6个月-1年）
- **异常记录**：需要人工审查的记录单独处理

### 3. **监控告警**
- 设置清理失败告警
- 监控数据量异常增长
- 定期检查归档表完整性

## 🔄 与对话记录清理的对比

| 项目 | 对话记录清理 | 智能体分成记录清理 |
|------|------------|------------------|
| **清理周期** | 每周（半年数据） | 每月（一年数据） |
| **清理方式** | 软删除（标记删除） | 硬删除/归档 |
| **数据重要性** | 中等 | 高（涉及财务） |
| **恢复难度** | 容易 | 困难 |
| **存储影响** | 高 | 中等 |

这样设计确保了智能体分成记录既能得到有效清理，又能保障重要财务数据的安全性。 