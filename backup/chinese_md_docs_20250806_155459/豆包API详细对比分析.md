# 豆包API详细对比分析文档

基于[火山方舟大模型服务平台官方文档](https://www.volcengine.com/docs/82379/1494384)的API规范分析。

## 1. API概述

豆包大模型服务提供两种主要的对话API类型：
- **普通对话API** (`/chat/completions`) - 用于标准的文本生成模型
- **Bot对话API** (`/bots/chat/completions`) - 用于具有特殊能力的智能体模型（如联网检索、工具调用等）

## 2. API端点对比

### 2.1 普通对话API
```
POST https://ark.cn-beijing.volces.com/api/v3/chat/completions
```

### 2.2 Bot对话API  
```
POST https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions
```

**关键差异：** Bot API在路径中增加了 `/bots` 段，这是区分两种模型类型的关键标识。

## 3. 请求参数对比

### 3.1 普通对话API请求示例
```bash
curl https://ark.cn-beijing.volces.com/api/v3/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ARK_API_KEY" \
  -d '{
    "messages": [
        {
            "content": "You are a helpful assistant.",
            "role": "system"
        },
        {
            "content": "hello",
            "role": "user"
        }
    ],
    "model": "doubao-1-5-pro-32k-250115",
    "stream": true
}'
```

### 3.2 Bot对话API请求示例
```bash
curl 'https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions' \
  -H "Authorization: Bearer $ARK_API_KEY" \
  -H 'Content-Type: application/json' \
  -d '{
    "model": "bot-20250630160952-xphcl", 
    "stream": true,
    "stream_options": {"include_usage": true},
    "messages": [  
        {
            "role": "system",
            "content": "You are a helpful assistant."
        },
        {
            "role": "user",
            "content": "Hello!"
        }
    ]
}'
```

### 3.3 请求参数差异分析

| 参数 | 普通对话API | Bot对话API | 说明 |
|------|-------------|------------|------|
| `model` | `doubao-1-5-pro-32k-250115` | `bot-20250630160952-xphcl` | Bot模型通常以`bot-`前缀命名 |
| `stream` | `true` | `true` | 两者都支持流式输出 |
| `stream_options` | ❌ 不需要 | ✅ **必需**: `{"include_usage": true}` | Bot模型的关键区别参数 |
| `messages` | 标准格式 | 标准格式 | 消息格式相同 |

**关键发现：** `stream_options` 是Bot模型独有的必需参数，普通模型无此参数。

## 4. 响应格式分析

### 4.1 普通对话API响应格式
基于官方文档提供的流式响应示例：

```json
{"choices":[{"delta":{"content":"Hello","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":"!","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":" How","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":" can","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":" I","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":" help","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":" you","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":" today","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":"?","role":"assistant"},"index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

{"choices":[{"delta":{"content":"","role":"assistant"},"finish_reason":"stop","index":0}],"created":1742632436,"id":"021742632435712396f12d018b5d576a7a55349c2eba0815061fc","model":"doubao-1-5-pro-32k-250115","service_tier":"default","object":"chat.completion.chunk","usage":null}

[DONE]
```

### 4.2 响应字段结构分析

#### 4.2.1 标准响应字段
| 字段 | 类型 | 描述 | 示例值 |
|------|------|------|--------|
| `choices` | Array | 生成的选择数组 | `[{"delta":{"content":"Hello","role":"assistant"},"index":0}]` |
| `created` | Integer | 创建时间戳 | `1742632436` |
| `id` | String | 请求唯一标识 | `"021742632435712396f12d018b5d576a7a55349c2eba0815061fc"` |
| `model` | String | 使用的模型名称 | `"doubao-1-5-pro-32k-250115"` |
| `service_tier` | String | 服务层级 | `"default"` |
| `object` | String | 对象类型 | `"chat.completion.chunk"` |
| `usage` | Object | 使用情况统计 | `null` (流式响应中通常为null) |

#### 4.2.2 choices数组结构
```json
{
  "choices": [
    {
      "delta": {
        "content": "Hello",    // 生成的文本内容
        "role": "assistant"    // 角色类型
      },
      "index": 0,              // 选择的索引
      "finish_reason": null    // 完成原因（最后一个chunk中为"stop"）
    }
  ]
}
```

#### 4.2.3 流式响应特点
1. **逐词输出**：每个chunk包含一个或几个词汇
2. **角色延续**：每个chunk都带有`"role": "assistant"`
3. **索引一致**：所有chunk的`index`都为0
4. **结束标识**：最后一个chunk的`finish_reason`为`"stop"`
5. **结束符**：所有chunk后跟`[DONE]`标识流结束

### 4.3 Bot对话API响应格式推测

基于Bot模型的特殊能力，其响应格式可能包含以下额外字段：

#### 4.3.1 可能的特殊字段
```json
{
  "choices": [
    {
      "delta": {
        "content": "根据搜索结果...",
        "role": "assistant",
        "search_results": [           // 联网检索结果
          {
            "title": "搜索结果标题",
            "url": "https://example.com",
            "snippet": "搜索结果摘要"
          }
        ],
        "tool_calls": [               // 工具调用信息
          {
            "type": "web_search",
            "function": {
              "name": "search",
              "arguments": "{\"query\": \"用户查询\"}"
            }
          }
        ],
        "web_search": {               // 网络搜索状态
          "query": "用户查询",
          "status": "searching"
        }
      },
      "index": 0,
      "finish_reason": null
    }
  ],
  "created": 1742632436,
  "id": "bot-request-id",
  "model": "bot-20250630160952-xphcl",
  "service_tier": "default",
  "object": "chat.completion.chunk",
  "usage": {                        // 使用情况统计（因为有stream_options）
    "prompt_tokens": 20,
    "completion_tokens": 50,
    "total_tokens": 70
  }
}
```

## 5. 实现差异对比

### 5.1 URL构建差异
```php
// 普通模型
$url = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

// Bot模型
$url = 'https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions';
```

### 5.2 请求参数构建差异
```php
// 普通模型请求参数
$data = [
    'model' => 'doubao-1-5-pro-32k-250115',
    'stream' => true,
    'messages' => $messages,
    'temperature' => 0.9,
    'frequency_penalty' => 0
];

// Bot模型请求参数
$data = [
    'model' => 'bot-20250630160952-xphcl',
    'stream' => true,
    'stream_options' => ['include_usage' => true],  // 关键差异
    'messages' => $messages,
    'temperature' => 0.9,
    'frequency_penalty' => 0
];
```

### 5.3 响应处理差异
```php
// 普通模型响应处理
if (isset($parsedData['choices'][0]['delta']['content'])) {
    $content = $parsedData['choices'][0]['delta']['content'];
    // 处理文本内容
}

// Bot模型响应处理
if (isset($parsedData['choices'][0]['delta']['content'])) {
    $content = $parsedData['choices'][0]['delta']['content'];
    // 处理文本内容
}

// Bot模型特有字段处理
if (isset($parsedData['choices'][0]['delta']['search_results'])) {
    $searchResults = $parsedData['choices'][0]['delta']['search_results'];
    // 处理搜索结果
}

if (isset($parsedData['choices'][0]['delta']['tool_calls'])) {
    $toolCalls = $parsedData['choices'][0]['delta']['tool_calls'];
    // 处理工具调用
}
```

## 6. 错误处理对比

### 6.1 普通模型常见错误
```json
{
  "error": {
    "code": "invalid_request_error",
    "message": "Invalid model: doubao-1-5-pro-32k-250115",
    "param": "model",
    "type": "invalid_request_error"
  }
}
```

### 6.2 Bot模型特有错误
```json
{
  "error": {
    "code": "invalid_request_error",
    "message": "The model or endpoint bot-20250630160952-xphcl does not exist or you do not have access to it.",
    "param": "model",
    "type": "invalid_request_error"
  }
}
```

**关键差异：** Bot模型错误信息中明确提到了"endpoint"，说明Bot模型需要特定的API端点。

## 7. 超时和性能差异

### 7.1 性能特点对比
| 特性 | 普通模型 | Bot模型 |
|------|----------|---------|
| 响应速度 | 快速（秒级） | 较慢（需要联网检索） |
| 建议超时 | 301秒 | 600秒 |
| 网络依赖 | 低 | 高（需要访问外部资源） |
| 计算复杂度 | 中等 | 高（多步骤处理） |

### 7.2 超时设置建议
```php
// 根据模型类型设置超时
$timeout = $isBotModel ? 600 : 301;

curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);

// Bot模型需要更宽松的低速传输限制
if ($isBotModel) {
    curl_setopt($ch, CURLOPT_LOW_SPEED_LIMIT, 1);
    curl_setopt($ch, CURLOPT_LOW_SPEED_TIME, 60);  // 60秒内传输低于1字节/秒才超时
} else {
    curl_setopt($ch, CURLOPT_LOW_SPEED_LIMIT, 1);
    curl_setopt($ch, CURLOPT_LOW_SPEED_TIME, 30);  // 30秒内传输低于1字节/秒才超时
}
```

## 8. 最佳实践建议

### 8.1 模型识别策略
```php
function detectBotModel(string $model): bool {
    // 方法1：前缀识别（最可靠）
    if (str_starts_with($model, 'bot-')) {
        return true;
    }
    
    // 方法2：已知Bot模型列表
    $knownBotModels = [
        'bot-20250630160952-xphcl',
        // 添加其他已知Bot模型
    ];
    
    return in_array($model, $knownBotModels);
}
```

### 8.2 统一API调用接口
```php
function callDoubaoAPI(string $model, array $messages, bool $stream = true) {
    $isBotModel = detectBotModel($model);
    
    // 构建URL
    $baseUrl = 'https://ark.cn-beijing.volces.com/api/v3';
    $url = $isBotModel ? $baseUrl . '/bots/chat/completions' : $baseUrl . '/chat/completions';
    
    // 构建请求数据
    $data = [
        'model' => $model,
        'stream' => $stream,
        'messages' => $messages
    ];
    
    // Bot模型特有参数
    if ($isBotModel && $stream) {
        $data['stream_options'] = ['include_usage' => true];
    }
    
    // 发送请求
    return sendRequest($url, $data, $isBotModel);
}
```

### 8.3 响应解析统一处理
```php
function parseDoubaoResponse(string $response, bool $isBotModel): array {
    $parsedData = json_decode($response, true);
    
    if (!$parsedData || !isset($parsedData['choices'][0]['delta'])) {
        return ['error' => 'Invalid response format'];
    }
    
    $delta = $parsedData['choices'][0]['delta'];
    $result = [
        'content' => $delta['content'] ?? '',
        'role' => $delta['role'] ?? '',
        'finish_reason' => $parsedData['choices'][0]['finish_reason'] ?? null
    ];
    
    // Bot模型特有字段处理
    if ($isBotModel) {
        $result['search_results'] = $delta['search_results'] ?? null;
        $result['tool_calls'] = $delta['tool_calls'] ?? null;
        $result['web_search'] = $delta['web_search'] ?? null;
    }
    
    return $result;
}
```

## 9. 总结

### 9.1 关键差异总结
1. **API端点**：Bot模型需要使用 `/bots/chat/completions` 路径
2. **必需参数**：Bot模型必须包含 `stream_options: {include_usage: true}`
3. **响应格式**：Bot模型可能包含特殊字段如 `search_results`、`tool_calls`
4. **性能特点**：Bot模型响应时间更长，需要更长的超时设置
5. **错误处理**：Bot模型错误信息中会提到"endpoint"关键词

### 9.2 实现建议
1. **自动识别**：基于模型名称前缀自动识别Bot模型
2. **差异化处理**：根据模型类型使用不同的API端点和参数
3. **超时优化**：Bot模型使用更长的超时时间
4. **响应解析**：处理Bot模型特有的响应字段
5. **错误处理**：针对Bot模型的特殊错误进行专门处理

通过以上分析，我们可以确保系统能够正确处理两种不同类型的豆包模型，提供稳定可靠的AI对话服务。

---

*基于火山方舟大模型服务平台官方文档分析 - 2025年7月1日* 