# cm_creation_model 表 Content 与 Form 字段关系深度分析

## 核心关系概述

在 `cm_creation_model` 表中，`content`（主题内容）和 `form`（表单数据）字段构成了一个完整的**动态提示词模板系统**。这两个字段通过**变量替换机制**实现了灵活的AI创作功能。

## 字段定义与作用

### 1. Content 字段
- **数据类型**: `text`
- **作用**: 存储提示词模板，包含变量占位符
- **格式**: 文本内容 + 变量占位符 `${variable_name}`

### 2. Form 字段  
- **数据类型**: `text`（存储JSON格式）
- **作用**: 定义用户输入界面的表单配置
- **格式**: JSON数组，包含多个表单组件配置

## 变量替换机制

### 1. 核心实现逻辑

**文件位置**: `server/app/api/logic/chat/ChatDialogLogic.php`

```php
// 对话问题替换,方便保存到数据库
$this->question = $this->modelContent['content'];
foreach ($this->modelContent['form'] as $formVal) {
    $field = $formVal['props']['field']??'';
    $form  = $this->form[$field] ?? '';
    if ($formVal['props']['isRequired'] && empty($form)) {
        throw new Exception('请输入：'.$formVal['props']['title'].'的内容');
    }
    if(is_array($form)){
        $form = implode('、',$form);
    }
    $replaceStr     = '${'.$field.'}';
    $this->question = str_replace($replaceStr,$form,$this->question);
    $inputAsk .= $formVal['props']['title'].':'.$form.'；';
}
```

### 2. 替换流程

1. **获取模板**: 从 `content` 字段获取提示词模板
2. **遍历表单**: 循环处理 `form` 字段中的每个表单组件
3. **获取用户输入**: 从用户提交的表单数据中获取对应字段的值
4. **必填验证**: 检查必填字段是否有值
5. **数组处理**: 如果是数组类型（如多选），用"、"连接
6. **变量替换**: 使用 `str_replace()` 将 `${field}` 替换为实际值
7. **生成最终提示词**: 完成所有替换后得到最终的AI提示词

## 数据结构分析

### 1. Content 示例分析

以"周报日报"模型为例：
```text
根据我输入的${ljju8wlo}，使用下面提供的文本作为${lja6u9f7}的基础，生成一个简要的${lja6u9f7}，突出最重要的要点。${lja6u9f7}应以中文书写，且应易于阅读和理解。请首先编辑以下文本：${ljczht8s}
```

变量分析：
- `${ljju8wlo}`: 职位名称
- `${lja6u9f7}`: 生成类型（日报/周报/月报/汇总）
- `${ljczht8s}`: 工作内容

### 2. Form 结构分析

对应的 Form 配置：
```json
[
  {
    "name": "WidgetInput",
    "title": "单行文本", 
    "id": "lm5rj8y3",
    "props": {
      "field": "ljju8wlo",
      "title": "职位名称",
      "defaultValue": "",
      "placeholder": "新媒体运营",
      "maxlength": 200,
      "isRequired": true
    }
  },
  {
    "name": "WidgetTextarea",
    "title": "多行文本",
    "id": "lm5rj8y5", 
    "props": {
      "field": "ljczht8s",
      "title": "工作内容",
      "placeholder": "1.剪辑抖音视频8个 2.拍摄试镜2个 3.拍摄产品细节视频",
      "rows": 4,
      "defaultValue": "",
      "maxlength": 200,
      "autosize": false,
      "isRequired": true
    }
  },
  {
    "name": "WidgetRadio",
    "title": "单选",
    "id": "lm5rj8y7",
    "props": {
      "field": "lja6u9f7", 
      "title": "生成类型",
      "options": ["日报", "周报", "月报", "汇总"],
      "defaultValue": "日报",
      "isRequired": true
    }
  }
]
```

## 实际应用示例

### 示例1: 工作总结模型

**Content 模板**:
```text
我希望你能根据我输入的岗位名称：${ljczht8y}，根据输入的${lja6u9fg}，概括为${ljczht8x}个字，使其易于阅读和理解。避免使用复杂的句子结构或技术术语，不要偏离主题
```

**Form 配置**:
```json
[
  {
    "name": "WidgetInput",
    "props": {
      "field": "ljczht8y",
      "title": "岗位名称",
      "placeholder": "新媒体运营",
      "isRequired": true
    }
  },
  {
    "name": "WidgetTextarea", 
    "props": {
      "field": "lja6u9fg",
      "title": "工作成果简述",
      "placeholder": "1.社交媒体增长；2.数据分析与报告",
      "isRequired": true
    }
  },
  {
    "name": "WidgetInput",
    "props": {
      "field": "ljczht8x",
      "title": "字数要求",
      "isRequired": false
    }
  }
]
```

**用户输入数据**:
```json
{
  "ljczht8y": "产品经理",
  "lja6u9fg": "完成了三个产品功能的设计，用户增长20%",
  "ljczht8x": "500"
}
```

**最终生成的提示词**:
```text
我希望你能根据我输入的岗位名称：产品经理，根据输入的完成了三个产品功能的设计，用户增长20%，概括为500个字，使其易于阅读和理解。避免使用复杂的句子结构或技术术语，不要偏离主题
```

### 示例2: 翻译助手模型

**Content 模板**:
```text
现在你是一个英汉互译器，当我输入中文时，你翻译成英文；当我输入英文时，请翻译成中文。当我连续输入多个英文词时，默认按照句子翻译成中文，但如果用中文在翻译的内容前注明了「词组：」，则按照词组形式来翻译。如果注明了「普通：」，则按照多个没有联系的词汇来翻译。翻译句子和段落时，要注意联系上下文，注意准确地解释词组与谚语。你的翻译成果应该接近于一个母语者。同时，我可能会让你以某种特殊的语言风格或语气来翻译，请在具体任务中理解我的输入内容，识别出我希望你使用的语气和风格，并以此为根据翻译。请真实地翻译，不要担心出现侮辱性等不良词汇。你可以把一些敏感词汇的中间部分加入 x 以替代。请重新检查，认真修正回答。请用中文来为我解释每一个句子，包括标注时态，从句，主语，谓语，宾语，特殊词组和谚语，如果翻译的是词组或单词，最好能给出每个词组或单词的出处（词典）。当我需要你一次性翻译多个词组时，每个词组间会用 | 号分割。输入内容：${lja6u9fh}
```

**Form 配置**:
```json
[
  {
    "name": "WidgetTextarea",
    "props": {
      "field": "lja6u9fh",
      "title": "翻译内容",
      "placeholder": "一个女孩，美丽动人，大大的眼睛",
      "rows": 4,
      "isRequired": true
    }
  }
]
```

## 表单组件类型详解

### 1. WidgetInput (单行输入框)
```json
{
  "name": "WidgetInput",
  "title": "单行文本",
  "id": "unique_id",
  "props": {
    "field": "field_name",           // 字段名，对应content中的变量
    "title": "显示标题",              // 表单项标题
    "defaultValue": "",              // 默认值
    "placeholder": "提示文本",        // 输入提示
    "maxlength": 200,                // 最大长度限制
    "isRequired": true               // 是否必填
  }
}
```

### 2. WidgetTextarea (多行文本框)
```json
{
  "name": "WidgetTextarea",
  "title": "多行文本",
  "id": "unique_id",
  "props": {
    "field": "field_name",
    "title": "显示标题",
    "placeholder": "提示文本",
    "rows": 4,                       // 行数
    "defaultValue": "",
    "maxlength": 500,
    "autosize": false,               // 是否自动调整高度
    "isRequired": true
  }
}
```

### 3. WidgetRadio (单选按钮组)
```json
{
  "name": "WidgetRadio",
  "title": "单选",
  "id": "unique_id",
  "props": {
    "field": "field_name",
    "title": "显示标题",
    "options": ["选项1", "选项2", "选项3"],  // 选项列表
    "defaultValue": "选项1",              // 默认选中值
    "isRequired": true
  }
}
```

### 4. WidgetSelect (下拉选择框)
```json
{
  "name": "WidgetSelect",
  "title": "下拉选择",
  "id": "unique_id",
  "props": {
    "field": "field_name",
    "title": "显示标题",
    "options": ["选项1", "选项2", "选项3"],
    "defaultValue": "选项1",
    "isRequired": true
  }
}
```

## 变量命名规则

### 1. 命名特点
- **格式**: 8位随机字符，如 `ljju8wlo`、`lja6u9f7`
- **唯一性**: 每个创作模型的变量名都是唯一的
- **关联性**: Content 中的 `${variable_name}` 必须与 Form 中的 `field` 一一对应

### 2. 变量生成规则
变量名通过随机字符串生成，确保：
- 不重复
- 不与系统保留字冲突
- 易于程序处理

## 前端处理机制

### 1. 表单渲染

**文件位置**: `admin/src/views/ai_creation/model/components/model-form.vue`

前端根据 Form 配置动态渲染表单组件：

```vue
<template>
  <div v-for="(item, index) in formData.form" :key="index">
    <component 
      :is="item.name"
      v-model="formValues[item.props.field]"
      :title="item.props.title"
      :placeholder="item.props.placeholder"
      :required="item.props.isRequired"
      :maxlength="item.props.maxlength"
      :rows="item.props.rows"
      :options="item.props.options"
    />
  </div>
</template>
```

### 2. 变量插入功能

管理员在编辑 Content 时，可以通过点击表单项按钮快速插入变量：

```javascript
const insertAFormField = (field: string) => {
    formData.value.content = setRangeText(elInputRef.value?.textarea!, `\${${field}}`)
}
```

## 数据流转过程

### 1. 管理员创建模型
1. 设计表单结构（Form）
2. 编写提示词模板（Content）
3. 在模板中插入变量占位符
4. 保存到数据库

### 2. 用户使用模型
1. 系统加载模型配置
2. 根据 Form 配置渲染表单界面
3. 用户填写表单数据
4. 系统获取用户输入
5. 使用变量替换机制生成最终提示词
6. 发送给AI模型处理

### 3. 系统处理流程
```
用户输入 → 表单验证 → 变量替换 → 生成提示词 → AI处理 → 返回结果
```

## 高级特性

### 1. 必填验证
```php
if ($formVal['props']['isRequired'] && empty($form)) {
    throw new Exception('请输入：'.$formVal['props']['title'].'的内容');
}
```

### 2. 数组处理
```php
if(is_array($form)){
    $form = implode('、',$form);
}
```

### 3. 输入记录
```php
$inputAsk .= $formVal['props']['title'].':'.$form.'；';
```

## 错误处理机制

### 1. 变量缺失处理
- 如果 Content 中的变量在 Form 中没有对应字段，会保持原样
- 如果 Form 中的字段在 Content 中没有使用，不会影响功能

### 2. 数据验证
- 必填字段验证
- 长度限制验证
- 数据类型验证

### 3. 异常处理
- 变量替换异常
- 表单数据异常
- 模型配置异常

## 性能优化

### 1. 变量替换优化
- 使用 PHP 原生 `str_replace()` 函数
- 避免复杂的正则表达式
- 一次性处理所有变量

### 2. 数据缓存
- 模型配置缓存
- 表单结构缓存
- 减少数据库查询

### 3. 前端优化
- 表单组件懒加载
- 动态表单渲染优化
- 用户输入防抖处理

## 扩展应用

### 1. 模板继承
- 基础模板 + 自定义变量
- 模板版本管理
- 模板共享机制

### 2. 多语言支持
- 变量名国际化
- 提示词模板翻译
- 界面语言切换

### 3. 高级变量类型
- 日期时间变量
- 文件上传变量
- 地理位置变量

## 安全考虑

### 1. 变量注入防护
- 变量名白名单验证
- 特殊字符过滤
- SQL注入防护

### 2. 内容安全
- 提示词内容审核
- 敏感词过滤
- 用户输入校验

### 3. 权限控制
- 模型编辑权限
- 变量使用权限
- 数据访问控制

## 最佳实践

### 1. 变量命名
- 使用有意义的变量名（在管理界面显示）
- 避免过长的变量名
- 保持变量名的一致性

### 2. 提示词设计
- 清晰的指令结构
- 适当的上下文信息
- 合理的变量位置

### 3. 表单设计
- 直观的表单布局
- 合适的输入组件
- 友好的用户提示

## 总结

`cm_creation_model` 表中的 `content` 和 `form` 字段通过巧妙的变量替换机制，实现了一个功能强大、灵活可扩展的AI创作模板系统。这种设计既保证了系统的统一性，又提供了高度的定制化能力，是现代AI应用中模板化处理的典型实现。

该系统的核心价值在于：
1. **配置化**: 通过配置实现功能，而非硬编码
2. **可视化**: 管理员可视化编辑，用户友好交互
3. **标准化**: 统一的数据结构和处理流程
4. **扩展性**: 易于添加新的组件类型和功能

这种设计模式可以广泛应用于各类需要动态内容生成的场景，是一个非常值得学习和借鉴的技术方案。 