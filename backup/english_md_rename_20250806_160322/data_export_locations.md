# 定时任务数据导出位置说明

## 📍 数据导出位置总览

定时任务清理后的数据会根据不同的任务类型导出到不同的位置，以确保数据安全和便于管理。

### 🗂️ 主要导出目录

| 目录路径 | 用途 | 对应任务 |
|----------|------|----------|
| `/server/runtime/export/` | 日志数据导出 | LogsCleanup |
| `/server/runtime/backup/` | 对话记录备份 | ChatRecordCleanup |
| **数据库归档表** | 财务记录归档 | RevenueCleanup |

---

## 1. 📊 LogsCleanup.php - 日志数据导出

### 🎯 **导出位置**
```
/server/runtime/export/
```

### 📁 **导出文件格式**
- **CSV格式**: `{表名}_{时间戳}.csv`
- **JSON格式**: `{表名}_{时间戳}.json`
- **SQL格式**: `{表名}_{时间戳}.sql`

### 📋 **导出内容**
| 表名 | 描述 | 默认导出 | 文件示例 |
|------|------|----------|----------|
| `cm_operation_log` | 系统操作日志 | ✅ 是 | `cm_operation_log_2025-08-05_15-30-45.json` |
| `cm_user_account_log` | 用户账户日志 | ❌ 否 | `cm_user_account_log_2025-08-05_15-30-45.json` |
| `cm_email_log` | 邮件发送日志 | ❌ 否 | `cm_email_log_2025-08-05_15-30-45.json` |
| `cm_sms_log` | 短信发送日志 | ❌ 否 | `cm_sms_log_2025-08-05_15-30-45.json` |

### 🔧 **使用方法**
```bash
# 默认导出（JSON格式）
php think logs:cleanup --days=180

# 指定导出格式
php think logs:cleanup --days=180 --export-format=csv

# 跳过导出，直接删除
php think logs:cleanup --days=180 --skip-export

# 自定义导出目录
php think logs:cleanup --days=180 --export-dir=/custom/path
```

### 📦 **文件管理**
- **自动压缩**: 文件大小超过10MB时自动压缩为`.gz`格式
- **自动清理**: 保留最近7天的导出文件，自动删除过期文件
- **权限设置**: 导出目录权限为`0755`

---

## 2. 💾 ChatRecordCleanup.php - 对话记录备份

### 🎯 **备份位置**
```
/server/runtime/backup/
```

### 📁 **备份文件格式**
- **SQL格式**: `{表名}_backup_{时间戳}.sql`

### 📋 **备份内容**
| 表名 | 描述 | 备份条件 | 文件示例 |
|------|------|----------|----------|
| `cm_chat_record` | AI对话记录 | 使用`--backup`参数 | `cm_chat_record_backup_2025-08-05_15-30-45.sql` |
| `cm_kb_robot_record` | 智能体对话记录 | 使用`--backup`参数 | `cm_kb_robot_record_backup_2025-08-05_15-30-45.sql` |

### 🔧 **使用方法**
```bash
# 清理时备份数据
php think chat:cleanup --days=365 --backup

# 仅预览，不备份
php think chat:cleanup --days=365 --dry-run

# 强制清理，不备份
php think chat:cleanup --days=365 --force
```

### 📦 **备份特点**
- **可选备份**: 只有使用`--backup`参数时才会备份
- **SQL格式**: 备份为标准SQL INSERT语句，便于恢复
- **完整数据**: 备份即将被删除的所有记录
- **时间戳**: 文件名包含精确的备份时间

---

## 3. 🏦 RevenueCleanup.php - 财务记录归档

### 🎯 **归档位置**
```
数据库归档表: cm_kb_robot_revenue_log_archive
```

### 📊 **归档机制**
- **数据库内归档**: 数据不导出到文件，而是移动到专门的归档表
- **完整数据保留**: 原始数据完整保存，并添加归档元信息
- **可查询性**: 归档后的数据仍可通过SQL查询访问

### 📋 **归档表结构**
```sql
CREATE TABLE `cm_kb_robot_revenue_log_archive` (
  -- 原表所有字段
  `id` int(10) unsigned NOT NULL,
  `user_id` int(10) unsigned NOT NULL,
  `sharer_id` int(10) unsigned NOT NULL,
  `robot_id` int(10) unsigned NOT NULL,
  `square_id` int(10) unsigned NOT NULL,
  `record_id` int(10) unsigned NOT NULL,
  `total_cost` decimal(15,7) unsigned NOT NULL,
  `share_amount` decimal(15,7) unsigned NOT NULL,
  `platform_amount` decimal(15,7) unsigned NOT NULL,
  `share_ratio` decimal(5,2) unsigned NOT NULL,
  `settle_status` tinyint(1) unsigned NOT NULL,
  `settle_time` int(10) unsigned NOT NULL,
  `create_time` int(10) unsigned NOT NULL,
  `update_time` int(10) unsigned NOT NULL,
  
  -- 归档元信息字段
  `archived_at` int(11) NOT NULL DEFAULT 0 COMMENT '归档时间',
  `archived_by` varchar(100) DEFAULT 'system' COMMENT '归档操作者',
  `archive_reason` varchar(200) DEFAULT '' COMMENT '归档原因',
  
  -- 索引
  KEY `idx_archived_at` (`archived_at`),
  KEY `idx_archived_by` (`archived_by`)
);
```

### 🔧 **使用方法**
```bash
# 生产环境归档（严格检查）
php think revenue:cleanup --days=1095 --force

# 测试环境归档（跳过未结算检查）
php think revenue:cleanup --days=365 --test-mode --force

# 归档并验证完整性
php think revenue:cleanup --days=1095 --verify-archive --force
```

### 📦 **归档特点**
- **财务合规**: 满足财务数据保留要求
- **数据完整性**: 自动验证归档数据完整性
- **可恢复性**: 归档数据可以随时查询和恢复
- **安全删除**: 只有已结算的记录才会被归档

---

## 🔍 数据查看和管理

### 📊 **查看导出的日志数据**
```bash
# 查看导出目录
ls -la /server/runtime/export/

# 查看具体文件内容（JSON格式）
cat /server/runtime/export/cm_operation_log_2025-08-05_15-30-45.json

# 查看压缩文件
zcat /server/runtime/export/cm_operation_log_2025-08-05_15-30-45.json.gz
```

### 💾 **查看备份的对话数据**
```bash
# 查看备份目录
ls -la /server/runtime/backup/

# 查看备份文件内容
cat /server/runtime/backup/cm_chat_record_backup_2025-08-05_15-30-45.sql
```

### 🏦 **查看归档的财务数据**
```sql
-- 查看归档表中的数据
SELECT * FROM cm_kb_robot_revenue_log_archive 
WHERE archived_at > UNIX_TIMESTAMP('2025-08-01') 
ORDER BY archived_at DESC;

-- 统计归档数据
SELECT 
    COUNT(*) as total_archived,
    SUM(share_amount) as total_share_amount,
    FROM_UNIXTIME(MIN(archived_at)) as first_archived,
    FROM_UNIXTIME(MAX(archived_at)) as last_archived
FROM cm_kb_robot_revenue_log_archive;
```

---

## 🛠️ 数据恢复指南

### 📊 **恢复日志数据**
1. **从JSON文件恢复**: 需要编写脚本解析JSON并插入数据库
2. **从SQL文件恢复**: 直接执行SQL文件即可
3. **从CSV文件恢复**: 使用数据库导入工具

### 💾 **恢复对话数据**
```bash
# 直接执行备份的SQL文件
mysql -u username -p database_name < /server/runtime/backup/cm_chat_record_backup_2025-08-05_15-30-45.sql
```

### 🏦 **恢复财务数据**
```sql
-- 从归档表恢复到主表
INSERT INTO cm_kb_robot_revenue_log 
SELECT id, user_id, sharer_id, robot_id, square_id, record_id, 
       total_cost, share_amount, platform_amount, share_ratio, 
       settle_status, settle_time, create_time, update_time
FROM cm_kb_robot_revenue_log_archive 
WHERE archived_at BETWEEN start_time AND end_time;
```

---

## 📋 总结

### 🎯 **数据安全保障**
- ✅ **多重保护**: 备份、归档、导出三重数据保护机制
- ✅ **格式多样**: 支持SQL、JSON、CSV多种格式
- ✅ **自动管理**: 自动压缩、自动清理过期文件
- ✅ **完整性验证**: 归档数据完整性自动验证

### 📍 **快速定位数据**
- **日志数据**: `/server/runtime/export/` 目录
- **对话备份**: `/server/runtime/backup/` 目录  
- **财务归档**: `cm_kb_robot_revenue_log_archive` 表

### 🔧 **最佳实践**
1. **定期检查**: 定期检查导出目录的磁盘空间
2. **备份重要数据**: 对重要的对话记录使用`--backup`参数
3. **验证归档**: 财务数据归档后使用`--verify-archive`验证
4. **监控日志**: 关注清理任务的执行日志

**所有清理的数据都有完善的备份和归档机制，确保数据安全可恢复！** 🛡️✨
