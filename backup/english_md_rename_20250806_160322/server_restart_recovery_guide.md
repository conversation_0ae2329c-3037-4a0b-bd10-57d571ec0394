# 服务器重启后智能体分成定时任务恢复指南

## 📋 概述

本指南提供服务器重启后恢复智能体分成定时任务自动执行的完整操作步骤和命令。

## 🔧 系统架构说明

智能体分成定时任务系统采用以下架构：
- **Docker容器**: 应用运行在Docker容器中
- **Supervisor**: 进程管理器，负责管理定时任务进程
- **ThinkPHP定时任务**: 应用层定时任务调度器
- **MySQL数据库**: 存储定时任务配置和执行状态

## 🚀 快速恢复步骤

### 1. 启动Docker容器

```bash
# 进入项目目录
cd /www/wwwroot/ai

# 启动所有容器
docker-compose up -d

# 验证容器状态
docker-compose ps
```

**预期输出**:
```
Name                Command               State                    Ports
chatmoney-mysql     docker-entrypoint.sh mysqld    Up      0.0.0.0:3306->3306/tcp
chatmoney-php       docker-php-entrypoint php-fpm  Up      0.0.0.0:9000->9000/tcp
chatmoney-redis     docker-entrypoint.sh redis ... Up      0.0.0.0:6379->6379/tcp
```

### 2. 检查Supervisor状态

```bash
# 检查Supervisor进程
docker exec chatmoney-php ps aux | grep supervisor

# 如果Supervisor未运行，启动它
docker exec chatmoney-php supervisord -c /etc/supervisor/conf.d/supervisord.conf

# 检查Supervisor管理的进程
docker exec chatmoney-php supervisorctl status
```

### 3. 验证定时任务配置

```bash
# 检查定时任务数据库配置
docker exec chatmoney-mysql mysql -u root -p123456Abcd chatmoney --default-character-set=utf8 -e "
SELECT id, name, command, params, expression, status, last_time, 
       FROM_UNIXTIME(last_time) as last_time_readable 
FROM cm_dev_crontab WHERE id = 9;"
```

**预期输出**:
```
+----+--------------------------------+-------------------------+----------------------------------+-------------+--------+-----------+---------------------+
| id | name                           | command                 | params                           | expression  | status | last_time | last_time_readable  |
+----+--------------------------------+-------------------------+----------------------------------+-------------+--------+-----------+---------------------+
|  9 | 智能体分成定时任务处理         | optimized_revenue_settle| 1000 --use-cache --cache-warmup | */2 * * * * |      1 | 1722780000| 2025-08-04 20:00:00 |
+----+--------------------------------+-------------------------+----------------------------------+-------------+--------+-----------+---------------------+
```

### 4. 手动执行测试

```bash
# 测试定时任务命令
docker exec chatmoney-php php think optimized_revenue_settle 10 --debug --stats

# 测试缓存功能
docker exec chatmoney-php php think optimized_revenue_settle 5 --use-cache --cache-warmup --debug
```

### 5. 监控定时任务执行

```bash
# 实时监控定时任务日志
docker exec chatmoney-php tail -f /server/runtime/log/$(date +%Y%m%d).log | grep "优化分成"

# 检查最近的执行状态
docker exec chatmoney-mysql mysql -u root -p123456Abcd chatmoney --default-character-set=utf8 -e "
SELECT id, name, status, last_time, FROM_UNIXTIME(last_time) as last_exec, error 
FROM cm_dev_crontab WHERE id = 9;"
```

## 🔍 故障排查步骤

### 问题1: Docker容器未启动

**症状**: `docker-compose ps` 显示容器状态为 `Exit` 或 `Down`

**解决方案**:
```bash
# 查看容器日志
docker-compose logs chatmoney-php
docker-compose logs chatmoney-mysql
docker-compose logs chatmoney-redis

# 重新构建并启动
docker-compose down
docker-compose up -d --build

# 如果仍有问题，清理并重启
docker-compose down -v
docker system prune -f
docker-compose up -d
```

### 问题2: Supervisor未运行

**症状**: `ps aux | grep supervisor` 无输出

**解决方案**:
```bash
# 进入容器
docker exec -it chatmoney-php bash

# 检查Supervisor配置
ls -la /etc/supervisor/conf.d/

# 启动Supervisor
supervisord -c /etc/supervisor/conf.d/supervisord.conf

# 检查状态
supervisorctl status

# 如果配置文件不存在，创建基本配置
cat > /etc/supervisor/conf.d/supervisord.conf << 'EOF'
[supervisord]
nodaemon=false
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:crontab]
command=php think crontab
directory=/server
autostart=true
autorestart=true
user=www-data
stdout_logfile=/var/log/supervisor/crontab.log
stderr_logfile=/var/log/supervisor/crontab_error.log
EOF

# 重新加载配置
supervisorctl reread
supervisorctl update
```

### 问题3: 定时任务未执行

**症状**: `last_time` 长时间未更新

**解决方案**:
```bash
# 检查定时任务进程
docker exec chatmoney-php ps aux | grep "think crontab"

# 如果进程不存在，手动启动
docker exec chatmoney-php nohup php think crontab > /dev/null 2>&1 &

# 检查定时任务配置
docker exec chatmoney-mysql mysql -u root -p123456Abcd chatmoney --default-character-set=utf8 -e "
UPDATE cm_dev_crontab SET status = 1 WHERE id = 9;
SELECT * FROM cm_dev_crontab WHERE id = 9;"

# 手动触发一次执行
docker exec chatmoney-php php think optimized_revenue_settle 10 --debug
```

### 问题4: 数据库连接失败

**症状**: 命令执行时出现数据库连接错误

**解决方案**:
```bash
# 检查MySQL容器状态
docker exec chatmoney-mysql mysqladmin -u root -p123456Abcd ping

# 检查数据库配置
docker exec chatmoney-php cat /server/config/database.php | grep -A 10 "mysql"

# 测试数据库连接
docker exec chatmoney-mysql mysql -u root -p123456Abcd chatmoney -e "SELECT 1;"

# 如果连接失败，重启MySQL容器
docker-compose restart chatmoney-mysql
```

### 问题5: Redis缓存连接失败

**症状**: 缓存相关功能报错

**解决方案**:
```bash
# 检查Redis容器状态
docker exec chatmoney-redis redis-cli ping

# 检查Redis配置
docker exec chatmoney-php cat /server/config/cache.php | grep -A 10 "redis"

# 测试Redis连接
docker exec chatmoney-redis redis-cli info server

# 如果连接失败，重启Redis容器
docker-compose restart chatmoney-redis
```

## 📊 健康检查脚本

创建自动化健康检查脚本：

```bash
#!/bin/bash
# 文件名: health_check.sh

echo "🔍 智能体分成定时任务健康检查"
echo "================================"

# 1. 检查Docker容器
echo "1. Docker容器状态:"
docker-compose ps

# 2. 检查Supervisor
echo -e "\n2. Supervisor状态:"
docker exec chatmoney-php supervisorctl status 2>/dev/null || echo "Supervisor未运行"

# 3. 检查定时任务进程
echo -e "\n3. 定时任务进程:"
docker exec chatmoney-php ps aux | grep "think crontab" | grep -v grep || echo "定时任务进程未运行"

# 4. 检查数据库连接
echo -e "\n4. 数据库连接:"
docker exec chatmoney-mysql mysqladmin -u root -p123456Abcd ping 2>/dev/null && echo "数据库连接正常" || echo "数据库连接失败"

# 5. 检查Redis连接
echo -e "\n5. Redis连接:"
docker exec chatmoney-redis redis-cli ping 2>/dev/null && echo "Redis连接正常" || echo "Redis连接失败"

# 6. 检查定时任务配置
echo -e "\n6. 定时任务配置:"
docker exec chatmoney-mysql mysql -u root -p123456Abcd chatmoney --default-character-set=utf8 -e "
SELECT id, name, status, FROM_UNIXTIME(last_time) as last_exec 
FROM cm_dev_crontab WHERE id = 9;" 2>/dev/null || echo "定时任务配置检查失败"

# 7. 测试命令执行
echo -e "\n7. 命令执行测试:"
docker exec chatmoney-php php think optimized_revenue_settle 1 --debug 2>/dev/null && echo "命令执行正常" || echo "命令执行失败"

echo -e "\n✅ 健康检查完成"
```

使用方法：
```bash
# 赋予执行权限
chmod +x health_check.sh

# 执行健康检查
./health_check.sh
```

## 🔄 自动恢复脚本

创建自动恢复脚本：

```bash
#!/bin/bash
# 文件名: auto_recovery.sh

echo "🚀 智能体分成定时任务自动恢复"
echo "==============================="

# 1. 启动Docker容器
echo "1. 启动Docker容器..."
cd /www/wwwroot/ai
docker-compose up -d

# 等待容器启动
sleep 10

# 2. 启动Supervisor
echo "2. 启动Supervisor..."
docker exec chatmoney-php supervisord -c /etc/supervisor/conf.d/supervisord.conf 2>/dev/null || echo "Supervisor可能已在运行"

# 3. 确保定时任务启用
echo "3. 确保定时任务启用..."
docker exec chatmoney-mysql mysql -u root -p123456Abcd chatmoney --default-character-set=utf8 -e "
UPDATE cm_dev_crontab SET status = 1 WHERE id = 9;" 2>/dev/null

# 4. 手动启动定时任务进程（如果需要）
echo "4. 检查并启动定时任务进程..."
CRONTAB_RUNNING=$(docker exec chatmoney-php ps aux | grep "think crontab" | grep -v grep | wc -l)
if [ $CRONTAB_RUNNING -eq 0 ]; then
    echo "启动定时任务进程..."
    docker exec chatmoney-php nohup php think crontab > /dev/null 2>&1 &
fi

# 5. 测试执行
echo "5. 测试执行..."
docker exec chatmoney-php php think optimized_revenue_settle 1 --debug

echo "✅ 自动恢复完成"
```

使用方法：
```bash
# 赋予执行权限
chmod +x auto_recovery.sh

# 执行自动恢复
./auto_recovery.sh
```

## 📝 维护建议

### 日常监控
1. **每日检查**: 运行健康检查脚本
2. **日志监控**: 定期查看执行日志
3. **性能监控**: 关注处理速度和成功率

### 定期维护
1. **每周**: 检查数据库和缓存性能
2. **每月**: 清理过期日志文件
3. **每季度**: 评估系统性能和优化空间

### 备份策略
1. **配置备份**: 定期备份定时任务配置
2. **数据备份**: 定期备份相关数据表
3. **代码备份**: 保持代码版本控制

## 🆘 紧急联系信息

如遇到无法解决的问题，请：
1. 保存错误日志和系统状态信息
2. 记录问题发生的时间和操作步骤
3. 联系技术支持团队

---

**文档版本**: v1.0  
**最后更新**: 2025-08-04  
**适用系统**: Docker + ThinkPHP + Supervisor
