# 缓存管理建议

## 当前缓存策略
- 直接广场查询：缓存60秒
- Session联合查询：缓存30秒  
- Robot广场查询：缓存300秒

## 建议的缓存清除机制

### 1. 在广场信息更新时清除缓存
```php
// 在 KbRobotSquare 模型的更新方法中添加
public function updateSquare($id, $data) {
    $result = $this->where('id', $id)->update($data);
    if ($result) {
        // 清除相关缓存
        \think\facade\Cache::delete("square_robot_" . $data['robot_id']);
        \think\facade\Cache::delete("square_direct_" . $id);
    }
    return $result;
}
```

### 2. 使用标签缓存
```php
// 修改缓存调用，添加标签
->cache(60, 'square_' . $this->robotId)

// 清除时可以按标签清除
\think\facade\Cache::tag('square_' . $robotId)->clear();
```

### 3. 监控缓存命中率
建议添加缓存监控，确保缓存策略有效。 