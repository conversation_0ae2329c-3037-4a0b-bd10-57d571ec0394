# Docker环境下Bot模型问题诊断与解决方案

## 问题描述
在Docker环境中，豆包联网检索模型（bot-20250630160952-xphcl）调用时出现卡住不动的问题，点击暂停后显示"请求失败，请重试"。

## 已完成的修复

### 1. DoubaoService核心优化
已对 `server/app/common/service/ai/chat/DoubaoService.php` 进行了全面优化：

#### ✅ Bot模型自动识别
- 添加了 `detectBotModel()` 方法
- 支持 `bot-` 前缀识别
- 支持关键词匹配（bot、agent、search、web）

#### ✅ 动态API路径构建
- Bot模型：`/bots/chat/completions`
- 普通模型：`/chat/completions`

#### ✅ 智能请求参数构建
- Bot模型自动添加 `stream_options: {include_usage: true}`
- 根据模型类型调整参数

#### ✅ 超时机制优化
- Bot模型：600秒（10分钟）
- 普通模型：301秒（5分钟）
- 优化了低速传输检测

#### ✅ 增强日志记录
- 详细的请求和响应日志
- Bot模型特殊处理日志
- 错误诊断信息

#### ✅ 响应解析优化
- 支持Bot模型特有字段（search_results、tool_calls）
- 增强错误处理
- 改进流式数据解析

## Docker环境特殊注意事项

### 1. 容器网络配置
```bash
# 检查容器网络连通性
docker exec -it <php_container_name> ping ark.cn-beijing.volces.com

# 检查DNS解析
docker exec -it <php_container_name> nslookup ark.cn-beijing.volces.com
```

### 2. 容器时间同步
```bash
# 检查容器时间
docker exec -it <php_container_name> date

# 如果时间不正确，重启容器或同步时间
docker restart <php_container_name>
```

### 3. 容器资源限制
检查Docker容器的资源限制：
```bash
# 查看容器资源使用情况
docker stats <php_container_name>

# 检查容器配置
docker inspect <php_container_name> | grep -A 10 "Memory\|Cpu"
```

## 问题排查步骤

### 步骤1：检查模型配置
进入后台管理系统，确认：
1. **模型管理** → 添加新模型
   - 模型名称：`豆包联网检索模型`
   - 模型标识：`bot-20250630160952-xphcl`
   - 渠道：`doubao`
   - 状态：`启用`

### 步骤2：配置API密钥
1. **模型管理** → **密钥池管理**
2. 为Bot模型配置有效的API密钥
3. 确保密钥有Bot模型的访问权限

### 步骤3：Docker环境日志检查
```bash
# 查看PHP容器日志
docker logs <php_container_name> -f

# 查看应用日志
docker exec -it <php_container_name> tail -f /www/wwwroot/ai/server/runtime/log/$(date +%Y%m)/$(date +%d).log

# 搜索Bot模型相关日志
docker exec -it <php_container_name> grep -r "Bot模型\|豆包\|doubao" /www/wwwroot/ai/server/runtime/log/
```

### 步骤4：网络连接测试
```bash
# 在PHP容器内测试API连接
docker exec -it <php_container_name> curl -I https://ark.cn-beijing.volces.com

# 测试Bot API接口
docker exec -it <php_container_name> curl -X POST https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{"model":"bot-20250630160952-xphcl","messages":[{"role":"user","content":"test"}]}'
```

## 可能的Docker环境问题

### 1. 防火墙/网络策略
Docker容器可能受到宿主机防火墙或网络策略限制：
```bash
# 检查iptables规则
iptables -L

# 检查Docker网络
docker network ls
docker network inspect bridge
```

### 2. DNS解析问题
```bash
# 修改容器DNS配置
docker run --dns=******* --dns=*************** <your_image>

# 或在docker-compose.yml中添加：
# dns:
#   - *******
#   - ***************
```

### 3. 代理设置
如果使用了HTTP代理：
```bash
# 检查环境变量
docker exec -it <php_container_name> env | grep -i proxy

# 清除代理设置（如果不需要）
docker exec -it <php_container_name> unset http_proxy https_proxy
```

## 测试验证

### 1. 前端测试
1. 登录系统前端
2. 进入对话页面
3. 选择"豆包联网检索模型"
4. 发送测试消息："请帮我搜索今天的新闻"
5. 观察响应情况

### 2. 浏览器开发者工具
1. 打开F12开发者工具
2. 切换到Network标签
3. 发送对话请求
4. 检查请求状态和响应

### 3. 日志监控
```bash
# 实时监控日志
docker exec -it <php_container_name> tail -f /www/wwwroot/ai/server/runtime/log/$(date +%Y%m)/$(date +%d).log | grep -E "(Bot模型|豆包|doubao|ERROR|Exception)"
```

## 如果问题仍然存在

### 可能原因分析：
1. **API密钥权限不足** - 密钥可能没有Bot模型的访问权限
2. **网络连接问题** - Docker容器无法访问豆包API
3. **模型未正确配置** - 后台模型配置不完整
4. **资源限制** - 容器内存或CPU限制导致超时
5. **时区问题** - 容器时间与API服务器时间不同步

### 进一步排查：
1. 联系豆包API技术支持确认模型访问权限
2. 检查Docker宿主机的网络配置
3. 增加容器资源限制
4. 考虑使用代理或VPN
5. 检查API配额和限制

## 总结

我们已经完成了DoubaoService的全面优化，主要解决了：
- ✅ Bot模型识别和API路径问题
- ✅ 请求参数格式问题
- ✅ 超时设置问题
- ✅ 错误处理问题
- ✅ 日志记录问题

接下来需要在Docker环境中验证配置和网络连接。如果问题仍然存在，请按照上述步骤进行排查，并提供具体的错误日志信息。 