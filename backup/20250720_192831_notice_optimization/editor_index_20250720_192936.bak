<template>
    <div class="border border-br flex flex-col" :style="styles">
        <!-- HTML编辑模式切换 -->
        <div v-if="allowHtml" class="flex items-center justify-between p-2 bg-gray-50 border-b">
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">编辑模式：</span>
                <el-radio-group v-model="editMode" size="small">
                    <el-radio-button label="visual">可视化编辑</el-radio-button>
                    <el-radio-button label="code">HTML源码</el-radio-button>
                </el-radio-group>
                <!-- HTML功能提示 -->
                <el-tag type="success" size="small">🎯 支持所有HTML标签</el-tag>
            </div>
            <div class="flex items-center space-x-2">
                <el-button 
                    size="small" 
                    type="primary" 
                    link 
                    @click="insertCommonHtml"
                    v-if="editMode === 'visual'"
                >
                    插入HTML模板
                </el-button>
                <el-button 
                    size="small" 
                    type="warning" 
                    link 
                    @click="formatHtml"
                    v-if="editMode === 'code'"
                >
                    格式化代码
                </el-button>
                <!-- HTML验证功能 -->
                <el-button 
                    v-if="editMode === 'code'"
                    size="small" 
                    type="success" 
                    link 
                    @click="validateHtml"
                >
                    验证HTML
                </el-button>
            </div>
        </div>
        
        <!-- 可视化编辑器 -->
        <div v-show="editMode === 'visual'" class="flex-1 flex flex-col">
            <toolbar
                class="border-b border-br"
                :editor="editorRef"
                :defaultConfig="computedToolbarConfig"
                :mode="mode"
            />
            <w-editor
                class="overflow-y-auto flex-1"
                v-model="valueHtml"
                :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="handleCreated"
            />
        </div>
        
        <!-- HTML源码编辑器 -->
        <div v-show="editMode === 'code'" class="flex-1">
            <el-input
                v-model="valueHtml"
                type="textarea"
                :rows="20"
                class="h-full"
                placeholder="请输入HTML代码..."
                style="font-family: 'Courier New', Courier, monospace;"
            />
        </div>
        
        <material-picker
            ref="materialPickerRef"
            :type="fileType"
            :limit="-1"
            hidden-upload
            @change="selectChange"
        />
        
        <!-- 常用HTML插入弹窗 -->
        <el-dialog v-model="showHtmlDialog" title="插入常用HTML" width="600px">
            <div class="space-y-4">
                <div>
                    <h4 class="mb-2">常用HTML模板：</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <el-button 
                            v-for="template in htmlTemplates" 
                            :key="template.name"
                            @click="insertTemplate(template.html)"
                            size="small"
                        >
                            {{ template.name }}
                        </el-button>
                    </div>
                </div>
                <div>
                    <h4 class="mb-2">自定义HTML：</h4>
                    <el-input
                        v-model="customHtml"
                        type="textarea"
                        :rows="8"
                        placeholder="请输入自定义HTML代码..."
                        style="font-family: 'Courier New', Courier, monospace;"
                    />
                </div>
            </div>
            <template #footer>
                <el-button @click="showHtmlDialog = false">取消</el-button>
                <el-button type="primary" @click="insertCustomHtml">插入自定义HTML</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { Editor as WEditor, Toolbar } from '@wangeditor/editor-for-vue'
import type { IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import MaterialPicker from '@/components/material/picker.vue'
import { addUnit } from '@/utils/util'
import type { CSSProperties } from 'vue'

const props = withDefaults(
    defineProps<{
        modelValue?: string
        mode?: 'default' | 'simple'
        height?: string | number
        width?: string | number
        toolbarConfig?: Partial<IToolbarConfig>
        // 新增：是否允许HTML代码
        allowHtml?: boolean
        // 新增：是否显示源码编辑按钮
        showCodeView?: boolean
        // 新增：自定义允许的HTML标签（为空时表示允许所有标签）
        allowedTags?: string[]
        // 新增：管理员模式（放开所有限制）
        adminMode?: boolean
    }>(),
    {
        modelValue: '',
        mode: 'default',
        height: '100%',
        width: 'auto',
        toolbarConfig: () => ({}),
        allowHtml: false,
        showCodeView: false,
        allowedTags: () => [],
        adminMode: false
    }
)

const emit = defineEmits<{
    (event: 'update:modelValue', value: string): void
}>()

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const materialPickerRef = shallowRef<InstanceType<typeof MaterialPicker>>()
const fileType = ref('')
const editMode = ref<'visual' | 'code'>('visual')
const showHtmlDialog = ref(false)
const customHtml = ref('')

let insertFn: any

// HTML模板
const htmlTemplates = ref([
    {
        name: '通知公告',
        html: `<div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 16px; margin: 16px 0;">
    <h3 style="color: #0369a1; margin: 0 0 8px 0;">📢 重要通知</h3>
    <p style="margin: 0; line-height: 1.6;">请在此处输入通知内容...</p>
</div>`
    },
    {
        name: '提示信息',
        html: `<div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin: 16px 0;">
    <h4 style="color: #d97706; margin: 0 0 8px 0;">💡 温馨提示</h4>
    <p style="margin: 0; line-height: 1.6;">请在此处输入提示内容...</p>
</div>`
    },
    {
        name: '警告信息',
        html: `<div style="background: #fee2e2; border: 1px solid #ef4444; border-radius: 8px; padding: 16px; margin: 16px 0;">
    <h4 style="color: #dc2626; margin: 0 0 8px 0;">⚠️ 重要警告</h4>
    <p style="margin: 0; line-height: 1.6;">请在此处输入警告内容...</p>
</div>`
    },
    {
        name: '成功信息',
        html: `<div style="background: #d1fae5; border: 1px solid #10b981; border-radius: 8px; padding: 16px; margin: 16px 0;">
    <h4 style="color: #059669; margin: 0 0 8px 0;">✅ 操作成功</h4>
    <p style="margin: 0; line-height: 1.6;">请在此处输入成功信息...</p>
</div>`
    },
    {
        name: '按钮链接',
        html: `<div style="text-align: center; margin: 20px 0;">
    <a href="#" style="display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">点击了解更多</a>
</div>`
    },
    {
        name: '表格样式',
        html: `<table style="width: 100%; border-collapse: collapse; margin: 16px 0;">
    <thead>
        <tr style="background: #f8fafc;">
            <th style="border: 1px solid #e2e8f0; padding: 12px; text-align: left;">列标题1</th>
            <th style="border: 1px solid #e2e8f0; padding: 12px; text-align: left;">列标题2</th>
            <th style="border: 1px solid #e2e8f0; padding: 12px; text-align: left;">列标题3</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style="border: 1px solid #e2e8f0; padding: 12px;">数据1</td>
            <td style="border: 1px solid #e2e8f0; padding: 12px;">数据2</td>
            <td style="border: 1px solid #e2e8f0; padding: 12px;">数据3</td>
        </tr>
    </tbody>
</table>`
    },
    // 管理员专用高级模板
    ...(props.allowHtml ? [
        {
            name: '视频嵌入',
            html: `<div style="text-align: center; margin: 20px 0;">
    <iframe width="560" height="315" src="https://www.youtube.com/embed/VIDEO_ID" frameborder="0" allowfullscreen style="max-width: 100%; border-radius: 8px;"></iframe>
    <p style="margin-top: 8px; color: #666; font-size: 14px;">请替换VIDEO_ID为实际视频ID</p>
</div>`
        },
        {
            name: '外部网页嵌入',
            html: `<div style="border: 1px solid #ddd; border-radius: 8px; margin: 20px 0;">
    <iframe src="https://example.com" width="100%" height="400" frameborder="0" style="border-radius: 8px;"></iframe>
</div>`
        },
        {
            name: '交互式内容',
            html: `<div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h4 style="margin-top: 0;">交互式内容区域</h4>
    <button onclick="alert('这是一个交互按钮！')" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">点击测试</button>
</div>`
        },
        {
            name: '自定义样式',
            html: `<style>
.custom-notice {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}
.custom-notice h3 {
    margin: 0 0 10px 0;
    font-size: 1.5em;
}
</style>
<div class="custom-notice">
    <h3>🚀 自定义样式公告</h3>
    <p>这是使用自定义CSS样式的公告内容，支持渐变背景和阴影效果。</p>
</div>`
        },
        {
            name: '复杂表单',
            html: `<form style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4 style="margin-top: 0; color: #495057;">意见反馈表单</h4>
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">姓名：</label>
        <input type="text" placeholder="请输入您的姓名" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
    </div>
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">意见建议：</label>
        <textarea rows="4" placeholder="请输入您的意见建议" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; resize: vertical;"></textarea>
    </div>
    <button type="submit" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">提交反馈</button>
</form>`
        }
    ] : [])
])

// 增强的编辑器配置
const editorConfig: Partial<IEditorConfig> = {
    placeholder: '请输入内容...',
    // HTML粘贴配置
    MENU_CONF: {
        uploadImage: {
            customBrowseAndUpload(insert: any) {
                fileType.value = 'image'
                materialPickerRef.value?.showPopup(-1)
                insertFn = insert
            }
        },
        uploadVideo: {
            customBrowseAndUpload(insert: any) {
                fileType.value = 'video'
                materialPickerRef.value?.showPopup(-1)
                insertFn = insert
            }
        },
        // HTML粘贴处理配置
        pasteText: {
            // 允许粘贴HTML
            pasteText: props.allowHtml
        }
    },
    // HTML处理配置
    ...props.allowHtml ? {
        // 允许粘贴HTML时的配置
        customPaste: (editor: any, event: ClipboardEvent) => {
            // 获取粘贴的HTML内容
            const html = event.clipboardData?.getData('text/html') || ''
            const text = event.clipboardData?.getData('text/plain') || ''
            
            if (html && props.allowHtml) {
                // 如果有HTML内容且允许HTML，则插入HTML
                event.preventDefault()
                editor.dangerouslyInsertHtml(html)
                return false
            }
            
            // 否则使用默认处理
            return true
        },
        // 自定义解析元素 - allowHtml模式下的标签处理
        customParseElemHtml: (elemNode: any, elem: any) => {
            // 如果启用HTML且没有指定标签限制，允许所有标签
            if (props.allowHtml && props.allowedTags.length === 0) {
                return elem // 不过滤任何标签
            }
            
            // 如果指定了允许的标签列表，按列表过滤
            if (props.allowHtml && props.allowedTags.length > 0) {
                const tagName = elem.type?.toLowerCase()
                if (tagName && props.allowedTags.includes(tagName)) {
                    return elem
                }
                return null
            }
            return elem
        }
    } : {}
}

// 增强的工具栏配置
const computedToolbarConfig = computed(() => {
    const baseConfig = {
        ...props.toolbarConfig
    }
    
    // 如果启用源码编辑，添加相关按钮
    if (props.showCodeView) {
        const toolbarKeys = baseConfig.toolbarKeys || []
        if (!toolbarKeys.includes('codeView')) {
            toolbarKeys.push('|', 'codeView')
            baseConfig.toolbarKeys = toolbarKeys
        }
    }
    
    return baseConfig
})

const styles = computed<CSSProperties>(() => ({
    height: addUnit(props.height),
    width: addUnit(props.width)
}))

const valueHtml = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

// 插入常用HTML
const insertCommonHtml = () => {
    showHtmlDialog.value = true
}

// 插入模板
const insertTemplate = (html: string) => {
    const editor = editorRef.value
    if (editor) {
        editor.dangerouslyInsertHtml(html)
    }
    showHtmlDialog.value = false
}

// 插入自定义HTML
const insertCustomHtml = () => {
    if (customHtml.value) {
        const editor = editorRef.value
        if (editor) {
            editor.dangerouslyInsertHtml(customHtml.value)
        }
        customHtml.value = ''
        showHtmlDialog.value = false
    }
}

// 格式化HTML代码
const formatHtml = () => {
    try {
        // 简单的HTML格式化
        let formatted = valueHtml.value
        formatted = formatted.replace(/></g, '>\n<')
        formatted = formatted.replace(/\n\s*\n/g, '\n')
        valueHtml.value = formatted
        ElMessage.success('HTML代码已格式化')
    } catch (error) {
        ElMessage.error('格式化失败，请检查HTML语法')
    }
}

const selectChange = (fileUrl: string[]) => {
    fileUrl.forEach((url) => {
        insertFn(url)
    })
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

const handleCreated = (editor: any) => {
    editorRef.value = editor // 记录 editor 实例，重要！
    
    // 如果允许HTML，增加一些额外的功能
    if (props.allowHtml) {
        // 可以在这里添加自定义的HTML处理逻辑
        console.log('HTML模式已启用，支持粘贴和编辑HTML代码')
    }
}

// HTML验证功能
const validateHtml = () => {
    try {
        const htmlContent = valueHtml.value
        
        // 基本HTML语法检查
        const parser = new DOMParser()
        const doc = parser.parseFromString(htmlContent, 'text/html')
        const errors = doc.querySelectorAll('parsererror')
        
        if (errors.length > 0) {
            ElMessage.warning('HTML语法可能存在问题，请检查标签闭合')
        } else {
            ElMessage.success('HTML语法验证通过')
        }
        
        // 检查常见的潜在问题
        const warnings = []
        
        // 检查外部链接
        const externalLinks = htmlContent.match(/https?:\/\/[^\s"'<>]+/g) || []
        if (externalLinks.length > 0) {
            warnings.push(`发现 ${externalLinks.length} 个外部链接`)
        }
        
        // 检查JavaScript代码
        const scripts = htmlContent.match(/<script[^>]*>[\s\S]*?<\/script>/gi) || []
        if (scripts.length > 0) {
            warnings.push(`发现 ${scripts.length} 个JavaScript代码块`)
        }
        
        // 检查iframe嵌入
        const iframes = htmlContent.match(/<iframe[^>]*>/gi) || []
        if (iframes.length > 0) {
            warnings.push(`发现 ${iframes.length} 个iframe嵌入`)
        }
        
        if (warnings.length > 0) {
            ElMessage.info(`检查完成，发现：${warnings.join('、')}`)
        }
        
    } catch (error) {
        ElMessage.error('HTML验证失败：' + error.message)
    }
}
</script>

<style lang="scss">
.w-e-full-screen-container {
    z-index: 999;
}
.w-e-text-container [data-slate-editor] ul {
    list-style: disc;
}
.w-e-text-container [data-slate-editor] ol {
    list-style: decimal;
}
h1 {
    font-size: 2em;
}
h2 {
    font-size: 1.5em;
}
h3 {
    font-size: 1.17em;
}
h4 {
    font-size: 1em;
}
h5 {
    font-size: 0.83em;
}
h1,
h2,
h3,
h4,
h5 {
    font-weight: bold;
}

/* 增强样式 */
.w-e-text-container {
    /* 支持更多HTML元素的样式 */
    pre {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        overflow-x: auto;
        font-family: 'Courier New', Courier, monospace;
    }
    
    code {
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', Courier, monospace;
    }
    
    blockquote {
        border-left: 4px solid #ddd;
        padding-left: 16px;
        margin: 16px 0;
        color: #666;
    }
}

/* HTML源码编辑器样式 */
.el-textarea__inner {
    font-family: 'Courier New', Courier, monospace !important;
    line-height: 1.5 !important;
}
</style>
