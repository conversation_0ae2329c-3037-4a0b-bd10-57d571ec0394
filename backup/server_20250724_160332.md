# AI聊天系统 - 生产环境部署完整指南

## 📋 部署概述

本指南提供将开发环境代码迁移到生产环境的完整步骤。基于项目架构分析，需要拷贝替换以下核心文件夹。

## 🎯 需要拷贝的文件夹清单 【重要修正】

### ✅ 必须拷贝的文件夹

#### 1. **server/** - 后端核心服务 【唯一必需】
```bash
# 包含所有PHP后端代码、API接口、业务逻辑、编译后的前端文件
源路径: ./server/
目标路径: 生产环境/server/
说明: 包含后端PHP代码 + 所有前端编译后的静态文件
```

### ❌ 前端源码文件夹 【不需要拷贝】

#### 2. **admin/** - 管理后台源码 【不需要】
```bash
# Vue.js源码，编译后已在 server/public/admin/
状态: ❌ 不需要拷贝
说明: 这是源码文件夹，编译后的文件已在server/public/admin/
```

#### 3. **pc/** - PC端源码 【不需要】
```bash
# Nuxt.js源码，编译后已在 server/public/
状态: ❌ 不需要拷贝
说明: 这是源码文件夹，编译后的文件已在server/public/
```

#### 4. **uniapp/** - 移动端源码 【不需要】
```bash
# uni-app源码，编译后已在 server/public/mobile/ 和 server/public/weapp/
状态: ❌ 不需要拷贝
说明: 这是源码文件夹，编译后的文件已在server/public/
```

### ⚠️ 配置文件夹 【谨慎操作】

#### 5. **docker/** - Docker配置 【需要合并】
```bash
# Docker容器配置
源路径: ./docker/
目标路径: 生产环境/docker/
注意: 需要检查生产环境的具体配置差异
```

### ❌ 不需要拷贝的文件夹

#### 排除文件夹说明
```bash
# 这些文件夹不需要拷贝到生产环境
./yuanshi/          # 原始源代码参考，仅用于开发
./backup/           # 备份文件夹，生产环境会自己生成
./runtime/          # 运行时临时文件
./scripts/          # 开发脚本
./upgrade/          # 升级包文件
./.specstory/       # 开发历史记录
各种.md文档文件      # 开发文档
各种.log日志文件     # 开发日志
各种.sql数据文件     # 开发测试数据
```

## 🚀 完整部署步骤

### 第一步：生产环境备份
```bash
# 在生产环境执行备份
cd /生产环境路径
mkdir -p backup/production_backup_$(date +%Y%m%d_%H%M%S)

# 备份核心文件夹
cp -r server/ backup/production_backup_$(date +%Y%m%d_%H%M%S)/
cp -r admin/ backup/production_backup_$(date +%Y%m%d_%H%M%S)/
cp -r pc/ backup/production_backup_$(date +%Y%m%d_%H%M%S)/
cp -r uniapp/ backup/production_backup_$(date +%Y%m%d_%H%M%S)/
cp -r docker/ backup/production_backup_$(date +%Y%m%d_%H%M%S)/

# 备份数据库
mysqldump -h数据库主机 -u用户名 -p密码 数据库名 > backup/db_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 第二步：停止生产服务
```bash
# 停止Docker服务
cd /生产环境路径/docker
docker-compose down

# 或者单独停止服务
docker stop chatmoney-php chatmoney-nginx
```

### 第三步：复制新代码 【重要简化】
```bash
# 方法一：只拷贝server文件夹（推荐）
rsync -av --delete /开发环境路径/server/ /生产环境路径/server/

# 方法二：压缩传输（网络传输时使用）
# 在开发环境
tar -czf deployment_$(date +%Y%m%d_%H%M%S).tar.gz server/ docker/
# 传输到生产环境后解压
tar -xzf deployment_*.tar.gz

# 注意：前端编译文件已包含在server/public/目录中
# - server/public/admin/     ← admin项目编译后的文件
# - server/public/mobile/    ← uniapp H5编译后的文件  
# - server/public/weapp/     ← uniapp小程序编译后的文件
# - server/public/*.html     ← pc项目编译后的文件
```

### 第四步：检查配置差异
```bash
# 检查生产环境特殊配置
diff -r /生产环境路径/backup/production_backup_*/docker/ /生产环境路径/docker/

# 确保重要配置文件不被覆盖
# 检查数据库连接配置
# 检查Redis配置
# 检查域名和SSL配置
```

### 第五步：重新启动服务
```bash
# 重新启动Docker服务
cd /生产环境路径/docker
docker-compose up -d

# 检查服务状态
docker-compose ps
docker logs chatmoney-php
docker logs chatmoney-nginx
```

### 第六步：验证部署
```bash
# 检查前端是否正常
curl -I http://生产域名/
curl -I http://生产域名/admin/

# 检查API是否正常
curl http://生产域名/api/index/config

# 检查后台登录
# 测试前台功能
# 验证AI对话功能
```

## ⚙️ 关键配置检查清单

### 环境配置
- [ ] 数据库连接信息
- [ ] Redis连接信息  
- [ ] AI模型API密钥
- [ ] 文件上传路径
- [ ] 域名和SSL配置

### 权限设置
- [ ] 文件夹权限 755/644
- [ ] uploads目录可写权限
- [ ] runtime目录可写权限
- [ ] 日志目录权限

### 服务状态
- [ ] MySQL服务运行正常
- [ ] Redis服务运行正常
- [ ] PHP-FPM运行正常
- [ ] Nginx服务运行正常

## 🔧 常见问题解决

### 问题1：前端页面空白
```bash
# 检查Nginx配置
docker exec chatmoney-nginx nginx -t
# 检查文件权限
ls -la /生产环境路径/server/public/
```

### 问题2：API请求失败
```bash
# 检查PHP错误日志
docker logs chatmoney-php
# 检查数据库连接
docker exec chatmoney-php php think config:get database
```

### 问题3：静态资源加载失败
```bash
# 检查静态文件是否存在
ls -la /生产环境路径/server/public/admin/
ls -la /生产环境路径/server/public/mobile/
```

## 📊 部署完成验证

### 功能验证清单
- [ ] 管理后台登录正常
- [ ] PC端首页加载正常
- [ ] 移动端访问正常
- [ ] AI对话功能正常
- [ ] 用户注册登录正常
- [ ] VIP功能正常
- [ ] 智能体功能正常
- [ ] 知识库功能正常

### 性能验证
- [ ] 页面加载速度正常
- [ ] API响应时间正常
- [ ] 数据库查询性能正常
- [ ] 缓存功能正常

## 📝 部署记录

**部署时间**: 2025年7月24日
**部署版本**: 基于开发环境最新代码
**部署人员**: AI助手协助
**验证状态**: 等待用户确认

## 🎯 总结 【重要更正】

✅ **生产环境部署只需要拷贝替换2个文件夹：**
1. **server/** - 后端核心 + 所有前端编译文件（必须）
2. **docker/** - 容器配置（谨慎处理，检查配置差异）

❌ **不需要拷贝的文件夹：**
- `admin/` - 前端源码（编译后在server/public/admin/）
- `pc/` - 前端源码（编译后在server/public/）  
- `uniapp/` - 前端源码（编译后在server/public/mobile/和weapp/）
- `yuanshi/`、`backup/`、各种文档和日志文件

🔑 **关键发现：**
这是一个标准的前后端分离项目，前端代码在开发环境编译后，生成的静态文件已经放在了`server/public/`目录中：
- 管理后台：`server/public/admin/`
- PC端页面：`server/public/*.html` + `server/public/_nuxt/`
- H5移动端：`server/public/mobile/`
- 小程序：`server/public/weapp/`

因此，**只拷贝server文件夹就包含了完整的前后端代码**！

**注意**: 部署前务必备份生产环境数据，部署后要进行全面的功能验证。 