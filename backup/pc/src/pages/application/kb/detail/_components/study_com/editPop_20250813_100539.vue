<template>
    <Popup
        ref="popRef"
        title="录入数据"
        width="800px"
        async
        @confirm="submit"
        @close="$emit('close')"
    >
        <div>
        
            
            <!-- 重要提示 -->
            <div class="important-notice mb-4">
                <div class="notice-header">
                    <Icon name="el-icon-Warning" color="#e6a23c" size="18" />
                    <span class="notice-title">⚠️ 重要提示</span>
                </div>
                <div class="notice-content">
                    为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。
                </div>
            </div>
            
            <!-- 醒目提示 -->
            <div class="example-tip mb-4">
                <div class="tip-header">
                    <Icon name="el-icon-Lightbulb" color="#409EFF" size="18" />
                    <span class="tip-title">💡 小贴士</span>
                </div>
                <div class="tip-content">
                    可以试试直接选择示例，快速掌握知识库使用方法，成为AI达人
                </div>
            </div>
            
            <div class="mb-4">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-select
                            v-model="selectedCategoryId"
                            placeholder="请选择示例类别"
                            clearable
                            class="w-full"
                            @change="handleCategoryChange"
                        >
                            <el-option
                                v-for="category in exampleCategories"
                                :key="category.id"
                                :label="category.name"
                                :value="category.id"
                            />
                        </el-select>
                    </el-col>
                    <el-col :span="12">
                        <el-select
                            v-model="selectedExampleId"
                            placeholder="请选择具体示例"
                            clearable
                            class="w-full"
                            :disabled="!selectedCategoryId"
                            @change="handleExampleChange"
                            filterable
                        >
                            <el-option
                                v-for="example in currentExamples"
                                :key="example.id"
                                :label="example.title"
                                :value="example.id"
                            />
                        </el-select>
                    </el-col>
                </el-row>
            </div>
            <div class="grid grid-cols-2 gap-x-[20px]">
                <el-input
                    v-model="formData.question"
                    type="textarea"
                    placeholder="请输入文档内容，你可以理解为提问的问题（必填）"
                    rows="10"
                >
                </el-input>
                <el-input
                    v-model="formData.answer"
                    type="textarea"
                    placeholder="请填入补充内容，你可以理解为问题的答案"
                    rows="10"
                >
                </el-input>
            </div>
            <!-- 隐藏图片上传功能 -->
            <div class="mt-4" v-if="false">
                <Upload
                    v-model:files="formData.images"
                    type="image"
                    list-type="picture-card"
                    :limit="9"
                    multiple
                    show-file-list
                >
                    <div class="flex flex-col items-center justify-center">
                        <Icon name="el-icon-Plus" :size="20" />
                        <div class="text-info mt-2 text-sm">上传图片</div>
                    </div>
                </Upload>
                <div class="form-tips">最多上传9张</div>
            </div>
            <!-- 隐藏视频上传功能 -->
            <div class="mt-4" v-if="false">
                <UploadVideo v-model="video" size="80px"> </UploadVideo>
                <div class="form-tips">格式为MP4，大小不能超过20M</div>
            </div>
            <!-- 隐藏附件上传功能 -->
            <div class="mt-4" v-if="false">
                <Upload
                    v-model:files="formData.files"
                    type="file"
                    show-file-list
                >
                    <el-button>上传附件</el-button>
                    <template #tip>
                        <div class="el-upload__tip">
                            支持上传PDF、docx、excel、等文件格式
                        </div>
                    </template>
                </Upload>
            </div>
        </div>
    </Popup>
</template>

<script setup lang="ts">
import { itemDataImport, itemDataDetail, itemDataEdit, getAllExamples } from '@/api/my_database'
import { ElMessageBox } from 'element-plus'
import feedback from '@/utils/feedback'

const emits = defineEmits(['success', 'close'])

const popRef = shallowRef()

const video = ref('')
const formData = ref({
    kb_id: '',
    fd_id: '',
    question: '',
    answer: '',
    files: [],
    images: [],
    video: [],
    uuid: ''
})

// 示例库相关数据
const selectedCategoryId = ref('')
const selectedExampleId = ref('')
const exampleCategories = ref<any[]>([])
const allExamples = ref<any[]>([])
const currentExamples = computed(() => {
    if (!selectedCategoryId.value) return []
    const category = allExamples.value.find(c => c.id === selectedCategoryId.value)
    return category ? category.examples : []
})

// 处理类别选择变化
const handleCategoryChange = () => {
    selectedExampleId.value = ''
}

// 处理示例选择变化
const handleExampleChange = () => {
    const category = allExamples.value.find(c => c.id === selectedCategoryId.value)
    if (category) {
        const example = category.examples.find((e: any) => e.id === selectedExampleId.value)
        if (example) {
            formData.value.question = example.question
            formData.value.answer = example.answer
        }
    }
}

// 获取示例库数据
const fetchExamples = async () => {
    try {
        const res = await getAllExamples()
        allExamples.value = res
        exampleCategories.value = res.map((category: any) => ({
            id: category.id,
            name: category.name
        }))
    } catch (error) {
        console.error('获取示例库数据失败:', error)
    }
}

watch(video, (value) => {
    formData.value.video = [{ url: value, name: '' }] as any
})
//提交数据
const { lockFn: submit } = useLockFn(async () => {
    try {
        if (formData.value.uuid) {
            await itemDataEdit({ ...formData.value })
        } else {
            await itemDataImport({ ...formData.value })
        }
        emits('success')
    } catch (apiError: any) {
        // 检查是否是知识库关联智能体已上架需要确认的错误
        if (apiError.msg === 'KB_RELATED_ROBOTS_ONLINE') {
            console.log('🔍 [知识库编辑] 检测到KB_RELATED_ROBOTS_ONLINE错误');
            console.log('📊 [知识库编辑] 关联智能体数据:', apiError.data);

            const relatedRobots = apiError.data || {};
            const robotList = relatedRobots.related_robots || [];

            console.log('📝 [知识库编辑] 智能体列表:', robotList);

            // 构建提示信息 - 添加安全检查
            const onlineRobots = robotList.filter((robot: any) => robot && robot.is_online);
            console.log('🟢 [知识库编辑] 已上架智能体:', onlineRobots);

            if (onlineRobots.length === 0) {
                console.warn('⚠️ [知识库编辑] 没有找到已上架的智能体，但收到了KB_RELATED_ROBOTS_ONLINE错误');
                throw apiError; // 如果没有已上架智能体，按普通错误处理
            }

            const robotNames = onlineRobots
                .map((robot: any) => `• ${robot.name || '未知智能体'}`)
                .join('\n');

            const message = `此知识库被以下已上架的智能体使用：\n${robotNames}\n\n编辑后这些智能体将自动从广场下架，是否继续？`

            // 显示确认对话框
            const confirmResult = await ElMessageBox.confirm(
                message,
                '知识库编辑确认',
                {
                    confirmButtonText: '确认编辑',
                    cancelButtonText: '取消',
                    type: 'warning',
                    center: true
                }
            ).catch(() => false)

            if (confirmResult) {
                // 用户确认，带上确认标识重新提交
                const confirmData = { ...formData.value, confirm_offline: true }
                if (formData.value.uuid) {
                    await itemDataEdit(confirmData)
                } else {
                    await itemDataImport(confirmData)
                }
                feedback.msgSuccess('知识库已更新，关联的智能体已从广场下架')
                emits('success')
            }
        } else {
            // 其他错误正常处理
            throw apiError
        }
    }
})

//获取详情
const getDetail = async () => {
    const res = await itemDataDetail({ uuid: formData.value.uuid })
    Object.keys(formData.value).map((item) => {
        //@ts-ignore
        formData.value[item] = res[item]
    })
    video.value = res.video[0]?.url || ''
}

const open = (ids: any) => {
    popRef.value.open()
    
    // 重置选择状态
    selectedCategoryId.value = ''
    selectedExampleId.value = ''
    
    ;[formData.value.kb_id, formData.value.fd_id, formData.value.uuid] = [
        ids.kb_id,
        ids.fd_id,
        ids.uuid || ''
    ]

    if (ids.hasOwnProperty('uuid')) {
        getDetail()
    }
    
    // 获取示例库数据
    fetchExamples()
}

defineExpose({ open })
</script>

<style scoped lang="scss">
.important-notice {
    background: linear-gradient(135deg, #fff8e6 0%, #fff3d6 100%);
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
    
    .notice-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .notice-title {
            font-weight: 600;
            color: #e6a23c;
            margin-left: 8px;
            font-size: 16px;
        }
    }
    
    .notice-content {
        color: #856404;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        padding-left: 26px;
    }
}

.example-tip {
    background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
    border: 1px solid #409EFF;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
    
    .tip-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .tip-title {
            font-weight: 600;
            color: #409EFF;
            margin-left: 8px;
            font-size: 16px;
        }
    }
    
    .tip-content {
        color: #1f2937;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        padding-left: 26px;
    }
}
</style>
