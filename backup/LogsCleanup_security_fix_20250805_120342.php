<?php
// +----------------------------------------------------------------------
// | 系统日志清理定时任务
// +----------------------------------------------------------------------

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use Exception;

/**
 * 系统日志清理定时任务
 * 清理系统操作日志、用户账户变动日志等运维数据
 */
class LogsCleanup extends Command
{
    // 清理配置
    private array $logTables = [
        'cm_operation_log' => ['name' => '系统操作日志', 'retention_days' => 90, 'export' => true],
        'cm_user_account_log' => ['name' => '用户账户日志', 'retention_days' => 365, 'export' => false],
        'cm_email_log' => ['name' => '邮件发送日志', 'retention_days' => 180, 'export' => false],
        'cm_sms_log' => ['name' => '短信发送日志', 'retention_days' => 180, 'export' => false]
    ];
    
    // 默认保留180天
    private int $defaultRetentionDays = 180;
    
    // 批处理大小
    private int $batchSize = 1000;
    
    // 日志文件路径
    private string $logFile = '';
    
    // 导出目录
    private string $exportDir = '';
    
    // 日志文件最大大小 (10MB)
    private int $maxLogSize = 10 * 1024 * 1024;

    protected function configure(): void
    {
        $this->setName('logs:cleanup')
            ->addOption('days', 'd', \think\console\input\Option::VALUE_OPTIONAL, '保留天数', 180)
            ->addOption('dry-run', null, \think\console\input\Option::VALUE_NONE, '预览模式，不实际删除')
            ->addOption('batch-size', 'b', \think\console\input\Option::VALUE_OPTIONAL, '批处理大小', 1000)
            ->addOption('export-format', 'f', \think\console\input\Option::VALUE_OPTIONAL, '导出格式: csv|json|sql', 'json')
            ->addOption('skip-export', null, \think\console\input\Option::VALUE_NONE, '跳过数据导出')
            ->setDescription('系统日志清理命令（支持数据导出）');
    }

    /**
     * 执行清理任务
     */
    protected function execute(Input $input, Output $output)
    {
        // 初始化
        $this->initPaths();
        
        $days = (int)$input->getOption('days');
        $isDryRun = $input->getOption('dry-run');
        $this->batchSize = (int)$input->getOption('batch-size') ?: 1000;
        $exportFormat = $input->getOption('export-format') ?: 'json';
        $skipExport = $input->getOption('skip-export');
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $output->writeln("=== 系统日志清理任务开始 ===");
        $output->writeln("保留期限: {$days}天");
        $output->writeln("截止日期: {$cutoffDate}");
        $output->writeln("导出格式: {$exportFormat}");
        
        if ($isDryRun) {
            $output->writeln("【预览模式】- 仅显示统计信息，不实际删除数据");
        }
        
        if ($skipExport) {
            $output->writeln("【跳过导出】- 不导出数据，直接删除");
        }
        
        try {
            $results = [];
            $totalCleaned = 0;
            $exportedFiles = [];
            
            // 清理各类日志表
            foreach ($this->logTables as $tableName => $config) {
                $tableDays = $days > 0 ? $days : $config['retention_days'];
                $tableResult = $this->cleanupLogTable(
                    $tableName, 
                    $config, 
                    $tableDays, 
                    $isDryRun, 
                    $skipExport ? false : $config['export'], 
                    $exportFormat, 
                    $output
                );
                
                $results[$tableName] = $tableResult;
                $totalCleaned += $tableResult['cleaned_count'];
                
                if (!empty($tableResult['export_file'])) {
                    $exportedFiles[] = $tableResult['export_file'];
                }
            }
            
            // 清理runtime日志文件
            $fileResult = $this->cleanupRuntimeLogs($days, $isDryRun, $output);
            $results['runtime_files'] = $fileResult;
            
            // 输出清理总结
            $this->outputSummary($results, $totalCleaned, $days, $isDryRun, $exportedFiles, $output);
            
            // 记录清理结果
            if (!$isDryRun && $totalCleaned > 0) {
                $this->logCleanupResult($days, $results, $totalCleaned, $exportedFiles);
            }
            
        } catch (\Exception $e) {
            $output->writeln("❌ 清理失败: " . $e->getMessage());
            $this->logError($e);
        }
        
        $output->writeln("=== 清理任务完成 ===");
    }

    /**
     * 清理指定日志表
     */
    private function cleanupLogTable(string $tableName, array $config, int $days, bool $dryRun, bool $exportData, string $exportFormat, Output $output): array
    {
        $output->writeln("📝 清理{$config['name']}");
        
        $cutoffTimestamp = strtotime("-{$days} days");
        $exportFile = '';
        
        // 检查表是否存在
        if (!$this->tableExists($tableName)) {
            $output->writeln("⚠️  表 {$tableName} 不存在，跳过清理");
            return ['cleaned_count' => 0, 'description' => $config['name'], 'export_file' => ''];
        }
        
        // 获取需要清理的记录统计
        $totalQuery = "SELECT COUNT(*) as total FROM `{$tableName}` 
                      WHERE create_time < {$cutoffTimestamp}";
        
        $result = Db::query($totalQuery);
        $totalCount = $result[0]['total'] ?? 0;
        
        if ($totalCount == 0) {
            $output->writeln("  ✅ 没有需要清理的记录");
            return ['cleaned_count' => 0, 'description' => $config['name'], 'export_file' => ''];
        }
        
        if ($dryRun) {
            $output->writeln("  📊 发现 {$totalCount} 条可清理记录");
            return ['cleaned_count' => $totalCount, 'description' => $config['name'], 'export_file' => ''];
        }
        
        // 导出数据（如果需要）
        if ($exportData && $totalCount > 0) {
            $exportFile = $this->exportTableData($tableName, $cutoffTimestamp, $exportFormat, $output);
        }
        
        $output->writeln("  📊 发现 {$totalCount} 条记录需要清理，开始分批处理...");
        
        // 分批清理
        $totalCleaned = 0;
        $processed = 0;
        
        while ($processed < $totalCount) {
            $currentBatchSize = min($this->batchSize, $totalCount - $processed);
            
            // 删除记录
            $deleteQuery = "DELETE FROM `{$tableName}` 
                          WHERE create_time < {$cutoffTimestamp} 
                          LIMIT {$currentBatchSize}";
            
            $affected = Db::execute($deleteQuery);
            $processed += $affected;
            $totalCleaned += $affected;
            
            $output->writeln("  ⏳ 已处理 {$processed}/{$totalCount} 条记录");
            
            if ($affected == 0) {
                break; // 没有更多记录可处理
            }
            
            // 休息一下，避免给数据库造成压力
            usleep(50000); // 0.05秒
        }
        
        $output->writeln("  ✅ 完成清理，共处理 {$totalCleaned} 条记录");
        
        return [
            'cleaned_count' => $totalCleaned, 
            'description' => $config['name'],
            'export_file' => $exportFile
        ];
    }

    /**
     * 导出表数据
     */
    private function exportTableData(string $tableName, int $cutoffTimestamp, string $format, Output $output): string
    {
        $fileName = $tableName . '_' . date('Y-m-d_H-i-s') . '.' . $format;
        $filePath = $this->exportDir . '/' . $fileName;
        
        try {
            $output->writeln("  📦 正在导出数据到 {$fileName}...");
            
            // 查询需要导出的数据
            $exportQuery = "SELECT * FROM `{$tableName}` WHERE create_time < {$cutoffTimestamp}";
            $data = Db::query($exportQuery);
            
            if (empty($data)) {
                return '';
            }
            
            switch (strtolower($format)) {
                case 'json':
                    $content = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                    break;
                    
                case 'csv':
                    $content = $this->arrayToCsv($data);
                    break;
                    
                case 'sql':
                    $content = $this->arrayToSql($tableName, $data);
                    break;
                    
                default:
                    throw new \Exception("不支持的导出格式: {$format}");
            }
            
            // 检查文件大小
            if (strlen($content) > $this->maxLogSize) {
                $output->writeln("  ⚠️ 导出文件过大，进行压缩...");
                $content = gzcompress($content);
                $filePath .= '.gz';
                $fileName .= '.gz';
            }
            
            file_put_contents($filePath, $content);
            $fileSize = round(filesize($filePath) / 1024 / 1024, 2);
            
            $output->writeln("  ✅ 数据已导出: {$fileName} ({$fileSize}MB)");
            
            return $fileName;
            
        } catch (\Exception $e) {
            $output->writeln("  ❌ 导出失败: " . $e->getMessage());
            return '';
        }
    }

    /**
     * 清理runtime日志文件
     */
    private function cleanupRuntimeLogs(int $days, bool $dryRun, Output $output): array
    {
        $output->writeln("📁 清理runtime日志文件");
        
        $runtimeLogDir = runtime_path('log');
        $cutoffTime = time() - ($days * 24 * 3600);
        $cleanedCount = 0;
        
        if (!is_dir($runtimeLogDir)) {
            $output->writeln("  ⚠️ runtime/log目录不存在");
            return ['cleaned_count' => 0, 'description' => 'Runtime日志文件'];
        }
        
        $files = glob($runtimeLogDir . '/*.log*');
        $oldFiles = [];
        
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoffTime) {
                $oldFiles[] = $file;
            }
        }
        
        if (empty($oldFiles)) {
            $output->writeln("  ✅ 没有需要清理的日志文件");
            return ['cleaned_count' => 0, 'description' => 'Runtime日志文件'];
        }
        
        if ($dryRun) {
            $output->writeln("  📊 发现 " . count($oldFiles) . " 个可清理的日志文件");
            foreach ($oldFiles as $file) {
                $fileName = basename($file);
                $fileSize = round(filesize($file) / 1024, 2);
                $output->writeln("    - {$fileName} ({$fileSize}KB)");
            }
            return ['cleaned_count' => count($oldFiles), 'description' => 'Runtime日志文件'];
        }
        
        foreach ($oldFiles as $file) {
            try {
                $fileName = basename($file);
                $fileSize = round(filesize($file) / 1024, 2);
                
                if (unlink($file)) {
                    $cleanedCount++;
                    $output->writeln("  ✅ 删除文件: {$fileName} ({$fileSize}KB)");
                } else {
                    $output->writeln("  ⚠️ 删除失败: {$fileName}");
                }
            } catch (\Exception $e) {
                $output->writeln("  ❌ 删除文件失败: " . $e->getMessage());
            }
        }
        
        return ['cleaned_count' => $cleanedCount, 'description' => 'Runtime日志文件'];
    }

    /**
     * 检查表是否存在
     */
    private function tableExists(string $tableName): bool
    {
        try {
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 数组转CSV
     */
    private function arrayToCsv(array $data): string
    {
        if (empty($data)) {
            return '';
        }
        
        $csv = '';
        $headers = array_keys($data[0]);
        $csv .= implode(',', $headers) . "\n";
        
        foreach ($data as $row) {
            $escapedRow = array_map(function($value) {
                return '"' . str_replace('"', '""', $value) . '"';
            }, $row);
            $csv .= implode(',', $escapedRow) . "\n";
        }
        
        return $csv;
    }

    /**
     * 数组转SQL
     */
    private function arrayToSql(string $tableName, array $data): string
    {
        if (empty($data)) {
            return '';
        }
        
        $sql = "-- 备份数据来源表: {$tableName}\n";
        $sql .= "-- 导出时间: " . date('Y-m-d H:i:s') . "\n\n";
        
        $headers = array_keys($data[0]);
        $columns = '`' . implode('`, `', $headers) . '`';
        
        foreach ($data as $row) {
            $values = array_map(function($value) {
                if (is_null($value)) {
                    return 'NULL';
                }
                return "'" . addslashes($value) . "'";
            }, $row);
            
            $sql .= "INSERT INTO `{$tableName}` ({$columns}) VALUES (" . implode(', ', $values) . ");\n";
        }
        
        return $sql;
    }

    /**
     * 输出清理总结
     */
    private function outputSummary(array $results, int $total, int $days, bool $dryRun, array $exportedFiles, Output $output): void
    {
        $output->writeln("=== 清理总结 ===");
        
        if ($total == 0) {
            $output->writeln("✨ 没有发现需要清理的日志记录");
            return;
        }
        
        foreach ($results as $table => $info) {
            $output->writeln("📝 {$info['description']}: {$info['cleaned_count']} 条记录");
        }
        
        $action = $dryRun ? '发现' : '清理';
        $output->writeln("🎯 总计{$action}: {$total} 条日志记录（保留{$days}天内）");
        
        if (!empty($exportedFiles)) {
            $output->writeln("📦 已导出的数据文件:");
            foreach ($exportedFiles as $file) {
                $output->writeln("  - {$file}");
            }
            $output->writeln("💡 导出文件保存在: {$this->exportDir}");
        }
        
        if ($dryRun) {
            $output->writeln("💡 要执行实际清理，请移除 --dry-run 参数");
        } else {
            $output->writeln("💾 已释放数据库和文件系统存储空间");
        }
    }

    /**
     * 初始化路径
     */
    private function initPaths(): void
    {
        $logDir = runtime_path('log');
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFile = $logDir . '/logs_cleanup.log';
        
        // 创建导出目录
        $this->exportDir = runtime_path('export');
        if (!is_dir($this->exportDir)) {
            mkdir($this->exportDir, 0755, true);
        }
        
        // 清理旧的导出文件（保留最近7天）
        $this->cleanupOldExports();
    }

    /**
     * 清理旧的导出文件
     */
    private function cleanupOldExports(): void
    {
        $files = glob($this->exportDir . '/*');
        $cutoffTime = time() - (7 * 24 * 3600); // 7天前
        
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoffTime) {
                unlink($file);
            }
        }
    }

    /**
     * 记录清理结果到日志文件
     */
    private function logCleanupResult(int $days, array $results, int $total, array $exportedFiles): void
    {
        $logData = [
            'cleanup_date' => date('Y-m-d H:i:s'),
            'retention_days' => $days,
            'cleanup_results' => $results,
            'total_cleaned' => $total,
            'exported_files' => $exportedFiles,
            'execution_type' => 'logs_cleanup'
        ];
        
        $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 记录错误日志
     */
    private function logError(Exception $e): void
    {
        $errorData = [
            'error_date' => date('Y-m-d H:i:s'),
            'error_message' => $e->getMessage(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'execution_type' => 'logs_cleanup_error'
        ];
        
        $logContent = json_encode($errorData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 写入日志
     */
    private function writeLog(string $content): void
    {
        $logEntry = "[" . date('Y-m-d H:i:s') . "] " . $content . PHP_EOL;
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
} 