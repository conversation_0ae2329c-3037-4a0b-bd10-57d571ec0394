{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.0", "topthink/framework": "^6.0.0", "topthink/think-orm": "^2.0", "topthink/think-multi-app": "^1.0", "topthink/think-view": "^1.0", "dragonmantank/cron-expression": "^3.3", "phpoffice/phpspreadsheet": "^1.22", "qiniu/php-sdk": "7.4", "qcloud/cos-sdk-v5": "^2.5", "aliyuncs/oss-sdk-php": "^2.4", "alibabacloud/client": "^1.5", "rmccue/requests": "^2.0", "w7corp/easywechat": "^6.8", "tencentcloud/sms": "^3.0", "ext-curl": "*", "ext-iconv": "*", "ext-zip": "*", "ext-pdo": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-redis": "*", "alipaysdk/easysdk": "^2.2", "phpoffice/phpword": "^1.1", "firebase/php-jwt": "^6.10", "textalk/websocket": "^1.6", "ramsey/uuid": "^4.7", "gioni06/gpt3-tokenizer": "^1.2", "topthink/think-queue": "^3.0", "lustre/php-dfa-sensitive": "^1.4", "phpmailer/phpmailer": "^6.9", "iflytekop/xfyun-sdk": "^2.0", "fabpot/goutte": "^4.0", "volcengine/volc-sdk-php": "^1.0"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "allow-plugins": {"easywechat-composer/easywechat-composer": false, "topthink/think-installer": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}