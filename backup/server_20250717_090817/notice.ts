import request from '@/utils/request'

// 获取公告配置详情
export const getBulletinDetail = () => {
  return request.get({ url: '/setting.bulletin/detail' })
}

// 保存公告配置
export const saveBulletin = (data: any) => {
  return request.post({ url: '/setting.bulletin/save', data })
}

// 获取公告列表 (保留原功能)
export const getNoticeList = (params: any) => {
  return request.get({ url: '/notice.notice/settingLists', params })
}

// 获取公告详情 (保留原功能)
export const getNoticeDetail = (id: string | number) => {
  return request.get({ url: `/setting/notice/detail`, params: { id } })
}

// 保存公告 (保留原功能)
export const saveNotice = (data: any) => {
  return request.post({ url: '/setting/notice/save', data })
}

// 删除公告
export const deleteNotice = (id: number) => {
  return request.delete({ url: `/setting/notice/delete/${id}` })
}

// 更新公告状态 (保留原功能)
export const updateNoticeStatus = (id: string | number, status: number) => {
  return request.post({ url: '/setting/notice/status', data: { id, status } })
}
