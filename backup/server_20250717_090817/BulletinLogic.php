<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\logic\setting;

use app\common\logic\BaseLogic;
use app\common\service\ConfigService;

/**
 * 公告设置逻辑类
 */
class BulletinLogic extends BaseLogic
{
    /**
     * @notes 获取公告配置详情
     * @return array
     * <AUTHOR>
     * @date 2024/1/10 15:28
     */
    public static function detail(): array
    {
        return [
            'is_bulletin' => intval(ConfigService::get('bulletin', 'is_bulletin', 0)),
            'bulletin_content' => ConfigService::get('bulletin', 'bulletin_content', '')
        ];
    }

    /**
     * @notes 保存公告配置
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2024/1/10 15:28
     */
    public static function save(array $params): bool
    {
        try {
            $data = [
                'is_bulletin' => intval($params['is_bulletin'] ?? 0),
                'bulletin_content' => $params['bulletin_content'] ?? ''
            ];
            
            ConfigService::set('bulletin', 'is_bulletin', $data['is_bulletin']);
            ConfigService::set('bulletin', 'bulletin_content', $data['bulletin_content']);
            
            return true;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
}