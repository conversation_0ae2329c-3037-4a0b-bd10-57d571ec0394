mysqldump: [Warning] Using a password on the command line interface can be insecure.
-- My<PERSON><PERSON> dump 10.13  Distrib 5.7.29, for Linux (x86_64)
--
-- Host: localhost    Database: chatmoney
-- ------------------------------------------------------
-- Server version	5.7.29

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `cm_example_content`
--

DROP TABLE IF EXISTS `cm_example_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cm_example_content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属类别ID',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '示例标题',
  `question` text NOT NULL COMMENT '问题内容',
  `answer` text NOT NULL COMMENT '答案内容',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-关闭',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '修改时间',
  `delete_time` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_category_id` (`category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='示例库内容表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cm_example_content`
--

LOCK TABLES `cm_example_content` WRITE;
/*!40000 ALTER TABLE `cm_example_content` DISABLE KEYS */;
INSERT INTO `cm_example_content` VALUES (1,2,'2223333','333333333','444333333',0,1,1747830890,1748068373,1753782223),(2,1,'2222333','2222333','2222333',0,1,1747882286,1748419952,1753782225),(3,3,'444','777777777777777777','333',0,1,1747887315,1748067708,1753782227),(4,2,'23213','421421','3213213',0,1,1747990211,1747990211,1748419946),(5,4,'6666','6666','666666',0,1,1748055194,1748055194,1748058329),(6,4,'123','777','777',0,1,1748058427,1748058427,1748068378),(7,4,'5555','55555','555555',0,1,1748059166,1748059166,1748059169),(8,4,'33333','333333','3333333',1,1,1748064949,1748064949,1753782221),(9,4,'6666','6666666','6666666666',0,1,1748066491,1748066491,1748068330),(10,4,'3333','3333','33311111',0,1,1748419961,1748419961,1753782228),(15,83,'家庭饮食营养规划','请详细介绍家庭成员的基本信息，包括成员数量、年龄、性别、身高、体重等，这些信息将用于制定个性化的营养方案。\n\n请说明每位家庭成员的饮食喜好和禁忌，包括喜欢的食物、不喜欢的食物、过敏食物等，以便为您推荐合适的菜谱。\n\n家庭成员是否有特殊的饮食需求？比如减肥、增肌、调理身体、疾病饮食管理等，请详细说明。','家庭成员构成（共6人）：\n\n? 男主人：36岁，身高173cm，体重75kg，办公室工作，运动量中等\n? 女主人：35岁，身高157cm，体重62.5kg，居家工作，运动量较少  \n? 大儿子：5岁，身高134cm，体重21kg，活泼好动，正值生长发育期\n? 小女儿：4岁，身高123cm，体重16.5kg，活动量中等，食量较小\n? 爷爷：60岁，身高168cm，体重70kg，轻微高血压，需控制盐分\n? 奶奶：61岁，身高155cm，体重58kg，身体健康，消化能力较弱\n\n成员特点说明：\n• 男主人和女主人都是上班族，工作压力适中\n• 两个孩子正处于快速成长期，营养需求较高\n• 爷爷奶奶年纪较大，需要特别关注营养吸收和消化问题\n• 家庭整体健康状况良好，无严重疾病史\n\n---\n\n饮食偏好详情：\n\n? 男主人：\n• 喜欢：豆制品、牛肉、坚果类、深海鱼\n• 不喜欢：芹菜、苦瓜、动物内脏\n• 过敏情况：无食物过敏\n• 饮食习惯：喜欢清淡口味，偶尔喝酒\n\n? 女主人：\n• 喜欢：土豆、番茄、绿叶蔬菜、水果\n• 不喜欢：鸡蛋、海鲜、过于油腻的食物\n• 过敏情况：对虾类轻微过敏\n• 饮食习惯：偏爱素食，注重营养搭配\n\n? 大儿子：\n• 喜欢：西红柿、胡萝卜、苹果、牛奶\n• 不喜欢：绿叶蔬菜、辛辣食物\n• 过敏情况：无食物过敏\n• 饮食习惯：活泼好动，食量较大\n\n? 小女儿：\n• 喜欢：菜花、玉米、酸奶、香蕉\n• 不喜欢：蘑菇、茄子、太甜的食物\n• 过敏情况：无食物过敏\n• 饮食习惯：挑食较轻，愿意尝试新食物\n\n?? 爷爷奶奶：\n• 共同喜欢：清淡口味、软烂易消化的食物、粥类\n• 共同不喜欢：肥肉、过分油腻、太硬的食物\n• 特殊要求：爷爷需避免高盐食物，奶奶喜欢温热食物\n\n---\n\n特殊饮食需求：\n\n? 男主人：\n• 目标：健身塑形，增加肌肉量\n• 需求：高蛋白、适量碳水化合物、控制脂肪\n• 建议摄入：每日蛋白质120-140g\n\n? 女主人：\n• 目标：健康减重，目标体重55kg\n• 需求：控制总热量，保证营养均衡\n• 建议摄入：每日1200-1400大卡\n\n?? 孩子们：\n• 目标：健康成长发育\n• 需求：营养全面，钙质充足，适量零食\n• 重点：保证优质蛋白质和维生素摄入\n\n? 爷爷：\n• 目标：血压控制，心血管健康\n• 需求：低盐低脂，富含钾元素的食物\n• 建议：每日盐分不超过5g\n\n? 奶奶：\n• 目标：消化健康，骨骼强健\n• 需求：易消化、高钙、适量膳食纤维\n• 建议：多摄入奶制品和豆制品',100,1,1753706773,1753751354,NULL);
/*!40000 ALTER TABLE `cm_example_content` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-29 17:48:02
