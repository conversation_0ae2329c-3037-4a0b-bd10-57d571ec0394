<?php
// +----------------------------------------------------------------------
// | 智能体分成记录清理定时任务
// +----------------------------------------------------------------------

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\common\model\kb\KbRobotRevenueLog;
use Exception;

/**
 * 智能体分成收益记录清理定时任务
 * 专门清理已结算的历史分成记录，保留财务数据一致性
 */
class RevenueCleanup extends Command
{
    // 清理配置
    private array $revenueTables = [
        'cm_kb_robot_revenue_log' => '智能体分成记录'
    ];
    
    // 默认保留365天（一年）- 财务数据保留期较长
    private int $defaultRetentionDays = 365;
    
    // 批处理大小
    private int $batchSize = 500;
    
    // 日志文件路径
    private string $logFile = '';
    
    // 日志文件最大大小 (5MB)
    private int $maxLogSize = 5 * 1024 * 1024;

    protected function configure(): void
    {
        $this->setName('revenue:cleanup')
            ->addOption('days', 'd', \think\console\input\Option::VALUE_OPTIONAL, '保留天数（建议≥365天）', 365)
            ->addOption('dry-run', null, \think\console\input\Option::VALUE_NONE, '预览模式，不实际删除')
            ->addOption('batch-size', 'b', \think\console\input\Option::VALUE_OPTIONAL, '批处理大小', 500)
            ->setDescription('智能体分成记录清理命令（财务敏感数据）');
    }

    /**
     * 执行清理任务
     */
    protected function execute(Input $input, Output $output)
    {
        // 初始化日志文件
        $this->initLogFile();
        
        $days = (int)$input->getOption('days');
        $isDryRun = $input->getOption('dry-run');
        $this->batchSize = (int)$input->getOption('batch-size') ?: 500;
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $output->writeln("=== 智能体分成记录清理任务开始 ===");
        $output->writeln("保留期限: {$days}天");
        $output->writeln("截止日期: {$cutoffDate}");
        
        // 财务数据安全警告
        if ($days < 365) {
            $output->writeln("<error>⚠️ 警告：财务数据保留期少于一年存在合规风险！</error>");
            $output->writeln("<comment>建议保留期至少365天以满足财务审计要求</comment>");
        }
        
        if ($isDryRun) {
            $output->writeln("<comment>【预览模式】- 仅显示统计信息，不实际删除数据</comment>");
        }
        
        try {
            $results = [];
            $totalCleaned = 0;
            
            // 清理已结算的分成记录
            $revenueResult = $this->cleanupRevenueRecords($cutoffDate, $isDryRun, $output);
            $results['revenue_records'] = $revenueResult;
            $totalCleaned += $revenueResult['cleaned_count'];
            
            // 输出清理总结
            $this->outputSummary($results, $totalCleaned, $days, $isDryRun, $output);
            
            // 记录清理结果
            if (!$isDryRun && $totalCleaned > 0) {
                $this->logCleanupResult($days, $results, $totalCleaned);
            }
            
        } catch (\Exception $e) {
            $output->writeln("<error>❌ 清理失败: " . $e->getMessage() . "</error>");
            $this->logError($e);
        }
        
        $output->writeln("=== 清理任务完成 ===");
    }

    /**
     * 清理智能体分成记录
     */
    private function cleanupRevenueRecords(string $cutoffDate, bool $dryRun, Output $output): array
    {
        $output->writeln("<info>💰 清理智能体分成记录</info>");
        
        $cutoffTimestamp = strtotime($cutoffDate);
        $tableName = 'cm_kb_robot_revenue_log';
        
        // 检查表是否存在
        if (!$this->tableExists($tableName)) {
            $output->writeln("<comment>⚠️  表 {$tableName} 不存在，跳过清理</comment>");
            return ['cleaned_count' => 0, 'description' => '智能体分成记录'];
        }
        
        // 获取已结算记录统计
        $totalQuery = "SELECT COUNT(*) as total FROM `{$tableName}` 
                      WHERE create_time < {$cutoffTimestamp} 
                      AND settle_status = 1";  // 只清理已结算的记录
        
        $result = Db::query($totalQuery);
        $totalCount = $result[0]['total'] ?? 0;
        
        // 获取待结算记录统计（用于警告）
        $pendingQuery = "SELECT COUNT(*) as pending FROM `{$tableName}` 
                        WHERE create_time < {$cutoffTimestamp} 
                        AND settle_status = 0";
        
        $pendingResult = Db::query($pendingQuery);
        $pendingCount = $pendingResult[0]['pending'] ?? 0;
        
        if ($pendingCount > 0) {
            $output->writeln("<error>⚠️ 发现 {$pendingCount} 条未结算的历史记录</error>");
            $output->writeln("<comment>  建议先执行分成结算: php think robot_revenue_settle</comment>");
        }
        
        if ($totalCount == 0) {
            $output->writeln("<comment>  ✅ 没有需要清理的已结算记录</comment>");
            return ['cleaned_count' => 0, 'description' => '智能体分成记录'];
        }
        
        if ($dryRun) {
            $output->writeln("<comment>  📊 发现 {$totalCount} 条可清理的已结算记录</comment>");
            
            // 显示金额统计
            $amountQuery = "SELECT 
                              SUM(share_amount) as total_share_amount,
                              SUM(platform_amount) as total_platform_amount,
                              SUM(total_cost) as total_cost
                           FROM `{$tableName}` 
                           WHERE create_time < {$cutoffTimestamp} 
                           AND settle_status = 1";
            
            $amountResult = Db::query($amountQuery);
            if (!empty($amountResult[0])) {
                $amounts = $amountResult[0];
                $output->writeln("<comment>  💰 涉及金额统计：</comment>");
                $output->writeln("<comment>    - 分成金额: {$amounts['total_share_amount']} 灵感值</comment>");
                $output->writeln("<comment>    - 平台金额: {$amounts['total_platform_amount']} 灵感值</comment>");
                $output->writeln("<comment>    - 总消费: {$amounts['total_cost']} 灵感值</comment>");
            }
            
            return ['cleaned_count' => $totalCount, 'description' => '智能体分成记录'];
        }
        
        $output->writeln("<info>  📊 发现 {$totalCount} 条已结算记录需要清理，开始分批处理...</info>");
        
        // 分批清理（使用归档+删除方式）
        $totalCleaned = 0;
        $processed = 0;
        
        while ($processed < $totalCount) {
            $currentBatchSize = min($this->batchSize, $totalCount - $processed);
            
            // 创建归档记录（可选，用于审计）
            $archiveQuery = "INSERT INTO `{$tableName}_archive` 
                           SELECT *, NOW() as archived_at 
                           FROM `{$tableName}` 
                           WHERE create_time < {$cutoffTimestamp} 
                           AND settle_status = 1 
                           LIMIT {$currentBatchSize}";
            
            // 如果归档表不存在，跳过归档步骤
            if ($this->tableExists($tableName . '_archive')) {
                try {
                    Db::execute($archiveQuery);
                    $output->writeln("<comment>    📦 已归档 {$currentBatchSize} 条记录</comment>");
                } catch (\Exception $e) {
                    $output->writeln("<comment>    ⚠️ 归档失败，直接删除: " . $e->getMessage() . "</comment>");
                }
            }
            
            // 删除已处理的记录
            $deleteQuery = "DELETE FROM `{$tableName}` 
                          WHERE create_time < {$cutoffTimestamp} 
                          AND settle_status = 1 
                          LIMIT {$currentBatchSize}";
            
            $affected = Db::execute($deleteQuery);
            $processed += $affected;
            $totalCleaned += $affected;
            
            $output->writeln("<info>  ⏳ 已处理 {$processed}/{$totalCount} 条记录</info>");
            
            if ($affected == 0) {
                break; // 没有更多记录可处理
            }
            
            // 休息一下，避免给数据库造成压力
            usleep(100000); // 0.1秒
        }
        
        $output->writeln("<info>  ✅ 完成清理，共处理 {$totalCleaned} 条已结算记录</info>");
        
        return ['cleaned_count' => $totalCleaned, 'description' => '智能体分成记录'];
    }

    /**
     * 检查表是否存在
     */
    private function tableExists(string $tableName): bool
    {
        try {
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 输出清理总结
     */
    private function outputSummary(array $results, int $total, int $days, bool $dryRun, Output $output): void
    {
        $output->writeln("<info>=== 清理总结 ===</info>");
        
        if ($total == 0) {
            $output->writeln("<comment>✨ 没有发现需要清理的分成记录</comment>");
            return;
        }
        
        foreach ($results as $key => $info) {
            $output->writeln("<info>💰 {$info['description']}: {$info['cleaned_count']} 条记录</info>");
        }
        
        $action = $dryRun ? '发现' : '清理';
        $output->writeln("<info>🎯 总计{$action}: {$total} 条分成记录（保留{$days}天内）</info>");
        
        if ($dryRun) {
            $output->writeln("<comment>💡 要执行实际清理，请移除 --dry-run 参数</comment>");
            $output->writeln("<comment>⚠️ 注意：这是财务敏感数据，清理前请确保已完成必要的财务审计</comment>");
        } else {
            $output->writeln("<info>💾 已释放数据库存储空间，保留财务审计要求的必要数据</info>");
            $this->suggestDatabaseOptimization($output);
        }
    }

    /**
     * 建议数据库优化
     */
    private function suggestDatabaseOptimization(Output $output): void
    {
        $output->writeln("");
        $output->writeln("<comment>💡 建议执行数据库优化以回收存储空间：</comment>");
        $output->writeln("<comment>   OPTIMIZE TABLE cm_kb_robot_revenue_log;</comment>");
    }

    /**
     * 记录清理结果到日志文件
     */
    private function logCleanupResult(int $days, array $results, int $total): void
    {
        $logData = [
            'cleanup_date' => date('Y-m-d H:i:s'),
            'retention_days' => $days,
            'cleanup_results' => $results,
            'total_cleaned' => $total,
            'execution_type' => 'revenue_cleanup'
        ];
        
        $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 记录错误日志
     */
    private function logError(Exception $e): void
    {
        $errorData = [
            'error_date' => date('Y-m-d H:i:s'),
            'error_message' => $e->getMessage(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'execution_type' => 'revenue_cleanup_error'
        ];
        
        $logContent = json_encode($errorData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 初始化日志文件
     */
    private function initLogFile(): void
    {
        $logDir = runtime_path('log');
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFile = $logDir . '/revenue_cleanup.log';
        
        // 检查日志文件大小，如果超过限制则轮转
        $this->rotateLogIfNeeded();
    }

    /**
     * 日志轮转机制
     */
    private function rotateLogIfNeeded(): void
    {
        if (!file_exists($this->logFile)) {
            return;
        }
        
        if (filesize($this->logFile) > $this->maxLogSize) {
            $rotatedFile = $this->logFile . '.' . date('Y-m-d-H-i-s');
            rename($this->logFile, $rotatedFile);
            
            // 只保留最近5个轮转文件
            $this->cleanupOldLogFiles();
        }
    }

    /**
     * 清理旧日志文件
     */
    private function cleanupOldLogFiles(): void
    {
        $logDir = dirname($this->logFile);
        $logBaseName = basename($this->logFile);
        $pattern = $logDir . '/' . $logBaseName . '.*';
        
        $files = glob($pattern);
        if (count($files) > 5) {
            // 按修改时间排序
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的文件
            $filesToDelete = array_slice($files, 0, count($files) - 5);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }

    /**
     * 写入日志
     */
    private function writeLog(string $content): void
    {
        $logEntry = "[" . date('Y-m-d H:i:s') . "] " . $content . PHP_EOL;
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
} 