/*
 * 生成知识库动态图标 GIF (无 external native 依赖)
 * 依赖：gifencoder@2.x
 * 使用：
 *   npm install gifencoder@2 --save-dev
 *   node scripts/generate_kb_icon_gif.js
 */

const fs = require('fs')
const path = require('path')
const GIFEncoder = require('gifencoder')

const SIZE = 64
const FRAME_COUNT = 20
const DELAY = 100

const outputPath = path.resolve(__dirname, '..', 'kb-animated-icon.gif')

const encoder = new GIFEncoder(SIZE, SIZE)
encoder.start()
encoder.setRepeat(0)
encoder.setDelay(DELAY)
encoder.setQuality(10)

/**
 * 填充像素数组 (RGB)
 * @param {Uint8Array} buffer length = SIZE*SIZE*3
 * @param {number} offsetX 翻页 X 偏移
 */
function drawFrame(buffer, offsetX) {
    const leftPageColor = [0x38, 0xB2, 0xAC] // #38B2AC
    const rightPageColor = [0x38, 0xB2, 0xAC]
    const flipPageColor = [0x4F, 0xD1, 0xC5] // #4FD1C5
    const spineColor = [0x2C, 0x7A, 0x7B]

    // 填充透明背景 (白色)
    buffer.fill(0xFF)

    // 辅助函数: 填充矩形
    const fillRect = (x, y, w, h, color) => {
        for (let yy = y; yy < y + h; yy++) {
            for (let xx = x; xx < x + w; xx++) {
                const idx = (yy * SIZE + xx) * 3
                buffer[idx] = color[0]
                buffer[idx + 1] = color[1]
                buffer[idx + 2] = color[2]
            }
        }
    }

    // 左页
    fillRect(8, 12, 22, 40, leftPageColor)
    // 右页
    fillRect(34, 12, 22, 40, rightPageColor)
    // 翻页 (offsetX)
    fillRect(offsetX, 12, 22, 40, flipPageColor)
    // 书脊
    fillRect(30, 10, 4, 44, spineColor)
}

for (let frame = 0; frame < FRAME_COUNT; frame++) {
    const pixels = new Uint8Array(SIZE * SIZE * 3)
    // offset 在 34 ~ 30 之间往复
    const offsetX = Math.round(34 - 4 * Math.sin((frame / (FRAME_COUNT - 1)) * Math.PI))
    drawFrame(pixels, offsetX)
    encoder.addFrame(pixels)
}

encoder.finish()
fs.writeFileSync(outputPath, encoder.out.getData())
console.log('GIF 已生成: ' + outputPath) 