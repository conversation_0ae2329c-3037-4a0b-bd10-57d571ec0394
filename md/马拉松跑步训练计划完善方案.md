# 马拉松跑步训练计划完善方案

## 项目背景
通过分析数据库中的训练计划模型，发现：
- **骑行训练计划**（ID: 28）：设计完整，科学合理，包含多维度训练目标和详细个人信息
- **马拉松跑步训练计划**（ID: 33）：设计不完整，缺少训练目标、频次等关键信息

## 问题分析

### 当前马拉松跑步训练计划的问题
1. **训练目标缺失**：没有长跑、节奏跑、间歇训练等专业选项
2. **个人信息不全**：缺少跑步经验、训练频次、目标时间等关键信息
3. **表单设计简单**：只有基本输入框，没有多选、单选等丰富组件
4. **内容模板错误**：错误地写成了"骑行训练计划"
5. **缺少专业要素**：没有包含配速、心率、恢复等专业概念

### 骑行训练计划的优点
1. **训练目标多样化**：FTP训练、最大摄氧量训练、爬坡训练、冲刺训练、耐力训练
2. **个人信息完整**：体重、历史距离、FTP值、最大摄氧量、训练频次
3. **表单设计科学**：多选、单选、下拉选项、输入框等多种组件
4. **内容详细**：包含训练内容、饮食注意事项、休息时间等全面信息

## 完善方案

### 1. 修正后的Content（提示词模板）
```text
你是一名世界级的马拉松运动教练，请根据以下信息为一名马拉松运动员制定一套科学严谨的详细跑步训练计划，包括但不限于训练内容、饮食注意事项、休息时间、配速分析、心率区间等。

训练目标为：${training_goals}
计划时长为：${plan_duration}
年龄为：${age}岁
身高：${height}cm
体重：${weight}kg
跑步经验：${running_experience}
目前历史单次最长跑程：${max_distance}km
半马PB：${half_marathon_pb}
全马PB：${full_marathon_pb}
训练频次：${training_frequency}
目标完赛时间：${target_time}
静息心率：${resting_heart_rate}bpm
最大心率：${max_heart_rate}bpm
目前周跑量：${weekly_mileage}km
其他要求：${other_requirements}

请制定详细的训练计划，包括：
1. 周训练计划安排
2. 不同阶段的训练重点
3. 配速和心率区间建议
4. 营养补充方案
5. 伤病预防措施
6. 恢复和休息安排
7. 比赛策略建议

内容尽量详尽，并有可操作性。
```

### 2. 完善后的Form（表单配置）
```json
[
  {
    "name": "WidgetCheckbox",
    "title": "多选",
    "id": "training_goals_checkbox",
    "props": {
      "field": "training_goals",
      "title": "训练目标（可多选，推荐使用专业跑步模型）",
      "options": [
        "完成首个全马",
        "提高全马成绩",
        "基础耐力训练",
        "速度训练",
        "节奏跑训练",
        "间歇训练",
        "长距离训练",
        "恢复跑训练",
        "比赛配速训练",
        "心率区间训练"
      ],
      "defaultValue": ["基础耐力训练"],
      "isRequired": true
    }
  },
  {
    "name": "WidgetSelect",
    "title": "下拉选项",
    "id": "plan_duration_select",
    "props": {
      "field": "plan_duration",
      "title": "训练计划时长",
      "options": [
        "12周（3个月）",
        "16周（4个月）",
        "20周（5个月）",
        "24周（6个月）",
        "自定义时长"
      ],
      "defaultValue": "16周（4个月）",
      "isRequired": true
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "age_input",
    "props": {
      "field": "age",
      "title": "年龄（岁）",
      "defaultValue": "",
      "placeholder": "26",
      "maxlength": 200,
      "isRequired": true
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "height_input",
    "props": {
      "field": "height",
      "title": "身高（cm）",
      "defaultValue": "",
      "placeholder": "172",
      "maxlength": 200,
      "isRequired": true
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "weight_input",
    "props": {
      "field": "weight",
      "title": "体重（kg）",
      "defaultValue": "",
      "placeholder": "60",
      "maxlength": 200,
      "isRequired": true
    }
  },
  {
    "name": "WidgetRadio",
    "title": "单选",
    "id": "running_experience_radio",
    "props": {
      "field": "running_experience",
      "title": "跑步经验",
      "options": [
        "新手（0-1年）",
        "进阶（1-3年）",
        "中级（3-5年）",
        "高级（5年以上）",
        "专业运动员"
      ],
      "defaultValue": "进阶（1-3年）",
      "isRequired": true
    }
  },
  {
    "name": "WidgetSelect",
    "title": "下拉选项",
    "id": "max_distance_select",
    "props": {
      "field": "max_distance",
      "title": "历史单次最长跑程",
      "options": [
        "5公里以内",
        "5-10公里",
        "10-15公里",
        "15-21公里",
        "21-30公里",
        "30-42公里",
        "42公里以上"
      ],
      "defaultValue": "15-21公里",
      "isRequired": true
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "half_marathon_pb_input",
    "props": {
      "field": "half_marathon_pb",
      "title": "半马PB（非必填）",
      "defaultValue": "",
      "placeholder": "1:45:30",
      "maxlength": 200,
      "isRequired": false
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "full_marathon_pb_input",
    "props": {
      "field": "full_marathon_pb",
      "title": "全马PB（非必填）",
      "defaultValue": "",
      "placeholder": "3:30:00",
      "maxlength": 200,
      "isRequired": false
    }
  },
  {
    "name": "WidgetRadio",
    "title": "单选",
    "id": "training_frequency_radio",
    "props": {
      "field": "training_frequency",
      "title": "训练频次",
      "options": [
        "每周2-3次",
        "每周4-5次",
        "每周6次",
        "每周7次（每天）",
        "每周8次以上（一天两练）"
      ],
      "defaultValue": "每周4-5次",
      "isRequired": true
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "target_time_input",
    "props": {
      "field": "target_time",
      "title": "目标完赛时间",
      "defaultValue": "",
      "placeholder": "4:00:00",
      "maxlength": 200,
      "isRequired": true
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "resting_heart_rate_input",
    "props": {
      "field": "resting_heart_rate",
      "title": "静息心率（bpm）（非必填）",
      "defaultValue": "",
      "placeholder": "60",
      "maxlength": 200,
      "isRequired": false
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "max_heart_rate_input",
    "props": {
      "field": "max_heart_rate",
      "title": "最大心率（bpm）（非必填）",
      "defaultValue": "",
      "placeholder": "190",
      "maxlength": 200,
      "isRequired": false
    }
  },
  {
    "name": "WidgetInput",
    "title": "单行文本",
    "id": "weekly_mileage_input",
    "props": {
      "field": "weekly_mileage",
      "title": "目前周跑量（km）",
      "defaultValue": "",
      "placeholder": "30",
      "maxlength": 200,
      "isRequired": true
    }
  },
  {
    "name": "WidgetTextarea",
    "title": "多行文本",
    "id": "other_requirements_textarea",
    "props": {
      "field": "other_requirements",
      "title": "其他要求（非必填）",
      "placeholder": "希望重点提升速度，容易膝盖疼痛，需要避免伤病等",
      "rows": 4,
      "defaultValue": "",
      "maxlength": 500,
      "autosize": false,
      "isRequired": false
    }
  }
]
```

## 设计理念对比

### 骑行训练计划的设计理念
1. **多维度目标**：包含技术性（FTP）、生理性（最大摄氧量）、战术性（爬坡、冲刺）训练目标
2. **精确测量**：FTP值、最大摄氧量等精确的生理指标
3. **个性化配置**：根据个人能力和目标灵活调整
4. **科学严谨**：基于运动生理学原理设计

### 马拉松跑步训练计划的设计理念
1. **渐进式目标**：从基础耐力到专项训练的渐进式目标设置
2. **全面评估**：包含经验、历史成绩、心率等多维度评估
3. **实用性强**：针对不同水平跑者的实际需求
4. **伤病预防**：重视恢复和伤病预防

## 训练目标对比分析

### 骑行训练目标
- **FTP训练**：功能性阈值功率训练
- **最大摄氧量训练**：有氧能力提升
- **爬坡训练**：专项技术训练
- **冲刺训练**：无氧能力训练
- **耐力训练**：长距离有氧训练

### 马拉松跑步训练目标
- **完成首个全马**：新手目标
- **提高全马成绩**：进阶目标
- **基础耐力训练**：有氧基础
- **速度训练**：提升最大速度
- **节奏跑训练**：乳酸阈训练
- **间歇训练**：间歇性高强度训练
- **长距离训练**：专项耐力训练
- **恢复跑训练**：主动恢复
- **比赛配速训练**：目标配速适应
- **心率区间训练**：科学化训练

## 表单设计优化

### 1. 组件类型多样化
- **多选框**：训练目标（可同时选择多个）
- **单选框**：训练频次、跑步经验（只能选择一个）
- **下拉选择**：计划时长、最长跑程（预设选项）
- **输入框**：年龄、身高、体重等数值
- **多行文本**：其他要求（详细描述）

### 2. 必填项设计
- **必填**：基础信息（年龄、身高、体重）、训练目标、训练频次
- **选填**：专业指标（心率、PB成绩）、特殊要求

### 3. 默认值设置
- 为每个字段设置合理的默认值
- 降低用户填写门槛
- 提供专业指导建议

## 变量命名规范

### 原有变量问题
- 使用随机字符串：`${aaa}`、`${bbb}`、`${ccc}`
- 缺乏语义化：难以理解变量含义
- 不利于维护：代码可读性差

### 优化后变量命名
- 使用语义化命名：`${training_goals}`、`${plan_duration}`
- 遵循驼峰命名法：`${restingHeartRate}`
- 便于理解和维护

## 实施建议

### 1. 数据库更新
```sql
UPDATE cm_creation_model 
SET 
  content = '你是一名世界级的马拉松运动教练，请根据以下信息为一名马拉松运动员制定一套科学严谨的详细跑步训练计划...',
  form = '[{...}]'
WHERE id = 33;
```

### 2. 测试验证
- 测试各种组件的数据提交
- 验证变量替换功能
- 确保提示词生成正确

### 3. 用户体验优化
- 添加字段说明和示例
- 优化表单布局
- 提供专业术语解释

## 总结

通过参考骑行训练计划的设计理念，马拉松跑步训练计划可以在以下方面得到显著改善：

1. **专业性提升**：增加专业的跑步训练目标和指标
2. **个性化增强**：更全面的个人信息收集
3. **科学性加强**：基于运动科学的训练理念
4. **实用性改善**：更贴近实际训练需求
5. **用户体验优化**：更丰富的交互组件

这样的完善方案既保持了骑行训练计划的科学严谨性，又结合了跑步运动的专业特点，为用户提供更加专业和实用的训练计划生成服务。 