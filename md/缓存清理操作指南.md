# 清除缓存说明

## 问题原因
数据库中的菜单权限配置使用了错误的路径，导致前端调用API时出现404错误。

## 解决步骤

### 1. 执行SQL修正脚本
执行 `fix_role_example_menu.sql` 文件来修正数据库中的菜单配置。

### 2. 清除后端缓存
删除或清空以下目录：
```
server/runtime/cache/
server/runtime/temp/
```

### 3. 清除前端缓存
- 清除浏览器缓存
- 重新登录后台管理系统
- 或者按 Ctrl+F5 强制刷新页面

### 4. 验证修复
1. 重新登录后台管理系统
2. 进入"智能体管理" → "角色示例"
3. 确认页面正常加载，不再出现404错误

## 修正的权限路径
- 旧路径：`robot.roleExample/lists`
- 新路径：`kb.robot/roleExampleLists`

## 如果仍有问题
检查浏览器开发者工具的Network标签，查看具体的API请求URL是否正确。 