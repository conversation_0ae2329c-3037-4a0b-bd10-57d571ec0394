# 智能体广场编辑提示与自动下架功能需求文档

## 📋 项目概述

**项目名称**: 智能体广场编辑提示与自动下架功能  
**需求提出时间**: 2025-08-12  
**需求优先级**: 高  
**预计开发周期**: 5-7个工作日  

### 🎯 功能目标
当用户编辑已在智能体广场上架且审核通过的智能体或其关联知识库时，系统自动检测并提示用户，告知编辑后需要重新提交审核。用户确认后，相关智能体自动从广场下架并变为私有状态，确保广场内容的审核完整性。

## 📊 详细需求分析

### 1. 触发条件

#### 1.1 智能体编辑触发
- **触发范围**: 对智能体进行的任何修改操作
- **包含内容**:
  - 基本配置修改（名称、简介、图标等）
  - AI模型/搜索配置修改
  - 界面配置修改
  - 形象配置修改
  - 工作流配置修改
  - 知识库关联修改
- **状态检查**: 检查该智能体是否在广场中已上架（`verify_status = 1` 且 `is_show = 1`）

#### 1.2 知识库编辑触发
- **触发范围**: 仅对知识库内容的新增和变更操作
- **包含内容**:
  - **文档导入类**:
    - 上传文档文件（PDF、Word、TXT、Markdown等）
    - 批量文档上传
    - 从URL导入文档内容
    - 从其他知识库导入数据
  - **手动录入类**:
    - 手动添加新的知识条目
    - 修改已有知识条目内容
    - 批量导入结构化数据（CSV、Excel等）
    - 通过API接口导入数据
  - **内容编辑类**:
    - 编辑已上传文档的内容
    - 修改文档标题和描述
    - 重新处理/解析已有文档
    - 知识库内容重新训练/向量化（涉及内容变更时）
- **不包含内容**:
  - 知识库基本信息修改（名称、简介、图标等）
  - 文档处理模型配置变更
  - 向量化模型配置变更
  - 知识库文档/文件删除操作
  - 知识库权限设置变更
  - 知识库分享设置变更
- **关联检查**: 检查是否有已上架的智能体关联了此知识库

### 2. 状态判断逻辑

#### 2.1 智能体上架状态定义
```sql
-- 智能体已上架的判断条件
SELECT * FROM cm_kb_robot_square 
WHERE robot_id = ? 
AND verify_status = 1  -- 审核通过
AND is_show = 1        -- 在广场显示
AND delete_time IS NULL -- 未删除
```

#### 2.2 知识库关联智能体检查
```sql
-- 查找使用指定知识库的已上架智能体
SELECT r.*, s.verify_status, s.is_show
FROM cm_kb_robot r
JOIN cm_kb_robot_square s ON r.id = s.robot_id
WHERE FIND_IN_SET(?, r.kb_ids)  -- 知识库ID在kb_ids字段中
AND s.verify_status = 1
AND s.is_show = 1
AND s.delete_time IS NULL
```

#### 2.3 知识库内容操作的具体判断
**触发检查的操作类型**:
- **文档导入操作**:
  - 单个文档上传（PDF、Word、TXT、MD等）
  - 批量文档上传
  - URL内容导入
  - 从其他知识库复制/导入内容
- **手动录入操作**:
  - 手动添加知识条目
  - 编辑已有知识条目内容
  - 批量导入结构化数据（CSV、Excel）
  - API接口数据导入
- **内容编辑操作**:
  - 修改已上传文档内容
  - 重新解析/处理文档
  - 文档标题和描述修改
  - 知识库重新训练/向量化（涉及内容变更）

**不触发检查的操作类型**:
- 文档/条目删除操作
- 知识库配置修改（模型选择、参数调整等）
- 知识库基本信息修改（名称、简介、图标）
- 权限和分享设置变更
- 知识库访问统计查看

### 3. 用户交互设计

#### 3.1 提示对话框设计
**智能体编辑提示**:
```
标题: 智能体编辑确认
内容: 此智能体已在智能体广场上架，编辑后需要重新提交审核。
      确认编辑将自动下架此智能体，是否继续？
按钮: [取消] [确认编辑]
```

**知识库编辑提示**:
```
标题: 知识库编辑确认  
内容: 此知识库被以下已上架的智能体使用：
      • 智能体A
      • 智能体B
      编辑后这些智能体需要重新提交审核，是否继续？
按钮: [取消] [确认编辑]
```

#### 3.2 用户操作流程
1. **用户点击保存** → 系统检查上架状态
2. **检测到已上架** → 显示确认对话框
3. **用户选择**:
   - **点击取消**: 关闭对话框，返回编辑页面，保持用户已编辑的内容
   - **点击确认**: 执行下架操作，然后保存编辑内容

### 4. 下架处理逻辑

#### 4.1 智能体下架操作
```sql
-- 1. 更新广场状态
UPDATE cm_kb_robot_square 
SET is_show = 0,           -- 不在广场显示
    verify_status = 0,     -- 重置为待审核
    update_time = ?
WHERE robot_id = ?;

-- 2. 更新智能体状态  
UPDATE cm_kb_robot 
SET is_public = 0,         -- 变为私有
    update_time = ?
WHERE id = ?;
```

#### 4.2 批量下架处理（知识库编辑场景）
- 查找所有关联的已上架智能体
- 逐个执行下架操作
- 记录下架日志

### 5. 重新上架流程

#### 5.1 提交审核
- 复用现有的智能体分享到广场功能
- 用户在智能体列表中点击"分享到广场"
- 系统自动提交审核申请

#### 5.2 审核对比功能
- **后台审核时显示变更对比**:
  - 上次审核通过的版本
  - 当前提交审核的版本
  - 高亮显示变更内容
- **变更记录存储**:
  - 记录每次编辑的时间戳
  - 存储关键字段的变更历史

## 🛠️ 技术实现方案

### 1. 技术栈版本信息
- **后端**: ThinkPHP 6.0 + PHP 8.0
- **PC端**: Nuxt 3.12.4 + Vue 3.3.4 + Element Plus 2.7.3
- **H5端**: UniApp 3.0 + Vue 3 + uView UI
- **数据库**: MySQL 5.7 + Redis 7.4

### 2. 后端API设计

#### 2.1 新增接口
```php
// 检查智能体上架状态
POST /api/kb.robot/checkSquareStatus
参数: robot_id
返回: {is_online: boolean, square_info: object}

// 检查知识库关联智能体状态
POST /api/kb.know/checkRelatedRobots
参数: know_id
返回: {related_robots: array, has_online: boolean}

// 智能体下架接口
POST /api/kb.robot/offlineFromSquare
参数: robot_id, reason
返回: {success: boolean, message: string}
```

#### 2.2 修改现有接口
- **智能体编辑**: `/api/kb.robot/edit` (KbRobotLogic::edit)
- **知识库基本信息编辑**: `/api/kb.know/edit` (KbKnowLogic::edit)
- **知识库文件导入**: `/api/kb.teach/import` - 添加关联智能体检查
- **知识库条目添加**: `/api/kb.teach/insert` - 添加关联智能体检查
- **知识库条目编辑**: `/api/kb.teach/update` - 添加关联智能体检查

#### 1.3 知识库操作类型识别
```php
// 需要检查的知识库操作类型
const KB_OPERATION_TYPES = [
    // 文档导入类
    'file_upload',          // 单个文件上传
    'batch_upload',         // 批量文件上传
    'url_import',           // URL内容导入
    'kb_import',            // 从其他知识库导入

    // 手动录入类
    'entry_add',            // 手动添加条目
    'entry_edit',           // 编辑条目内容
    'data_import',          // 结构化数据导入（CSV/Excel）
    'api_import',           // API接口数据导入

    // 内容编辑类
    'content_edit',         // 文档内容编辑
    'doc_reprocess',        // 文档重新处理
    'doc_meta_edit',        // 文档标题描述修改
    'retrain'               // 重新训练（涉及内容变更）
];

// 不需要检查的操作类型
const KB_SKIP_OPERATION_TYPES = [
    'file_delete',          // 文件删除
    'entry_delete',         // 条目删除
    'config_edit',          // 配置修改（模型选择等）
    'info_edit',            // 基本信息修改
    'permission_edit',      // 权限修改
    'share_setting',        // 分享设置
    'stats_view'            // 统计查看
];
```

### 2. 前端实现方案

#### 2.1 PC端实现 (Nuxt 3 + Element Plus)
- **智能体编辑页面**: `pc/src/pages/application/robot/_components/app-edit/index.vue`
  - 保存函数: `handelSubmit()` 调用 `putRobot(formData.value)`
- **知识库编辑页面**: `pc/src/pages/application/kb/detail/_components/base-setting.vue`
- **知识库内容编辑**: `pc/src/pages/application/kb/detail/_components/study_com/editPop.vue`
  - 保存函数: `submit()` 调用 `itemDataEdit()` 或 `itemDataImport()`
- **确认对话框**: 使用 `ElMessageBox.confirm()` 实现

#### 2.2 H5端实现 (UniApp + uView)
- **智能体编辑页面**: `uniapp/src/packages/pages/robot_info/component/robot-setting/`
  - 基本配置: `base-config.vue`
  - 保存逻辑: 调用 `editRobot()` API
- **知识库编辑页面**: `uniapp/src/packages/pages/kb_info/`
  - 文件导入: `components/import/` 目录下各种导入组件
  - 手动录入: `components/addPop.vue`
- **确认弹窗**: 使用 `uni.showModal()` 实现

### 3. 数据库设计

#### 3.1 新增字段（可选）
```sql
-- 智能体表添加编辑历史字段
ALTER TABLE cm_kb_robot 
ADD COLUMN last_edit_time INT(10) COMMENT '最后编辑时间',
ADD COLUMN edit_version INT(10) DEFAULT 1 COMMENT '编辑版本号';

-- 广场表添加下架原因字段
ALTER TABLE cm_kb_robot_square 
ADD COLUMN offline_reason VARCHAR(255) COMMENT '下架原因',
ADD COLUMN offline_time INT(10) COMMENT '下架时间';
```

#### 3.2 新增日志表
```sql
-- 智能体编辑日志表
CREATE TABLE cm_kb_robot_edit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    robot_id INT NOT NULL COMMENT '智能体ID',
    user_id INT NOT NULL COMMENT '用户ID', 
    edit_type TINYINT NOT NULL COMMENT '编辑类型：1=智能体编辑，2=知识库编辑触发',
    before_data TEXT COMMENT '编辑前数据',
    after_data TEXT COMMENT '编辑后数据',
    is_auto_offline TINYINT DEFAULT 0 COMMENT '是否自动下架',
    create_time INT NOT NULL COMMENT '创建时间'
);
```

## 📱 平台适配说明

### 1. PC端实现要点
- 使用Element Plus的MessageBox组件实现确认对话框
- 保持现有的表单验证和提交流程
- 在提交前插入状态检查逻辑

### 2. H5端实现要点  
- 使用UniApp的uni.showModal实现确认对话框
- 适配移动端的交互体验
- 保持与PC端一致的业务逻辑

### 3. 管理后台
- 仅针对普通用户实现此功能
- 管理员编辑时不触发此提示
- 后台审核时显示变更对比信息

## 🧪 测试用例设计

### 1. 智能体编辑测试
- 编辑未上架智能体 → 正常保存，无提示
- 编辑已上架智能体 → 显示提示，用户取消 → 返回编辑页面
- 编辑已上架智能体 → 显示提示，用户确认 → 下架并保存

### 2. 知识库编辑测试
**触发检查的操作测试**:

*文档导入类测试*:
- 单个文档上传到无关联智能体的知识库 → 正常保存，无提示
- 单个文档上传到有关联已上架智能体的知识库 → 显示提示，列出关联智能体
- 批量文档上传（有关联已上架智能体）→ 显示提示
- URL内容导入（有关联已上架智能体）→ 显示提示
- 从其他知识库导入内容（有关联已上架智能体）→ 显示提示

*手动录入类测试*:
- 手动添加知识条目（有关联已上架智能体）→ 显示提示
- 修改已有知识条目（有关联已上架智能体）→ 显示提示
- CSV/Excel数据导入（有关联已上架智能体）→ 显示提示
- API接口数据导入（有关联已上架智能体）→ 显示提示

*内容编辑类测试*:
- 修改已有文档内容（有关联已上架智能体）→ 显示提示
- 重新处理文档（有关联已上架智能体）→ 显示提示
- 修改文档标题描述（有关联已上架智能体）→ 显示提示

**不触发检查的操作测试**:
- 删除知识库文档/条目 → 正常删除，无提示
- 修改知识库名称、简介、图标 → 正常保存，无提示
- 更换文档处理模型 → 正常保存，无提示
- 更换向量化模型 → 正常保存，无提示
- 修改知识库权限设置 → 正常保存，无提示
- 修改知识库分享设置 → 正常保存，无提示
- 查看知识库统计信息 → 正常查看，无提示

### 3. 边界情况测试
- 网络异常时的处理
- 并发编辑的处理
- 权限验证

## 📈 预期效果

### 1. 业务价值
- 确保广场内容的审核完整性
- 提升用户对审核流程的认知
- 减少审核工作量和争议

### 2. 用户体验
- 明确的操作提示和确认流程
- 保持编辑内容不丢失
- 简化重新上架流程

### 3. 系统稳定性
- 完善的状态管理机制
- 详细的操作日志记录
- 可靠的数据一致性保证

## 🔄 开发实施计划

### 阶段一：后端API开发（2-3天）
1. **智能体编辑检查逻辑**
   - 修改 `KbRobotLogic::edit()` 方法
   - 添加上架状态检查函数
   - 实现智能体下架接口

2. **知识库编辑检查逻辑**
   - 修改 `KbKnowLogic::edit()` 方法
   - 添加关联智能体查询函数
   - 实现批量下架逻辑

3. **新增API接口**
   - 状态检查接口
   - 下架操作接口
   - 关联查询接口

### 阶段二：前端界面开发（2-3天）
1. **PC端实现**
   - 智能体编辑页面改造
   - 知识库编辑页面改造
   - 确认对话框组件开发

2. **H5端实现**
   - UniApp页面改造
   - 移动端确认弹窗
   - 交互体验优化

### 阶段三：测试验证（1-2天）
1. **功能测试**
   - 各种编辑场景测试
   - 状态检查准确性验证
   - 下架流程完整性测试

2. **兼容性测试**
   - PC端浏览器兼容性
   - H5端设备适配
   - 数据一致性验证

## 🚨 风险评估与应对

### 1. 技术风险
- **数据一致性风险**: 下架操作可能影响其他功能
  - **应对**: 使用数据库事务确保操作原子性
- **性能风险**: 关联查询可能影响响应速度
  - **应对**: 添加数据库索引，优化查询语句

### 2. 业务风险
- **用户体验风险**: 频繁提示可能影响用户操作流畅性
  - **应对**: 优化提示文案，提供"不再提示"选项
- **审核流程风险**: 可能增加审核工作量
  - **应对**: 提供详细的变更对比，简化审核流程

### 3. 兼容性风险
- **版本兼容**: 新功能可能与现有功能冲突
  - **应对**: 充分的回归测试，渐进式发布

## 📋 验收标准

### 1. 功能验收
- ✅ 智能体编辑时正确检测上架状态
- ✅ 知识库编辑时正确检测关联智能体
- ✅ 提示对话框显示准确信息
- ✅ 用户确认后智能体正确下架
- ✅ 下架后可正常编辑保存

### 2. 性能验收
- ✅ 状态检查响应时间 < 500ms
- ✅ 下架操作响应时间 < 1s
- ✅ 不影响现有编辑功能性能

### 3. 兼容性验收
- ✅ PC端主流浏览器正常运行
- ✅ H5端主流设备正常运行
- ✅ 与现有功能无冲突

## 📚 相关文档

### 1. 技术文档
- [智能体广场审核流程说明](./智能体广场审核流程说明.md)
- [数据库表结构设计](./数据库分析与升级管理指南.md)
- [API接口设计规范](./后台管理系统详细开发文档.md)

### 2. 业务文档
- [智能体分成与用户赠送系统](./05_智能体分成与用户赠送系统.md)
- [VIP会员系统与模型管理](./04_VIP会员系统与模型管理.md)

### 3. 测试文档
- 功能测试用例（待创建）
- 性能测试报告（待创建）
- 用户验收测试（待创建）

## 🔧 后续优化方向

### 1. 功能增强
- 支持批量编辑时的智能提示
- 提供编辑历史版本对比
- 实现智能审核建议

### 2. 用户体验优化
- 个性化提示设置
- 编辑内容自动保存
- 一键重新提交审核

### 3. 管理功能完善
- 审核工作台优化
- 变更统计分析
- 自动化审核规则

## 📋 关键信息总结

### 1. 核心API接口
- **智能体编辑**: `POST /api/kb.robot/edit` (现有)
- **知识库基本编辑**: `POST /api/kb.know/edit` (现有)
- **知识库文件导入**: `POST /api/kb.teach/import` (现有)
- **知识库条目操作**: `POST /api/kb.teach/insert|update` (现有)
- **状态检查**: `POST /api/kb.robot/checkSquareStatus` (新增)
- **关联查询**: `POST /api/kb.know/checkRelatedRobots` (新增)
- **智能体下架**: `POST /api/kb.robot/offlineFromSquare` (新增)

### 2. 关键页面组件
**PC端**:
- 智能体编辑: `pc/src/pages/application/robot/_components/app-edit/index.vue`
- 知识库内容编辑: `pc/src/pages/application/kb/detail/_components/study_com/editPop.vue`

**H5端**:
- 智能体编辑: `uniapp/src/packages/pages/robot_info/component/robot-setting/`
- 知识库编辑: `uniapp/src/packages/pages/kb_info/components/`

### 3. 数据库核心表
- `cm_kb_robot_square`: 智能体广场表 (verify_status, is_show)
- `cm_kb_robot`: 智能体表 (is_public, kb_ids)
- `cm_kb_know`: 知识库表

### 4. 实现要点
- 在现有保存逻辑前插入状态检查
- 使用事务确保数据一致性
- PC端用ElMessageBox，H5端用uni.showModal
- 支持批量下架关联智能体

---

**文档版本**: v1.0
**创建时间**: 2025-08-12
**创建者**: AI助手
**审核状态**: 待审核
**预计开发完成时间**: 2025-08-19
