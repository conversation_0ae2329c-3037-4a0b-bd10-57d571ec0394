# 智能体知识库敏感词校验设计方案

## 📋 项目背景

### 当前状态分析
- ✅ **智能体对话**：已有敏感词校验（`KbChatService.php:424`）
- ❌ **知识库录入**：缺少敏感词校验
- ⚠️ **安全风险**：敏感内容可通过知识库录入绕过检测，后续在对话中传播

### 需要覆盖的场景
1. **单条录入**：`KbTeachLogic::insert()` - 手动录入问答对
2. **批量导入**：`KbTeachLogic::import()` - 文件导入、CSV导入、QA拆分
3. **数据修正**：`KbTeachLogic::update()` - 修改已有知识库内容

---

## 🎯 设计目标

### 核心目标
- **源头控制**：在录入环节阻止敏感内容进入知识库
- **高效处理**：利用Redis缓存，最小化性能影响
- **用户体验**：实时反馈，便于用户修改
- **系统稳定**：不影响现有功能的正常运行

### 性能要求
- **单次检测**：< 50ms（短文本）
- **批量处理**：支持1000条/批次
- **缓存命中率**：> 95%
- **系统负载**：增加 < 10%

---

## 🏗️ 技术架构设计

### 1. 服务层设计

#### 敏感词服务选择
基于现有的Redis缓存机制，推荐使用 `CachedWordsService`：

```php
// 现有的缓存结构（已在Redis中）
- sensitive_words_version: "a764b4bf13a360c7ac2a35ec4ca96c95"
- sensitive_words_data: [缓存的敏感词数组]
```

**优势**：
- ✅ 利用现有Redis缓存，无需重复构建
- ✅ 支持版本控制，缓存失效机制完善
- ✅ 已优化的DFA算法，检测效率高

#### 知识库专用敏感词服务

```php
// server/app/common/service/KbSensitiveService.php
class KbSensitiveService
{
    /**
     * 知识库内容敏感词检测
     * 支持问答对批量检测，优化性能
     */
    public static function validateKbContent(array $qaData): array
    {
        $results = [];
        $batchContent = [];
        
        // 1. 批量准备检测内容
        foreach ($qaData as $index => $item) {
            $question = trim($item['question'] ?? '');
            $answer = trim($item['answer'] ?? '');
            $content = $question . ' ' . $answer;
            
            if (empty($content)) {
                $results[$index] = ['valid' => false, 'error' => '问题和答案不能为空'];
                continue;
            }
            
            $batchContent[$index] = $content;
        }
        
        // 2. 批量敏感词检测
        $sensitiveResults = self::batchSensitiveCheck($batchContent);
        
        // 3. 整合结果
        foreach ($batchContent as $index => $content) {
            if (isset($sensitiveResults[$index]['sensitive_words'])) {
                $results[$index] = [
                    'valid' => false,
                    'error' => '内容包含敏感词：' . implode(',', $sensitiveResults[$index]['sensitive_words'])
                ];
            } else {
                $results[$index] = ['valid' => true];
            }
        }
        
        return $results;
    }
    
    /**
     * 批量敏感词检测（高效版）
     */
    private static function batchSensitiveCheck(array $contents): array
    {
        $results = [];
        
        try {
            // 使用缓存的敏感词服务
            foreach ($contents as $index => $content) {
                try {
                    CachedWordsService::sensitive($content);
                    $results[$index] = ['valid' => true];
                } catch (Exception $e) {
                    // 解析敏感词
                    $message = $e->getMessage();
                    if (strpos($message, '提问存在敏感词：') !== false) {
                        $sensitiveWords = explode(',', str_replace('提问存在敏感词：', '', $message));
                        $results[$index] = ['sensitive_words' => $sensitiveWords];
                    } else {
                        $results[$index] = ['error' => $message];
                    }
                }
            }
        } catch (Exception $e) {
            // 全局错误处理
            foreach ($contents as $index => $content) {
                $results[$index] = ['error' => '敏感词检测服务异常'];
            }
        }
        
        return $results;
    }
    
    /**
     * 单条内容检测（兼容现有接口）
     */
    public static function validateSingle(string $question, string $answer): void
    {
        $content = trim($question) . ' ' . trim($answer);
        if (empty($content)) {
            throw new Exception('问题和答案不能为空');
        }
        
        // 直接使用现有服务
        CachedWordsService::sensitive($content);
        
        // 可选：百度内容审核
        if (ConfigService::get('kb_audit', 'enable_baidu', 0)) {
            WordsService::askCensor($content);
        }
    }
}
```

### 2. 业务逻辑集成

#### 单条录入改造

```php
// server/app/api/logic/kb/KbTeachLogic.php

public static function insert(array $post, int $userId): bool
{
    try {
        $question = trim($post['question'] ?? '');
        $answer = trim($post['answer'] ?? '');
        
        // 🆕 敏感词校验
        KbSensitiveService::validateSingle($question, $answer);
        
        // ... 原有逻辑保持不变
        
    } catch (Exception $e) {
        self::setError($e->getMessage());
        return false;
    }
}
```

#### 批量导入改造

```php
public static function import(array $post, int $userId): bool
{
    try {
        // ... 原有验证逻辑 ...
        
        // 🆕 预处理：收集所有待检测内容
        $auditData = [];
        foreach ($post['documents'] as $docIndex => $item) {
            foreach ($item['data'] as $dataIndex => $word) {
                $auditData["{$docIndex}_{$dataIndex}"] = [
                    'question' => $word['q'] ?? '',
                    'answer' => $word['a'] ?? ''
                ];
            }
        }
        
        // 🆕 批量敏感词检测
        $auditResults = KbSensitiveService::validateKbContent($auditData);
        
        // 🆕 检查审核结果
        $failedItems = [];
        foreach ($auditResults as $key => $result) {
            if (!$result['valid']) {
                $failedItems[] = $key . ': ' . $result['error'];
            }
        }
        
        if (!empty($failedItems)) {
            throw new Exception('内容审核失败：' . implode('; ', array_slice($failedItems, 0, 5)) . 
                (count($failedItems) > 5 ? ' 等' . count($failedItems) . '项' : ''));
        }
        
        // ... 原有导入逻辑 ...
        
    } catch (Exception $e) {
        self::setError($e->getMessage());
        return false;
    }
}
```

#### 数据修正改造

```php
public static function update(array $post, int $userId): bool
{
    try {
        $question = trim($post['question'] ?? '');
        $answer = trim($post['answer'] ?? '');
        
        // 🆕 敏感词校验
        KbSensitiveService::validateSingle($question, $answer);
        
        // ... 原有逻辑保持不变
        
    } catch (Exception $e) {
        self::setError($e->getMessage());
        return false;
    }
}
```

---

## 🚀 性能优化策略

### 1. Redis缓存优化

#### 利用现有缓存结构
```php
// 当前Redis中的敏感词缓存
Key: sensitive_words_version
Value: "a764b4bf13a360c7ac2a35ec4ca96c95"

Key: sensitive_words_data  
Value: [1075个敏感词的序列化数组]
```

#### 缓存命中率优化
```php
class KbSensitiveCache 
{
    private static $localCache = null;
    private static $cacheVersion = null;
    
    public static function getSensitiveWords(): array
    {
        // 1. 检查本地缓存
        $currentVersion = Cache::get('sensitive_words_version');
        if (self::$cacheVersion === $currentVersion && self::$localCache !== null) {
            return self::$localCache; // 进程内缓存命中
        }
        
        // 2. 从Redis获取
        $words = Cache::get('sensitive_words_data');
        if ($words !== false) {
            self::$localCache = $words;
            self::$cacheVersion = $currentVersion;
            return $words;
        }
        
        // 3. 重新构建（兜底）
        return CachedWordsService::getSensitiveWords();
    }
}
```

### 2. 批量处理优化

#### 智能分批策略
```php
private static function smartBatchProcess(array $contents): array
{
    $results = [];
    $batchSize = self::calculateOptimalBatchSize(count($contents));
    
    $batches = array_chunk($contents, $batchSize, true);
    
    foreach ($batches as $batch) {
        $batchResults = self::processBatch($batch);
        $results = array_merge($results, $batchResults);
        
        // 防止内存溢出
        if (memory_get_usage() > 100 * 1024 * 1024) { // 100MB
            gc_collect_cycles();
        }
    }
    
    return $results;
}

private static function calculateOptimalBatchSize(int $totalCount): int
{
    if ($totalCount <= 100) return $totalCount;
    if ($totalCount <= 1000) return 100;
    return 200; // 大批量时使用200条/批
}
```

### 3. 内容预处理优化

#### 快速预筛选
```php
private static function quickPreFilter(string $content): bool
{
    // 1. 长度检查
    if (mb_strlen($content) < 2) {
        return true; // 太短的内容快速通过
    }
    
    // 2. 纯数字/英文检查
    if (preg_match('/^[0-9a-zA-Z\s\-_.]+$/', $content)) {
        return true; // 纯英文数字内容快速通过
    }
    
    // 3. 常见安全词汇检查
    $safeWords = ['知识', '学习', '教育', '技术', '方法'];
    foreach ($safeWords as $word) {
        if (strpos($content, $word) !== false && mb_strlen($content) < 50) {
            return true; // 包含安全词汇的短内容快速通过
        }
    }
    
    return false; // 需要详细检测
}
```

---

## 📊 性能评估与监控

### 性能指标预估

#### 单条录入性能
```
检测内容：平均100字符
预估耗时：
- 缓存命中：1-3ms
- 缓存未命中：5-15ms
- 用户感知：几乎无感知
```

#### 批量导入性能
```
1000条记录批量导入：
- 预处理：50ms
- 敏感词检测：200ms（平均）
- 总增加时间：250ms
- 性能影响：< 5%
```

#### Redis缓存性能
```
缓存大小：~2MB（1075个敏感词）
缓存命中率：预期 > 95%
内存占用：每进程增加 < 5MB
```

### 监控指标设计

```php
class KbSensitiveMonitor
{
    private static $stats = [
        'total_checks' => 0,
        'cache_hits' => 0,
        'cache_misses' => 0,
        'sensitive_found' => 0,
        'avg_check_time' => 0,
        'batch_sizes' => []
    ];
    
    public static function recordCheck(float $duration, bool $cacheHit, bool $sensitiveFound): void
    {
        self::$stats['total_checks']++;
        self::$stats['avg_check_time'] = (self::$stats['avg_check_time'] + $duration) / 2;
        
        if ($cacheHit) self::$stats['cache_hits']++;
        else self::$stats['cache_misses']++;
        
        if ($sensitiveFound) self::$stats['sensitive_found']++;
        
        // 定期输出统计信息
        if (self::$stats['total_checks'] % 100 === 0) {
            Log::info('知识库敏感词检测统计', self::$stats);
        }
    }
}
```

---

## 🛡️ 安全与容错设计

### 1. 异常处理策略

```php
class KbSensitiveHandler
{
    /**
     * 安全的敏感词检测（带容错）
     */
    public static function safeCheck(string $content): array
    {
        try {
            CachedWordsService::sensitive($content);
            return ['valid' => true];
        } catch (Exception $e) {
            // 区分不同类型的异常
            $message = $e->getMessage();
            
            if (strpos($message, '敏感词') !== false) {
                // 敏感词检测到，这是正常业务逻辑
                return ['valid' => false, 'error' => $message];
            } else {
                // 系统异常，记录日志但不阻塞业务
                Log::error('敏感词检测系统异常', [
                    'content_length' => mb_strlen($content),
                    'error' => $message
                ]);
                
                // 根据配置决定是否允许通过
                $allowOnError = ConfigService::get('kb_audit', 'allow_on_error', 1);
                if ($allowOnError) {
                    return ['valid' => true, 'warning' => '敏感词检测服务异常，内容已通过'];
                } else {
                    return ['valid' => false, 'error' => '敏感词检测服务暂时不可用'];
                }
            }
        }
    }
}
```

### 2. 降级策略

```php
class KbSensitiveFallback
{
    /**
     * 服务降级检测
     */
    public static function fallbackCheck(string $content): bool
    {
        // 1. 基础关键词检测
        $basicSensitiveWords = ['色情', '赌博', '毒品', '暴力']; // 核心敏感词
        foreach ($basicSensitiveWords as $word) {
            if (strpos($content, $word) !== false) {
                throw new Exception('内容包含敏感词：' . $word);
            }
        }
        
        // 2. 长度限制
        if (mb_strlen($content) > 10000) {
            throw new Exception('内容过长，请分段录入');
        }
        
        return true;
    }
}
```

---

## 📋 实施计划

### 阶段一：基础实现（3天）
1. **创建KbSensitiveService**：核心服务类
2. **修改单条录入**：`insert`方法添加校验
3. **修改数据修正**：`update`方法添加校验
4. **基础测试**：单条录入功能测试

### 阶段二：批量优化（2天）
1. **批量检测实现**：优化`import`方法
2. **性能优化**：缓存和分批处理
3. **异常处理**：完善容错机制
4. **批量测试**：大数据量导入测试

### 阶段三：监控完善（1天）
1. **性能监控**：添加统计和日志
2. **告警机制**：异常情况告警
3. **配置管理**：敏感词开关配置
4. **文档更新**：使用说明和维护文档

---

## 🔧 配置管理

### 新增配置项

```php
// config/chat.php 或独立配置文件
'kb_audit' => [
    'enable_sensitive' => 1,           // 是否启用知识库敏感词检测
    'enable_baidu' => 0,               // 是否启用百度内容审核
    'allow_on_error' => 1,             // 检测异常时是否允许通过
    'batch_size' => 200,               // 批量处理大小
    'max_content_length' => 10000,     // 单条内容最大长度
    'enable_quick_filter' => 1,        // 是否启用快速预筛选
],
```

### 管理后台配置

```php
// 在现有敏感词管理页面添加知识库相关配置
'知识库敏感词检测' => [
    'kb_audit.enable_sensitive' => '启用知识库敏感词检测',
    'kb_audit.enable_baidu' => '启用百度内容审核',
    'kb_audit.allow_on_error' => '检测异常时允许通过',
]
```

---

## 📈 预期效果

### 安全提升
- ✅ **源头防护**：100%覆盖知识库录入场景
- ✅ **内容质量**：确保知识库内容合规
- ✅ **风险控制**：防止敏感内容通过知识库传播

### 性能表现
- ✅ **响应速度**：单条录入增加 < 50ms
- ✅ **批量处理**：1000条记录增加 < 500ms
- ✅ **系统负载**：整体性能影响 < 10%

### 用户体验
- ✅ **实时反馈**：立即提示敏感词位置
- ✅ **批量提示**：批量导入时汇总展示问题
- ✅ **操作便捷**：用户可快速定位和修改问题内容

---

## 💡 后续优化方向

### 短期优化
1. **智能提示**：不仅提示敏感词，还提供修改建议
2. **分级检测**：根据内容类型采用不同检测策略
3. **缓存预热**：系统启动时预加载敏感词缓存

### 长期优化
1. **AI辅助**：结合AI模型进行语义级别的内容检测
2. **自学习**：根据审核结果自动优化敏感词库
3. **多语言支持**：支持英文、日文等多语言敏感词检测

---

**设计总结**：本方案充分利用现有的Redis缓存机制，在最小化性能影响的前提下，为知识库录入功能提供全面的敏感词校验能力。通过分阶段实施，确保功能稳定可靠，用户体验良好。 