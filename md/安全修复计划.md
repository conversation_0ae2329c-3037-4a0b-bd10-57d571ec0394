# 定时任务安全修复计划

## 📋 修复计划概述

**修复时间**: 2025-08-05 10:30开始  
**修复范围**: 3个定时任务文件  
**修复优先级**: 高风险 → 业务风险 → 功能完善  

## 🎯 修复目标

### 立即修复（高风险问题）
1. **SQL注入漏洞修复**
   - 替换动态表名拼接为白名单验证
   - 使用ThinkPHP查询构造器替代原生SQL
   - 添加参数绑定防止注入

2. **权限控制实现**
   - 添加用户身份验证检查
   - 实现操作权限验证
   - 添加管理员权限要求

3. **文件操作安全加固**
   - 添加文件路径白名单验证
   - 限制文件操作范围
   - 防止路径遍历攻击

### 紧急修复（业务风险问题）
4. **数据删除策略改进**
   - ChatRecordCleanup: 硬删除 → 软删除
   - LogsCleanup: 硬删除 → 软删除
   - 保留数据恢复能力

5. **财务数据合规处理**
   - RevenueCleanup: 删除 → 归档
   - 满足财务审计要求
   - 保留历史数据可追溯性

6. **备份机制完善**
   - 为所有任务添加数据备份功能
   - 支持数据导出和恢复
   - 建立备份验证机制

### 优化改进（功能完善）
7. **输入参数验证强化**
   - 添加参数类型检查
   - 实现参数范围验证
   - 完善错误提示信息

8. **批处理机制优化**
   - 实现大数据量分批处理
   - 添加进度显示
   - 优化内存使用

9. **错误处理完善**
   - 统一异常处理机制
   - 完善日志记录
   - 添加操作审计日志

## 📁 文件修复顺序

### 第一阶段：ChatRecordCleanup.php
- 备份文件: `backup/ChatRecordCleanup_security_fix_20250805_103000.php`
- 修复内容: SQL注入 + 权限控制 + 文件安全 + 软删除 + 参数验证
- 测试脚本: `test_chatrecord_security_fix.php`

### 第二阶段：RevenueCleanup.php  
- 备份文件: `backup/RevenueCleanup_security_fix_20250805_104000.php`
- 修复内容: SQL注入 + 权限控制 + 归档机制 + 合规处理 + 参数验证
- 测试脚本: `test_revenue_security_fix.php`

### 第三阶段：LogsCleanup.php
- 备份文件: `backup/LogsCleanup_security_fix_20250805_105000.php`
- 修复内容: SQL注入 + 权限控制 + 文件安全 + 软删除 + 导出优化
- 测试脚本: `test_logs_security_fix.php`

## 🔒 安全修复标准

### SQL注入防护
```php
// 修复前（危险）
$sql = "SELECT * FROM `{$tableName}` WHERE create_time < {$timestamp}";

// 修复后（安全）
$allowedTables = ['cm_chat_record', 'cm_kb_robot_record'];
if (!in_array($tableName, $allowedTables)) {
    throw new SecurityException('Invalid table name');
}
$result = Db::table($tableName)->where('create_time', '<', $timestamp)->select();
```

### 权限控制标准
```php
// 添加权限检查
private function checkPermissions(): void
{
    if (!function_exists('auth') || !auth()->check()) {
        throw new AuthException('Authentication required');
    }
    
    if (!auth()->user()->hasPermission('data_cleanup')) {
        throw new AuthException('Insufficient permissions');
    }
}
```

### 文件操作安全标准
```php
// 文件路径验证
private function validateFilePath(string $filePath): string
{
    $safePaths = [
        runtime_path('log'),
        runtime_path('export'),
        runtime_path('backup')
    ];
    
    $realPath = realpath($filePath);
    foreach ($safePaths as $safePath) {
        if (strpos($realPath, realpath($safePath)) === 0) {
            return $realPath;
        }
    }
    
    throw new SecurityException('Invalid file path');
}
```

### 软删除标准
```php
// 软删除实现
private function softDelete(string $tableName, array $conditions): int
{
    return Db::table($tableName)
        ->where($conditions)
        ->where('delete_time', 'null')
        ->update(['delete_time' => time()]);
}
```

## 📊 验证标准

### 安全测试
- [ ] SQL注入测试通过
- [ ] 权限绕过测试通过
- [ ] 文件操作安全测试通过
- [ ] 路径遍历测试通过

### 功能测试
- [ ] 基本功能正常工作
- [ ] 参数验证正确
- [ ] 错误处理完善
- [ ] 性能无明显下降

### 合规测试
- [ ] 财务数据归档正确
- [ ] 审计日志完整
- [ ] 数据恢复能力验证
- [ ] 备份机制有效

## 🚀 修复执行计划

1. **准备阶段** (10分钟)
   - 创建备份目录
   - 准备修复工具脚本
   - 设置测试环境

2. **修复阶段** (每个文件30-45分钟)
   - 创建文件备份
   - 实施安全修复
   - 功能测试验证
   - 安全测试验证

3. **验证阶段** (15分钟)
   - 综合功能测试
   - 安全漏洞扫描
   - 性能影响评估
   - 修复效果确认

4. **文档阶段** (10分钟)
   - 更新修复记录
   - 生成修复报告
   - 更新README文档

## 📝 修复记录模板

每个文件修复完成后记录：
- 修复时间
- 修复内容
- 测试结果
- 遗留问题
- 后续建议

---

**修复计划创建时间**: 2025-08-05 10:30  
**预计完成时间**: 2025-08-05 12:30  
**修复负责人**: AI Assistant  
**修复状态**: 🟡 **准备中**
