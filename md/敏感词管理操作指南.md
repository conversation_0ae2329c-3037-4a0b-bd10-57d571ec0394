# 敏感词库管理指南

## 📋 概述

本指南介绍如何安全地管理和更新系统的敏感词库，包括解密、编辑和重新加密的完整流程。

## 🔧 工具说明

### 1. 解密工具
- **文件**: `decrypt_sensitive_words.php`
- **功能**: 将加密的敏感词库解密为可编辑的明文文件
- **输出**: `sensitive_words_decrypted.txt`

### 2. 加密工具
- **文件**: `encrypt_sensitive_words.php` (完整版)
- **文件**: `quick_encrypt_sensitive.php` (快速版)
- **功能**: 将明文敏感词文件重新加密为系统可用格式

## 📝 操作流程

### 步骤1：解密现有敏感词库

```bash
# 解密敏感词库
php decrypt_sensitive_words.php
```

**输出文件**: `sensitive_words_decrypted.txt`

### 步骤2：编辑敏感词库

使用任何文本编辑器编辑 `sensitive_words_decrypted.txt` 文件：

```bash
# 使用vim编辑
vim sensitive_words_decrypted.txt

# 或使用nano编辑
nano sensitive_words_decrypted.txt
```

**编辑规则**：
- 每行一个敏感词
- 不要有空行
- 建议使用UTF-8编码
- 避免特殊符号和空格

**示例格式**：
```
敏感词1
敏感词2
敏感词3
```

### 步骤3：重新加密敏感词库

#### 方法一：快速加密（推荐）
```bash
# 使用原有密钥快速加密
php quick_encrypt_sensitive.php
```

#### 方法二：完整加密
```bash
# 完整的加密流程，支持更多选项
php encrypt_sensitive_words.php
```

## 🔒 安全特性

### 自动备份
- 加密前自动备份原有文件
- 备份文件带时间戳，便于恢复
- 备份目录：`backup/sensitive_words/`

### 数据验证
- 自动验证敏感词格式
- 检测可能有问题的词汇
- 加密后自动验证解密结果

### 密钥管理
- 支持使用原有密钥（保持兼容性）
- 支持生成新密钥（提高安全性）
- 密钥和数据分离存储

## 📊 文件结构

```
敏感词库相关文件：
├── server/extend/
│   ├── sensitive_key.bin      # 密钥文件（32字节密钥+16字节IV）
│   └── sensitive_data.bin     # 加密数据文件
├── sensitive_words_decrypted.txt  # 解密后的明文文件
├── decrypt_sensitive_words.php    # 解密工具
├── encrypt_sensitive_words.php    # 完整加密工具
├── quick_encrypt_sensitive.php    # 快速加密工具
└── backup/sensitive_words/        # 备份目录
    ├── sensitive_key_YYYY-MM-DD_HH-mm-ss.bin
    └── sensitive_data_YYYY-MM-DD_HH-mm-ss.bin
```

## ⚠️ 注意事项

### 安全注意事项
1. **权限控制**: 确保敏感词文件只有必要的用户可以访问
2. **备份重要**: 操作前务必备份原有文件
3. **测试验证**: 更新后要测试敏感词检测功能
4. **清理临时文件**: 操作完成后删除明文敏感词文件

### 操作注意事项
1. **编码格式**: 使用UTF-8编码保存文件
2. **格式规范**: 每行一个敏感词，避免空行
3. **字符限制**: 避免使用特殊符号和控制字符
4. **数量控制**: 建议敏感词数量控制在合理范围内

## 🧪 测试验证

### 验证加密结果
```bash
# 重新解密验证
php decrypt_sensitive_words.php

# 检查解密后的内容是否正确
diff sensitive_words_decrypted.txt sensitive_words_decrypted.txt.new
```

### 测试系统功能
1. 在AI问答中输入已知敏感词
2. 检查是否正确拦截
3. 查看系统日志确认检测正常

## 🔄 常见操作

### 添加新敏感词
1. 解密现有敏感词库
2. 在文件末尾添加新敏感词
3. 重新加密敏感词库
4. 测试验证功能

### 删除敏感词
1. 解密现有敏感词库
2. 删除不需要的敏感词行
3. 重新加密敏感词库
4. 测试验证功能

### 批量更新
1. 准备新的敏感词列表文件
2. 替换 `sensitive_words_decrypted.txt` 内容
3. 使用快速加密工具重新加密
4. 验证更新结果

## 🚨 故障恢复

### 如果加密失败
1. 检查文件权限和路径
2. 确认明文文件格式正确
3. 从备份目录恢复原文件

### 如果系统检测异常
1. 立即从备份恢复原文件
2. 检查新敏感词是否有格式问题
3. 重新编辑并加密

## 📞 技术支持

如果在操作过程中遇到问题：
1. 检查错误信息和日志
2. 确认文件权限和路径
3. 验证敏感词格式是否正确
4. 必要时从备份恢复并重新操作

---

**重要提醒**: 敏感词库是系统安全的重要组成部分，请谨慎操作并做好备份！ 