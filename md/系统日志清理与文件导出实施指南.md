# 系统日志清理与文件导出功能实施指南

## 📋 功能概述

本系统为后台管理系统提供了智能化的日志清理功能，支持：

- **半年数据保留**：默认保留半年内的操作记录，清理更早的数据
- **多格式文件导出**：支持JSON、CSV、Excel三种格式导出
- **压缩存储**：自动压缩导出文件，节省存储空间
- **多日志类型支持**：操作日志、账户流水、短信日志、邮件日志
- **批量处理**：智能批处理，避免数据库压力
- **安全预览**：支持干运行模式，预览清理结果

## 🎯 核心特性

### 与智能体分成记录清理的区别

| 功能对比 | 系统日志清理 | 智能体分成记录清理 |
|---------|------------|------------------|
| **主要目标** | 管理操作记录 | 财务记录管理 |
| **保留期限** | 半年（180天） | 一年（365天） |
| **清理方式** | 硬删除+文件导出 | 选择性清理/归档 |
| **导出格式** | JSON/CSV/Excel | JSON/归档表 |
| **清理频率** | 每周 | 每月 |
| **数据敏感性** | 中等 | 高（财务相关） |
| **恢复难度** | 通过导出文件 | 通过归档表 |

### 支持的日志类型

1. **系统操作日志** (`cm_operation_log`)
   - 管理员操作记录
   - 访问链接和参数
   - IP地址和时间戳

2. **用户账户流水** (`cm_user_account_log`)
   - 用户余额变动记录
   - 充值和消费明细
   - 管理员操作记录

3. **短信日志** (`cm_sms_log`)
   - 短信发送记录
   - 发送状态和结果

4. **邮件日志** (`cm_email_log`)
   - 邮件发送记录
   - 发送状态和内容

## 🚀 部署步骤

### 1. 文件部署

#### 1.1 复制清理命令文件
```bash
# 将清理命令文件复制到正确位置
cp system_log_cleanup_command.php server/app/common/command/SystemLogCleanup.php
```

#### 1.2 注册命令
在 `server/config/console.php` 中添加命令注册：

```php
<?php
return [
    'commands' => [
        // ... 现有命令 ...
        'log:cleanup' => \app\common\command\SystemLogCleanup::class,
    ]
];
```

### 2. 数据库配置

#### 2.1 执行定时任务配置
```bash
# 导入定时任务配置
mysql -u用户名 -p数据库名 < system_log_cleanup_crontab.sql
```

#### 2.2 验证配置
```sql
-- 查看已配置的定时任务
SELECT name, target, parameter, rule, status 
FROM cm_crontab 
WHERE target = 'log:cleanup';
```

### 3. 目录权限设置

```bash
# 创建导出目录
mkdir -p server/runtime/exports/
chmod 755 server/runtime/exports/

# 确保日志目录权限
chmod 755 server/runtime/log/
```

## 🧪 测试和验证

### 1. 环境测试
```bash
# 运行完整测试
php test_system_log_cleanup.php
```

### 2. 功能预览测试
```bash
# 进入项目目录
cd server/

# 预览清理（安全模式）
php think log:cleanup --dry-run

# 预览半年前数据
php think log:cleanup --dry-run --days=180

# 预览所有日志类型
php think log:cleanup --dry-run --log-types=operation,account,sms,email
```

### 3. 小批量测试
```bash
# 清理少量数据进行测试
php think log:cleanup --days=365 --batch-size=100 --log-types=operation
```

## ⚙️ 配置选项详解

### 基础参数

| 参数 | 默认值 | 说明 | 示例 |
|------|--------|------|------|
| `--days` | 180 | 保留天数 | `--days=90` |
| `--batch-size` | 1000 | 批处理大小 | `--batch-size=500` |
| `--dry-run` | false | 预览模式 | `--dry-run` |

### 导出参数

| 参数 | 默认值 | 可选值 | 说明 |
|------|--------|--------|------|
| `--export-format` | json | json,csv,excel | 导出格式 |
| `--export-path` | runtime/exports/ | 任意路径 | 导出目录 |
| `--compress` | true | true,false | 是否压缩 |

### 日志类型参数

| 参数值 | 对应表 | 说明 |
|--------|--------|------|
| `operation` | cm_operation_log | 系统操作日志 |
| `account` | cm_user_account_log | 账户流水日志 |
| `sms` | cm_sms_log | 短信日志 |
| `email` | cm_email_log | 邮件日志 |

## 📅 推荐配置方案

### 方案一：基础配置（推荐）
- **执行频率**：每周日凌晨2点
- **保留期限**：半年（180天）
- **清理范围**：仅系统操作日志
- **导出格式**：压缩JSON

```sql
-- 定时任务配置
'0 2 * * 0' 
--days=180 --export-format=json --compress=true --log-types=operation
```

### 方案二：完整配置
- **执行频率**：每月1号凌晨2点
- **保留期限**：半年（180天）
- **清理范围**：所有日志类型
- **导出格式**：压缩Excel

```sql
-- 定时任务配置
'0 2 1 * *'
--days=180 --export-format=excel --compress=true --log-types=operation,account,sms,email
```

### 方案三：频繁清理
- **执行频率**：每天凌晨3点
- **保留期限**：一年（365天）
- **清理范围**：仅系统操作日志
- **导出格式**：压缩CSV

```sql
-- 定时任务配置
'0 3 * * *'
--days=365 --export-format=csv --compress=true --log-types=operation
```

## 🔧 常用命令示例

### 日常使用命令

```bash
# 1. 预览清理（推荐首次执行）
php think log:cleanup --dry-run --days=180

# 2. 标准清理操作
php think log:cleanup --days=180 --export-format=json --compress=true --log-types=operation

# 3. 完整日志清理
php think log:cleanup --days=180 --export-format=excel --compress=true --log-types=operation,account,sms,email

# 4. 紧急清理（一年前数据）
php think log:cleanup --days=365 --export-format=csv --compress=true --log-types=operation,account

# 5. 指定路径导出
php think log:cleanup --days=180 --export-path=/data/log_backups/ --export-format=json --compress=true
```

### 特殊场景命令

```bash
# 清理特定类型日志
php think log:cleanup --days=90 --log-types=sms,email

# 大数据量小批处理
php think log:cleanup --days=180 --batch-size=500 --log-types=operation

# 不压缩导出
php think log:cleanup --days=180 --compress=false --export-format=excel
```

## 📊 监控和维护

### 1. 日志监控

清理操作会在以下位置记录日志：

```bash
# 查看清理日志
tail -f server/runtime/log/system_log_cleanup.log

# 查看详细日志格式
cat server/runtime/log/system_log_cleanup.log | jq '.'
```

### 2. 导出文件管理

```bash
# 查看导出文件
ls -la server/runtime/exports/

# 检查文件大小
du -sh server/runtime/exports/*

# 清理旧的导出文件（可选）
find server/runtime/exports/ -name "*.gz" -mtime +30 -delete
```

### 3. 定时任务监控

```sql
-- 查看任务执行状态
SELECT name, status, error, update_time 
FROM cm_crontab 
WHERE target = 'log:cleanup';

-- 查看最近执行记录
SELECT * FROM cm_crontab_log 
WHERE crontab_id IN (
    SELECT id FROM cm_crontab WHERE target = 'log:cleanup'
) 
ORDER BY create_time DESC LIMIT 10;
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 内存不足错误
**问题**：`Fatal error: Allowed memory size exhausted`

**解决方案**：
```bash
# 减少批处理大小
php think log:cleanup --batch-size=500

# 或增加PHP内存限制
php -d memory_limit=512M think log:cleanup
```

#### 2. 导出目录权限问题
**问题**：`Permission denied`

**解决方案**：
```bash
# 设置正确权限
chmod -R 755 server/runtime/exports/
chown -R www-data:www-data server/runtime/exports/
```

#### 3. 压缩功能失败
**问题**：`zlib extension not found`

**解决方案**：
```bash
# 禁用压缩
php think log:cleanup --compress=false

# 或安装zlib扩展
apt-get install php8.0-zip php8.0-zlib
```

#### 4. Excel导出失败
**问题**：`PhpSpreadsheet not found`

**解决方案**：
```bash
# 使用其他格式
php think log:cleanup --export-format=json

# 或安装依赖
composer require phpoffice/phpspreadsheet
```

### 应急恢复方案

#### 1. 误删数据恢复
```bash
# 从导出文件恢复（需要自定义脚本）
# 1. 解压导出文件
gunzip operation_log_2024-01-15_02-00-00.json.gz

# 2. 编写恢复脚本导入数据
# 3. 验证数据完整性
```

#### 2. 暂停清理任务
```sql
-- 禁用定时任务
UPDATE cm_crontab SET status = 0 WHERE target = 'log:cleanup';
```

## 🔒 安全考虑

### 1. 敏感数据处理
- 自动过滤密码等敏感参数
- 导出文件加密存储（可选配置）
- 限制导出文件访问权限

### 2. 访问控制
```bash
# 设置导出文件权限
chmod 600 server/runtime/exports/*.gz

# 创建专用清理用户（可选）
useradd -r -s /bin/false log-cleanup
```

### 3. 审计追踪
- 所有清理操作记录详细日志
- 导出文件包含完整元数据
- 支持操作回溯和审计

## 📈 性能优化建议

### 1. 数据库优化
```sql
-- 为清理查询添加索引
ALTER TABLE cm_operation_log ADD INDEX idx_create_time (create_time);
ALTER TABLE cm_user_account_log ADD INDEX idx_create_time_delete (create_time, delete_time);
```

### 2. 执行时间优化
- 选择系统负载较低的时间执行
- 避免与备份任务冲突
- 监控执行时间，必要时调整批大小

### 3. 存储优化
- 启用压缩功能
- 定期清理旧的导出文件
- 考虑使用外部存储

## 📋 验收标准

### 功能验收
- [ ] 预览模式正常工作
- [ ] 各种导出格式正常生成
- [ ] 压缩功能正常工作
- [ ] 定时任务正常执行
- [ ] 清理日志正常记录

### 性能验收
- [ ] 大数据量清理无内存溢出
- [ ] 批处理机制工作正常
- [ ] 清理速度满足要求
- [ ] 不影响系统正常运行

### 安全验收
- [ ] 敏感数据正确过滤
- [ ] 导出文件权限正确
- [ ] 清理操作可审计
- [ ] 误操作可恢复

## 📞 技术支持

如遇到问题，请按以下步骤操作：

1. **查看错误日志**
   ```bash
   tail -f server/runtime/log/system_log_cleanup.log
   ```

2. **运行诊断测试**
   ```bash
   php test_system_log_cleanup.php
   ```

3. **检查系统状态**
   ```bash
   php think log:cleanup --dry-run
   ```

4. **提供详细信息**
   - 错误信息截图
   - 系统环境信息
   - 数据量统计
   - 配置参数

---

## 📝 更新日志

**v1.0.0** (2024-01-15)
- 初始版本发布
- 支持多格式导出
- 支持压缩存储
- 支持多日志类型清理
- 完善的测试和监控机制 