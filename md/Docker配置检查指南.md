# Docker配置检查完整指南

## 📋 检查概述

在拷贝docker配置到生产环境前，需要仔细检查以下关键配置项，确保不会覆盖生产环境的重要设置。

## 🔍 需要检查的具体内容

### 1. **docker-compose.yml 核心配置**

#### 🚨 **端口映射检查**
```yaml
# 当前开发环境配置
services:
  nginx:
    ports:
      - "180:80"      # ← 检查：生产环境是否使用相同端口
  
  mysql:
    ports:
      - "13306:3306"  # ← 检查：生产环境MySQL端口
  
  postgres:
    ports:
      - "15432:5432"  # ← 检查：生产环境PostgreSQL端口
  
  php:
    ports:
      - "7314:7314"   # ← 检查：frp内网穿透端口
```

**检查要点：**
- 确认生产环境这些端口是否被占用
- 是否与现有服务冲突
- 防火墙是否需要开放这些端口

#### 🔐 **密码配置检查**
```yaml
mysql:
  environment:
    MYSQL_ROOT_PASSWORD: 123456Abcd  # ← 检查：生产环境是否使用不同密码

postgres:
  environment:
    POSTGRES_PASSWORD: 123456Abcd   # ← 检查：生产环境是否使用不同密码
```

**检查要点：**
- 生产环境可能使用更复杂的密码
- 确认不要覆盖现有的数据库密码

#### 👤 **用户权限检查**
```yaml
php:
  user: "1000:1000"  # ← 检查：生产环境的用户ID是否匹配
```

**检查方法：**
```bash
# 在生产环境检查当前用户ID
id
# 输出示例：uid=1000(user) gid=1000(user)
```

### 2. **Nginx配置检查**

#### 🌐 **域名配置检查**
```nginx
# docker/config/nginx/conf.d/default.conf
server {
    server_name  www.chatmoney.localhost;  # ← 检查：生产环境域名
}
```

**需要修改为生产环境实际域名：**
```nginx
server_name  your-production-domain.com;
```

#### 📁 **SSL证书配置**
生产环境通常需要HTTPS，需要添加SSL配置：
```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
}
```

### 3. **MySQL配置检查**

#### ⚙️ **性能参数检查**
```ini
# docker/config/mysql/mysqld.cnf
max_connections=1000  # ← 检查：生产环境是否需要调整
```

**生产环境可能需要调整的参数：**
- `innodb_buffer_pool_size` - 内存缓冲池大小
- `max_connections` - 最大连接数
- `query_cache_size` - 查询缓存大小

### 4. **PHP配置检查**

#### 💾 **内存和上传限制检查**
```ini
# docker/config/php/php.ini
upload_max_filesize = 200M  # ← 检查：生产环境是否需要调整
post_max_size = 200M        # ← 检查：生产环境是否需要调整
memory_limit = 512M         # ← 检查：生产环境是否需要调整
max_execution_time = 300    # ← 检查：生产环境是否需要调整
```

### 5. **数据存储路径检查**

#### 💾 **数据持久化目录**
```yaml
volumes:
  - ./data/mysql5.7.29/lib:/var/lib/mysql     # ← 检查：生产环境数据路径
  - ./data/redis:/data                        # ← 检查：Redis数据路径
  - ./data/postgres:/var/lib/postgresql/data  # ← 检查：PostgreSQL数据路径
```

**检查要点：**
- 确认生产环境数据目录位置
- 确保有足够的磁盘空间
- 数据目录权限设置正确

### 6. **日志配置检查**

#### 📝 **日志存储路径**
```yaml
volumes:
  - ./log/nginx/logs:/logs           # ← 检查：Nginx日志路径
  - ./log/supervisor:/var/log        # ← 检查：Supervisor日志路径
```

### 7. **容器网络检查**

#### 🔗 **网络配置**
```yaml
networks:
  chatmoney:
    driver: bridge  # ← 检查：是否与生产环境网络冲突
```

## 🛠️ **具体检查步骤**

### 步骤1：环境对比检查
```bash
# 在生产环境执行，获取当前配置信息
docker ps                          # 查看现有容器
docker network ls                  # 查看现有网络
docker volume ls                   # 查看现有数据卷
netstat -tulpn | grep -E ":(80|3306|6379|5432)"  # 检查端口占用
```

### 步骤2：配置文件备份
```bash
# 备份生产环境现有docker配置
cd /生产环境路径
mkdir -p backup/docker_backup_$(date +%Y%m%d_%H%M%S)
cp -r docker/ backup/docker_backup_$(date +%Y%m%d_%H%M%S)/
```

### 步骤3：配置差异对比
```bash
# 对比关键配置文件
diff -u backup/docker_backup_*/docker-compose.yml docker/docker-compose.yml
diff -u backup/docker_backup_*/config/nginx/conf.d/default.conf docker/config/nginx/conf.d/default.conf
```

### 步骤4：选择性合并策略

#### 🟢 **可以安全覆盖的配置：**
- `docker/config/supervisor/supervisor.ini` - 定时任务配置
- `docker/config/php/php.ini` - PHP运行参数
- `docker/scripts/` - 辅助脚本

#### 🟡 **需要谨慎合并的配置：**
- `docker/config/nginx/conf.d/default.conf` - 需要修改域名
- `docker/config/mysql/mysqld.cnf` - 需要检查性能参数

#### 🔴 **不建议覆盖的配置：**
- `docker-compose.yml` - 需要手动合并，保留生产环境的关键设置

## ⚠️ **重要注意事项**

### 1. **数据安全**
```bash
# 在修改配置前，确保数据已备份
docker exec chatmoney-mysql mysqldump -u root -p --all-databases > backup_$(date +%Y%m%d).sql
docker exec chatmoney-redis redis-cli BGSAVE
```

### 2. **服务依赖**
确保容器启动顺序正确：
- MySQL/PostgreSQL → PHP → Nginx

### 3. **环境变量**
检查生产环境的 `.env` 文件配置：
```bash
# 检查数据库连接配置
grep -E "(DATABASE|CACHE|QUEUE)" /生产环境路径/server/.env
```

## 🚀 **推荐的安全部署流程**

### 1. 只更新必要的配置
```bash
# 只更新supervisor配置（定时任务）
cp docker/config/supervisor/supervisor.ini /生产环境/docker/config/supervisor/

# 只更新PHP配置
cp docker/config/php/php.ini /生产环境/docker/config/php/
```

### 2. 手动合并docker-compose.yml
- 保留生产环境的端口、密码、域名配置
- 只更新必要的镜像版本和新增服务

### 3. 逐步重启服务
```bash
# 逐个重启容器，避免全部服务中断
docker restart chatmoney-php
docker restart chatmoney-nginx
# 注意：不要重启数据库容器，除非确实需要
```

## 📝 **检查清单**

- [ ] 端口映射是否冲突
- [ ] 数据库密码是否匹配
- [ ] 域名配置是否正确
- [ ] SSL证书是否配置
- [ ] 数据存储路径是否正确
- [ ] 日志路径是否合适
- [ ] 用户权限是否匹配
- [ ] 网络配置是否冲突
- [ ] 环境变量是否正确
- [ ] 数据已完整备份

## 🎯 **总结**

Docker配置检查的核心原则：
1. **数据安全第一** - 绝不能丢失生产数据
2. **谨慎合并** - 不要盲目覆盖整个docker文件夹
3. **逐步验证** - 每个配置修改后都要验证功能
4. **保留备份** - 确保可以快速回滚

**最安全的做法**：只拷贝`server/`文件夹，docker配置保持生产环境原有设置，或者只更新必要的配置文件。 