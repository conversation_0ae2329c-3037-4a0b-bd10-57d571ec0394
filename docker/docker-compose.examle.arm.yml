version: '3'

networks:
  chatmoney:
    driver: bridge

services:

  nginx:
    container_name: chatmoney-nginx #Nginx容器名
    image: registry.cn-guangzhou.aliyuncs.com/likeadmin/nginx:1.23.1
    restart: always
    depends_on:
      - "php"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ../server:/server
      - ./config/nginx/conf.d:/etc/nginx/conf.d
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./log/nginx/logs:/logs
    networks:
      - chatmoney
    ports:
      - "180:80" #【180】为Nginx挂载主机的端口，一般不会和系统其他进程冲突，建议不要修改，用文本保存。80端口为容器内部端口，不要修改！

  php:
    container_name: chatmoney-php #PHP容器名
    image: registry.cn-guangzhou.aliyuncs.com/likeadmin/php:8.0.30.3-fpm 
    restart: always
    working_dir: /server
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ../server:/server
      - ./config/php/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./config/supervisor/supervisor.ini:/etc/supervisor/supervisord.ini
      - ./config/frps/frps.toml:/usr/local/etc/frps.toml
      - ./log/supervisor:/var/log
    networks:
      - chatmoney
    ports:
      - "7314:7314"
    #user: "1001:1001" # ①【挂载主机ID】

  mysql:
    container_name: chatmoney-mysql #Mysql容器
    image: registry.cn-guangzhou.aliyuncs.com/likeadmin/mysql:5.7.29-amd64 #arm架构
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456Abcd #②【123456Abcd】为Mysql容器root账号的密码，建议修改成复杂密码。
      TZ: Asia/Shanghai
    volumes:
      - ./data/mysql5.7.29/lib:/var/lib/mysql
      - ./config/mysql/mysqld.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - chatmoney
    ports:
      - "13306:3306" #【13306】为Mysql容器挂载主机端口，一般用于远程连接。

  postgres:
    image: registry.cn-guangzhou.aliyuncs.com/likeadmin/pgvector:v0.4.4
    container_name: chatmoney-postgres #postgres容器名
    restart: always
    networks:
      - chatmoney
    environment:
      - TZ=Asia/Shanghai
      - POSTGRES_USER=postgres #③【postgres】为postgres容器的默认账号。
      - POSTGRES_PASSWORD=123456Abcd #④【123456Abcd】为postgres容器默认账号的密码。
      - POSTGRES_DB=postgres #⑤【postgres】为postgres容器的默认数据库名。
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    ports:
      - "15432:5432" #【15432】为postgres容器挂载主机端口，一般用于远程连接。

  redis:
    container_name: chatmoney-redis #Redis容器名
    image: registry.cn-guangzhou.aliyuncs.com/likeadmin/redis:7.4.0
    restart: always
    volumes:
      - ./data/redis:/data
    networks:
      - chatmoney