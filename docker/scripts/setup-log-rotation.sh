#!/bin/bash

# Nginx日志轮转定时任务设置脚本
# 功能：设置crontab定时任务，自动执行日志轮转
# 作者：AI助手
# 创建时间：2025-06-25

SCRIPT_DIR="/www/wwwroot/ai/docker/scripts"
LOG_ROTATE_SCRIPT="${SCRIPT_DIR}/nginx-log-rotate.sh"

echo "🔧 设置Nginx日志轮转定时任务"
echo "================================="

# 检查脚本是否存在
if [[ ! -f "${LOG_ROTATE_SCRIPT}" ]]; then
    echo "❌ 错误: 日志轮转脚本不存在: ${LOG_ROTATE_SCRIPT}"
    exit 1
fi

# 设置脚本执行权限
chmod +x "${LOG_ROTATE_SCRIPT}"
echo "✅ 设置脚本执行权限"

# 备份现有的crontab
echo "📋 备份现有crontab配置"
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "# 新的crontab配置" > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)

# 检查是否已存在相同的定时任务
if crontab -l 2>/dev/null | grep -q "nginx-log-rotate.sh"; then
    echo "⚠️ 警告: 已存在nginx日志轮转定时任务"
    echo "当前的nginx日志轮转任务:"
    crontab -l 2>/dev/null | grep "nginx-log-rotate.sh"
    echo
    read -p "是否要替换现有任务? (y/N): " replace_task
    if [[ "${replace_task,,}" != "y" ]]; then
        echo "❌ 取消设置定时任务"
        exit 0
    fi
    
    # 移除现有任务
    crontab -l 2>/dev/null | grep -v "nginx-log-rotate.sh" | crontab -
    echo "🗑️ 移除现有任务"
fi

# 创建新的crontab配置
{
    # 保留现有的crontab任务
    crontab -l 2>/dev/null | grep -v "nginx-log-rotate.sh" || true
    
    echo "# Nginx日志轮转任务 - 每天凌晨2点执行"
    echo "0 2 * * * ${LOG_ROTATE_SCRIPT} >/dev/null 2>&1"
    echo
    echo "# Nginx日志轮转任务 - 每6小时执行一次（可选，用于高流量站点）"
    echo "# 0 */6 * * * ${LOG_ROTATE_SCRIPT} >/dev/null 2>&1"
    echo
    echo "# Nginx日志轮转任务 - 每小时执行一次（可选，用于超高流量站点）"
    echo "# 0 * * * * ${LOG_ROTATE_SCRIPT} >/dev/null 2>&1"
    
} | crontab -

echo "✅ 设置定时任务完成"
echo
echo "📅 当前crontab配置:"
crontab -l
echo
echo "📝 定时任务说明:"
echo "  - 默认每天凌晨2点执行日志轮转"
echo "  - 只有日志文件大于1MB才会轮转"
echo "  - 归档文件会自动压缩"
echo "  - 保留30天的历史归档"
echo "  - 原有日志文件保持不变，只是清空内容"
echo
echo "🔍 手动测试命令:"
echo "  bash ${LOG_ROTATE_SCRIPT}"
echo
echo "📊 查看轮转日志:"
echo "  tail -f /www/wwwroot/ai/docker/log/nginx/logs/log-rotate.log"
echo
echo "🎯 生产环境部署建议:"
echo "  1. 根据日志增长速度调整轮转频率"
echo "  2. 监控磁盘空间使用情况"
echo "  3. 定期检查归档文件完整性"
echo "  4. 设置日志监控告警"
echo
echo "✅ 日志轮转系统设置完成！" 