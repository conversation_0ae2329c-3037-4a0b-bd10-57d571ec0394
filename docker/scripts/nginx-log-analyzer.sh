#!/bin/bash

# Nginx日志分析脚本
# 功能：分析归档的nginx日志，生成统计报告
# 作者：AI助手
# 创建时间：2025-06-25

ARCHIVE_DIR="/www/wwwroot/ai/docker/log/nginx/logs/archive"
REPORT_DIR="/www/wwwroot/ai/docker/log/nginx/logs/reports"

# 创建报告目录
mkdir -p "${REPORT_DIR}"

# 显示帮助信息
show_help() {
    cat << EOF
Nginx日志分析工具

用法: $0 [选项] [日期]

选项:
  -h, --help          显示帮助信息
  -d, --date DATE     分析指定日期的日志 (格式: YYYYMMDD)
  -r, --range DAYS    分析最近N天的日志
  -t, --top N         显示前N个访问最多的IP/页面 (默认: 10)
  -s, --stats         生成详细统计报告
  -e, --errors        分析错误日志
  -a, --all           分析所有可用的日志

示例:
  $0 -d 20250625              # 分析2025年6月25日的日志
  $0 -r 7                     # 分析最近7天的日志
  $0 -t 20 -s                 # 生成详细统计，显示前20个结果
  $0 -e                       # 分析错误日志
  $0 -a                       # 分析所有日志

EOF
}

# 分析单个日志文件
analyze_access_log() {
    local log_file=$1
    local output_file=$2
    
    if [[ ! -f "${log_file}" ]]; then
        echo "警告: 日志文件不存在: ${log_file}"
        return 1
    fi
    
    echo "分析日志文件: ${log_file}"
    
    # 判断是否为压缩文件
    if [[ "${log_file}" == *.gz ]]; then
        local cat_cmd="zcat"
    else
        local cat_cmd="cat"
    fi
    
    {
        echo "Nginx访问日志分析报告"
        echo "====================="
        echo "日志文件: ${log_file}"
        echo "分析时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "文件大小: $(ls -lh "${log_file}" | awk '{print $5}')"
        echo
        
        # 基本统计
        echo "基本统计:"
        echo "--------"
        local total_requests=$(${cat_cmd} "${log_file}" | wc -l)
        echo "总请求数: ${total_requests}"
        
        if [[ ${total_requests} -eq 0 ]]; then
            echo "日志文件为空"
            return 0
        fi
        
        local unique_ips=$(${cat_cmd} "${log_file}" | awk '{print $1}' | sort | uniq | wc -l)
        echo "唯一IP数: ${unique_ips}"
        
        local success_requests=$(${cat_cmd} "${log_file}" | awk '$9 ~ /^[23]/ {count++} END {print count+0}')
        echo "成功请求: ${success_requests} ($(echo "scale=2; ${success_requests}*100/${total_requests}" | bc -l)%)"
        
        local error_requests=$(${cat_cmd} "${log_file}" | awk '$9 ~ /^[45]/ {count++} END {print count+0}')
        echo "错误请求: ${error_requests} ($(echo "scale=2; ${error_requests}*100/${total_requests}" | bc -l)%)"
        echo
        
        # 状态码统计
        echo "HTTP状态码统计:"
        echo "---------------"
        ${cat_cmd} "${log_file}" | awk '{print $9}' | sort | uniq -c | sort -nr | head -10 | while read count code; do
            printf "  %s: %d (%.2f%%)\n" "${code}" "${count}" "$(echo "scale=2; ${count}*100/${total_requests}" | bc -l)"
        done
        echo
        
        # 访问最多的IP
        echo "访问最多的IP地址 (前${TOP_N}个):"
        echo "--------------------------------"
        ${cat_cmd} "${log_file}" | awk '{print $1}' | sort | uniq -c | sort -nr | head -${TOP_N} | while read count ip; do
            printf "  %s: %d 次\n" "${ip}" "${count}"
        done
        echo
        
        # 访问最多的页面
        echo "访问最多的页面 (前${TOP_N}个):"
        echo "-----------------------------"
        ${cat_cmd} "${log_file}" | awk '{print $7}' | sort | uniq -c | sort -nr | head -${TOP_N} | while read count page; do
            printf "  %s: %d 次\n" "${page}" "${count}"
        done
        echo
        
        # 用户代理统计
        echo "主要用户代理 (前${TOP_N}个):"
        echo "---------------------------"
        ${cat_cmd} "${log_file}" | awk -F'"' '{print $6}' | sort | uniq -c | sort -nr | head -${TOP_N} | while read count agent; do
            printf "  %s: %d 次\n" "${agent}" "${count}"
        done
        echo
        
        # 每小时请求统计
        echo "每小时请求统计:"
        echo "---------------"
        ${cat_cmd} "${log_file}" | awk '{print $4}' | sed 's/\[//g' | awk -F':' '{print $2}' | sort | uniq -c | sort -k2 -n | while read count hour; do
            printf "  %02d:00-%02d:59: %d 次\n" "${hour}" "${hour}" "${count}"
        done
        echo
        
        # 流量统计
        echo "流量统计:"
        echo "---------"
        local total_bytes=$(${cat_cmd} "${log_file}" | awk '{sum+=$10} END {print sum+0}')
        local total_mb=$(echo "scale=2; ${total_bytes}/1024/1024" | bc -l)
        local total_gb=$(echo "scale=2; ${total_mb}/1024" | bc -l)
        echo "总流量: ${total_bytes} bytes (${total_mb} MB / ${total_gb} GB)"
        
        local avg_bytes=$(echo "scale=2; ${total_bytes}/${total_requests}" | bc -l)
        echo "平均请求大小: ${avg_bytes} bytes"
        echo
        
    } > "${output_file}"
    
    echo "分析完成: ${output_file}"
}

# 分析错误日志
analyze_error_log() {
    local log_file=$1
    local output_file=$2
    
    if [[ ! -f "${log_file}" ]]; then
        echo "警告: 错误日志文件不存在: ${log_file}"
        return 1
    fi
    
    echo "分析错误日志: ${log_file}"
    
    # 判断是否为压缩文件
    if [[ "${log_file}" == *.gz ]]; then
        local cat_cmd="zcat"
    else
        local cat_cmd="cat"
    fi
    
    {
        echo "Nginx错误日志分析报告"
        echo "====================="
        echo "日志文件: ${log_file}"
        echo "分析时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo
        
        local total_errors=$(${cat_cmd} "${log_file}" | wc -l)
        echo "总错误数: ${total_errors}"
        
        if [[ ${total_errors} -eq 0 ]]; then
            echo "无错误记录"
            return 0
        fi
        
        echo
        echo "错误级别统计:"
        echo "-------------"
        ${cat_cmd} "${log_file}" | grep -oE '\[(emerg|alert|crit|error|warn|notice|info|debug)\]' | sort | uniq -c | sort -nr
        echo
        
        echo "主要错误类型 (前${TOP_N}个):"
        echo "---------------------------"
        ${cat_cmd} "${log_file}" | awk -F', ' '{print $NF}' | sort | uniq -c | sort -nr | head -${TOP_N}
        echo
        
    } > "${output_file}"
    
    echo "错误日志分析完成: ${output_file}"
}

# 主函数
main() {
    local date_filter=""
    local range_days=""
    local analyze_errors=false
    local analyze_all=false
    local generate_stats=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--date)
                date_filter="$2"
                shift 2
                ;;
            -r|--range)
                range_days="$2"
                shift 2
                ;;
            -t|--top)
                TOP_N="$2"
                shift 2
                ;;
            -s|--stats)
                generate_stats=true
                shift
                ;;
            -e|--errors)
                analyze_errors=true
                shift
                ;;
            -a|--all)
                analyze_all=true
                shift
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置默认值
    TOP_N=${TOP_N:-10}
    
    echo "🔍 Nginx日志分析工具"
    echo "==================="
    
    # 检查归档目录
    if [[ ! -d "${ARCHIVE_DIR}" ]]; then
        echo "❌ 错误: 归档目录不存在: ${ARCHIVE_DIR}"
        exit 1
    fi
    
    # 根据参数执行相应的分析
    if [[ "${analyze_all}" == true ]]; then
        echo "分析所有可用的日志文件..."
        find "${ARCHIVE_DIR}" -name "access_*.log.gz" | sort | while read -r log_file; do
            local basename=$(basename "${log_file}" .log.gz)
            local report_file="${REPORT_DIR}/${basename}_report.txt"
            analyze_access_log "${log_file}" "${report_file}"
        done
        
        if [[ "${analyze_errors}" == true ]]; then
            find "${ARCHIVE_DIR}" -name "error_*.log.gz" | sort | while read -r log_file; do
                local basename=$(basename "${log_file}" .log.gz)
                local report_file="${REPORT_DIR}/${basename}_report.txt"
                analyze_error_log "${log_file}" "${report_file}"
            done
        fi
        
    elif [[ -n "${date_filter}" ]]; then
        echo "分析日期: ${date_filter}"
        local access_log="${ARCHIVE_DIR}/${date_filter}/access_${date_filter}_*.log.gz"
        local error_log="${ARCHIVE_DIR}/${date_filter}/error_${date_filter}_*.log.gz"
        
        for log_file in ${access_log}; do
            if [[ -f "${log_file}" ]]; then
                local basename=$(basename "${log_file}" .log.gz)
                local report_file="${REPORT_DIR}/${basename}_report.txt"
                analyze_access_log "${log_file}" "${report_file}"
            fi
        done
        
        if [[ "${analyze_errors}" == true ]]; then
            for log_file in ${error_log}; do
                if [[ -f "${log_file}" ]]; then
                    local basename=$(basename "${log_file}" .log.gz)
                    local report_file="${REPORT_DIR}/${basename}_report.txt"
                    analyze_error_log "${log_file}" "${report_file}"
                fi
            done
        fi
        
    else
        echo "请指定分析参数，使用 -h 查看帮助"
        exit 1
    fi
    
    echo
    echo "✅ 分析完成！"
    echo "📊 报告保存在: ${REPORT_DIR}/"
    echo "📋 查看报告: ls -la ${REPORT_DIR}/"
}

# 执行主函数
main "$@" 