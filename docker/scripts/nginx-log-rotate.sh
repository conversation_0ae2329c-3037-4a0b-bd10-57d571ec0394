#!/bin/bash

# Nginx日志轮转脚本
# 功能：按日期和时间切分nginx日志，保持原有日志文件不变
# 作者：AI助手
# 创建时间：2025-06-25

# 配置变量
NGINX_CONTAINER_NAME="chatmoney-nginx"
LOG_DIR="/www/wwwroot/ai/docker/log/nginx/logs"
ARCHIVE_DIR="${LOG_DIR}/archive"
ROTATED_DIR="${LOG_DIR}/rotated"
BACKUP_RETENTION_DAYS=30  # 保留30天的备份

# 日志文件配置
ACCESS_LOG="${LOG_DIR}/www.chatmoney.localhost_access_nginx.log"
ERROR_LOG="${LOG_DIR}/www.chatmoney.localhost_error_nginx.log"

# 获取当前时间戳
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
DATE_ONLY=$(date '+%Y%m%d')

# 创建必要的目录
mkdir -p "${ARCHIVE_DIR}/${DATE_ONLY}"
mkdir -p "${ROTATED_DIR}"

# 日志记录函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${LOG_DIR}/log-rotate.log"
}

# 检查nginx容器是否运行
check_nginx_container() {
    if ! docker ps | grep -q "${NGINX_CONTAINER_NAME}"; then
        log_message "错误: Nginx容器 ${NGINX_CONTAINER_NAME} 未运行"
        exit 1
    fi
}

# 轮转单个日志文件
rotate_log_file() {
    local log_file=$1
    local log_type=$2
    
    if [[ ! -f "${log_file}" ]]; then
        log_message "警告: 日志文件 ${log_file} 不存在"
        return 1
    fi
    
    # 检查文件大小，只有大于1MB才轮转
    local file_size=$(stat -c%s "${log_file}" 2>/dev/null || echo 0)
    if [[ ${file_size} -lt 1048576 ]]; then
        log_message "跳过: ${log_file} 文件太小 (${file_size} bytes)"
        return 0
    fi
    
    # 生成归档文件名
    local archive_file="${ARCHIVE_DIR}/${DATE_ONLY}/${log_type}_${TIMESTAMP}.log"
    
    # 复制日志文件到归档目录
    if cp "${log_file}" "${archive_file}"; then
        log_message "成功: 归档 ${log_file} -> ${archive_file}"
        
        # 压缩归档文件
        if gzip "${archive_file}"; then
            log_message "成功: 压缩 ${archive_file}.gz"
        else
            log_message "警告: 压缩失败 ${archive_file}"
        fi
        
        # 清空原日志文件（不删除，保持inode不变）
        if > "${log_file}"; then
            log_message "成功: 清空 ${log_file}"
        else
            log_message "错误: 清空失败 ${log_file}"
            return 1
        fi
        
        # 重新加载nginx配置
        if docker exec "${NGINX_CONTAINER_NAME}" nginx -s reload 2>/dev/null; then
            log_message "成功: 重新加载nginx配置"
        else
            log_message "警告: nginx重新加载失败，尝试发送USR1信号"
            docker exec "${NGINX_CONTAINER_NAME}" kill -USR1 1 2>/dev/null || true
        fi
        
    else
        log_message "错误: 归档失败 ${log_file}"
        return 1
    fi
}

# 清理过期的归档文件
cleanup_old_archives() {
    log_message "开始清理${BACKUP_RETENTION_DAYS}天前的归档文件"
    
    find "${ARCHIVE_DIR}" -name "*.log.gz" -mtime +${BACKUP_RETENTION_DAYS} -type f | while read -r old_file; do
        if rm -f "${old_file}"; then
            log_message "删除: ${old_file}"
        else
            log_message "错误: 删除失败 ${old_file}"
        fi
    done
    
    # 删除空目录
    find "${ARCHIVE_DIR}" -type d -empty -delete 2>/dev/null || true
}

# 生成日志统计报告
generate_log_stats() {
    local stats_file="${LOG_DIR}/log-stats-${DATE_ONLY}.txt"
    
    {
        echo "Nginx日志统计报告 - $(date '+%Y-%m-%d %H:%M:%S')"
        echo "================================================"
        echo
        
        # 当前日志文件大小
        echo "当前日志文件状态:"
        if [[ -f "${ACCESS_LOG}" ]]; then
            echo "  访问日志: $(ls -lh "${ACCESS_LOG}" | awk '{print $5}')"
        fi
        if [[ -f "${ERROR_LOG}" ]]; then
            echo "  错误日志: $(ls -lh "${ERROR_LOG}" | awk '{print $5}')"
        fi
        echo
        
        # 归档文件统计
        echo "归档文件统计:"
        local archive_count=$(find "${ARCHIVE_DIR}" -name "*.log.gz" | wc -l)
        local archive_size=$(du -sh "${ARCHIVE_DIR}" 2>/dev/null | awk '{print $1}' || echo "0")
        echo "  归档文件数: ${archive_count}"
        echo "  归档总大小: ${archive_size}"
        echo
        
        # 今日归档
        if [[ -d "${ARCHIVE_DIR}/${DATE_ONLY}" ]]; then
            echo "今日归档文件:"
            ls -lh "${ARCHIVE_DIR}/${DATE_ONLY}/" 2>/dev/null | grep -v "^total" || echo "  无"
        fi
        
    } > "${stats_file}"
    
    log_message "生成统计报告: ${stats_file}"
}

# 主函数
main() {
    log_message "开始nginx日志轮转 - 时间戳: ${TIMESTAMP}"
    
    # 检查nginx容器
    check_nginx_container
    
    # 轮转访问日志
    if rotate_log_file "${ACCESS_LOG}" "access"; then
        log_message "访问日志轮转完成"
    else
        log_message "访问日志轮转失败"
    fi
    
    # 轮转错误日志
    if rotate_log_file "${ERROR_LOG}" "error"; then
        log_message "错误日志轮转完成"
    else
        log_message "错误日志轮转失败"
    fi
    
    # 清理过期归档
    cleanup_old_archives
    
    # 生成统计报告
    generate_log_stats
    
    log_message "nginx日志轮转完成"
    echo
}

# 执行主函数
main "$@" 