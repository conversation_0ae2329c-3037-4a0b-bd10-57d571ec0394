[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log ; (main log file;default $CWD/supervisord.log)
logfile_maxbytes=50MB        ; (max main logfile bytes b4 rotation; default 50MB)
logfile_backups=10           ; (num of main logfile rotation backups;default 10)
loglevel=info

[program:crontab]
command=/bin/bash -c "while true; do /usr/local/bin/php think crontab; sleep 60; done"
directory=/server
autostart=true
autorestart=true
stderr_logfile=/var/log/crontab.err.log
stdout_logfile=/var/log/crontab.out.log

[program:qa]
command=/usr/local/bin/php think queue:listen --timeout 300  --queue qaJob
directory=/server
autostart=true
autorestart=true
stderr_logfile=/var/log/qa.err.log
stdout_logfile=/var/log/qa.out.log

[program:em]
command=/usr/local/bin/php think queue:listen --timeout 300  --queue emJob
directory=/server
autostart=true
autorestart=true
stderr_logfile=/var/log/em.err.log
stdout_logfile=/var/log/em.out.log

[program:sd]
command=/usr/local/bin/php think queue:listen --timeout 3000 --queue ChatMoneySdJob
directory=/server
autostart=true
autorestart=true
stderr_logfile=/var/log/sd.err.log
stdout_logfile=/var/log/sd.out.log

[program:dalle3]
command=/usr/local/bin/php think queue:listen --timeout 300 --queue ChatMoneyDalleJob
directory=/server
autostart=true
autorestart=true
stderr_logfile=/var/log/dalle3.err.log
stdout_logfile=/var/log/dalle3.out.log

