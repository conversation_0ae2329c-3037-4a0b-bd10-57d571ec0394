
user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    set_real_ip_from 0.0.0.0/0;
    real_ip_header X-Forwarded-For;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    tcp_nopush     on;

    keepalive_timeout  300;
    client_max_body_size 50M;

    #gzip  on;

    server {
        listen       80;
        location / {
               return 404;
         }
    }

    include /etc/nginx/conf.d/*.conf;
}
