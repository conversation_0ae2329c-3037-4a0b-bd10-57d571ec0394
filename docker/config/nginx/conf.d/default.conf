server {
    listen 80 default;
    server_name  www.chatmoney.localhost;
    access_log /logs/www.chatmoney.localhost_access_nginx.log;
    error_log /logs/www.chatmoney.localhost_error_nginx.log;
    client_max_body_size 200M;
    location / {
        root   /server/public;
        index  index.html index.htm index.php;
        if (!-e $request_filename)
        {
            rewrite ^/(.*)$ /index.php?s=$1 last;
            break;
        }
    }
    location ~ /.*\.php/ {
        rewrite ^(.*?/?)(.*\.php)(.*)$ /$2?s=$3 last;
        break;
    }

    location ~ \.php$ {
        fastcgi_pass   chatmoney-php:9000;
        fastcgi_index  index.php;
        fastcgi_param  SCRIPT_FILENAME  /server/public$fastcgi_script_name;
        include        fastcgi_params;
    }
    location = /favicon.ico {
            log_not_found off;
            access_log off;
        }
} 