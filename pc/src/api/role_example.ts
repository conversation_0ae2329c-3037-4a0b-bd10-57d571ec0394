/**
 * @description 获取所有角色示例（按分类分组）
 * @return { Promise }
 */
export function getAllRoleExamples() {
    return $request.get({ url: '/robot.roleExample/getAllRoleExamples' })
}

/**
 * @description 根据分类ID获取角色示例列表
 * @param { Object } params
 * @return { Promise }
 */
export function getRoleExampleListByCategoryId(params: any) {
    return $request.get({ url: '/robot.roleExample/getListByCategoryId', params } as any)
}

/**
 * @description 获取角色示例分类列表
 * @return { Promise }
 */
export function getRoleExampleCategoryList() {
    return $request.get({ url: '/robot.roleExample/getCategoryList' })
} 