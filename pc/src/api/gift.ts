// 赠送相关API接口

// 执行赠送操作
export function executeGift(params: any) {
  return $request.post({ url: '/user_gift/gift', params })
}

// 获取赠送记录
export function getGiftRecords(params: any) {
  return $request.get({ url: '/user_gift/records', params })
}

// 获取赠送配置
export function getGiftConfig() {
  return $request.get({ url: '/user_gift/config' })
}

// 根据用户ID获取用户信息（修复参数传递）
export function getUserById(userSn: string | number) {
  return $request.get({ url: '/user/getUserById', params: { user_sn: userSn } })
}

// 获取最近赠送用户列表
export function getRecentGiftUsers() {
  return $request.get({ url: '/user_gift/recentUsers' })
}

// 获取用户赠送统计
export function getUserGiftStatistics() {
  return $request.get({ url: '/user_gift/statistics' })
}
