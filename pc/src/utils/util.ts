import feedback from './feedback'
import { isEmpty } from './validate'

/**
 * @description 添加单位
 * @param {String | Number} value 值 100
 * @param {String} unit 单位 px em rem
 */
export const addUnit = (value: string | number, unit = 'px') => {
    return !Object.is(Number(value), NaN) ? `${value}${unit}` : value
}

/**
 * @description 树转数组，队列实现广度优先遍历
 * @param {Array} data  数据
 * @param {Object} props `{ children: 'children' }`
 */

export const treeToArray = (data: any[], props = { children: 'children' }) => {
    data = JSON.parse(JSON.stringify(data))
    const { children } = props
    const newData = []
    const queue: any[] = []
    data.forEach((child: any) => queue.push(child))
    while (queue.length) {
        const item: any = queue.shift()
        if (item[children]) {
            item[children].forEach((child: any) => queue.push(child))
            delete item[children]
        }
        newData.push(item)
    }
    return newData
}

/**
 * @description 获取正确的路经
 * @param {String} path  数据
 */
export function getNormalPath(path: string) {
    if (path.length === 0 || !path || path === undefined) {
        return path
    }
    const newPath = path.replace('//', '/')
    const length = newPath.length
    if (newPath[length - 1] === '/') {
        return newPath.slice(0, length - 1)
    }
    return newPath
}

/**
 * @description对象格式化为Query语法
 * @param { Object } params
 * @return {string} Query语法
 */
export function objectToQuery(params: Record<string, any>): string {
    let query = ''
    for (const props of Object.keys(params)) {
        const value = params[props]
        if (!isEmpty(value)) {
            query += `${props}=${value}&`
        }
    }
    return query.slice(0, -1)
}

/**
 * @description 格式化输出价格
 * @param  { string } price 价格
 * @param  { string } take 小数点操作
 * @param  { string } prec 小数位补
 */
export function formatPrice({ price, take = 'all', prec = undefined }: any) {
    let [integer, decimals = ''] = `${price}`.split('.')

    // 小数位补
    if (prec !== undefined) {
        const LEN = decimals.length
        for (let i = prec - LEN; i > 0; --i) decimals += '0'
        decimals = decimals.substr(0, prec)
    }

    switch (take) {
        case 'int':
            return integer
        case 'dec':
            return decimals
        case 'all':
            return `${integer}.${decimals}`
    }
}

/**
 *
 * @export
 * @param {string} str
 * @param {number} frontLen
 * @param {number} endLen
 * @param {string} separator
 * @return {string}
 * 222222 => 2****2
 */
export function replaceSeparatorInStr(
    str: string,
    frontLen: number,
    endLen: number,
    separator: string
) {
    const len = endLen - frontLen
    let separators = ''
    for (let i = 0; i < len; i++) {
        separators += separator
    }
    return str.substring(0, frontLen) + separators + str.substring(endLen)
}

// H5复制方法
export function copy(str: string) {
    const aux = document.createElement('textarea')
    aux.value = str
    document.body.appendChild(aux)
    aux.select()
    document.execCommand('Copy')
    document.body.removeChild(aux)
    feedback.msgSuccess('复制成功！')
}
