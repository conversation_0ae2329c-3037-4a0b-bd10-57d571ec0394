<template>
    <div class="p-[20px] flex bg-body rounded-[12px] flex-col h-full">
        <div
            class="grid grid-cols-2 md:grid-cols-2 gap-4 bg-page py-[20px] rounded-lg flex-none"
        >
            <div class="flex flex-col items-center justify-center">
                <div class="font-medium text-[25px] text-[#0256FF]">
                    {{ Math.floor(userInfo.balance) }}
                </div>
                <div class="mt-2">{{ appStore.getTokenUnit }}数量</div>
            </div>
            <div class="flex flex-col items-center justify-center">
                <div class="font-medium text-[25px] text-[#0256FF]">
                    {{ userInfo.robot_num }}
                </div>
                <div class="mt-2">智能体</div>
            </div>
            <!-- <div class="flex flex-col items-center justify-center">
                <div class=" font-medium text-[20px]">10000.4343</div>
                <div class="mt-2">知识库</div>
            </div> -->
            <!-- <div class="flex flex-col items-center justify-center">
                <div class=" font-medium text-[20px]">{{ userInfo.video_num }}</div>
                <div class="mt-2">形象时长</div>
            </div> -->
        </div>

        <!-- 赠送按钮区域 -->
        <div class="mt-4 flex justify-center">
            <el-button type="primary" size="large" @click="openGiftModal">
                <Icon name="local-icon-gift" size="16px" class="mr-2" />
                赠送他人灵感值
            </el-button>
        </div>

        <!-- 智能体分成收益提醒 -->
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg flex-none">
            <div class="flex items-start space-x-2">
                <div class="text-blue-500 mt-0.5">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="text-sm text-blue-700">
                    <div class="font-medium">智能体分成收益说明</div>
                    <div class="mt-1">
                        • 通过使用智能体广场的智能体可获得分成收益奖励<br>
                        • 分成收益将在每天凌晨自动结算并发放到您的账户<br>
                        • 具体分成比例和规则请查看智能体详情页面
                    </div>
                </div>
            </div>
        </div>

        <div class="flex mt-4 flex-none">
            <div
                class="p-[8px] flex justify-around bg-page rounded-[10px] font-medium"
            >
                <div
                    :class="{ isSelect: params.type == item.type }"
                    class="px-[15px] md:px-[30px] py-[10px] cursor-pointer"
                    v-for="(item, index) in typeList"
                    :key="index"
                    @click="toSelect(item.type)"
                >
                    <span>{{ item.name }}</span>
                </div>
            </div>
        </div>
        <div class="mt-4 flex-1 min-h-0 flex flex-col">
            <div class="flex-1 min-h-0">
                <el-table :data="pager.lists" height="100%">
                    <el-table-column
                        label="订单编号"
                        prop="sn"
                        min-width="150"
                    ></el-table-column>
                    <el-table-column
                        label="变动类型"
                        prop="change_type"
                        min-width="150"
                    >
                        <template #default="{ row }">
                            <div class="flex items-center space-x-2">
                                <span>{{ row.change_type }}</span>
                                <!-- 智能体分成收益标识 -->
                                <span 
                                    v-if="isRobotRevenue(row.change_type)"
                                    class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full"
                                >
                                    分成收益
                                </span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="智能体/应用名"
                        prop="robot_name"
                        min-width="150"
                    >
                        <template #default="{ row }">
                            <div>{{ row.robot_name || '-' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="操作时间"
                        prop="create_time"
                        min-width="150"
                    ></el-table-column>
                    <el-table-column
                        :label="`变动${watchType}`"
                        prop="change_amount"
                        min-width="100"
                    >
                        <template #default="{ row }">
                            <div :class="{ 'text-danger': row.action == 2, 'text-green-600': isRobotRevenue(row.change_type) && row.action == 1 }">
                                <span>{{ row.action == 1 ? '+' : '-' }}</span>
                                <span>{{ Math.floor(parseFloat(row.change_amount)) }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="80">
                        <template #default="{ row }">
                            <el-button
                                @click="showDetail(row.id)"
                                link
                                type="primary"
                            >
                                详情
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex justify-end mt-4">
                <pagination 
                    v-model="pager" 
                    @change="getLists"
                    :hideOnSinglePage="false"
                    :background="true"
                />
            </div>
        </div>

        <detailPop
            :type="watchType"
            v-if="popShow"
            ref="popRef"
            @close="popShow = false"
        ></detailPop>

        <!-- 赠送模态框 -->
        <GiftModal 
            v-model="giftModalVisible" 
            @success="handleGiftSuccess"
        />
    </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import detailPop from './_components/recordDetailPop.vue'
import { useAppStore } from '@/stores/app'
import { accountList } from '@/api/account'
import GiftModal from '@/components/gift/GiftModal.vue'

const { userInfo } = useUserStore()
const appStore = useAppStore()

const params = ref({
    type: 1
})

// 赠送模态框状态
const giftModalVisible = ref(false)

//监控type
const watchType = computed(() => {
    return '数量'
})

const popShow = ref(false)
const popRef = shallowRef()

const typeList = computed(() => [
    {
        name: `${appStore.getTokenUnit}明细`,
        type: 1
    },
    {
        name: '智能体明细',
        type: 2
    }
    // {
    //     name: '知识库明细',
    //     type: 'knowledgeBase'
    // },
    // {
    //     name: '形象明细',
    //     type: 3
    // }
])

// 判断是否是智能体分成收益
const isRobotRevenue = (changeType: string) => {
    return changeType && changeType.includes('智能体分成收益')
}

//选择
const toSelect = async (type: number) => {
    params.value.type = type
    await nextTick()
    getLists()
}

const { pager, getLists } = usePaging({
    fetchFun: accountList,
    params: params.value
})

//详情弹框
const showDetail = async (id: number) => {
    popShow.value = true
    await nextTick()
    popRef.value.open(id)
}

// 打开赠送模态框
const openGiftModal = () => {
    giftModalVisible.value = true
}

// 赠送成功后的处理
const handleGiftSuccess = () => {
    // 刷新余额明细列表
    getLists()
    // 可以显示成功提示
    ElMessage.success('赠送成功！')
}

onMounted(() => {
    getLists()
})
</script>

<style scoped lang="scss">
.isSelect {
    color: white;
    border-radius: 8px;
    @apply bg-primary;
}
</style>
