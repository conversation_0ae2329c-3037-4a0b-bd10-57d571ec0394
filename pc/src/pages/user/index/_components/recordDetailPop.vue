<template>
  <Popup ref="popRef" width="600px" @close="$emit('close')">
    <el-form label-width="90px" label-position="left">
      <el-form-item label="订单编号">
        {{ data?.sn }}
      </el-form-item>
      <el-form-item label="用户信息">
        {{ data?.user?.nickname }}
      </el-form-item>
      <el-form-item label="操作时间">
        {{ data?.create_time }}
      </el-form-item>
      <el-form-item label="应用名">
        {{ data?.robot_name || '-' }}
      </el-form-item>
      <el-form-item label="变动类型">
        {{ data?.change_type }}
      </el-form-item>
      <el-form-item :label="`变动${type}`">
        <span>{{ data?.action == 1 ? '+' : '-' }}</span
        >{{ data?.change_amount }}
      </el-form-item>
      
      <!-- 智能体分成收益详细信息 -->
      <template v-if="data?.revenue_info">
        <el-divider content-position="left">分成收益详情</el-divider>
        
        <el-form-item label="收益说明">
          <div class="text-green-600 font-medium">
            {{ data.revenue_info.revenue_desc }}
          </div>
        </el-form-item>
        
        <el-form-item label="智能体信息">
          <div class="flex items-center space-x-3">
            <el-avatar 
              :size="40" 
              :src="data.revenue_info.robot_image"
              class="flex-shrink-0"
            />
            <div>
              <div class="font-medium">{{ data.revenue_info.robot_name }}</div>
              <div class="text-sm text-gray-500">来自智能体广场</div>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="分享者">
          <div class="flex items-center space-x-3">
            <el-avatar 
              :size="32" 
              :src="data.revenue_info.sharer_avatar"
              class="flex-shrink-0"
            />
            <span>{{ data.revenue_info.sharer_nickname }}</span>
          </div>
        </el-form-item>
        
        <el-form-item label="分成详情">
          <div class="space-y-2">
            <div class="flex justify-between">
              <span>总消耗电力值：</span>
              <span class="font-medium">{{ data.revenue_info.total_cost }}</span>
            </div>
            <div class="flex justify-between">
              <span>分成比例：</span>
              <span class="font-medium text-blue-600">{{ data.revenue_info.share_ratio }}%</span>
            </div>
            <div class="flex justify-between">
              <span>您的收益：</span>
              <span class="font-medium text-green-600">+{{ data.revenue_info.share_amount }}</span>
            </div>
            <div class="flex justify-between text-sm text-gray-500">
              <span>平台保留：</span>
              <span>{{ data.revenue_info.platform_amount }}</span>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="结算信息">
          <div class="space-y-1">
            <div>
              <span class="text-sm text-gray-500">结算方式：</span>
              <span class="text-sm">{{ data.revenue_info.settle_type_desc }}</span>
            </div>
            <div v-if="data.revenue_info.settle_time">
              <span class="text-sm text-gray-500">结算时间：</span>
              <span class="text-sm">{{ data.revenue_info.settle_time }}</span>
            </div>
          </div>
        </el-form-item>
        
        <el-alert
          title="收益说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #description>
            <div class="text-sm">
              • 通过使用智能体广场的智能体可获得分成收益<br>
              • 分成收益将在每天凌晨自动结算并发放<br>
              • 分成比例由智能体分享者设置决定
            </div>
          </template>
        </el-alert>
      </template>
      
      <el-form-item label="扣费明细" v-if="data?.flows?.length"> </el-form-item>
    </el-form>
    <div class="mt-[-10px]" v-if="data?.flows?.length">
      <el-table :data="data?.flows">
        <el-table-column prop="name" label="模块名称"></el-table-column>
        <el-table-column prop="model" label="AI模型"></el-table-column>
        <el-table-column
          prop="total_price"
          :label="`消耗${appStore.getTokenUnit}`"
        ></el-table-column>
      </el-table>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { accountDetail } from '@/api/account'
import { useAppStore } from '@/stores/app'

const props = defineProps({
  type: {
    type: Number,
    default: -1
  }
})

const emits = defineEmits(['close'])
const appStore = useAppStore()
const popRef = shallowRef()

const id = ref(-1)

const data: any = ref({})

//获取详情
const getDetail = async () => {
  data.value = await accountDetail({ id: id.value })
}

//打开弹框
const open = async (value: number) => {
  popRef.value.open()
  id.value = value
  await nextTick()
  getDetail()
}

defineExpose({ open })
</script>
