<template>
  <div class="p-[20px] flex bg-body rounded-[12px] flex-col h-full">
    <div class="flex justify-between items-center mb-4">
      <div class="title font-medium text-xl">灵感赠送</div>
      <el-button type="primary" @click="openGiftModal">
        <Icon name="local-icon-gift" size="16px" class="mr-2" />
        赠送他人灵感值
      </el-button>
    </div>

    <!-- 统计面板 -->
    <div class="mb-4 grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-page p-4 rounded-lg">
        <div class="text-sm text-gray-500 mb-1">本月赠送</div>
        <div class="text-2xl font-bold text-red-500">{{ statistics?.monthSend || 0 }} {{ appStore.getTokenUnit }}</div>
      </div>
      <div class="bg-page p-4 rounded-lg">
        <div class="text-sm text-gray-500 mb-1">本月接收</div>
        <div class="text-2xl font-bold text-green-500">{{ statistics?.monthReceive || 0 }} {{ appStore.getTokenUnit }}</div>
      </div>
      <div class="bg-page p-4 rounded-lg">
        <div class="text-sm text-gray-500 mb-1">累计赠送</div>
        <div class="text-2xl font-bold text-red-500">{{ statistics?.totalSend || 0 }} {{ appStore.getTokenUnit }}</div>
      </div>
      <div class="bg-page p-4 rounded-lg">
        <div class="text-sm text-gray-500 mb-1">累计接收</div>
        <div class="text-2xl font-bold text-green-500">{{ statistics?.totalReceive || 0 }} {{ appStore.getTokenUnit }}</div>
      </div>
    </div>

    <!-- 规则说明区域 -->
    <div class="bg-page p-4 rounded-lg mb-4">
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="赠送规则说明" name="rules">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="font-medium text-gray-800 mb-3">基础规则</h4>
              <div v-if="!config" class="text-center py-2">
                <i class="el-icon-loading"></i> 加载配置中...
              </div>
              <div v-else class="space-y-2 text-sm text-gray-600">
                <div>• 单次赠送范围：{{ config.min_gift_amount }}-{{ config.max_gift_amount }} {{ appStore.getTokenUnit }}</div>
                <div>• 每日赠送限额：{{ config.daily_gift_limit }} {{ appStore.getTokenUnit }}</div>
                <div>• 每日接收限额：{{ config.daily_receive_limit }} {{ appStore.getTokenUnit }}</div>
                <div>• 每日赠送次数：{{ config.gift_times_limit }} 次</div>
                <div>• 每日接收次数：{{ config.receive_times_limit }} 次</div>
              </div>
            </div>
            <div v-if="statistics && config">
              <h4 class="font-medium text-gray-800 mb-3">今日使用情况</h4>
              <div class="space-y-2 text-sm text-gray-600">
                <div>• 今日已赠送：{{ statistics.dailyGiftAmount || 0 }} {{ appStore.getTokenUnit }}（{{ statistics.dailyGiftTimes || 0 }} 次）</div>
                <div>• 今日剩余额度：{{ Math.max(0, config.daily_gift_limit - (statistics.dailyGiftAmount || 0)) }} {{ appStore.getTokenUnit }}</div>
                <div>• 今日剩余次数：{{ Math.max(0, config.gift_times_limit - (statistics.dailyGiftTimes || 0)) }} 次</div>
                <div>• 今日已接收：{{ statistics.dailyReceiveAmount || 0 }} {{ appStore.getTokenUnit }}（{{ statistics.dailyReceiveTimes || 0 }} 次）</div>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 筛选区域 -->
    <div class="bg-page p-4 rounded-lg mb-4">
      <el-form :model="queryParams" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="用户搜索">
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索对方用户昵称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="全部状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="成功" :value="1" />
            <el-option label="失败" :value="2" />
            <el-option label="已撤回" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="mb-4">
      <el-tab-pane label="全部记录" name="all" />
      <el-tab-pane label="我的赠送" name="send" />
      <el-tab-pane label="我的接收" name="receive" />
    </el-tabs>

    <!-- 数据表格 -->
    <div class="flex-1 min-h-0 flex flex-col">
      <div class="flex-1 min-h-0">
        <el-table :data="pager.lists" height="100%" v-loading="loading">
          <el-table-column label="流水号" prop="gift_sn" min-width="180">
            <template #default="{ row }">
              <el-button type="primary" link @click="showDetail(row)">
                {{ row.gift_sn }}
              </el-button>
            </template>
          </el-table-column>
          
          <el-table-column label="类型" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.type === 'send' ? 'danger' : 'success'">
                {{ row.type === 'send' ? '赠送' : '接收' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="对方用户" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center">
                <el-avatar 
                  :size="40" 
                  :src="row.type === 'send' ? row.to_user_avatar : row.from_user_avatar" 
                />
                <div class="ml-3">
                  <div class="font-medium">
                    {{ row.type === 'send' ? row.to_user_nickname : row.from_user_nickname }}
                  </div>
                  <div class="text-sm text-gray-500">
                    ID: {{ row.type === 'send' ? (row.to_user_sn || row.to_user_id) : (row.from_user_sn || row.from_user_id) }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="灵感值数量" prop="gift_amount" width="140" align="right">
            <template #default="{ row }">
              <div class="text-lg font-medium" :class="row.type === 'send' ? 'text-red-500' : 'text-green-500'">
                {{ row.type === 'send' ? '-' : '+' }}{{ Math.floor(parseFloat(row.gift_amount)) }} {{ appStore.getTokenUnit }}
              </div>
            </template>
          </el-table-column>
          

          
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="row.status === 1 ? 'success' : row.status === 2 ? 'danger' : 'warning'"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="时间" prop="create_time" width="180" />
          
          <el-table-column label="操作" width="80" align="center">
            <template #default="{ row }">
              <el-button type="primary" link @click="showDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <pagination 
          v-model="pager" 
          @change="getLists"
        />
      </div>
    </div>

    <!-- 赠送模态框 -->
    <GiftModal 
      v-model="giftModalVisible" 
      @success="handleGiftSuccess"
    />

    <!-- 详情模态框 -->
    <el-dialog 
      v-model="detailVisible" 
      title="赠送详情" 
      width="500px"
    >
      <div v-if="selectedRecord" class="space-y-4">
        <div class="flex justify-between">
          <span class="font-medium">流水号：</span>
          <span>{{ selectedRecord.gift_sn }}</span>
        </div>
        <div class="flex justify-between">
          <span class="font-medium">类型：</span>
          <el-tag :type="selectedRecord.type === 'send' ? 'danger' : 'success'">
            {{ selectedRecord.type === 'send' ? '赠送' : '接收' }}
          </el-tag>
        </div>
        <div class="flex justify-between">
          <span class="font-medium">对方用户：</span>
          <div class="flex items-center">
            <el-avatar 
              :size="32" 
              :src="selectedRecord.type === 'send' ? selectedRecord.to_user_avatar : selectedRecord.from_user_avatar" 
            />
            <span class="ml-2">
              {{ selectedRecord.type === 'send' ? selectedRecord.to_user_nickname : selectedRecord.from_user_nickname }}
            </span>
          </div>
        </div>
        <div class="flex justify-between">
          <span class="font-medium">灵感值数量：</span>
          <span class="text-lg font-medium" :class="selectedRecord.type === 'send' ? 'text-red-500' : 'text-green-500'">
            {{ selectedRecord.type === 'send' ? '-' : '+' }}{{ Math.floor(parseFloat(selectedRecord.gift_amount)) }} {{ appStore.getTokenUnit }}
          </span>
        </div>

        <div class="flex justify-between">
          <span class="font-medium">状态：</span>
          <el-tag :type="selectedRecord.status === 1 ? 'success' : selectedRecord.status === 2 ? 'danger' : 'warning'">
            {{ getStatusText(selectedRecord.status) }}
          </el-tag>
        </div>
        <div class="flex justify-between">
          <span class="font-medium">时间：</span>
          <span>{{ selectedRecord.create_time }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getGiftRecords, getUserGiftStatistics, getGiftConfig } from '@/api/gift'
import GiftModal from '@/components/gift/GiftModal.vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

const loading = ref(false)
const activeTab = ref('all')
const activeCollapse = ref<string[]>([])
const giftModalVisible = ref(false)
const detailVisible = ref(false)
const selectedRecord = ref<any>(null)
const dateRange = ref<[string, string] | null>(null)

const queryParams = ref({
  type: 'all',
  keyword: '',
  status: '',
  start_date: '',
  end_date: ''
})

const statistics = ref({
  monthSend: 0,
  monthReceive: 0,
  totalSend: 0,
  totalReceive: 0,
  dailyGiftAmount: 0,
  dailyReceiveAmount: 0,
  dailyGiftTimes: 0,
  dailyReceiveTimes: 0
})

const config = ref(null)

const { pager, getLists } = usePaging({
  fetchFun: getGiftRecords,
  params: queryParams.value
})

const handleTabClick = (tab: any) => {
  queryParams.value.type = tab.name
  getLists()
}

const handleDateChange = (value: [string, string] | null) => {
  if (value) {
    queryParams.value.start_date = value[0]
    queryParams.value.end_date = value[1]
  } else {
    queryParams.value.start_date = ''
    queryParams.value.end_date = ''
  }
}

const handleSearch = () => {
  getLists()
}

const handleReset = () => {
  queryParams.value = {
    type: activeTab.value,
    keyword: '',
    status: '',
    start_date: '',
    end_date: ''
  }
  dateRange.value = null
  getLists()
}

const openGiftModal = () => {
  giftModalVisible.value = true
}

const handleGiftSuccess = () => {
  getLists()
  loadStatistics()
}

const showDetail = (record: any) => {
  selectedRecord.value = record
  detailVisible.value = true
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '成功',
    2: '失败',
    3: '已撤回'
  }
  return statusMap[status] || '未知'
}

const loadStatistics = async () => {
  try {
    const { data } = await getUserGiftStatistics()
    statistics.value = data || {
      monthSend: 0,
      monthReceive: 0,
      totalSend: 0,
      totalReceive: 0,
      dailyGiftAmount: 0,
      dailyReceiveAmount: 0,
      dailyGiftTimes: 0,
      dailyReceiveTimes: 0
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 确保在失败时也有默认值
    statistics.value = {
      monthSend: 0,
      monthReceive: 0,
      totalSend: 0,
      totalReceive: 0,
      dailyGiftAmount: 0,
      dailyReceiveAmount: 0,
      dailyGiftTimes: 0,
      dailyReceiveTimes: 0
    }
  }
}

const loadConfig = async () => {
  try {
    const { data } = await getGiftConfig()
    config.value = data
  } catch (error) {
    console.error('加载配置失败:', error)
    // 设置默认配置
    config.value = {
      is_enable: 1,
      min_gift_amount: 1,
      max_gift_amount: 1000,
      daily_gift_limit: 100,
      daily_receive_limit: 500,
      gift_times_limit: 10,
      receive_times_limit: 20,
      friend_only: 0,
      need_verify: 0
    }
  }
}

onMounted(() => {
  getLists()
  loadStatistics()
  loadConfig()
})
</script>

<style scoped lang="scss">
.title {
  color: var(--el-text-color-primary);
}

:deep(.el-table) {
  .el-table__row:hover > td {
    background-color: var(--el-table-row-hover-bg-color);
  }
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style> 