<template>
    <el-form
        v-if="isRenderForm"
        ref="formRef"
        :model="formData"
        label-width="140px"
        :rules="formRules"
        class="app-edit flex flex-col"
    >
        <div class="flex-1 min-h-0">
            <el-tabs :model-value="'base'">
                <el-tab-pane label="基本配置" name="base">
                    <ElScrollbar>
                        <BaseConfig v-model="formData"/>
                    </ElScrollbar>
                </el-tab-pane>

                <el-tab-pane label="AI模型/搜索配置" name="search">
                    <ElScrollbar>
                        <SearchConfig v-model="formData"/>
                    </ElScrollbar>
                </el-tab-pane>
                <el-tab-pane label="界面配置" name="interface">
                    <ElScrollbar>
                        <InterfaceConfig v-model="formData"/>
                    </ElScrollbar>
                </el-tab-pane>
                <el-tab-pane label="形象配置" name="digital">
                    <ElScrollbar>
                        <DigitalConfig v-model="formData"/>
                    </ElScrollbar>
                </el-tab-pane>
                <el-tab-pane label="工作流配置" name="flow">
                    <ElScrollbar>
                        <FlowConfig v-model="formData"/>
                    </ElScrollbar>
                </el-tab-pane>
            </el-tabs>
        </div>
        <div class="my-[15px] flex justify-center">
            <el-button type="primary" @click="handelSubmit"> 保存</el-button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import type {FormInstance, FormRules} from 'element-plus'
import {ElMessageBox} from 'element-plus'
import {cloneDeep} from 'lodash-es'
import BaseConfig from './base-config.vue'
import SearchConfig from './search-config.vue'
import InterfaceConfig from './interface-config.vue'
import DigitalConfig from './digital-config.vue'
import FlowConfig from './flow-config.vue'
import feedback from '@/utils/feedback'
import {putRobot} from '@/api/robot'

const props = defineProps<{
    modelValue: Record<string, any>
}>()
const emit = defineEmits<{
    (event: 'success'): void
}>()

const formData = ref({})

const isRenderForm = computed(() => !!Object.keys(formData.value).length)
watchEffect(() => {
    formData.value = cloneDeep(props.modelValue)
})
const formRef = shallowRef<FormInstance>()
const formRules = shallowReactive<FormRules>({
    image: [
        {
            required: true,
            type: 'string',
            message: '请选择应用图标'
        }
    ],
    name: [
        {
            required: true,
            message: '请输入应用名称'
        }
    ],
    model_id: [
        {
            required: true,
            message: '请选择AI通道',
            trigger: ['blur']
        }
    ],
    model_sub_id: [
        {
            required: true,
            message: '请选择AI模型',
            trigger: ['blur']
        }
    ],
    cate_id: [
        {
            required: true,
            message: '请选择分类',
            trigger: ['blur']
        }
    ],
    digital_id: [
        {
            required: true,
            message: '请选择形象',
            trigger: ['change']
        },
        {
            validator(rule: any, value: any, callback: any) {
                if (Number(value) === 0) {
                    callback(new Error('请选择形象'))
                }
                callback()
            }
        }
    ]
})

const handelSubmit = async () => {
    try {
        await formRef.value?.validate()

        // 先尝试保存，如果返回特殊错误码则显示确认对话框
        try {
            await putRobot(formData.value)
            emit('success')
        } catch (apiError: any) {
            // 🔧 PC端调试日志 - 开始
            console.log('🚨 [PC端] 捕获到API错误:', apiError);
            console.log('📝 [PC端] 错误对象类型:', typeof apiError);
            console.log('📝 [PC端] 错误对象结构:', JSON.stringify(apiError, null, 2));
            console.log('📝 [PC端] 错误消息:', apiError.msg);
            console.log('📝 [PC端] 错误消息类型:', typeof apiError.msg);
            console.log('📝 [PC端] 错误数据:', apiError.data);
            console.log('🔍 [PC端] 是否包含msg属性:', 'msg' in apiError);
            console.log('🔍 [PC端] msg属性值:', apiError.msg);
            console.log('🔍 [PC端] 字符串比较结果:', apiError.msg === 'ROBOT_ONLINE_NEED_CONFIRM');
            console.log('🔍 [PC端] 字符串长度比较:', apiError.msg?.length, 'vs', 'ROBOT_ONLINE_NEED_CONFIRM'.length);

            // 检查是否是智能体已上架需要确认的错误
            if (apiError.msg === 'ROBOT_ONLINE_NEED_CONFIRM') {
                console.log('✅ [PC端] 检测到ROBOT_ONLINE_NEED_CONFIRM错误');
                console.log('🔔 [PC端] 准备显示确认对话框');

                const squareInfo = apiError.data
                console.log('📊 [PC端] 智能体状态信息:', squareInfo);

                try {
                    console.log('🖥️ [PC端] 开始显示ElMessageBox确认对话框');

                    // 显示确认对话框
                    const confirmResult = await ElMessageBox.confirm(
                        '此智能体已在智能体广场上架，编辑后需要重新提交审核。确认编辑将自动下架此智能体，是否继续？',
                        '智能体编辑确认',
                        {
                            confirmButtonText: '确认编辑',
                            cancelButtonText: '取消',
                            type: 'warning',
                            center: true
                        }
                    ).catch((dialogError) => {
                        console.log('❌ [PC端] 用户取消或对话框错误:', dialogError);
                        return false;
                    })

                    console.log('🎯 [PC端] 用户选择结果:', confirmResult);

                    if (confirmResult) {
                        console.log('✅ [PC端] 用户确认编辑，准备重新提交');

                        // 用户确认，带上确认标识重新提交
                        const confirmData = { ...formData.value, confirm_offline: true }
                        console.log('📝 [PC端] 重新提交数据:', confirmData);

                        await putRobot(confirmData)
                        console.log('✅ [PC端] 重新提交成功');

                        feedback.msgSuccess('智能体已更新并从广场下架，如需重新上架请重新提交审核')
                        emit('success')
                    } else {
                        console.log('❌ [PC端] 用户取消编辑');
                    }
                } catch (dialogError) {
                    console.error('❌ [PC端] 确认对话框处理失败:', dialogError);
                }
            } else {
                console.log('❌ [PC端] 不是ROBOT_ONLINE_NEED_CONFIRM错误');
                console.log('🔍 [PC端] 实际错误消息:', JSON.stringify(apiError.msg));
                console.log('🔍 [PC端] 期望错误消息:', 'ROBOT_ONLINE_NEED_CONFIRM');

                // 其他错误正常处理
                throw apiError
            }
            // 🔧 PC端调试日志 - 结束
        }
    } catch (error: any) {
        for (const err in error) {
            const isInRules = Object.keys(formRules).includes(err)
            isInRules && feedback.msgError(error[err][0]?.message)
            break
        }
    }
}
</script>

<style lang="scss" scoped>
.app-edit {
    height: 100%;

    :deep(.el-tabs) {
        --el-tabs-header-height: 50px;
        height: 100%;
        display: flex;
        flex-direction: column;

        .el-tabs__nav {
            padding: 0 20px;
        }

        .el-tabs__content,
        .el-tab-pane {
            min-height: 0;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .el-tabs__item {
            font-size: 15px;
        }
    }
}
</style>
