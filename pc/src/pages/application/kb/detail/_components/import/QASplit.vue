<template>
    <div>
        <!-- 重要提示 -->
        <div class="important-notice">
            <div class="notice-header">
                <Icon name="el-icon-Warning" color="#ff9900" size="18" />
                <span class="notice-title">重要提示</span>
            </div>
            <div class="notice-content">
                为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。
            </div>
        </div>

        <div class="h-full flex flex-col">
            <div v-loading="loading" class="py-[16px]">
                <el-upload
                    ref="uploadRef"
                    drag
                    :on-change="fileInput"
                    :auto-upload="false"
                    :show-file-list="false"
                    :accept="accept"
                    :multiple="true"
                    :limit="50"
                >
                    <div class="el-upload__text">
                        <Icon name="el-icon-Upload" />
                        拖拽文件至此，或点击<em> 选择文件 </em>
                    </div>
                    <div class="el-upload__text">支持 {{ accept }} 文件</div>
                </el-upload>
            </div>
            <div
                v-if="data.length > 0"
                class="grid grid-cols-2 gap-4 flex-1 min-h-[500px]"
            >
                <div style="border-right: 1px solid #eeeeee">
                    <div class="mt-4 max-w-[500px]">
                        <div
                            v-for="(item, index) in data"
                            :key="index"
                            class="fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer hover:bg-page transition duration-300"
                            :class="{ 'bg-page': showIndex == index }"
                            @click="selectShow(index)"
                        >
                            <Icon
                                name="el-icon-Folder"
                                :size="16"
                                color="#ffc94d"
                            />
                            <div class="ml-2">
                                {{ item.name }}
                            </div>
                            <div
                                class="closeIcon ml-auto opacity-0 transition duration-300 flex items-center"
                            >
                                <Icon
                                    name="el-icon-DeleteFilled"
                                    @click="delFile(index)"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col">
                    <div class="text-lg">
                        分段预览（{{ data[showIndex]?.data.length }}组）
                    </div>
                    <div class="flex-auto mt-2 h-[100px]">
                        <el-scrollbar height="100%">
                            <div
                                class="bg-page rounded p-[10px] mt-2"
                                v-for="(item, index) in data[showIndex]?.data"
                                :key="index"
                            >
                                <data-item
                                    :index="index"
                                    :name="data[showIndex].name"
                                    v-model:data="item.q"
                                    @delete="handleDelete(index)"
                                />
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { UploadFile, UploadInstance } from 'element-plus'
import { useVModel } from '@vueuse/core'
import { type IDataItem, isSameFile } from './hook'
import feedback from '@/utils/feedback'
import { splitText2ChunksArray } from '@/utils/textSplitter'
import DataItem from './data-item.vue'
import { readDocContent, readPdfContent, readTxtContent } from '@/utils/fileReader'
import { useLockFn } from '@/hooks/useLockFn'

const props = defineProps<{
    modelValue: IDataItem[]
}>()
const emit = defineEmits(['update:modelValue'])
const data = useVModel(props, 'modelValue', emit)

//文件类型
const fileAccept = ['.txt', '.docx', '.pdf', '.md']
const accept = fileAccept.join(', ')
const fileList = ref<File[]>([])
const uploadRef = shallowRef<UploadInstance>()
//加载
const loading = ref(false)

//预览
const showIndex = ref(-1)
const showContent = reactive<string[]>([])
const knowDetail: any = inject('knowDetail')
//选择文件
const fileInput = async ({ raw: file }: UploadFile) => {
    try {
        if (file) {
            // 验证文件类型
            const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
            if (!fileAccept.includes(fileExtension)) {
                throw `不支持的文件类型，请上传 ${accept} 格式的文件`
            }

            loading.value = true
            await isSameFile(file, fileList.value)
            const content = await parseFile(file)
            if (!content) {
                throw '解析结果为空，已自动忽略'
            }
            const isSplitContent: any = splitContent(content)
            data.value.push({
                name: file.name,
                path: '',
                data: isSplitContent
            })

            fileList.value.push(file)
            selectShow(fileList.value.length - 1)
        }
    } catch (error: any) {
        feedback.msgError(error)
    } finally {
        loading.value = false
        uploadRef.value?.clearFiles()
    }
}

const parseFile = async (file: File) => {
    const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
    let res = ''
    switch (suffix) {
        case 'md':
        case 'txt':
            res = await readTxtContent(file)
            break
        case 'pdf':
            res = await readPdfContent(file)
            break
        case 'doc':
        case 'docx':
            res = await readDocContent(file)
            break
        default:
            res = await readTxtContent(file)
            break
    }
    return res
}

//选择预览文件
const selectShow = (index: number) => {
    // console.log(index)
    showIndex.value = index
    showContent.length = 0
    //   splitContent(data.value[index].question)
}

const splitContent = (content: string) => {
    const data: { q: string; a: string }[] = []
    const contentList = splitText2ChunksArray({
        text: content,
        chunkLen: knowDetail.qa_length
    })
    contentList.forEach((item) => {
        data.push({ q: item, a: '' })
    })
    return data
}
//拆分预览文件
// const splitContent = (content: string) => {
//   const step = 7000
//   const contentList = content.split('')
//   const data = []
//   while (contentList.length) {
//     if (content.length > 7000) {
//       const res = contentList.splice(0, step)
//       const resString = res.join('')
//       //   showContent.push(resString)
//       data.push({ q: resString, a: '' })
//     } else {
//       const resString = contentList.join('')
//       //   showContent.push(resString)
//       data.push({ q: resString, a: '' })
//       break
//     }
//   }
//   return data
// }

const handleDelete = async (index: any) => {
    data.value[showIndex.value].data.splice(index, 1)
}
//删除文件
const delFile = (index: number) => {
    data.value.splice(index, 1)
    fileList.value.splice(index, 1)
}
</script>

<style scoped lang="scss">
.fileItem {
    &:hover {
        .closeIcon {
            opacity: 1 !important;
        }
    }
}
.important-notice {
    background: linear-gradient(135deg, #fff8e6 0%, #fff3d6 100%);
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
    
    .notice-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .notice-title {
            font-weight: 600;
            color: #e6a23c;
            margin-left: 8px;
            font-size: 16px;
        }
    }
    
    .notice-content {
        color: #856404;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        padding-left: 26px;
    }
}
</style>
