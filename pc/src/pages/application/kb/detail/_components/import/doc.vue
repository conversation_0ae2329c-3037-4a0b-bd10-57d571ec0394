<template>
    <div class="h-full flex flex-col min-h-0">
        <!-- 模板库选择区域 -->
        <div class="mb-4 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center mb-3">
                <Icon name="el-icon-Document" size="16" class="mr-2" />
                <span class="font-medium">模板库</span>
                <el-tooltip content="选择模板快速下载导入文档" placement="top">
                    <Icon name="el-icon-QuestionFilled" size="14" class="ml-2 text-gray-400" />
                </el-tooltip>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <el-select
                        v-model="selectedCategoryId"
                        placeholder="请选择模板类别"
                        clearable
                        class="w-full"
                        @change="handleCategoryChange"
                    >
                        <el-option
                            v-for="category in templateCategories"
                            :key="category.id"
                            :label="category.name"
                            :value="category.id"
                        />
                    </el-select>
                </div>
                <div>
                    <el-select
                        v-model="selectedTemplateId"
                        placeholder="请选择具体模板"
                        clearable
                        class="w-full"
                        :disabled="!selectedCategoryId"
                        @change="handleTemplateChange"
                        filterable
                    >
                        <el-option
                            v-for="template in currentTemplates"
                            :key="template.id"
                            :label="template.name"
                            :value="template.id"
                        >
                            <div class="flex justify-between items-center">
                                <span>{{ template.name }}</span>
                            </div>
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div v-if="selectedTemplate" class="mt-3 p-3 bg-white rounded border">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="font-medium text-sm">{{ selectedTemplate.name }}</div>
                        <div class="text-xs text-gray-500 mt-1">{{ selectedTemplate.description }}</div>
                        <div class="text-xs text-gray-400 mt-1">
                            文件类型：{{ selectedTemplate.file_type }}
                        </div>
                    </div>
                    <el-button 
                        type="primary" 
                        size="small" 
                        @click="downloadTemplate"
                        :loading="downloadLoading"
                    >
                        下载模板
                    </el-button>
                </div>
            </div>
        </div>

        <div v-loading="loading" class="py-[16px]">
            <el-upload
                ref="uploadRef"
                drag
                :on-change="fileInput"
                :auto-upload="false"
                :show-file-list="false"
                :accept="accept"
                :multiple="true"
                :limit="50"
            >
                <div class="el-upload__text">
                    <Icon name="el-icon-Upload" />
                    拖拽文件至此，或点击<em> 选择文件 </em>
                </div>
                <div class="el-upload__text">支持 {{ accept }} 文件</div>
                <!-- <template #tip>
          <div class="el-upload__tip">选择文本文件，直接将其按分段进行处理</div>
        </template> -->
            </el-upload>
        </div>

        <!-- <div class="flex-1 min-h-0">
      <el-scrollbar>
        <div v-for="(item, index) in data" :key="index">
          <div class="my-2 text-tx-primary font-medium text-lg">
            #{{ index + 1 }}
            {{ item.source }}
            <Icon
              class="icon-delete align-[-3px] text-primary"
              name="el-icon-Delete"
              @click="handleDelete(item)"
            />
          </div>
          <el-input
            v-model="item.question"
            :placeholder="`文件内容，空内容会自动省略`"
            type="textarea"
            resize="none"
            :rows="15"
          />
        </div>
      </el-scrollbar>
    </div> -->

        <div
            v-if="data.length > 0"
            class="grid grid-cols-2 gap-4 flex-1 min-h-[500px]"
        >
            <div style="border-right: 1px solid #eeeeee">
                <div class="mt-4 max-w-[500px]">
                    <div
                        v-for="(item, index) in data"
                        :key="index"
                        class="fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer hover:bg-page transition duration-300"
                        :class="{ 'bg-page': showIndex == index }"
                        @click="selectShow(index)"
                    >
                        <Icon
                            name="el-icon-Folder"
                            :size="16"
                            color="#ffc94d"
                        />
                        <div class="ml-2">
                            {{ item.name }}
                        </div>
                        <div
                            class="closeIcon ml-auto opacity-0 transition duration-300 flex items-center"
                        >
                            <Icon
                                name="el-icon-DeleteFilled"
                                @click="delFile(index)"
                            />
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex">
                        <div>分段长度</div>
                        <el-tooltip
                            content="按结束符号进行分段。我们建议您的文档应合理的使用标点符号，以确保每个完整的句子长度不要超过该值中文文档建议400~1000英文文档建议600~1200"
                            placement="top"
                        >
                            <span>
                                <Icon name="el-icon-QuestionFilled"></Icon>
                            </span>
                        </el-tooltip>
                    </div>

                    <el-input class="mt-2 !w-[300px]" v-model="step"></el-input>
                    <div class="mt-2">
                        <el-button type="primary" @click="reSplit"
                            >重新预览</el-button
                        >
                    </div>
                </div>
            </div>
            <div class="flex flex-col">
                <div class="text-lg">
                    分段预览（{{ data[showIndex]?.data.length }}组）
                </div>
                <div class="flex-auto mt-2 h-[100px]">
                    <el-scrollbar height="100%">
                        <div
                            class="bg-page rounded p-[10px] mt-2"
                            v-for="(item, index) in data[showIndex]?.data"
                            :key="index"
                        >
                            <data-item
                                :index="index"
                                :name="data[showIndex].name"
                                v-model:data="item.q"
                                @delete="handleDelete(index)"
                            />
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { UploadFile, UploadInstance } from 'element-plus'
import { useVModel } from '@vueuse/core'
import { type IDataItem, isSameFile } from './hook'
import {
    readDocContent,
    readPdfContent,
    readTxtContent
} from '@/utils/fileReader'
import { splitText2ChunksArray } from '@/utils/textSplitter'
import DataItem from './data-item.vue'
import { getAllTemplates, downloadTemplate as downloadTemplateApi } from '@/api/my_database'
// import feedback from '@/utils/feedback'
const props = defineProps<{
    modelValue: IDataItem[]
}>()
const emit = defineEmits(['update:modelValue'])
const data = useVModel(props, 'modelValue', emit)

const fileAccept = ['.txt', '.docx', '.pdf', '.md']
const accept = fileAccept.join(', ')
const fileList = ref<File[]>([])
const uploadRef = shallowRef<UploadInstance>()
const loading = ref(false)

//预览
const showIndex = ref(-1)

const isSplitContent = ref('')

//分段长度
const step = ref(512)

// 模板库相关数据
const selectedCategoryId = ref('')
const selectedTemplateId = ref('')
const templateCategories = ref<any[]>([])
const allTemplates = ref<any[]>([])
const downloadLoading = ref(false)

const currentTemplates = computed(() => {
    if (!selectedCategoryId.value) return []
    const category = allTemplates.value.find(c => c.id === selectedCategoryId.value)
    return category ? category.templates : []
})

const selectedTemplate = computed(() => {
    if (!selectedTemplateId.value) return null
    return currentTemplates.value.find((t: any) => t.id === selectedTemplateId.value)
})

// 处理类别选择变化
const handleCategoryChange = () => {
    selectedTemplateId.value = ''
}

// 处理模板选择变化
const handleTemplateChange = () => {
    // 模板选择后可以显示详细信息
}

// 下载模板
const downloadTemplate = async () => {
    if (!selectedTemplate.value) return
    
    try {
        downloadLoading.value = true
        const res = await downloadTemplateApi({ id: selectedTemplate.value.id })
        
        // 创建下载链接
        const link = document.createElement('a')
        link.href = res.download_url
        link.download = res.name
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        feedback.msgSuccess('模板下载成功')
    } catch (error: any) {
        feedback.msgError('模板下载失败: ' + (error.message || '未知错误'))
    } finally {
        downloadLoading.value = false
    }
}

// 获取模板库数据
const fetchTemplates = async () => {
    try {
        const res = await getAllTemplates()
        allTemplates.value = res
        templateCategories.value = res.map((category: any) => ({
            id: category.id,
            name: category.name
        }))
    } catch (error) {
        console.error('获取模板库数据失败:', error)
    }
}

// 组件挂载时获取模板库数据
onMounted(() => {
    fetchTemplates()
})

//选择文件
const fileInput = async ({ raw: file }: UploadFile) => {
    try {
        if (file) {
            // 验证文件类型
            const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
            if (!fileAccept.includes(fileExtension)) {
                throw `不支持的文件类型，请上传 ${accept} 格式的文件`
            }

            loading.value = true
            await isSameFile(file, fileList.value)
            const content = await parseFile(file)
            if (!content) {
                throw '解析结果为空，已自动忽略'
            }

            data.value.push({
                name: file.name,
                path: '',
                data: []
            })
            //@ts-ignore
            file.data = content

            fileList.value.push(file)
            selectShow(fileList.value.length - 1)
            reSplit()
        }
    } catch (error: any) {
        feedback.msgError(error)
    } finally {
        loading.value = false
        uploadRef.value?.clearFiles()
    }
}

const reSplit = () => {
    data.value.forEach((item: any) => {
        item.data = []
        const index = fileList.value.findIndex(
            (fileItem) => fileItem.name == item.name
        )
        const contentList = splitText2ChunksArray({
            //@ts-ignore
            text: fileList.value[index].data,
            chunkLen: step.value
        })
        contentList.forEach((contentListItem) => {
            item.data.push({ q: contentListItem, a: '' })
        })
    })
}

const parseFile = async (file: File) => {
    const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
    let res = ''
    switch (suffix) {
        case 'md':
        case 'txt':
            res = await readTxtContent(file)
            break
        case 'pdf':
            res = await readPdfContent(file)
            break
        case 'doc':
        case 'docx':
            res = await readDocContent(file)
            break
        default:
            res = await readTxtContent(file)
            break
    }
    return res
}

const handleDelete = async (index: any) => {
    data.value[showIndex.value].data.splice(index, 1)
}
//删除文件
const delFile = (index: number) => {
    data.value.splice(index, 1)
    fileList.value.splice(index, 1)
}

//拆分预览文件
const splitContent = (content: string) => {
    const step = 7000
    const contentList = content.split('')
    const data = []
    while (contentList.length) {
        if (content.length > 7000) {
            const res = contentList.splice(0, step)
            const resString = res.join('')
            //   showContent.push(resString)
            data.push({ q: resString, a: '' })
        } else {
            const resString = contentList.join('')
            //   showContent.push(resString)
            data.push({ q: resString, a: '' })
            break
        }
    }
    return data
}

//选择预览文件
const selectShow = (index: number) => {
    // console.log(index)
    showIndex.value = index
    //   showContent.length = 0
    //   splitContent(data.value[index].question)
}

defineExpose({
    clearFiles: () => {
        fileList.value = []
    }
})
</script>

<style scoped lang="scss">
.fileItem {
    &:hover {
        .closeIcon {
            opacity: 1 !important;
        }
    }
}
</style>
