<template>
  <div class="manual-import">
    <!-- 重要提示 -->
    <div class="important-notice">
      <div class="notice-header">
        <Icon name="el-icon-Warning" color="#ff9900" size="18" />
        <span class="notice-title">重要提示</span>
      </div>
      <div class="notice-content">
        为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。
      </div>
    </div>
    
    <el-scrollbar>
      <div class="py-4">
        <el-form label-width="0px">
          <el-form-item>
            <el-input v-model="formData.question" :placeholder="`请输入问题`" />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="formData.answer"
              :placeholder="`请输入问题答案，10000个字以内。`"
              type="textarea"
              resize="none"
              :rows="15"
              maxlength="10000"
            />
          </el-form-item>
          <!-- 隐藏图片上传功能 -->
          <el-form-item v-if="false">
            <div class="flex-1">
              <div>
                <Upload
                  v-model:files="images"
                  type="image"
                  list-type="picture-card"
                  :limit="9"
                  multiple
                  show-file-list
                >
                  <Icon name="el-icon-Plus" :size="20" />
                </Upload>
              </div>
              <div class="form-tips">最多支持上传 9 张图</div>
            </div>
          </el-form-item>
          <!-- 隐藏附件上传功能 -->
          <el-form-item v-if="false">
            <div class="flex-1">
              <div class="max-w-[600px]">
                <Upload
                  v-model:files="formData.files"
                  type="file"
                  show-file-list
                >
                  <el-button>上传附件</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持上传PDF、docx、excel、等文件格式
                    </div>
                  </template>
                </Upload>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>
  </div>
</template>
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import type { IManualQAData } from './hook'

const props = defineProps<{
  modelValue: IManualQAData
}>()

const emit = defineEmits<{
  (event: 'update:modelValue', value: IManualQAData): void
}>()
const formData = useVModel(props, 'modelValue', emit)
const images = ref([])
watch(images, (value) => {
  formData.value.images = value.map(({ url }) => url)
})
</script>

<style scoped lang="scss">
.important-notice {
  background: linear-gradient(135deg, #fff8e6 0%, #fff3d6 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
  
  .notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .notice-title {
      font-weight: 600;
      color: #e6a23c;
      margin-left: 8px;
      font-size: 16px;
    }
  }
  
  .notice-content {
    color: #856404;
    font-size: 14px;
    line-height: 1.6;
    text-align: justify;
    padding-left: 26px;
  }
}
</style>
