<template>
  <div class="web-page">
    <!-- 重要提示 -->
    <div class="important-notice mb-4">
      <div class="notice-header">
        <Icon name="el-icon-Warning" color="#ff9900" size="18" />
        <span class="notice-title">重要提示</span>
      </div>
      <div class="notice-content">
        为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。
      </div>
    </div>
    
    <div class="py-4">
      <ElForm>
        <ElFormItem>
          <div class="flex-1">
            <el-input
              v-model="url"
              :placeholder="`请输入要解析的网页链接，添加多个请按回车键分隔`"
              type="textarea"
              resize="none"
              :rows="6"
            />
          </div>
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" :loading="isLock" @click="parseUrl">
            解析
          </ElButton>
        </ElFormItem>
      </ElForm>
      <div>
        <div v-for="(item, index) in formData" :key="index" class="mb-4">
          <div class="mb-2 text-tx-primary font-medium text-lg">
            #{{ index + 1 }}
            {{ item.name }}
            <ElButton link type="primary">
              <Icon name="el-icon-Delete" @click="handleDelete(item)" />
            </ElButton>
          </div>
          <template v-for="(data, index) in item.data" :key="index">
            <el-input
              v-model="data.q"
              :placeholder="`文件内容，空内容会自动省略`"
              type="textarea"
              resize="none"
              :rows="15"
            />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import type { IDataItem } from './hook'
import { webHtmlCapture } from '@/api/my_database'
import feedback from '@/utils/feedback'
const props = defineProps<{
  modelValue: IDataItem[]
}>()

const emit = defineEmits<{
  (event: 'update:modelValue', value: IDataItem): void
}>()
const formData = useVModel(props, 'modelValue', emit)
const url = ref('')

const handleDelete = async (item: IDataItem) => {
  await feedback.confirm(`确定删除：${item.name}？`)
  const index = formData.value.indexOf(item)
  if (index !== -1) {
    formData.value.splice(index, 1)
  }
}

const { lockFn: parseUrl, isLock } = useLockFn(async () => {
  if (!url.value) return feedback.msgError('请输入网页链接')
  const data = await webHtmlCapture({
    url: url.value.split('\n').filter(Boolean)
  })
  formData.value = [
    ...data.map((item: any) => ({
      data: [
        {
          a: '',
          q: item.content
        }
      ],
      path: '',
      name: item.url
    })),
    ...formData.value
  ]
  url.value = ''
})
</script>

<style scoped lang="scss">
.important-notice {
  background: linear-gradient(135deg, #fff8e6 0%, #fff3d6 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
  
  .notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .notice-title {
      font-weight: 600;
      color: #e6a23c;
      margin-left: 8px;
      font-size: 16px;
    }
  }
  
  .notice-content {
    color: #856404;
    font-size: 14px;
    line-height: 1.6;
    text-align: justify;
    padding-left: 26px;
  }
}
</style>
