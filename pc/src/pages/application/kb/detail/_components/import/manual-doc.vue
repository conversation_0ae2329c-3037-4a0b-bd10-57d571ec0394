<template>
  <div class="manual-import">
    <!-- 重要提示 -->
    <div class="important-notice">
      <div class="notice-header">
        <Icon name="el-icon-Warning" color="#ff9900" size="18" />
        <span class="notice-title">重要提示</span>
      </div>
      <div class="notice-content">
        为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。
      </div>
    </div>
    
    <div class="py-4 flex flex-col">
      <ElForm>
        <ElFormItem>
          <div class="flex-1 min-w-0">
            <el-input
              v-model="formData.question"
              :placeholder="`请输入文本内容，10000个字以内。`"
              type="textarea"
              resize="none"
              :rows="15"
              maxlength="10000"
            />
          </div>
        </ElFormItem>
      </ElForm>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import type { IDataItem } from './hook'

const props = defineProps<{
  modelValue: IDataItem
}>()

const emit = defineEmits<{
  (event: 'update:modelValue', value: IDataItem): void
}>()
const formData = useVModel(props, 'modelValue', emit)
</script>

<style scoped lang="scss">
.important-notice {
  background: linear-gradient(135deg, #fff8e6 0%, #fff3d6 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
  
  .notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .notice-title {
      font-weight: 600;
      color: #e6a23c;
      margin-left: 8px;
      font-size: 16px;
    }
  }
  
  .notice-content {
    color: #856404;
    font-size: 14px;
    line-height: 1.6;
    text-align: justify;
    padding-left: 26px;
  }
}
</style>
