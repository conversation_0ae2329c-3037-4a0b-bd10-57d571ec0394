<template>
    <div class="share-popup">
        <popup
            ref="popupRef"
            title="分享至广场"
            :async="true"
            width="400px"
            :center="true"
            cancelButtonText=""
            confirmButtonText=""
            :appendToBody="false"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <!-- 审核拒绝原因显示 -->
            <div v-if="rejectReason" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-start space-x-2">
                    <div class="text-red-500 mt-0.5">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="text-sm text-red-700">
                        <div class="font-medium mb-1">上次审核拒绝原因</div>
                        <div class="bg-red-100 px-3 py-2 rounded-md">
                            {{ rejectReason }}
                        </div>
                        <div class="text-xs text-red-600 mt-2">
                            请根据拒绝原因调整智能体内容后重新提交
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审核提示说明 -->
            <div v-if="!revenueConfig.auto_audit" class="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <div class="flex items-start space-x-2">
                    <div class="text-amber-500 mt-0.5">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="text-sm text-amber-700">
                        <div class="font-medium mb-1">审核提醒</div>
                        <div>
                            当前系统采用人工审核模式，提交后需要管理员审核通过才能在广场显示，请耐心等待审核结果。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分成收益说明 -->
            <div v-if="revenueConfig.is_enable" class="mb-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
                <div class="flex items-start space-x-3">
                    <div class="text-green-500 mt-0.5">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                            <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <div class="text-base font-semibold text-green-800 mb-2">💰 分成收益说明</div>
                        <div class="text-sm text-green-700 leading-relaxed">
                            <div class="mb-2">
                                <span class="font-medium">🎯 收益机制：</span>分享智能体至广场后，其他用户每次使用您的智能体时，您都将获得分成收益！
                            </div>
                            <div class="mb-2">
                                <span class="font-medium">💎 分成比例：</span>用户消耗{{ tokenUnit }}的 <span class="bg-green-200 px-2 py-1 rounded font-bold text-green-900">{{ shareRatio }}%</span> 将作为您的收益
                            </div>
                            <div class="text-xs text-green-600 bg-green-100 px-3 py-2 rounded-md mt-2">
                                <span class="font-medium">示例：</span>用户使用您的智能体消耗了100{{ tokenUnit }}，您将获得{{ Math.round(100 * shareRatio / 100) }}{{ tokenUnit }}收益
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="h-[100px]">
                <el-select
                    size="large"
                    class="w-[360px]"
                    v-model="formData.cate_id"
                    placeholder="全部"
                    style="--el-fill-color-blank: #F7F7FB"
                >
                    <el-option
                        v-for="item in cateLists"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </div>
            <template #footer>
                <div class="dialog-footer flex justify-center pb-2">
                    <el-button
                        type="primary"
                        :loading="isLock"
                        class="!rounded-md"
                        @click="handleSubmit"
                    >
                        分享至广场
                    </el-button>
                </div>
            </template>
        </popup>
    </div>
</template>
<script lang="ts" setup>
import { getAgentCategoryList, shareAgent } from '~/api/task_reward'
import Popup from '~/components/popup/index.vue'
import { useLockFn } from '@/composables/useLockFn'
import { useUserStore } from '~/stores/user'
import { useAppStore } from '~/stores/app'

const userStore = useUserStore()
const appStore = useAppStore()

const emit = defineEmits(['success', 'close'])
const popupRef = shallowRef<InstanceType<typeof Popup>>()

const cateLists = ref<{
    name: string,
    id: string
    image: string
}[]>([])
const formData = reactive<{
    cate_id: string,
    id: string
}>({
    cate_id: '',
    id: ''
})
const rejectReason = ref<string>('')

// 获取分成收益配置信息
const revenueConfig = computed(() => ({
    ...appStore.getSquareConfig?.robot_award?.revenue_config || {},
    auto_audit: appStore.getSquareConfig?.robot_award?.auto_audit || 0
}))
const tokenUnit = computed(() => appStore.getTokenUnit)
const shareRatio = computed(() => revenueConfig.value.share_ratio || 30)

const getData = async () => {
    try {
        const list = await getAgentCategoryList()
        list.unshift({ name: '全部', id: '' })
        cateLists.value = list
    } catch (error) {
        console.log('获取视频分类失败=>', error)
    }
}

//提交
const { lockFn: handleSubmit, isLock } = useLockFn(async () => {
    await shareAgent(formData)
    await userStore.getUser()
    popupRef.value?.close()
    emit('success', formData.id)
})

const handleClose = () => {
    emit('close')
}

const open = (id: string, verifyResult?: string) => {
    getData()
    popupRef.value?.open()
    formData.id = id
    rejectReason.value = verifyResult || ''  // 设置拒绝原因
}

defineExpose({ open })
</script>

<style scoped>
.share-popup {
    :deep() {
        .el-dialog {
            border-radius: 20px;
        }
        .el-select__wrapper {
            box-shadow: none;
        }
        .el-select__wrapper:hover {
            box-shadow: 0 0 0 1px var(--el-border-color) inset;
        }
    }
}
</style>