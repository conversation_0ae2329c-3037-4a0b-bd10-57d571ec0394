<template>
    <div class="chat-container">
        <!-- VIP限制提示 -->
        <div v-if="showVipLimitTip" class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-300 rounded-lg p-4 shadow-lg text-blue-700 max-w-lg">
                <div class="font-semibold mb-2 flex items-center">
                    <span class="text-blue-500 mr-2 text-lg">💡</span>
                    会员免费额度提醒
                </div>
                <div class="leading-relaxed">{{ vipLimitTip }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const showVipLimitTip = ref(false)
const vipLimitTip = ref('')

// 监听消息发送前的VIP限制检查
const checkVipLimit = (modelInfo: any) => {
    if (modelInfo?.vip_limit_info?.is_exceeded) {
        showVipLimitTip.value = true
        vipLimitTip.value = '该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。'
        // 3秒后自动隐藏提示
        setTimeout(() => {
            showVipLimitTip.value = false
        }, 3000)
    }
}

// 在发送消息前检查VIP限制
const handleSend = async () => {
    if (!currentModel.value) return
    checkVipLimit(currentModel.value)
    // ... 原有的发送消息逻辑 ...
}
</script>

<style lang="scss" scoped>
// ... existing code ...
</style> 