<template>
    <div class="flex items-center">
        <!--    向量模型    -->
        <el-select
            v-if="type === 'vectorModels' || type === 'rankingModels'"
            class="flex-1"
            v-model="model"
            filterable
            :disabled="disabled"
        >
            <el-option
                class="!h-fit"
                v-for="item in chatModel.modelList"
                :value="item.id"
                :key="item.id"
                :label="item.alias || item.name"
            >
                <div class="my-1 flex items-center justify-between">
                    <div class="flex items-center flex-1">
                        <div class="leading-6 mr-2">{{ item.alias || item.name }}</div>
                        <div
                            v-if="item.price == '0' || item.is_free"
                            class="text-[#23B571] font-medium bg-[#E3FFF2] px-[8px] py-[4px] rounded-[6px] text-xs leading-[20px]"
                        >
                            会员免费
                        </div>
                        <div
                            v-else
                            class="text-gray-500 text-xs leading-5"
                        >
                            消耗{{ item.price }}{{ appStore.getTokenUnit }}/1000字符
                        </div>
                    </div>
                </div>
            </el-option>
        </el-select>

        <!--    对话模型    -->
        <div
            v-if="type === 'chatModels' || type === 'vlModels'"
            class="select-input flex items-center justify-between flex-1 cursor-pointer rounded-[8px] w-[266px] h-[32px] px-[15px]"
            :class="[
                subModel ? '' : 'text-tx-placeholder',
                disabled ? 'text-tx-placeholder cursor-no-drop bg-[--el-disabled-bg-color]' : ''
            ]"
            @click="popupRef.open()"
        >
            <div class="line-clamp-1 flex-1 flex items-center">
                <span v-if="currentModel.alias">{{ currentModel.alias }}</span>
                <span v-else class="text-[#a8abb2]">请选择</span>
                <!-- 选择后显示完整的模型名称（包括括号内描述），但不显示价格信息 -->
            </div>
            <div class="flex-none ml-2 flex items-center">
                <Icon name="el-icon-ArrowDown" />
            </div>
        </div>

        <popup
            v-if="type === 'chatModels' || type === 'vlModels'"
            ref="popupRef"
            width="780px"
            title="模型选择"

            customClass="!rounded-[15px]"
        >
            <template #footer>
                <div></div>
            </template>

            <el-scrollbar height="50vh" max-height="70vh">
                <div class="model-container">
                    <el-collapse
                        v-model:active-name="activeName"
                        class="flex flex-wrap justify-between"
                        accordion
                    >
                        <div
                            v-for="(items, fIndex) in [evenArray, oddArray]"
                            :key="fIndex"
                        >
                            <div
                                v-for="(item, index) in items"
                                :key="item.id"
                                class="w-[350px] mt-[15px]"
                            >
                                <el-collapse-item
                                    class="bg-[#f8f8f8] dark:bg-[#0d0e10] border border-solid border-[transparent]"
                                    :class="{
                                        'el-collapse-item--active':
                                            isActiveItem(fIndex, index)
                                    }"
                                    :name="findItemIndex(fIndex, index)"
                                >
                                    <template #title>
                                        <div>
                                            <div
                                                class="flex items-center h-[46px] py-2"
                                            >
                                                <img
                                                    v-if="item.logo"
                                                    :src="item.logo"
                                                    class="w-[30px] h-[30px]"
                                                    alt="模型logo"
                                                />
                                                <span
                                                    class="mx-2 leading-[24px] mt-[2px] font-medium"
                                                    >{{ item.name }}</span
                                                >
                                                <span
                                                    v-if="item.is_free"
                                                    class="bg-[#E3FFF2] text-[#23B571] font-medium px-[8px] py-[4px] leading-[20px] rounded-[6px] text-xs"
                                                >
                                                    会员免费
                                                </span>
                                            </div>
                                            <!--                                            <div-->
                                            <!--                                                v-if="item.remarks"-->
                                            <!--                                                class="flex items-center text-xs text-tx-placeholder font-normal mb-2"-->
                                            <!--                                            >-->
                                            <!--                                                {{ item.remarks }}-->
                                            <!--                                            </div>-->
                                        </div>
                                    </template>

                                    <el-scrollbar
                                        height="100%"
                                        max-height="250px"
                                    >
                                        <div
                                            v-for="cItem in item.models"
                                            :key="cItem.id"
                                            class="flex justify-between mb-[14px] px-[15px] cursor-pointer hover:text-primary"
                                            :class="{
                                                'text-primary':
                                                    subModel === cItem.id
                                            }"
                                            @click="
                                                setModelAndClose(
                                                    item.id,
                                                    cItem.id
                                                )
                                            "
                                        >
                                            <div class="flex items-center">
                                                <span class="mr-2 leading-6">{{
                                                    cItem.alias || '请选择'
                                                }}</span>
                                                <span
                                                    v-if="
                                                        cItem.alias &&
                                                        (cItem.price == '0' || cItem.is_free)
                                                    "
                                                    class="text-[#23B571] font-medium bg-[#E3FFF2] px-[8px] py-[2px] rounded-[4px] text-xs leading-[16px]"
                                                >
                                                    会员免费
                                                </span>
                                                <span
                                                    v-else-if="cItem.alias"
                                                    class="text-gray-500 text-xs leading-5"
                                                >
                                                    消耗{{ cItem.price }}{{ appStore.getTokenUnit }}/1000字符
                                                </span>
                                            </div>
                                            <div
                                                class="flex items-center"
                                                v-if="subModel === cItem.id"
                                            >
                                                <Icon
                                                    name="el-icon-CircleCheck"
                                                    size="20"
                                                />
                                            </div>
                                            <div
                                                class="flex items-center"
                                                v-else
                                            >
                                                <div
                                                    class="w-[18px] h-[18px] rounded-full border border-solid border-[#cacbd3]"
                                                ></div>
                                            </div>
                                        </div>

                                        <!-- VIP限制超出提示 - 告知用户会扣费 -->
                                        <div
                                            v-if="cItem && cItem.vip_limit_info && cItem.vip_limit_info.is_exceeded"
                                            class="mt-3 p-3 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-300 rounded-lg text-blue-700"
                                            style="box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);"
                                        >
                                            <div class="font-semibold mb-2 flex items-center text-sm">
                                                <span class="text-blue-500 mr-2 text-base">💡</span>
                                                会员免费额度提醒
                                            </div>
                                            <div class="text-xs leading-5 text-blue-600">
                                                该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。
                                            </div>
                                        </div>
                                    </el-scrollbar>
                                </el-collapse-item>
                            </div>
                        </div>
                    </el-collapse>
                </div>
            </el-scrollbar>
        </popup>
    </div>
</template>

<script setup lang="ts">
import { getAiModels } from '@/api/app'
import { useQuery } from '@tanstack/vue-query'
import { useVModel } from '@vueuse/core'
import { useAppStore } from '@/stores/app'
import Popup from '~/components/popup/index.vue'

const emit = defineEmits(['update:id', 'update:sub_id', 'update:modelConfig', 'update:config'])
const props = defineProps({
    id: {
        type: [String, Number],
        default: ''
    },
    sub_id: {
        type: [String, Number],
        default: ''
    },
    setDefault: {
        type: Boolean,
        default: true
    },
    type: {
        type: String as PropType<'chatModels' | 'vectorModels' | 'vlModels' | 'rankingModels'>,
        default: 'chatModels'
    },
    disabled: {
        type: Boolean,
        default: false
    }
})
const model = useVModel(props, 'id', emit)
const subModel = useVModel(props, 'sub_id', emit)
const appStore = useAppStore()

const popupRef = shallowRef<InstanceType<typeof Popup>>()
const activeName = ref<number | string | number[]>(-1)

const chatModel = reactive({
    modelList: [] as any[]
})

const evenArray = computed(() =>
    chatModel.modelList.filter((_, index) => index % 2 === 0)
)
const oddArray = computed(() =>
    chatModel.modelList.filter((_, index) => index % 2 !== 0)
)

const currentModel = computed(() => {
    // 防止modelList为undefined或空时出错
    if (!chatModel.modelList || !Array.isArray(chatModel.modelList)) {
        return {}
    }

    if (props.type === 'chatModels') {
        // 对话模型有子模型结构
        return (
            chatModel.modelList
                .flatMap((item: any) => item.models || [])
                .find((item: any) => item.id === subModel.value) || {}
        )
    } else {
        // 向量模型、VL模型、重排模型直接查找主模型
        return (
            chatModel.modelList
                .find((item: any) => item.id === model.value) || {}
        )
    }
})

watch(
    () => currentModel.value,
    (value) => {
        emit('update:modelConfig', value)        
        
        const configs = chatModel.modelList.find((item: any) => item.id === model.value)?.configs?.[0] || {}
        emit('update:configs', configs)

    }
)

const { suspense } = useQuery(['modelLists'], {
    queryFn: getAiModels,
    cacheTime: 1000
})

const getChatModelFunc = async () => {
    try {
        const { data } = await suspense()
        chatModel.modelList = data[props.type] || []
        console.log(`PC端获取${props.type}模型数据:`, chatModel.modelList)
        
        // 初始化已保存的值
        initSavedValues()
        
        if (props.setDefault && chatModel.modelList.length > 0) {
            setDefaultModel()
        }
    } catch (error) {
        console.log('PC端获取模型数据错误=>', error)
        chatModel.modelList = []
    }
}

// 初始化已保存的值
const initSavedValues = () => {
    if (!chatModel.modelList || chatModel.modelList.length === 0) {
        return
    }
    
    console.log(`PC端initSavedValues - type: ${props.type}, id: ${props.id}, sub_id: ${props.sub_id}`)
    console.log('PC端modelList:', chatModel.modelList)
    
    if (props.type === 'chatModels') {
        // 对话模型：如果有保存的sub_id，需要找到对应的主模型
        if (props.sub_id) {
            for (const item of chatModel.modelList) {
                if (item.models && item.models.some((subItem: any) => subItem.id === props.sub_id)) {
                    model.value = item.id
                    subModel.value = props.sub_id
                    // 设置对应的activeName用于展开面板
                    const index = chatModel.modelList.findIndex(modelItem => modelItem.id === item.id)
                    if (index !== -1) {
                        activeName.value = index
                    }
                    console.log(`PC端对话模型初始化: model=${item.id}, subModel=${props.sub_id}`)
                    break
                }
            }
        }
    } else {
        // 向量模型和重排模型：直接使用主模型ID
        if (props.id) {
            // 将props.id转换为字符串进行比较
            const propId = String(props.id)
            const savedModel = chatModel.modelList.find((item: any) => String(item.id) === propId)
            if (savedModel) {
                model.value = savedModel.id
                console.log(`PC端${props.type}初始化成功:`, propId, '->', savedModel.alias || savedModel.name)
            } else {
                console.log(`PC端${props.type}初始化失败: 未找到ID为 ${propId} 的模型`)
                console.log('PC端可用模型列表:', chatModel.modelList.map(item => ({ id: item.id, name: item.name, alias: item.alias })))
            }
        } else {
            console.log(`PC端${props.type}初始化跳过: props.id为空`)
        }
    }
}

const setDefaultModel = () => {
    if (!chatModel.modelList || chatModel.modelList.length === 0) {
        return
    }

    const defaultGroupIndex =
        chatModel.modelList.findIndex((item) => item.is_default) || 0
    
    if (props.type === 'chatModels') {
        // 对话模型有子模型结构
        const defaultModel = chatModel.modelList[defaultGroupIndex]?.models?.[0]
        if (defaultModel) {
            model.value = chatModel.modelList[defaultGroupIndex].id
            subModel.value = defaultModel.id
            activeName.value = defaultGroupIndex
        }
    } else {
        // 向量模型和重排模型直接设置主模型
        if (chatModel.modelList[defaultGroupIndex]) {
            model.value = chatModel.modelList[defaultGroupIndex].id
            activeName.value = defaultGroupIndex
        }
    }
}

const findItemIndex = (fIndex: number, index: number) =>
    fIndex === 0 ? index * 2 : index * 2 + 1

const isActiveItem = (fIndex: number, index: number) => {
    const targetModel = chatModel.modelList[findItemIndex(fIndex, index)]
    if (!targetModel || !targetModel.models) return false
    return targetModel.models.some(
        (item: any) => item.id === subModel.value
    )
}

const setModelAndClose = (id: string, sub_id: string) => {
    model.value = id
    if (props.type === 'chatModels') {
        subModel.value = sub_id
    } else {
        // 向量模型和重排模型不使用subModel
        subModel.value = ''
    }
    popupRef.value!.close()
}

// 监听props变化，重新初始化值
watch(
    () => [props.id, props.sub_id],
    () => {
        if (chatModel.modelList && chatModel.modelList.length > 0) {
            initSavedValues()
        }
    }
)

// 监听modelList变化，当数据加载完成后初始化已保存的值
watch(
    () => chatModel.modelList,
    (newList) => {
        if (newList && newList.length > 0) {
            console.log('PC端modelList更新，重新初始化已保存值')
            initSavedValues()
        }
    },
    { immediate: true }
)

getChatModelFunc()
</script>

<style lang="scss" scoped>
.select-input {
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
}
.model-container {
    :deep(.el-collapse) {
        border: none;
    }
    :deep(.el-collapse-item) {
        border-radius: 8px;
        .is-active {
            border: none;
            @apply bg-primary-light-9;
        }
    }
    :deep(.is-active) {
        @apply border border-solid border-primary bg-primary-light-9;
    }
    :deep(.el-collapse-item__header) {
        height: 100%;
        line-height: inherit;
        text-align: left;
        padding: 0 15px;
        border: none;
        border-radius: 8px;
        background-color: inherit;
    }
    :deep(.el-collapse-item__wrap) {
        border: none;
        border-radius: 8px;
        background-color: transparent;
    }
    :deep(.el-collapse-item__content) {
        border-radius: 8px;
        padding-bottom: 0;
    }
    .el-collapse-item--active {
        border-radius: 8px;
        @apply border border-solid border-primary bg-primary-light-9;

        :deep(.el-collapse-item__header) {
            @apply bg-primary-light-9;
        }
    }
}

/* VIP标签样式优化 */
.vip-free-tag {
    background: linear-gradient(135deg, #E3FFF2 0%, #D4F7E8 100%);
    color: #23B571;
    font-weight: 500;
    border: 1px solid rgba(35, 181, 113, 0.2);
    transition: all 0.2s ease;
}

.vip-free-tag:hover {
    background: linear-gradient(135deg, #D4F7E8 0%, #C5F4DD 100%);
    border-color: rgba(35, 181, 113, 0.3);
}

/* VIP限制提醒样式 */
.vip-limit-notice {
    background: linear-gradient(135deg, #EBF4FF 0%, #DBEAFE 100%);
    border: 1px solid #93C5FD;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
    transition: all 0.2s ease;
}

.vip-limit-notice:hover {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}
</style>
