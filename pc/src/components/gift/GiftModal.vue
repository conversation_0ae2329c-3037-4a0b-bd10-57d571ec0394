<template>
  <el-dialog
    v-model="visible"
    title="赠送灵感值"
    width="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="接收用户" prop="to_user_id">
        <div class="w-full">
          <el-input
            v-model="userIdInput"
            placeholder="请输入完整的用户ID（8位数字）"
            @blur="handleUserIdBlur"
            @keyup.enter="handleUserIdBlur"
            clearable
            maxlength="8"
          >
            <template #append>
              <el-button @click="handleUserIdBlur" :loading="searchLoading">
                搜索
              </el-button>
            </template>
          </el-input>
          <div v-if="selectedUser" class="mt-2 p-2 bg-gray-50 rounded flex items-center">
            <el-avatar :size="40" :src="selectedUser.avatar" />
            <div class="ml-3 flex-1">
              <div class="font-medium">{{ selectedUser.nickname }}</div>
              <div class="text-sm text-gray-500">ID: {{ selectedUser.sn }}</div>
            </div>
            <el-button @click="clearSelectedUser" size="small" text>
              <Icon name="el-icon-Close" />
            </el-button>
          </div>
          <div class="mt-1 text-xs text-gray-500">
            <div>• 请输入完整的用户ID，不支持模糊搜索</div>
            <div>• 为防止恶意搜索，每分钟最多可搜索{{ SEARCH_LIMIT }}次</div>
          </div>
        </div>
      </el-form-item>
      

      
      <el-form-item label="赠送灵感值数量" prop="gift_amount">
        <div class="w-full">
          <el-input-number
            v-model="form.gift_amount"
            :min="config?.min_gift_amount || 1"
            :max="Math.min(config?.max_gift_amount || 1000, Math.floor(userStore.userInfo.balance))"
            :precision="0"
            :step="1"
            class="w-full"
          />
          <div class="mt-1 text-sm text-gray-500 flex justify-between">
            <span>当前余额：{{ Math.floor(userStore.userInfo.balance) }} {{ appStore.getTokenUnit }}</span>
            <span v-if="config">单次限额：{{ config.min_gift_amount }}-{{ config.max_gift_amount }}</span>
            <span v-else>加载配置中...</span>
          </div>
        </div>
      </el-form-item>
      

      
      <div class="p-3 bg-blue-50 rounded text-sm text-blue-700 mb-4">
        <div class="font-medium mb-2">赠送规则提示：</div>
        <div v-if="!config" class="text-center py-2">
          <i class="el-icon-loading"></i> 加载配置中...
        </div>
        <div v-else class="space-y-1">
          <div>• 单次赠送范围：{{ config.min_gift_amount }}-{{ config.max_gift_amount }} {{ appStore.getTokenUnit }}</div>
          <div>• 每日赠送限额：{{ config.daily_gift_limit }} {{ appStore.getTokenUnit }}</div>
          <div>• 每日接收限额：{{ config.daily_receive_limit }} {{ appStore.getTokenUnit }}</div>
          <div>• 每日赠送次数：{{ config.gift_times_limit }} 次</div>
          <div>• 每日接收次数：{{ config.receive_times_limit }} 次</div>
          <div v-if="stats" class="border-t pt-2 mt-2">
            <strong>今日使用情况：</strong>
          </div>
          <div v-if="stats">• 今日已赠送：{{ stats.today_gift_amount || 0 }} {{ appStore.getTokenUnit }}（{{ stats.today_gift_times || 0 }} 次）</div>
          <div v-if="stats">• 今日剩余额度：{{ stats.remaining_daily_gift_limit || 0 }} {{ appStore.getTokenUnit }}</div>
          <div v-if="stats">• 今日剩余次数：{{ config.gift_times_limit - (stats.today_gift_times || 0) }} 次</div>
        </div>
      </div>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!canSubmit"
        >
          确认赠送
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { executeGift, getGiftConfig, getUserById, getUserGiftStatistics } from '@/api/gift'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const appStore = useAppStore()

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单引用
const formRef = ref()

// 表单数据
const form = ref({
  to_user_id: 0,
  gift_amount: 1
})

// 表单验证规则
const rules = {
  to_user_id: [
    { required: true, message: '请选择接收用户', trigger: 'blur' }
  ],
  gift_amount: [
    { required: true, message: '请输入赠送灵感值数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '赠送灵感值数量不能小于1', trigger: 'blur' }
  ]
}

// 状态数据
const loading = ref(false)
const searchLoading = ref(false)
const userIdInput = ref('')
const selectedUser = ref(null)
const config = ref(null)
const stats = ref(null)

// 搜索频次限制（只针对真正的API请求）
const searchAttempts = ref(0)
const lastSearchTime = ref(0)
const SEARCH_LIMIT = 20 // 每分钟最多搜索20次（放宽限制）
const SEARCH_WINDOW = 60000 // 1分钟时间窗口
const MIN_SEARCH_INTERVAL = 500 // 最小搜索间隔0.5秒（进一步降低限制）

// 计算属性
const canSubmit = computed(() => {
  if (!config.value) return false // 配置未加载时不能提交
  
  return form.value.to_user_id > 0 && 
         form.value.gift_amount > 0 && 
         form.value.gift_amount <= userStore.userInfo.balance &&
         form.value.gift_amount >= config.value.min_gift_amount &&
         form.value.gift_amount <= config.value.max_gift_amount
})

// 监听弹窗显示
watch(visible, (val) => {
  if (val) {
    // 检查用户是否已登录
    if (!userStore.isLogin) {
      ElMessage.error('请先登录')
      handleClose()
      userStore.showLogin = true
      return
    }
    initData()
  } else {
    resetForm()
  }
})

// 初始化数据
const initData = async () => {
  try {
    console.log('开始加载赠送配置数据...')
    
    // 并行加载配置和统计数据
    const [configRes, statsRes] = await Promise.all([
      getGiftConfig(),
      getUserGiftStatistics()
    ])
    
    console.log('配置API响应:', configRes)
    
    // 安全地更新配置，确保有默认值
    if (configRes && configRes.data) {
      config.value = configRes.data
      console.log('配置数据加载成功:', config.value)
    } else if (configRes) {
      // 如果configRes存在但data为空，直接使用configRes
      config.value = configRes
      console.log('直接使用配置响应:', config.value)
    } else {
      console.warn('配置API返回空数据')
      throw new Error('配置数据为空')
    }
    
    stats.value = statsRes.data || statsRes || null
    
    console.log('所有数据加载完成')
  } catch (error: any) {
    console.error('初始化数据失败:', error)
    console.error('错误详情:', {
      message: error.message,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data
    })
    
    // 检查是否是登录失败
    if (typeof error === 'string' && error.includes('登录')) {
      ElMessage.error('登录已过期，请重新登录')
      handleClose()
    } else if (error.response?.status === 401) {
      ElMessage.error('请先登录')
    } else {
      ElMessage.error('加载数据失败: ' + (error.message || error))
      // 设置默认配置以防止页面报错
      config.value = {
        is_enable: 1,
        min_gift_amount: 1,
        max_gift_amount: 1000,
        daily_gift_limit: 100,
        daily_receive_limit: 500,
        gift_times_limit: 10,
        receive_times_limit: 20,
        friend_only: 0,
        need_verify: 0
      }
    }
  }
}

// 检查搜索频次限制
const checkSearchLimit = () => {
  const now = Date.now()
  
  console.log('搜索频率检查:', {
    now,
    lastSearchTime: lastSearchTime.value,
    timeDiff: now - lastSearchTime.value,
    searchAttempts: searchAttempts.value,
    minInterval: MIN_SEARCH_INTERVAL,
    searchLimit: SEARCH_LIMIT,
    searchWindow: SEARCH_WINDOW
  })
  
  // 重置时间窗口
  if (now - lastSearchTime.value > SEARCH_WINDOW) {
    console.log('重置搜索计数')
    searchAttempts.value = 0
  }
  
  // 检查搜索间隔
  if (now - lastSearchTime.value < MIN_SEARCH_INTERVAL) {
    console.log('搜索间隔过短')
    ElMessage.warning(`搜索过于频繁，请等待${MIN_SEARCH_INTERVAL/1000}秒后再试`)
    return false
  }
  
  // 检查搜索次数限制
  if (searchAttempts.value >= SEARCH_LIMIT) {
    console.log('搜索次数超限')
    ElMessage.warning(`搜索次数过多，已搜索${searchAttempts.value}次，请1分钟后再试`)
    return false
  }
  
  console.log('搜索频率检查通过')
  return true
}

// 验证用户ID格式
const validateUserId = (userId: string) => {
  // 用户ID应该是8位数字
  const userIdRegex = /^\d{8}$/
  return userIdRegex.test(userId)
}

// 搜索用户
const handleUserIdBlur = async () => {
  const userSn = userIdInput.value.trim()
  
  // 清除之前的搜索结果
  selectedUser.value = null
  form.value.to_user_id = 0
  
  if (!userSn) {
    return
  }
  
  // 检查登录状态
  if (!userStore.isLogin) {
    ElMessage.error('请先登录')
    userStore.showLogin = true
    return
  }
  
  // 验证用户ID格式
  if (!validateUserId(userSn)) {
    ElMessage.error('请输入正确的用户ID（8位数字）')
    return
  }
  
  // 检查是否是自己
  if (userSn === userStore.userInfo.sn) {
    ElMessage.error('不能给自己赠送')
    return
  }
  
  // 检查搜索频次限制（只在即将发起API请求时检查）
  if (!checkSearchLimit()) {
    return
  }
  
  searchLoading.value = true
  
  // 无论成功失败，都要更新搜索记录（在API调用前更新）
  searchAttempts.value++
  lastSearchTime.value = Date.now()
  
  try {
    const res = await getUserById(userSn)
    
    // 由于HTTP拦截器的处理，成功的请求会直接返回data
    if (res && res.id) {
      selectedUser.value = res
      form.value.to_user_id = res.id
      ElMessage.success('用户查找成功')
    } else {
      ElMessage.error('未查询到用户信息，请确认用户ID是否正确')
    }
  } catch (error: any) {
    console.error('搜索用户失败:', error)
    selectedUser.value = null
    form.value.to_user_id = 0
    
    // 处理不同类型的错误
    let errorMessage = '查询失败，请稍后重试'
    
    if (typeof error === 'string') {
      // 直接的错误信息字符串
      errorMessage = error
    } else if (error?.message) {
      // 错误对象的message属性
      errorMessage = error.message
    } else if (error?.response?.data?.msg) {
      // HTTP响应中的错误信息
      errorMessage = error.response.data.msg
    } else if (error?.response?.status) {
      // 根据HTTP状态码提供友好的错误信息
      switch (error.response.status) {
        case 400:
          errorMessage = '请求参数错误'
          break
        case 401:
          errorMessage = '请先登录'
          break
        case 403:
          errorMessage = '没有权限执行此操作'
          break
        case 404:
          errorMessage = '未查询到用户信息，请确认用户ID是否正确'
          break
        case 429:
          errorMessage = '搜索过于频繁，请稍后再试'
          break
        case 500:
          errorMessage = '服务器错误，请稍后重试'
          break
        default:
          errorMessage = '查询失败，请稍后重试'
      }
    }
    
    // 特殊错误处理
    if (errorMessage.includes('登录') || errorMessage.includes('token') || errorMessage.includes('未登录')) {
      ElMessage.error('登录已过期，请重新登录')
      handleClose()
      return
    }
    
    ElMessage.error(errorMessage)
  } finally {
    searchLoading.value = false
  }
}



// 清除选中用户
const clearSelectedUser = () => {
  selectedUser.value = null
  form.value.to_user_id = 0
  userIdInput.value = ''
}

// 提交赠送
const handleSubmit = async () => {
  if (!formRef.value) return
  
  // 检查登录状态
  if (!userStore.isLogin) {
    ElMessage.error('请先登录')
    userStore.showLogin = true
    return
  }
  
  try {
    await formRef.value.validate()
  } catch (error) {
    return
  }
  
  loading.value = true
  try {
    const params = {
      to_user_id: form.value.to_user_id,
      gift_amount: form.value.gift_amount
    }
    
    const res = await executeGift(params)
    
    // 由于HTTP拦截器的处理，成功的请求会直接返回data
    ElMessage.success('赠送成功！')
    emit('success', res)
    handleClose()
    
    // 刷新用户信息
    userStore.getUser()
  } catch (error: any) {
    console.error('赠送失败:', error)
    // HTTP拦截器会将失败的请求转换为Promise.reject，错误信息在error中
    if (typeof error === 'string') {
      // 直接显示后端返回的错误信息
      ElMessage.error(error)
      
      // 如果是登录相关错误，关闭弹窗
      if (error.includes('登录') || error.includes('token')) {
        handleClose()
      }
    } else {
      ElMessage.error(error.message || '赠送失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 重置表单
const resetForm = () => {
  form.value = {
    to_user_id: 0,
    gift_amount: 1
  }
  selectedUser.value = null
  userIdInput.value = ''
  // 重置搜索限制
  searchAttempts.value = 0
  lastSearchTime.value = 0
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped lang="scss">
:deep(.el-input-number) {
  width: 100%;
}
</style> 