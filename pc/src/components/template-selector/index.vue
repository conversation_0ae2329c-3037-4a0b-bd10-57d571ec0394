<template>
    <div class="template-selector">
        <el-button 
            type="warning" 
            :icon="Download" 
            @click="showDialog = true"
            size="small"
            class="template-selector-btn"
        >
            <span class="btn-text">选择模板下载</span>
            <el-icon class="btn-icon"><ArrowRight /></el-icon>
        </el-button>
        
        <!-- 选择状态显示 -->
        <div v-if="selectedTemplate" class="selected-indicator">
            <el-tag type="warning" size="small" closable @close="clearSelection">
                <el-icon><Check /></el-icon>
                已选择：{{ selectedTemplate.name }}
            </el-tag>
        </div>
        
        <el-dialog
            v-model="showDialog"
            title="选择文档模板"
            width="900px"
            :close-on-click-modal="false"
            class="template-dialog"
        >
            <div class="dialog-content">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <el-input
                        v-model="searchKeyword"
                        placeholder="搜索模板名称或描述..."
                        :prefix-icon="Search"
                        clearable
                        class="search-input"
                    />
                </div>
                
                <div class="flex h-[520px] mt-4">
                    <!-- 左侧分类列表 -->
                    <div class="w-[220px] border-r border-gray-200 pr-4">
                        <div class="category-header">
                            <el-icon><FolderOpened /></el-icon>
                            <span>模板分类</span>
                        </div>
                        <div class="category-list">
                            <div
                                v-for="category in categories"
                                :key="category.id"
                                :class="[
                                    'category-item',
                                    selectedCategoryId === category.id ? 'category-active' : ''
                                ]"
                                @click="selectCategory(category.id)"
                            >
                                <div class="category-content">
                                    <span class="category-name">{{ category.name }}</span>
                                    <el-badge 
                                        :value="getCategoryTemplateCount(category.id)" 
                                        class="category-badge"
                                        :max="99"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧模板列表 -->
                    <div class="flex-1 pl-4">
                        <div class="template-header">
                            <el-icon><DocumentCopy /></el-icon>
                            <span>文档模板</span>
                            <span class="template-count" v-if="filteredTemplates.length">
                                ({{ filteredTemplates.length }} 个)
                            </span>
                        </div>
                        
                        <div v-if="loading" class="loading-container">
                            <el-icon class="is-loading loading-icon"><Loading /></el-icon>
                            <div class="loading-text">加载中...</div>
                        </div>
                        
                        <div v-else-if="filteredTemplates.length === 0" class="empty-container">
                            <el-icon class="empty-icon"><DocumentDelete /></el-icon>
                            <div class="empty-text">
                                {{ searchKeyword ? '未找到匹配的模板' : '暂无模板' }}
                            </div>
                        </div>
                        
                        <div v-else class="template-list">
                            <div
                                v-for="template in filteredTemplates"
                                :key="template.id"
                                :class="[
                                    'template-item',
                                    selectedTemplateId === template.id ? 'template-selected' : ''
                                ]"
                                @click="selectTemplate(template)"
                            >
                                <div class="template-header-info">
                                    <div class="template-title">
                                        <el-icon class="title-icon"><Document /></el-icon>
                                        {{ template.name }}
                                    </div>
                                    <div class="template-actions">
                                        <el-icon 
                                            v-if="selectedTemplateId === template.id" 
                                            class="selected-icon"
                                        >
                                            <CircleCheck />
                                        </el-icon>
                                    </div>
                                </div>
                                
                                <div v-if="template.description" class="template-description">
                                    {{ template.description }}
                                </div>
                                
                                <div class="template-info">
                                    <div class="info-row">
                                        <div class="info-item">
                                            <el-icon><Files /></el-icon>
                                            <span>文件类型：{{ template.file_type || '未知' }}</span>
                                        </div>
                                        <div class="info-item">
                                            <el-icon><DataLine /></el-icon>
                                            <span>文件大小：{{ template.file_size || '未知' }}</span>
                                        </div>
                                    </div>
                                    <div class="info-row">
                                        <div class="info-item">
                                            <el-icon><Download /></el-icon>
                                            <span>下载次数：{{ template.download_count || 0 }}</span>
                                        </div>
                                        <div class="download-action">
                                            <el-button 
                                                type="primary" 
                                                size="small"
                                                :icon="Download"
                                                @click.stop="downloadTemplate(template)"
                                                :loading="downloadingId === template.id"
                                            >
                                                {{ downloadingId === template.id ? '下载中' : '立即下载' }}
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <div class="footer-info">
                        <span v-if="selectedTemplate" class="selected-info">
                            <el-icon><InfoFilled /></el-icon>
                            已选择：{{ selectedTemplate.name }}
                        </span>
                    </div>
                    <div class="footer-actions">
                        <el-button @click="showDialog = false" size="large">取消</el-button>
                        <el-button 
                            type="primary" 
                            @click="confirmSelect"
                            :disabled="!selectedTemplate"
                            size="large"
                            class="confirm-btn"
                        >
                            <el-icon><Check /></el-icon>
                            确定选择
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
    Download, Loading, Search, FolderOpened, DocumentCopy, 
    CircleCheck, Check, ArrowRight, DocumentDelete,
    InfoFilled, Document, Files, DataLine
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Template {
    id: number
    name: string
    description?: string
    download_url: string
    file_size?: string
    file_type?: string
    download_count?: number
}

interface Category {
    id: number
    name: string
    templates: Template[]
}

const emit = defineEmits<{
    (event: 'select', template: Template): void
    (event: 'download', template: Template): void
}>()

const showDialog = ref(false)
const loading = ref(false)
const downloadingId = ref<number | null>(null)
const searchKeyword = ref('')
const categories = ref<Category[]>([])
const selectedCategoryId = ref<number | null>(null)
const selectedTemplateId = ref<number | null>(null)
const selectedTemplate = ref<Template | null>(null)

// 当前选中分类的模板列表
const currentTemplates = computed(() => {
    if (!selectedCategoryId.value) return []
    const category = categories.value.find(cat => cat.id === selectedCategoryId.value)
    return category?.templates || []
})

// 过滤后的模板列表（支持搜索）
const filteredTemplates = computed(() => {
    let templates = currentTemplates.value
    if (searchKeyword.value.trim()) {
        const keyword = searchKeyword.value.toLowerCase()
        templates = templates.filter(template => 
            template.name.toLowerCase().includes(keyword) ||
            (template.description && template.description.toLowerCase().includes(keyword))
        )
    }
    return templates
})

// 获取分类下的模板数量
const getCategoryTemplateCount = (categoryId: number) => {
    const category = categories.value.find(cat => cat.id === categoryId)
    return category?.templates?.length || 0
}

// 选择分类
const selectCategory = (categoryId: number) => {
    selectedCategoryId.value = categoryId
    selectedTemplateId.value = null
    selectedTemplate.value = null
    searchKeyword.value = '' // 清空搜索
}

// 选择模板
const selectTemplate = (template: Template) => {
    selectedTemplateId.value = template.id
    selectedTemplate.value = template
}

// 清除选择
const clearSelection = () => {
    selectedTemplateId.value = null
    selectedTemplate.value = null
}

// 下载模板
const downloadTemplate = async (template: Template) => {
    try {
        downloadingId.value = template.id
        
        // 创建下载链接
        const link = document.createElement('a')
        link.href = template.download_url
        link.download = template.name
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        emit('download', template)
        ElMessage.success('模板下载成功')
    } catch (error) {
        console.error('下载模板失败:', error)
        ElMessage.error('下载模板失败')
    } finally {
        downloadingId.value = null
    }
}

// 确认选择
const confirmSelect = () => {
    if (selectedTemplate.value) {
        emit('select', selectedTemplate.value)
        showDialog.value = false
        ElMessage.success({
            message: '模板选择成功',
            type: 'success',
            duration: 2000
        })
    }
}

// 获取模板数据 - 这里需要根据实际API调整
const fetchTemplates = async () => {
    try {
        loading.value = true
        // 模拟数据，实际应该调用API
        const mockData = [
            {
                id: 1,
                name: '通用文档',
                templates: [
                    {
                        id: 1,
                        name: '产品说明书模板.docx',
                        description: '适用于各类产品说明书的标准模板',
                        download_url: '/templates/product-manual.docx',
                        file_size: '2.5MB',
                        file_type: 'DOCX',
                        download_count: 156
                    },
                    {
                        id: 2,
                        name: '用户手册模板.pdf',
                        description: '详细的用户操作手册模板',
                        download_url: '/templates/user-manual.pdf',
                        file_size: '1.8MB',
                        file_type: 'PDF',
                        download_count: 89
                    }
                ]
            },
            {
                id: 2,
                name: '技术文档',
                templates: [
                    {
                        id: 3,
                        name: 'API文档模板.md',
                        description: 'RESTful API接口文档标准模板',
                        download_url: '/templates/api-doc.md',
                        file_size: '156KB',
                        file_type: 'Markdown',
                        download_count: 234
                    }
                ]
            }
        ]
        
        categories.value = mockData
        
        // 默认选中第一个分类
        if (categories.value.length > 0) {
            selectedCategoryId.value = categories.value[0].id
        }
    } catch (error) {
        console.error('获取模板失败:', error)
        ElMessage.error('获取模板失败')
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    fetchTemplates()
})
</script>

<style scoped>
.template-selector {
    display: inline-block;
}

.template-selector-btn {
    background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.template-selector-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(230, 162, 60, 0.4);
}

.btn-text {
    margin-right: 4px;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.template-selector-btn:hover .btn-icon {
    transform: translateX(2px);
}

.selected-indicator {
    margin-top: 8px;
}

.template-dialog :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
    padding: 20px 24px;
    border-radius: 8px 8px 0 0;
}

.template-dialog :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.dialog-content {
    padding: 0;
}

.search-bar {
    padding: 16px 24px 0;
}

.search-input {
    width: 100%;
}

.search-input :deep(.el-input__wrapper) {
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-header, .template-header {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    padding: 8px 0;
}

.category-header i, .template-header i {
    margin-right: 6px;
    color: #e6a23c;
}

.template-count {
    color: #909399;
    font-weight: normal;
    margin-left: 4px;
}

.category-list {
    max-height: 440px;
    overflow-y: auto;
}

.category-item {
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-item:hover {
    background: #fff7ed;
    border-color: #fed7aa;
}

.category-item.category-active {
    background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);
    color: white;
    border-color: #e6a23c;
    box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.category-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-name {
    font-weight: 500;
}

.category-badge :deep(.el-badge__content) {
    background: #f56c6c;
    border: none;
}

.category-active .category-badge :deep(.el-badge__content) {
    background: rgba(255, 255, 255, 0.9);
    color: #e6a23c;
}

.loading-container, .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #909399;
}

.loading-icon {
    font-size: 32px;
    color: #e6a23c;
    margin-bottom: 12px;
}

.loading-text {
    font-size: 14px;
}

.empty-icon {
    font-size: 48px;
    color: #c0c4cc;
    margin-bottom: 12px;
}

.empty-text {
    font-size: 14px;
    color: #909399;
}

.template-list {
    max-height: 440px;
    overflow-y: auto;
    padding-right: 8px;
}

.template-item {
    border: 2px solid #e4e7ed;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    position: relative;
    overflow: hidden;
}

.template-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    transition: all 0.3s ease;
}

.template-item:hover {
    border-color: #fed7aa;
    box-shadow: 0 4px 16px rgba(230, 162, 60, 0.15);
    transform: translateY(-2px);
}

.template-item:hover::before {
    background: #e6a23c;
}

.template-item.template-selected {
    border-color: #e6a23c;
    background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);
    box-shadow: 0 4px 20px rgba(230, 162, 60, 0.25);
}

.template-item.template-selected::before {
    background: #e6a23c;
}

.template-header-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.template-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 15px;
    color: #2c3e50;
}

.title-icon {
    margin-right: 6px;
    color: #f39c12;
}

.selected-icon {
    color: #67c23a;
    font-size: 18px;
}

.template-description {
    font-size: 13px;
    color: #606266;
    margin-bottom: 12px;
    line-height: 1.5;
}

.template-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
}

.info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #606266;
}

.info-item i {
    margin-right: 4px;
    color: #909399;
}

.download-action {
    margin-left: auto;
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.footer-info {
    flex: 1;
}

.selected-info {
    display: flex;
    align-items: center;
    color: #e6a23c;
    font-size: 14px;
    font-weight: 500;
}

.selected-info i {
    margin-right: 6px;
}

.footer-actions {
    display: flex;
    gap: 12px;
}

.confirm-btn {
    background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);
    border: none;
    padding: 10px 24px;
    font-weight: 500;
}

.confirm-btn:hover {
    background: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%);
}

/* 滚动条样式 */
.category-list::-webkit-scrollbar,
.template-list::-webkit-scrollbar {
    width: 6px;
}

.category-list::-webkit-scrollbar-track,
.template-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.category-list::-webkit-scrollbar-thumb,
.template-list::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
}

.category-list::-webkit-scrollbar-thumb:hover,
.template-list::-webkit-scrollbar-thumb:hover {
    background: #909399;
}
</style> 