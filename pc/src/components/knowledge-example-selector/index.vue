<template>
    <div class="knowledge-example-selector">
        <el-button 
            type="success" 
            :icon="DocumentAdd" 
            @click="showDialog = true"
            size="small"
            class="example-selector-btn"
        >
            <span class="btn-text">选择示例模板</span>
            <el-icon class="btn-icon"><ArrowRight /></el-icon>
        </el-button>
        
        <!-- 选择状态显示 -->
        <div v-if="selectedExample" class="selected-indicator">
            <el-tag type="success" size="small" closable @close="clearSelection">
                <el-icon><Check /></el-icon>
                已选择：{{ selectedExample.title }}
            </el-tag>
        </div>
        
        <el-dialog
            v-model="showDialog"
            title="选择知识库示例"
            width="1000px"
            :close-on-click-modal="false"
            class="example-dialog"
        >
            <div class="dialog-content">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <el-input
                        v-model="searchKeyword"
                        placeholder="搜索示例标题、问题或答案..."
                        :prefix-icon="Search"
                        clearable
                        class="search-input"
                    />
                </div>
                
                <div class="flex h-[560px] mt-4">
                    <!-- 左侧分类列表 -->
                    <div class="w-[240px] border-r border-gray-200 pr-4">
                        <div class="category-header">
                            <el-icon><Collection /></el-icon>
                            <span>示例分类</span>
                        </div>
                        <div class="category-list">
                            <div
                                v-for="category in categories"
                                :key="category.id"
                                :class="[
                                    'category-item',
                                    selectedCategoryId === category.id ? 'category-active' : ''
                                ]"
                                @click="selectCategory(category.id)"
                            >
                                <div class="category-content">
                                    <span class="category-name">{{ category.name }}</span>
                                    <el-badge 
                                        :value="getCategoryExampleCount(category.id)" 
                                        class="category-badge"
                                        :max="99"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧示例列表 -->
                    <div class="flex-1 pl-4">
                        <div class="example-header">
                            <el-icon><ChatDotRound /></el-icon>
                            <span>知识库示例</span>
                            <span class="example-count" v-if="filteredExamples.length">
                                ({{ filteredExamples.length }} 个)
                            </span>
                        </div>
                        
                        <div v-if="loading" class="loading-container">
                            <el-icon class="is-loading loading-icon"><Loading /></el-icon>
                            <div class="loading-text">加载中...</div>
                        </div>
                        
                        <div v-else-if="filteredExamples.length === 0" class="empty-container">
                            <el-icon class="empty-icon"><DocumentDelete /></el-icon>
                            <div class="empty-text">
                                {{ searchKeyword ? '未找到匹配的示例' : '暂无示例' }}
                            </div>
                        </div>
                        
                        <div v-else class="example-list">
                            <div
                                v-for="example in filteredExamples"
                                :key="example.id"
                                :class="[
                                    'example-item',
                                    selectedExampleId === example.id ? 'example-selected' : ''
                                ]"
                                @click="selectExample(example)"
                            >
                                <div class="example-header-info">
                                    <div class="example-title">
                                        <el-icon class="title-icon"><ChatLineRound /></el-icon>
                                        {{ example.title }}
                                    </div>
                                    <div class="example-actions">
                                        <el-icon 
                                            v-if="selectedExampleId === example.id" 
                                            class="selected-icon"
                                        >
                                            <CircleCheck />
                                        </el-icon>
                                    </div>
                                </div>
                                
                                <div class="qa-section">
                                    <div class="question-section">
                                        <div class="qa-label">
                                            <el-icon><QuestionFilled /></el-icon>
                                            <span>问题</span>
                                        </div>
                                        <div class="qa-content question-content">
                                            {{ example.question }}
                                        </div>
                                    </div>
                                    
                                    <div class="answer-section">
                                        <div class="qa-label">
                                            <el-icon><ChatDotSquare /></el-icon>
                                            <span>答案</span>
                                        </div>
                                        <div class="qa-content answer-content">
                                            {{ example.answer }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="example-meta">
                                    <span class="meta-item">
                                        问题：{{ example.question.length }} 字符
                                    </span>
                                    <span class="meta-item">
                                        答案：{{ example.answer.length }} 字符
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <div class="footer-info">
                        <span v-if="selectedExample" class="selected-info">
                            <el-icon><InfoFilled /></el-icon>
                            已选择：{{ selectedExample.title }}
                        </span>
                    </div>
                    <div class="footer-actions">
                        <el-button @click="showDialog = false" size="large">取消</el-button>
                        <el-button 
                            type="primary" 
                            @click="confirmSelect"
                            :disabled="!selectedExample"
                            size="large"
                            class="confirm-btn"
                        >
                            <el-icon><Check /></el-icon>
                            确定选择
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
    DocumentAdd, Loading, Search, Collection, ChatDotRound, 
    CircleCheck, Check, ArrowRight, DocumentDelete,
    InfoFilled, ChatLineRound, QuestionFilled, ChatDotSquare
} from '@element-plus/icons-vue'
import { getAllExamples } from '@/api/my_database'
import { ElMessage } from 'element-plus'

interface KnowledgeExample {
    id: number
    title: string
    question: string
    answer: string
}

interface Category {
    id: number
    name: string
    examples: KnowledgeExample[]
}

const emit = defineEmits<{
    (event: 'select', example: KnowledgeExample): void
}>()

const showDialog = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const categories = ref<Category[]>([])
const selectedCategoryId = ref<number | null>(null)
const selectedExampleId = ref<number | null>(null)
const selectedExample = ref<KnowledgeExample | null>(null)

// 当前选中分类的示例列表
const currentExamples = computed(() => {
    if (!selectedCategoryId.value) return []
    const category = categories.value.find(cat => cat.id === selectedCategoryId.value)
    return category?.examples || []
})

// 过滤后的示例列表（支持搜索）
const filteredExamples = computed(() => {
    let examples = currentExamples.value
    if (searchKeyword.value.trim()) {
        const keyword = searchKeyword.value.toLowerCase()
        examples = examples.filter(example => 
            example.title.toLowerCase().includes(keyword) ||
            example.question.toLowerCase().includes(keyword) ||
            example.answer.toLowerCase().includes(keyword)
        )
    }
    return examples
})

// 获取分类下的示例数量
const getCategoryExampleCount = (categoryId: number) => {
    const category = categories.value.find(cat => cat.id === categoryId)
    return category?.examples?.length || 0
}

// 选择分类
const selectCategory = (categoryId: number) => {
    selectedCategoryId.value = categoryId
    selectedExampleId.value = null
    selectedExample.value = null
    searchKeyword.value = '' // 清空搜索
}

// 选择示例
const selectExample = (example: KnowledgeExample) => {
    selectedExampleId.value = example.id
    selectedExample.value = example
}

// 清除选择
const clearSelection = () => {
    selectedExampleId.value = null
    selectedExample.value = null
}

// 确认选择
const confirmSelect = () => {
    if (selectedExample.value) {
        emit('select', selectedExample.value)
        showDialog.value = false
        ElMessage.success({
            message: '知识库示例选择成功',
            type: 'success',
            duration: 2000
        })
    }
}

// 获取知识库示例数据
const fetchKnowledgeExamples = async () => {
    try {
        loading.value = true
        const data = await getAllExamples()
        categories.value = data || []
        
        // 默认选中第一个分类
        if (categories.value.length > 0) {
            selectedCategoryId.value = categories.value[0].id
        }
    } catch (error) {
        console.error('获取知识库示例失败:', error)
        ElMessage.error('获取知识库示例失败')
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    fetchKnowledgeExamples()
})
</script>

<style scoped>
.knowledge-example-selector {
    display: inline-block;
}

.example-selector-btn {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.example-selector-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(103, 194, 58, 0.4);
}

.btn-text {
    margin-right: 4px;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.example-selector-btn:hover .btn-icon {
    transform: translateX(2px);
}

.selected-indicator {
    margin-top: 8px;
}

.example-dialog :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    padding: 20px 24px;
    border-radius: 8px 8px 0 0;
}

.example-dialog :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.dialog-content {
    padding: 0;
}

.search-bar {
    padding: 16px 24px 0;
}

.search-input {
    width: 100%;
}

.search-input :deep(.el-input__wrapper) {
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-header, .example-header {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    padding: 8px 0;
}

.category-header i, .example-header i {
    margin-right: 6px;
    color: #67c23a;
}

.example-count {
    color: #909399;
    font-weight: normal;
    margin-left: 4px;
}

.category-list {
    max-height: 480px;
    overflow-y: auto;
}

.category-item {
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-item:hover {
    background: #f0f9ff;
    border-color: #e1e6ff;
}

.category-item.category-active {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    color: white;
    border-color: #67c23a;
    box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.category-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-name {
    font-weight: 500;
}

.category-badge :deep(.el-badge__content) {
    background: #f56c6c;
    border: none;
}

.category-active .category-badge :deep(.el-badge__content) {
    background: rgba(255, 255, 255, 0.9);
    color: #67c23a;
}

.loading-container, .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #909399;
}

.loading-icon {
    font-size: 32px;
    color: #67c23a;
    margin-bottom: 12px;
}

.loading-text {
    font-size: 14px;
}

.empty-icon {
    font-size: 48px;
    color: #c0c4cc;
    margin-bottom: 12px;
}

.empty-text {
    font-size: 14px;
    color: #909399;
}

.example-list {
    max-height: 480px;
    overflow-y: auto;
    padding-right: 8px;
}

.example-item {
    border: 2px solid #e4e7ed;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    position: relative;
    overflow: hidden;
}

.example-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    transition: all 0.3s ease;
}

.example-item:hover {
    border-color: #c6d1f0;
    box-shadow: 0 4px 16px rgba(103, 194, 58, 0.15);
    transform: translateY(-2px);
}

.example-item:hover::before {
    background: #67c23a;
}

.example-item.example-selected {
    border-color: #67c23a;
    background: linear-gradient(135deg, #f0f9ff 0%, #e8f5e8 100%);
    box-shadow: 0 4px 20px rgba(103, 194, 58, 0.25);
}

.example-item.example-selected::before {
    background: #67c23a;
}

.example-header-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.example-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 15px;
    color: #2c3e50;
}

.title-icon {
    margin-right: 6px;
    color: #409eff;
}

.selected-icon {
    color: #67c23a;
    font-size: 18px;
}

.qa-section {
    margin-bottom: 12px;
}

.question-section, .answer-section {
    margin-bottom: 12px;
}

.qa-label {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 600;
    color: #606266;
    margin-bottom: 6px;
}

.qa-label i {
    margin-right: 4px;
}

.qa-content {
    font-size: 13px;
    line-height: 1.5;
    padding: 8px 12px;
    border-radius: 6px;
    max-height: 60px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.question-content {
    background: #f0f9ff;
    color: #1d4ed8;
    border-left: 3px solid #3b82f6;
}

.answer-content {
    background: #f0fdf4;
    color: #166534;
    border-left: 3px solid #22c55e;
}

.example-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
}

.meta-item {
    font-size: 11px;
    color: #c0c4cc;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 4px;
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.footer-info {
    flex: 1;
}

.selected-info {
    display: flex;
    align-items: center;
    color: #67c23a;
    font-size: 14px;
    font-weight: 500;
}

.selected-info i {
    margin-right: 6px;
}

.footer-actions {
    display: flex;
    gap: 12px;
}

.confirm-btn {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border: none;
    padding: 10px 24px;
    font-weight: 500;
}

.confirm-btn:hover {
    background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
}

/* 滚动条样式 */
.category-list::-webkit-scrollbar,
.example-list::-webkit-scrollbar {
    width: 6px;
}

.category-list::-webkit-scrollbar-track,
.example-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.category-list::-webkit-scrollbar-thumb,
.example-list::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
}

.category-list::-webkit-scrollbar-thumb:hover,
.example-list::-webkit-scrollbar-thumb:hover {
    background: #909399;
}
</style> 