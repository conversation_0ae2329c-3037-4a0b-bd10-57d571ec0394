:root {
    // 弹窗居中
    .el-overlay-dialog {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100%;
        position: static;

        .el-dialog {
            --el-dialog-content-font-size: var(--el-font-size-base);
            --el-dialog-margin-top: 50px;
            max-width: calc(100vw - 30px);
            flex: none;
            display: flex;
            flex-direction: column;
            border-radius: 12px;

            &.body-padding .el-dialog__body {
                padding: 0;
            }

            .el-dialog__body {
                flex: 1;
                padding: 15px 20px;
            }
            .el-dialog__header {
                font-size: var(--el-font-size-large);
            }
        }
    }

    .el-drawer {
        --el-drawer-padding-primary: 16px;
        &__header {
            margin-bottom: 0;
            padding: 13px 16px;
            border-bottom: 1px solid var(--el-border-color-lighter);
        }
        &__title {
            @apply text-tx-primary;
        }
    }

    .el-table {
        --el-table-header-text-color: var(--el-text-color-primary);
        --el-table-header-bg-color: var(--table-header-bg-color);
        font-size: var(--el-font-size-base);

        thead {
            th:first-child {
                border-radius: 12px 0 0 12px;
            }
            th:last-child {
                border-radius: 0 12px 12px 0;
            }
            th {
                font-weight: 400;
                border: none !important;
            }
        }

        .el-table__body-wrapper {
            margin-top: 10px;
        }
    }

    .el-popper {
        border: none !important;
        border-radius: 12px !important;
        box-shadow: 0 0 16px 0 rgba(0, 108, 255, 0.102) !important;
    }

    .el-message-box {
        --el-messagebox-border-radius: 8px;
    }

    .el-button {
        --el-border-radius-base: 8px;
    }

    .el-input__wrapper {
        --el-border-radius-base: 8px;
    }

    .el-input-number__increase,
    .el-input-number__decrease {
        --el-border-radius-base: 8px;
    }

    .el-input,
    .el-textarea {
        --el-border-radius-base: 8px;
    }

    .el-input-group__prepend {
        background-color: var(--el-fill-color-blank);
    }

    .el-select__wrapper {
        --el-border-radius-base: 8px;
    }

    .el-checkbox {
        --el-checkbox-border-radius: 4px;
        --el-checkbox-font-size: var(--el-font-size-base);
    }

    .el-menu--popup-container {
        &.theme-light {
            .el-menu {
                .el-menu-item {
                    &.is-active {
                        @apply bg-primary-light-9 border-primary border-r-2;
                    }
                }
                .el-menu-item:hover,
                .el-sub-menu__title:hover {
                    color: var(--el-color-primary);
                }
            }
        }
        &.theme-dark {
            .el-menu {
                .el-menu-item {
                    &.is-active {
                        @apply bg-primary;
                    }
                }
            }
        }
    }

    .el-message-box {
        --el-messagebox-width: 350px;
    }
    .el-date-editor {
        --el-date-editor-datetimerange-width: 380px;
        .el-range-input {
            font-size: var(--el-font-size-small);
        }
    }

    .el-button--primary {
        --el-button-hover-link-text-color: var(--el-color-primary-light-3);
    }
    .el-button--success {
        --el-button-hover-link-text-color: var(--el-color-success-light-3);
    }
    .el-button--info {
        --el-button-hover-link-text-color: var(--el-color-info-light-3);
    }
    .el-button--warning {
        --el-button-hover-link-text-color: var(--el-color-warning-light-3);
    }
    .el-button--danger {
        --el-button-hover-link-text-color: var(--el-color-danger-light-3);
    }
    .el-image__error {
        font-size: 12px;
    }
    .el-tabs__nav-wrap::after {
        height: 1px;
    }
    .el-page-header {
        &__breadcrumb {
            margin-bottom: 0;
        }
    }
    .el-button {
        // 防止被tailwindcss默认样式覆盖
        background-color: var(--el-button-bg-color, var(--el-color-white));

        //覆盖el-button的点击样式
        &:focus {
            color: var(--el-button-text-color);
            border-color: var(--el-button-border-color);
            background-color: var(--el-button-bg-color);
        }
        &:hover {
            color: var(--el-button-hover-text-color);
            border-color: var(--el-button-hover-border-color);
            background-color: var(--el-button-hover-bg-color);
        }
    }

    .the-chat-msg-collapse {
        --el-collapse-header-bg-color: var(--el-bg-color);
        --el-border-color-lighter: transparent;
        --el-collapse-content-bg-color: transparent;

        .el-collapse-item__header {
            border-radius: 12px;
            padding: 0 14px;
            margin-bottom: 10px;
        }
    }
}
@media (max-width: 768px) {
    .el-pagination > .el-pagination__jump {
        display: none !important;
    }
    .el-pagination > .el-pagination__sizes {
        display: none !important;
    }
}

.dark {
    .el-pagination.is-background {
        .btn-prev:disabled,
        .btn-next:disabled {
            --el-text-color-placeholder: #8d9095;
            --el-disabled-bg-color: #262727;
        }
    }
}
