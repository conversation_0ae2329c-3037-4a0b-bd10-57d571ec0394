body {
    @apply text-base text-tx-primary overflow-hidden overflow-x-auto;
}
body,
html {
    *:focus-visible {
        outline: none;
    }
}
.form-tips {
    @apply text-tx-secondary text-xs leading-6 mt-1;
}

.text-plain {
    background: linear-gradient(90deg, #54c6ee 0%, #3c5efd 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.clearfix:after {
    content: '';
    display: block;
    clear: both;
    visibility: hidden;
}

/* NProgress */
#nprogress .bar {
    @apply bg-primary #{!important};
}

.text_hidden {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.updownAnimation {
    animation-name: shake;
    animation-duration: 3s;
    animation-iteration-count: infinite;
}
@keyframes shake {
    0% {
        transform: translateY(0); /*开始为原始大小*/
    }
    25% {
        transform: translateY(-20%); /*放大1.1倍*/
    }
    50% {
        transform: translateY(0);
    }
    75% {
        transform: translateY(-20%);
    }
}

.hover-to-right {
    .target {
        transition: transform 0.3s;
    }
    &:hover {
        .target {
            transform: translateX(10px);
        }
    }
}

.el-radio-group-margin {
    &.el-radio-group {
        margin: 0 -5px;
        .el-radio-button {
            padding: 5px;
            &.is-active {
                .el-radio-button__inner {
                    @apply bg-primary-light-9 text-primary border-primary;
                }
            }
            .el-radio-button__inner {
                border: var(--el-border);
                border-radius: var(--el-border-radius-base);
                box-shadow: none;
            }
        }
    }
}

/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
// 滚动条整体部分
body::-webkit-scrollbar {
    width: 6px;
    height: 8px;
}
// 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
body::-webkit-scrollbar-button {
    display: none;
}
// 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
body::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    //@apply bg-primary #{!important};
    cursor: pointer;
    border-radius: 4px;
}
// 边角，即两个滚动条的交汇处
body::-webkit-scrollbar-corner {
    display: none;
}
// 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
body::-webkit-resizer {
    display: none;
}

/*滚动条整体样式*/
.scrollbar::-webkit-scrollbar {
    width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 4px;
}
.scrollbar::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(144, 147, 153, 0.3);
}
.scrollbar::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: #ffffff;
}

.text-hidden-2 {
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}


/*  TinyMCE Style  */
button[data-mce-name = 'h1'],
button[data-mce-name = 'h2'],
button[data-mce-name = 'h3'] {
    width: auto;
    font-size: 16px !important;
}


/*  自定义 element plus 的 popove 动画 */
.custom-popover-enter-active,
.custom-popover-leave-active {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.custom-popover-enter-from,
.custom-popover-leave-to {
    opacity: 0;
    transform: scale(0.95);
}

.custom-popover-enter-to,
.custom-popover-leave-from {
    opacity: 1;
    transform: scale(1);
}