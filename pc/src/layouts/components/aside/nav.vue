<template>
    <div class="menu">
        <el-menu :default-active="getActiveMenuKey()">
            <menu-item
                v-for="item in navList"
                :key="item.id"
                :item="item"
                :showName="true"
                :is-show-icon="isShowIcon"
                :path="getMenuPath(item)"
                :is-active="isMenuActive(item)"
            />
        </el-menu>
    </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import MenuItem from './menu-item.vue'

interface Props {
    isHome?: boolean
}

const props = defineProps<Props>()

const appStore = useAppStore()
const navList = computed(() => {
    return (
        appStore.pageAside.nav?.filter((item: any) => Number(item.is_show) === 1) ||
        []
    )
})
const isShowIcon = computed(() => {
    return appStore.pageAside.showNavIcon
})
const route = useRoute()
const activeMenu = computed<string>(() => {
    const routePath =
        route.path === '/' ? route.path : route.path.replace(/\/$/, '')
    return route.meta.parentPath || route.meta.activePath || routePath
})

// 获取菜单路径（处理完整URL）
const getMenuPath = (item: any): string => {
    const menuPath = item.link.path
    if (menuPath.startsWith('http://') || menuPath.startsWith('https://')) {
        try {
            const url = new URL(menuPath)
            return url.pathname
        } catch (e) {
            console.warn('无法解析菜单URL:', menuPath)
            return menuPath
        }
    }
    return menuPath
}

// 判断菜单项是否激活
const isMenuActive = (item: any): boolean => {
    const currentPath = activeMenu.value
    const extractedPath = getMenuPath(item)
    
    // 精确匹配
    if (currentPath === extractedPath) {
        return true
    }
    
    // 前缀匹配（支持子路径）
    if (currentPath.startsWith(extractedPath + '/')) {
        return true
    }
    
    return false
}

// 获取激活的菜单key
const getActiveMenuKey = (): string => {
    const currentPath = activeMenu.value
    
    // 遍历所有菜单，找到匹配的菜单路径
    for (const item of navList.value) {
        const menuPath = getMenuPath(item)
        if (currentPath === menuPath || currentPath.startsWith(menuPath + '/')) {
            return menuPath
        }
    }
    
    return currentPath
}
</script>

<style lang="scss" scoped>
.menu {
    :deep() {
        .el-menu {
            border-right: none;
            --el-menu-bg-color: transparent;
            --el-menu-text-color: var(--el-text-color-primary);
            .el-menu-item {
                flex-direction: column;
                line-height: 20px;
                justify-content: center;
                align-items: center;
                padding: 0 12px;
                [class^='el-icon'] {
                    margin-right: 0;
                }
                margin: 18px 0;
                &:hover {
                    background: transparent;
                    @apply text-primary;
                }
                .menu-icon {
                    @apply text-tx-secondary;
                }
                &.is-active {
                    background: linear-gradient(90deg, #70c3ec 0%, #4A92FF 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    .menu-icon {
                        @apply text-primary;
                    }
                }
            }
        }
    }
}
</style>
