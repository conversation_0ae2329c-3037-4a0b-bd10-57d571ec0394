<template>
  <div class="setting-drawer">
    <el-drawer
      v-model="showSetting"
      append-to-body
      direction="rtl"
      size="250px"
      class="setting-drawer"
      title="主题设置"
    >
      <div class="setting-item mb-5">
        <span class="text-tx-secondary">风格设置</span>
        <div class="flex mt-4 cursor-pointer">
          <div
            v-for="item in sideThemeList"
            :key="item.type"
            class="mr-4 flex relative text-primary"
            @click="sideTheme = item.type"
          >
            <img :src="item.image" width="52" height="36" />
            <Icon
              v-if="sideTheme == item.type"
              class="icon-select"
              name="el-icon-Select"
            />
          </div>
        </div>
      </div>
      <div class="setting-item mb-5 flex justify-between items-center">
        <span class="text-tx-secondary">主题颜色</span>
        <div>
          <el-color-picker v-model="theme" :predefine="predefineColors" />
        </div>
      </div>
      <!-- <div class="setting-item mb-5 flex justify-between items-center">
        <span class="text-tx-secondary">开启黑暗模式</span>
        <div>
          <el-switch :model-value="isDark" @change="toggleDark" />
        </div>
      </div>
      <div class="setting-item mb-5 flex justify-between items-center">
        <div class="text-tx-secondary flex-none mr-3">显示LOGO</div>
        <div>
          <el-switch
            v-model="showLogo"
            :active-value="true"
            :inactive-value="false"
          />
        </div>
      </div> -->

      <div class="setting-item mb-5 flex justify-between items-center">
        <el-button @click="resetTheme">重置主题</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { useDark, useToggle } from '@vueuse/core'
import { useSettingStore } from '@/stores/setting'
import theme_light from '@/assets/image/theme_white.png'
import theme_dark from '@/assets/image/theme_black.png'

const settingStore = useSettingStore()
const predefineColors = ref([
  '#409EFF',
  '#28C76F',
  '#EA5455',
  '#FF9F43',
  '#01CFE8',
  '#4A5DFF'
])
const sideThemeList = [
  {
    type: 'dark',
    image: theme_dark
  },
  {
    type: 'light',
    image: theme_light
  }
]

const sideTheme = computed({
  get() {
    return settingStore.sideTheme
  },
  set(value) {
    settingStore.setSetting({
      key: 'sideTheme',
      value
    })
  }
})

const showSetting = computed({
  get() {
    return settingStore.showDrawer
  },
  set(value) {
    settingStore.setSetting({
      key: 'showDrawer',
      value
    })
  }
})
const theme = computed({
  get() {
    return settingStore.theme
  },
  set(value) {
    settingStore.setSetting({
      key: 'theme',
      value
    })
    themeChange()
  }
})

const showLogo = computed({
  get() {
    return settingStore.showLogo
  },
  set(value) {
    settingStore.setSetting({
      key: 'showLogo',
      value
    })
  }
})

const isDark = useDark()
const themeChange = () => {
  settingStore.setTheme(isDark.value)
}

const toggleDark = () => {
  useToggle(isDark)()
  themeChange()
}
const resetTheme = () => {
  isDark.value = false
  settingStore.resetTheme()
  themeChange()
}
</script>

<style lang="scss">
.setting-drawer {
  .icon-select {
    @apply absolute left-1/2 top-1/2;
    transform: translate(-50%, -50%);
  }
}
</style>
