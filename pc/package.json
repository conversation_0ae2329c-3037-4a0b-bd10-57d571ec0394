{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt generate --dotenv .env.production && node scripts/release.mjs", "dev": "nuxt dev", "start": "nuxt start", "build:ssr": "nuxt build --dotenv .env.production && node scripts/release.mjs", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint --ext .ts,.tsx,.js,.jsx,.vue src", "prettier": "prettier --write ./src"}, "devDependencies": {"@element-plus/nuxt": "^1.0.9", "@nuxtjs/tailwindcss": "6.8.0", "@pinia/nuxt": "0.4.11", "@types/fabric": "5.3.5", "@types/markdown-it": "12.2.3", "@types/node": "18", "@types/sortablejs": "1.15.2", "@typescript-eslint/eslint-plugin": "6.9.0", "@vue/eslint-config-prettier": "7.1.0", "@vue/eslint-config-typescript": "12.0.0", "eslint": "8.41.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-nuxt": "4.0.0", "eslint-plugin-vue": "9.18.1", "nuxt": "3.12.4", "nuxt-lodash": "2.5.0", "prettier": "2.8.8", "prettier-eslint": "^16.3.0", "sass": "1.62.1", "typescript": "4.9.3", "vite-plugin-svg-icons": "2.0.1"}, "dependencies": {"@chenfengyuan/vue-countdown": "2.1.1", "@fingerprintjs/fingerprintjs": "3.4.2", "@tanstack/vue-query": "4.32.0", "@tinymce/tinymce-vue": "^6.0.1", "@videojs-player/vue": "^1.0.0", "@vscode/markdown-it-katex": "^1.1.0", "css-color-function": "1.3.3", "domino-ext": "^2.1.4", "element-plus": "2.7.3", "fabric": "5.3.0", "fontfaceobserver": "2.3.0", "github-markdown-css": "5.2.0", "highlight.js": "11.8.0", "ismobilejs": "1.1.1", "joplin-turndown-plugin-gfm": "^1.0.12", "jsonc-parser": "3.2.0", "mammoth": "1.6.0", "markdown-it": "13.0.1", "markmap-common": "^0.17.0", "markmap-lib": "^0.17.0", "markmap-toolbar": "^0.17.0", "markmap-view": "^0.17.0", "nuxt-icons": "3.2.1", "nuxt-swiper": "1.2.2", "papaparse": "5.4.1", "pdfjs-dist": "2.10.377", "pinia": "2.0.3", "qrcode.vue": "3.4.0", "recorder-core": "^1.3.23122400", "sortablejs": "1.15.0", "swiper": "11.0.3", "tinymce": "^7.2.0", "turndown": "^7.2.0", "vconsole": "^3.15.1", "video.js": "^7.21.6", "vue": "^3.3.4", "vue-cropper": "1.0.5", "vue-qr": "4.0.9", "vuedraggable": "4.1.0", "weixin-js-sdk": "^1.6.5", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.0/xlsx-0.20.0.tgz"}, "packageManager": "pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee"}