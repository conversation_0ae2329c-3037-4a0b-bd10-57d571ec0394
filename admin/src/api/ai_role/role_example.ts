import request from '@/utils/request'

// 角色示例列表
export function roleExampleLists(params: any) {
    return request.get(
        { url: '/kb.role_example/roleExampleLists', params },
        {
            ignoreCancelToken: true
        }
    )
}

// 角色示例详情
export function roleExampleDetail(params: any) {
    return request.get({ url: '/kb.role_example/roleExampleDetail', params })
}

// 新增角色示例
export function addRoleExample(params: any) {
    return request.post({ url: '/kb.role_example/roleExampleAdd', params })
}

// 编辑角色示例
export function editRoleExample(params: any) {
    return request.post({ url: '/kb.role_example/roleExampleEdit', params })
}

// 删除角色示例
export function delRoleExample(params: any) {
    return request.post({ url: '/kb.role_example/roleExampleDel', params })
}

// 更新角色示例状态
export function changeRoleExampleStatus(params: any) {
    return request.post({ url: '/kb.role_example/roleExampleStatus', params })
}

// 获取分类列表
export function getRoleExampleCategoryList() {
    return request.get({ url: '/kb.role_example/roleExampleCategoryList' })
}

// 根据分类ID获取角色示例列表
export function getRoleExampleListByCategoryId(params: any) {
    return request.get({ url: '/kb.role_example/roleExampleListByCategory', params })
}

// 获取所有角色示例（按分类分组）
export function getAllRoleExamples() {
    return request.get({ url: '/kb.role_example/roleExampleAll' })
} 