import request from '@/utils/request'

// 获取示例库内容列表
export function getContentList(params: any) {
    return request.get({ url: '/kb.example/lists', params })
}

// 获取示例库内容详情
export function getContentDetail(params: any) {
    return request.get({ url: '/kb.example/detail', params })
}

// 添加示例库内容
export function addContent(params: any) {
    return request.post({ url: '/kb.example/add', params })
}

// 编辑示例库内容
export function editContent(params: any) {
    return request.post({ url: '/kb.example/edit', params })
}

// 删除示例库内容
export function deleteContent(params: any) {
    return request.post({ url: '/kb.example/del', params })
}

// 修改示例库内容状态
export function updateContentStatus(params: any) {
    return request.post({ url: '/kb.example/status', params })
}

// 根据类别ID获取示例列表
export function getExampleListByCategory(params: any) {
    return request.get({ url: '/kb.example/getListByCategoryId', params })
}

// 获取所有示例
export function getAllExamples() {
    return request.get({ url: '/kb.example/getAllExamples' })
} 