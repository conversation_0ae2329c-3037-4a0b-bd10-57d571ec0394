import request from '@/utils/request'

// 分成收益管理API
export const robotRevenueApi = {
    // 获取分成记录列表
    lists: (params?: any) =>
        request.get({ url: '/kb.robotRevenue/lists', params }),

    // 获取分成收益统计
    statistics: () =>
        request.get({ url: '/kb.robotRevenue/statistics' }),

    // 批量结算分成收益
    batchSettle: () =>
        request.post({ url: '/kb.robotRevenue/batchSettle' }),

    // 获取分成配置
    getConfig: () =>
        request.get({ url: '/kb.robotRevenue/getConfig' }),

    // 设置分成配置
    setConfig: (params: any) =>
        request.post({ url: '/kb.robotRevenue/setConfig', params })
} 