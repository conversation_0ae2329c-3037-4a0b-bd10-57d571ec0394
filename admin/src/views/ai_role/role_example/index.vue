<template>
    <div>
        <el-card shadow="never" class="!border-none mt-4">
            <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
                <el-form-item label="示例标题">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.title"
                        placeholder="请输入示例标题"
                        clearable
                        @keyup.enter="resetPage"
                    />
                </el-form-item>
                <el-form-item label="所属类目">
                    <el-select class="w-[280px]" v-model="queryParams.category_id">
                        <el-option label="全部" value></el-option>
                        <el-option
                            v-for="(item, index) in categoryList"
                            :key="index"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select class="w-[280px]" v-model="queryParams.status">
                        <el-option label="全部" value></el-option>
                        <el-option label="启用" value="1"></el-option>
                        <el-option label="禁用" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        
        <el-card class="!border-none mt-4" shadow="never">
            <div class="mb-4">
                <el-button type="primary" @click="handleAdd">
                    新增角色示例
                </el-button>
                <el-button
                    class="ml-2"
                    type="default"
                    :plain="true"
                    :disabled="!multipleSelection.length"
                    @click="handleDelete(multipleSelection.map((item) => item.id))"
                >
                    批量删除
                </el-button>
            </div>
            
            <el-table
                size="large"
                v-loading="pager.loading"
                :data="pager.lists"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"/>
                <el-table-column label="示例标题" prop="title" min-width="200"/>
                <el-table-column label="所属类目" prop="category_name" min-width="120"/>
                <el-table-column label="角色设定" prop="content" min-width="250" show-overflow-tooltip/>
                <el-table-column label="示例描述" prop="description" min-width="250" show-overflow-tooltip/>
                <el-table-column label="状态" min-width="100">
                    <template #default="{ row }">
                        <el-switch
                            @change="changeStatus(row.id)"
                            v-model="row.status"
                            :active-value="1"
                            :inactive-value="0"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="排序" prop="sort" min-width="80"/>
                <el-table-column label="创建时间" prop="create_time" sortable min-width="180"/>
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="handleEdit(row)">
                            编辑
                        </el-button>
                        <el-button
                            class="ml-2"
                            type="danger"
                            link
                            @click="handleDelete([row.id])"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists"/>
            </div>
        </el-card>

        <!-- 新增/编辑弹窗 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogTitle"
            width="600px"
            :before-close="handleClose"
        >
            <el-form
                ref="formRef"
                :model="formData"
                :rules="formRules"
                label-width="100px"
            >
                <el-form-item label="示例标题" prop="title">
                    <el-input
                        v-model="formData.title"
                        placeholder="请输入示例标题"
                        maxlength="100"
                        show-word-limit
                    />
                </el-form-item>
                
                <el-form-item label="所属类目" prop="category_id">
                    <el-select v-model="formData.category_id" placeholder="请选择类目" class="w-full">
                        <el-option
                            v-for="item in categoryList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="角色设定" prop="content">
                    <el-input
                        v-model="formData.content"
                        type="textarea"
                        :rows="8"
                        placeholder="请输入角色设定内容，这将作为智能体的角色设定"
                        maxlength="2000"
                        show-word-limit
                    />
                </el-form-item>
                
                <el-form-item label="示例描述" prop="description">
                    <el-input
                        v-model="formData.description"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入示例描述，用于管理员了解该角色示例的用途"
                        maxlength="500"
                        show-word-limit
                    />
                </el-form-item>
                
                <el-form-item label="排序" prop="sort">
                    <el-input-number
                        v-model="formData.sort"
                        :min="0"
                        :max="9999"
                        placeholder="数值越大越靠前"
                    />
                </el-form-item>
                
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio :label="1">启用</el-radio>
                        <el-radio :label="0">禁用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="handleClose">取消</el-button>
                    <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { usePaging } from '@/hooks/usePaging'
import {
    roleExampleLists,
    addRoleExample,
    editRoleExample,
    delRoleExample,
    changeRoleExampleStatus,
    getRoleExampleCategoryList
} from '@/api/ai_role/role_example'
import feedback from '@/utils/feedback'

// 搜索参数
const queryParams = reactive({
    title: '',
    category_id: '',
    status: ''
})

// 分类列表
const categoryList: any = ref([])
const multipleSelection = ref<any[]>([])

// 弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
    id: '',
    title: '',
    category_id: '',
    content: '',
    description: '',
    sort: 0,
    status: 1
})

// 表单验证规则
const formRules = {
    title: [
        { required: true, message: '请输入示例标题', trigger: 'blur' },
        { min: 1, max: 100, message: '标题长度在1到100个字符', trigger: 'blur' }
    ],
    category_id: [
        { required: true, message: '请选择所属类目', trigger: 'change' }
    ],
    content: [
        { required: true, message: '请输入角色设定', trigger: 'blur' },
        { min: 10, max: 2000, message: '描述长度在10到2000个字符', trigger: 'blur' }
    ],
    description: [
        { max: 500, message: '描述长度不能超过500字符', trigger: 'blur' }
    ],
    sort: [
        { required: true, message: '请输入排序值', trigger: 'blur' }
    ]
}

// 多选处理
const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val
}

// 删除
const handleDelete = async (ids: number[]) => {
    await feedback.confirm('确定要删除选中的角色示例吗？')
    await delRoleExample({ id: ids })
    feedback.msgSuccess('删除成功')
    getLists()
}

// 修改状态
const changeStatus = async (id: any) => {
    await changeRoleExampleStatus({ id })
    feedback.msgSuccess('状态修改成功')
}

// 新增
const handleAdd = () => {
    dialogTitle.value = '新增角色示例'
    dialogVisible.value = true
    resetForm()
}

// 编辑
const handleEdit = (row: any) => {
    dialogTitle.value = '编辑角色示例'
    dialogVisible.value = true
    Object.assign(formData, {
        id: row.id,
        title: row.title,
        category_id: row.category_id,
        content: row.content,
        description: row.description,
        sort: row.sort,
        status: row.status
    })
}

// 重置表单
const resetForm = () => {
    Object.assign(formData, {
        id: '',
        title: '',
        category_id: '',
        content: '',
        description: '',
        sort: 0,
        status: 1
    })
    formRef.value?.clearValidate()
}

// 关闭弹窗
const handleClose = () => {
    dialogVisible.value = false
    resetForm()
}

// 提交表单
const handleSubmit = async () => {
    await formRef.value?.validate()
    
    try {
        submitLoading.value = true
        
        if (formData.id) {
            await editRoleExample(formData)
            feedback.msgSuccess('编辑成功')
        } else {
            await addRoleExample(formData)
            feedback.msgSuccess('新增成功')
        }
        
        handleClose()
        getLists()
    } finally {
        submitLoading.value = false
    }
}

// 获取分类列表
const getCategoryList = async () => {
    try {
        const data = await getRoleExampleCategoryList()
        categoryList.value = data.lists || []
    } catch (error) {
        console.error('获取分类列表失败:', error)
    }
}

// 分页和列表
const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: roleExampleLists,
    params: queryParams
})

// 初始化
onMounted(() => {
    getLists()
    getCategoryList()
})
</script> 