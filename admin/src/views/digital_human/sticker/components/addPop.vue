<template>
    <Popup
        ref="popRef"
        :title="formData.id ? '编辑贴纸' : '新增贴纸'"
        width="600px"
        async
        @confirm="submit"
        @close="$emit('close')"
        :confirm-loading="confirmLoading"
    >
        <el-form label-width="90px" ref="formRef" :model="formData" :rules="rules">
            <el-form-item label="贴纸图" prop="cover">
                <MaterialPicker
                    :limit="formData.id ? 1 : 10"
                    v-model="formData.url"
                    :data="{ use_type: 2 }"
                />
            </el-form-item>
            <el-form-item label="所属分类">
                <el-select v-model="formData.category_id">
                    <el-option label="无分类" :value="0" />
                    <el-option
                        v-for="item in categoryList"
                        :label="item.name"
                        :key="item.id"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-radio-group v-model="formData.type">
                    <el-radio :label="1">图片</el-radio>
                    <el-radio :label="2">动图</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="排序">
                <el-input v-model="formData.sort" class="w-[320px]" />
            </el-form-item>
            <el-form-item label="状态">
                <el-switch :active-value="1" :inactive-value="0" v-model="formData.status" />
            </el-form-item>
        </el-form>
    </Popup>
</template>

<script setup lang="ts">
import type { FormRules } from 'element-plus'
import { shallowRef } from 'vue'
import { addSticker, getCategoryLists, editSticker } from '@/api/digital_human/sticker'

const emits = defineEmits(['success', 'close'])

const formRef = shallowRef()

const confirmLoading = ref(false)
const popRef = shallowRef()
const formData = ref({
    id: '',
    status: 1,
    sort: '0',
    url: [],
    category_id: 0,
    type: 1
})

const rules: FormRules = {
    type: [{ required: true }],
    cover: [{ required: true }]
}

//分类列表
const categoryList = ref()

//获取分类列表
const getCategoryList = async () => {
    categoryList.value = await getCategoryLists()
}

//提交
const submit = async () => {
    try {
        confirmLoading.value = true
        if (formData.value.id) {
            await editSticker({ ...formData.value })
        } else {
            await addSticker({ ...formData.value })
        }
        emits('success')
    } catch (error) {
        confirmLoading.value = false
    }
}

const open = async (row?: any) => {
    popRef.value.open()
    if (row) {
        Object.keys(formData.value).map((item) => {
            //@ts-ignore
            formData.value[item] = row[item]
        })
        formData.value.id = row.id
    }
    await getCategoryList()
}

defineExpose({ open })
</script>

<style scoped lang="scss"></style>
