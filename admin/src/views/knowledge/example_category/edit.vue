<template>
    <div>
        <popup
            ref="popupRef"
            :title="title"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form
                ref="formRef"
                :model="formData"
                label-width="120px"
                :rules="rules"
                class="pr-[30px]"
            >
                <el-form-item label="类别名称" prop="name">
                    <el-input v-model="formData.name" placeholder="请输入类别名称" clearable />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number v-model="formData.sort" :min="0" />
                    <div class="form-tips">数字越小越靠前</div>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio :label="1">开启</el-radio>
                        <el-radio :label="0">关闭</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, shallowRef } from 'vue'
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import feedback from '@/utils/feedback'
import { 
    addCategory, 
    getCategoryDetail, 
    editCategory 
} from '@/api/knowledge/example_category'

const emit = defineEmits(['success', 'close'])
const props = defineProps({
    title: {
        type: String,
        default: ''
    }
})

const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')
const formData = reactive({
    id: '',
    name: '',
    sort: 0,
    status: 1
})

const rules = {
    name: [
        {
            required: true,
            message: '请输入类别名称',
            trigger: ['blur', 'change']
        }
    ],
    status: [
        {
            required: true,
            message: '请选择状态',
            trigger: ['blur', 'change']
        }
    ]
}

const open = async (row: Record<string, any> = {}) => {
    if (row && row.id !== undefined) {
        mode.value = 'edit'
        const data = await getCategoryDetail({ id: row.id })
        Object.assign(formData, data)
    } else {
        mode.value = 'add'
        Object.assign(formData, {
            id: '',
            name: '',
            sort: 0,
            status: 1
        })
    }
    popupRef.value?.open()
}

const handleSubmit = async () => {
    await formRef.value?.validate()
    
    try {
        if (mode.value === 'edit') {
            await editCategory(formData)
        } else {
            await addCategory(formData)
        }
        
        popupRef.value?.close()
        feedback.msgSuccess('操作成功')
        emit('success')
    } catch (error) {
        console.error(error)
    }
}

const handleClose = () => {
    emit('close')
}

defineExpose({
    open
})
</script> 