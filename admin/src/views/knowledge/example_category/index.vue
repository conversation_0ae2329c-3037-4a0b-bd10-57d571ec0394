<template>
    <div>
        <el-card class="!border-none" shadow="never">
            <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
                <el-form-item label="类别名称">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入类别名称"
                        clearable
                        @keyup.enter="resetPage"
                    />
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                        <el-option label="开启" value="1" />
                        <el-option label="关闭" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none mt-4" shadow="never">
            <div>
                <el-button
                    v-perms="['kb.example_category/add']"
                    type="primary"
                    @click="handleAdd"
                >
                    <template #icon>
                        <icon name="el-icon-Plus" />
                    </template>
                    新增
                </el-button>
            </div>
            <el-table
                class="mt-4"
                v-loading="pager.loading"
                :data="pager.lists"
                size="large"
            >
                <el-table-column label="类别名称" prop="name" min-width="150" />
                <el-table-column label="示例数量" prop="example_content_count" min-width="100" />
                <el-table-column label="状态" min-width="100">
                    <template #default="{ row }">
                        <el-switch
                            v-perms="['kb.example_category/status']"
                            v-model="row.status"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleStatusChange(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="排序" prop="sort" min-width="100" />
                <el-table-column label="创建时间" prop="create_time" min-width="180" />
                <el-table-column label="操作" width="150" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['kb.example_category/edit']"
                            type="primary"
                            link
                            @click="handleEdit(row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-perms="['kb.example_category/del']"
                            type="danger"
                            link
                            @click="handleDelete(row)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>

        <edit-popup
            v-if="showEdit"
            ref="editRef"
            :title="editTitle"
            @success="getLists"
            @close="showEdit = false"
        />
    </div>
</template>

<script lang="ts" setup name="ExampleCategory">
import { ref, reactive, shallowRef, onMounted, nextTick } from 'vue'
import { usePaging } from '@/hooks/usePaging'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'
import {
    getCategoryList,
    deleteCategory,
    updateCategoryStatus
} from '@/api/knowledge/example_category'

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
const showEdit = ref(false)
const editTitle = ref('')

const queryParams = reactive({
    name: '',
    status: ''
})

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: getCategoryList,
    params: queryParams
})

const handleAdd = async () => {
    showEdit.value = true
    editTitle.value = '新增示例库类别'
    await nextTick()
    editRef.value?.open()
}

const handleEdit = async (row: any) => {
    showEdit.value = true
    editTitle.value = '编辑示例库类别'
    await nextTick()
    editRef.value?.open(row)
}

const handleDelete = async (row: any) => {
    await feedback.confirm('确定要删除该示例库类别吗？')
    await deleteCategory({ id: row.id })
    feedback.msgSuccess('删除成功')
    getLists()
}

const handleStatusChange = async (row: any) => {
    await updateCategoryStatus({ id: row.id, status: row.status })
}

onMounted(() => {
    getLists()
})
</script> 