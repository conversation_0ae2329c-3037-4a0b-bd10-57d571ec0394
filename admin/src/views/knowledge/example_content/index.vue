<template>
    <div>
        <el-card class="!border-none" shadow="never">
            <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
                <el-form-item label="示例标题">
                    <el-input
                        v-model="queryParams.title"
                        placeholder="请输入示例标题"
                        clearable
                        @keyup.enter="resetPage"
                    />
                </el-form-item>
                <el-form-item label="所属类别">
                    <el-select v-model="queryParams.category_id" placeholder="请选择类别" clearable>
                        <el-option
                            v-for="item in categoryList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                        <el-option label="开启" value="1" />
                        <el-option label="关闭" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none mt-4" shadow="never">
            <div>
                <el-button
                    v-perms="['kb.example/add']"
                    type="primary"
                    @click="handleAdd"
                >
                    <template #icon>
                        <icon name="el-icon-Plus" />
                    </template>
                    新增
                </el-button>
            </div>
            <el-table
                class="mt-4"
                v-loading="pager.loading"
                :data="pager.lists"
                size="large"
            >
                <el-table-column label="示例标题" prop="title" min-width="150" />
                <el-table-column label="所属类别" prop="category_name" min-width="120" />
                <el-table-column label="问题内容" min-width="200">
                    <template #default="{ row }">
                        <div class="line-clamp-2">{{ row.question_brief }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="答案内容" min-width="200">
                    <template #default="{ row }">
                        <div class="line-clamp-2">{{ row.answer_brief }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="状态" min-width="100">
                    <template #default="{ row }">
                        <el-switch
                            v-perms="['kb.example/status']"
                            v-model="row.status"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleStatusChange(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="排序" prop="sort" min-width="80" />
                <el-table-column label="创建时间" prop="create_time" min-width="180" />
                <el-table-column label="操作" width="150" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['kb.example/edit']"
                            type="primary"
                            link
                            @click="handleEdit(row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-perms="['kb.example/del']"
                            type="danger"
                            link
                            @click="handleDelete(row)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>

        <edit-popup
            v-if="showEdit"
            ref="editRef"
            :title="editTitle"
            :category-list="categoryList"
            @success="getLists"
            @close="showEdit = false"
        />
    </div>
</template>

<script lang="ts" setup name="ExampleContent">
import { ref, reactive, shallowRef, onMounted, nextTick } from 'vue'
import { usePaging } from '@/hooks/usePaging'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'
import {
    getContentList,
    deleteContent,
    updateContentStatus
} from '@/api/knowledge/example_content'

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
const showEdit = ref(false)
const editTitle = ref('')
const categoryList = ref<any[]>([])

const queryParams = reactive({
    title: '',
    category_id: '',
    status: ''
})

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: getContentList,
    params: queryParams
})

// 获取数据后设置类别列表
onMounted(() => {
    getLists().then((res) => {
        if (res && res.category_lists) {
            categoryList.value = res.category_lists;
        }
    });
})

const handleAdd = async () => {
    showEdit.value = true
    editTitle.value = '新增示例库内容'
    await nextTick()
    editRef.value?.open()
}

const handleEdit = async (row: any) => {
    showEdit.value = true
    editTitle.value = '编辑示例库内容'
    await nextTick()
    editRef.value?.open(row)
}

const handleDelete = async (row: any) => {
    await feedback.confirm('确定要删除该示例库内容吗？')
    await deleteContent({ id: row.id })
    feedback.msgSuccess('删除成功')
    getLists()
}

const handleStatusChange = async (row: any) => {
    await updateContentStatus({ id: row.id, status: row.status })
}
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style> 