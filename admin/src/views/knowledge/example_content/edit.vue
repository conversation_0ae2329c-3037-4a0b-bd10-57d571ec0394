<template>
    <div>
        <popup
            ref="popupRef"
            :title="title"
            :async="true"
            width="750px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form
                ref="formRef"
                :model="formData"
                label-width="120px"
                :rules="rules"
                class="pr-[30px]"
            >
                <el-form-item label="所属类别" prop="category_id">
                    <el-select v-model="formData.category_id" placeholder="请选择类别" class="w-[320px]">
                        <el-option
                            v-for="item in categoryList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="示例标题" prop="title">
                    <el-input 
                        v-model="formData.title" 
                        placeholder="请输入示例标题" 
                        clearable
                        class="w-[550px]"
                    />
                </el-form-item>
                <el-form-item label="问题内容" prop="question">
                    <el-input 
                        v-model="formData.question" 
                        type="textarea" 
                        rows="5" 
                        placeholder="请输入问题内容"
                        class="w-[550px]"
                    />
                </el-form-item>
                <el-form-item label="答案内容" prop="answer">
                    <el-input 
                        v-model="formData.answer" 
                        type="textarea" 
                        rows="8" 
                        placeholder="请输入答案内容"
                        class="w-[550px]"
                    />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number v-model="formData.sort" :min="0" />
                    <div class="form-tips">数字越小越靠前</div>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio :label="1">开启</el-radio>
                        <el-radio :label="0">关闭</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, shallowRef } from 'vue'
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import feedback from '@/utils/feedback'
import {
    addContent,
    getContentDetail,
    editContent
} from '@/api/knowledge/example_content'

const emit = defineEmits(['success', 'close'])
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    categoryList: {
        type: Array,
        default: () => []
    }
})

const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')
const formData = reactive({
    id: '',
    category_id: '',
    title: '',
    question: '',
    answer: '',
    sort: 0,
    status: 1
})

const rules = {
    category_id: [
        {
            required: true,
            message: '请选择所属类别',
            trigger: ['blur', 'change']
        }
    ],
    title: [
        {
            required: true,
            message: '请输入示例标题',
            trigger: ['blur', 'change']
        }
    ],
    question: [
        {
            required: true,
            message: '请输入问题内容',
            trigger: ['blur', 'change']
        }
    ],
    answer: [
        {
            required: true,
            message: '请输入答案内容',
            trigger: ['blur', 'change']
        }
    ],
    status: [
        {
            required: true,
            message: '请选择状态',
            trigger: ['blur', 'change']
        }
    ]
}

const open = async (row: Record<string, any> = {}) => {
    if (row && row.id !== undefined) {
        mode.value = 'edit'
        const data = await getContentDetail({ id: row.id })
        Object.assign(formData, data)
    } else {
        mode.value = 'add'
        Object.assign(formData, {
            id: '',
            category_id: '',
            title: '',
            question: '',
            answer: '',
            sort: 0,
            status: 1
        })
    }
    popupRef.value?.open()
}

const handleSubmit = async () => {
    await formRef.value?.validate()
    
    try {
        if (mode.value === 'edit') {
            await editContent(formData)
        } else {
            await addContent(formData)
        }
        
        popupRef.value?.close()
        feedback.msgSuccess('操作成功')
        emit('success')
    } catch (error) {
        console.error(error)
    }
}

const handleClose = () => {
    emit('close')
}

defineExpose({
    open
})
</script> 