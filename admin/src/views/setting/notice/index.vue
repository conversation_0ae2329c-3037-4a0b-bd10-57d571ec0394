<template>
    <div>
        <el-card shadow="never" class="!border-none">
            <div class="font-medium">公告设置</div>
            <div class="mt-4">
                <el-form label-width="120px">
                    <el-form-item label="公告弹框">
                        <div>
                            <div class="flex items-center">
                                <el-switch
                                    :active-value="1"
                                    :inactive-value="0"
                                    v-model="formData.is_bulletin"
                                />
                                <div class="ml-2 font-medium">
                                    {{ formData.is_bulletin == 1 ? '开启' : '关闭' }}
                                </div>
                            </div>
                            <div class="form-tips">用户每天首次进入站点会触发弹框</div>
                        </div>
                    </el-form-item>
                    <el-form-item label="公告内容">
                        <!-- 直接启用HTML功能，无需复杂配置 -->
                        <Editor 
                            v-model="formData.bulletin_content" 
                            height="500px" 
                            width="100%"
                            :allow-html="true"
                            :allowed-tags="[]"
                        />
                        
                        <!-- 简化的功能说明 -->
                        <div class="mt-3 space-y-2">
                            <el-alert
                                title="🎯 HTML编辑功能已启用"
                                type="success"
                                :closable="false"
                                description="支持所有HTML标签，包括JavaScript、iframe、style等功能，让您创建丰富的公告内容。"
                            />
                            
                            <el-collapse>
                                <el-collapse-item title="📚 功能说明" name="features">
                                    <div class="text-sm space-y-2">
                                        <div><strong>🎯 支持的功能：</strong></div>
                                        <ul class="list-disc list-inside ml-4 space-y-1">
                                            <li><code>&lt;script&gt;</code> - JavaScript代码执行</li>
                                            <li><code>&lt;iframe&gt;</code> - 外部网页和视频嵌入</li>
                                            <li><code>&lt;style&gt;</code> - 自定义CSS样式</li>
                                            <li><code>&lt;form&gt;</code> - 表单和交互元素</li>
                                            <li><code>&lt;embed&gt;</code> - 嵌入式媒体内容</li>
                                            <li><code>&lt;object&gt;</code> - 对象嵌入</li>
                                            <li><code>&lt;link&gt;</code> - 外部资源链接</li>
                                            <li>以及所有其他HTML标签</li>
                                        </ul>
                                    </div>
                                </el-collapse-item>
                                
                                <el-collapse-item title="⚡ 使用示例" name="examples">
                                    <div class="text-sm space-y-3">
                                        <div>
                                            <strong>JavaScript交互：</strong>
                                            <pre class="bg-gray-100 p-2 rounded text-xs mt-1"><code>&lt;button onclick="alert('欢迎访问！')"&gt;点击问候&lt;/button&gt;</code></pre>
                                        </div>
                                        
                                        <div>
                                            <strong>视频嵌入：</strong>
                                            <pre class="bg-gray-100 p-2 rounded text-xs mt-1"><code>&lt;iframe src="https://player.bilibili.com/player.html?bvid=BV1xx411c7mD" width="500" height="300"&gt;&lt;/iframe&gt;</code></pre>
                                        </div>
                                        
                                        <div>
                                            <strong>自定义样式：</strong>
                                            <pre class="bg-gray-100 p-2 rounded text-xs mt-1"><code>&lt;style&gt;.highlight{background:gold;padding:5px;}&lt;/style&gt;
&lt;div class="highlight"&gt;高亮内容&lt;/div&gt;</code></pre>
                                        </div>
                                    </div>
                                </el-collapse-item>
                                
                                <el-collapse-item title="💡 使用技巧" name="tips">
                                    <div class="text-sm space-y-2">
                                        <ul class="list-disc list-inside ml-4 space-y-1">
                                            <li><strong>模式切换</strong>：使用顶部的"可视化编辑"和"HTML源码"按钮切换编辑模式</li>
                                            <li><strong>快速插入</strong>：在可视化模式下点击"插入HTML模板"可快速插入预设模板</li>
                                            <li><strong>代码格式化</strong>：在HTML源码模式下点击"格式化代码"可整理代码结构</li>
                                            <li><strong>代码验证</strong>：使用"验证HTML"检查代码质量和潜在问题</li>
                                            <li><strong>样式建议</strong>：推荐使用内联样式（style属性）来确保样式正确显示</li>
                                        </ul>
                                    </div>
                                </el-collapse-item>
                            </el-collapse>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </el-card>
        <FooterBtns>
            <el-button type="primary" @click="setData">保存</el-button>
        </FooterBtns>
    </div>
</template>

<script setup lang="ts">
import { getNoticeSet, setNoticeSet } from '@/api/setting/notice'

const formData = reactive({
    is_bulletin: 0,
    bulletin_content: ''
})

const getData = async () => {
    const res = await getNoticeSet()
    Object.keys(formData).map((item) => {
        //@ts-ignore
        formData[item] = res[item]
    })
}

const setData = async () => {
    await setNoticeSet({ ...formData })
    getData()
}

onMounted(() => {
    getData()
})
</script>

<style scoped lang="scss"></style>
 