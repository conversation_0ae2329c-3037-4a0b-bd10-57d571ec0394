<template>
    <div>
        <el-card class="!border-none" shadow="never">
            <div class="mb-4">
                <h3 class="text-lg font-medium">赠送功能配置</h3>
                <p class="text-gray-500 text-sm mt-1">配置用户间赠送灵感值的各项限制和规则</p>
            </div>
            
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-width="150px"
                class="max-w-2xl"
                v-loading="loading"
            >
                <el-card class="mb-6" shadow="never">
                    <template #header>
                        <span class="text-base font-medium">基础设置</span>
                    </template>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="启用赠送功能" prop="is_enable">
                                <el-switch
                                    v-model="form.is_enable"
                                />
                                <div class="text-xs text-gray-500 mt-1">
                                    关闭后用户无法进行赠送操作
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="需要人工审核" prop="need_verify">
                                <el-switch
                                    v-model="form.need_verify"
                                />
                                <div class="text-xs text-gray-500 mt-1">
                                    开启后赠送需要管理员审核通过
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </el-card>

                <el-card class="mb-6" shadow="never">
                    <template #header>
                        <span class="text-base font-medium">金额限制</span>
                    </template>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="最小赠送金额" prop="min_gift_amount">
                                <el-input-number
                                    v-model="form.min_gift_amount"
                                    :min="0.01"
                                    :max="10000"
                                    :precision="2"
                                    style="width: 100%"
                                />
                                <div class="text-xs text-gray-500 mt-1">
                                    单次赠送的最小金额限制
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="最大赠送金额" prop="max_gift_amount">
                                <el-input-number
                                    v-model="form.max_gift_amount"
                                    :min="0.01"
                                    :max="100000"
                                    :precision="2"
                                    style="width: 100%"
                                />
                                <div class="text-xs text-gray-500 mt-1">
                                    单次赠送的最大金额限制
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>

                <el-card class="mb-6" shadow="never">
                    <template #header>
                        <span class="text-base font-medium">每日限额</span>
                    </template>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="每日赠送限额" prop="daily_gift_limit">
                                <el-input-number
                                    v-model="form.daily_gift_limit"
                                    :min="0"
                                    :max="100000"
                                    :precision="2"
                                    style="width: 100%"
                                />
                                <div class="text-xs text-gray-500 mt-1">
                                    用户每天可赠送的总金额限制
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="每日接收限额" prop="daily_receive_limit">
                                <el-input-number
                                    v-model="form.daily_receive_limit"
                                    :min="0"
                                    :max="100000"
                                    :precision="2"
                                    style="width: 100%"
                                />
                                <div class="text-xs text-gray-500 mt-1">
                                    用户每天可接收的总金额限制
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>

                <el-card class="mb-6" shadow="never">
                    <template #header>
                        <span class="text-base font-medium">次数限制</span>
                    </template>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="每日赠送次数" prop="gift_times_limit">
                                <el-input-number
                                    v-model="form.gift_times_limit"
                                    :min="1"
                                    :max="1000"
                                    style="width: 100%"
                                />
                                <div class="text-xs text-gray-500 mt-1">
                                    用户每天可进行赠送的次数限制
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="每日接收次数" prop="receive_times_limit">
                                <el-input-number
                                    v-model="form.receive_times_limit"
                                    :min="1"
                                    :max="1000"
                                    style="width: 100%"
                                />
                                <div class="text-xs text-gray-500 mt-1">
                                    用户每天可接收赠送的次数限制
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>

                <el-form-item>
                    <el-button
                        type="primary"
                        @click="handleSave"
                        :loading="loading"
                        size="large"
                    >
                        保存配置
                    </el-button>
                    <el-button @click="handleReset" size="large">
                        重置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { giftGetConfigApi, giftSaveConfigApi } from '@/api/user/gift'

defineOptions({
    name: 'UserGiftConfig'
})

const formRef = ref()
const loading = ref(false)

// 表单数据
const form = reactive({
    is_enable: true,
    min_gift_amount: 1,
    max_gift_amount: 1000,
    daily_gift_limit: 100,
    daily_receive_limit: 500,
    gift_times_limit: 10,
    receive_times_limit: 20,
    need_verify: false
})

// 表单验证规则
const rules = {
    min_gift_amount: [
        { required: true, message: '请输入最小赠送金额', trigger: 'blur' },
        { type: 'number', min: 0, message: '最小赠送金额不能小于0' }
    ],
    max_gift_amount: [
        { required: true, message: '请输入最大赠送金额', trigger: 'blur' },
        { type: 'number', min: 0, message: '最大赠送金额不能小于0' },
        {
            validator: (rule: any, value: number, callback: Function) => {
                if (value < form.min_gift_amount) {
                    callback(new Error('最大赠送金额不能小于最小赠送金额'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    daily_gift_limit: [
        { required: true, message: '请输入每日赠送限额', trigger: 'blur' },
        { type: 'number', min: 0, message: '每日赠送限额不能小于0' }
    ],
    daily_receive_limit: [
        { required: true, message: '请输入每日接收限额', trigger: 'blur' },
        { type: 'number', min: 0, message: '每日接收限额不能小于0' },
        {
            validator: (rule: any, value: number, callback: Function) => {
                if (value < form.daily_gift_limit) {
                    callback(new Error('每日接收限额不能小于每日赠送限额'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    gift_times_limit: [
        { required: true, message: '请输入每日赠送次数限制', trigger: 'blur' },
        { type: 'number', min: 0, message: '每日赠送次数限制不能小于0' }
    ],
    receive_times_limit: [
        { required: true, message: '请输入每日接收次数限制', trigger: 'blur' },
        { type: 'number', min: 0, message: '每日接收次数限制不能小于0' }
    ]
}

// 原始数据备份
let originalData: any = {}

// 获取配置
const getConfig = async () => {
    try {
        loading.value = true
        console.log('开始获取配置...')
        
        // 检查API函数是否存在
        if (typeof giftGetConfigApi !== 'function') {
            console.error('giftGetConfigApi 函数未定义')
            ElMessage.error('API函数未正确导入')
            return
        }
        
        const data = await giftGetConfigApi()
        console.log('获取到的配置数据:', data)
        
        // 简化数据处理
        if (data && typeof data === 'object') {
            Object.keys(form).forEach(key => {
                if (data[key] !== undefined) {
                    form[key] = data[key]
                }
            })
            
            // 备份原始数据
            originalData = { ...form }
            ElMessage.success('配置加载成功')
        } else {
            ElMessage.warning('配置数据格式异常')
        }
        
    } catch (error) {
        console.error('获取配置失败:', error)
        ElMessage.error(`获取配置失败: ${error.message || error}`)
    } finally {
        loading.value = false
    }
}

// 保存配置
const handleSave = async () => {
    try {
        const valid = await formRef.value?.validate()
        if (!valid) return
        
        loading.value = true
        console.log('开始保存配置...', form)
        
        // 检查API函数是否存在
        if (typeof giftSaveConfigApi !== 'function') {
            console.error('giftSaveConfigApi 函数未定义')
            ElMessage.error('API函数未正确导入')
            return
        }
        
        const result = await giftSaveConfigApi(form)
        console.log('保存结果:', result)
        
        ElMessage.success('保存成功')
        
        // 更新原始数据
        originalData = { ...form }
        
    } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error(`保存失败: ${error.message || error}`)
    } finally {
        loading.value = false
    }
}

// 重置配置
const handleReset = () => {
    Object.assign(form, originalData)
    formRef.value?.clearValidate()
    ElMessage.info('表单已重置为默认值')
}

// 初始化
onMounted(() => {
    getConfig()
    ElMessage.info('赠送配置页面已加载')
})
</script> 