<template>
    <div>
        <!-- 统计卡片 -->
        <el-row :gutter="20" class="mb-4" v-loading="loading">
            <el-col :span="6">
                <el-card class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.todayGiftAmount || '0.00' }}</div>
                        <div class="stat-label">今日赠送总额</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.todayGiftCount || '0' }}</div>
                        <div class="stat-label">今日赠送笔数</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.totalUsers || '0' }}</div>
                        <div class="stat-label">参与用户数</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.avgAmount || '0.00' }}</div>
                        <div class="stat-label">平均赠送金额</div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 数据表格 -->
        <el-row :gutter="20" v-loading="loading">
            <el-col :span="12">
                <el-card title="赠送排行榜" class="!border-none" shadow="never">
                    <template #header>
                        <div class="card-header">
                            <span>赠送排行榜</span>
                        </div>
                    </template>
                    <el-table :data="giftRanking" stripe>
                        <el-table-column type="index" label="排名" width="60" />
                        <el-table-column label="用户" prop="nickname">
                            <template #default="{ row }">
                                {{ row.nickname || `用户${row.user_id}` }}
                            </template>
                        </el-table-column>
                        <el-table-column label="赠送金额" prop="amount" width="120">
                            <template #default="{ row }">
                                <span class="text-red-500">{{ Math.floor(parseFloat(row.amount)) }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div v-if="giftRanking.length === 0" class="text-center py-4 text-gray-500">
                        暂无数据
                    </div>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card title="接收排行榜" class="!border-none" shadow="never">
                    <template #header>
                        <div class="card-header">
                            <span>接收排行榜</span>
                        </div>
                    </template>
                    <el-table :data="receiveRanking" stripe>
                        <el-table-column type="index" label="排名" width="60" />
                        <el-table-column label="用户" prop="nickname">
                            <template #default="{ row }">
                                {{ row.nickname || `用户${row.user_id}` }}
                            </template>
                        </el-table-column>
                        <el-table-column label="接收金额" prop="amount" width="120">
                            <template #default="{ row }">
                                <span class="text-green-500">{{ Math.floor(parseFloat(row.amount)) }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div v-if="receiveRanking.length === 0" class="text-center py-4 text-gray-500">
                        暂无数据
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 最近记录 -->
        <el-card title="最近记录" class="!border-none mt-4" shadow="never">
            <template #header>
                <div class="card-header">
                    <span>最近记录</span>
                    <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
                </div>
            </template>
            <el-table :data="recentRecords" stripe v-loading="loading">
                <el-table-column label="流水号" prop="gift_sn" width="160" />
                <el-table-column label="赠送用户" prop="from_user_nickname">
                    <template #default="{ row }">
                        {{ row.from_user_nickname || `用户${row.from_user_id}` }}
                    </template>
                </el-table-column>
                <el-table-column label="接收用户" prop="to_user_nickname">
                    <template #default="{ row }">
                        {{ row.to_user_nickname || `用户${row.to_user_id}` }}
                    </template>
                </el-table-column>
                <el-table-column label="金额" prop="gift_amount" width="100">
                    <template #default="{ row }">
                        <span class="text-red-500 font-medium">{{ Math.floor(parseFloat(row.gift_amount)) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="时间" prop="create_time" width="160" />
                <el-table-column label="状态" prop="status" width="80">
                    <template #default="{ row }">
                        <el-tag type="success" size="small">成功</el-tag>
                    </template>
                </el-table-column>
            </el-table>
            <div v-if="recentRecords.length === 0" class="text-center py-4 text-gray-500">
                暂无数据
            </div>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { giftGetStatisticsApi } from '@/api/user/gift'

defineOptions({
    name: 'UserGiftStatistics'
})

const loading = ref(false)

// 统计数据
const stats = reactive({
    todayGiftAmount: '0.00',
    todayGiftCount: '0',
    totalUsers: '0',
    avgAmount: '0.00'
})

// 赠送排行榜
const giftRanking = ref([])

// 接收排行榜
const receiveRanking = ref([])

// 最近记录
const recentRecords = ref([])

// 获取统计数据
const getStatistics = async () => {
    try {
        loading.value = true
        console.log('开始获取统计数据...')
        
        if (typeof giftGetStatisticsApi !== 'function') {
            console.error('giftGetStatisticsApi 函数未定义')
            ElMessage.error('API函数未正确导入')
            return
        }

        const data = await giftGetStatisticsApi()
        console.log('获取到的统计数据:', data)
        
        if (data) {
            // 更新统计数据
            if (data.stats) {
                Object.assign(stats, {
                    todayGiftAmount: data.stats.todayGiftAmount || '0.00',
                    todayGiftCount: data.stats.todayGiftCount || '0',
                    totalUsers: data.stats.totalUsers || '0',
                    avgAmount: data.stats.avgAmount || '0.00'
                })
            }
            
            // 更新排行榜
            giftRanking.value = data.giftRanking || []
            receiveRanking.value = data.receiveRanking || []
            recentRecords.value = data.recentRecords || []
        }
        
        ElMessage.success('统计数据加载成功')
    } catch (error) {
        console.error('获取统计数据失败:', error)
        ElMessage.error(`获取统计数据失败: ${error}`)
    } finally {
        loading.value = false
    }
}

// 刷新数据
const refreshData = () => {
    getStatistics()
}

// 初始化
onMounted(() => {
    getStatistics()
})
</script>

<style scoped>
.stat-card {
    text-align: center;
}

.stat-content {
    padding: 10px;
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.stat-trend {
    font-size: 12px;
}

.stat-trend.positive {
    color: #67c23a;
}

.stat-trend.negative {
    color: #f56c6c;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style> 