<template>
    <div>
        <el-card class="!border-none" shadow="never">
            <el-form class="mb-[-16px]" :model="queryParams" inline>
                <el-form-item label="流水号">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.gift_sn"
                        placeholder="请输入流水号"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="状态">
                    <el-select
                        class="w-[280px]"
                        v-model="queryParams.status"
                        placeholder="请选择状态"
                        clearable
                    >
                        <el-option label="成功" :value="1" />
                        <el-option label="失败" :value="2" />
                        <el-option label="已撤回" :value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="类型">
                    <el-select
                        class="w-[280px]"
                        v-model="queryParams.type"
                        placeholder="请选择类型"
                        clearable
                    >
                        <el-option label="全部" value="all" />
                        <el-option label="赠送" value="send" />
                        <el-option label="接收" value="receive" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none mt-4" shadow="never">
            <div>
                <el-table
                    v-loading="loading"
                    :data="tableData"
                >
                    <el-table-column label="流水号" prop="gift_sn" min-width="160" />
                    <el-table-column label="赠送用户" prop="from_user_nickname" min-width="120">
                        <template #default="{ row }">
                            {{ row.from_user_info?.nickname || row.from_user_nickname || `用户${row.from_user_id}` }}
                        </template>
                    </el-table-column>
                    <el-table-column label="接收用户" prop="to_user_nickname" min-width="120">
                        <template #default="{ row }">
                            {{ row.to_user_info?.nickname || row.to_user_nickname || `用户${row.to_user_id}` }}
                        </template>
                    </el-table-column>
                    <el-table-column label="赠送金额" prop="gift_amount" min-width="120">
                        <template #default="{ row }">
                            <span class="text-red-500 font-medium">{{ Math.floor(parseFloat(row.gift_amount)) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="status" min-width="100">
                        <template #default="{ row }">
                            <el-tag
                                :type="getStatusType(row.status)"
                                effect="light"
                            >
                                {{ getStatusText(row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" prop="create_time" min-width="160" />
                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
                            <el-button
                                type="primary"
                                size="small"
                                @click="handleViewDetail(row)"
                            >
                                详情
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex justify-end mt-4">
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </el-card>

        <!-- 详情弹窗 -->
        <el-dialog
            v-model="detailVisible"
            title="赠送记录详情"
            width="600px"
            :before-close="handleCloseDetail"
        >
            <div v-if="detailData" class="detail-content">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="流水号">
                        {{ detailData.gift_sn }}
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                        <el-tag
                            :type="getStatusType(detailData.status)"
                            effect="light"
                        >
                            {{ getStatusText(detailData.status) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="赠送用户">
                        {{ detailData.from_user_info?.nickname || detailData.from_user_nickname || `用户${detailData.from_user_id}` }}
                    </el-descriptions-item>
                    <el-descriptions-item label="接收用户">
                        {{ detailData.to_user_info?.nickname || detailData.to_user_nickname || `用户${detailData.to_user_id}` }}
                    </el-descriptions-item>
                    <el-descriptions-item label="赠送金额">
                        <span class="text-red-500 font-medium text-lg">{{ Math.floor(parseFloat(detailData.gift_amount)) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="类型">
                        <el-tag :type="detailData.type === 'send' ? 'danger' : 'success'">
                            {{ detailData.type === 'send' ? '赠送' : '接收' }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间">
                        {{ detailData.create_time }}
                    </el-descriptions-item>
                    <el-descriptions-item label="更新时间">
                        {{ detailData.update_time || detailData.create_time }}
                    </el-descriptions-item>
                </el-descriptions>

                <div v-if="detailData.remark" class="mt-4">
                    <h4 class="text-base font-medium mb-2">备注信息</h4>
                    <el-card shadow="never" class="bg-gray-50">
                        <p class="text-gray-700">{{ detailData.remark }}</p>
                    </el-card>
                </div>

                <div v-if="detailData.revoke_reason" class="mt-4">
                    <h4 class="text-base font-medium mb-2">撤回原因</h4>
                    <el-card shadow="never" class="bg-red-50">
                        <p class="text-red-700">{{ detailData.revoke_reason }}</p>
                    </el-card>
                </div>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailVisible = false">关闭</el-button>
                    <el-button 
                        v-if="detailData && detailData.status === 1 && canRevoke(detailData)"
                        type="danger" 
                        @click="handleRevoke(detailData)"
                    >
                        撤回
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { giftRecordsApi, giftDetailApi, giftRevokeApi } from '@/api/user/gift'

defineOptions({
    name: 'UserGiftRecords'
})

// 搜索参数
const queryParams = reactive({
    gift_sn: '',
    status: '',
    type: 'all'
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(15)
const total = ref(0)

// 详情弹窗
const detailVisible = ref(false)
const detailData = ref(null)

// 获取状态标签类型
const getStatusType = (status: number) => {
    const typeMap: Record<number, string> = {
        1: 'success',
        2: 'danger',
        3: 'warning'
    }
    return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
    const textMap: Record<number, string> = {
        1: '成功',
        2: '失败',
        3: '已撤回'
    }
    return textMap[status] || '未知'
}

// 获取记录列表
const getRecords = async () => {
    try {
        loading.value = true
        console.log('开始获取记录列表...')
        
        if (typeof giftRecordsApi !== 'function') {
            console.error('giftRecordsApi 函数未定义')
            ElMessage.error('API函数未正确导入')
            return
        }
        
        const params = {
            page_no: currentPage.value,
            page_size: pageSize.value,
            ...queryParams
        }
        
        const data = await giftRecordsApi(params)
        console.log('获取到的记录数据:', data)
        
        if (data && data.lists) {
            // 处理分页数据结构
            if (data.lists.data && Array.isArray(data.lists.data)) {
                // 新的分页结构：data.lists = {total, per_page, current_page, data: [...]}
                tableData.value = data.lists.data
                total.value = data.lists.total || 0
            } else if (Array.isArray(data.lists)) {
                // 旧的结构：data.lists = [...]
                tableData.value = data.lists
                total.value = data.count || 0
            } else {
                console.error('意外的数据结构:', data.lists)
                tableData.value = []
                total.value = 0
            }
        } else {
            tableData.value = []
            total.value = 0
        }
        
    } catch (error) {
        console.error('获取记录失败:', error)
        ElMessage.error(`获取记录失败: ${error}`)
    } finally {
        loading.value = false
    }
}

// 查询数据
const handleQuery = () => {
    currentPage.value = 1
    getRecords()
}

// 重置表单
const handleReset = () => {
    queryParams.gift_sn = ''
    queryParams.status = ''
    queryParams.type = 'all'
    currentPage.value = 1
    getRecords()
}

// 页码变化
const handleCurrentChange = (page: number) => {
    currentPage.value = page
    getRecords()
}

// 页大小变化
const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    getRecords()
}

// 查看详情
const handleViewDetail = async (row: any) => {
    try {
        console.log('查看详情:', row)
        
        if (typeof giftDetailApi !== 'function') {
            console.error('giftDetailApi 函数未定义')
            ElMessage.error('API函数未正确导入')
            return
        }
        
        const data = await giftDetailApi({ id: row.id })
        console.log('获取到的详情数据:', data)
        
        if (data) {
            detailData.value = data
            detailVisible.value = true
        } else {
            ElMessage.error('获取详情失败')
        }
        
    } catch (error) {
        console.error('获取详情失败:', error)
        ElMessage.error(`获取详情失败: ${error}`)
    }
}

// 关闭详情弹窗
const handleCloseDetail = () => {
    detailVisible.value = false
    detailData.value = null
}

// 判断是否可以撤回
const canRevoke = (record: any) => {
    // 只有成功状态且创建时间在24小时内的记录可以撤回
    if (record.status !== 1) return false
    
    const createTime = new Date(record.create_time).getTime()
    const now = new Date().getTime()
    const diffHours = (now - createTime) / (1000 * 60 * 60)
    
    return diffHours <= 24
}

// 撤回记录
const handleRevoke = async (record: any) => {
    try {
        const { value: reason } = await ElMessageBox.prompt(
            '请输入撤回原因',
            '撤回赠送',
            {
                confirmButtonText: '确定撤回',
                cancelButtonText: '取消',
                inputType: 'textarea',
                inputPlaceholder: '请输入撤回原因（可选）'
            }
        )
        
        if (typeof giftRevokeApi !== 'function') {
            console.error('giftRevokeApi 函数未定义')
            ElMessage.error('API函数未正确导入')
            return
        }
        
        await giftRevokeApi({
            id: record.id,
            remark: reason || ''
        })
        
        ElMessage.success('撤回成功')
        
        // 刷新列表和详情
        getRecords()
        if (detailVisible.value) {
            handleViewDetail(record)
        }
        
    } catch (error) {
        if (error !== 'cancel') {
            console.error('撤回失败:', error)
            ElMessage.error(`撤回失败: ${error}`)
        }
    }
}

// 初始化
onMounted(() => {
    getRecords()
})
</script>

<style scoped>
.detail-content {
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-footer {
    text-align: right;
}
</style> 