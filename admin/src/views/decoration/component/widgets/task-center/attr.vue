<template>
    <div>
        <el-form label-width="85px" label-position="left">
            <div class="mb-[18px] max-w-[400px]">
                <el-form-item label="标题名称" class="is-required">
                    <el-input v-model="content.title" placeholder="请输入标题名称" />
                </el-form-item>
                <el-form-item label="副标题" class="is-required">
                    <el-input v-model="content.subTitle" placeholder="请输入副标题" />
                </el-form-item>
                <div class="">
                    <div class="mb-[15px] pr-[12px] w-[85px]">
                        <span class="text-error">*</span>
                        <span class="text-tx-regular">奖励设置</span>
                    </div>
                    <Draggable
                        class="draggable"
                        v-model="content.data"
                        animation="300"
                        handle=".drag-move"
                    >
                        <template v-slot:item="{ element, index }">
                            <del-wrap :key="index" @close="handleDelete(index)">
                                <div class="flex items-center bg-fill-light w-full p-4 mb-4">
                                    <material-picker
                                        v-model="element.image"
                                        upload-class="bg-body"
                                        exclude-domain
                                        size="60px"
                                    >
                                    </material-picker>
                                    <div class="ml-6">
                                        <el-form-item label="任务类型">
                                            <el-select
                                                v-model="element.type"
                                                placeholder="请选择任务类型"
                                                @change="setTaskName($event, index)"
                                            >
                                                <el-option
                                                    v-for="item in taskListsNew"
                                                    :key="item.type"
                                                    :label="item.name"
                                                    :value="item.type"
                                                    :disabled="
                                                        item.disabled && item.type !== element.type
                                                    "
                                                />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="自定义">
                                            <el-input
                                                v-model="element.customName"
                                                placeholder="请输入自定义名称"
                                            />
                                        </el-form-item>
                                        <el-form-item label="排序" class="!mb-0">
                                            <div class="flex-1 flex items-center">
                                                <!-- <el-switch
                                                    v-model="element.show"
                                                    :active-value="1"
                                                    :inactive-value="0"
                                                /> -->
                                                <div class="drag-move cursor-move ml-auto">
                                                    <icon name="el-icon-Rank" size="18" />
                                                </div>
                                            </div>
                                        </el-form-item>
                                    </div>
                                </div>
                            </del-wrap>
                        </template>
                    </Draggable>
                </div>
                <el-form-item label-width="0">
                    <el-button type="primary" @click="handleAdd">添加任务</el-button>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue'
import type options from './options'
import Draggable from 'vuedraggable'
import feedback from '@/utils/feedback'
type OptionsType = ReturnType<typeof options>
const props = defineProps({
    content: {
        type: Object as PropType<OptionsType['content']>,
        default: () => ({})
    },
    styles: {
        type: Object as PropType<OptionsType['styles']>,
        default: () => ({})
    }
})

const taskLists = reactive([
    { name: '分享好友', type: 3, disabled: false },
    { name: '邀请新用户', type: 2, disabled: false },
    { name: '分享绘画至广场', type: 4, disabled: false },
    { name: '每日签到', type: 1, disabled: false },
    { name: '视频分享', type: 5, disabled: false },
    { name: '音乐分享', type: 6, disabled: false },
    { name: '智能体分享', type: 7, disabled: false }
])
const taskListsNew = computed(() => {
    return taskLists.map((item) => {
        let disabled = false
        if (props.content.data.some((i: any) => i.type === item.type)) {
            disabled = true
        }
        return {
            ...item,
            disabled
        }
    })
})

const setTaskName = (type: number, index: number) => {
    const task = taskLists.find((item) => item.type === type)
    if (task) {
        const data = props.content.data
        data[index].name = task.name
        data[index].customName = task.name
    }
}

const handleAdd = () => {
    if (props.content.data?.length < taskLists.length) {
        const data = props.content.data
        data.push({
            image: '',
            name: '',
            customName: '',
            show: 1,
            type: '' as any
        })
    } else {
        feedback.msgError(`已达到添加上限`)
    }
}

const handleDelete = (index: number) => {
    const data = props.content.data
    data.splice(index, 1)
}
</script>

<style lang="scss" scoped></style>
