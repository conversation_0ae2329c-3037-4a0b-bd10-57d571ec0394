<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form
                class="ls-form"
                ref="formRef"
                :rules="rules"
                :model="formData"
                label-width="90px"
            >
                <el-form-item label="类别名称" prop="name">
                    <el-input
                        class="ls-input"
                        v-model="formData.name"
                        placeholder="请输入类别名称"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="排序">
                    <div>
                        <el-input-number v-model="formData.sort" :min="0" :max="9999" />
                        <div class="form-tips">默认为0，数值越大排越前面</div>
                    </div>
                </el-form-item>
                <el-form-item label="状态">
                    <el-switch v-model="formData.status" :active-value="1" :inactive-value="0" />
                </el-form-item>
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup>
import { ref, shallowRef, nextTick } from 'vue'
import Popup from '@/components/popup/index.vue'
import { addCategory, editCategory } from '@/api/knowledge_base/example_category'
import feedback from '@/utils/feedback'

const emit = defineEmits(['success', 'close'])

const formRef = shallowRef()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const popupTitle = ref('')

const formData: any = ref({
    id: '',
    name: '',
    sort: 0,
    status: 1
})

const rules = {
    name: [
        {
            required: true,
            message: '请输入类别名称',
            trigger: ['blur']
        }
    ]
}

const handleSubmit = async () => {
    try {
        await formRef.value?.validate()
        
        const submitData: any = {
            name: formData.value.name,
            sort: Number(formData.value.sort),
            status: Number(formData.value.status)
        }
        
        if (!formData.value.id || formData.value.id === 0) {
            await addCategory(submitData)
            feedback.msgSuccess('添加成功')
        } else {
            submitData.id = Number(formData.value.id)
            await editCategory(submitData)
            feedback.msgSuccess('编辑成功')
        }
        
        popupRef.value?.close()
        emit('success')
    } catch (error) {
        return error
    }
}

const handleClose = () => {
    emit('close')
}

const open = (type: string, value: any) => {
    if (type == 'add') {
        formData.value = {
            id: '',
            name: '',
            sort: 0,
            status: 1
        }
        popupTitle.value = '新增示例类别'
    } else if (type == 'edit') {
        formData.value = {
            id: value.id ? Number(value.id) : 0,
            name: value.name || '',
            sort: value.sort !== undefined ? Number(value.sort) : 0,
            status: value.status !== undefined ? Number(value.status) : 1
        }
        popupTitle.value = '编辑示例类别'
    }
    
    nextTick(() => {
        popupRef.value?.open()
    })
}

defineExpose({
    open
})
</script> 