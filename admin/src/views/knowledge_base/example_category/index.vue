<template>
    <div class="container">
        <el-card class="box-card !border-none" shadow="never">
            <el-form ref="formRef" :model="queryParams" inline>
                <el-form-item label="类别名称">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入类别名称"
                        clearable
                        @keyup.enter="resetPage"
                    />
                </el-form-item>
                <el-form-item label="状态">
                    <el-select
                        v-model="queryParams.status"
                        placeholder="请选择状态"
                        clearable
                    >
                        <el-option label="开启" :value="1" />
                        <el-option label="关闭" :value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card class="box-card !border-none mt-4" shadow="never">
            <div class="mb-4 flex justify-between">
                <div>
                    <el-button
                        v-perms="['kb.example_category/add']"
                        type="primary"
                        @click="handleAdd"
                    >
                        <template #icon>
                            <icon name="el-icon-Plus" />
                        </template>
                        新增
                    </el-button>
                </div>
            </div>

            <el-table
                v-loading="pager.loading"
                :data="pager.lists"
                :row-style="{ cursor: 'pointer' }"
                height="580"
            >
                <el-table-column label="类别名称" prop="name" min-width="200" />
                <el-table-column label="排序" prop="sort" min-width="100" />
                <el-table-column label="状态" min-width="100">
                    <template #default="{ row }">
                        <el-switch
                            v-model="row.status"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleStatus(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="create_time" min-width="180" />
                <el-table-column label="操作" width="180" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['kb.example_category/edit']"
                            type="primary"
                            link
                            @click="handleEdit(row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-perms="['kb.example_category/del']"
                            type="danger"
                            link
                            @click="handleDelete(row)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>

        <edit-popup
            v-if="showEdit"
            ref="editRef"
            @success="getLists"
            @close="showEdit = false"
        />
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, shallowRef, nextTick } from 'vue'
import { getCategoryList, deleteCategory, updateCategoryStatus } from '@/api/knowledge_base/example_category'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'

const queryParams = reactive({
    name: '',
    status: '',
    page: 1
})

const pager = reactive<{
    page: number
    pageSize: number
    loading: boolean
    total: number
    lists: any[]
}>({
    page: 1,
    pageSize: 10,
    loading: true,
    total: 0,
    lists: []
})

const showEdit = ref(false)
const editRef = shallowRef<InstanceType<typeof EditPopup>>()

// 获取数据
const getLists = async () => {
    try {
        pager.loading = true
        const data = await getCategoryList({
            ...queryParams,
            page_no: pager.page,
            page_size: pager.pageSize
        })
        pager.lists = data.lists
        pager.total = data.count
    } catch (error) {
        console.error(error)
    } finally {
        pager.loading = false
    }
}

// 重置分页
const resetPage = () => {
    pager.page = 1
    getLists()
}

// 重置查询参数
const resetParams = () => {
    queryParams.name = ''
    queryParams.status = ''
    resetPage()
}

// 打开弹框
const openPop = async (type: string, value: any = {}) => {
    showEdit.value = true
    // 使用 nextTick 确保 DOM 更新完成后再调用 open 方法
    await nextTick()
    editRef.value?.open(type, value)
}

// 新增
const handleAdd = () => {
    openPop('add')
}

// 编辑
const handleEdit = (row: any) => {
    openPop('edit', row)
}

// 删除
const handleDelete = async (row: any) => {
    await feedback.confirm('确定要删除该类别吗？')
    await deleteCategory({ id: row.id })
    feedback.msgSuccess('删除成功')
    getLists()
}

// 修改状态
const handleStatus = async (row: any) => {
    await updateCategoryStatus({
        id: row.id,
        status: row.status
    })
    feedback.msgSuccess('修改成功')
}

getLists()
</script> 