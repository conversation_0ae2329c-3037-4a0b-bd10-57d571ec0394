<template>
    <el-dialog
        v-model="visible"
        :title="`编辑历史 - ${robotName || '智能体'}`"
        width="80%"
        :before-close="handleClose"
        destroy-on-close
    >
        <!-- 统计面板 -->
        <el-card v-if="showStatistics" class="!border-none mb-4" shadow="never">
            <template #header>
                <div class="flex justify-between items-center">
                    <span>编辑统计</span>
                    <el-button size="small" @click="showStatistics = false">隐藏</el-button>
                </div>
            </template>
            <el-row :gutter="20">
                <el-col :span="6">
                    <div class="stat-item">
                        <div class="stat-value">{{ statistics.total_edits }}</div>
                        <div class="stat-label">总编辑次数</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-item">
                        <div class="stat-value">{{ statistics.auto_offline_count }}</div>
                        <div class="stat-label">自动下架次数</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-item">
                        <div class="stat-value">{{ statistics.today_edits }}</div>
                        <div class="stat-label">今日编辑</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-item">
                        <div class="stat-value">{{ statistics.week_edits }}</div>
                        <div class="stat-label">本周编辑</div>
                    </div>
                </el-col>
            </el-row>
        </el-card>

        <!-- 操作栏 -->
        <div class="flex justify-between mb-4">
            <div>
                <el-button type="primary" @click="resetPage">
                    <template #icon>
                        <icon name="el-icon-Refresh" />
                    </template>
                    刷新
                </el-button>
            </div>
            <div>
                <el-button @click="showStatistics = !showStatistics">
                    {{ showStatistics ? '隐藏统计' : '显示统计' }}
                </el-button>
            </div>
        </div>

        <!-- 筛选条件 -->
        <el-form :model="queryParams" :inline="true" class="mb-4">
            <el-form-item label="编辑类型">
                <el-select v-model="queryParams.edit_type" placeholder="请选择" clearable>
                    <el-option label="智能体编辑" :value="1" />
                    <el-option label="知识库编辑触发" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否自动下架">
                <el-select v-model="queryParams.is_auto_offline" placeholder="请选择" clearable>
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
                <el-date-picker
                    v-model="dateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @change="handleDateChange"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="resetPage">查询</el-button>
                <el-button @click="resetParams">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- 编辑历史列表 -->
        <el-table :data="pager.lists" v-loading="pager.loading">
            <el-table-column label="智能体信息" min-width="200">
                <template #default="{ row }">
                    <div class="flex items-center">
                        <image-contain
                            :src="row.robot_image"
                            :width="40"
                            :height="40"
                            class="mr-2"
                        />
                        <div>
                            <div class="font-medium">{{ row.robot_name || '已删除' }}</div>
                            <div class="text-xs text-gray-500">ID: {{ row.robot_id }}</div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="编辑用户" prop="user_nickname" min-width="120" />
            <el-table-column label="编辑类型" min-width="120">
                <template #default="{ row }">
                    <el-tag :type="row.edit_type === 1 ? 'primary' : 'warning'">
                        {{ row.edit_type === 1 ? '智能体编辑' : '知识库编辑触发' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否自动下架" min-width="120">
                <template #default="{ row }">
                    <el-tag :type="row.is_auto_offline ? 'danger' : 'success'">
                        {{ row.is_auto_offline ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="编辑时间" prop="create_time" min-width="180" />
            <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ row }">
                    <el-button
                        type="primary"
                        link
                        @click="viewDetail(row)"
                    >
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-end mt-4">
            <pagination v-model="pager" @change="getLists" />
        </div>

        <!-- 详情弹窗 -->
        <DetailPopup ref="detailPopupRef" />
    </el-dialog>
</template>

<script lang="ts" setup name="EditHistoryPopup">
import { getRobotEditLogLists, getRobotEditLogStatistics } from '@/api/knowledge_base/robot_edit_log'
import { usePaging } from '@/hooks/usePaging'
import DetailPopup from '../robot_edit_log/detail.vue'

const emit = defineEmits(['close'])

const visible = ref(false)
const robotId = ref<number>(0)
const robotName = ref<string>('')
const showStatistics = ref(false)
const detailPopupRef = ref<InstanceType<typeof DetailPopup>>()

const queryParams = reactive({
    robot_id: 0,
    edit_type: '',
    is_auto_offline: '',
    start_time: '',
    end_time: ''
})

const dateRange = ref<string[]>([])

const statistics = reactive({
    total_edits: 0,
    auto_offline_count: 0,
    today_edits: 0,
    week_edits: 0
})

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: getRobotEditLogLists,
    params: queryParams
})

// 打开弹窗
const open = (id: number, name: string = '') => {
    robotId.value = id
    robotName.value = name
    queryParams.robot_id = id
    visible.value = true
    
    // 加载数据
    getLists()
    loadStatistics()
}

// 加载统计数据
const loadStatistics = async () => {
    try {
        const data = await getRobotEditLogStatistics({ robot_id: robotId.value })
        Object.assign(statistics, data)
    } catch (error) {
        console.error('加载统计数据失败:', error)
    }
}

// 处理日期范围变化
const handleDateChange = (dates: string[]) => {
    if (dates && dates.length === 2) {
        queryParams.start_time = dates[0]
        queryParams.end_time = dates[1]
    } else {
        queryParams.start_time = ''
        queryParams.end_time = ''
    }
}

// 查看详情
const viewDetail = (row: any) => {
    detailPopupRef.value?.open(row)
}

// 关闭弹窗
const handleClose = () => {
    visible.value = false
    emit('close')
}

// 暴露方法
defineExpose({
    open
})
</script>

<style scoped>
.stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}
</style>
