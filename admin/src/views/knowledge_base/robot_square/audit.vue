<template>
    <div class="audit-popup">
        <popup
            ref="popupRef"
            title="智能体审核"
            :async="true"
            width="800px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <!-- 智能体基本信息 -->
            <div v-if="robotInfo.robot_name" class="robot-info mb-4">
                <el-card shadow="never">
                    <div class="flex items-center">
                        <el-avatar
                            :src="robotInfo.robot_image"
                            :size="50"
                            class="mr-4"
                        >
                            <el-icon><User /></el-icon>
                        </el-avatar>
                        <div class="flex-1">
                            <h3 class="text-lg font-medium mb-1">{{ robotInfo.robot_name }}</h3>
                            <p class="text-gray-500 text-sm">{{ robotInfo.robot_intro || '暂无简介' }}</p>
                            <div class="flex items-center mt-2 text-sm text-gray-400">
                                <span>智能体ID: {{ robotInfo.robot_id }}</span>
                                <span class="mx-2">|</span>
                                <span>创建者: {{ robotInfo.user_nickname || robotInfo.user_account }}</span>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 编辑历史摘要 -->
            <div class="edit-history-summary mb-4">
                <el-card shadow="never">
                    <template #header>
                        <div class="flex justify-between items-center">
                            <span class="font-medium">编辑历史</span>
                            <el-button
                                type="primary"
                                link
                                @click="viewFullHistory"
                                :disabled="!robotInfo.robot_id"
                            >
                                查看完整历史
                            </el-button>
                        </div>
                    </template>

                    <div v-loading="historyLoading">
                        <div v-if="editHistory.length > 0">
                            <div class="mb-3">
                                <el-tag type="info" class="mr-2">
                                    总编辑次数: {{ editHistory.length }}
                                </el-tag>
                                <el-tag type="warning" class="mr-2">
                                    最近编辑: {{ lastEditTime }}
                                </el-tag>
                                <el-tag
                                    v-if="hasAutoOffline"
                                    type="danger"
                                >
                                    曾自动下架
                                </el-tag>
                            </div>

                            <!-- 最近3次编辑记录 -->
                            <div class="recent-edits">
                                <div
                                    v-for="edit in recentEdits"
                                    :key="edit.id"
                                    class="edit-item"
                                >
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center">
                                            <el-tag
                                                :type="edit.edit_type === 1 ? 'primary' : 'success'"
                                                size="small"
                                                class="mr-2"
                                            >
                                                {{ edit.edit_type_text }}
                                            </el-tag>
                                            <span class="text-sm">{{ edit.create_time_text }}</span>
                                        </div>
                                        <el-button
                                            type="primary"
                                            link
                                            size="small"
                                            @click="viewEditDetail(edit)"
                                        >
                                            查看详情
                                        </el-button>
                                    </div>
                                    <div v-if="edit.is_auto_offline" class="text-red-500 text-sm mt-1">
                                        <el-icon class="mr-1"><Warning /></el-icon>
                                        自动下架: {{ edit.offline_reason }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-else class="text-gray-500 text-center py-6">
                            <el-icon class="text-2xl mb-2"><Document /></el-icon>
                            <div>暂无编辑记录</div>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 审核表单 -->
            <el-form
                class="ls-form"
                ref="formRef"
                :rules="rules"
                :model="formData"
                label-width="90px"
            >
                <el-form-item label="审核结果" prop="verify_status">
                    <el-radio-group v-model="formData.verify_status">
                        <el-radio :label="1" size="large">审核通过</el-radio>
                        <el-radio :label="2" size="large">审核拒绝</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="拒绝原因" prop="verify_result" v-if="formData.verify_status == 2">
                    <el-input
                        v-model="formData.verify_result"
                        type="textarea"
                        :autosize="{ minRows: 8, maxRows: 20 }"
                        placeholder="请输入拒绝原因"
                    />
                </el-form-item>
            </el-form>
        </popup>

        <!-- 编辑历史详情弹窗 -->
        <edit-history-popup
            ref="editHistoryPopupRef"
            @close="editHistoryPopupRef?.close()"
        />

        <!-- 编辑详情弹窗 -->
        <edit-detail-popup
            ref="editDetailPopupRef"
            @close="editDetailPopupRef?.close()"
        />
    </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from 'element-plus'
import { User, Warning, Document } from '@element-plus/icons-vue'
import Popup from '@/components/popup/index.vue'
import EditHistoryPopup from './components/edit-history-popup.vue'
import EditDetailPopup from './components/edit-detail-popup.vue'
import { auditAgentSquare } from '@/api/knowledge_base/robot_square'
import { getRobotEditLogLists } from '@/api/knowledge_base/robot_edit_log'

const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const editHistoryPopupRef = shallowRef<InstanceType<typeof EditHistoryPopup>>()
const editDetailPopupRef = shallowRef<InstanceType<typeof EditDetailPopup>>()

// 表单数据
const formData = reactive({
    id: [] as number[],
    verify_status: 1,
    verify_result: ''
})

// 智能体信息
const robotInfo = ref<any>({})

// 编辑历史
const editHistory = ref<any[]>([])
const historyLoading = ref(false)

// 校验规则
const rules = {}

// 计算属性
const recentEdits = computed(() => editHistory.value.slice(0, 3))

const lastEditTime = computed(() => {
    return editHistory.value.length > 0
        ? editHistory.value[0].create_time_text
        : '无'
})

const hasAutoOffline = computed(() => {
    return editHistory.value.some(edit => edit.is_auto_offline)
})

// 获取编辑历史
const getEditHistory = async (robotId: number) => {
    if (!robotId) return

    try {
        historyLoading.value = true
        console.log('🔍 [审核弹窗] 获取编辑历史，智能体ID:', robotId)

        const response = await getRobotEditLogLists({
            robot_id: robotId,
            page_size: 10,
            page_no: 1
        })

        editHistory.value = response.lists || []
        console.log('📊 [审核弹窗] 编辑历史数据:', editHistory.value)
    } catch (error) {
        console.error('❌ [审核弹窗] 获取编辑历史失败:', error)
        editHistory.value = []
    } finally {
        historyLoading.value = false
    }
}

// 查看完整历史
const viewFullHistory = () => {
    if (!robotInfo.value.robot_id) {
        ElMessage.warning('智能体信息不完整，无法查看历史记录')
        return
    }
    editHistoryPopupRef.value?.open(robotInfo.value.robot_id, robotInfo.value.robot_name)
}

// 查看编辑详情
const viewEditDetail = (edit: any) => {
    editDetailPopupRef.value?.open(edit.id)
}

// 提交审核
const handleSubmit = async () => {
    try {
        await formRef.value?.validate()
        console.log('📝 [审核弹窗] 提交审核数据:', formData)

        await auditAgentSquare(formData)

        ElMessage.success('审核完成')
        popupRef.value?.close()
        emit('success')
    } catch (error) {
        console.error('❌ [审核弹窗] 审核提交失败:', error)
    }
}

const handleClose = () => {
    // 重置数据
    robotInfo.value = {}
    editHistory.value = []
    formData.verify_status = 1
    formData.verify_result = ''

    emit('close')
}

const open = (ids: number[], robot: any = {}) => {
    console.log('🚀 [审核弹窗] 打开审核弹窗')
    console.log('📋 [审核弹窗] 审核ID列表:', ids)
    console.log('🤖 [审核弹窗] 智能体信息:', robot)

    // 设置表单数据
    formData.id = ids
    formData.verify_status = 1
    formData.verify_result = ''

    // 设置智能体信息
    robotInfo.value = robot || {}

    // 获取编辑历史
    if (robot && robot.robot_id) {
        getEditHistory(robot.robot_id)
    } else {
        editHistory.value = []
    }

    popupRef.value?.open()
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.audit-popup {
    .robot-info {
        .el-card {
            border: 1px solid #e4e7ed;
        }
    }

    .edit-history-summary {
        .el-card {
            border: 1px solid #e4e7ed;
        }

        .edit-item {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background-color: #f8f9fa;
                margin: 0 -12px;
                padding: 12px;
                border-radius: 4px;
            }
        }

        .recent-edits {
            max-height: 200px;
            overflow-y: auto;
        }
    }
}

.ls-form {
    .el-form-item {
        margin-bottom: 20px;
    }
}
</style>
