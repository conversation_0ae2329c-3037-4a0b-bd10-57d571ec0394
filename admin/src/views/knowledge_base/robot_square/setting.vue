<template>
    <div class="draw-square-setting">
        <el-card shadow="never" class="!border-none mt-4">
            <div class="font-medium mb-7">智能体广场设置</div>
            <el-form ref="formRef" :model="formData" label-width="120px">
                <el-form-item label="显示用户信息">
                    <div>
                        <el-switch
                            v-model="formData.is_show_user"
                            :active-value="1"
                            :inactive-value="0"
                        />
                        <div class="form-tips">
                            开启后，前台智能体广场图片显示分享用户的信息，默认开启
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="审核设置">
                    <div>
                        <el-radio-group
                            v-model="formData.auto_audit"
                            :active-value="1"
                            :inactive-value="0"
                        >
                            <el-radio :label="1">自动通过审核</el-radio>
                            <el-radio :label="0">人工审核</el-radio>
                        </el-radio-group>
                        <div class="form-tips">
                            {{
                                formData.auto_audit ?
                                    '用户公开智能体，无需后台人工审核，系统自动通过' :
                                    '用户公开智能体，需要后台人工审核，审核通过后才显示在前台广场'
                            }}
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 分成收益设置 -->
        <el-card shadow="never" class="!border-none mt-4">
            <div class="font-medium mb-7">智能体分成收益设置</div>
            <el-form ref="revenueFormRef" :model="formData" label-width="120px">
                <el-form-item label="分成功能">
                    <div>
                        <el-switch
                            v-model="formData.revenue_enable"
                            :active-value="1"
                            :inactive-value="0"
                        />
                        <div class="form-tips">
                            开启后，用户使用广场智能体时，分享者可获得电力值分成收益
                        </div>
                    </div>
                </el-form-item>
                
                <template v-if="formData.revenue_enable">
                    <el-form-item label="分享者分成比例">
                        <div class="flex items-center">
                            <el-input-number
                                v-model="formData.share_ratio"
                                :min="0"
                                :max="100"
                                :precision="2"
                                :step="1"
                                style="width: 120px"
                            />
                            <span class="ml-2">%</span>
                            <div class="form-tips ml-4">
                                分享者获得的分成比例，剩余部分为平台保留
                            </div>
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="平台保留比例">
                        <div class="flex items-center">
                            <span class="text-gray-600">{{ (100 - formData.share_ratio).toFixed(2) }}%</span>
                            <div class="form-tips ml-4">
                                平台保留的分成比例，自动计算
                            </div>
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="最小分成金额">
                        <div class="flex items-center">
                            <el-input-number
                                v-model="formData.min_revenue"
                                :min="0"
                                :precision="4"
                                :step="0.001"
                                style="width: 120px"
                            />
                            <span class="ml-2">电力值</span>
                            <div class="form-tips ml-4">
                                单次对话消耗低于此值时不进行分成
                            </div>
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="结算方式">
                        <div>
                            <el-radio-group v-model="formData.settle_type">
                                <el-radio :label="1">实时结算</el-radio>
                                <el-radio :label="2">每日结算</el-radio>
                            </el-radio-group>
                            <div class="form-tips">
                                {{
                                    formData.settle_type === 1 ?
                                        '用户使用智能体后，立即给分享者分配收益' :
                                        '每日定时批量结算分成收益（需配置定时任务）'
                                }}
                            </div>
                        </div>
                    </el-form-item>
                </template>
            </el-form>
        </el-card>

        <footer-btns v-perms="['kb.square/setConfig']">
            <el-button type="primary" @click="handleSubmit">保存</el-button>
        </footer-btns>
    </div>
</template>

<script lang="ts" setup name="robotSquareSetting">
import { getRobotSquareConfig, setRobotSquareConfig } from '@/api/knowledge_base/robot_square'

const formData = reactive<{
    is_show_user: number
    auto_audit: number
    revenue_enable: number
    share_ratio: number
    platform_ratio: number
    min_revenue: number
    settle_type: number
}>({
    is_show_user: 0,
    auto_audit: 0,
    revenue_enable: 0,
    share_ratio: 30.00,
    platform_ratio: 70.00,
    min_revenue: 0.01,
    settle_type: 1
})

const getData = async () => {
    const data = await getRobotSquareConfig()
    Object.keys(formData).map((item) => {
        //@ts-ignore
        formData[item] = data[item]
    })
}

const handleSubmit = async () => {
    await setRobotSquareConfig(formData)
    getData()
}

getData()
</script>

<style lang="scss" scoped></style>
