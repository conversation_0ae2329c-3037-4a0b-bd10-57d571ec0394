<template>
    <popup
        ref="popupRef"
        title="编辑详情"
        width="1000px"
        :show-footer="false"
        @close="$emit('close')"
    >
        <div v-loading="loading">
            <el-descriptions :column="2" border>
                <el-descriptions-item label="日志ID">
                    {{ detail.id }}
                </el-descriptions-item>
                <el-descriptions-item label="编辑时间">
                    {{ detail.create_time_text }}
                </el-descriptions-item>
                <el-descriptions-item label="智能体">
                    <div class="flex items-center">
                        <el-avatar
                            v-if="detail.robot_image"
                            :src="detail.robot_image"
                            :size="32"
                            class="mr-2"
                        >
                            <el-icon><User /></el-icon>
                        </el-avatar>
                        <div>
                            <div class="font-medium">{{ detail.robot_name || '已删除' }}</div>
                            <div class="text-xs text-gray-500">ID: {{ detail.robot_id }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="操作用户">
                    <div class="flex items-center">
                        <el-avatar
                            v-if="detail.user_avatar"
                            :src="detail.user_avatar"
                            :size="32"
                            class="mr-2"
                        >
                            <el-icon><User /></el-icon>
                        </el-avatar>
                        <div>
                            <div class="font-medium">{{ detail.user_nickname || detail.user_account }}</div>
                            <div class="text-xs text-gray-500">{{ detail.user_account }}</div>
                        </div>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="编辑类型">
                    <el-tag :type="detail.edit_type === 1 ? 'primary' : 'success'">
                        {{ detail.edit_type_text }}
                    </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="是否自动下架">
                    <el-tag :type="detail.is_auto_offline ? 'danger' : 'success'">
                        {{ detail.is_auto_offline_text }}
                    </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="下架原因" :span="2">
                    <span v-if="detail.offline_reason" class="text-red-500">
                        {{ detail.offline_reason }}
                    </span>
                    <span v-else class="text-gray-400">无</span>
                </el-descriptions-item>
            </el-descriptions>

            <!-- 编辑前后数据对比 -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4">编辑数据对比</h3>
                
                <el-row :gutter="20">
                    <!-- 编辑前数据 -->
                    <el-col :span="12">
                        <el-card shadow="never" class="h-full">
                            <template #header>
                                <div class="card-header">
                                    <span>编辑前数据</span>
                                    <el-tag type="info" size="small">Before</el-tag>
                                </div>
                            </template>
                            <div v-if="detail.before_data">
                                <pre class="data-content">{{ formatJsonData(detail.before_data) }}</pre>
                            </div>
                            <div v-else class="text-gray-500 text-center py-8">
                                <el-icon class="text-2xl mb-2"><Document /></el-icon>
                                <div>无编辑前数据</div>
                            </div>
                        </el-card>
                    </el-col>
                    
                    <!-- 编辑后数据 -->
                    <el-col :span="12">
                        <el-card shadow="never" class="h-full">
                            <template #header>
                                <div class="card-header">
                                    <span>编辑后数据</span>
                                    <el-tag type="success" size="small">After</el-tag>
                                </div>
                            </template>
                            <div v-if="detail.after_data">
                                <pre class="data-content">{{ formatJsonData(detail.after_data) }}</pre>
                            </div>
                            <div v-else class="text-gray-500 text-center py-8">
                                <el-icon class="text-2xl mb-2"><Document /></el-icon>
                                <div>无编辑后数据</div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </div>

            <!-- 智能体简介 -->
            <div v-if="detail.robot_intro" class="mt-6">
                <h3 class="text-lg font-medium mb-4">智能体简介</h3>
                <el-card shadow="never">
                    <p class="text-gray-700 leading-relaxed">{{ detail.robot_intro }}</p>
                </el-card>
            </div>
        </div>
    </popup>
</template>

<script lang="ts" setup>
import { User, Document } from '@element-plus/icons-vue'
import Popup from '@/components/popup/index.vue'
import { getRobotEditLogDetail } from '@/api/knowledge_base/robot_edit_log'

const emit = defineEmits(['close'])

const popupRef = shallowRef<InstanceType<typeof Popup>>()
const loading = ref(false)

const detail = ref<any>({})

// 格式化JSON数据显示
const formatJsonData = (data: any) => {
    if (!data) return ''
    try {
        if (typeof data === 'string') {
            // 尝试解析字符串为JSON
            const parsed = JSON.parse(data)
            return JSON.stringify(parsed, null, 2)
        }
        return JSON.stringify(data, null, 2)
    } catch (error) {
        return String(data)
    }
}

// 获取详情
const getDetail = async (id: number) => {
    try {
        loading.value = true
        console.log('🔍 [编辑详情弹窗] 获取详情，ID:', id)
        
        const data = await getRobotEditLogDetail({ id })
        detail.value = data
        
        console.log('📊 [编辑详情弹窗] 详情数据:', detail.value)
    } catch (error) {
        console.error('❌ [编辑详情弹窗] 获取详情失败:', error)
        ElMessage.error('获取详情失败')
    } finally {
        loading.value = false
    }
}

// 打开弹窗
const open = (id: number) => {
    detail.value = {}
    popupRef.value?.open()
    getDetail(id)
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.data-content {
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px;
    font-size: 12px;
    line-height: 1.5;
    color: #606266;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.el-descriptions {
    :deep(.el-descriptions__body) {
        background-color: #fafafa;
    }
}

.el-card {
    border: 1px solid #e4e7ed;
    
    :deep(.el-card__header) {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e4e7ed;
    }
}
</style>
