<template>
    <div class="container">
        <el-card class="box-card !border-none" shadow="never">
            <el-form ref="formRef" :model="queryParams" inline>
                <el-form-item label="模板名称">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入模板名称"
                        clearable
                        @keyup.enter="resetPage"
                    />
                </el-form-item>
                <el-form-item label="所属类别">
                    <el-select
                        v-model="queryParams.category_id"
                        placeholder="请选择所属类别"
                        clearable
                    >
                        <el-option
                            v-for="item in categoryList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="文件类型">
                    <el-select
                        v-model="queryParams.file_type"
                        placeholder="请选择文件类型"
                        clearable
                    >
                        <el-option label="Word文档(docx)" value="docx" />
                        <el-option label="Excel表格(xlsx)" value="xlsx" />
                        <el-option label="PDF文档(pdf)" value="pdf" />
                        <el-option label="文本文件(txt)" value="txt" />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select
                        v-model="queryParams.status"
                        placeholder="请选择状态"
                        clearable
                    >
                        <el-option label="开启" :value="1" />
                        <el-option label="关闭" :value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card class="box-card !border-none mt-4" shadow="never">
            <div class="mb-4 flex justify-between">
                <div>
                    <el-button
                        v-perms="['kb.template/add']"
                        type="primary"
                        @click="handleAdd"
                    >
                        <template #icon>
                            <icon name="el-icon-Plus" />
                        </template>
                        新增
                    </el-button>
                </div>
            </div>

            <el-table
                v-loading="pager.loading"
                :data="pager.lists"
                :row-style="{ cursor: 'pointer' }"
                height="580"
            >
                <el-table-column label="模板名称" prop="name" min-width="160" />
                <el-table-column label="所属类别" prop="category_name" min-width="120" />
                <el-table-column label="模板描述" min-width="200">
                    <template #default="{ row }">
                        <div class="line-clamp-2">{{ row.description || '--' }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="文件类型" prop="file_type" min-width="100" />
                <el-table-column label="文件大小" prop="file_size" min-width="100" />
                <el-table-column label="下载次数" prop="download_count" min-width="100" />
                <el-table-column label="排序" prop="sort" min-width="80" />
                <el-table-column label="状态" min-width="80">
                    <template #default="{ row }">
                        <el-switch
                            v-model="row.status"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleStatus(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="create_time" min-width="160" />
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['kb.template/edit']"
                            type="primary"
                            link
                            @click="handleEdit(row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            type="success"
                            link
                            @click="handlePreview(row)"
                        >
                            预览
                        </el-button>
                        <el-button
                            v-perms="['kb.template/del']"
                            type="danger"
                            link
                            @click="handleDelete(row)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>

        <edit-popup
            v-if="showEdit"
            ref="editRef"
            @success="getLists"
            @close="showEdit = false"
        />
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, shallowRef, nextTick } from 'vue'
import { getTemplateList, deleteTemplate, updateTemplateStatus } from '@/api/knowledge_base/template'
import { getTemplateCategoryList } from '@/api/knowledge_base/template'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'

const queryParams = reactive({
    name: '',
    category_id: '',
    file_type: '',
    status: '',
    page: 1
})

const pager = reactive<{
    page: number
    pageSize: number
    loading: boolean
    total: number
    lists: any[]
}>({
    page: 1,
    pageSize: 10,
    loading: true,
    total: 0,
    lists: []
})

const showEdit = ref(false)
const editRef = shallowRef<InstanceType<typeof EditPopup>>()
const categoryList = ref<any[]>([])

// 获取数据
const getLists = async () => {
    try {
        pager.loading = true
        const data = await getTemplateList({
            ...queryParams,
            page_no: pager.page,
            page_size: pager.pageSize
        })
        pager.lists = data.lists
        pager.total = data.count
    } catch (error) {
        console.error(error)
    } finally {
        pager.loading = false
    }
}

// 获取类别列表
const getCategoryList = async () => {
    try {
        const res = await getTemplateCategoryList()
        categoryList.value = res.lists || []
    } catch (error) {
        console.error('获取类别列表失败', error)
    }
}

// 重置分页
const resetPage = () => {
    pager.page = 1
    getLists()
}

// 重置查询参数
const resetParams = () => {
    queryParams.name = ''
    queryParams.category_id = ''
    queryParams.file_type = ''
    queryParams.status = ''
    resetPage()
}

// 打开弹框
const openPop = async (type: string, value: any = {}) => {
    showEdit.value = true
    // 使用 nextTick 确保 DOM 更新完成后再调用 open 方法
    await nextTick()
    editRef.value?.open(type, value)
}

// 新增
const handleAdd = () => {
    openPop('add')
}

// 编辑
const handleEdit = (row: any) => {
    openPop('edit', row)
}

// 删除
const handleDelete = async (row: any) => {
    await feedback.confirm('确定要删除该模板吗？')
    await deleteTemplate({ id: row.id })
    feedback.msgSuccess('删除成功')
    getLists()
}

// 修改状态
const handleStatus = async (row: any) => {
    await updateTemplateStatus({
        id: row.id,
        status: row.status
    })
    feedback.msgSuccess('修改成功')
}

// 预览模板
const handlePreview = (row: any) => {
    if (row.download_url) {
        window.open(row.download_url, '_blank')
    } else {
        feedback.msgError('该模板暂无下载链接')
    }
}

onMounted(() => {
    getLists()
    getCategoryList()
})
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style> 