<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="700px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form
                class="ls-form"
                ref="formRef"
                :rules="rules"
                :model="formData"
                label-width="90px"
            >
                <el-form-item label="所属类别" prop="category_id">
                    <el-select
                        class="w-full"
                        placeholder="请选择所属类别"
                        v-model="formData.category_id"
                    >
                        <el-option
                            v-for="item in categoryList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="模板名称" prop="name">
                    <el-input
                        class="ls-input"
                        v-model="formData.name"
                        placeholder="请输入模板名称"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="模板描述">
                    <el-input
                        v-model="formData.description"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 5 }"
                        placeholder="请输入模板描述"
                    />
                </el-form-item>
                <el-form-item label="下载链接" prop="download_url">
                    <el-input
                        class="ls-input"
                        v-model="formData.download_url"
                        placeholder="请输入模板下载链接地址"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="文件大小">
                    <el-input
                        class="ls-input"
                        v-model="formData.file_size"
                        placeholder="例如：2.5MB"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="文件类型">
                    <el-input
                        class="ls-input"
                        v-model="formData.file_type"
                        placeholder="例如：docx, xlsx, pdf"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="排序">
                    <div>
                        <el-input-number v-model="formData.sort" :min="0" :max="9999" />
                        <div class="form-tips">默认为0，数值越大排越前面</div>
                    </div>
                </el-form-item>
                <el-form-item label="状态">
                    <el-switch v-model="formData.status" :active-value="1" :inactive-value="0" />
                </el-form-item>
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup>
import { ref, shallowRef, nextTick } from 'vue'
import Popup from '@/components/popup/index.vue'
import { addTemplate, editTemplate } from '@/api/knowledge_base/template'
import { getTemplateCategoryList } from '@/api/knowledge_base/template'
import feedback from '@/utils/feedback'

const emit = defineEmits(['success', 'close'])

const formRef = shallowRef()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const popupTitle = ref('')
const categoryList = ref<any[]>([])

const formData: any = ref({
    id: '',
    category_id: '',
    name: '',
    description: '',
    download_url: '',
    file_size: '',
    file_type: '',
    sort: 0,
    status: 1
})

const rules = {
    category_id: [
        {
            required: true,
            message: '请选择所属类别',
            trigger: ['change']
        }
    ],
    name: [
        {
            required: true,
            message: '请输入模板名称',
            trigger: ['blur']
        }
    ],
    download_url: [
        {
            required: true,
            message: '请输入下载链接地址',
            trigger: ['blur']
        },
        {
            type: 'url',
            message: '请输入正确的链接地址',
            trigger: ['blur']
        }
    ]
}

const getCategoryList = async () => {
    try {
        const res = await getTemplateCategoryList()
        categoryList.value = res.lists || []
    } catch (error) {
        console.error('获取类别列表失败', error)
    }
}

const handleSubmit = async () => {
    try {
        await formRef.value?.validate()
        
        const submitData: any = {
            category_id: Number(formData.value.category_id),
            name: formData.value.name,
            description: formData.value.description,
            download_url: formData.value.download_url,
            file_size: formData.value.file_size,
            file_type: formData.value.file_type,
            sort: Number(formData.value.sort),
            status: Number(formData.value.status)
        }
        
        if (!formData.value.id || formData.value.id === 0) {
            await addTemplate(submitData)
            feedback.msgSuccess('添加成功')
        } else {
            submitData.id = Number(formData.value.id)
            await editTemplate(submitData)
            feedback.msgSuccess('编辑成功')
        }
        
        popupRef.value?.close()
        emit('success')
    } catch (error) {
        return error
    }
}

const handleClose = () => {
    emit('close')
}

const open = (type: string, value: any) => {
    getCategoryList()
    
    if (type == 'add') {
        formData.value = {
            id: '',
            category_id: '',
            name: '',
            description: '',
            download_url: '',
            file_size: '',
            file_type: '',
            sort: 0,
            status: 1
        }
        popupTitle.value = '新增模板'
    } else if (type == 'edit') {
        formData.value = {
            id: value.id ? Number(value.id) : 0,
            category_id: value.category_id ? Number(value.category_id) : '',
            name: value.name || '',
            description: value.description || '',
            download_url: value.download_url || '',
            file_size: value.file_size || '',
            file_type: value.file_type || '',
            sort: value.sort !== undefined ? Number(value.sort) : 0,
            status: value.status !== undefined ? Number(value.status) : 1
        }
        popupTitle.value = '编辑模板'
    }
    
    nextTick(() => {
        popupRef.value?.open()
    })
}

defineExpose({
    open
})
</script> 