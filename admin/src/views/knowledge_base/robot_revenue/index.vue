<template>
    <div class="edit-popup">
        <div class="flex justify-between mb-4">
            <div class="text-xl font-bold">智能体分成收益管理</div>
        </div>

        <!-- 统计数据 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <el-card class="statistics-card">
                <div class="stat-item">
                    <div class="stat-value text-blue-600">{{ statistics.total.count }}</div>
                    <div class="stat-label">总分成次数</div>
                </div>
            </el-card>
            <el-card class="statistics-card">
                <div class="stat-item">
                    <div class="stat-value text-green-600">{{ formatAmount(statistics.total.share_amount) }}</div>
                    <div class="stat-label">累计分成收益</div>
                </div>
            </el-card>
            <el-card class="statistics-card">
                <div class="stat-item">
                    <div class="stat-value text-purple-600">{{ formatAmount(statistics.total.platform_amount) }}</div>
                    <div class="stat-label">平台累计收益</div>
                </div>
            </el-card>
            <el-card class="statistics-card">
                <div class="stat-item">
                    <div class="stat-value text-orange-600">{{ statistics.pending.count }}</div>
                    <div class="stat-label">待结算笔数</div>
                </div>
            </el-card>
        </div>

        <!-- 今日统计 -->
        <div class="bg-blue-50 p-4 rounded-lg mb-6">
            <div class="text-sm font-medium text-blue-800 mb-2">今日统计</div>
            <div class="grid grid-cols-4 gap-4 text-sm">
                <div>
                    <span class="text-gray-600">分成次数：</span>
                    <span class="font-medium">{{ statistics.today.count }}</span>
                </div>
                <div>
                    <span class="text-gray-600">分成收益：</span>
                    <span class="font-medium">{{ formatAmount(statistics.today.share_amount) }}</span>
                </div>
                <div>
                    <span class="text-gray-600">平台收益：</span>
                    <span class="font-medium">{{ formatAmount(statistics.today.platform_amount) }}</span>
                </div>
                <div>
                    <span class="text-gray-600">总消耗：</span>
                    <span class="font-medium">{{ formatAmount(statistics.today.total_cost) }}</span>
                </div>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <el-card shadow="never" class="!border-none mb-4">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
                <el-form-item label="使用者昵称">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.user_nickname"
                        placeholder="请输入使用者昵称"
                        clearable
                        @keyup.enter="resetPage"
                    />
                </el-form-item>
                <el-form-item label="分享者昵称">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.sharer_nickname"
                        placeholder="请输入分享者昵称"
                        clearable
                        @keyup.enter="resetPage"
                    />
                </el-form-item>
                <el-form-item label="智能体名称">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.robot_name"
                        placeholder="请输入智能体名称"
                        clearable
                        @keyup.enter="resetPage"
                    />
                </el-form-item>
                <el-form-item label="结算状态">
                    <el-select 
                        class="w-[280px]"
                        v-model="queryParams.settle_status"
                        placeholder="请选择结算状态"
                        clearable
                    >
                        <el-option label="待结算" :value="0" />
                        <el-option label="已结算" :value="1" />
                    </el-select>
                </el-form-item>
                <el-form-item label="创建时间">
                    <daterange-picker
                        v-model:startTime="queryParams.start_time"
                        v-model:endTime="queryParams.end_time"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                    <el-button 
                        type="success" 
                        @click="batchSettle"
                        :loading="settleLoading"
                        v-if="statistics.pending.count > 0"
                    >
                        批量结算({{ statistics.pending.count }}笔)
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 分成记录列表 -->
        <el-card shadow="never" class="!border-none">
            <el-table
                class="mt-4"
                :data="pager.lists"
                v-loading="pager.loading"
            >
                <el-table-column label="使用者" min-width="120">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-avatar 
                                :size="30" 
                                :src="row.user_avatar" 
                                class="mr-2"
                            />
                            <span class="text-sm">{{ row.user_nickname || '未知用户' }}</span>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="分享者" min-width="120">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-avatar 
                                :size="30" 
                                :src="row.sharer_avatar" 
                                class="mr-2"
                            />
                            <span class="text-sm">{{ row.sharer_nickname || '未知用户' }}</span>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="智能体" min-width="150">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-avatar 
                                :size="30" 
                                :src="row.robot_image" 
                                class="mr-2"
                                shape="square"
                            />
                            <span class="text-sm">{{ row.robot_name || '未知智能体' }}</span>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="费用明细" min-width="180">
                    <template #default="{ row }">
                        <div class="text-xs space-y-1">
                            <div>
                                <span class="text-gray-500">总消耗：</span>
                                <span class="font-medium">{{ formatAmount(row.total_cost) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">分成收益：</span>
                                <span class="text-green-600 font-medium">{{ formatAmount(row.share_amount) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">平台保留：</span>
                                <span class="text-blue-600 font-medium">{{ formatAmount(row.platform_amount) }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="分成比例" width="80">
                    <template #default="{ row }">
                        <span class="text-sm">{{ row.share_ratio }}%</span>
                    </template>
                </el-table-column>

                <el-table-column label="结算状态" width="80">
                    <template #default="{ row }">
                        <el-tag 
                            :type="row.settle_status ? 'success' : 'warning'"
                            size="small"
                        >
                            {{ row.settle_status_text }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="创建时间" width="150">
                    <template #default="{ row }">
                        <span class="text-sm">{{ row.create_time_text }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="结算时间" width="150">
                    <template #default="{ row }">
                        <span class="text-sm">{{ row.settle_time_text }}</span>
                    </template>
                </el-table-column>
            </el-table>

            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup name="robotRevenue">
import { usePaging } from '@/hooks/usePaging'
import { robotRevenueApi } from '@/api/knowledge_base/robot_revenue'
import { ref, reactive, onMounted } from 'vue'
import ElMessage from 'element-plus'
import ElMessageBox from 'element-plus'

// 查询参数
const queryParams = reactive({
    user_nickname: '',
    sharer_nickname: '',
    robot_name: '',
    settle_status: '',
    start_time: '',
    end_time: ''
})

// 分页数据
const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: robotRevenueApi.lists,
    params: queryParams
})

// 统计数据
const statistics = ref({
    total: {
        count: 0,
        total_cost: 0,
        share_amount: 0,
        platform_amount: 0
    },
    today: {
        count: 0,
        total_cost: 0,
        share_amount: 0,
        platform_amount: 0
    },
    pending: {
        count: 0,
        amount: 0
    }
})

// 批量结算状态
const settleLoading = ref(false)

// 格式化金额显示
const formatAmount = (amount: number) => {
    return Number(amount || 0).toFixed(4)
}

// 获取统计数据
const getStatistics = async () => {
    try {
        const data = await robotRevenueApi.statistics()
        statistics.value = data
    } catch (error) {
        console.error('获取统计数据失败:', error)
    }
}

// 批量结算
const batchSettle = async () => {
    try {
        await ElMessageBox.confirm(
            `确定要批量结算 ${statistics.value.pending.count} 笔待结算记录吗？`,
            '批量结算确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        settleLoading.value = true
        await robotRevenueApi.batchSettle()
        
        ElMessage.success('批量结算成功')
        
        // 刷新数据
        await Promise.all([
            getLists(),
            getStatistics()
        ])
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量结算失败:', error)
        }
    } finally {
        settleLoading.value = false
    }
}

onMounted(() => {
    getLists()
    getStatistics()
})
</script>

<style lang="scss" scoped>
.statistics-card {
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    .stat-item {
        text-align: center;
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #666;
        }
    }
}
</style> 