<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="700px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form
                class="ls-form"
                ref="formRef"
                :rules="rules"
                :model="formData"
                label-width="90px"
            >
                <el-form-item label="所属类别" prop="category_id">
                    <el-select
                        class="w-full"
                        placeholder="请选择所属类别"
                        v-model="formData.category_id"
                    >
                        <el-option
                            v-for="item in categoryList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="示例标题" prop="title">
                    <el-input
                        class="ls-input"
                        v-model="formData.title"
                        placeholder="请输入示例标题"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="问题内容" prop="question">
                    <el-input
                        v-model="formData.question"
                        type="textarea"
                        :autosize="{ minRows: 4, maxRows: 6 }"
                        placeholder="请输入问题内容"
                    />
                </el-form-item>
                <el-form-item label="答案内容" prop="answer">
                    <el-input
                        v-model="formData.answer"
                        type="textarea"
                        :autosize="{ minRows: 4, maxRows: 6 }"
                        placeholder="请输入答案内容"
                    />
                </el-form-item>
                <el-form-item label="排序">
                    <div>
                        <el-input-number v-model="formData.sort" :min="0" :max="9999" />
                        <div class="form-tips">默认为0，数值越大排越前面</div>
                    </div>
                </el-form-item>
                <el-form-item label="状态">
                    <el-switch v-model="formData.status" :active-value="1" :inactive-value="0" />
                </el-form-item>
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup>
import { ref, shallowRef, nextTick } from 'vue'
import Popup from '@/components/popup/index.vue'
import { addContent, editContent } from '@/api/knowledge_base/example_content'
import { getCategorySelectList } from '@/api/knowledge_base/example_category'
import feedback from '@/utils/feedback'

const emit = defineEmits(['success', 'close'])

const formRef = shallowRef()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const popupTitle = ref('')
const categoryList = ref<any[]>([])

const formData: any = ref({
    id: '',
    category_id: '',
    title: '',
    question: '',
    answer: '',
    sort: 0,
    status: 1
})

const rules = {
    category_id: [
        {
            required: true,
            message: '请选择所属类别',
            trigger: ['change']
        }
    ],
    title: [
        {
            required: true,
            message: '请输入示例标题',
            trigger: ['blur']
        }
    ],
    question: [
        {
            required: true,
            message: '请输入问题内容',
            trigger: ['blur']
        }
    ],
    answer: [
        {
            required: true,
            message: '请输入答案内容',
            trigger: ['blur']
        }
    ]
}

const getCategoryList = async () => {
    try {
        const res = await getCategorySelectList()
        categoryList.value = res.lists || []
    } catch (error) {
        console.error('获取类别列表失败', error)
    }
}

const handleSubmit = async () => {
    try {
        await formRef.value?.validate()
        
        const submitData: any = {
            category_id: Number(formData.value.category_id),
            title: formData.value.title,
            question: formData.value.question,
            answer: formData.value.answer,
            sort: Number(formData.value.sort),
            status: Number(formData.value.status)
        }
        
        if (!formData.value.id || formData.value.id === 0) {
            await addContent(submitData)
            feedback.msgSuccess('添加成功')
        } else {
            submitData.id = Number(formData.value.id)
            await editContent(submitData)
            feedback.msgSuccess('编辑成功')
        }
        
        popupRef.value?.close()
        emit('success')
    } catch (error) {
        return error
    }
}

const handleClose = () => {
    emit('close')
}

const open = (type: string, value: any) => {
    getCategoryList()
    
    if (type == 'add') {
        formData.value = {
            id: '',
            category_id: '',
            title: '',
            question: '',
            answer: '',
            sort: 0,
            status: 1
        }
        popupTitle.value = '新增示例内容'
    } else if (type == 'edit') {
        formData.value = {
            id: value.id ? Number(value.id) : 0,
            category_id: value.category_id ? Number(value.category_id) : '',
            title: value.title || '',
            question: value.question || '',
            answer: value.answer || '',
            sort: value.sort !== undefined ? Number(value.sort) : 0,
            status: value.status !== undefined ? Number(value.status) : 1
        }
        popupTitle.value = '编辑示例内容'
    }
    
    nextTick(() => {
        popupRef.value?.open()
    })
}

defineExpose({
    open
})
</script> 