import { reactive, toRaw } from 'vue'

// 分页钩子函数
interface Options {
    page?: number
    size?: number
    fetchFun: (_arg: any) => Promise<any>
    params?: Record<any, any>
    firstLoading?: boolean
}

export function usePaging(options: Options) {
    const { page = 1, size = 15, fetchFun, params = {}, firstLoading = false } = options
    // 记录分页初始参数
    const paramsInit: Record<any, any> = Object.assign({}, toRaw(params))
    // 分页数据
    const pager = reactive({
        page,
        size,
        loading: firstLoading,
        count: 0,
        lists: [] as any[],
        extend: {} as Record<string, any>
    })
    // 请求分页接口
    const getLists = () => {
        pager.loading = true
        return fetchFun({
            page_no: pager.page,
            page_size: pager.size,
            ...params
        })
            .then((res: any) => {
                // 兼容后端返回数据结构 { code, msg, data: { list, count } }
                if (res && res.data && typeof res.data === 'object') {
                    pager.count = res.data?.count || 0
                    // 优先使用list字段，兼容不同的命名
                    pager.lists = res.data?.list || res.data?.lists || []
                    pager.extend = res.data?.extend || {}
                    console.log('处理data嵌套数据:', { count: pager.count, lists: pager.lists.length })
                    return Promise.resolve(res.data)
                } else {
                    // 兼容旧的直接返回 { lists, count } 的结构
                    pager.count = res?.count || 0
                    pager.lists = res?.lists || res?.list || []
                    pager.extend = res?.extend || {}
                    console.log('处理直接返回数据:', { count: pager.count, lists: pager.lists.length })
                    return Promise.resolve(res)
                }
            })
            .catch((err: any) => {
                console.error('获取列表数据失败:', err)
                return Promise.reject(err)
            })
            .finally(() => {
                pager.loading = false
            })
    }
    // 重置为第一页
    const resetPage = () => {
        pager.page = 1
        getLists()
    }
    // 重置参数
    const resetParams = () => {
        Object.keys(paramsInit).forEach((item) => {
            params[item] = paramsInit[item]
        })
        getLists()
    }
    return {
        pager,
        getLists,
        resetParams,
        resetPage
    }
}
