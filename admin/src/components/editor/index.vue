<template>
    <div class="border border-br flex flex-col" :style="styles">
        <!-- HTML编辑模式切换 -->
        <div v-if="allowHtml" class="flex items-center justify-between p-2 bg-gray-50 border-b">
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">编辑模式：</span>
                <el-radio-group v-model="editMode" size="small">
                    <el-radio-button label="visual">可视化编辑</el-radio-button>
                    <el-radio-button label="code">HTML源码</el-radio-button>
                </el-radio-group>
                <!-- HTML功能提示 -->
                <el-tag type="success" size="small">🎯 支持所有HTML标签</el-tag>
            </div>
            <div class="flex items-center space-x-2">
                <el-button 
                    size="small" 
                    type="primary" 
                    link 
                    @click="insertCommonHtml"
                    v-if="editMode === 'visual'"
                >
                    插入HTML模板
                </el-button>
                <el-button 
                    size="small" 
                    type="warning" 
                    link 
                    @click="formatHtml"
                    v-if="editMode === 'code'"
                >
                    格式化代码
                </el-button>
                <!-- HTML验证功能 -->
                <el-button 
                    v-if="editMode === 'code'"
                    size="small" 
                    type="success" 
                    link 
                    @click="validateHtml"
                >
                    验证HTML
                </el-button>
            </div>
        </div>
        
        <!-- 可视化编辑器 -->
        <div v-show="editMode === 'visual'" class="flex-1 flex flex-col">
            <toolbar
                class="border-b border-br"
                :editor="editorRef"
                :defaultConfig="computedToolbarConfig"
                :mode="mode"
            />
            <w-editor
                class="overflow-y-auto flex-1"
                v-model="valueHtml"
                :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="handleCreated"
            />
        </div>
        
        <!-- HTML源码编辑器 -->
        <div v-show="editMode === 'code'" class="flex-1">
            <el-input
                v-model="valueHtml"
                type="textarea"
                :rows="20"
                class="h-full"
                placeholder="请输入HTML代码..."
                style="font-family: 'Courier New', Courier, monospace;"
            />
        </div>
        
        <material-picker
            ref="materialPickerRef"
            :type="fileType"
            :limit="-1"
            hidden-upload
            @change="selectChange"
        />
        
        <!-- HTML模板插入弹窗 -->
        <el-dialog v-model="showHtmlDialog" title="插入HTML模板" width="600px">
            <div class="space-y-4">
                <div>
                    <h4 class="mb-2">常用模板：</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <el-button 
                            v-for="template in htmlTemplates" 
                            :key="template.name"
                            @click="insertTemplate(template.html)"
                            size="small"
                        >
                            {{ template.name }}
                        </el-button>
                    </div>
                </div>
                <div>
                    <h4 class="mb-2">自定义HTML：</h4>
                    <el-input
                        v-model="customHtml"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入自定义HTML代码..."
                        style="font-family: 'Courier New', Courier, monospace;"
                    />
                </div>
            </div>
            <template #footer>
                <el-button @click="showHtmlDialog = false">取消</el-button>
                <el-button type="primary" @click="insertCustomHtml">插入自定义HTML</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { Editor as WEditor, Toolbar } from '@wangeditor/editor-for-vue'
import type { IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import MaterialPicker from '@/components/material/picker.vue'
import { addUnit } from '@/utils/util'
import type { CSSProperties } from 'vue'

const props = withDefaults(
    defineProps<{
        modelValue?: string
        mode?: 'default' | 'simple'
        height?: string | number
        width?: string | number
        toolbarConfig?: Partial<IToolbarConfig>
        // 是否允许HTML代码（当allowedTags为空数组时，允许所有标签）
        allowHtml?: boolean
        // 自定义允许的HTML标签（为空时表示允许所有标签）
        allowedTags?: string[]
    }>(),
    {
        modelValue: '',
        mode: 'default',
        height: '100%',
        width: 'auto',
        toolbarConfig: () => ({}),
        allowHtml: false,
        allowedTags: () => []
    }
)

const emit = defineEmits<{
    (event: 'update:modelValue', value: string): void
}>()

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const materialPickerRef = shallowRef<InstanceType<typeof MaterialPicker>>()
const fileType = ref('')
const editMode = ref<'visual' | 'code'>('visual')
const showHtmlDialog = ref(false)
const customHtml = ref('')

let insertFn: any

// HTML模板库
const htmlTemplates = ref([
    // 基础模板
    {
        name: '通知公告',
        html: '<div style="background: #e7f3ff; border-left: 4px solid #409eff; padding: 16px; margin: 16px 0; border-radius: 4px;">' +
              '<h4 style="margin: 0 0 8px 0; color: #409eff;">📢 重要通知</h4>' +
              '<p style="margin: 0; color: #606266;">这里是公告内容，请根据需要修改。</p>' +
              '</div>'
    },
    {
        name: '提示信息',
        html: '<div style="background: #f0f9ff; border: 1px solid #409eff; padding: 16px; margin: 16px 0; border-radius: 4px;">' +
              '<p style="margin: 0; color: #409eff;"><strong>💡 温馨提示：</strong>这里是提示信息内容。</p>' +
              '</div>'
    },
    {
        name: '警告信息',
        html: '<div style="background: #fdf6ec; border: 1px solid #e6a23c; padding: 16px; margin: 16px 0; border-radius: 4px;">' +
              '<p style="margin: 0; color: #e6a23c;"><strong>⚠️ 注意：</strong>这里是警告信息内容。</p>' +
              '</div>'
    },
    {
        name: '成功信息',
        html: '<div style="background: #f0f9ff; border: 1px solid #67c23a; padding: 16px; margin: 16px 0; border-radius: 4px;">' +
              '<p style="margin: 0; color: #67c23a;"><strong>✅ 成功：</strong>操作已成功完成。</p>' +
              '</div>'
    },
    {
        name: '按钮组',
        html: '<div style="text-align: center; margin: 20px 0;">' +
              '<button style="background: #409eff; color: white; border: none; padding: 12px 24px; margin: 0 8px; border-radius: 4px; cursor: pointer;">主要按钮</button>' +
              '<button style="background: #67c23a; color: white; border: none; padding: 12px 24px; margin: 0 8px; border-radius: 4px; cursor: pointer;">成功按钮</button>' +
              '<button style="background: #e6a23c; color: white; border: none; padding: 12px 24px; margin: 0 8px; border-radius: 4px; cursor: pointer;">警告按钮</button>' +
              '</div>'
    },
    {
        name: '数据表格',
        html: '<table style="width: 100%; border-collapse: collapse; margin: 16px 0;">' +
              '<thead>' +
              '<tr style="background: #f8fafc;">' +
              '<th style="border: 1px solid #e2e8f0; padding: 12px; text-align: left;">列标题1</th>' +
              '<th style="border: 1px solid #e2e8f0; padding: 12px; text-align: left;">列标题2</th>' +
              '<th style="border: 1px solid #e2e8f0; padding: 12px; text-align: left;">列标题3</th>' +
              '</tr>' +
              '</thead>' +
              '<tbody>' +
              '<tr>' +
              '<td style="border: 1px solid #e2e8f0; padding: 12px;">数据1</td>' +
              '<td style="border: 1px solid #e2e8f0; padding: 12px;">数据2</td>' +
              '<td style="border: 1px solid #e2e8f0; padding: 12px;">数据3</td>' +
              '</tr>' +
              '</tbody>' +
              '</table>'
    },
    // 高级功能模板
        {
            name: '视频嵌入',
        html: '<div style="text-align: center; margin: 20px 0;">' +
              '<iframe width="560" height="315" src="https://www.youtube.com/embed/VIDEO_ID" frameborder="0" allowfullscreen style="max-width: 100%; border-radius: 8px;"></iframe>' +
              '<p style="margin-top: 8px; color: #666; font-size: 14px;">请替换VIDEO_ID为实际视频ID</p>' +
              '</div>'
        },
        {
            name: '外部网页嵌入',
        html: '<div style="border: 1px solid #ddd; border-radius: 8px; margin: 20px 0;">' +
              '<iframe src="https://example.com" width="100%" height="400" frameborder="0" style="border-radius: 8px;"></iframe>' +
              '</div>'
        },
        {
            name: '交互式内容',
        html: '<div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0;">' +
              '<h4 style="margin-top: 0;">交互式内容区域</h4>' +
              '<button onclick="alert(\'这是一个交互按钮！\')" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">点击测试</button>' +
              '<p style="margin-top: 10px; color: #666; font-size: 14px;">此区域支持JavaScript交互功能</p>' +
              '</div>'
        },
        {
            name: '自定义样式',
        html: '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">' +
              '<h3 style="margin: 0 0 10px 0; font-size: 1.5em;">🚀 自定义样式公告</h3>' +
              '<p style="margin: 0;">这是使用自定义CSS样式的公告内容，支持渐变背景和阴影效果。</p>' +
              '</div>'
        },
        {
            name: '复杂表单',
        html: '<form style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">' +
              '<h4 style="margin-top: 0; color: #495057;">意见反馈表单</h4>' +
              '<div style="margin-bottom: 15px;">' +
              '<label style="display: block; margin-bottom: 5px; font-weight: bold;">姓名：</label>' +
              '<input type="text" placeholder="请输入您的姓名" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">' +
              '</div>' +
              '<div style="margin-bottom: 15px;">' +
              '<label style="display: block; margin-bottom: 5px; font-weight: bold;">意见建议：</label>' +
              '<textarea rows="4" placeholder="请输入您的意见建议" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; resize: vertical;"></textarea>' +
              '</div>' +
              '<button type="submit" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">提交反馈</button>' +
              '</form>'
        }
])

// 增强的编辑器配置
const editorConfig: Partial<IEditorConfig> = {
    placeholder: '请输入内容...',
    // HTML粘贴配置
    MENU_CONF: {
        uploadImage: {
            customBrowseAndUpload(insert: any) {
                fileType.value = 'image'
                materialPickerRef.value?.showPopup(-1)
                insertFn = insert
            }
        },
        uploadVideo: {
            customBrowseAndUpload(insert: any) {
                fileType.value = 'video'
                materialPickerRef.value?.showPopup(-1)
                insertFn = insert
            }
        }
    },
    // HTML处理配置
    ...props.allowHtml ? {
        // 允许粘贴HTML时的配置
        customPaste: (editor: any, event: ClipboardEvent) => {
            // 获取粘贴的HTML内容
            const html = event.clipboardData?.getData('text/html') || ''
            
            if (html) {
                // 如果有HTML内容且允许HTML，则插入HTML
                event.preventDefault()
                editor.dangerouslyInsertHtml(html)
                return false
            }
            
            // 否则使用默认处理
            return true
        },
        // 自定义解析元素 - 当allowedTags为空数组时，允许所有标签
        customParseElemHtml: (elemNode: any, elem: any) => {
            // 如果allowedTags为空数组，允许所有标签
            if (props.allowedTags.length === 0) {
                return elem // 不过滤任何标签
            }
            
            // 如果指定了允许的标签列表，按列表过滤
                const tagName = elem.type?.toLowerCase()
                if (tagName && props.allowedTags.includes(tagName)) {
                    return elem
                }
                return null
        }
    } : {}
}

// 工具栏配置
const computedToolbarConfig = computed(() => {
    return {
        ...props.toolbarConfig
    }
})

const styles = computed<CSSProperties>(() => ({
    height: addUnit(props.height),
    width: addUnit(props.width)
}))

const valueHtml = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

// 插入常用HTML
const insertCommonHtml = () => {
    showHtmlDialog.value = true
}

// 插入模板
const insertTemplate = (html: string) => {
    const editor = editorRef.value
    if (editor) {
        editor.dangerouslyInsertHtml(html)
    }
    showHtmlDialog.value = false
}

// 插入自定义HTML
const insertCustomHtml = () => {
    if (customHtml.value) {
        const editor = editorRef.value
        if (editor) {
            editor.dangerouslyInsertHtml(customHtml.value)
        }
        customHtml.value = ''
        showHtmlDialog.value = false
    }
}

// 格式化HTML代码
const formatHtml = () => {
    try {
        // 简单的HTML格式化
        let formatted = valueHtml.value
        formatted = formatted.replace(/></g, '>\n<')
        formatted = formatted.replace(/\n\s*\n/g, '\n')
        valueHtml.value = formatted
        ElMessage.success('HTML代码已格式化')
    } catch (error) {
        ElMessage.error('格式化失败，请检查HTML语法')
    }
}

// HTML验证功能
const validateHtml = () => {
    try {
        const htmlContent = valueHtml.value
        
        // 基本HTML语法检查
        const parser = new DOMParser()
        const doc = parser.parseFromString(htmlContent, 'text/html')
        const errors = doc.querySelectorAll('parsererror')
        
        if (errors.length > 0) {
            ElMessage.warning('HTML语法可能存在问题，请检查标签闭合')
        } else {
            ElMessage.success('HTML语法验证通过')
        }
        
        // 检查常见的潜在问题
        const warnings = []
        
        // 检查外部链接
        const externalLinks = htmlContent.match(/https?:\/\/[^\s"'<>]+/g) || []
        if (externalLinks.length > 0) {
            warnings.push(`发现 ${externalLinks.length} 个外部链接`)
        }
        
        // 检查JavaScript代码
        const scripts = htmlContent.match(/<script[^>]*>[\s\S]*?<\/script>/gi) || []
        if (scripts.length > 0) {
            warnings.push(`发现 ${scripts.length} 个JavaScript代码块`)
        }
        
        // 检查iframe嵌入
        const iframes = htmlContent.match(/<iframe[^>]*>/gi) || []
        if (iframes.length > 0) {
            warnings.push(`发现 ${iframes.length} 个iframe嵌入`)
        }
        
        if (warnings.length > 0) {
            ElMessage.info(`检查完成，发现：${warnings.join('、')}`)
        }
        
    } catch (error) {
        ElMessage.error('HTML验证失败：' + error.message)
    }
}

const selectChange = (fileUrl: string[]) => {
    fileUrl.forEach((url) => {
        insertFn(url)
    })
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

const handleCreated = (editor: any) => {
    editorRef.value = editor // 记录 editor 实例，重要！
}
</script>

<style lang="scss">
.w-e-full-screen-container {
    z-index: 999;
}
.w-e-text-container [data-slate-editor] ul {
    list-style: disc;
}
.w-e-text-container [data-slate-editor] ol {
    list-style: decimal;
}
h1 {
    font-size: 2em;
}
h2 {
    font-size: 1.5em;
}
h3 {
    font-size: 1.17em;
}
h4 {
    font-size: 1em;
}
h5 {
    font-size: 0.83em;
}
h1,
h2,
h3,
h4,
h5 {
    font-weight: bold;
}

/* 增强样式 */
.w-e-text-container {
    pre {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        overflow-x: auto;
        font-family: 'Courier New', Courier, monospace;
    }
    
    code {
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', Courier, monospace;
    }
    
    blockquote {
        border-left: 4px solid #ddd;
        padding-left: 16px;
        margin: 16px 0;
        color: #666;
    }
}

/* HTML源码编辑器样式 */
.el-textarea__inner {
    font-family: 'Courier New', Courier, monospace !important;
    line-height: 1.5 !important;
}
</style>
