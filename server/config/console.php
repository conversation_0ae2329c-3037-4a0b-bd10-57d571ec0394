<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
use app\common\command\QueryDoubao;
use app\common\command\QueryPPT;
use app\common\command\WechatMerchantTransfer;

return [
    // 指令定义
    'commands' => [
        // 定时任务
        'crontab' => 'app\common\command\Crontab',
        // 内容审核
        'content_censor' => 'app\common\command\ContentCensor',
        // 退款查询
        'query_refund' => 'app\common\command\QueryRefund',
        // 修改密码
        'password' => 'app\common\command\Password',
        // 音乐查询处理
        'query_music' => 'app\common\command\QueryMusic',
        // 更新脚本
        'update_data' => 'app\common\command\UpdateData',
        // 绘画失败
        'draw_fail' => 'app\common\command\DrawFail',
        // 视频查询处理
        'query_video' => 'app\common\command\QueryVideo',
        // 商家转账到零钱查询
        'wechat_merchant_transfer' => WechatMerchantTransfer::class,
        // AI-PPT查询
        'query_ppt' => QueryPPT::class,
        // 豆包绘画处理
        'query_doubao' => QueryDoubao::class,
        // 智能体分成收益批量结算
        'robot_revenue_settle' => 'app\command\RobotRevenueSettle',
        // 🚀 优化的智能体分成处理任务
        'revenue:process' => 'app\command\OptimizedRevenueProcess',
        // 智能体分成定时任务处理
        'scheduled_revenue_settle' => 'app\command\ScheduledRevenueSettle',
        // 优化版智能体分成定时任务处理
        'optimized_revenue_settle' => 'app\command\OptimizedRevenueSettle',
        // 改进版智能体分成定时任务处理（解决缓存风险）
        'improved_revenue_settle' => 'app\command\ImprovedRevenueSettle',
        // 用户信息审核
        'user_info_censor' => 'app\common\command\UserInfoCensor',
        // AI对话记录清理命令
        'chat:cleanup' => \app\common\command\ChatRecordCleanup::class,
        // 智能体分成记录清理命令
        'revenue:cleanup' => \app\common\command\RevenueCleanup::class,
        // 系统日志清理命令
        'logs:cleanup' => \app\common\command\LogsCleanup::class,
    ],
];
