-- 智能体编辑日志表创建脚本
-- 执行时间：2025-08-12

-- 创建智能体编辑日志表
CREATE TABLE IF NOT EXISTS `cm_kb_robot_edit_log` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    `robot_id` INT NOT NULL COMMENT '智能体ID',
    `user_id` INT NOT NULL COMMENT '用户ID', 
    `edit_type` TINYINT NOT NULL COMMENT '编辑类型：1=智能体编辑，2=知识库编辑触发',
    `before_data` TEXT COMMENT '编辑前数据',
    `after_data` TEXT COMMENT '编辑后数据',
    `is_auto_offline` TINYINT DEFAULT 0 COMMENT '是否自动下架：0=否，1=是',
    `offline_reason` VARCHAR(255) DEFAULT NULL COMMENT '下架原因',
    `create_time` INT NOT NULL COMMENT '创建时间',
    INDEX `idx_robot_id` (`robot_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体编辑日志表';

-- 验证表创建
SELECT 
    'cm_kb_robot_edit_log_table' as table_name,
    'description' as description,
    COUNT(*) as verification_count
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'cm_kb_robot_edit_log';
