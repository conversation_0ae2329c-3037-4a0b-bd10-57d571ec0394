-- 为智能体广场表添加下架相关字段
-- 用于智能体编辑提示与自动下架功能

-- 添加下架原因和下架时间字段
-- 检查字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE() AND table_name = 'cm_kb_robot_square' AND column_name = 'offline_reason') = 0,
    'ALTER TABLE `cm_kb_robot_square` ADD COLUMN `offline_reason` VARCHAR(255) NULL DEFAULT NULL COMMENT ''下架原因'' AFTER `verify_result`',
    'SELECT ''offline_reason字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE() AND table_name = 'cm_kb_robot_square' AND column_name = 'offline_time') = 0,
    'ALTER TABLE `cm_kb_robot_square` ADD COLUMN `offline_time` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT ''下架时间'' AFTER `offline_reason`',
    'SELECT ''offline_time字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否创建成功
SELECT COUNT(*) as offline_reason_field, 'cm_kb_robot_square表offline_reason字段验证' as description
FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'cm_kb_robot_square' AND column_name = 'offline_reason';

SELECT COUNT(*) as offline_time_field, 'cm_kb_robot_square表offline_time字段验证' as description
FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'cm_kb_robot_square' AND column_name = 'offline_time';

-- 添加索引以提高查询性能
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_schema = DATABASE() AND table_name = 'cm_kb_robot_square' AND index_name = 'idx_offline_time') = 0,
    'ALTER TABLE `cm_kb_robot_square` ADD INDEX `idx_offline_time` (`offline_time`)',
    'SELECT ''idx_offline_time索引已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证索引是否创建成功
SELECT COUNT(*) as offline_time_index, 'cm_kb_robot_square表offline_time索引验证' as description
FROM information_schema.statistics 
WHERE table_schema = DATABASE() AND table_name = 'cm_kb_robot_square' AND index_name = 'idx_offline_time';
