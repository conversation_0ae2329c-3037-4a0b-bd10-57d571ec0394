<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\common\model;

/**
 * 用户赠送配置模型
 */
class UserGiftConfig extends BaseModel
{
    protected $name = 'user_gift_config';

    /**
     * 获取赠送配置
     */
    public static function getConfig()
    {
        $config = self::find(1);
        if (!$config) {
            // 如果没有配置，创建默认配置
            $config = self::create([
                'is_enable' => 1,
                'min_gift_amount' => 1.0,
                'max_gift_amount' => 1000.0,
                'daily_gift_limit' => 100.0,
                'daily_receive_limit' => 500.0,
                'gift_times_limit' => 10,
                'receive_times_limit' => 20,
                'friend_only' => 0,
                'need_verify' => 0,
                'create_time' => time(),
                'update_time' => time()
            ]);
        }
        
        return $config;
    }

    /**
     * 更新配置
     */
    public static function updateConfig(array $data): bool
    {
        $config = self::find(1);
        if (!$config) {
            $data['create_time'] = time();
            $data['update_time'] = time();
            return (bool)self::create($data);
        } else {
            $data['update_time'] = time();
            return (bool)$config->save($data);
        }
    }

    /**
     * 检查赠送功能是否启用
     */
    public static function isEnabled()
    {
        $config = self::getConfig();
        return $config->is_enable == 1;
    }

    /**
     * 获取最小赠送金额
     */
    public static function getMinGiftAmount()
    {
        $config = self::getConfig();
        return $config->min_gift_amount;
    }

    /**
     * 获取最大赠送金额
     */
    public static function getMaxGiftAmount()
    {
        $config = self::getConfig();
        return $config->max_gift_amount;
    }

    /**
     * 获取每日赠送限额
     */
    public static function getDailyGiftLimit()
    {
        $config = self::getConfig();
        return $config->daily_gift_limit;
    }

    /**
     * 获取每日接收限额
     */
    public static function getDailyReceiveLimit()
    {
        $config = self::getConfig();
        return $config->daily_receive_limit;
    }

    /**
     * 获取每日赠送次数限制
     */
    public static function getGiftTimesLimit()
    {
        $config = self::getConfig();
        return $config->gift_times_limit;
    }

    /**
     * 获取每日接收次数限制
     */
    public static function getReceiveTimesLimit()
    {
        $config = self::getConfig();
        return $config->receive_times_limit;
    }

    /**
     * 检查是否仅限好友间赠送
     */
    public static function isFriendOnly()
    {
        $config = self::getConfig();
        return $config->friend_only == 1;
    }

    /**
     * 检查是否需要人工审核
     */
    public static function needVerify()
    {
        $config = self::getConfig();
        return $config->need_verify == 1;
    }

    /**
     * 验证赠送金额是否在允许范围内
     */
    public static function validateGiftAmount($amount)
    {
        $config = self::getConfig();
        return $amount >= $config->min_gift_amount && $amount <= $config->max_gift_amount;
    }

    /**
     * 获取配置的数组格式（用于API返回）
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        
        // 格式化数字字段
        $data['min_gift_amount'] = (float)$data['min_gift_amount'];
        $data['max_gift_amount'] = (float)$data['max_gift_amount'];
        $data['daily_gift_limit'] = (float)$data['daily_gift_limit'];
        $data['daily_receive_limit'] = (float)$data['daily_receive_limit'];
        
        // 格式化布尔字段
        $data['is_enable'] = (bool)$data['is_enable'];
        $data['friend_only'] = (bool)$data['friend_only'];
        $data['need_verify'] = (bool)$data['need_verify'];
        
        // 格式化时间
        if ($data['create_time']) {
            if (is_numeric($data['create_time'])) {
                $data['create_time'] = date('Y-m-d H:i:s', $data['create_time']);
            }
        }
        if ($data['update_time']) {
            if (is_numeric($data['update_time'])) {
                $data['update_time'] = date('Y-m-d H:i:s', $data['update_time']);
            }
        }
        
        return $data;
    }
} 