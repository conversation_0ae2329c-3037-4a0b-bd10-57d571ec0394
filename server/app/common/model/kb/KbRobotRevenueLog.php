<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\model\kb;

use app\common\model\BaseModel;

/**
 * 智能体分成收益记录模型
 */
class KbRobotRevenueLog extends BaseModel
{
    protected $table = 'cm_kb_robot_revenue_log';

    /**
     * 🔒 安全修复：创建分成收益记录 - 已增强数据完整性验证
     * @param array $data
     * @return mixed
     * @security_fix 2025/08/02 - 数据完整性保护
     */
    public static function createRevenue(array $data)
    {
        // 🔒 数据完整性验证
        if (!self::validateRevenueData($data)) {
            throw new \Exception('分成数据验证失败');
        }

        $data['create_time'] = time();
        $data['update_time'] = time();

        // 🔒 生成数据签名
        if (!isset($data['data_signature'])) {
            $data['data_signature'] = self::generateDataSignature($data);
        }

        return self::create($data);
    }

    /**
     * 🔒 安全修复：验证分成数据
     * @param array $data 分成数据
     * @return bool
     * @security_fix 2025/08/02 - 数据验证
     */
    private static function validateRevenueData(array $data): bool
    {
        // 必要字段检查
        $required = ['user_id', 'sharer_id', 'record_id', 'total_cost', 'share_amount'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                \think\facade\Log::error('[分成模型] 缺少必要字段', [
                    'missing_field' => $field,
                    'data' => $data
                ]);
                return false;
            }
        }

        // 数值合理性检查
        if ($data['total_cost'] < 0 || $data['share_amount'] < 0) {
            \think\facade\Log::error('[分成模型] 金额不能为负数', [
                'total_cost' => $data['total_cost'],
                'share_amount' => $data['share_amount']
            ]);
            return false;
        }

        // 分成金额不能超过总费用
        if ($data['share_amount'] > $data['total_cost']) {
            \think\facade\Log::error('[分成模型] 分成金额超过总费用', [
                'total_cost' => $data['total_cost'],
                'share_amount' => $data['share_amount']
            ]);
            return false;
        }

        // 用户ID不能相同（防止自分成）
        if ($data['user_id'] == $data['sharer_id']) {
            \think\facade\Log::error('[分成模型] 用户不能给自己分成', [
                'user_id' => $data['user_id'],
                'sharer_id' => $data['sharer_id']
            ]);
            return false;
        }

        return true;
    }

    /**
     * 🔒 安全修复：生成数据签名
     * @param array $data 数据
     * @return string
     * @security_fix 2025/08/02 - 数据完整性签名
     */
    private static function generateDataSignature(array $data): string
    {
        // 排除签名字段和时间字段
        $signData = $data;
        unset($signData['data_signature'], $signData['create_time'], $signData['update_time']);

        // 按键排序
        ksort($signData);

        // 生成签名
        $dataString = json_encode($signData, JSON_UNESCAPED_UNICODE);
        $key = config('app.app_key', 'default_revenue_key');

        return hash_hmac('sha256', $dataString, $key);
    }

    /**
     * 🔒 安全修复：验证数据完整性
     * @param array $data 数据
     * @param string $signature 签名
     * @return bool
     * @security_fix 2025/08/02 - 数据完整性验证
     */
    public static function verifyDataIntegrity(array $data, string $signature): bool
    {
        $expectedSignature = self::generateDataSignature($data);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * 🔒 安全修复：获取用户分成收益统计 - 已增强数据验证
     * @param int $userId
     * @param string $timeRange 可选：today, week, month, all
     * @return array
     * @security_fix 2025/08/02 - 数据安全验证
     */
    public static function getUserRevenueStats(int $userId, string $timeRange = 'all'): array
    {
        // 🔒 参数验证
        if ($userId <= 0) {
            return ['total_amount' => 0, 'total_count' => 0, 'records' => []];
        }

        $allowedRanges = ['today', 'week', 'month', 'all'];
        if (!in_array($timeRange, $allowedRanges)) {
            $timeRange = 'all';
        }

        $where = ['sharer_id' => $userId, 'settle_status' => 1];

        switch ($timeRange) {
            case 'today':
                $where[] = ['create_time', '>=', strtotime('today')];
                break;
            case 'week':
                $where[] = ['create_time', '>=', strtotime('-7 days')];
                break;
            case 'month':
                $where[] = ['create_time', '>=', strtotime('-30 days')];
                break;
            default:
                // 全部时间
                break;
        }
        
        $stats = self::where($where)->field([
            'COUNT(*) as count',
            'SUM(share_amount) as total_revenue',
            'AVG(share_amount) as avg_revenue'
        ])->find();
        
        return [
            'count' => $stats['count'] ?? 0,
            'total_revenue' => $stats['total_revenue'] ?? 0,
            'avg_revenue' => $stats['avg_revenue'] ?? 0
        ];
    }

    /**
     * @notes 获取智能体分成收益统计
     * @param int $robotId
     * @return array
     */
    public static function getRobotRevenueStats(int $robotId): array
    {
        $stats = self::where(['robot_id' => $robotId, 'settle_status' => 1])
            ->field([
                'COUNT(*) as use_count',
                'SUM(total_cost) as total_cost',
                'SUM(share_amount) as total_share_amount',
                'SUM(platform_amount) as total_platform_amount'
            ])->find();
        
        return [
            'use_count' => $stats['use_count'] ?? 0,
            'total_cost' => $stats['total_cost'] ?? 0,
            'total_share_amount' => $stats['total_share_amount'] ?? 0,
            'total_platform_amount' => $stats['total_platform_amount'] ?? 0
        ];
    }

    /**
     * 🔒 安全修复：批量结算收益 - 已增强安全验证
     * @param array $ids
     * @return bool
     * @security_fix 2025/08/02 - 结算安全验证
     */
    public static function batchSettle(array $ids): bool
    {
        if (empty($ids)) {
            return false;
        }

        // 🔒 验证记录完整性
        $verifyResults = self::batchVerifyIntegrity($ids);
        $validIds = [];

        foreach ($verifyResults as $id => $result) {
            if ($result['valid']) {
                $validIds[] = $id;
            } else {
                \think\facade\Log::warning('[分成模型] 结算时发现数据完整性异常', [
                    'record_id' => $id,
                    'has_signature' => $result['has_signature']
                ]);
            }
        }

        if (empty($validIds)) {
            return false;
        }

        return self::whereIn('id', $validIds)
            ->where(['settle_status' => 0])
            ->update([
                'settle_status' => 1,
                'settle_time' => time(),
                'update_time' => time()
            ]) !== false;
    }

    /**
     * 🔒 安全修复：检查重复分成
     * @param int $recordId 对话记录ID
     * @return bool
     * @security_fix 2025/08/02 - 防止重复分成
     */
    public static function isDuplicateRevenue(int $recordId): bool
    {
        if ($recordId <= 0) {
            return false;
        }

        $count = self::where('record_id', $recordId)->count();

        if ($count > 0) {
            \think\facade\Log::warning('[分成模型] 发现重复分成尝试', [
                'record_id' => $recordId,
                'existing_count' => $count,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return true;
        }

        return false;
    }

    /**
     * 🔒 安全修复：获取用户分成频率
     * @param int $userId 用户ID
     * @param int $sharerId 分享者ID
     * @param int $timeWindow 时间窗口（秒）
     * @return int
     * @security_fix 2025/08/02 - 分成频率检查
     */
    public static function getRevenueFrequency(int $userId, int $sharerId, int $timeWindow = 3600): int
    {
        if ($userId <= 0 || $sharerId <= 0) {
            return 0;
        }

        $startTime = time() - $timeWindow;

        return self::where([
            'user_id' => $userId,
            'sharer_id' => $sharerId,
            ['create_time', '>=', $startTime]
        ])->count();
    }

    /**
     * 🔒 安全修复：验证分成记录完整性
     * @param int $id 记录ID
     * @return bool
     * @security_fix 2025/08/02 - 数据完整性验证
     */
    public static function verifyRecordIntegrity(int $id): bool
    {
        $record = self::find($id);
        if (!$record) {
            return false;
        }

        $data = $record->toArray();
        $signature = $data['data_signature'] ?? '';

        if (empty($signature)) {
            \think\facade\Log::warning('[分成模型] 记录缺少数据签名', [
                'record_id' => $id
            ]);
            return false;
        }

        return self::verifyDataIntegrity($data, $signature);
    }

    /**
     * 🔒 安全修复：批量验证分成记录
     * @param array $recordIds 记录ID数组
     * @return array 验证结果
     * @security_fix 2025/08/02 - 批量完整性验证
     */
    public static function batchVerifyIntegrity(array $recordIds): array
    {
        $results = [];

        if (empty($recordIds)) {
            return $results;
        }

        $records = self::whereIn('id', $recordIds)->select();

        foreach ($records as $record) {
            $data = $record->toArray();
            $signature = $data['data_signature'] ?? '';

            $results[$record->id] = [
                'valid' => !empty($signature) && self::verifyDataIntegrity($data, $signature),
                'has_signature' => !empty($signature)
            ];
        }

        return $results;
    }
}