<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\model\kb;

use app\common\model\BaseModel;
use think\facade\Log;

/**
 * 智能体分成收益配置模型
 */
class KbRobotRevenueConfig extends BaseModel
{
    protected $table = 'cm_kb_robot_revenue_config';

    /**
     * @notes 获取分成配置（改进版 - 自动恢复机制）
     * @return array
     */
    public static function getConfig(): array
    {
        // 1. 首先尝试获取最新的配置记录
        $config = self::order('id desc')->findOrEmpty();
        
        if (!$config->isEmpty()) {
            return $config->toArray();
        }
        
        // 2. 如果没有任何配置记录，自动创建默认配置
        Log::warning('智能体分成配置丢失，自动创建默认配置');
        
        $defaultConfig = [
            'is_enable' => 1,           // 默认开启分成功能
            'share_ratio' => 15.00,     // 分享者获得30%
            'platform_ratio' => 85.00,  // 平台获得70%
            'min_revenue' => 0.01,      // 最小分成金额0.01元
            'settle_type' => 2,         // 定时结算
            'create_time' => time(),
            'update_time' => time()
        ];
        
        // 创建配置记录
        $newConfig = self::create($defaultConfig);
        
        Log::info('智能体分成配置自动创建成功', [
            'config_id' => $newConfig->getData('id'),
            'config' => $defaultConfig
        ]);
        
        return $newConfig->toArray();
    }

    /**
     * @notes 确保配置存在（预防性方法）
     * @return void
     */
    public static function ensureConfigExists(): void
    {
        $config = self::findOrEmpty();
        if ($config->isEmpty()) {
            self::getConfig(); // 触发自动创建
        }
    }

    /**
     * @notes 更新分成配置（改进版）
     * @param array $data
     * @return bool
     */
    public static function updateConfig(array $data): bool
    {
        $data['update_time'] = time();
        
        // 确保分成比例合理
        if (isset($data['share_ratio'])) {
            if (isset($data['platform_ratio'])) {
                $total = $data['share_ratio'] + $data['platform_ratio'];
                if ($total != 100.00) {
                    $data['platform_ratio'] = 100.00 - $data['share_ratio'];
                }
            } else {
                $data['platform_ratio'] = 100.00 - $data['share_ratio'];
            }
        }
        
        // 1. 获取当前最新配置的ID
        $currentConfig = self::order('id desc')->findOrEmpty();
        
        if ($currentConfig->isEmpty()) {
            // 如果没有配置，创建新的配置
            $data['create_time'] = time();
            $newConfig = self::create($data);
            return !$newConfig->isEmpty();
        } else {
            // 更新现有配置
            $configId = $currentConfig->getData('id');
            return self::where(['id' => $configId])->update($data) !== false;
        }
    }

    /**
     * @notes 获取有效配置（确保返回启用的配置）
     * @return array|null
     */
    public static function getActiveConfig(): ?array
    {
        $config = self::getConfig();
        
        // 如果配置被禁用，返回null
        if (empty($config) || !$config['is_enable']) {
            return null;
        }
        
        return $config;
    }

    /**
     * @notes 检查分成功能是否可用
     * @return bool
     */
    public static function isRevenueEnabled(): bool
    {
        try {
            $config = self::getConfig();
            return !empty($config) && $config['is_enable'] == 1;
        } catch (\Throwable $e) {
            Log::error('检查分成功能状态时出错', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * @notes 清理重复配置（维护工具）
     * @return int 清理的记录数
     */
    public static function cleanDuplicateConfigs(): int
    {
        // 保留最新的配置，删除其他重复配置
        $latestConfig = self::order('id desc')->find();
        
        if (!$latestConfig) {
            return 0;
        }
        
        $deletedCount = self::where('id', '<', $latestConfig->getData('id'))->delete();
        
        return $deletedCount;
    }

    /**
     * @notes 获取分成配置的简单文本描述
     * @return string
     */
    public static function getConfigDescription(): string
    {
        $config = self::getConfig();
        
        if (empty($config)) {
            return '分成配置未设置';
        }
        
        $status = $config['is_enable'] ? '已开启' : '已关闭';
        $settleType = $config['settle_type'] == 1 ? '实时结算' : '定时结算';
        
        return sprintf(
            '分成功能：%s，分享者：%.1f%%，平台：%.1f%%，最小分成：%.2f元，结算方式：%s',
            $status,
            $config['share_ratio'],
            $config['platform_ratio'],
            $config['min_revenue'],
            $settleType
        );
    }
} 