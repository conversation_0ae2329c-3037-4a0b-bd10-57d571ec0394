<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\model\kb;

use app\common\enum\user\UserEnum;
use app\common\enum\user\UserTerminalEnum;
use app\common\model\BaseModel;
use think\model\concern\SoftDelete;

/**
 * 机器人分享模型
 */
class KbRobotShareLog extends BaseModel
{
    use SoftDelete;

    protected string $deleteTime = 'delete_time';


    public function getChannelDescAttr($value,$data)
    {
        return UserTerminalEnum::getTerminalDesc($data['channel']);
    }
}