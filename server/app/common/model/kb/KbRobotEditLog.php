<?php

namespace app\common\model\kb;

use app\common\model\BaseModel;

/**
 * 智能体编辑日志模型
 */
class KbRobotEditLog extends BaseModel
{
    protected $name = 'kb_robot_edit_log';

    /**
     * 编辑类型常量
     */
    const EDIT_TYPE_ROBOT = 1;      // 智能体编辑
    const EDIT_TYPE_KNOWLEDGE = 2;  // 知识库编辑触发

    /**
     * 获取编辑类型文本
     */
    public static function getEditTypeText($type)
    {
        $types = [
            self::EDIT_TYPE_ROBOT => '智能体编辑',
            self::EDIT_TYPE_KNOWLEDGE => '知识库编辑触发'
        ];
        return $types[$type] ?? '未知类型';
    }

    /**
     * 记录编辑日志
     */
    public static function recordEditLog($robotId, $userId, $editType, $beforeData = null, $afterData = null, $isAutoOffline = 0, $offlineReason = null)
    {
        return self::create([
            'robot_id' => $robotId,
            'user_id' => $userId,
            'edit_type' => $editType,
            'before_data' => $beforeData ? json_encode($beforeData, JSON_UNESCAPED_UNICODE) : null,
            'after_data' => $afterData ? json_encode($afterData, JSON_UNESCAPED_UNICODE) : null,
            'is_auto_offline' => $isAutoOffline,
            'offline_reason' => $offlineReason,
            'create_time' => time()
        ]);
    }
}
