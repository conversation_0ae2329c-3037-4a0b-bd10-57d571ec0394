<?php

namespace app\common\model\notice;

use app\common\model\BaseModel;

/**
 * 公告模型
 */
class Notice extends BaseModel
{
    protected $name = 'cm_notice';

    protected $updateTime = 'update_time';
    protected $createTime = 'create_time';

    /**
     * @notes 状态
     * @param $value
     * @param $data
     * @return string
     * <AUTHOR>
     * @date 2024/1/10 15:28
     */
    public function getStatusTextAttr($value, $data)
    {
        return $data['status'] ? '启用' : '禁用';
    }
} 