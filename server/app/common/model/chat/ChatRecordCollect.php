<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\model\chat;

use app\common\model\BaseModel;
use think\model\relation\HasOne;

/**
 * 对话收藏模型
 */
class ChatRecordCollect extends BaseModel
{
    /**
     * @notes 关联对话记录模型
     * @return HasOne
     * <AUTHOR>
     */
    public function chatRecords(): HasOne
    {
        return $this->hasOne(ChatRecord::class,'id','records_id')->field('id,ask,reply');
    }
}