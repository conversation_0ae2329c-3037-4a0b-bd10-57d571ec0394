<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\model;

use app\common\model\user\User;
use think\model\concern\SoftDelete;

/**
 * 用户赠送记录模型
 */
class UserGiftLog extends BaseModel
{
    use SoftDelete;
    
    protected $name = 'user_gift_log';
    protected $deleteTime = 'delete_time';
    
    /**
     * 状态常量
     */
    const STATUS_SUCCESS = 1;  // 成功
    const STATUS_FAILED = 2;   // 失败
    const STATUS_REVOKED = 3;  // 已撤回

    /**
     * 获取器 - 格式化状态
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_REVOKED => '已撤回'
        ];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 关联赠送者用户
     */
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id', 'id');
    }

    /**
     * 关联接收者用户
     */
    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id', 'id');
    }

    /**
     * 关联操作管理员
     */
    public function admin()
    {
        return $this->belongsTo('app\common\model\auth\SystemAdmin', 'admin_id', 'id');
    }

    /**
     * 生成赠送流水号
     */
    public static function generateGiftSn()
    {
        return 'GFT' . date('YmdHis') . sprintf('%06d', mt_rand(0, 999999));
    }

    /**
     * 创建赠送记录
     */
    public static function createGiftLog($data)
    {
        if (empty($data['gift_sn'])) {
            $data['gift_sn'] = self::generateGiftSn();
        }
        
        return self::create($data);
    }

    /**
     * 撤回赠送记录
     */
    public function revokeGift($adminId, $remark = '')
    {
        if ($this->status != self::STATUS_SUCCESS) {
            return false;
        }

        return $this->save([
            'status' => self::STATUS_REVOKED,
            'admin_id' => $adminId,
            'remark' => $remark,
            'update_time' => time()
        ]);
    }

    /**
     * 获取今日赠送金额
     */
    public static function getTodayGiftAmount(int $userId): float
    {
        $today = date('Y-m-d');
        $startTime = strtotime($today . ' 00:00:00');
        $endTime = strtotime($today . ' 23:59:59');
        
        $amount = self::where('from_user_id', $userId)
            ->where('status', self::STATUS_SUCCESS)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->sum('gift_amount');
            
        return (float)$amount;
    }

    /**
     * 获取今日接收金额
     */
    public static function getTodayReceiveAmount(int $userId): float
    {
        $today = date('Y-m-d');
        $startTime = strtotime($today . ' 00:00:00');
        $endTime = strtotime($today . ' 23:59:59');
        
        $amount = self::where('to_user_id', $userId)
            ->where('status', self::STATUS_SUCCESS)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->sum('gift_amount');
            
        return (float)$amount;
    }

    /**
     * 获取今日赠送次数
     */
    public static function getTodayGiftTimes(int $userId): int
    {
        $today = date('Y-m-d');
        $startTime = strtotime($today . ' 00:00:00');
        $endTime = strtotime($today . ' 23:59:59');
        
        $count = self::where('from_user_id', $userId)
            ->where('status', self::STATUS_SUCCESS)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->count();
            
        return (int)$count;
    }

    /**
     * 获取今日接收次数
     */
    public static function getTodayReceiveTimes(int $userId): int
    {
        $today = date('Y-m-d');
        $startTime = strtotime($today . ' 00:00:00');
        $endTime = strtotime($today . ' 23:59:59');
        
        $count = self::where('to_user_id', $userId)
            ->where('status', self::STATUS_SUCCESS)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->count();
            
        return (int)$count;
    }

    /**
     * 获取赠送统计数据
     */
    public static function getStatistics($startTime = null, $endTime = null)
    {
        $query = self::where('status', self::STATUS_SUCCESS);
        
        if ($startTime) {
            $query->where('create_time', '>=', strtotime($startTime));
        }
        if ($endTime) {
            $query->where('create_time', '<=', strtotime($endTime . ' 23:59:59'));
        }
        
        $totalCount = $query->count();
        $totalAmount = $query->sum('gift_amount');
        $avgAmount = $totalCount > 0 ? $totalAmount / $totalCount : 0;
        
        return [
            'total_count' => $totalCount,
            'total_amount' => $totalAmount,
            'avg_amount' => $avgAmount
        ];
    }
} 