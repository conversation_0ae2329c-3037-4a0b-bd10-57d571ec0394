<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\common\model\robot;
use app\common\model\BaseModel;
use app\common\model\knowledge\ExampleCategory;

/**
 * 智能体角色示例模型类
 * Class RoleExample
 * @package app\common\model\robot
 */
class RoleExample extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $name = 'role_example';
    
    /**
     * 表前缀
     * @var string
     */
    protected $prefix = 'cm_';
    
    /**
     * 软删除
     */
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    /**
     * 重写查询范围，兼容NULL值
     * @param \think\db\Query $query
     * @return void
     */
    public function scopeWithNoDeleted($query)
    {
        $query->where(function($query) {
            $query->where($this->getDeleteTimeField(), '=', $this->getDefaultSoftDelete())
                  ->whereOr(function($q) {
                      $q->whereNull($this->getDeleteTimeField());
                  });
        });
    }
    
    /**
     * 与示例库类别的关联
     */
    public function category()
    {
        return $this->belongsTo(ExampleCategory::class, 'category_id');
    }
    
    /**
     * 获取列表
     * @param array $where 查询条件
     * @param int $limit 查询数量
     * @param int $offset 查询偏移
     * @return array
     */
    public static function getList($where = [], $limit = 15, $offset = 0)
    {
        $query = self::alias('RE')
            ->leftJoin('example_category C', 'C.id = RE.category_id')
            ->where($where)
            ->field('RE.*, C.name as category_name');
            
        if ($limit > 0) {
            $query = $query->limit($offset, $limit);
        }
        
        return $query->order(['RE.sort' => 'desc', 'RE.id' => 'asc'])
            ->select()
            ->toArray();
    }
    
    /**
     * 获取总数
     * @param array $where 查询条件
     * @return int
     */
    public static function getCount($where = [])
    {
        return self::alias('RE')
            ->leftJoin('example_category C', 'C.id = RE.category_id')
            ->where($where)
            ->count();
    }
    
    /**
     * 根据类别ID获取角色示例列表
     * @param int $categoryId 类别ID
     * @return array
     */
    public static function getListByCategoryId($categoryId)
    {
        return self::where([
            'category_id' => $categoryId,
            'status' => 1
        ])
        ->where(function($query) {
            $query->where('delete_time', '=', 0)
                  ->whereOr(function($q) {
                      $q->whereNull('delete_time');
                  });
        })
        ->field('id,title,content,description')
        ->order(['sort' => 'desc', 'id' => 'asc'])
        ->select()
        ->toArray();
    }
    
    /**
     * 获取所有角色示例（按类别分组）
     * @return array
     */
    public static function getAllRoleExamples()
    {
        // 先获取所有开启状态的类别
        $categories = \think\facade\Db::table('cm_example_category')
            ->where('status', 1)
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            })
            ->field('id, name')
            ->order('sort', 'desc')
            ->select()
            ->toArray();
        
        if (empty($categories)) {
            return [];
        }
        
        // 获取所有开启状态的角色示例
        $examples = self::where(['status' => 1])
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            })
            ->field('id,category_id,title,content,description')
            ->select()
            ->toArray();
        
        // 将角色示例按类别分组
        $examplesByCategory = [];
        foreach ($examples as $example) {
            $categoryId = $example['category_id'];
            if (!isset($examplesByCategory[$categoryId])) {
                $examplesByCategory[$categoryId] = [];
            }
            $examplesByCategory[$categoryId][] = [
                'id' => $example['id'],
                'title' => $example['title'],
                'content' => $example['content'],
                'description' => $example['description']
            ];
        }
        
        // 构建结果数组
        $result = [];
        foreach ($categories as $category) {
            $categoryId = $category['id'];
            $result[] = [
                'id' => $categoryId,
                'name' => $category['name'],
                'examples' => $examplesByCategory[$categoryId] ?? []
            ];
        }
        
        return $result;
    }
} 