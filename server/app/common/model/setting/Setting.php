<?php

namespace app\common\model\setting;

use app\common\model\BaseModel;

/**
 * 系统设置模型
 */
class Setting extends BaseModel
{
    protected $name = 'setting';

    protected $updateTime = 'update_time';
    protected $createTime = 'create_time';

    /**
     * @notes 获取配置值
     * @param string $key
     * @param mixed $default
     * @return mixed
     * <AUTHOR>
     * @date 2024/1/10 15:28
     */
    public static function getConfig(string $key, $default = null)
    {
        $setting = self::where('key', $key)->findOrEmpty();
        if ($setting->isEmpty()) {
            return $default;
        }
        
        $value = json_decode($setting->value, true);
        return $value ?: $default;
    }

    /**
     * @notes 设置配置值
     * @param string $key
     * @param mixed $value
     * @return bool
     * <AUTHOR>
     * @date 2024/1/10 15:28
     */
    public static function setConfig(string $key, $value): bool
    {
        try {
            $setting = self::where('key', $key)->findOrEmpty();
            $data = [
                'value' => json_encode($value),
                'update_time' => time()
            ];
            
            if ($setting->isEmpty()) {
                $data['key'] = $key;
                $data['create_time'] = time();
                self::create($data);
            } else {
                $setting->save($data);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
} 