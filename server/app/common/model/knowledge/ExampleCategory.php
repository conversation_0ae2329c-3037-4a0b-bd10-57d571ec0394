<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\common\model\knowledge;
use app\common\model\BaseModel;

/**
 * 示例库类别模型类
 * Class ExampleCategory
 * @package app\common\model\knowledge
 */
class ExampleCategory extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $name = 'example_category';
    
    /**
     * 表前缀
     * @var string
     */
    protected $prefix = 'cm_';
    
    /**
     * 软删除
     */
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    /**
     * 重写查询范围，兼容NULL值
     * @param \think\db\Query $query
     * @return void
     */
    public function scopeWithNoDeleted($query)
    {
        $query->where(function($query) {
            $query->where($this->getDeleteTimeField(), '=', $this->getDefaultSoftDelete())
                  ->whereOr(function($q) {
                      $q->whereNull($this->getDeleteTimeField());
                  });
        });
    }
    
    /**
     * 与示例库内容的关联
     */
    public function contents()
    {
        return $this->hasMany(ExampleContent::class, 'category_id');
    }
    
    /**
     * 与示例库内容的关联（用于统计）
     */
    public function exampleContent()
    {
        return $this->hasMany(ExampleContent::class, 'category_id');
    }
    
    /**
     * 获取列表
     * @param array $where 查询条件
     * @return array
     */
    public static function getList($where = [])
    {
        return self::where(function($query) use ($where) {
            // 处理where条件
            foreach ($where as $key => $value) {
                if (is_array($value)) {
                    $query->where($value[0], $value[1], $value[2]);
                } else {
                    $query->where($key, $value);
                }
            }
            
            // 添加delete_time条件，兼容NULL值
            $query->where(function($subQuery) {
                $subQuery->where('delete_time', '=', 0)
                        ->whereOr(function($q) {
                            $q->whereNull('delete_time');
                        });
            });
        })
        ->order(['sort' => 'desc', 'id' => 'asc'])
        ->select()
        ->toArray();
    }
} 