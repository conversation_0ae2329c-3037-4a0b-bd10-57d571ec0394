<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\cache;

use app\common\model\user\User;
use app\common\model\user\UserSession;
use DateTime;
use think\facade\Log;

class UserTokenCache extends BaseCache
{
    private string $prefix = 'token_user_';
    private string $memoryPrefix = 'mem_token_';
    
    // 内存缓存存储（进程级）
    private static $memoryCache = [];
    private static $memoryCacheExpire = [];
    
    // 缓存配置
    private const MEMORY_CACHE_TTL = 300; // 内存缓存5分钟
    private const REDIS_CACHE_TTL_BUFFER = 600; // Redis缓存额外10分钟缓冲
    private const REFRESH_THRESHOLD = 300; // 5分钟内过期时异步刷新

    /**
     * @notes 通过token获取缓存用户信息（优化版）
     * @param $token
     * @return mixed
     * @throws @\think\db\exception\DataNotFoundException
     * @throws @\think\db\exception\DbException
     * @throws @\think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/9/16 10:11
     */
    public function getUserInfo($token): mixed
    {
        // 1. 先从内存缓存获取（最快）
        $userInfo = $this->getFromMemoryCache($token);
        if ($userInfo !== null) {
            // 检查是否即将过期，如果是则异步刷新
            if ($userInfo && isset($userInfo['expire_time']) && 
                ($userInfo['expire_time'] - time()) < self::REFRESH_THRESHOLD) {
                $this->asyncRefreshToken($token);
            }
            return $userInfo ?: false;
        }

        // 2. 从Redis缓存获取
        $userInfo = $this->get($this->prefix . $token);
        if ($userInfo) {
            // 写入内存缓存
            $this->setToMemoryCache($token, $userInfo);
            
            // 检查是否需要异步刷新
            if (isset($userInfo['expire_time']) && 
                ($userInfo['expire_time'] - time()) < self::REFRESH_THRESHOLD) {
                $this->asyncRefreshToken($token);
            }
            return $userInfo;
        }

        // 3. 从数据库获取并设置缓存
        $userInfo = $this->setUserInfo($token);
        return $userInfo ?: false;
    }
    
    /**
     * @notes 从内存缓存获取用户信息
     * @param string $token
     * @return mixed|null
     */
    private function getFromMemoryCache($token)
    {
        $key = $this->memoryPrefix . $token;
        
        // 检查是否过期
        if (isset(self::$memoryCacheExpire[$key]) && self::$memoryCacheExpire[$key] < time()) {
            unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
            return null;
        }
        
        return self::$memoryCache[$key] ?? null;
    }
    
    /**
     * @notes 写入内存缓存
     * @param string $token
     * @param mixed $userInfo
     */
    private function setToMemoryCache($token, $userInfo)
    {
        $key = $this->memoryPrefix . $token;
        
        // 防止内存缓存过大，清理过期项
        if (count(self::$memoryCache) > 500) {
            $this->cleanupMemoryCache();
        }
        
        self::$memoryCache[$key] = $userInfo;
        self::$memoryCacheExpire[$key] = time() + self::MEMORY_CACHE_TTL;
    }
    
    /**
     * @notes 清理过期的内存缓存
     */
    private function cleanupMemoryCache()
    {
        $now = time();
        foreach (self::$memoryCacheExpire as $key => $expireTime) {
            if ($expireTime < $now) {
                unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
            }
        }
    }
    
    /**
     * @notes 异步刷新Token缓存
     * @param string $token
     */
    private function asyncRefreshToken($token)
    {
        // 使用简单的异步机制，避免阻塞当前请求
        try {
            // 检查是否已经在刷新中
            $refreshKey = 'refreshing_' . $token;
            if ($this->get($refreshKey)) {
                return; // 已经在刷新中，避免重复刷新
            }
            
            // 标记正在刷新，防止重复
            $this->set($refreshKey, 1, 60); // 1分钟标记
            
            // 异步刷新（这里使用简化版本，实际可以集成队列系统）
            $this->setUserInfo($token, true);
            
        } catch (\Throwable $e) {
            Log::error('Token异步刷新失败', [
                'token' => substr($token, 0, 10) . '...',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * @notes 通过有效token设置用户信息缓存（优化版）
     * @param $token
     * @param bool $isRefresh 是否为刷新调用
     * @return mixed
     * @throws @\think\db\exception\DataNotFoundException
     * @throws @\think\db\exception\DbException
     * @throws @\think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/9/16 10:11
     */
    public function setUserInfo($token, $isRefresh = false): mixed
    {
        try {
            // 使用优化的索引查询（利用之前创建的idx_token_expire索引）
            $userSession = (new UserSession())
                ->where([
                    ['token', '=', $token], 
                    ['expire_time', '>', time()]
                ])
                ->field('user_id,terminal,expire_time') // 只查询需要的字段
                ->find();
                
            if (empty($userSession)) {
                // 缓存空结果，防止缓存穿透
                $emptyResult = [];
                $this->set($this->prefix . $token, $emptyResult, 300); // 缓存5分钟
                $this->setToMemoryCache($token, $emptyResult);
                return $emptyResult;
            }

            // 批量查询用户信息，减少数据库查询
            $user = (new User())
                ->where('id', '=', $userSession->user_id)
                ->field('id,nickname,sn,mobile,avatar') // 只查询需要的字段
                ->find();
                
            if (empty($user)) {
                $emptyResult = [];
                $this->set($this->prefix . $token, $emptyResult, 300);
                $this->setToMemoryCache($token, $emptyResult);
                return $emptyResult;
            }

            $userInfo = [
                'user_id'     => $user->id,
                'nickname'    => $user->nickname,
                'token'       => $token,
                'sn'          => $user->sn,
                'mobile'      => $user->mobile,
                'avatar'      => $user->avatar,
                'terminal'    => $userSession->terminal,
                'expire_time' => $userSession->expire_time,
            ];

            // 计算缓存过期时间，添加缓冲时间防止缓存雪崩
            $expireTime = $userSession->expire_time;
            $cacheExpireTime = $expireTime + self::REDIS_CACHE_TTL_BUFFER + rand(0, 300); // 添加随机时间
            
            // 设置Redis缓存
            $ttl = new DateTime(Date('Y-m-d H:i:s', $cacheExpireTime));
            $this->set($this->prefix . $token, $userInfo, $ttl);
            
            // 设置内存缓存
            $this->setToMemoryCache($token, $userInfo);
            
            // 清理刷新标记
            if ($isRefresh) {
                $this->delete('refreshing_' . $token);
            }
            
            // 记录缓存更新日志（仅在刷新时记录，避免日志过多）
            if ($isRefresh) {
                Log::info('Token缓存刷新成功', [
                    'user_id' => $user->id,
                    'token' => substr($token, 0, 10) . '...',
                    'expire_time' => date('Y-m-d H:i:s', $expireTime)
                ]);
            }

            return $userInfo;
            
        } catch (\Throwable $e) {
            Log::error('设置用户Token缓存失败', [
                'token' => substr($token, 0, 10) . '...',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 清理刷新标记
            if ($isRefresh) {
                $this->delete('refreshing_' . $token);
            }
            
            return [];
        }
    }

    /**
     * @notes 删除缓存（优化版）
     * @param $token
     * @return bool
     * <AUTHOR>
     * @date 2022/9/16 10:13
     */
    public function deleteUserInfo($token): bool
    {
        // 删除内存缓存
        $memoryKey = $this->memoryPrefix . $token;
        unset(self::$memoryCache[$memoryKey], self::$memoryCacheExpire[$memoryKey]);
        
        // 删除刷新标记
        $this->delete('refreshing_' . $token);
        
        // 删除Redis缓存
        return $this->delete($this->prefix . $token);
    }
    
    /**
     * @notes 批量删除用户Token缓存
     * @param array $tokens Token数组
     * @return int 删除成功数量
     */
    public function batchDeleteUserInfo(array $tokens): int
    {
        $deletedCount = 0;
        
        foreach ($tokens as $token) {
            if ($this->deleteUserInfo($token)) {
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * @notes 清理过期的Token缓存
     * @return int 清理数量
     */
    public function cleanupExpiredTokens(): int
    {
        $cleanedCount = 0;
        
        try {
            // 清理内存缓存中的过期项
            $this->cleanupMemoryCache();
            
            // 清理数据库中的过期会话（可选，减少数据库压力）
            $expiredSessions = (new UserSession())
                ->where('expire_time', '<=', time())
                ->field('token')
                ->limit(100) // 限制批量处理数量
                ->select();
                
            foreach ($expiredSessions as $session) {
                $this->deleteUserInfo($session->token);
                $cleanedCount++;
            }
            
            Log::info('清理过期Token缓存', ['cleaned_count' => $cleanedCount]);
            
        } catch (\Throwable $e) {
            Log::error('清理过期Token缓存失败', [
                'error' => $e->getMessage()
            ]);
        }
        
        return $cleanedCount;
    }
    
    /**
     * @notes 获取缓存统计信息
     * @return array
     */
    public function getCacheStats(): array
    {
        return [
            'memory_cache_count' => count(self::$memoryCache),
            'memory_cache_size' => strlen(serialize(self::$memoryCache)),
            'memory_expire_count' => count(self::$memoryCacheExpire)
        ];
    }
}