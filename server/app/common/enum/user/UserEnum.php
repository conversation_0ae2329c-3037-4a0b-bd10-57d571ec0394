<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\enum\user;

/**
 * 管理后台登录终端
 * Class terminalEnum
 * @package app\common\enum
 */
class UserEnum
{
    /**
     * 性别
     * SEX_OTHER = 未知
     * SEX_MEN =  男
     * SEX_WOMAN = 女
     */
    const SEX_OTHER = 0;
    const SEX_MEN   = 1;
    const SEX_WOMAN = 2;

    /**
     * 用户信息审核状态
     * CENSOR_STATUS_WAIT = 待审核
     * CENSOR_STATUS_PASS = 审核通过
     * CENSOR_STATUS_NON_COMPLIANCE = 不合规
     * CENSOR_STATUS_FAIL = 审核失败
     */
    const CENSOR_STATUS_WAIT = 0;           // 待审核
    const CENSOR_STATUS_PASS = 1;           // 审核通过
    const CENSOR_STATUS_NON_COMPLIANCE = 2; // 不合规
    const CENSOR_STATUS_FAIL = 3;           // 审核失败

    /**
     * @notes 性别描述
     * @param bool|int $from
     * @return string|string[]
     * <AUTHOR>
     * @date 2022/9/7 15:05
     */
    public static function getSexDesc(bool|int $from = true): array|string
    {
        $desc = [
            self::SEX_OTHER => '未知',
            self::SEX_MEN   => '男',
            self::SEX_WOMAN => '女',
        ];
        if (true === $from) {
            return $desc;
        }
        return $desc[$from] ?? '';
    }

    /**
     * @notes 审核状态描述
     * @param bool|int $from
     * @return string|string[]
     * <AUTHOR>
     * @date 2024/01/27
     */
    public static function getCensorStatusDesc(bool|int $from = true): array|string
    {
        $desc = [
            self::CENSOR_STATUS_WAIT => '待审核',
            self::CENSOR_STATUS_PASS => '审核通过',
            self::CENSOR_STATUS_NON_COMPLIANCE => '不合规',
            self::CENSOR_STATUS_FAIL => '审核失败',
        ];
        if (true === $from) {
            return $desc;
        }
        return $desc[$from] ?? '';
    }
}