<?php

namespace app\common\enum;

/**
 * 智能体分成状态枚举
 * Class RevenueStatusEnum
 * @package app\common\enum
 */
class RevenueStatusEnum
{
    // 分成状态
    const PENDING = 0;  // 待分成
    const SUCCESS = 1;  // 分成成功  
    const FAILED = 2;   // 分成失败
    const SKIPPED = 3;  // 跳过分成
    
    // 状态描述映射
    const STATUS_MAP = [
        self::PENDING => '待分成',
        self::SUCCESS => '分成成功',
        self::FAILED => '分成失败',
        self::SKIPPED => '跳过分成'
    ];
    
    // 状态转换规则
    const STATUS_TRANSITIONS = [
        self::PENDING => [self::SUCCESS, self::FAILED, self::SKIPPED],
        self::FAILED => [self::SUCCESS, self::SKIPPED], // 失败可重试
        self::SUCCESS => [], // 成功状态不可变更
        self::SKIPPED => []  // 跳过状态不可变更
    ];
    
    /**
     * 获取状态描述
     * @param int $status
     * @return string
     */
    public static function getStatusDesc(int $status): string
    {
        return self::STATUS_MAP[$status] ?? '未知状态';
    }
    
    /**
     * 检查状态转换是否合法
     * @param int $fromStatus
     * @param int $toStatus
     * @return bool
     */
    public static function canTransition(int $fromStatus, int $toStatus): bool
    {
        $allowedTransitions = self::STATUS_TRANSITIONS[$fromStatus] ?? [];
        return in_array($toStatus, $allowedTransitions);
    }
    
    /**
     * 获取所有状态
     * @return array
     */
    public static function getAllStatus(): array
    {
        return self::STATUS_MAP;
    }
    
    /**
     * 检查是否为有效状态
     * @param int $status
     * @return bool
     */
    public static function isValidStatus(int $status): bool
    {
        return array_key_exists($status, self::STATUS_MAP);
    }
    
    /**
     * 获取可重试的状态
     * @return array
     */
    public static function getRetryableStatus(): array
    {
        return [self::PENDING, self::FAILED];
    }
    
    /**
     * 获取最终状态（不可再变更）
     * @return array
     */
    public static function getFinalStatus(): array
    {
        return [self::SUCCESS, self::SKIPPED];
    }
}
