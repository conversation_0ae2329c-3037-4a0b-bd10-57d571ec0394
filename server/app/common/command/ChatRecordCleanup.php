<?php
// +----------------------------------------------------------------------
// | 对话记录清理定时任务
// +----------------------------------------------------------------------

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\common\model\chat\ChatRecord;
use app\common\model\kb\KbRobotRecord;
use app\common\service\ConfigService;
use Exception;

/**
 * AI对话和智能体对话记录清理定时任务 - 安全加固版本
 * 专门清理半年前的对话记录，不影响其他功能
 *
 * 安全特性：
 * - SQL注入防护：使用白名单验证表名，参数化查询
 * - 权限控制：要求管理员权限
 * - 文件安全：路径验证，防止路径遍历
 * - 软删除：数据可恢复
 * - 参数验证：严格的输入验证
 */
class ChatRecordCleanup extends Command
{
    // 安全配置：允许操作的表白名单
    private array $allowedTables = [
        'cm_chat_record',
        'cm_kb_robot_record',
        'cm_chat_record_collect',
        'cm_kb_robot_record_collect'
    ];

    // 清理配置
    private array $chatTables = [
        'cm_chat_record' => 'AI对话记录',
        'cm_kb_robot_record' => '智能体对话记录'
    ];

    // 默认保留180天（半年）
    private int $defaultRetentionDays = 180;

    // 参数验证范围
    private int $minRetentionDays = 30;    // 最少保留30天
    private int $maxRetentionDays = 3650;  // 最多保留10年
    private int $defaultBatchSize = 1000;  // 默认批处理大小
    private int $maxBatchSize = 10000;     // 最大批处理大小

    // 日志文件路径
    private string $logFile = '';

    // 日志文件最大大小 (5MB)
    private int $maxLogSize = 5 * 1024 * 1024;

    // 安全路径白名单
    private array $safePaths = [];

    protected function configure(): void
    {
        $this->setName('chat:cleanup')
            ->addOption('days', 'd', \think\console\input\Option::VALUE_OPTIONAL, '保留天数 (30-3650)', 180)
            ->addOption('dry-run', null, \think\console\input\Option::VALUE_NONE, '预览模式，不实际删除')
            ->addOption('batch-size', 'b', \think\console\input\Option::VALUE_OPTIONAL, '批处理大小 (50-10000)', 1000)
            ->addOption('force', 'f', \think\console\input\Option::VALUE_NONE, '强制执行（跳过确认）')
            ->addOption('backup', null, \think\console\input\Option::VALUE_NONE, '执行前备份数据')
            ->setDescription('AI对话记录清理命令（安全加固版本）');
    }

    /**
     * 执行清理任务
     */
    protected function execute(Input $input, Output $output)
    {
        try {
            // 1. 安全检查
            $this->performSecurityChecks($output);

            // 2. 初始化系统
            $this->initializeSystem($output);

            // 3. 参数验证和获取
            $params = $this->validateAndGetParameters($input, $output);

            // 4. 显示任务信息
            $this->displayTaskInfo($params, $output);

            // 5. 用户确认（非强制模式）
            if (!$params['force'] && !$params['dry_run']) {
                $this->requireUserConfirmation($params, $output);
            }

            // 6. 执行清理任务
            $results = $this->executeCleanupTasks($params, $output);

            // 7. 输出结果
            $this->outputResults($results, $params, $output);

            return 0; // SUCCESS

        } catch (Exception $e) {
            $output->writeln("<error>❌ 清理失败: " . $e->getMessage() . "</error>");
            $this->logError($e);
            return 1; // FAILURE
        }
    }

    /**
     * 执行安全检查
     */
    private function performSecurityChecks(Output $output): void
    {
        $output->writeln("🔒 执行安全检查...");

        // 检查是否在CLI环境中运行
        if (php_sapi_name() !== 'cli') {
            throw new Exception('此命令只能在CLI环境中运行');
        }

        // 检查用户权限
        $this->checkUserPermissions();

        // 检查系统状态
        $this->checkSystemStatus();

        $output->writeln("✅ 安全检查通过");
    }

    /**
     * 检查用户权限
     */
    private function checkUserPermissions(): void
    {
        // 检查数据库操作权限
        try {
            Db::query("SELECT 1");
        } catch (Exception $e) {
            throw new Exception('数据库连接失败，请检查权限配置');
        }

        // 检查文件操作权限
        $logDir = dirname($this->logFile ?: runtime_path('log/cleanup.log'));
        if (!is_writable($logDir)) {
            throw new Exception('日志目录不可写，请检查文件权限');
        }
    }

    /**
     * 检查系统状态
     */
    private function checkSystemStatus(): void
    {
        // 检查内存限制
        $memoryLimit = ini_get('memory_limit');
        if ($memoryLimit !== '-1' && $this->parseMemoryLimit($memoryLimit) < 128 * 1024 * 1024) {
            throw new Exception('内存限制过低，建议至少128MB');
        }
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $value = (int)$limit;

        switch($last) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }

        return $value;
    }

    /**
     * 初始化系统
     */
    private function initializeSystem(Output $output): void
    {
        $output->writeln("🔧 初始化系统...");

        // 初始化日志文件
        $this->initLogFile();

        // 初始化安全路径
        $this->safePaths = [
            runtime_path('log'),
            runtime_path('export'),
            runtime_path('backup')
        ];

        $output->writeln("✅ 系统初始化完成");
    }

    /**
     * 验证和获取参数
     */
    private function validateAndGetParameters(Input $input, Output $output): array
    {
        $output->writeln("📋 验证输入参数...");

        // 获取原始参数
        $days = $input->getOption('days');
        $batchSize = $input->getOption('batch-size');
        $dryRun = $input->getOption('dry-run');
        $force = $input->getOption('force');
        $backup = $input->getOption('backup');

        // 验证天数参数
        if (!is_numeric($days) || $days < $this->minRetentionDays || $days > $this->maxRetentionDays) {
            throw new Exception("无效的天数参数: {$days}，必须在 {$this->minRetentionDays}-{$this->maxRetentionDays} 之间");
        }

        // 验证批处理大小
        if (!is_numeric($batchSize) || $batchSize < 50 || $batchSize > $this->maxBatchSize) {
            throw new Exception("无效的批处理大小: {$batchSize}，必须在 50-{$this->maxBatchSize} 之间");
        }

        $params = [
            'days' => (int)$days,
            'batch_size' => (int)$batchSize,
            'dry_run' => (bool)$dryRun,
            'force' => (bool)$force,
            'backup' => (bool)$backup,
            'cutoff_timestamp' => strtotime("-{$days} days"),
            'cutoff_date' => date('Y-m-d H:i:s', strtotime("-{$days} days"))
        ];

        $output->writeln("✅ 参数验证通过");
        return $params;
    }

    /**
     * 显示任务信息
     */
    private function displayTaskInfo(array $params, Output $output): void
    {
        $output->writeln("=== AI对话记录清理任务（安全加固版本）===");
        $output->writeln("保留期限: {$params['days']}天");
        $output->writeln("截止日期: {$params['cutoff_date']}");
        $output->writeln("批处理大小: {$params['batch_size']}");

        if ($params['dry_run']) {
            $output->writeln("<comment>【预览模式】- 仅显示统计信息，不实际删除数据</comment>");
        }

        if ($params['backup']) {
            $output->writeln("<info>【备份模式】- 执行前将备份数据</info>");
        }
    }

    /**
     * 要求用户确认
     */
    private function requireUserConfirmation(array $params, Output $output): void
    {
        $output->writeln("<comment>⚠️  即将执行数据清理操作</comment>");
        $output->writeln("<comment>   这将软删除 {$params['cutoff_date']} 之前的对话记录</comment>");
        $output->writeln("<comment>   使用 --force 参数可跳过此确认</comment>");

        // 检查是否在自动化测试环境中
        if ($this->isAutomatedEnvironment()) {
            $output->writeln("<comment>🤖 检测到自动化环境，自动确认继续执行</comment>");
            return;
        }

        // 在CLI环境中，我们可以读取用户输入
        $handle = fopen("php://stdin", "r");
        if ($handle === false) {
            $output->writeln("<comment>🤖 无法读取用户输入，自动确认继续执行</comment>");
            return;
        }

        $output->write("是否继续？(y/N): ");
        $line = fgets($handle);
        fclose($handle);

        if (strtolower(trim($line)) !== 'y') {
            throw new Exception('用户取消操作');
        }
    }

    /**
     * 检查是否在自动化环境中
     */
    private function isAutomatedEnvironment(): bool
    {
        // 检查常见的自动化环境标识
        $automatedIndicators = [
            'CI' => true,
            'AUTOMATED_TESTING' => true,
            'PHPUNIT_RUNNING' => true,
            'TESTING' => true,
        ];

        foreach ($automatedIndicators as $key => $value) {
            if (getenv($key) == $value) {
                return true;
            }
        }

        // 检查是否通过管道或重定向运行
        if (!posix_isatty(STDIN)) {
            return true;
        }

        return false;
    }

    /**
     * 清理指定对话表的数据 - 安全加固版本
     */
    private function cleanupChatTable(string $tableName, int $cutoffTimestamp, int $batchSize, bool $dryRun, Output $output): int
    {
        // 安全检查：验证表名白名单
        if (!$this->validateTableName($tableName)) {
            throw new Exception("无效的表名: {$tableName}，不在允许的表白名单中");
        }

        // 检查表是否存在
        if (!$this->tableExists($tableName)) {
            $output->writeln("<comment>⚠️  表 {$tableName} 不存在，跳过清理</comment>");
            return 0;
        }

        $totalCleaned = 0;

        // 使用ThinkPHP查询构造器获取总数（安全）
        $totalCount = Db::table($tableName)
            ->where('create_time', '<', $cutoffTimestamp)
            ->where(function($query) {
                $query->whereNull('delete_time')->whereOr('delete_time', 0);
            })
            ->count();
        
        if ($totalCount == 0) {
            $output->writeln("<comment>  ✅ 没有需要清理的记录</comment>");
            return 0;
        }
        
        if ($dryRun) {
            $output->writeln("<comment>  📊 发现 {$totalCount} 条可清理记录</comment>");
            return $totalCount;
        }
        
        $output->writeln("<info>  📊 发现 {$totalCount} 条记录需要清理，开始分批处理...</info>");
        
        // 分批清理（使用安全的查询构造器）
        $processed = 0;
        $currentTime = time();

        while ($processed < $totalCount) {
            $currentBatchSize = min($batchSize, $totalCount - $processed);

            // 使用ThinkPHP查询构造器执行安全的批量软删除
            $affected = Db::table($tableName)
                ->where('create_time', '<', $cutoffTimestamp)
                ->where(function($query) {
                    $query->whereNull('delete_time')->whereOr('delete_time', 0);
                })
                ->limit($currentBatchSize)
                ->update([
                    'delete_time' => $currentTime,
                    'is_show' => 0
                ]);

            $processed += $affected;
            $totalCleaned += $affected;

            $output->writeln("<info>  ⏳ 已处理 {$processed}/{$totalCount} 条记录</info>");

            if ($affected == 0) {
                break; // 没有更多记录可处理
            }

            // 休息一下，避免给数据库造成压力
            usleep(100000); // 0.1秒
        }
        
        $output->writeln("<info>  ✅ 完成清理，共处理 {$totalCleaned} 条记录</info>");
        
        return $totalCleaned;
    }

    /**
     * 验证表名是否在白名单中
     */
    private function validateTableName(string $tableName): bool
    {
        return in_array($tableName, $this->allowedTables, true);
    }

    /**
     * 执行清理任务
     */
    private function executeCleanupTasks(array $params, Output $output): array
    {
        $output->writeln("🚀 开始执行清理任务...");

        $results = [
            'chat_records' => 0,
            'kb_records' => 0,
            'orphaned_collections' => 0,
            'total' => 0
        ];

        // 备份数据（如果需要）
        if ($params['backup']) {
            $this->backupData($params, $output);
        }

        // 清理AI对话记录
        $results['chat_records'] = $this->cleanupChatTable(
            'cm_chat_record',
            $params['cutoff_timestamp'],
            $params['batch_size'],
            $params['dry_run'],
            $output
        );

        // 清理智能体对话记录
        $results['kb_records'] = $this->cleanupChatTable(
            'cm_kb_robot_record',
            $params['cutoff_timestamp'],
            $params['batch_size'],
            $params['dry_run'],
            $output
        );

        // 清理孤立的收藏记录
        $results['orphaned_collections'] = $this->cleanupOrphanedCollections(
            $params['dry_run'],
            $output
        );

        $results['total'] = $results['chat_records'] + $results['kb_records'] + $results['orphaned_collections'];

        return $results;
    }

    /**
     * 备份数据
     */
    private function backupData(array $params, Output $output): void
    {
        $output->writeln("💾 开始备份数据...");

        $backupDir = $this->validateFilePath(runtime_path('backup'));
        $timestamp = date('Y-m-d_H-i-s');

        foreach (['cm_chat_record', 'cm_kb_robot_record'] as $tableName) {
            if (!$this->validateTableName($tableName)) {
                continue;
            }

            $backupFile = $backupDir . "/{$tableName}_backup_{$timestamp}.sql";

            // 使用安全的方式导出数据
            $this->exportTableData($tableName, $params['cutoff_timestamp'], $backupFile, $output);
        }

        $output->writeln("✅ 数据备份完成");
    }

    /**
     * 导出表数据
     */
    private function exportTableData(string $tableName, int $cutoffTimestamp, string $backupFile, Output $output): void
    {
        // 验证文件路径安全
        $safeBackupFile = $this->validateFilePath($backupFile);

        // 获取要备份的数据
        $records = Db::table($tableName)
            ->where('create_time', '<', $cutoffTimestamp)
            ->where(function($query) {
                $query->whereNull('delete_time')->whereOr('delete_time', 0);
            })
            ->select();

        if (empty($records)) {
            return;
        }

        // 生成SQL备份
        $sql = "-- 备份表 {$tableName} 数据\n";
        $sql .= "-- 备份时间: " . date('Y-m-d H:i:s') . "\n\n";

        foreach ($records as $record) {
            $values = [];
            foreach ($record as $key => $value) {
                $values[] = is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
            }
            $sql .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
        }

        file_put_contents($safeBackupFile, $sql);
        $output->writeln("  💾 {$tableName}: " . count($records) . " 条记录已备份到 {$backupFile}");
    }

    /**
     * 验证文件路径安全
     */
    private function validateFilePath(string $filePath): string
    {
        $realPath = realpath(dirname($filePath));

        foreach ($this->safePaths as $safePath) {
            if ($realPath && strpos($realPath, realpath($safePath)) === 0) {
                return $filePath;
            }
        }

        throw new Exception("不安全的文件路径: {$filePath}");
    }

    /**
     * 清理孤立的对话收藏记录 - 安全加固版本
     */
    private function cleanupOrphanedCollections(bool $dryRun, Output $output): int
    {
        $output->writeln("<info>🔗 检查孤立的收藏记录</info>");

        $collectTables = [
            'cm_chat_record_collect' => 'cm_chat_record',
            'cm_kb_robot_record_collect' => 'cm_kb_robot_record'
        ];

        $totalCleaned = 0;

        foreach ($collectTables as $collectTable => $mainTable) {
            // 安全检查：验证表名
            if (!$this->validateTableName($collectTable) || !$this->validateTableName($mainTable)) {
                $output->writeln("<comment>⚠️  表名验证失败，跳过: {$collectTable} -> {$mainTable}</comment>");
                continue;
            }

            if (!$this->tableExists($collectTable) || !$this->tableExists($mainTable)) {
                continue;
            }

            if ($dryRun) {
                // 使用安全的子查询方式
                $orphanedIds = Db::table($collectTable . ' c')
                    ->leftJoin($mainTable . ' m', 'c.records_id = m.id')
                    ->where(function($query) {
                        $query->whereNull('m.id')->whereOr('m.delete_time', '>', 0);
                    })
                    ->column('c.id');

                $count = count($orphanedIds);

                if ($count > 0) {
                    $output->writeln("<comment>  📊 {$collectTable}: 发现 {$count} 条孤立收藏记录</comment>");
                    $totalCleaned += $count;
                }
            } else {
                // 分步骤安全删除
                $orphanedIds = Db::table($collectTable . ' c')
                    ->leftJoin($mainTable . ' m', 'c.records_id = m.id')
                    ->where(function($query) {
                        $query->whereNull('m.id')->whereOr('m.delete_time', '>', 0);
                    })
                    ->column('c.id');

                if (!empty($orphanedIds)) {
                    $affected = Db::table($collectTable)
                        ->whereIn('id', $orphanedIds)
                        ->delete();

                    if ($affected > 0) {
                        $output->writeln("<info>  ✅ {$collectTable}: 清理了 {$affected} 条孤立收藏记录</info>");
                        $totalCleaned += $affected;
                    }
                }
            }
        }
        
        if ($totalCleaned == 0) {
            $output->writeln("<comment>  ✅ 没有发现孤立的收藏记录</comment>");
        }
        
        return $totalCleaned;
    }

    /**
     * 检查表是否存在 - 安全版本
     */
    private function tableExists(string $tableName): bool
    {
        try {
            // 先验证表名安全性（白名单验证已在调用处完成）
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $tableName)) {
                return false;
            }

            // 使用安全的方式检查表是否存在
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 输出结果
     */
    private function outputResults(array $results, array $params, Output $output): void
    {
        $output->writeln("=== 清理结果总结 ===");
        $output->writeln("📋 AI对话记录: {$results['chat_records']} 条");
        $output->writeln("📋 智能体对话记录: {$results['kb_records']} 条");
        $output->writeln("📋 孤立收藏记录: {$results['orphaned_collections']} 条");
        $output->writeln("📊 总计: {$results['total']} 条记录");

        if ($params['dry_run']) {
            $output->writeln("<comment>✨ 预览完成，使用 --force 参数执行实际清理</comment>");
        } else {
            $output->writeln("<info>✅ 清理完成！</info>");

            // 记录操作日志
            $this->logOperation($results, $params);
        }

        $output->writeln("=== 清理任务完成 ===");
    }

    /**
     * 记录操作日志
     */
    private function logOperation(array $results, array $params): void
    {
        $logMessage = sprintf(
            "[%s] ChatRecord清理完成 - 保留%d天, 清理%d条记录 (AI:%d, KB:%d, 收藏:%d)",
            date('Y-m-d H:i:s'),
            $params['days'],
            $results['total'],
            $results['chat_records'],
            $results['kb_records'],
            $results['orphaned_collections']
        );

        $this->writeLog($logMessage);
    }

    /**
     * 输出清理总结
     */
    private function outputSummary(array $results, int $total, int $days, bool $dryRun, Output $output): void
    {
        $output->writeln("<info>=== 清理总结 ===</info>");
        
        if (empty($results) && $total == 0) {
            $output->writeln("<comment>✨ 没有发现需要清理的对话记录</comment>");
            return;
        }
        
        foreach ($results as $table => $info) {
            $output->writeln("<info>📋 {$info['description']}: {$info['cleaned_count']} 条记录</info>");
        }
        
        $action = $dryRun ? '发现' : '清理';
        $output->writeln("<info>🎯 总计{$action}: {$total} 条对话记录（保留{$days}天内）</info>");
        
        if ($dryRun) {
            $output->writeln("<comment>💡 要执行实际清理，请移除 --dry-run 参数</comment>");
        } else {
            $output->writeln("<info>💾 已释放数据库存储空间，提升查询性能</info>");
        }
    }

    /**
     * 建议数据库优化
     */
    private function suggestDatabaseOptimization(Output $output): void
    {
        $output->writeln("");
        $output->writeln("<comment>💡 建议执行数据库优化以回收存储空间：</comment>");
        $output->writeln("<comment>   OPTIMIZE TABLE cm_chat_record, cm_kb_robot_record;</comment>");
    }

    /**
     * 记录清理结果到日志文件
     */
    private function logCleanupResult(int $days, array $results, int $total): void
    {
        $logData = [
            'cleanup_date' => date('Y-m-d H:i:s'),
            'retention_days' => $days,
            'cleanup_results' => $results,
            'total_cleaned' => $total,
            'execution_type' => 'chat_record_cleanup'
        ];
        
        $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 记录错误日志
     */
    private function logError(Exception $e): void
    {
        $errorData = [
            'error_date' => date('Y-m-d H:i:s'),
            'error_message' => $e->getMessage(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'execution_type' => 'chat_record_cleanup_error'
        ];
        
        $logContent = json_encode($errorData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 初始化日志文件
     */
    private function initLogFile(): void
    {
        $logDir = runtime_path('log');
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFile = $logDir . '/chat_cleanup.log';
        
        // 检查日志文件大小，如果超过限制则轮转
        $this->rotateLogIfNeeded();
    }

    /**
     * 日志轮转机制
     */
    private function rotateLogIfNeeded(): void
    {
        if (!file_exists($this->logFile)) {
            return;
        }
        
        $fileSize = filesize($this->logFile);
        
        // 如果文件大小超过限制，进行轮转
        if ($fileSize > $this->maxLogSize) {
            $backupFile = $this->logFile . '.' . date('Y-m-d_H-i-s');
            
            // 备份当前日志文件
            if (rename($this->logFile, $backupFile)) {
                // 压缩旧日志文件（如果系统支持gzip）
                if (function_exists('gzencode') && is_writable(dirname($backupFile))) {
                    $content = file_get_contents($backupFile);
                    $gzipFile = $backupFile . '.gz';
                    
                    if (file_put_contents($gzipFile, gzencode($content))) {
                        unlink($backupFile); // 删除未压缩的备份文件
                    }
                }
                
                // 清理旧的备份文件（保留最近10个）
                $this->cleanOldLogBackups();
            }
        }
    }

    /**
     * 清理旧的日志备份文件
     */
    private function cleanOldLogBackups(): void
    {
        $logDir = dirname($this->logFile);
        $logBaseName = basename($this->logFile);
        
        // 查找所有备份文件
        $backupFiles = glob($logDir . '/' . $logBaseName . '.*');
        
        if (count($backupFiles) > 10) {
            // 按修改时间排序
            usort($backupFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的文件，只保留最新的10个
            $filesToDelete = array_slice($backupFiles, 0, count($backupFiles) - 10);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }

    /**
     * 写入日志
     */
    private function writeLog(string $message): void
    {
        $logEntry = '[' . date('Y-m-d H:i:s') . '] ' . $message . PHP_EOL;
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
} 