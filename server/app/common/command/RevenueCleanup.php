<?php
// +----------------------------------------------------------------------
// | 智能体分成记录清理定时任务
// +----------------------------------------------------------------------

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\common\model\kb\KbRobotRevenueLog;
use Exception;

/**
 * 智能体分成收益记录清理定时任务
 * 专门清理已结算的历史分成记录，保留财务数据一致性
 */
class RevenueCleanup extends Command
{
    // 清理配置
    private array $revenueTables = [
        'cm_kb_robot_revenue_log' => '智能体分成记录'
    ];
    
    // 默认保留365天（一年）- 财务数据保留期较长
    private int $defaultRetentionDays = 365;
    
    // 批处理大小
    private int $batchSize = 500;
    
    // 日志文件路径
    private string $logFile = '';
    
    // 日志文件最大大小 (5MB)
    private int $maxLogSize = 5 * 1024 * 1024;

    protected function configure(): void
    {
        $this->setName('revenue:cleanup')
            ->addOption('days', 'd', \think\console\input\Option::VALUE_OPTIONAL, '保留天数 (365-3650，财务合规要求)', 1095)
            ->addOption('dry-run', null, \think\console\input\Option::VALUE_NONE, '预览模式，不实际归档')
            ->addOption('batch-size', 'b', \think\console\input\Option::VALUE_OPTIONAL, '批处理大小 (50-5000)', 500)
            ->addOption('force', 'f', \think\console\input\Option::VALUE_NONE, '强制执行（跳过确认）')
            ->addOption('verify-archive', null, \think\console\input\Option::VALUE_NONE, '验证归档数据完整性')
            ->addOption('test-mode', null, \think\console\input\Option::VALUE_NONE, '测试模式（跳过未结算记录检查）')
            ->setDescription('智能体分成记录归档命令（财务合规版本，安全加固）');
    }

    /**
     * 执行清理任务
     */
    protected function execute(Input $input, Output $output)
    {
        try {
            // 1. 安全检查
            $this->performSecurityChecks($output);

            // 2. 初始化系统
            $this->initLogFile();

            // 3. 参数验证和获取
            $params = $this->validateAndGetParameters($input, $output);

            // 4. 财务合规检查
            $this->performFinancialComplianceCheck($params['days'], $params['test_mode'], $output);

            // 5. 显示任务信息
            $this->displayTaskInfo($params, $output);

            // 6. 用户确认（非强制模式和非预览模式）
            if (!$params['force'] && !$params['dry_run']) {
                $this->requireUserConfirmation($params, $output);
            }

            // 7. 执行归档任务
            $results = $this->executeArchiveTasks($params, $output);

            // 8. 验证归档完整性（如果需要）
            if ($params['verify_archive'] && !$params['dry_run']) {
                $this->verifyArchiveIntegrity('cm_kb_robot_revenue_log', 'cm_kb_robot_revenue_log_archive', $params['cutoff_timestamp'], $output);
            }

            // 9. 输出结果
            $this->outputResults($results, $params, $output);

            return 0; // SUCCESS

        } catch (Exception $e) {
            $output->writeln("<error>❌ 归档失败: " . $e->getMessage() . "</error>");
            $this->logError($e);
            return 1; // FAILURE
        }
    }

    /**
     * 执行安全检查
     */
    private function performSecurityChecks(Output $output): void
    {
        $output->writeln("🔒 执行安全检查...");

        // 检查是否在CLI环境中运行
        if (php_sapi_name() !== 'cli') {
            throw new Exception('此命令只能在CLI环境中运行');
        }

        // 检查用户权限
        $this->checkUserPermissions();

        // 检查系统状态
        $this->checkSystemStatus();

        $output->writeln("✅ 安全检查通过");
    }

    /**
     * 检查用户权限
     */
    private function checkUserPermissions(): void
    {
        // 检查数据库操作权限
        try {
            Db::query("SELECT 1");
        } catch (Exception $e) {
            throw new Exception('数据库连接失败，请检查权限配置');
        }

        // 检查文件操作权限
        $logDir = dirname($this->logFile ?: runtime_path('log/revenue_cleanup.log'));
        if (!is_writable($logDir)) {
            throw new Exception('日志目录不可写，请检查文件权限');
        }
    }

    /**
     * 检查系统状态
     */
    private function checkSystemStatus(): void
    {
        // 检查内存限制
        $memoryLimit = ini_get('memory_limit');
        if ($memoryLimit !== '-1' && $this->parseMemoryLimit($memoryLimit) < 256 * 1024 * 1024) {
            throw new Exception('内存限制过低，财务数据处理建议至少256MB');
        }
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $value = (int)$limit;

        switch($last) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }

        return $value;
    }

    /**
     * 验证和获取参数
     */
    private function validateAndGetParameters(Input $input, Output $output): array
    {
        $output->writeln("📋 验证输入参数...");

        // 获取原始参数
        $days = $input->getOption('days');
        $batchSize = $input->getOption('batch-size');
        $dryRun = $input->getOption('dry-run');
        $force = $input->hasOption('force') ? $input->getOption('force') : false;
        $verifyArchive = $input->hasOption('verify-archive') ? $input->getOption('verify-archive') : false;
        $testMode = $input->hasOption('test-mode') ? $input->getOption('test-mode') : false;

        // 验证天数参数（财务数据特殊要求）
        if (!is_numeric($days) || $days < 365 || $days > 3650) {
            throw new Exception("无效的天数参数: {$days}，财务数据必须在 365-3650 天之间");
        }

        // 验证批处理大小
        if (!is_numeric($batchSize) || $batchSize < 50 || $batchSize > 5000) {
            throw new Exception("无效的批处理大小: {$batchSize}，必须在 50-5000 之间");
        }

        $params = [
            'days' => (int)$days,
            'batch_size' => (int)$batchSize,
            'dry_run' => (bool)$dryRun,
            'force' => (bool)$force,
            'verify_archive' => (bool)$verifyArchive,
            'test_mode' => (bool)$testMode,
            'cutoff_timestamp' => strtotime("-{$days} days"),
            'cutoff_date' => date('Y-m-d H:i:s', strtotime("-{$days} days"))
        ];

        $output->writeln("✅ 参数验证通过");
        return $params;
    }

    /**
     * 显示任务信息
     */
    private function displayTaskInfo(array $params, Output $output): void
    {
        $output->writeln("=== 智能体分成记录归档任务（财务合规版本）===");
        $output->writeln("保留期限: {$params['days']}天");
        $output->writeln("截止日期: {$params['cutoff_date']}");
        $output->writeln("批处理大小: {$params['batch_size']}");

        if ($params['dry_run']) {
            $output->writeln("<comment>【预览模式】- 仅显示统计信息，不实际归档数据</comment>");
        }

        if ($params['verify_archive']) {
            $output->writeln("<info>【验证归档】- 归档完成后将验证数据完整性</info>");
        }

        if ($params['test_mode']) {
            $output->writeln("<comment>【测试模式】- 跳过未结算记录检查，只归档已结算记录</comment>");
        }
    }

    /**
     * 要求用户确认
     */
    private function requireUserConfirmation(array $params, Output $output): void
    {
        $output->writeln("<comment>⚠️  即将执行财务数据归档操作</comment>");
        $output->writeln("<comment>   这将把 {$params['cutoff_date']} 之前的分成记录移动到归档表</comment>");
        $output->writeln("<comment>   归档后的数据仍可查询，但会从主表中软删除</comment>");
        $output->writeln("<comment>   使用 --force 参数可跳过此确认</comment>");

        // 检查是否在自动化测试环境中
        if ($this->isAutomatedEnvironment()) {
            $output->writeln("<comment>🤖 检测到自动化环境，自动确认继续执行</comment>");
            return;
        }

        // 在CLI环境中，我们可以读取用户输入
        $handle = fopen("php://stdin", "r");
        if ($handle === false) {
            $output->writeln("<comment>🤖 无法读取用户输入，自动确认继续执行</comment>");
            return;
        }

        $output->write("是否继续？(y/N): ");
        $line = fgets($handle);
        fclose($handle);

        if (strtolower(trim($line)) !== 'y') {
            throw new Exception('用户取消操作');
        }
    }

    /**
     * 检查是否在自动化环境中
     */
    private function isAutomatedEnvironment(): bool
    {
        // 检查常见的自动化环境标识
        $automatedIndicators = [
            'CI' => true,
            'AUTOMATED_TESTING' => true,
            'PHPUNIT_RUNNING' => true,
            'TESTING' => true,
        ];

        foreach ($automatedIndicators as $key => $value) {
            if (getenv($key) == $value) {
                return true;
            }
        }

        // 检查是否通过管道或重定向运行
        if (!posix_isatty(STDIN)) {
            return true;
        }

        return false;
    }

    /**
     * 执行归档任务
     */
    private function executeArchiveTasks(array $params, Output $output): array
    {
        $output->writeln("🚀 开始执行归档任务...");

        $results = [
            'revenue_records' => 0,
            'total_amount' => 0,
            'total' => 0
        ];

        // 归档智能体分成记录
        $archiveResult = $this->cleanupRevenueRecords($params['cutoff_date'], $params['dry_run'], $output);
        $results['revenue_records'] = $archiveResult['cleaned_count'];
        $results['total'] = $archiveResult['cleaned_count'];

        return $results;
    }

    /**
     * 输出结果
     */
    private function outputResults(array $results, array $params, Output $output): void
    {
        $output->writeln("=== 归档结果总结 ===");
        $output->writeln("📋 智能体分成记录: {$results['revenue_records']} 条");
        $output->writeln("📊 总计: {$results['total']} 条记录");

        if ($params['dry_run']) {
            $output->writeln("<comment>✨ 预览完成，使用 --force 参数执行实际归档</comment>");
        } else {
            $output->writeln("<info>✅ 归档完成！</info>");

            // 记录操作日志
            $this->logOperation($results, $params);
        }

        $output->writeln("=== 归档任务完成 ===");
    }

    /**
     * 记录操作日志
     */
    private function logOperation(array $results, array $params): void
    {
        $logMessage = sprintf(
            "[%s] RevenueCleanup归档完成 - 保留%d天, 归档%d条记录",
            date('Y-m-d H:i:s'),
            $params['days'],
            $results['total']
        );

        $this->writeLog($logMessage);
    }

    /**
     * 记录错误日志
     */
    private function logError(Exception $e): void
    {
        $logMessage = sprintf(
            "[%s] RevenueCleanup归档失败 - 错误: %s, 文件: %s, 行号: %d",
            date('Y-m-d H:i:s'),
            $e->getMessage(),
            $e->getFile(),
            $e->getLine()
        );

        $this->writeLog($logMessage);
    }

    /**
     * 清理智能体分成记录 - 重构为归档机制
     */
    private function cleanupRevenueRecords(string $cutoffDate, bool $dryRun, Output $output): array
    {
        $output->writeln("<info>💰 清理智能体分成记录</info>");
        
        $cutoffTimestamp = strtotime($cutoffDate);
        $tableName = 'cm_kb_robot_revenue_log';
        
        // 检查表是否存在
        if (!$this->tableExists($tableName)) {
            $output->writeln("<comment>⚠️  表 {$tableName} 不存在，跳过清理</comment>");
            return ['cleaned_count' => 0, 'description' => '智能体分成记录'];
        }
        
        // 安全检查：验证表名白名单
        if (!$this->validateTableName($tableName)) {
            throw new Exception("无效的表名: {$tableName}，不在允许的表白名单中");
        }

        // 使用安全的查询构造器获取已结算记录统计
        $totalCount = Db::table($tableName)
            ->where('create_time', '<', $cutoffTimestamp)
            ->where('settle_status', '=', 1)  // 只清理已结算的记录
            ->count();

        // 使用安全的查询构造器获取待结算记录统计（用于警告）
        $pendingCount = Db::table($tableName)
            ->where('create_time', '<', $cutoffTimestamp)
            ->where('settle_status', '=', 0)
            ->count();
        
        if ($pendingCount > 0) {
            $output->writeln("<error>⚠️ 发现 {$pendingCount} 条未结算的历史记录</error>");
            $output->writeln("<comment>  建议先执行分成结算: php think robot_revenue_settle</comment>");
        }
        
        if ($totalCount == 0) {
            $output->writeln("<comment>  ✅ 没有需要清理的已结算记录</comment>");
            return ['cleaned_count' => 0, 'description' => '智能体分成记录'];
        }
        
        if ($dryRun) {
            $output->writeln("<comment>  📊 发现 {$totalCount} 条可清理的已结算记录</comment>");
            
            // 使用安全的查询构造器显示金额统计
            $amountStats = Db::table($tableName)
                ->where('create_time', '<', $cutoffTimestamp)
                ->where('settle_status', '=', 1)
                ->field([
                    'SUM(IFNULL(share_amount, 0)) as total_share_amount',
                    'SUM(IFNULL(platform_amount, 0)) as total_platform_amount',
                    'SUM(IFNULL(total_cost, 0)) as total_cost'
                ])
                ->find();

            if ($amountStats) {
                $output->writeln("<comment>  💰 涉及金额统计：</comment>");
                $output->writeln("<comment>    - 分成金额: " . number_format($amountStats['total_share_amount'] ?? 0, 2) . " 元</comment>");
                $output->writeln("<comment>    - 平台金额: " . number_format($amountStats['total_platform_amount'] ?? 0, 2) . " 元</comment>");
                $output->writeln("<comment>    - 总成本: " . number_format($amountStats['total_cost'] ?? 0, 2) . " 元</comment>");
            }
            
            return ['cleaned_count' => $totalCount, 'description' => '智能体分成记录'];
        }
        
        $output->writeln("<info>  📊 发现 {$totalCount} 条已结算记录需要归档，开始分批处理...</info>");

        // 使用安全的归档机制进行分批处理
        $totalArchived = 0;
        $processed = 0;
        $archiveTableName = $tableName . '_archive';

        // 确保归档表存在
        $this->ensureArchiveTableExists($tableName, $archiveTableName, $output);

        while ($processed < $totalCount) {
            $currentBatchSize = min($this->batchSize, $totalCount - $processed);

            // 获取当前批次的记录（使用安全查询）
            $records = Db::table($tableName)
                ->where('create_time', '<', $cutoffTimestamp)
                ->where('settle_status', '=', 1)
                ->limit($currentBatchSize)
                ->select();

            if (empty($records)) {
                break; // 没有更多记录
            }

            // 开始事务确保数据一致性
            Db::startTrans();
            try {
                $currentTime = time();
                $archivedCount = 0;

                // 安全地插入到归档表
                foreach ($records->toArray() as $record) {
                    $archiveRecord = $record;
                    $archiveRecord['archived_at'] = $currentTime;
                    $archiveRecord['archived_by'] = 'system_cleanup';
                    $archiveRecord['archive_reason'] = 'retention_policy';

                    Db::table($archiveTableName)->insert($archiveRecord);
                    $archivedCount++;
                }

                // 从原表删除记录（已归档，可以安全删除）
                $recordIds = array_column($records->toArray(), 'id');
                $deleted = Db::table($tableName)
                    ->whereIn('id', $recordIds)
                    ->delete();

                Db::commit();

                $processed += $deleted;
                $totalArchived += $archivedCount;

                $output->writeln("<info>    ⏳ 已归档 {$processed}/{$totalCount} 条记录</info>");

                // 休息一下，避免给数据库造成压力
                usleep(100000); // 0.1秒

            } catch (Exception $e) {
                Db::rollback();
                throw new Exception("归档处理失败: " . $e->getMessage());
            }
        }
        
        $output->writeln("<info>  ✅ 完成归档，共处理 {$processed} 条已结算记录</info>");

        return ['cleaned_count' => $processed, 'description' => '智能体分成记录'];
    }

    /**
     * 检查表是否存在
     */
    private function tableExists(string $tableName): bool
    {
        try {
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 输出清理总结
     */
    private function outputSummary(array $results, int $total, int $days, bool $dryRun, Output $output): void
    {
        $output->writeln("<info>=== 清理总结 ===</info>");
        
        if ($total == 0) {
            $output->writeln("<comment>✨ 没有发现需要清理的分成记录</comment>");
            return;
        }
        
        foreach ($results as $key => $info) {
            $output->writeln("<info>💰 {$info['description']}: {$info['cleaned_count']} 条记录</info>");
        }
        
        $action = $dryRun ? '发现' : '清理';
        $output->writeln("<info>🎯 总计{$action}: {$total} 条分成记录（保留{$days}天内）</info>");
        
        if ($dryRun) {
            $output->writeln("<comment>💡 要执行实际清理，请移除 --dry-run 参数</comment>");
            $output->writeln("<comment>⚠️ 注意：这是财务敏感数据，清理前请确保已完成必要的财务审计</comment>");
        } else {
            $output->writeln("<info>💾 已释放数据库存储空间，保留财务审计要求的必要数据</info>");
            $this->suggestDatabaseOptimization($output);
        }
    }

    /**
     * 建议数据库优化
     */
    private function suggestDatabaseOptimization(Output $output): void
    {
        $output->writeln("");
        $output->writeln("<comment>💡 建议执行数据库优化以回收存储空间：</comment>");
        $output->writeln("<comment>   OPTIMIZE TABLE cm_kb_robot_revenue_log;</comment>");
    }

    /**
     * 记录清理结果到日志文件
     */
    private function logCleanupResult(int $days, array $results, int $total): void
    {
        $logData = [
            'cleanup_date' => date('Y-m-d H:i:s'),
            'retention_days' => $days,
            'cleanup_results' => $results,
            'total_cleaned' => $total,
            'execution_type' => 'revenue_cleanup'
        ];
        
        $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }



    /**
     * 初始化日志文件
     */
    private function initLogFile(): void
    {
        $logDir = runtime_path('log');
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFile = $logDir . '/revenue_cleanup.log';
        
        // 检查日志文件大小，如果超过限制则轮转
        $this->rotateLogIfNeeded();
    }

    /**
     * 日志轮转机制
     */
    private function rotateLogIfNeeded(): void
    {
        if (!file_exists($this->logFile)) {
            return;
        }
        
        if (filesize($this->logFile) > $this->maxLogSize) {
            $rotatedFile = $this->logFile . '.' . date('Y-m-d-H-i-s');
            rename($this->logFile, $rotatedFile);
            
            // 只保留最近5个轮转文件
            $this->cleanupOldLogFiles();
        }
    }

    /**
     * 清理旧日志文件
     */
    private function cleanupOldLogFiles(): void
    {
        $logDir = dirname($this->logFile);
        $logBaseName = basename($this->logFile);
        $pattern = $logDir . '/' . $logBaseName . '.*';
        
        $files = glob($pattern);
        if (count($files) > 5) {
            // 按修改时间排序
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的文件
            $filesToDelete = array_slice($files, 0, count($files) - 5);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }

    /**
     * 写入日志
     */
    private function writeLog(string $content): void
    {
        $logEntry = "[" . date('Y-m-d H:i:s') . "] " . $content . PHP_EOL;
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * 验证表名是否在白名单中
     */
    private function validateTableName(string $tableName): bool
    {
        $allowedTables = [
            'cm_kb_robot_revenue_log'
        ];

        return in_array($tableName, $allowedTables, true);
    }

    /**
     * 确保归档表存在
     */
    private function ensureArchiveTableExists(string $sourceTable, string $archiveTable, Output $output): void
    {
        // 验证源表名
        if (!$this->validateTableName($sourceTable)) {
            throw new Exception("无效的源表名: {$sourceTable}");
        }

        // 检查归档表是否存在
        if ($this->tableExists($archiveTable)) {
            $output->writeln("<comment>  📋 归档表 {$archiveTable} 已存在</comment>");
            return;
        }

        $output->writeln("<info>  🏗️ 创建归档表 {$archiveTable}...</info>");

        try {
            // 创建归档表（复制源表结构）
            $createTableSql = "CREATE TABLE `{$archiveTable}` LIKE `{$sourceTable}`";
            Db::execute($createTableSql);

            // 添加归档相关字段
            $alterSql = "ALTER TABLE `{$archiveTable}`
                         ADD COLUMN `archived_at` int(11) NOT NULL DEFAULT 0 COMMENT '归档时间',
                         ADD COLUMN `archived_by` varchar(100) DEFAULT 'system' COMMENT '归档操作者',
                         ADD COLUMN `archive_reason` varchar(200) DEFAULT '' COMMENT '归档原因',
                         ADD INDEX `idx_archived_at` (`archived_at`),
                         ADD INDEX `idx_archived_by` (`archived_by`)";

            Db::execute($alterSql);

            $output->writeln("<info>  ✅ 归档表创建完成</info>");

        } catch (Exception $e) {
            throw new Exception("创建归档表失败: " . $e->getMessage());
        }
    }

    /**
     * 验证归档数据完整性
     */
    private function verifyArchiveIntegrity(string $sourceTable, string $archiveTable, int $cutoffTimestamp, Output $output): bool
    {
        $output->writeln("<info>  🔍 验证归档数据完整性...</info>");

        try {
            // 检查归档表中的记录数
            $archiveCount = Db::table($archiveTable)
                ->where('create_time', '<', $cutoffTimestamp)
                ->where('settle_status', '=', 1)
                ->count();

            if ($archiveCount > 0) {
                $output->writeln("<info>  ✅ 归档数据完整性验证通过 ({$archiveCount} 条记录已归档)</info>");
                return true;
            } else {
                $output->writeln("<error>  ❌ 归档数据完整性验证失败</error>");
                $output->writeln("<error>     归档表记录: {$archiveCount}</error>");
                return false;
            }

        } catch (Exception $e) {
            $output->writeln("<error>  ❌ 归档数据完整性验证异常: " . $e->getMessage() . "</error>");
            return false;
        }
    }

    /**
     * 财务合规检查
     */
    private function performFinancialComplianceCheck(int $days, bool $testMode, Output $output): void
    {
        $output->writeln("<info>  📊 执行财务合规检查...</info>");

        if ($testMode) {
            $output->writeln("<comment>  🧪 测试模式：跳过严格的财务合规检查</comment>");
        }

        // 检查保留期是否符合财务法规
        if ($days < 1095 && !$testMode) { // 3年，测试模式下放宽限制
            $output->writeln("<error>  ⚠️ 警告：财务数据保留期少于3年存在合规风险！</error>");
            $output->writeln("<comment>     建议保留期至少3年以满足财务审计要求</comment>");
            $output->writeln("<comment>     当前保留期: {$days}天，建议最少: 1095天</comment>");
        } else {
            $output->writeln("<info>  ✅ 财务合规检查通过 (保留期: {$days}天)</info>");
        }

        // 检查是否有未结算的记录
        $unsettledCount = Db::table('cm_kb_robot_revenue_log')
            ->where('create_time', '<', strtotime("-{$days} days"))
            ->where('settle_status', '<>', 1)
            ->count();

        if ($unsettledCount > 0) {
            if ($testMode) {
                $output->writeln("<comment>  🧪 测试模式：发现 {$unsettledCount} 条未结算记录，将只归档已结算记录</comment>");
                $output->writeln("<info>  ✅ 测试模式下未结算记录检查通过</info>");
            } else {
                throw new Exception("发现 {$unsettledCount} 条未结算记录，不能归档未结算的财务数据。使用 --test-mode 参数可在测试环境下跳过此检查");
            }
        } else {
            $output->writeln("<info>  ✅ 未结算记录检查通过</info>");
        }
    }
}