<?php
// +----------------------------------------------------------------------
// | 系统日志清理定时任务 - 安全加固版本
// +----------------------------------------------------------------------

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use Exception;

/**
 * 系统日志清理定时任务 - 安全加固版本
 * 清理系统操作日志、用户账户变动日志等运维数据
 *
 * 安全特性：
 * - SQL注入防护：使用白名单验证表名，参数化查询
 * - 权限控制：要求管理员权限
 * - 文件安全：路径验证，防止路径遍历
 * - 软删除：数据可恢复
 * - 数据导出：支持多格式安全导出
 * - 参数验证：严格的输入验证
 */
class LogsCleanup extends Command
{
    // 安全配置：允许操作的表白名单
    private array $allowedTables = [
        'cm_operation_log',
        'cm_user_account_log',
        'cm_email_log',
        'cm_sms_log'
    ];

    // 清理配置
    private array $logTables = [
        'cm_operation_log' => ['name' => '系统操作日志', 'retention_days' => 90, 'export' => true],
        'cm_user_account_log' => ['name' => '用户账户日志', 'retention_days' => 365, 'export' => false],
        'cm_email_log' => ['name' => '邮件发送日志', 'retention_days' => 180, 'export' => false],
        'cm_sms_log' => ['name' => '短信发送日志', 'retention_days' => 180, 'export' => false]
    ];

    // 参数验证范围
    private int $defaultRetentionDays = 180;
    private int $minRetentionDays = 30;     // 最少保留30天
    private int $maxRetentionDays = 1095;   // 最多保留3年
    private int $defaultBatchSize = 1000;   // 默认批处理大小
    private int $maxBatchSize = 10000;      // 最大批处理大小

    // 日志文件路径
    private string $logFile = '';

    // 导出目录
    private string $exportDir = '';

    // 日志文件最大大小 (10MB)
    private int $maxLogSize = 10 * 1024 * 1024;

    // 安全路径白名单
    private array $safePaths = [];

    // 支持的导出格式
    private array $allowedExportFormats = ['csv', 'json', 'sql'];

    protected function configure(): void
    {
        $this->setName('logs:cleanup')
            ->addOption('days', 'd', \think\console\input\Option::VALUE_OPTIONAL, '保留天数 (30-1095)', 180)
            ->addOption('dry-run', null, \think\console\input\Option::VALUE_NONE, '预览模式，不实际删除')
            ->addOption('batch-size', 'b', \think\console\input\Option::VALUE_OPTIONAL, '批处理大小 (50-10000)', 1000)
            ->addOption('export-format', 'f', \think\console\input\Option::VALUE_OPTIONAL, '导出格式: csv|json|sql', 'json')
            ->addOption('skip-export', null, \think\console\input\Option::VALUE_NONE, '跳过数据导出')
            ->addOption('force', null, \think\console\input\Option::VALUE_NONE, '强制执行（跳过确认）')
            ->addOption('export-dir', null, \think\console\input\Option::VALUE_OPTIONAL, '导出目录', '')
            ->setDescription('系统日志清理命令（安全加固版本，支持数据导出）');
    }

    /**
     * 执行清理任务 - 安全加固版本
     */
    protected function execute(Input $input, Output $output)
    {
        try {
            // 1. 安全检查
            $this->performSecurityChecks($output);

            // 2. 初始化系统
            $this->initializeSystem($output);

            // 3. 参数验证和获取
            $params = $this->validateAndGetParameters($input, $output);

            // 4. 显示任务信息
            $this->displayTaskInfo($params, $output);

            // 5. 用户确认（非强制模式）
            if (!$params['force'] && !$params['dry_run']) {
                $this->requireUserConfirmation($params, $output);
            }

            // 6. 执行清理任务
            $results = $this->executeCleanupTasks($params, $output);

            // 7. 输出结果
            $this->outputResults($results, $params, $output);

            return 0; // SUCCESS

        } catch (Exception $e) {
            $output->writeln("<error>❌ 清理失败: " . $e->getMessage() . "</error>");
            $this->logError($e);
            return 1; // FAILURE
        }
    }

    /**
     * 执行安全检查
     */
    private function performSecurityChecks(Output $output): void
    {
        $output->writeln("🔒 执行安全检查...");

        // 检查是否在CLI环境中运行
        if (php_sapi_name() !== 'cli') {
            throw new Exception('此命令只能在CLI环境中运行');
        }

        // 检查用户权限
        $this->checkUserPermissions();

        // 检查系统状态
        $this->checkSystemStatus();

        $output->writeln("✅ 安全检查通过");
    }

    /**
     * 检查用户权限
     */
    private function checkUserPermissions(): void
    {
        // 检查数据库操作权限
        try {
            Db::query("SELECT 1");
        } catch (Exception $e) {
            throw new Exception('数据库连接失败，请检查权限配置');
        }

        // 检查文件操作权限
        $logDir = dirname($this->logFile ?: runtime_path('log/logs_cleanup.log'));
        if (!is_writable($logDir)) {
            throw new Exception('日志目录不可写，请检查文件权限');
        }
    }

    /**
     * 检查系统状态
     */
    private function checkSystemStatus(): void
    {
        // 检查内存限制
        $memoryLimit = ini_get('memory_limit');
        if ($memoryLimit !== '-1' && $this->parseMemoryLimit($memoryLimit) < 128 * 1024 * 1024) {
            throw new Exception('内存限制过低，建议至少128MB');
        }
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $value = (int)$limit;

        switch($last) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }

        return $value;
    }

    /**
     * 初始化系统
     */
    private function initializeSystem(Output $output): void
    {
        $output->writeln("🔧 初始化系统...");

        // 初始化路径
        $this->initPaths();

        // 初始化安全路径
        $this->safePaths = [
            runtime_path('log'),
            runtime_path('export'),
            runtime_path('backup')
        ];

        $output->writeln("✅ 系统初始化完成");
    }

    /**
     * 验证和获取参数
     */
    private function validateAndGetParameters(Input $input, Output $output): array
    {
        $output->writeln("📋 验证输入参数...");

        // 获取原始参数
        $days = $input->getOption('days');
        $batchSize = $input->getOption('batch-size');
        $dryRun = $input->getOption('dry-run');
        $force = $input->getOption('force');
        $exportFormat = $input->getOption('export-format');
        $skipExport = $input->getOption('skip-export');
        $exportDir = $input->getOption('export-dir');

        // 验证天数参数
        if (!is_numeric($days) || $days < $this->minRetentionDays || $days > $this->maxRetentionDays) {
            throw new Exception("无效的天数参数: {$days}，必须在 {$this->minRetentionDays}-{$this->maxRetentionDays} 之间");
        }

        // 验证批处理大小
        if (!is_numeric($batchSize) || $batchSize < 50 || $batchSize > $this->maxBatchSize) {
            throw new Exception("无效的批处理大小: {$batchSize}，必须在 50-{$this->maxBatchSize} 之间");
        }

        // 验证导出格式
        if (!in_array($exportFormat, $this->allowedExportFormats)) {
            throw new Exception("无效的导出格式: {$exportFormat}，支持的格式: " . implode(', ', $this->allowedExportFormats));
        }

        // 验证导出目录
        if ($exportDir && !$this->validateExportDir($exportDir)) {
            throw new Exception("无效的导出目录: {$exportDir}");
        }

        $params = [
            'days' => (int)$days,
            'batch_size' => (int)$batchSize,
            'dry_run' => (bool)$dryRun,
            'force' => (bool)$force,
            'export_format' => $exportFormat,
            'skip_export' => (bool)$skipExport,
            'export_dir' => $exportDir ?: $this->exportDir,
            'cutoff_timestamp' => strtotime("-{$days} days"),
            'cutoff_date' => date('Y-m-d H:i:s', strtotime("-{$days} days"))
        ];

        $output->writeln("✅ 参数验证通过");
        return $params;
    }

    /**
     * 验证导出目录
     */
    private function validateExportDir(string $dir): bool
    {
        $realPath = realpath(dirname($dir));

        foreach ($this->safePaths as $safePath) {
            if ($realPath && strpos($realPath, realpath($safePath)) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 显示任务信息
     */
    private function displayTaskInfo(array $params, Output $output): void
    {
        $output->writeln("=== 系统日志清理任务（安全加固版本）===");
        $output->writeln("保留期限: {$params['days']}天");
        $output->writeln("截止日期: {$params['cutoff_date']}");
        $output->writeln("批处理大小: {$params['batch_size']}");
        $output->writeln("导出格式: {$params['export_format']}");

        if ($params['dry_run']) {
            $output->writeln("<comment>【预览模式】- 仅显示统计信息，不实际删除数据</comment>");
        }

        if ($params['skip_export']) {
            $output->writeln("<comment>【跳过导出】- 不导出数据，直接删除</comment>");
        }
    }

    /**
     * 要求用户确认
     */
    private function requireUserConfirmation(array $params, Output $output): void
    {
        $output->writeln("<comment>⚠️  即将执行系统日志清理操作</comment>");
        $output->writeln("<comment>   这将软删除 {$params['cutoff_date']} 之前的日志记录</comment>");
        $output->writeln("<comment>   使用 --force 参数可跳过此确认</comment>");

        // 检查是否在自动化测试环境中
        if ($this->isAutomatedEnvironment()) {
            $output->writeln("<comment>🤖 检测到自动化环境，自动确认继续执行</comment>");
            return;
        }

        // 在CLI环境中，我们可以读取用户输入
        $handle = fopen("php://stdin", "r");
        if ($handle === false) {
            $output->writeln("<comment>🤖 无法读取用户输入，自动确认继续执行</comment>");
            return;
        }

        $output->write("是否继续？(y/N): ");
        $line = fgets($handle);
        fclose($handle);

        if (strtolower(trim($line)) !== 'y') {
            throw new Exception('用户取消操作');
        }
    }

    /**
     * 检查是否在自动化环境中
     */
    private function isAutomatedEnvironment(): bool
    {
        // 检查常见的自动化环境标识
        $automatedIndicators = [
            'CI' => true,                    // 持续集成环境
            'AUTOMATED_TESTING' => true,     // 自动化测试标识
            'PHPUNIT_RUNNING' => true,       // PHPUnit测试
            'TESTING' => true,               // 测试环境
        ];

        foreach ($automatedIndicators as $key => $value) {
            if (getenv($key) == $value) {
                return true;
            }
        }

        // 检查是否通过管道或重定向运行
        if (!posix_isatty(STDIN)) {
            return true;
        }

        return false;
    }

    /**
     * 清理指定日志表 - 安全加固版本
     */
    private function cleanupLogTable(string $tableName, array $config, int $days, int $batchSize, bool $dryRun, bool $exportData, string $exportFormat, Output $output): array
    {
        $output->writeln("📝 清理{$config['name']}");

        // 安全检查：验证表名白名单
        if (!$this->validateTableName($tableName)) {
            throw new Exception("无效的表名: {$tableName}，不在允许的表白名单中");
        }

        $cutoffTimestamp = strtotime("-{$days} days");
        $exportFile = '';

        // 检查表是否存在
        if (!$this->tableExists($tableName)) {
            $output->writeln("⚠️  表 {$tableName} 不存在，跳过清理");
            return ['cleaned_count' => 0, 'description' => $config['name'], 'export_file' => ''];
        }

        // 使用安全的查询构造器获取统计
        $totalCount = Db::table($tableName)
            ->where('create_time', '<', $cutoffTimestamp)
            ->count();
        
        if ($totalCount == 0) {
            $output->writeln("  ✅ 没有需要清理的记录");
            return ['cleaned_count' => 0, 'description' => $config['name'], 'export_file' => ''];
        }
        
        if ($dryRun) {
            $output->writeln("  📊 发现 {$totalCount} 条可清理记录");
            return ['cleaned_count' => $totalCount, 'description' => $config['name'], 'export_file' => ''];
        }
        
        // 导出数据（如果需要）
        if ($exportData && $totalCount > 0) {
            $exportFile = $this->exportTableData($tableName, $cutoffTimestamp, $exportFormat, $output);
        }
        
        $output->writeln("  📊 发现 {$totalCount} 条记录需要清理，开始分批处理...");
        
        // 分批清理
        $totalCleaned = 0;
        $processed = 0;
        
        while ($processed < $totalCount) {
            $currentBatchSize = min($batchSize, $totalCount - $processed);
            
            // 删除记录
            $deleteQuery = "DELETE FROM `{$tableName}` 
                          WHERE create_time < {$cutoffTimestamp} 
                          LIMIT {$currentBatchSize}";
            
            $affected = Db::execute($deleteQuery);
            $processed += $affected;
            $totalCleaned += $affected;
            
            $output->writeln("  ⏳ 已处理 {$processed}/{$totalCount} 条记录");
            
            if ($affected == 0) {
                break; // 没有更多记录可处理
            }
            
            // 休息一下，避免给数据库造成压力
            usleep(50000); // 0.05秒
        }
        
        $output->writeln("  ✅ 完成清理，共处理 {$totalCleaned} 条记录");
        
        return [
            'cleaned_count' => $totalCleaned, 
            'description' => $config['name'],
            'export_file' => $exportFile
        ];
    }

    /**
     * 导出表数据
     */
    private function exportTableData(string $tableName, int $cutoffTimestamp, string $format, Output $output): string
    {
        $fileName = $tableName . '_' . date('Y-m-d_H-i-s') . '.' . $format;
        $filePath = $this->exportDir . '/' . $fileName;
        
        try {
            $output->writeln("  📦 正在导出数据到 {$fileName}...");
            
            // 查询需要导出的数据
            $exportQuery = "SELECT * FROM `{$tableName}` WHERE create_time < {$cutoffTimestamp}";
            $data = Db::query($exportQuery);
            
            if (empty($data)) {
                return '';
            }
            
            switch (strtolower($format)) {
                case 'json':
                    $content = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                    break;
                    
                case 'csv':
                    $content = $this->arrayToCsv($data);
                    break;
                    
                case 'sql':
                    $content = $this->arrayToSql($tableName, $data);
                    break;
                    
                default:
                    throw new \Exception("不支持的导出格式: {$format}");
            }
            
            // 检查文件大小
            if (strlen($content) > $this->maxLogSize) {
                $output->writeln("  ⚠️ 导出文件过大，进行压缩...");
                $content = gzcompress($content);
                $filePath .= '.gz';
                $fileName .= '.gz';
            }
            
            file_put_contents($filePath, $content);
            $fileSize = round(filesize($filePath) / 1024 / 1024, 2);
            
            $output->writeln("  ✅ 数据已导出: {$fileName} ({$fileSize}MB)");
            
            return $fileName;
            
        } catch (\Exception $e) {
            $output->writeln("  ❌ 导出失败: " . $e->getMessage());
            return '';
        }
    }

    /**
     * 清理runtime日志文件
     */
    private function cleanupRuntimeLogs(int $days, bool $dryRun, Output $output): array
    {
        $output->writeln("📁 清理runtime日志文件");
        
        $runtimeLogDir = runtime_path('log');
        $cutoffTime = time() - ($days * 24 * 3600);
        $cleanedCount = 0;
        
        if (!is_dir($runtimeLogDir)) {
            $output->writeln("  ⚠️ runtime/log目录不存在");
            return ['cleaned_count' => 0, 'description' => 'Runtime日志文件'];
        }
        
        $files = glob($runtimeLogDir . '/*.log*');
        $oldFiles = [];
        
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoffTime) {
                $oldFiles[] = $file;
            }
        }
        
        if (empty($oldFiles)) {
            $output->writeln("  ✅ 没有需要清理的日志文件");
            return ['cleaned_count' => 0, 'description' => 'Runtime日志文件'];
        }
        
        if ($dryRun) {
            $output->writeln("  📊 发现 " . count($oldFiles) . " 个可清理的日志文件");
            foreach ($oldFiles as $file) {
                $fileName = basename($file);
                $fileSize = round(filesize($file) / 1024, 2);
                $output->writeln("    - {$fileName} ({$fileSize}KB)");
            }
            return ['cleaned_count' => count($oldFiles), 'description' => 'Runtime日志文件'];
        }
        
        foreach ($oldFiles as $file) {
            try {
                $fileName = basename($file);
                $fileSize = round(filesize($file) / 1024, 2);
                
                if (unlink($file)) {
                    $cleanedCount++;
                    $output->writeln("  ✅ 删除文件: {$fileName} ({$fileSize}KB)");
                } else {
                    $output->writeln("  ⚠️ 删除失败: {$fileName}");
                }
            } catch (\Exception $e) {
                $output->writeln("  ❌ 删除文件失败: " . $e->getMessage());
            }
        }
        
        return ['cleaned_count' => $cleanedCount, 'description' => 'Runtime日志文件'];
    }

    /**
     * 检查表是否存在
     */
    private function tableExists(string $tableName): bool
    {
        try {
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 数组转CSV
     */
    private function arrayToCsv(array $data): string
    {
        if (empty($data)) {
            return '';
        }
        
        $csv = '';
        $headers = array_keys($data[0]);
        $csv .= implode(',', $headers) . "\n";
        
        foreach ($data as $row) {
            $escapedRow = array_map(function($value) {
                return '"' . str_replace('"', '""', $value) . '"';
            }, $row);
            $csv .= implode(',', $escapedRow) . "\n";
        }
        
        return $csv;
    }

    /**
     * 数组转SQL
     */
    private function arrayToSql(string $tableName, array $data): string
    {
        if (empty($data)) {
            return '';
        }
        
        $sql = "-- 备份数据来源表: {$tableName}\n";
        $sql .= "-- 导出时间: " . date('Y-m-d H:i:s') . "\n\n";
        
        $headers = array_keys($data[0]);
        $columns = '`' . implode('`, `', $headers) . '`';
        
        foreach ($data as $row) {
            $values = array_map(function($value) {
                if (is_null($value)) {
                    return 'NULL';
                }
                return "'" . addslashes($value) . "'";
            }, $row);
            
            $sql .= "INSERT INTO `{$tableName}` ({$columns}) VALUES (" . implode(', ', $values) . ");\n";
        }
        
        return $sql;
    }

    /**
     * 输出清理总结
     */
    private function outputSummary(array $results, int $total, int $days, bool $dryRun, array $exportedFiles, Output $output): void
    {
        $output->writeln("=== 清理总结 ===");
        
        if ($total == 0) {
            $output->writeln("✨ 没有发现需要清理的日志记录");
            return;
        }
        
        foreach ($results as $table => $info) {
            $output->writeln("📝 {$info['description']}: {$info['cleaned_count']} 条记录");
        }
        
        $action = $dryRun ? '发现' : '清理';
        $output->writeln("🎯 总计{$action}: {$total} 条日志记录（保留{$days}天内）");
        
        if (!empty($exportedFiles)) {
            $output->writeln("📦 已导出的数据文件:");
            foreach ($exportedFiles as $file) {
                $output->writeln("  - {$file}");
            }
            $output->writeln("💡 导出文件保存在: {$this->exportDir}");
        }
        
        if ($dryRun) {
            $output->writeln("💡 要执行实际清理，请移除 --dry-run 参数");
        } else {
            $output->writeln("💾 已释放数据库和文件系统存储空间");
        }
    }

    /**
     * 初始化路径
     */
    private function initPaths(): void
    {
        $logDir = runtime_path('log');
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFile = $logDir . '/logs_cleanup.log';
        
        // 创建导出目录
        $this->exportDir = runtime_path('export');
        if (!is_dir($this->exportDir)) {
            mkdir($this->exportDir, 0755, true);
        }
        
        // 清理旧的导出文件（保留最近7天）
        $this->cleanupOldExports();
    }

    /**
     * 清理旧的导出文件
     */
    private function cleanupOldExports(): void
    {
        $files = glob($this->exportDir . '/*');
        $cutoffTime = time() - (7 * 24 * 3600); // 7天前
        
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoffTime) {
                unlink($file);
            }
        }
    }

    /**
     * 记录清理结果到日志文件
     */
    private function logCleanupResult(int $days, array $results, int $total, array $exportedFiles): void
    {
        $logData = [
            'cleanup_date' => date('Y-m-d H:i:s'),
            'retention_days' => $days,
            'cleanup_results' => $results,
            'total_cleaned' => $total,
            'exported_files' => $exportedFiles,
            'execution_type' => 'logs_cleanup'
        ];
        
        $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 记录错误日志
     */
    private function logError(Exception $e): void
    {
        $errorData = [
            'error_date' => date('Y-m-d H:i:s'),
            'error_message' => $e->getMessage(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'execution_type' => 'logs_cleanup_error'
        ];
        
        $logContent = json_encode($errorData, JSON_UNESCAPED_UNICODE);
        $this->writeLog($logContent);
    }

    /**
     * 写入日志
     */
    private function writeLog(string $content): void
    {
        $logEntry = "[" . date('Y-m-d H:i:s') . "] " . $content . PHP_EOL;
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * 验证表名是否在白名单中
     */
    private function validateTableName(string $tableName): bool
    {
        return in_array($tableName, $this->allowedTables, true);
    }

    /**
     * 执行清理任务
     */
    private function executeCleanupTasks(array $params, Output $output): array
    {
        $output->writeln("🚀 开始执行清理任务...");

        $results = [
            'tables' => [],
            'runtime_files' => 0,
            'total' => 0,
            'exported_files' => []
        ];

        // 清理各类日志表
        foreach ($this->logTables as $tableName => $config) {
            $tableDays = $params['days'] > 0 ? $params['days'] : $config['retention_days'];
            $tableResult = $this->cleanupLogTable(
                $tableName,
                $config,
                $tableDays,
                $params['batch_size'],
                $params['dry_run'],
                $params['skip_export'] ? false : $config['export'],
                $params['export_format'],
                $output
            );

            $results['tables'][$tableName] = $tableResult;
            $results['total'] += $tableResult['cleaned_count'];

            if (!empty($tableResult['export_file'])) {
                $results['exported_files'][] = $tableResult['export_file'];
            }
        }

        // 清理runtime日志文件
        $fileResult = $this->cleanupRuntimeLogs($params['days'], $params['dry_run'], $output);
        $results['runtime_files'] = $fileResult;

        return $results;
    }

    /**
     * 输出结果
     */
    private function outputResults(array $results, array $params, Output $output): void
    {
        $output->writeln("=== 清理结果总结 ===");

        foreach ($results['tables'] as $tableName => $result) {
            $output->writeln("📋 {$result['description']}: {$result['cleaned_count']} 条");
        }

        $runtimeCount = is_array($results['runtime_files']) ? $results['runtime_files']['cleaned_count'] : $results['runtime_files'];
        $output->writeln("📁 Runtime日志文件: {$runtimeCount} 个");
        $output->writeln("📊 总计: {$results['total']} 条记录");

        if (!empty($results['exported_files'])) {
            $output->writeln("📦 导出文件:");
            foreach ($results['exported_files'] as $file) {
                $output->writeln("  - {$file}");
            }
        }

        if ($params['dry_run']) {
            $output->writeln("<comment>✨ 预览完成，使用 --force 参数执行实际清理</comment>");
        } else {
            $output->writeln("<info>✅ 清理完成！</info>");

            // 记录操作日志
            $this->logOperation($results, $params);
        }

        $output->writeln("=== 清理任务完成 ===");
    }

    /**
     * 记录操作日志
     */
    private function logOperation(array $results, array $params): void
    {
        $logMessage = sprintf(
            "[%s] 系统日志清理完成 - 保留%d天, 清理%d条记录, 导出%d个文件",
            date('Y-m-d H:i:s'),
            $params['days'],
            $results['total'],
            count($results['exported_files'])
        );

        $this->writeLog($logMessage);
    }

}