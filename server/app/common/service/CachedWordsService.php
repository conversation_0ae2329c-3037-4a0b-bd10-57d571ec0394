<?php
/**
 * 带Redis缓存的敏感词服务类（优化版）
 * 优化敏感词库加载性能，减少重复解密和构建操作
 * 新增：布隆过滤器、多级缓存、批量检测优化
 * Redis不可用时自动降级到文件缓存
 */

namespace app\common\service;

use app\common\model\SensitiveWord;
use DfaFilter\SensitiveHelper;
use Error;
use Exception;
use think\facade\Cache;
use think\facade\Log;

class CachedWordsService
{
    // 缓存键名
    private static $cacheKey = 'sensitive_words_tree';
    private static $wordsKey = 'sensitive_words_data';
    private static $versionKey = 'sensitive_words_version';
    private static $bloomKey = 'sensitive_bloom_filter';
    private static $treeKey = 'sensitive_dfa_tree';
    
    // 缓存时间（秒）
    private static $cacheTime = 86400; // 24小时
    private static $bloomCacheTime = 3600; // 布隆过滤器1小时
    private static $treeCacheTime = 7200; // DFA树2小时
    
    // 内存缓存（内存优化版本）
    private static $memoryCache = [];
    private static $memoryCacheExpire = [];
    private static $bloomFilter = null;
    private static $dfaTree = null;
    
    // Docker环境内存管理
    private static int $maxMemoryCacheSize = 1000; // 最大缓存项数
    private static int $currentMemoryCacheSize = 0;
    private static int $memoryCheckInterval = 100; // 每100次检查内存
    private static int $checkCount = 0;
    private const MEMORY_THRESHOLD = 40 * 1024 * 1024; // 40MB阈值
    
    // Redis可用性检查
    private static $redisAvailable = null;
    private static $cacheMode = 'auto'; // auto, memory, file, redis
    
    // 性能统计
    private static $stats = [
        'cache_hits' => 0,
        'cache_misses' => 0,
        'bloom_hits' => 0,
        'bloom_misses' => 0,
        'build_time' => 0,
        'detect_time' => 0,
        'bloom_false_positive' => 0,
        'redis_available' => false,
        'cache_mode' => 'memory'
    ];
    
    /**
     * 检查Redis是否可用
     */
    private static function checkRedisAvailable(): bool
    {
        if (self::$redisAvailable !== null) {
            return self::$redisAvailable;
        }
        
        try {
            // 检查Redis扩展是否加载
            if (!extension_loaded('redis')) {
                self::$redisAvailable = false;
                self::$cacheMode = 'memory';
                self::$stats['redis_available'] = false;
                self::$stats['cache_mode'] = 'memory';
                return false;
            }
            
            // 尝试连接Redis
            Cache::get('test_redis_connection');
            self::$redisAvailable = true;
            self::$cacheMode = 'redis';
            self::$stats['redis_available'] = true;
            self::$stats['cache_mode'] = 'redis';
            return true;
            
        } catch (Exception $e) {
            self::$redisAvailable = false;
            self::$cacheMode = 'memory';
            self::$stats['redis_available'] = false;
            self::$stats['cache_mode'] = 'memory';
            
            // 记录Redis不可用的情况，但不阻断业务
            Log::info('Redis不可用，切换到内存缓存模式', [
                'error' => $e->getMessage(),
                'cache_mode' => self::$cacheMode
            ]);
            
            return false;
        }
    }
    
    /**
     * 安全的缓存获取
     */
    private static function safeGet(string $key, $default = false)
    {
        try {
            if (self::checkRedisAvailable()) {
                return Cache::get($key, $default);
            } else {
                // 使用内存缓存
                $now = time();
                if (isset(self::$memoryCache[$key]) && 
                    isset(self::$memoryCacheExpire[$key]) && 
                    self::$memoryCacheExpire[$key] > $now) {
                    return self::$memoryCache[$key];
                }
                return $default;
            }
        } catch (Exception $e) {
            Log::warning('缓存获取失败', ['key' => $key, 'error' => $e->getMessage()]);
            return $default;
        }
    }
    
    /**
     * 安全的缓存设置
     */
    private static function safeSet(string $key, $value, int $ttl = 3600): bool
    {
        try {
            if (self::checkRedisAvailable()) {
                return Cache::set($key, $value, $ttl);
            } else {
                // 使用内存缓存
                self::$memoryCache[$key] = $value;
                self::$memoryCacheExpire[$key] = time() + $ttl;
                self::$currentMemoryCacheSize++;
                
                // 内存缓存大小控制
                if (self::$currentMemoryCacheSize > self::$maxMemoryCacheSize) {
                    self::performLRUCleanup();
                }
                
                return true;
            }
        } catch (Exception $e) {
            Log::warning('缓存设置失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 敏感词检测（稳定优化版 - 基于WordsService逻辑优化）
     */
    public static function sensitive(string $content): void
    {
        $startTime = microtime(true);
        
        try {
            // 快速空内容检查
            if (empty(trim($content))) {
                return;
            }
            
            // 获取缓存的敏感词数据
            $sensitiveWords = self::getStableSensitiveWords();
            
            if (empty($sensitiveWords)) {
                return;
            }
            
            // 使用与WordsService相同的分块检测逻辑
            $sensitiveWordArr = array_chunk($sensitiveWords, 20000); // 与WordsService保持一致
            $sensitiveWordGroup = [];
            
            foreach ($sensitiveWordArr as $sensitiveWordArrValue) {
                $handle = SensitiveHelper::init()->setTree($sensitiveWordArrValue);
                $badWordList = $handle->getBadWord($content);
                $sensitiveWordGroup = array_merge($sensitiveWordGroup, $badWordList);
            }
            
            $sensitiveWordGroup = array_unique($sensitiveWordGroup);
            if (!empty($sensitiveWordGroup)) {
                throw new Exception('提问存在敏感词：' . implode(',', $sensitiveWordGroup));
            }
            
        } catch (Exception $e) {
            // 重新抛出敏感词异常
            if (strpos($e->getMessage(), '敏感词') !== false) {
                throw $e;
            }
            
            // 其他异常时的降级处理
            Log::error('敏感词检测异常，降级到WordsService', [
                'error' => $e->getMessage(),
                'content_length' => strlen($content)
            ]);
            
            // 降级到原始WordsService逻辑
            self::fallbackToOriginalLogic($content);
            
        } finally {
            // 记录检测时间
            self::$stats['detect_time'] += (microtime(true) - $startTime) * 1000;
        }
    }
    
    /**
     * 降级到原始WordsService逻辑
     */
    private static function fallbackToOriginalLogic(string $content): void
    {
        // Docker环境优化：强制启用文件敏感词，避免数据库连接问题
        $isSensitive = 1;        // 强制启用文件敏感词
        $isSensitiveSystem = 0;  // 禁用数据库敏感词（避免Docker环境数据库连接问题）

        $systemSensitiveArr = [];
        if ($isSensitiveSystem) {
            // 获取数据库敏感词
            $sensitiveWord = (new SensitiveWord())->where(['status' => 1])->column('word');
            // 一条数据可能含有多个敏感词, '；' 分隔开
            foreach ($sensitiveWord as $sensitiveWordValue) {
                $systemSensitiveArr = array_merge($systemSensitiveArr, explode('；', $sensitiveWordValue));
            }
        }

        $fileSensitiveArr = [];
        if ($isSensitive) {
            // 读取敏感词文件, 加密的密钥
            $file = fopen("../extend/sensitive_key.bin", "rb");
            $key  = fread($file, 32);
            $iv   = fread($file, 16);
            fclose($file);
            // 读取加密的数据
            $ciphertext = file_get_contents("../extend/sensitive_data.bin");
            // 使用 CBC 模式解密数据
            $plaintext = openssl_decrypt($ciphertext, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
            // 过滤敏感词
            $fileSensitiveArr = explode(PHP_EOL, trim($plaintext));
        }

        if (empty($systemSensitiveArr) && empty($fileSensitiveArr)) {
            return;
        }

        $sensitiveWord = array_merge($fileSensitiveArr, $systemSensitiveArr);
        $sensitiveWordArr = array_chunk($sensitiveWord, 20000);//拆分数组
        $sensitiveWordGroup = [];

        foreach ($sensitiveWordArr as $sensitiveWordArrValue) {
            $handle = SensitiveHelper::init()->setTree($sensitiveWordArrValue);
            $badWordList = $handle->getBadWord($content);// 获取内容中所有的敏感词
            $sensitiveWordGroup = array_merge($sensitiveWordGroup, $badWordList);
        }

        $sensitiveWordGroup = array_unique($sensitiveWordGroup);
        if (!empty($sensitiveWordGroup)) {
            throw new Exception('提问存在敏感词：' . implode(',', $sensitiveWordGroup));
        }
    }
    
    /**
     * 获取稳定的敏感词数据（带缓存优化）
     */
    private static function getStableSensitiveWords(): array
    {
        // 生成缓存版本号
        $version = self::generateStableVersion();
        $cacheKey = self::$wordsKey . '_stable';
        $versionKey = self::$versionKey . '_stable';
        
        // 检查缓存是否有效
        $cachedVersion = self::safeGet($versionKey, '');
        if ($cachedVersion === $version) {
            $cachedWords = self::safeGet($cacheKey);
            if ($cachedWords !== false) {
                self::$stats['cache_hits']++;
                return $cachedWords;
            }
        }
        
        // 缓存失效，重新加载
        self::$stats['cache_misses']++;
        
        // Docker环境优化：强制启用文件敏感词，避免数据库连接问题
        $isSensitive = 1;        // 强制启用文件敏感词（确保敏感词检测正常工作）
        $isSensitiveSystem = 0;  // 禁用数据库敏感词（避免Docker环境数据库连接问题）
        
        // 记录配置使用情况
        Log::info('敏感词服务：使用Docker优化配置', [
            'file_sensitive' => $isSensitive,
            'db_sensitive' => $isSensitiveSystem,
            'reason' => 'Docker环境优化，避免数据库连接问题'
        ]);
        
        $allWords = [];
        
        // 加载文件敏感词
        if ($isSensitive) {
            $fileWords = self::loadFileWords();
            $allWords = array_merge($allWords, $fileWords);
        }
        
        // 加载数据库敏感词
        if ($isSensitiveSystem) {
            $dbWords = self::loadDatabaseWords();
            $allWords = array_merge($allWords, $dbWords);
        }
        
        // 去重并过滤空值
        $allWords = array_unique(array_filter($allWords));
        
        // 缓存结果
        self::safeSet($cacheKey, $allWords, self::$cacheTime);
        self::safeSet($versionKey, $version, self::$cacheTime);
        
        return $allWords;
    }
    
    /**
     * 安全获取配置（数据库连接失败时使用默认值）
     */
    private static function safeGetConfig(string $config, string $key, $default = null)
    {
        try {
            return ConfigService::get($config, $key, $default);
        } catch (Exception $e) {
            // 数据库连接失败时使用默认值
            Log::warning('敏感词服务：配置读取失败，使用默认值', [
                'config' => $config,
                'key' => $key,
                'default' => $default,
                'error' => $e->getMessage()
            ]);
            return $default;
        } catch (Error $e) {
            // 捕获PHP Fatal Error（比如数据库连接为null）
            Log::warning('敏感词服务：配置读取错误，使用默认值', [
                'config' => $config,
                'key' => $key,
                'default' => $default,
                'error' => $e->getMessage()
            ]);
            return $default;
        }
    }
    
    /**
     * 生成稳定版本号
     */
    private static function generateStableVersion(): string
    {
        $factors = [
            'file_key' => file_exists('../extend/sensitive_key.bin') ? filemtime('../extend/sensitive_key.bin') : 0,
            'file_data' => file_exists('../extend/sensitive_data.bin') ? filemtime('../extend/sensitive_data.bin') : 0,
            'is_sensitive' => self::safeGetConfig('chat', 'is_sensitive', 1),
            'is_sensitive_system' => self::safeGetConfig('chat', 'is_sensitive_system', 1),
            'db_count' => 0
        ];
        
        // 尝试获取数据库敏感词数量
        try {
            $factors['db_count'] = (new SensitiveWord())->where(['status' => 1])->count();
        } catch (Exception $e) {
            // 数据库不可用时使用默认值
            $factors['db_count'] = 0;
        }
        
        return md5(serialize($factors));
    }
    
    /**
     * Docker环境内存管理
     */
    private static function memoryManagement(): void
    {
        $currentMemory = memory_get_usage(true);
        
        // 如果内存使用超过阈值，执行清理
        if ($currentMemory > self::MEMORY_THRESHOLD) {
            self::performMemoryCleanup();
        }
        
        // 清理过期的内存缓存
        self::cleanExpiredMemoryCache();
        
        // LRU清理：如果缓存过大，移除最旧的项
        if (self::$currentMemoryCacheSize > self::$maxMemoryCacheSize) {
            self::performLRUCleanup();
        }
    }
    
    /**
     * 执行内存清理
     */
    private static function performMemoryCleanup(): void
    {
        $beforeMemory = memory_get_usage(true);
        
        // 清理内存缓存
        self::$memoryCache = [];
        self::$memoryCacheExpire = [];
        self::$currentMemoryCacheSize = 0;
        
        // 清理布隆过滤器（释放大量内存）
        self::$bloomFilter = null;
        
        // 保留DFA树，但重置其他缓存
        // self::$dfaTree = null; // 不清理DFA树，因为重建成本高
        
        // 强制垃圾回收
        $cycles = gc_collect_cycles();
        
        $afterMemory = memory_get_usage(true);
        $freedMemory = $beforeMemory - $afterMemory;
        
        Log::info('敏感词服务内存清理', [
            'freed_memory_mb' => round($freedMemory / 1024 / 1024, 2),
            'current_memory_mb' => round($afterMemory / 1024 / 1024, 2),
            'gc_cycles' => $cycles
        ]);
    }
    
    /**
     * 清理过期的内存缓存
     */
    private static function cleanExpiredMemoryCache(): void
    {
        $now = time();
        $cleanedCount = 0;
        
        foreach (self::$memoryCacheExpire as $key => $expireTime) {
            if ($expireTime < $now) {
                unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
                self::$currentMemoryCacheSize--;
                $cleanedCount++;
            }
        }
        
        if ($cleanedCount > 0) {
            Log::debug('清理过期内存缓存', ['cleaned_count' => $cleanedCount]);
        }
    }
    
    /**
     * LRU清理策略
     */
    private static function performLRUCleanup(): void
    {
        // 移除25%的最旧缓存项
        $removeCount = intval(self::$maxMemoryCacheSize * 0.25);
        $removed = 0;
        
        foreach (self::$memoryCache as $key => $value) {
            if ($removed >= $removeCount) {
                break;
            }
            
            unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
            self::$currentMemoryCacheSize--;
            $removed++;
        }
        
        Log::debug('LRU内存缓存清理', ['removed_count' => $removed]);
    }
    
    /**
     * 布隆过滤器检查
     */
    private static function bloomFilterCheck(string $content): bool
    {
        try {
            // 获取布隆过滤器
            $bloomFilter = self::getBloomFilter();
            
            if (empty($bloomFilter)) {
                return true; // 无布隆过滤器数据，需要进行完整检测
            }
            
            // 提取内容中的词汇进行检查
            $words = self::extractWords($content);
            
            foreach ($words as $word) {
                if (isset($bloomFilter[$word])) {
                    return true; // 布隆过滤器命中，可能包含敏感词
                }
            }
            
            return false; // 布隆过滤器确认无敏感词
            
        } catch (\Throwable $e) {
            Log::warning('布隆过滤器检查失败', ['error' => $e->getMessage()]);
            return true; // 出错时进行完整检测
        }
    }
    
    /**
     * 获取布隆过滤器
     */
    private static function getBloomFilter(): ?array
    {
        // 先从内存获取
        if (self::$bloomFilter !== null) {
            return self::$bloomFilter;
        }
        
        // 从缓存获取
        $bloomFilter = self::safeGet(self::$bloomKey);
        
        if ($bloomFilter) {
            self::$bloomFilter = $bloomFilter;
            return $bloomFilter;
        }
        
        // 重建布隆过滤器
        return self::buildBloomFilter();
    }
    
    /**
     * 构建布隆过滤器
     */
    private static function buildBloomFilter(): array
    {
        try {
            $sensitiveWords = self::getSensitiveWords();
            
            if (empty($sensitiveWords)) {
                return [];
            }
            
            $bloomFilter = [];
            
            foreach ($sensitiveWords as $word) {
                $word = trim($word);
                if (!empty($word)) {
                    $bloomFilter[$word] = 1;
                    
                    // 为中文词汇添加子串索引
                    $len = mb_strlen($word, 'UTF-8');
                    if ($len > 2) {
                        for ($i = 0; $i < $len - 1; $i++) {
                            for ($j = $i + 2; $j <= min($i + 4, $len); $j++) { // 限制子串长度
                                $subword = mb_substr($word, $i, $j - $i, 'UTF-8');
                                $bloomFilter[$subword] = 1;
                            }
                        }
                    }
                }
            }
            
            // 缓存布隆过滤器
            self::safeSet(self::$bloomKey, $bloomFilter, self::$bloomCacheTime + rand(0, 300));
            self::$bloomFilter = $bloomFilter;
            
            Log::info('构建布隆过滤器', [
                'words_count' => count($sensitiveWords),
                'bloom_size' => count($bloomFilter)
            ]);
            
            return $bloomFilter;
            
        } catch (\Throwable $e) {
            Log::error('构建布隆过滤器失败', ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 获取优化的DFA树
     */
    private static function getOptimizedDfaTree(): ?SensitiveHelper
    {
        // 先从内存获取
        if (self::$dfaTree !== null) {
            return self::$dfaTree;
        }
        
        // 从缓存获取序列化的树
        $serializedTree = self::safeGet(self::$treeKey);
        
        if ($serializedTree) {
            try {
                self::$dfaTree = unserialize($serializedTree);
                return self::$dfaTree;
            } catch (\Throwable $e) {
                Log::warning('反序列化DFA树失败', ['error' => $e->getMessage()]);
            }
        }
        
        // 重建DFA树
        return self::buildOptimizedDfaTree();
    }
    
    /**
     * 构建优化的DFA树
     */
    private static function buildOptimizedDfaTree(): ?SensitiveHelper
    {
        try {
            $sensitiveWords = self::getSensitiveWords();
            
            if (empty($sensitiveWords)) {
                return null;
            }
            
            // 构建DFA树
            $dfaTree = SensitiveHelper::init()->setTree($sensitiveWords);
            
            // 缓存序列化的树
            try {
                $serializedTree = serialize($dfaTree);
                self::safeSet(self::$treeKey, $serializedTree, self::$treeCacheTime + rand(0, 600));
                
                Log::info('构建DFA树', [
                    'words_count' => count($sensitiveWords),
                    'tree_size' => strlen($serializedTree)
                ]);
                
            } catch (\Throwable $e) {
                Log::warning('序列化DFA树失败', ['error' => $e->getMessage()]);
            }
            
            self::$dfaTree = $dfaTree;
            return $dfaTree;
            
        } catch (\Throwable $e) {
            Log::error('构建DFA树失败', ['error' => $e->getMessage()]);
            return null;
        }
    }
    
    /**
     * 提取内容中的词汇（简化版分词）
     */
    private static function extractWords(string $content): array
    {
        // 简化的分词逻辑，实际可以集成更复杂的中文分词库
        $words = [];
        
        // 按标点符号分割
        $segments = preg_split('/[，。！？；：、\s\n\r\t]+/u', $content);
        
        foreach ($segments as $segment) {
            $segment = trim($segment);
            if (!empty($segment)) {
                $words[] = $segment;
                
                // 对长段落进行滑动窗口分词
                $len = mb_strlen($segment, 'UTF-8');
                if ($len > 10) {
                    for ($i = 0; $i < $len - 2; $i++) {
                        for ($j = 3; $j <= min(8, $len - $i); $j++) {
                            $words[] = mb_substr($segment, $i, $j, 'UTF-8');
                        }
                    }
                }
            }
        }
        
        return array_unique($words);
    }
    
    /**
     * 获取敏感词数据（优化版本 - 参考知识库实现）
     */
    private static function getSensitiveWords(): array
    {
        // 参考知识库敏感词校验的成功实现：强制启用，不依赖数据库配置
        $isSensitive = 1;        // 强制启用文件敏感词（知识库成功模式）
        $isSensitiveSystem = 0;  // 暂时禁用数据库敏感词（避免Docker环境数据库连接问题）
        
        if (!$isSensitive && !$isSensitiveSystem) {
            return [];
        }
        
        // 生成缓存版本号
        $version = self::generateVersion();
        $cachedVersion = self::safeGet(self::$versionKey, '');
        
        // 检查缓存是否有效
        if ($cachedVersion === $version) {
            $cachedWords = self::safeGet(self::$wordsKey);
            if ($cachedWords !== false) {
                self::$stats['cache_hits']++;
                return $cachedWords;
            }
        }
        
        // 缓存失效，重新加载
        self::$stats['cache_misses']++;
        $startTime = microtime(true);
        
        $allWords = [];
        
        // 加载文件敏感词
        if ($isSensitive) {
            $fileWords = self::loadFileWords();
            $allWords = array_merge($allWords, $fileWords);
        }
        
        // 加载数据库敏感词
        if ($isSensitiveSystem) {
            $dbWords = self::loadDatabaseWords();
            $allWords = array_merge($allWords, $dbWords);
        }
        
        // 去重并过滤空值
        $allWords = array_unique(array_filter($allWords));
        
        // 更新缓存
        self::safeSet(self::$wordsKey, $allWords, self::$cacheTime);
        self::safeSet(self::$versionKey, $version, self::$cacheTime);
        
        // 记录构建时间
        self::$stats['build_time'] += (microtime(true) - $startTime) * 1000;
        
        return $allWords;
    }
    
    /**
     * 加载文件敏感词
     */
    private static function loadFileWords(): array
    {
        try {
            // 修复Docker环境路径问题：框架运行在server目录中
            $keyFile = "extend/sensitive_key.bin";
            $dataFile = "extend/sensitive_data.bin";
            
            if (!file_exists($keyFile) || !file_exists($dataFile)) {
                return [];
            }
            
            // 读取密钥
            $file = fopen($keyFile, "rb");
            $key = fread($file, 32);
            $iv = fread($file, 16);
            fclose($file);
            
            // 解密数据
            $ciphertext = file_get_contents($dataFile);
            $plaintext = openssl_decrypt($ciphertext, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
            
            if ($plaintext === false) {
                return [];
            }
            
            return explode(PHP_EOL, trim($plaintext));
            
        } catch (Exception $e) {
            // 记录错误日志
            error_log("加载文件敏感词失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 加载数据库敏感词
     */
    private static function loadDatabaseWords(): array
    {
        try {
            $sensitiveWord = (new SensitiveWord())->where(['status' => 1])->column('word');
            $systemSensitiveArr = [];
            
            foreach ($sensitiveWord as $sensitiveWordValue) {
                $systemSensitiveArr = array_merge($systemSensitiveArr, explode('；', $sensitiveWordValue));
            }
            
            return $systemSensitiveArr;
            
        } catch (Exception $e) {
            // 记录错误日志
            error_log("加载数据库敏感词失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 生成缓存版本号
     */
    private static function generateVersion(): string
    {
        $version = '';
        
        // 文件版本（修复Docker环境路径）
        $keyFile = "extend/sensitive_key.bin";
        $dataFile = "extend/sensitive_data.bin";
        
        if (file_exists($keyFile) && file_exists($dataFile)) {
            $version .= filemtime($keyFile) . '_' . filemtime($dataFile);
        }
        
        // 数据库版本（获取最后更新时间）- 修复数据库连接问题
        try {
            $isSensitiveSystem = ConfigService::get('chat', 'is_sensitive_system', 1);
            if ($isSensitiveSystem) {
                $lastUpdate = (new SensitiveWord())->where(['status' => 1])->max('update_time');
                $version .= '_' . ($lastUpdate ?: 0);
            }
        } catch (Exception $e) {
            // 忽略数据库错误，记录日志
            Log::warning('数据库敏感词版本获取失败', ['error' => $e->getMessage()]);
            $version .= '_db_error';
        }
        
        return md5($version);
    }
    
    /**
     * 清理缓存
     */
    public static function clearCache(): bool
    {
        try {
            // 清理Redis缓存
            if (self::checkRedisAvailable()) {
                Cache::delete(self::$wordsKey);
                Cache::delete(self::$versionKey);
                Cache::delete(self::$bloomKey);
                Cache::delete(self::$treeKey);
            }
            
            // 清理内存缓存
            self::$memoryCache = [];
            self::$memoryCacheExpire = [];
            self::$currentMemoryCacheSize = 0;
            self::$bloomFilter = null;
            self::$dfaTree = null;
            
            return true;
        } catch (Exception $e) {
            Log::error('清理敏感词缓存失败', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 预热缓存
     */
    public static function warmupCache(): array
    {
        $startTime = microtime(true);
        
        try {
            // 预加载敏感词数据
            $words = self::getSensitiveWords();
            
            // 预构建布隆过滤器
            self::buildBloomFilter();
            
            // 预构建DFA树
            self::buildOptimizedDfaTree();
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            return [
                'success' => true,
                'words_count' => count($words),
                'duration_ms' => round($duration, 2),
                'cache_mode' => self::$cacheMode
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'cache_mode' => self::$cacheMode
            ];
        }
    }
    
    /**
     * 获取缓存状态
     */
    public static function getCacheStatus(): array
    {
        $words = self::getSensitiveWords();
        
        return [
            'cached' => !empty($words),
            'word_count' => count($words),
            'cache_size' => self::formatBytes(strlen(serialize($words))),
            'version' => self::generateVersion(),
            'redis_available' => self::checkRedisAvailable(),
            'cache_mode' => self::$cacheMode,
            'memory_cache_size' => self::$currentMemoryCacheSize,
            'memory_usage' => self::formatBytes(memory_get_usage(true))
        ];
    }
    
    /**
     * 获取性能统计
     */
    public static function getStats(): array
    {
        return array_merge(self::$stats, [
            'memory_cache_size' => self::$currentMemoryCacheSize,
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
        ]);
    }
    
    /**
     * 重置统计
     */
    public static function resetStats(): void
    {
        self::$stats = [
            'cache_hits' => 0,
            'cache_misses' => 0,
            'bloom_hits' => 0,
            'bloom_misses' => 0,
            'build_time' => 0,
            'detect_time' => 0,
            'bloom_false_positive' => 0,
            'redis_available' => self::checkRedisAvailable(),
            'cache_mode' => self::$cacheMode
        ];
    }
    
    /**
     * 格式化字节数
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 批量敏感词检测
     */
    public static function batchSensitive(array $contents): array
    {
        $results = [];
        
        foreach ($contents as $index => $content) {
            try {
                self::sensitive($content);
                $results[$index] = ['status' => 'pass', 'content' => $content];
            } catch (Exception $e) {
                $results[$index] = [
                    'status' => 'blocked',
                    'content' => $content,
                    'reason' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
} 