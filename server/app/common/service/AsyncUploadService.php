<?php
// +----------------------------------------------------------------------
// | AI项目异步文件上传服务
// +----------------------------------------------------------------------

namespace app\common\service;

use app\common\enum\FileEnum;
use app\common\model\file\File;
use app\common\service\storage\Driver as StorageDriver;
use app\queue\BaseQueue;
use Exception;
use think\facade\Cache;
use think\facade\Log;

/**
 * 异步文件上传服务
 * 优化文件处理性能，支持异步上传、批量处理、缓存预热等功能
 */
class AsyncUploadService
{
    // 缓存前缀
    private const CACHE_PREFIX = 'async_upload:';
    private const CACHE_TTL = 3600; // 1小时
    
    // 队列配置
    private const QUEUE_NAME = 'fileUpload';
    private const MAX_CONCURRENT = 5; // 最大并发数
    
    // 文件处理状态
    private const STATUS_PENDING = 'pending';
    private const STATUS_PROCESSING = 'processing';
    private const STATUS_SUCCESS = 'success';
    private const STATUS_FAILED = 'failed';
    
    /**
     * 异步上传文件（快速响应版本）
     */
    public static function asyncUpload($cid, int $sourceId = 0, int $source = FileEnum::SOURCE_ADMIN, string $saveDir = 'uploads/async'): array
    {
        $startTime = microtime(true);
        
        try {
            // 1. 快速获取文件基本信息（不进行实际上传）
            $file = request()->file('file');
            if (empty($file)) {
                throw new Exception('未找到上传文件的信息');
            }
            
            // 2. 基本验证（快速验证）
            $fileInfo = self::getFileBasicInfo($file);
            
            // 3. 生成任务ID和预分配文件记录
            $taskId = self::generateTaskId();
            $tempFile = self::prepareTempFile($file, $saveDir);
            
            // 4. 创建预分配数据库记录
            $fileRecord = File::create([
                'cid'         => $cid,
                'type'        => $fileInfo['type'],
                'name'        => $fileInfo['name'],
                'uri'         => $tempFile['temp_path'],
                'source'      => $source,
                'source_id'   => $sourceId,
                'create_time' => time(),
                'ip'          => request()->ip(),
                'status'      => 0, // 0=处理中
            ]);
            
            // 5. 缓存任务状态
            $taskData = [
                'task_id' => $taskId,
                'file_id' => $fileRecord->id,
                'status' => self::STATUS_PENDING,
                'file_info' => $fileInfo,
                'temp_file' => $tempFile,
                'upload_params' => [
                    'cid' => $cid,
                    'source_id' => $sourceId,
                    'source' => $source,
                    'save_dir' => $saveDir
                ],
                'created_at' => time(),
                'processing_time' => null,
            ];
            
            Cache::set(self::CACHE_PREFIX . $taskId, $taskData, self::CACHE_TTL);
            
            // 6. 推入异步队列
            BaseQueue::pushFileUpload([
                'task_id' => $taskId,
                'temp_file_path' => $tempFile['temp_path'],
                'target_path' => $tempFile['target_path'],
            ]);
            
            $processingTime = (microtime(true) - $startTime) * 1000;
            
            // 7. 立即返回结果（异步处理）
            return [
                'id' => $fileRecord->id,
                'task_id' => $taskId,
                'status' => self::STATUS_PENDING,
                'message' => '文件上传已提交，正在异步处理...',
                'processing_time' => round($processingTime, 2) . 'ms',
                'estimated_time' => self::estimateProcessingTime($fileInfo['size']),
            ];
            
        } catch (Exception $e) {
            Log::error('异步文件上传失败: ' . $e->getMessage());
            throw new Exception($e->getMessage());
        }
    }
    
    /**
     * 批量异步上传（高性能版本）
     */
    public static function batchAsyncUpload(array $files, $cid, int $sourceId = 0, int $source = FileEnum::SOURCE_ADMIN): array
    {
        $startTime = microtime(true);
        $results = [];
        $batchId = self::generateBatchId();
        
        try {
            // 1. 批量预处理文件
            $taskList = [];
            foreach ($files as $index => $file) {
                $taskId = self::generateTaskId($batchId, $index);
                $fileInfo = self::getFileBasicInfo($file);
                $tempFile = self::prepareTempFile($file, 'uploads/batch');
                
                // 创建文件记录
                $fileRecord = File::create([
                    'cid' => $cid,
                    'type' => $fileInfo['type'],
                    'name' => $fileInfo['name'],
                    'uri' => $tempFile['temp_path'],
                    'source' => $source,
                    'source_id' => $sourceId,
                    'create_time' => time(),
                    'ip' => request()->ip(),
                    'status' => 0,
                ]);
                
                $taskData = [
                    'task_id' => $taskId,
                    'batch_id' => $batchId,
                    'file_id' => $fileRecord->id,
                    'status' => self::STATUS_PENDING,
                    'file_info' => $fileInfo,
                    'temp_file' => $tempFile,
                ];
                
                $taskList[] = $taskData;
                Cache::set(self::CACHE_PREFIX . $taskId, $taskData, self::CACHE_TTL);
                
                $results[] = [
                    'id' => $fileRecord->id,
                    'task_id' => $taskId,
                    'status' => self::STATUS_PENDING,
                    'file_name' => $fileInfo['name'],
                ];
            }
            
            // 2. 批量推入队列（使用批量优化）
            BaseQueue::pushBatchFileUpload($taskList);
            
            // 3. 缓存批次信息
            Cache::set(self::CACHE_PREFIX . 'batch:' . $batchId, [
                'batch_id' => $batchId,
                'total_files' => count($files),
                'task_ids' => array_column($taskList, 'task_id'),
                'created_at' => time(),
                'status' => self::STATUS_PENDING,
            ], self::CACHE_TTL);
            
            $processingTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'batch_id' => $batchId,
                'total_files' => count($files),
                'files' => $results,
                'status' => self::STATUS_PENDING,
                'processing_time' => round($processingTime, 2) . 'ms',
                'message' => '批量文件上传已提交，正在异步处理...',
            ];
            
        } catch (Exception $e) {
            Log::error('批量异步文件上传失败: ' . $e->getMessage());
            throw new Exception($e->getMessage());
        }
    }
    
    /**
     * 获取上传任务状态
     */
    public static function getTaskStatus(string $taskId): array
    {
        $taskData = Cache::get(self::CACHE_PREFIX . $taskId);
        
        if (!$taskData) {
            return [
                'status' => 'not_found',
                'message' => '任务不存在或已过期',
            ];
        }
        
        // 检查文件记录状态
        if ($taskData['status'] === self::STATUS_SUCCESS) {
            $fileRecord = File::find($taskData['file_id']);
            if ($fileRecord) {
                return [
                    'task_id' => $taskId,
                    'status' => self::STATUS_SUCCESS,
                    'file' => [
                        'id' => $fileRecord->id,
                        'name' => $fileRecord->name,
                        'uri' => FileService::getFileUrl($fileRecord->uri),
                        'size' => $fileRecord->size ?? 0,
                    ],
                    'processing_time' => $taskData['processing_time'] ?? 0,
                    'message' => '文件上传完成',
                ];
            }
        }
        
        return [
            'task_id' => $taskId,
            'status' => $taskData['status'],
            'message' => self::getStatusMessage($taskData['status']),
            'progress' => self::calculateProgress($taskData),
            'estimated_remaining' => self::estimateRemainingTime($taskData),
        ];
    }
    
    /**
     * 获取批量上传状态
     */
    public static function getBatchStatus(string $batchId): array
    {
        $batchData = Cache::get(self::CACHE_PREFIX . 'batch:' . $batchId);
        
        if (!$batchData) {
            return [
                'status' => 'not_found',
                'message' => '批次不存在或已过期',
            ];
        }
        
        $taskResults = [];
        $completedCount = 0;
        $failedCount = 0;
        
        foreach ($batchData['task_ids'] as $taskId) {
            $taskStatus = self::getTaskStatus($taskId);
            $taskResults[] = $taskStatus;
            
            if ($taskStatus['status'] === self::STATUS_SUCCESS) {
                $completedCount++;
            } elseif ($taskStatus['status'] === self::STATUS_FAILED) {
                $failedCount++;
            }
        }
        
        $progress = ($completedCount / $batchData['total_files']) * 100;
        
        return [
            'batch_id' => $batchId,
            'total_files' => $batchData['total_files'],
            'completed' => $completedCount,
            'failed' => $failedCount,
            'progress' => round($progress, 2),
            'status' => $progress >= 100 ? self::STATUS_SUCCESS : self::STATUS_PROCESSING,
            'tasks' => $taskResults,
        ];
    }
    
    /**
     * 文件处理完成回调（由队列处理器调用）
     */
    public static function markTaskCompleted(string $taskId, array $result): void
    {
        $taskData = Cache::get(self::CACHE_PREFIX . $taskId);
        if (!$taskData) {
            return;
        }
        
        $taskData['status'] = $result['success'] ? self::STATUS_SUCCESS : self::STATUS_FAILED;
        $taskData['processing_time'] = $result['processing_time'] ?? 0;
        $taskData['error'] = $result['error'] ?? null;
        $taskData['completed_at'] = time();
        
        // 更新文件记录
        if ($result['success'] && isset($result['file_info'])) {
            File::where('id', $taskData['file_id'])->update([
                'uri' => $result['file_info']['uri'],
                'status' => 1, // 1=完成
                'size' => $result['file_info']['size'] ?? 0,
            ]);
        }
        
        Cache::set(self::CACHE_PREFIX . $taskId, $taskData, self::CACHE_TTL);
    }
    
    /**
     * 获取文件基本信息
     */
    private static function getFileBasicInfo($file): array
    {
        return [
            'name' => $file->getOriginalName(),
            'type' => self::getFileTypeByExtension($file->extension()),
            'size' => $file->getSize(),
            'ext' => $file->extension(),
            'mime' => $file->getMime(),
        ];
    }
    
    /**
     * 准备临时文件
     */
    private static function prepareTempFile($file, string $saveDir): array
    {
        $tempDir = runtime_path() . 'temp/uploads/';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        
        $tempFileName = uniqid() . '.' . $file->extension();
        $tempPath = $tempDir . $tempFileName;
        
        // 移动到临时目录
        $file->move($tempDir, $tempFileName);
        
        return [
            'temp_path' => $tempPath,
            'target_path' => $saveDir . '/' . date('Ymd') . '/' . $tempFileName,
        ];
    }
    
    /**
     * 生成任务ID
     */
    private static function generateTaskId(string $batchId = null, int $index = null): string
    {
        $prefix = $batchId ? $batchId . '_' : '';
        $suffix = $index !== null ? '_' . $index : '';
        return $prefix . uniqid('task_') . $suffix;
    }
    
    /**
     * 生成批次ID
     */
    private static function generateBatchId(): string
    {
        return 'batch_' . uniqid() . '_' . time();
    }
    
    /**
     * 根据扩展名获取文件类型
     */
    private static function getFileTypeByExtension(string $ext): int
    {
        $imageExts = config('project.file_image', []);
        $videoExts = config('project.file_video', []);
        $audioExts = config('project.file_audio', []);
        
        if (in_array(strtolower($ext), $imageExts)) {
            return FileEnum::IMAGE_TYPE;
        } elseif (in_array(strtolower($ext), $videoExts)) {
            return FileEnum::VIDEO_TYPE;
        } elseif (in_array(strtolower($ext), $audioExts)) {
            return FileEnum::AUDIO_TYPE;
        }
        
        return FileEnum::FILE_TYPE;
    }
    
    /**
     * 估算处理时间
     */
    private static function estimateProcessingTime(int $fileSize): string
    {
        // 基于文件大小估算处理时间
        $seconds = max(1, ceil($fileSize / 1024 / 1024)); // 1MB/秒
        return $seconds . '秒';
    }
    
    /**
     * 获取状态消息
     */
    private static function getStatusMessage(string $status): string
    {
        return match($status) {
            self::STATUS_PENDING => '等待处理',
            self::STATUS_PROCESSING => '正在处理',
            self::STATUS_SUCCESS => '处理完成',
            self::STATUS_FAILED => '处理失败',
            default => '未知状态',
        };
    }
    
    /**
     * 计算进度
     */
    private static function calculateProgress(array $taskData): int
    {
        if ($taskData['status'] === self::STATUS_SUCCESS) {
            return 100;
        } elseif ($taskData['status'] === self::STATUS_PROCESSING) {
            // 基于时间估算进度
            $elapsed = time() - $taskData['created_at'];
            $estimated = self::estimateProcessingTime($taskData['file_info']['size'] ?? 0);
            return min(90, ($elapsed / (int)$estimated) * 100);
        }
        return 0;
    }
    
    /**
     * 估算剩余时间
     */
    private static function estimateRemainingTime(array $taskData): string
    {
        if ($taskData['status'] === self::STATUS_SUCCESS) {
            return '0秒';
        }
        
        $elapsed = time() - $taskData['created_at'];
        $estimated = (int)self::estimateProcessingTime($taskData['file_info']['size'] ?? 0);
        $remaining = max(0, $estimated - $elapsed);
        
        return $remaining . '秒';
    }
    
    /**
     * 清理过期任务
     */
    public static function cleanupExpiredTasks(): int
    {
        $redis = Cache::store('redis')->handler();
        $keys = $redis->keys(self::CACHE_PREFIX . '*');
        $cleaned = 0;
        
        foreach ($keys as $key) {
            $data = Cache::get(str_replace(Cache::store('redis')->getConfig('prefix'), '', $key));
            if ($data && isset($data['created_at']) && 
                (time() - $data['created_at']) > self::CACHE_TTL) {
                Cache::delete(str_replace(Cache::store('redis')->getConfig('prefix'), '', $key));
                $cleaned++;
            }
        }
        
        return $cleaned;
    }
} 