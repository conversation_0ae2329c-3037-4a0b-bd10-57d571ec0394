<?php
/**
 * 简化敏感词检测服务
 * 特点：
 * 1. 只读取文件敏感词库，不使用数据库
 * 2. 高效缓存机制，避免重复解密
 * 3. 不依赖复杂的配置和日志系统
 * 4. 专为Docker环境优化
 */

namespace app\common\service;

use DfaFilter\SensitiveHelper;
use Exception;

class SimpleSensitiveService
{
    // 内存缓存
    private static $sensitiveWords = null;
    private static $dfaTree = null;
    private static $lastLoadTime = 0;
    private static $cacheTimeout = 3600; // 1小时缓存
    
    // 文件路径
    private static $keyFile = '/www/wwwroot/ai/server/extend/sensitive_key.bin';
    private static $dataFile = '/www/wwwroot/ai/server/extend/sensitive_data.bin';
    
    /**
     * 敏感词检测主方法
     * @param string $content 待检测内容
     * @throws Exception 发现敏感词时抛出异常
     */
    public static function sensitive(string $content): void
    {
        // 快速空内容检查
        if (empty(trim($content))) {
            return;
        }
        
        try {
            // 获取敏感词数据
            $sensitiveWords = self::getSensitiveWords();
            
            if (empty($sensitiveWords)) {
                return;
            }
            
            // 获取DFA树
            $dfaTree = self::getDfaTree($sensitiveWords);
            
            if ($dfaTree === null) {
                // DFA树构建失败，使用简单字符串匹配
                self::simpleStringMatch($content, $sensitiveWords);
                return;
            }
            
            // 使用DFA算法检测
            $badWords = $dfaTree->getBadWord($content);
            
            if (!empty($badWords)) {
                $badWords = array_unique($badWords);
                throw new Exception('提问存在敏感词：' . implode(',', $badWords));
            }
            
        } catch (Exception $e) {
            // 重新抛出敏感词异常
            if (strpos($e->getMessage(), '敏感词') !== false) {
                throw $e;
            }
            
            // 其他异常时使用降级检测
            self::fallbackDetection($content);
        }
    }
    
    /**
     * 获取敏感词数据（带缓存）
     */
    private static function getSensitiveWords(): array
    {
        $now = time();
        
        // 检查缓存是否有效
        if (self::$sensitiveWords !== null && 
            ($now - self::$lastLoadTime) < self::$cacheTimeout) {
            return self::$sensitiveWords;
        }
        
        // 重新加载敏感词
        self::$sensitiveWords = self::loadSensitiveWords();
        self::$lastLoadTime = $now;
        self::$dfaTree = null; // 清除DFA树缓存，下次重建
        
        return self::$sensitiveWords;
    }
    
    /**
     * 从文件加载敏感词
     */
    private static function loadSensitiveWords(): array
    {
        try {
            if (!file_exists(self::$keyFile) || !file_exists(self::$dataFile)) {
                return [];
            }
            
            // 读取密钥
            $file = fopen(self::$keyFile, "rb");
            if (!$file) {
                return [];
            }
            
            $key = fread($file, 32);
            $iv = fread($file, 16);
            fclose($file);
            
            // 读取加密数据
            $ciphertext = file_get_contents(self::$dataFile);
            if ($ciphertext === false) {
                return [];
            }
            
            // 解密
            $plaintext = openssl_decrypt($ciphertext, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
            if ($plaintext === false) {
                return [];
            }
            
            // 处理敏感词
            $words = explode(PHP_EOL, trim($plaintext));
            $words = array_filter($words); // 过滤空行
            $words = array_unique($words); // 去重
            
            return $words;
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取DFA树（带缓存）
     */
    private static function getDfaTree(array $sensitiveWords): ?SensitiveHelper
    {
        if (self::$dfaTree !== null) {
            return self::$dfaTree;
        }
        
        try {
            // 分块处理，避免内存问题
            $chunks = array_chunk($sensitiveWords, 10000);
            $allBadWords = [];
            
            foreach ($chunks as $chunk) {
                $tree = SensitiveHelper::init()->setTree($chunk);
                self::$dfaTree = $tree; // 缓存最后一个树（或者可以合并处理）
            }
            
            return self::$dfaTree;
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * 简单字符串匹配（DFA失败时的降级方案）
     */
    private static function simpleStringMatch(string $content, array $sensitiveWords): void
    {
        $foundWords = [];
        
        foreach ($sensitiveWords as $word) {
            if (strpos($content, $word) !== false) {
                $foundWords[] = $word;
            }
        }
        
        if (!empty($foundWords)) {
            $foundWords = array_unique($foundWords);
            throw new Exception('提问存在敏感词：' . implode(',', $foundWords));
        }
    }
    
    /**
     * 降级检测（异常时的最后保障）
     */
    private static function fallbackDetection(string $content): void
    {
        // 直接重新加载敏感词进行检测
        $words = self::loadSensitiveWords();
        
        if (!empty($words)) {
            self::simpleStringMatch($content, $words);
        }
    }
    
    /**
     * 清除缓存
     */
    public static function clearCache(): void
    {
        self::$sensitiveWords = null;
        self::$dfaTree = null;
        self::$lastLoadTime = 0;
    }
    
    /**
     * 获取统计信息
     */
    public static function getStats(): array
    {
        return [
            'words_count' => self::$sensitiveWords ? count(self::$sensitiveWords) : 0,
            'cache_valid' => self::$sensitiveWords !== null,
            'last_load_time' => self::$lastLoadTime ? date('Y-m-d H:i:s', self::$lastLoadTime) : 'never',
            'dfa_tree_ready' => self::$dfaTree !== null
        ];
    }
    
    /**
     * 预热缓存
     */
    public static function warmup(): array
    {
        $words = self::getSensitiveWords();
        $tree = self::getDfaTree($words);
        
        return [
            'words_loaded' => count($words),
            'dfa_tree_ready' => $tree !== null,
            'cache_time' => date('Y-m-d H:i:s', self::$lastLoadTime)
        ];
    }
}
?> 