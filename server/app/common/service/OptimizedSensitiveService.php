<?php
// +----------------------------------------------------------------------
// | 优化敏感词检测服务
// | 参考知识库敏感词校验的成功实现方式
// +----------------------------------------------------------------------

namespace app\common\service;

use DfaFilter\SensitiveHelper;
use Exception;
use think\facade\Log;

/**
 * 优化敏感词检测服务
 * 参考知识库敏感词校验的成功实现，确保AI对话和智能体对话的敏感词检测稳定可靠
 */
class OptimizedSensitiveService
{
    // 静态缓存敏感词数据，避免重复加载
    private static $cachedWords = null;
    private static $dfaTree = null;
    private static $lastLoadTime = 0;
    private static $cacheTimeout = 3600; // 1小时缓存时间
    
    // 统计信息
    private static $stats = [
        'check_count' => 0,
        'cache_hits' => 0,
        'sensitive_detected' => 0,
        'avg_check_time' => 0
    ];
    
    /**
     * 统一敏感词检测接口
     * 参考知识库的实现方式：强制启用，不依赖数据库配置
     * 
     * @param string $content 待检测内容
     * @param string $type 检测类型：chat|knowledge|agent
     * @throws Exception
     */
    public static function checkSensitive(string $content, string $type = 'chat'): void
    {
        $startTime = microtime(true);
        self::$stats['check_count']++;
        
        try {
            // 输入验证
            if (empty(trim($content))) {
                return;
            }
            
            // 内容长度限制（防止恶意超长内容）
            if (mb_strlen($content) > 50000) {
                Log::warning("敏感词检测：内容过长", [
                    'type' => $type,
                    'length' => mb_strlen($content)
                ]);
                throw new Exception('内容过长，请分段提交');
            }
            
            // 获取敏感词数据（强制启用，不依赖数据库配置）
            $sensitiveWords = self::getSensitiveWordsForced();
            
            if (empty($sensitiveWords)) {
                Log::warning("敏感词检测：敏感词库为空", ['type' => $type]);
                return; // 没有敏感词数据时放行，但记录日志
            }
            
            // 智能预筛选（优化性能）
            if (self::shouldSkipCheck($content)) {
                return;
            }
            
            // 构建或获取DFA树（延迟初始化）
            self::ensureDfaTree($sensitiveWords);
            
            // 执行敏感词检测
            $badWordList = self::$dfaTree->getBadWord($content);
            
            if (!empty($badWordList)) {
                self::$stats['sensitive_detected']++;
                
                // 记录敏感词检测日志
                Log::info("敏感词检测：发现敏感词", [
                    'type' => $type,
                    'content_length' => mb_strlen($content),
                    'sensitive_words' => $badWordList,
                    'content_preview' => mb_substr($content, 0, 100) . '...'
                ]);
                
                throw new Exception('提问存在敏感词：' . implode(',', $badWordList));
            }
            
            // 记录检测成功日志（调试模式下）
            if (config('app.app_debug')) {
                Log::debug("敏感词检测：内容通过", [
                    'type' => $type,
                    'content_length' => mb_strlen($content)
                ]);
            }
            
        } finally {
            // 更新统计信息
            $checkTime = (microtime(true) - $startTime) * 1000;
            self::$stats['avg_check_time'] = (self::$stats['avg_check_time'] * (self::$stats['check_count'] - 1) + $checkTime) / self::$stats['check_count'];
        }
    }
    
    /**
     * AI对话敏感词检测
     * @param string $content 对话内容
     * @throws Exception
     */
    public static function checkChatContent(string $content): void
    {
        self::checkSensitive($content, 'chat');
    }
    
    /**
     * 智能体对话敏感词检测
     * @param string $content 对话内容
     * @throws Exception
     */
    public static function checkAgentContent(string $content): void
    {
        self::checkSensitive($content, 'agent');
    }
    
    /**
     * 知识库内容敏感词检测
     * @param string $content 知识库内容
     * @throws Exception
     */
    public static function checkKnowledgeContent(string $content): void
    {
        self::checkSensitive($content, 'knowledge');
    }
    
    /**
     * 批量检测（高效版本）
     * @param array $contents 内容数组 ['key' => 'content']
     * @param string $type 检测类型
     * @return array 检测结果 ['key' => ['is_sensitive' => bool, 'sensitive_words' => array]]
     */
    public static function batchCheck(array $contents, string $type = 'chat'): array
    {
        $results = [];
        
        foreach ($contents as $key => $content) {
            try {
                self::checkSensitive($content, $type);
                $results[$key] = [
                    'is_sensitive' => false,
                    'sensitive_words' => []
                ];
            } catch (Exception $e) {
                // 解析敏感词
                $sensitiveWords = [];
                if (preg_match('/敏感词：(.+)/', $e->getMessage(), $matches)) {
                    $sensitiveWords = array_map('trim', explode(',', $matches[1]));
                }
                
                $results[$key] = [
                    'is_sensitive' => true,
                    'sensitive_words' => $sensitiveWords,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * 强制获取敏感词数据（不依赖数据库配置）
     * 参考知识库成功实现：直接启用敏感词文件检测
     * 
     * @return array
     */
    private static function getSensitiveWordsForced(): array
    {
        // 检查缓存有效性
        $currentTime = time();
        if (self::$cachedWords !== null && ($currentTime - self::$lastLoadTime) < self::$cacheTimeout) {
            self::$stats['cache_hits']++;
            return self::$cachedWords;
        }
        
        $allWords = [];
        
        try {
            // 强制加载文件敏感词（不依赖数据库配置）
            $fileWords = self::loadFileWords();
            $allWords = array_merge($allWords, $fileWords);
            
            // 可选：加载数据库敏感词（但不依赖配置）
            $dbWords = self::loadDatabaseWords();
            $allWords = array_merge($allWords, $dbWords);
            
        } catch (Exception $e) {
            Log::error("敏感词加载失败", ['error' => $e->getMessage()]);
            // 加载失败时使用基础敏感词
            $allWords = self::getBasicSensitiveWords();
        }
        
        // 去重并过滤空值
        $allWords = array_unique(array_filter($allWords, function($word) {
            return !empty(trim($word));
        }));
        
        // 更新缓存
        self::$cachedWords = $allWords;
        self::$lastLoadTime = $currentTime;
        
        Log::info("敏感词加载完成", [
            'total_words' => count($allWords),
            'source' => 'file_and_db'
        ]);
        
        return $allWords;
    }
    
    /**
     * 加载文件敏感词
     */
    private static function loadFileWords(): array
    {
        $keyFile = "extend/sensitive_key.bin";
        $dataFile = "extend/sensitive_data.bin";
        
        if (!file_exists($keyFile) || !file_exists($dataFile)) {
            throw new Exception("敏感词文件不存在");
        }
        
        // 读取解密密钥
        $keyContent = file_get_contents($keyFile);
        if (strlen($keyContent) < 48) { // 32字节key + 16字节iv
            throw new Exception("敏感词密钥文件格式错误");
        }
        
        $key = substr($keyContent, 0, 32);
        $iv = substr($keyContent, 32, 16);
        
        // 读取加密数据
        $encryptedData = file_get_contents($dataFile);
        if (!$encryptedData) {
            throw new Exception("敏感词数据文件读取失败");
        }
        
        // 解密
        $decryptedData = openssl_decrypt($encryptedData, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
        if ($decryptedData === false) {
            throw new Exception("敏感词数据解密失败");
        }
        
        // 解析敏感词
        $words = array_filter(explode(PHP_EOL, trim($decryptedData)));
        
        Log::debug("文件敏感词加载完成", ['count' => count($words)]);
        
        return $words;
    }
    
    /**
     * 加载数据库敏感词（尽力而为，不强制依赖）
     */
    private static function loadDatabaseWords(): array
    {
        try {
            // 尝试加载数据库敏感词，但不强制依赖
            $sensitiveWordModel = new \app\common\model\SensitiveWord();
            $dbWords = $sensitiveWordModel->where(['status' => 1])->column('word');
            
            $words = [];
            foreach ($dbWords as $wordItem) {
                // 处理分号分隔的敏感词
                $itemWords = explode('；', $wordItem);
                $words = array_merge($words, $itemWords);
            }
            
            Log::debug("数据库敏感词加载完成", ['count' => count($words)]);
            
            return $words;
            
        } catch (Exception $e) {
            Log::warning("数据库敏感词加载失败", ['error' => $e->getMessage()]);
            return []; // 失败时返回空数组，不影响文件敏感词
        }
    }
    
    /**
     * 获取基础敏感词（兜底方案）
     */
    private static function getBasicSensitiveWords(): array
    {
        return [
            '色情', '赌博', '毒品', '暴力', '恐怖', '反动',
            '法轮功', '邪教', '自杀', '爆炸', '枪支', '军火'
        ];
    }
    
    /**
     * 智能预筛选（性能优化）
     */
    private static function shouldSkipCheck(string $content): bool
    {
        // 纯数字内容
        if (preg_match('/^\d+$/', $content)) {
            return true;
        }
        
        // 短英文内容（不包含明显敏感词）
        if (preg_match('/^[a-zA-Z0-9\s\.,;:!?@#$%^&*()_+\-=\[\]{}|\\<>\/~`"\']+$/', $content) 
            && mb_strlen($content) < 50
            && !preg_match('/\b(sex|drug|kill|bomb|terror|violence|porn|fuck|shit|damn)\b/i', $content)) {
            return true;
        }
        
        // 技术代码片段
        if (preg_match('/^(SELECT|INSERT|UPDATE|DELETE|function|class|var|const|import|export)\s/i', $content)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 确保DFA树已构建
     */
    private static function ensureDfaTree(array $sensitiveWords): void
    {
        if (self::$dfaTree === null || empty($sensitiveWords)) {
            self::$dfaTree = SensitiveHelper::init()->setTree($sensitiveWords);
        }
    }
    
    /**
     * 获取统计信息
     */
    public static function getStats(): array
    {
        return array_merge(self::$stats, [
            'cached_words_count' => self::$cachedWords ? count(self::$cachedWords) : 0,
            'last_load_time' => self::$lastLoadTime ? date('Y-m-d H:i:s', self::$lastLoadTime) : 'never'
        ]);
    }
    
    /**
     * 清理缓存
     */
    public static function clearCache(): void
    {
        self::$cachedWords = null;
        self::$dfaTree = null;
        self::$lastLoadTime = 0;
        
        Log::info("敏感词缓存已清理");
    }
    
    /**
     * 重置统计信息
     */
    public static function resetStats(): void
    {
        self::$stats = [
            'check_count' => 0,
            'cache_hits' => 0,
            'sensitive_detected' => 0,
            'avg_check_time' => 0
        ];
    }
} 