<?php
// +----------------------------------------------------------------------
// | 知识库敏感词校验服务 - 高性能优化版本
// +----------------------------------------------------------------------

namespace app\common\service;

use app\common\service\CachedWordsService;
use Exception;

/**
 * 知识库敏感词校验服务
 * 专门处理知识库录入时的敏感词检测
 * 优化版本：移除不必要的分类逻辑，提升性能
 */
class KbSensitiveService
{
    /**
     * 检测单条问答对是否包含敏感词
     * @param string $question 问题内容
     * @param string $answer 答案内容
     * @return array 检测结果
     */
    public static function checkSingle(string $question, string $answer = ''): array
    {
        $result = [
            'is_sensitive' => false,
            'all_passed' => true,
            'sensitive_words' => [],
            'message' => '内容检测通过'
        ];

        try {
            // 合并内容一次性检测，提升性能
            $content = trim($question . ' ' . $answer);
            
            if (empty($content)) {
                return $result;
            }
            
            // 智能预筛选，跳过明显安全的内容
            if (!self::needCheck($content)) {
                return $result;
            }
            
            // 执行敏感词检测（统一使用PermanentSensitiveService）
            PermanentSensitiveService::sensitive($content);
            
        } catch (Exception $e) {
            // 安全的错误处理
            if (strpos($e->getMessage(), '敏感词：') !== false) {
                // 这是正常的敏感词检测结果
                $result['is_sensitive'] = true;
                $result['all_passed'] = false;
                $result['message'] = $e->getMessage();
                
                // 解析敏感词
                if (preg_match('/敏感词：(.+)/', $e->getMessage(), $matches)) {
                    $result['sensitive_words'] = array_map('trim', explode(',', $matches[1]));
                }
            } else {
                // 系统异常时的安全降级处理
                $fallbackResult = self::fallbackBasicCheck($content);
                if ($fallbackResult['is_sensitive']) {
                    // 降级检测发现敏感内容
                    $result = $fallbackResult;
                    $result['service_error'] = true;
                    $result['error_details'] = '敏感词检测服务异常，已启用基础检测';
                } else {
                    // 降级检测也通过，但记录异常
                    $result['is_sensitive'] = false;
                    $result['all_passed'] = true;
                    $result['message'] = '内容检测通过（基础模式）';
                    $result['service_error'] = true;
                    $result['error_details'] = '敏感词检测服务异常，已启用基础检测';
                }
                
                // 记录安全事件
                self::logSecurityEvent('service_error', [
                    'content_length' => mb_strlen($content),
                    'error' => $e->getMessage(),
                    'fallback_result' => $fallbackResult['is_sensitive'] ? 'blocked' : 'passed'
                ]);
            }
        }
        
        return $result;
    }

    /**
     * 批量检测问答对（优化版本）
     * @param array $qaList 问答对列表 [['q' => '问题', 'a' => '答案'], ...]
     * @return array 检测结果
     */
    public static function checkBatch(array $qaList): array
    {
        $result = [
            'success' => true,
            'total' => count($qaList),
            'passed' => 0,
            'failed' => 0,
            'error_count' => 0,
            'sensitive_items' => [],
            'all_passed' => true,
            'message' => '',
            'processing_time' => 0
        ];

        if (empty($qaList)) {
            $result['message'] = '没有需要检测的内容';
            return $result;
        }

        $startTime = microtime(true);
        $allSensitiveWords = [];

        try {
            foreach ($qaList as $index => $qa) {
                $question = $qa['q'] ?? '';
                $answer = $qa['a'] ?? '';
                
                $checkResult = self::checkSingle($question, $answer);
                
                if ($checkResult['is_sensitive']) {
                    $result['failed']++;
                    $result['error_count']++;
                    $result['all_passed'] = false;
                    
                    // 收集敏感词
                    if (!empty($checkResult['sensitive_words'])) {
                        $allSensitiveWords = array_merge($allSensitiveWords, $checkResult['sensitive_words']);
                    }
                    
                    $result['sensitive_items'][] = [
                        'index' => $index,
                        'question' => mb_substr($question, 0, 50) . (mb_strlen($question) > 50 ? '...' : ''),
                        'answer' => mb_substr($answer, 0, 50) . (mb_strlen($answer) > 50 ? '...' : ''),
                        'sensitive_words' => $checkResult['sensitive_words'],
                        'message' => $checkResult['message']
                    ];
                    
                    // 防止过度处理
                    if ($result['failed'] >= 100) {
                        $result['message'] = "检测到大量敏感内容，已停止检测。请清理数据后重试。";
                        break;
                    }
                } else {
                    $result['passed']++;
                }
            }

            // 生成结果消息
            if ($result['all_passed']) {
                $result['message'] = "批量检测完成，所有 {$result['total']} 条内容均通过审核";
            } else {
                $allSensitiveWords = array_unique($allSensitiveWords);
                $sensitiveWordsText = implode('、', array_slice($allSensitiveWords, 0, 10));
                if (count($allSensitiveWords) > 10) {
                    $sensitiveWordsText .= '等';
                }
                $result['message'] = "检测完成，{$result['passed']}/{$result['total']} 条通过，发现敏感词：{$sensitiveWordsText}";
            }

        } catch (Exception $e) {
            $result['success'] = false;
            $result['message'] = "批量检测异常：" . $e->getMessage();
            $result['all_passed'] = false;
        }

        $result['processing_time'] = round((microtime(true) - $startTime) * 1000, 2);
        return $result;
    }

    /**
     * 智能预筛选（安全增强版本）
     * 快速过滤明显安全的内容，提高批量检测性能
     * @param string $content 内容
     * @return bool true-需要检测, false-可以跳过
     */
    private static function needCheck(string $content): bool
    {
        // 输入验证
        self::validateInput($content);
        
        if (empty($content) || mb_strlen($content) < 2) {
            return false;
        }

        // 纯数字内容跳过
        if (preg_match('/^\d+$/', $content)) {
            return false;
        }

        // 更严格的英文内容预筛选
        if (preg_match('/^[a-zA-Z0-9\s\.,;:!?@#$%^&*()_+\-=\[\]{}|\\<>\/~`"\']+$/', $content) && 
            mb_strlen($content) < 20 && // 缩短长度限制
            !preg_match('/\b(sex|drug|kill|bomb|terror|violence|porn|fuck|shit|damn)\b/i', $content)) { // 排除明显敏感词
            return false;
        }

        // 技术术语检查更严格
        if (preg_match('/^(SELECT|INSERT|UPDATE|DELETE|function|class|var|const)\s/i', $content)) {
            return false;
        }

        return true;
    }

    /**
     * 大批量检测（高性能版本）
     * @param array $qaDataList 问答数据列表
     * @param array $options 配置选项
     * @return array 检测结果
     */
    public static function checkLargeBatch(array $qaDataList, array $options = []): array
    {
        $config = array_merge([
            'batch_size' => 50,
            'max_time' => 30,
            'memory_limit' => 50,
            'progress_callback' => null,
            'stop_on_error' => false
        ], $options);

        $result = [
            'success' => true,
            'total' => count($qaDataList),
            'processed' => 0,
            'passed' => 0,
            'failed' => 0,
            'error_count' => 0,
            'results' => [],
            'sensitive_items' => [],
            'processing_time' => 0,
            'memory_peak' => 0,
            'batches_processed' => 0,
            'message' => ''
        ];

        if (empty($qaDataList)) {
            $result['message'] = '没有需要检测的内容';
            return $result;
        }

        $startTime = microtime(true);
        $totalBatches = ceil(count($qaDataList) / $config['batch_size']);
        $allSensitiveWords = [];

        try {
            for ($i = 0; $i < $totalBatches; $i++) {
                $batchStart = $i * $config['batch_size'];
                $batch = array_slice($qaDataList, $batchStart, $config['batch_size']);
                
                // 处理当前批次
                $batchResult = self::processBatch($batch, $i + 1);
                
                // 合并结果
                $result['processed'] += count($batch);
                $result['passed'] += $batchResult['passed'];
                $result['failed'] += $batchResult['failed'];
                $result['error_count'] += $batchResult['error_count'];
                $result['results'] = array_merge($result['results'], $batchResult['results']);
                $result['sensitive_items'] = array_merge($result['sensitive_items'], $batchResult['sensitive_items']);
                
                if (!empty($batchResult['sensitive_words'])) {
                    $allSensitiveWords = array_merge($allSensitiveWords, $batchResult['sensitive_words']);
                }
                
                $result['batches_processed']++;
                
                // 进度回调
                if ($config['progress_callback']) {
                    $config['progress_callback']([
                        'batch' => $i + 1,
                        'total_batches' => $totalBatches,
                        'processed' => $result['processed'],
                        'total' => $result['total'],
                        'percentage' => round(($result['processed'] / $result['total']) * 100, 2),
                        'memory_usage' => memory_get_usage(true),
                        'time_elapsed' => microtime(true) - $startTime
                    ]);
                }
                
                // 检查时间限制
                if ((microtime(true) - $startTime) > $config['max_time']) {
                    $result['message'] = "达到时间限制（{$config['max_time']}秒），已处理 {$result['processed']}/{$result['total']} 条记录";
                    break;
                }
                
                // 检查内存限制
                $memoryUsage = memory_get_usage(true) / 1024 / 1024;
                if ($memoryUsage > $config['memory_limit']) {
                    $result['message'] = "达到内存限制（{$config['memory_limit']}MB），已处理 {$result['processed']}/{$result['total']} 条记录";
                    break;
                }
                
                // 错误处理策略
                if ($config['stop_on_error'] && $result['failed'] > 0) {
                    $result['message'] = "检测到敏感内容，已停止处理";
                    break;
                }
                
                // 定期垃圾回收
                if ($i % 10 === 9) {
                    gc_collect_cycles();
                }
            }

            // 生成最终消息
            if (empty($result['message'])) {
                if ($result['failed'] === 0) {
                    $result['message'] = "大批量检测完成，所有 {$result['processed']} 条内容均通过审核";
                } else {
                    $allSensitiveWords = array_unique($allSensitiveWords);
                    $sensitiveWordsText = implode('、', array_slice($allSensitiveWords, 0, 10));
                    if (count($allSensitiveWords) > 10) {
                        $sensitiveWordsText .= '等';
                    }
                    $result['message'] = "大批量检测完成，{$result['passed']}/{$result['processed']} 条通过，发现敏感词：{$sensitiveWordsText}";
                }
            }

        } catch (Exception $e) {
            $result['success'] = false;
            $result['message'] = "大批量检测异常：" . $e->getMessage();
        }

        $result['processing_time'] = round((microtime(true) - $startTime) * 1000, 2);
        $result['memory_peak'] = round(memory_get_peak_usage(true) / 1024 / 1024, 2);
        $result['all_passed'] = ($result['failed'] === 0);

        return $result;
    }

    /**
     * 处理单个批次（优化版本）
     * @param array $batch 批次数据
     * @param int $batchNumber 批次号
     * @return array 批次处理结果
     */
    private static function processBatch(array $batch, int $batchNumber): array
    {
        $result = [
            'batch_number' => $batchNumber,
            'passed' => 0,
            'failed' => 0,
            'error_count' => 0,
            'results' => [],
            'sensitive_items' => [],
            'sensitive_words' => []
        ];

        foreach ($batch as $index => $item) {
            $question = $item['q'] ?? '';
            $answer = $item['a'] ?? '';
            
            $checkResult = self::checkSingle($question, $answer);
            
            $itemResult = [
                'index' => $index,
                'q_passed' => !$checkResult['is_sensitive'],
                'a_passed' => !$checkResult['is_sensitive'],
                'all_passed' => !$checkResult['is_sensitive'],
                'sensitive_words' => $checkResult['sensitive_words']
            ];
            
            $result['results'][] = $itemResult;
            
            if ($checkResult['is_sensitive']) {
                $result['failed']++;
                $result['error_count']++;
                
                if (!empty($checkResult['sensitive_words'])) {
                    $result['sensitive_words'] = array_merge($result['sensitive_words'], $checkResult['sensitive_words']);
                }
                
                $result['sensitive_items'][] = [
                    'index' => $index,
                    'question' => mb_substr($question, 0, 50) . (mb_strlen($question) > 50 ? '...' : ''),
                    'answer' => mb_substr($answer, 0, 50) . (mb_strlen($answer) > 50 ? '...' : ''),
                    'sensitive_words' => $checkResult['sensitive_words'],
                    'message' => $checkResult['message']
                ];
            } else {
                $result['passed']++;
            }
        }

        return $result;
    }

    /**
     * 获取推荐配置（优化版本）
     * @param int $totalItems 总条目数
     * @return array 推荐配置
     */
    public static function getRecommendedConfig(int $totalItems): array
    {
        if ($totalItems <= 50) {
            return [
                'batch_size' => 25,
                'max_time' => 10,
                'memory_limit' => 20,
                'mode' => 'fast'
            ];
        } elseif ($totalItems <= 200) {
            return [
                'batch_size' => 20,
                'max_time' => 20,
                'memory_limit' => 30,
                'mode' => 'balanced'
            ];
        } elseif ($totalItems <= 500) {
            return [
                'batch_size' => 15,
                'max_time' => 30,
                'memory_limit' => 40,
                'mode' => 'stable'
            ];
        } else {
            return [
                'batch_size' => 10,
                'max_time' => 60,
                'memory_limit' => 50,
                'mode' => 'conservative'
            ];
        }
    }

    /**
     * 预检测功能（优化版本）
     * @param array $qaDataList 问答数据列表
     * @param int $sampleSize 采样大小
     * @return array 预检测结果
     */
    public static function preCheck(array $qaDataList, int $sampleSize = 10): array
    {
        if (empty($qaDataList)) {
            return [
                'estimated_time' => 0,
                'estimated_memory' => 0,
                'recommended_config' => self::getRecommendedConfig(0),
                'warnings' => ['没有需要检测的内容'],
                'sample_results' => []
            ];
        }

        $totalCount = count($qaDataList);
        $actualSampleSize = min($sampleSize, $totalCount);
        
        // 随机采样
        $sampleIndices = array_rand($qaDataList, $actualSampleSize);
        if (!is_array($sampleIndices)) {
            $sampleIndices = [$sampleIndices];
        }
        
        $sampleData = [];
        foreach ($sampleIndices as $index) {
            $sampleData[] = $qaDataList[$index];
        }
        
        // 执行采样检测
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        $sampleResult = self::checkBatch($sampleData);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        // 计算预估值
        $sampleTime = ($endTime - $startTime) * 1000; // 转换为毫秒
        $sampleMemory = ($endMemory - $startMemory) / 1024 / 1024; // 转换为MB
        
        $estimatedTime = ($sampleTime / $actualSampleSize) * $totalCount / 1000; // 转换为秒
        $estimatedMemory = max(($sampleMemory / $actualSampleSize) * $totalCount, 10); // 最小10MB
        
        return [
            'total_count' => $totalCount,
            'sample_size' => $actualSampleSize,
            'sample_time' => round($sampleTime, 2),
            'sample_memory' => round($sampleMemory, 2),
            'estimated_time' => round($estimatedTime, 2),
            'estimated_memory' => round($estimatedMemory, 2),
            'recommended_config' => self::getRecommendedConfig($totalCount),
            'warnings' => self::generateWarnings($totalCount, $estimatedTime, $estimatedMemory),
            'sample_results' => $sampleResult
        ];
    }

    /**
     * 生成警告信息（优化版本）
     * @param int $totalCount 总数量
     * @param float $estimatedTime 预估时间
     * @param float $estimatedMemory 预估内存
     * @return array 警告列表
     */
    private static function generateWarnings(int $totalCount, float $estimatedTime, float $estimatedMemory): array
    {
        $warnings = [];
        
        if ($totalCount > 1000) {
            $warnings[] = "数据量较大（{$totalCount}条），建议使用队列异步处理";
        }
        
        if ($estimatedTime > 30) {
            $warnings[] = "预估处理时间较长（{$estimatedTime}秒），建议分批处理";
        }
        
        if ($estimatedMemory > 100) {
            $warnings[] = "预估内存使用较高（{$estimatedMemory}MB），请确保服务器内存充足";
        }
        
        if ($totalCount > 500 && $estimatedTime > 60) {
            $warnings[] = "建议使用大批量优化检测方法（checkLargeBatch）";
        }
        
        return $warnings;
    }

    /**
     * 获取服务统计信息（优化版本）
     * @return array 统计信息
     */
    public static function getStats(): array
    {
        try {
            // 获取缓存服务统计
            $cacheStats = CachedWordsService::getStats();
            
            return [
                'service_status' => 'optimized',
                'version' => '2.0',
                'features' => [
                    'intelligent_filtering' => true,
                    'batch_processing' => true,
                    'memory_optimization' => true,
                    'performance_monitoring' => true
                ],
                'cache_stats' => $cacheStats,
                'last_check' => date('Y-m-d H:i:s')
            ];
        } catch (Exception $e) {
            return [
                'service_status' => 'error',
                'version' => '2.0',
                'error_message' => $e->getMessage(),
                'last_check' => date('Y-m-d H:i:s')
            ];
        }
    }

    /**
     * 输入验证和安全检查
     * @param string $content 内容
     * @throws Exception
     */
    private static function validateInput(string $content): void
    {
        // 长度限制
        if (mb_strlen($content) > 50000) {
            throw new Exception('内容长度超出限制（最大50000字符）');
        }

        // 字符编码验证
        if (!mb_check_encoding($content, 'UTF-8')) {
            throw new Exception('内容编码格式不正确');
        }

        // 恶意内容检测
        if (preg_match('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', $content)) {
            throw new Exception('内容包含非法字符');
        }

        // 频率限制检查
        if (!self::checkRateLimit()) {
            throw new Exception('请求过于频繁，请稍后重试');
        }
    }

    /**
     * 基础关键词检测（降级方案）
     * @param string $content 内容
     * @return array 检测结果
     */
    private static function fallbackBasicCheck(string $content): array
    {
        $result = [
            'is_sensitive' => false,
            'all_passed' => true,
            'sensitive_words' => [],
            'message' => '内容检测通过（基础模式）'
        ];

        // 核心敏感词列表（从实际敏感词库中选择的高频核心词汇）
        $criticalWords = [
            // 政治敏感（从实际词库选择）
            '八九六四', '1989', '六四事件', '法轮', '一九八九',
            // 色情违法（从实际词库选择）
            '阿宾', '爱液横流', '操你妈', '大鸡巴', '干你娘',
            // 暴力恐怖（从实际词库选择）
            '杀死你', '炸死', '血腥暴力', '恐怖分子', '爆炸物',
            // 分裂活动（从实际词库选择）
            '台独', '藏独', '疆独', '分裂祖国'
        ];

        foreach ($criticalWords as $word) {
            if (mb_strpos($content, $word) !== false) {
                $result['is_sensitive'] = true;
                $result['all_passed'] = false;
                $result['sensitive_words'][] = $word;
                $result['message'] = "内容包含敏感词：{$word}（基础检测）";
                
                // 记录降级检测事件
                self::logSecurityEvent('fallback_detection', [
                    'word' => $word,
                    'content_length' => mb_strlen($content)
                ]);
                break;
            }
        }

        return $result;
    }

    /**
     * 频率限制检查
     * @return bool
     */
    private static function checkRateLimit(): bool
    {
        try {
            $rateLimitKey = 'rate_limit:kb_sensitive:' . md5($_SERVER['REMOTE_ADDR'] ?? 'unknown');
            $currentCount = \think\facade\Cache::get($rateLimitKey, 0);
            $limit = 100; // 每分钟100次

            if ($currentCount >= $limit) {
                self::logSecurityEvent('rate_limit_exceeded', [
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'current_count' => $currentCount,
                    'limit' => $limit
                ]);
                return false;
            }

            \think\facade\Cache::set($rateLimitKey, $currentCount + 1, 60); // 1分钟窗口
            return true;
        } catch (Exception $e) {
            // 缓存异常时允许通过，但记录日志
            self::logSecurityEvent('rate_limit_error', ['error' => $e->getMessage()]);
            return true;
        }
    }

    /**
     * 记录安全审计日志
     * @param string $event 事件类型
     * @param array $data 事件数据
     */
    private static function logSecurityEvent(string $event, array $data): void
    {
        try {
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'event' => $event,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'data' => $data
            ];

            // 写入安全日志文件
            $logFile = 'runtime/log/security_' . date('Y-m-d') . '.log';
            $logDir = dirname($logFile);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);

            // 高危事件立即记录到系统日志
            if (in_array($event, ['sensitive_content_detected', 'service_bypass_attempt', 'rate_limit_exceeded', 'fallback_detection'])) {
                \think\facade\Log::warning('知识库敏感词安全事件', $logData);
            }
        } catch (Exception $e) {
            // 日志记录失败不应该影响主流程
            error_log("安全日志记录失败: " . $e->getMessage());
        }
    }
} 