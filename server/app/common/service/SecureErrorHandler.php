<?php

namespace app\common\service;

use think\facade\Log;
use think\facade\Config;

/**
 * 🔒 安全修复：安全错误处理服务
 * @notes 防止敏感信息泄露，提供生产环境友好的错误处理
 * @security_fix 2025/08/02 - 信息泄露防护
 */
class SecureErrorHandler
{
    /**
     * 生产环境标识
     */
    private static bool $isProduction;
    
    /**
     * 敏感信息模式列表
     */
    private static array $sensitivePatterns = [
        '/password/i',
        '/secret/i',
        '/key/i',
        '/token/i',
        '/auth/i',
        '/credential/i',
        '/database/i',
        '/mysql/i',
        '/redis/i',
        '/config/i',
        '/\.env/i',
        '/root/i',
        '/admin/i',
        '/\/var\/www/i',
        '/\/home\//i',
        '/\/etc\//i',
        '/\/tmp\//i',
        '/127\.0\.0\.1/i',
        '/localhost/i',
        '/192\.168\./i',
        '/10\.\d+\./i',
    ];
    
    /**
     * 初始化错误处理器
     */
    public static function init(): void
    {
        self::$isProduction = Config::get('app.app_debug', true) === false;
    }
    
    /**
     * 🔒 安全修复：处理异常并返回安全的错误信息
     * @param \Throwable $exception 异常对象
     * @param string $context 上下文信息
     * @return array 安全的错误响应
     */
    public static function handleException(\Throwable $exception, string $context = ''): array
    {
        // 记录详细错误到安全日志
        self::logSecureError($exception, $context);
        
        // 返回用户友好的错误信息
        return self::getUserFriendlyError($exception);
    }
    
    /**
     * 🔒 安全修复：记录安全错误日志
     * @param \Throwable $exception 异常对象
     * @param string $context 上下文信息
     */
    private static function logSecureError(\Throwable $exception, string $context): void
    {
        $errorData = [
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'code' => $exception->getCode(),
            'trace' => $exception->getTraceAsString(),
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => request()->ip() ?? 'unknown',
            'user_agent' => request()->header('User-Agent') ?? 'unknown',
            'url' => request()->url() ?? 'unknown',
        ];
        
        // 脱敏处理
        $errorData = self::sanitizeErrorData($errorData);
        
        // 记录到安全日志通道
        Log::channel('security')->error('系统异常', $errorData);
    }
    
    /**
     * 🔒 安全修复：获取用户友好的错误信息
     * @param \Throwable $exception 异常对象
     * @return array 用户友好的错误响应
     */
    private static function getUserFriendlyError(\Throwable $exception): array
    {
        $errorCode = $exception->getCode();
        $errorMessage = $exception->getMessage();
        
        // 生产环境返回通用错误信息
        if (self::$isProduction) {
            return [
                'success' => false,
                'message' => self::getGenericErrorMessage($errorCode),
                'code' => $errorCode ?: 500,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
        
        // 开发环境返回脱敏后的详细信息
        return [
            'success' => false,
            'message' => self::sanitizeMessage($errorMessage),
            'code' => $errorCode ?: 500,
            'file' => basename($exception->getFile()),
            'line' => $exception->getLine(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 🔒 安全修复：获取通用错误信息
     * @param int $errorCode 错误代码
     * @return string 通用错误信息
     */
    private static function getGenericErrorMessage(int $errorCode): string
    {
        $errorMessages = [
            400 => '请求参数错误',
            401 => '身份验证失败',
            403 => '权限不足',
            404 => '请求的资源不存在',
            405 => '请求方法不被允许',
            422 => '请求数据验证失败',
            429 => '请求过于频繁，请稍后重试',
            500 => '服务器内部错误，请稍后重试',
            502 => '服务暂时不可用',
            503 => '服务维护中，请稍后重试',
            504 => '请求超时，请稍后重试',
        ];
        
        return $errorMessages[$errorCode] ?? '系统暂时不可用，请稍后重试';
    }
    
    /**
     * 🔒 安全修复：脱敏错误数据
     * @param array $data 错误数据
     * @return array 脱敏后的数据
     */
    private static function sanitizeErrorData(array $data): array
    {
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $data[$key] = self::sanitizeMessage($value);
            } elseif (is_array($value)) {
                $data[$key] = self::sanitizeErrorData($value);
            }
        }
        
        return $data;
    }
    
    /**
     * 🔒 安全修复：脱敏错误消息
     * @param string $message 原始错误消息
     * @return string 脱敏后的消息
     */
    private static function sanitizeMessage(string $message): string
    {
        // 移除敏感信息
        foreach (self::$sensitivePatterns as $pattern) {
            $message = preg_replace($pattern, '[REDACTED]', $message);
        }
        
        // 移除绝对路径
        $message = preg_replace('/\/[a-zA-Z0-9_\-\/]+\//', '/[PATH]/', $message);
        
        // 移除IP地址
        $message = preg_replace('/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/', '[IP]', $message);
        
        // 移除端口号
        $message = preg_replace('/:\d{2,5}\b/', ':[PORT]', $message);
        
        return $message;
    }
    
    /**
     * 🔒 安全修复：处理数据库错误
     * @param \Throwable $exception 数据库异常
     * @return array 安全的错误响应
     */
    public static function handleDatabaseError(\Throwable $exception): array
    {
        // 记录数据库错误
        Log::channel('security')->error('数据库错误', [
            'message' => self::sanitizeMessage($exception->getMessage()),
            'code' => $exception->getCode(),
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => request()->ip() ?? 'unknown',
        ]);
        
        // 返回通用数据库错误信息
        return [
            'success' => false,
            'message' => '数据操作失败，请稍后重试',
            'code' => 500,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 🔒 安全修复：处理文件操作错误
     * @param \Throwable $exception 文件操作异常
     * @return array 安全的错误响应
     */
    public static function handleFileError(\Throwable $exception): array
    {
        // 记录文件操作错误
        Log::channel('security')->error('文件操作错误', [
            'message' => self::sanitizeMessage($exception->getMessage()),
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => request()->ip() ?? 'unknown',
        ]);
        
        // 返回通用文件错误信息
        return [
            'success' => false,
            'message' => '文件操作失败，请检查文件格式或稍后重试',
            'code' => 500,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 🔒 安全修复：处理网络请求错误
     * @param \Throwable $exception 网络请求异常
     * @return array 安全的错误响应
     */
    public static function handleNetworkError(\Throwable $exception): array
    {
        // 记录网络请求错误
        Log::channel('security')->error('网络请求错误', [
            'message' => self::sanitizeMessage($exception->getMessage()),
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => request()->ip() ?? 'unknown',
        ]);
        
        // 返回通用网络错误信息
        return [
            'success' => false,
            'message' => '网络连接失败，请检查网络连接或稍后重试',
            'code' => 502,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// 初始化错误处理器
SecureErrorHandler::init();
