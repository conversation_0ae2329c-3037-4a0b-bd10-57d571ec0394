<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\service;

use aip\AipContentCensor;
use app\common\enum\FileEnum;
use app\common\enum\user\UserEnum;
use app\common\model\file\File;
use app\common\service\storage\Driver as StorageDriver;
use Exception;
use think\facade\Log;

class UploadService
{
    /**
     * @notes 上传图片
     * @param $cid
     * @param int $sourceId
     * @param int $source
     * @param string $saveDir
     * @return array
     * @throws Exception
     * <AUTHOR>
     * @date 2021/12/29 16:30
     */
   public static function image($cid, int $sourceId = 0, int $source = FileEnum::SOURCE_ADMIN, string $saveDir = 'uploads/images'): array
   {
        try {
            $config = [
                'default' => ConfigService::get('storage', 'default', 'local'),
                'engine'  => ConfigService::get('storage') ?? ['local'=>[]],
            ];

            // 2、执行文件上传
            $StorageDriver = new StorageDriver($config);
            $StorageDriver->setUploadFile('file');
            $fileName = $StorageDriver->getFileName();
            $fileInfo = $StorageDriver->getFileInfo();

            // 🔒 安全修复：增强文件上传安全验证
            if (!self::validateImageFile($fileInfo, $StorageDriver->getUploadFile())) {
                throw new Exception("文件安全验证失败，不允许上传该文件");
            }

            // 上传文件
            $saveDir = $saveDir . '/' .  trim(date('Ymd'));
            if (!$StorageDriver->upload($saveDir)) {
                throw new Exception($StorageDriver->getError());
            }

            // 文件审核
            if ($source == FileEnum::SOURCE_USER) {
                $censorStatus = ConfigService::get('content_censor', 'upload_image_open', 0);
                if ($censorStatus) {
                    $url = $saveDir . '/' . str_replace("\\","/", $fileName);
                    $APP_ID      = ConfigService::get('content_censor', 'app_id');
                    $API_KEY     = ConfigService::get('content_censor', 'api_key');
                    $SECRET_KEY  = ConfigService::get('content_censor', 'secret_key');
                    $client      = new AipContentCensor($APP_ID, $API_KEY, $SECRET_KEY);
                    $imageResult = $client->imageCensorUserDefined(FileService::getFileUrl($url));
                    if (isset($imageResult['error_code'])) {
                        Log::write('用户上传图片审核失败-' . json_encode($imageResult, JSON_UNESCAPED_UNICODE));
                    }
                    if (isset($imageResult['conclusionType']) && $imageResult['conclusionType'] > UserEnum::CENSOR_STATUS_PASS) {
                        throw new Exception('上传图片涉嫌存在违规');
                    }
                }
            }

            // 3、处理文件名称
            if (strlen($fileInfo['name']) > (129 - 1)) {
                $name = substr($fileInfo['name'], 0, 123);
                $nameEnd = substr($fileInfo['name'], strlen($fileInfo['name'])-5, strlen($fileInfo['name']));
                $fileInfo['name'] = $name . $nameEnd;
            }

            // 4、写入数据库中
            $file = File::create([
                'cid'         => $cid,
                'type'        => FileEnum::IMAGE_TYPE,
                'name'        => trim($fileInfo['name']),
                'uri'         => $saveDir . '/' . str_replace("\\","/", $fileName),
                'source'      => $source,
                'source_id'   => $sourceId,
                'create_time' => time(),
                'ip'          => request()->ip(),
            ]);

            // 5、返回结果
            return [
                'id'   => $file['id'],
                'cid'  => $file['cid'],
                'type' => $file['type'],
                'name' => $file['name'],
                'uri'  => FileService::getFileUrl($file['uri']),
                'url'  => $file['uri']
            ]??[];

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @notes 视频上传
     * @param $cid
     * @param int $sourceId
     * @param int $source
     * @param string $saveDir
     * @return array
     * @throws Exception
     * <AUTHOR>
     * @date 2021/12/29 16:32
     */
    public static function video($cid, int $sourceId = 0, int $source = FileEnum::SOURCE_ADMIN, string $saveDir = 'uploads/video'): array
    {
        try {
            $config = [
                'default' => ConfigService::get('storage', 'default', 'local'),
                'engine'  => ConfigService::get('storage') ?? ['local'=>[]],
            ];

            // 2、执行文件上传
            $StorageDriver = new StorageDriver($config);
            $StorageDriver->setUploadFile('file', FileEnum::VIDEO_TYPE);
            $fileName = $StorageDriver->getFileName();
            $fileInfo = $StorageDriver->getFileInfo();

            // 🔒 安全修复：增强视频文件上传安全验证
            if (!self::validateVideoFile($fileInfo, $StorageDriver->getUploadFile())) {
                throw new Exception("视频文件安全验证失败，不允许上传该文件");
            }

            // 上传文件
            $saveDir = $saveDir . '/' .  date('Ymd');
            if (!$StorageDriver->upload($saveDir)) {
                throw new Exception($StorageDriver->getError());
            }

            // 3、处理文件名称
            if (strlen($fileInfo['name']) > 128) {
                $name = substr($fileInfo['name'], 0, 123);
                $nameEnd = substr($fileInfo['name'], strlen($fileInfo['name'])-5, strlen($fileInfo['name']));
                $fileInfo['name'] = $name . $nameEnd;
            }

            // 4、写入数据库中
            $file = File::create([
                'cid'         => $cid,
                'type'        => FileEnum::VIDEO_TYPE,
                'name'        => $fileInfo['name'],
                'uri'         => $saveDir . '/' . str_replace("\\","/", $fileName),
                'source'      => $source,
                'source_id'   => $sourceId,
                'create_time' => time(),
                'ip'          => request()->ip(),
            ]);

            // 5、返回结果
            return [
                'id'   => $file['id'],
                'cid'  => $file['cid'],
                'type' => $file['type'],
                'name' => $file['name'],
                'uri'  => FileService::getFileUrl($file['uri']),
                'url'  => $file['uri']
            ]??[];
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @notes 文件上传
     * @param $cid
     * @param int $sourceId
     * @param int $source
     * @param string $saveDir
     * @return array
     * @throws Exception
     * <AUTHOR>
     */
    public static function files($cid, int $sourceId = 0, int $source = FileEnum::SOURCE_ADMIN, string $saveDir = 'uploads/files'): array
    {
        try {
            $config = [
                'default' => ConfigService::get('storage', 'default', 'local'),
                'engine'  => ConfigService::get('storage') ?? ['local'=>[]],
            ];

            // 2、执行文件上传
            $StorageDriver = new StorageDriver($config);
            $StorageDriver->setUploadFile('file', FileEnum::FILE_TYPE);
            $fileName = $StorageDriver->getFileName();
            $fileInfo = $StorageDriver->getFileInfo();

            // 上传文件
            $saveDir = $saveDir . '/' .  date('Ymd');
            if (!$StorageDriver->upload($saveDir)) {
                throw new Exception($StorageDriver->getError());
            }

            // 3、处理文件名称
            if (strlen($fileInfo['name']) > 128) {
                $name = substr($fileInfo['name'], 0, 124);
                $nameEnd = substr($fileInfo['name'], strlen($fileInfo['name'])-5, strlen($fileInfo['name']));
                $fileInfo['name'] = $name . $nameEnd;
            }

            // 4、写入数据库中
            $file = File::create([
                'cid'         => $cid,
                'name'        => $fileInfo['name'],
                'type'        => FileEnum::FILE_TYPE,
                'uri'         => $saveDir . '/' . str_replace("\\","/", $fileName),
                'source_id'   => $sourceId,
                'source'      => $source,
                'create_time' => time(),
                'ip'          => request()->ip(),
            ]);

            // 5、返回结果
            return [
                    'id'   => $file['id'],
                    'cid'  => $file['cid'],
                    'type' => $file['type'],
                    'name' => $file['name'],
                    'uri'  => FileService::getFileUrl($file['uri']),
                    'url'  => $file['uri']
                ]??[];

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @notes 音频上传
     * @param $cid
     * @param int $sourceId
     * @param int $source
     * @param string $saveDir
     * @return array
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/9 19:14
     */
    public static function audio($cid,int $sourceId = 0, int $source = FileEnum::SOURCE_ADMIN, string $saveDir = 'uploads/audio'):array
    {
        try {
            $config = [
                'default' => ConfigService::get('storage', 'default', 'local'),
                'engine'  => ConfigService::get('storage') ?? ['local'=>[]],
            ];

            // 2、执行文件上传
            $StorageDriver = new StorageDriver($config);
            $StorageDriver->setUploadFile('file');
            $fileName = $StorageDriver->getFileName();
            $fileInfo = $StorageDriver->getFileInfo();

            // 🔒 安全修复：增强音频文件上传安全验证
            if (!self::validateAudioFile($fileInfo, $StorageDriver->getUploadFile())) {
                throw new Exception("音频文件安全验证失败，不允许上传该文件");
            }

            // 上传文件
            $saveDir = $saveDir . '/' .  date('Ymd');
            if (!$StorageDriver->upload($saveDir)) {
                throw new Exception($StorageDriver->getError());
            }

            // 3、处理文件名称
            if (strlen($fileInfo['name']) > 128) {
                $name = substr($fileInfo['name'], 0, 122);
                $nameEnd = substr($fileInfo['name'], strlen($fileInfo['name'])-5, strlen($fileInfo['name']));
                $fileInfo['name'] = $name . $nameEnd;
            }

            // 4、写入数据库中
            $file = File::create([
                'cid'         => $cid,
                'name'        => $fileInfo['name'],
                'type'        => FileEnum::AUDIO_TYPE,
                'uri'         => $saveDir . '/' . str_replace("\\","/", $fileName),
                'source'      => $source,
                'source_id'   => $sourceId,
                'create_time' => time(),
                'ip'          => request()->ip(),
            ]);

            // 5、返回结果
            return [
                    'id'   => $file['id'],
                    'cid'  => $file['cid'],
                    'type' => $file['type'],
                    'name' => $file['name'],
                    'uri'  => FileService::getFileUrl($file['uri']),
                    'url'  => $file['uri']
                ]??[];

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }


    /**
     * @notes 保存文件到指定存储
     * @param string $saveDir
     * @param string $storage
     * @return array
     * @throws Exception
     * <AUTHOR>
     * @date 2024/4/10 16:48
     */
    public static function saveFileStorage(string $saveDir = 'uploads/file',string $storage = 'local'): array
    {
        try {
            $config = [
                'default' => $storage,
                'engine' => ConfigService::get('storage') ?? ['local' => []],
            ];
            // 2、执行文件上传
            $StorageDriver = new StorageDriver($config,$storage);
            $StorageDriver->setUploadFile('file',FileEnum::FILE_TYPE);
            if (!$StorageDriver->upload($saveDir)) {
                throw new Exception($StorageDriver->getError());
            }

            $fileName = $StorageDriver->getFileName();
            $fileInfo = $StorageDriver->getFileInfo();

            // 3、处理文件名称
            if (strlen($fileInfo['name']) > 128) {
                $file_name = substr($fileInfo['name'], 0, 123);
                $file_end = substr($fileInfo['name'], strlen(trim($fileInfo['name'])) - 5, strlen($fileInfo['name']));
                $fileInfo['name'] = $file_name . $file_end;
            }
            $uri = $saveDir . '/' . str_replace("\\", "/", $fileName);

            // 5、返回结果
            return [
                'uri' => FileService::getFileUrl($uri),
                'url' => $uri
            ];

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @notes 判断当前存储配置(是否需要保存到oss)
     * @param $url
     * @return true
     * @throws Exception
     * <AUTHOR>
     */
    public static function saveOssFile($url): bool
    {
        $config = [
            'default' => ConfigService::get('storage', 'default', 'local'),
            'engine' => ConfigService::get('storage'),
        ];

        if('local' == $config['default']){
            return true;
        }

        // 这里不使用https,部分用户可能因为证书问题报错
        $localFileUrl = request()->domain(true).'/'.$url;
        $StorageDriver = new StorageDriver($config);
        if (!$StorageDriver->fetch($localFileUrl, $url)) {
            throw new Exception('文件保存失败:' . $StorageDriver->getError());
        }

        // 如果是oss,删除本地文件
        unlink($url);
        return true;
    }


    /**
     * @notes 根据oss下载文件
     * @param $teFileUrl
     * @param $savePath
     * @param $fileName
     * @return string
     * @throws Exception
     * <AUTHOR>
     * @date 2024/4/15 10:34
     */
    public static function baseOssSaveFile($teFileUrl,$savePath,$fileName): string
    {
        $config = [
            'default' => ConfigService::get('storage', 'default', 'local'),
            'engine' => ConfigService::get('storage')
        ];
        if ($config['default'] == 'local') {
            // 本地存储
            $filePath = download_file($teFileUrl,$savePath,$fileName);
        } else {
            // 第三方存储
            $StorageDriver = new StorageDriver($config);
            if (!$StorageDriver->fetch($teFileUrl,$savePath.$fileName)) {
                throw new Exception('图标下载失败:' . $StorageDriver->getError());
            }
        }
        return $savePath.$fileName;
    }

    /**
     * 🔒 安全修复：图片文件安全验证方法
     * @notes 增强的图片文件安全验证，包含扩展名、MIME类型、文件头验证
     * @param array $fileInfo 文件信息
     * @param mixed $uploadFile 上传的文件对象
     * @return bool 验证是否通过
     * @security_fix 2025/08/01 - 多层文件安全验证机制
     */
    private static function validateImageFile(array $fileInfo, $uploadFile): bool
    {
        try {
            // 1. 扩展名验证（基础验证）
            $allowedExtensions = config('project.file_image', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
            $fileExtension = strtolower($fileInfo['ext']);

            if (!in_array($fileExtension, $allowedExtensions)) {
                Log::warning('文件上传安全警告：不允许的扩展名', [
                    'extension' => $fileExtension,
                    'allowed' => $allowedExtensions,
                    'ip' => request()->ip(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return false;
            }

            // 2. MIME类型验证
            $allowedMimeTypes = [
                'image/jpeg',
                'image/jpg',
                'image/png',
                'image/gif',
                'image/webp'
            ];

            // 获取文件的真实MIME类型
            $filePath = $uploadFile->getPathname();
            if (function_exists('finfo_open')) {
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $filePath);
                finfo_close($finfo);

                if (!in_array($mimeType, $allowedMimeTypes)) {
                    Log::warning('文件上传安全警告：不允许的MIME类型', [
                        'mime_type' => $mimeType,
                        'extension' => $fileExtension,
                        'allowed_mimes' => $allowedMimeTypes,
                        'ip' => request()->ip(),
                        'timestamp' => date('Y-m-d H:i:s')
                    ]);
                    return false;
                }
            }

            // 3. 文件头魔数验证（防止文件伪装）
            $fileHeader = '';
            if (is_readable($filePath)) {
                $handle = fopen($filePath, 'rb');
                if ($handle) {
                    $fileHeader = bin2hex(fread($handle, 8)); // 读取前8字节
                    fclose($handle);
                }
            }

            // 常见图片格式的文件头魔数
            $validHeaders = [
                'ffd8ffe0', 'ffd8ffe1', 'ffd8ffe2', 'ffd8ffe3', 'ffd8ffe8', // JPEG
                '89504e47', // PNG
                '47494638', // GIF
                '52494646', // WEBP (RIFF)
            ];

            $headerMatch = false;
            foreach ($validHeaders as $validHeader) {
                if (strpos($fileHeader, $validHeader) === 0) {
                    $headerMatch = true;
                    break;
                }
            }

            if (!$headerMatch) {
                Log::warning('文件上传安全警告：文件头验证失败', [
                    'file_header' => $fileHeader,
                    'extension' => $fileExtension,
                    'valid_headers' => $validHeaders,
                    'ip' => request()->ip(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return false;
            }

            // 4. 文件大小验证（防止过大文件攻击）
            $maxFileSize = 10 * 1024 * 1024; // 10MB
            if ($fileInfo['size'] > $maxFileSize) {
                Log::warning('文件上传安全警告：文件过大', [
                    'file_size' => $fileInfo['size'],
                    'max_size' => $maxFileSize,
                    'ip' => request()->ip(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return false;
            }

            // 5. 文件名安全检查（防止路径遍历）
            $fileName = $fileInfo['name'];
            if (strpos($fileName, '..') !== false ||
                strpos($fileName, '/') !== false ||
                strpos($fileName, '\\') !== false ||
                strpos($fileName, '<') !== false ||
                strpos($fileName, '>') !== false) {
                Log::warning('文件上传安全警告：文件名包含危险字符', [
                    'file_name' => $fileName,
                    'ip' => request()->ip(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return false;
            }

            // 记录成功的文件验证
            Log::info('文件上传安全验证通过', [
                'file_name' => $fileName,
                'extension' => $fileExtension,
                'mime_type' => $mimeType ?? 'unknown',
                'file_size' => $fileInfo['size'],
                'ip' => request()->ip(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('文件上传安全验证异常', [
                'error' => $e->getMessage(),
                'file_info' => $fileInfo,
                'ip' => request()->ip(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return false;
        }
    }

    /**
     * 🔒 安全修复：视频文件安全验证方法
     * @notes 增强的视频文件安全验证
     * @param array $fileInfo 文件信息
     * @param mixed $uploadFile 上传的文件对象
     * @return bool 验证是否通过
     */
    private static function validateVideoFile(array $fileInfo, $uploadFile): bool
    {
        try {
            // 1. 扩展名验证
            $allowedExtensions = config('project.file_video', ['mp4', 'avi', 'mov', 'wmv', 'flv']);
            $fileExtension = strtolower($fileInfo['ext']);

            if (!in_array($fileExtension, $allowedExtensions)) {
                Log::warning('视频上传安全警告：不允许的扩展名', [
                    'extension' => $fileExtension,
                    'ip' => request()->ip()
                ]);
                return false;
            }

            // 2. MIME类型验证
            $allowedMimeTypes = [
                'video/mp4',
                'video/avi',
                'video/quicktime',
                'video/x-ms-wmv',
                'video/x-flv'
            ];

            $filePath = $uploadFile->getPathname();
            if (function_exists('finfo_open')) {
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $filePath);
                finfo_close($finfo);

                if (!in_array($mimeType, $allowedMimeTypes)) {
                    Log::warning('视频上传安全警告：不允许的MIME类型', [
                        'mime_type' => $mimeType,
                        'extension' => $fileExtension,
                        'ip' => request()->ip()
                    ]);
                    return false;
                }
            }

            // 3. 文件大小验证（视频文件限制更大）
            $maxFileSize = 100 * 1024 * 1024; // 100MB
            if ($fileInfo['size'] > $maxFileSize) {
                Log::warning('视频上传安全警告：文件过大', [
                    'file_size' => $fileInfo['size'],
                    'max_size' => $maxFileSize,
                    'ip' => request()->ip()
                ]);
                return false;
            }

            return true;
        } catch (Exception $e) {
            Log::error('视频文件安全验证异常', [
                'error' => $e->getMessage(),
                'ip' => request()->ip()
            ]);
            return false;
        }
    }

    /**
     * 🔒 安全修复：音频文件安全验证方法
     * @notes 增强的音频文件安全验证
     * @param array $fileInfo 文件信息
     * @param mixed $uploadFile 上传的文件对象
     * @return bool 验证是否通过
     */
    private static function validateAudioFile(array $fileInfo, $uploadFile): bool
    {
        try {
            // 1. 扩展名验证
            $allowedExtensions = config('project.file_audio', ['mp3', 'wav', 'ogg', 'aac', 'm4a']);
            $fileExtension = strtolower($fileInfo['ext']);

            if (!in_array($fileExtension, $allowedExtensions)) {
                Log::warning('音频上传安全警告：不允许的扩展名', [
                    'extension' => $fileExtension,
                    'ip' => request()->ip()
                ]);
                return false;
            }

            // 2. MIME类型验证
            $allowedMimeTypes = [
                'audio/mpeg',
                'audio/wav',
                'audio/ogg',
                'audio/aac',
                'audio/mp4'
            ];

            $filePath = $uploadFile->getPathname();
            if (function_exists('finfo_open')) {
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $filePath);
                finfo_close($finfo);

                if (!in_array($mimeType, $allowedMimeTypes)) {
                    Log::warning('音频上传安全警告：不允许的MIME类型', [
                        'mime_type' => $mimeType,
                        'extension' => $fileExtension,
                        'ip' => request()->ip()
                    ]);
                    return false;
                }
            }

            // 3. 文件大小验证
            $maxFileSize = 50 * 1024 * 1024; // 50MB
            if ($fileInfo['size'] > $maxFileSize) {
                Log::warning('音频上传安全警告：文件过大', [
                    'file_size' => $fileInfo['size'],
                    'max_size' => $maxFileSize,
                    'ip' => request()->ip()
                ]);
                return false;
            }

            return true;
        } catch (Exception $e) {
            Log::error('音频文件安全验证异常', [
                'error' => $e->getMessage(),
                'ip' => request()->ip()
            ]);
            return false;
        }
    }
}