<?php
/**
 * 永久开启敏感词检测服务
 * 特点：
 * 1. 永久开启，不依赖数据库配置
 * 2. 只读取文件敏感词库，提高稳定性
 * 3. 高效缓存机制
 * 4. Docker环境优化
 */

namespace app\common\service;

use DfaFilter\SensitiveHelper;
use Exception;

class PermanentSensitiveService
{
    // 内存缓存
    private static $sensitiveWords = null;
    private static $dfaTree = null;
    private static $lastLoadTime = 0;
    private static $cacheTimeout = 3600; // 1小时缓存
    
    // 文件路径（支持多种路径）
    private static $possiblePaths = [
        '/www/wwwroot/ai/server/extend/',
        './server/extend/',
        '../extend/',
        'extend/',
        'server/extend/'
    ];
    
    /**
     * 敏感词检测主方法
     * @param string $content 待检测内容
     * @throws Exception 发现敏感词时抛出异常
     */
    public static function sensitive(string $content): void
    {
        // 快速空内容检查
        if (empty(trim($content))) {
            return;
        }
        
        try {
            // 获取敏感词数据
            $sensitiveWords = self::getSensitiveWords();
            
            if (empty($sensitiveWords)) {
                // 如果没有敏感词，使用基础敏感词列表
                $sensitiveWords = self::getBasicSensitiveWords();
            }
            
            // 使用DFA算法检测
            $dfaTree = self::getDfaTree($sensitiveWords);
            
            if ($dfaTree === null) {
                // DFA树构建失败，使用简单字符串匹配
                self::simpleStringMatch($content, $sensitiveWords);
                return;
            }
            
            // 分批检测（避免单次构建过大的DFA树）
            $sensitiveWordArr = array_chunk($sensitiveWords, 20000);
            $sensitiveWordGroup = [];
            
            foreach ($sensitiveWordArr as $sensitiveWordArrValue) {
                $handle = SensitiveHelper::init()->setTree($sensitiveWordArrValue);
                $badWordList = $handle->getBadWord($content);
                $sensitiveWordGroup = array_merge($sensitiveWordGroup, $badWordList);
            }
            
            $sensitiveWordGroup = array_unique($sensitiveWordGroup);
            if (!empty($sensitiveWordGroup)) {
                throw new Exception('提问存在敏感词：' . implode(',', $sensitiveWordGroup));
            }
            
        } catch (Exception $e) {
            // 重新抛出敏感词异常
            if (strpos($e->getMessage(), '敏感词') !== false) {
                throw $e;
            }
            
            // 其他异常时使用降级检测
            self::fallbackDetection($content);
        }
    }
    
    /**
     * 获取敏感词数据（带缓存）
     */
    private static function getSensitiveWords(): array
    {
        $now = time();
        
        // 检查缓存是否有效
        if (self::$sensitiveWords !== null && 
            ($now - self::$lastLoadTime) < self::$cacheTimeout) {
            return self::$sensitiveWords;
        }
        
        // 重新加载敏感词
        self::$sensitiveWords = self::loadSensitiveWords();
        self::$lastLoadTime = $now;
        self::$dfaTree = null; // 清除DFA树缓存，下次重建
        
        return self::$sensitiveWords;
    }
    
    /**
     * 从文件加载敏感词
     */
    private static function loadSensitiveWords(): array
    {
        $keyFile = null;
        $dataFile = null;
        
        // 尝试多个可能的路径
        foreach (self::$possiblePaths as $path) {
            $testKeyFile = $path . 'sensitive_key.bin';
            $testDataFile = $path . 'sensitive_data.bin';
            
            if (file_exists($testKeyFile) && file_exists($testDataFile)) {
                $keyFile = $testKeyFile;
                $dataFile = $testDataFile;
                break;
            }
        }
        
        if (!$keyFile || !$dataFile) {
            return [];
        }
        
        try {
            // 读取密钥
            $file = fopen($keyFile, "rb");
            if (!$file) {
                return [];
            }
            
            $key = fread($file, 32);
            $iv = fread($file, 16);
            fclose($file);
            
            // 读取加密数据
            $ciphertext = file_get_contents($dataFile);
            if ($ciphertext === false) {
                return [];
            }
            
            // 解密
            $plaintext = openssl_decrypt($ciphertext, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
            if ($plaintext === false) {
                return [];
            }
            
            // 处理敏感词
            $words = explode(PHP_EOL, trim($plaintext));
            $words = array_filter($words); // 过滤空行
            $words = array_unique($words); // 去重
            
            return $words;
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取DFA树（带缓存）
     */
    private static function getDfaTree(array $sensitiveWords): ?SensitiveHelper
    {
        if (self::$dfaTree !== null) {
            return self::$dfaTree;
        }
        
        try {
            // 构建DFA树
            self::$dfaTree = SensitiveHelper::init()->setTree($sensitiveWords);
            return self::$dfaTree;
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * 简单字符串匹配（DFA失败时的降级方案）
     */
    private static function simpleStringMatch(string $content, array $sensitiveWords): void
    {
        $foundWords = [];
        
        foreach ($sensitiveWords as $word) {
            if (mb_strpos($content, $word) !== false) {
                $foundWords[] = $word;
            }
        }
        
        if (!empty($foundWords)) {
            $foundWords = array_unique($foundWords);
            throw new Exception('提问存在敏感词：' . implode(',', $foundWords));
        }
    }
    
    /**
     * 降级检测（异常时的最后保障）
     */
    private static function fallbackDetection(string $content): void
    {
        // 使用基础敏感词列表进行检测
        $basicWords = self::getBasicSensitiveWords();
        self::simpleStringMatch($content, $basicWords);
    }
    
    /**
     * 获取基础敏感词列表（兜底方案）
     */
    private static function getBasicSensitiveWords(): array
    {
        return [
            // 政治敏感
            '八九六四', '1989', '六四事件', '安街逆', '长安街逆行',
            '法轮功', '法轮', '台独', '藏独', '疆独',
            
            // 色情暴力
            '色情', '赌博', '毒品', '暴力', '恐怖',
            '自杀', '爆炸', '枪支', '军火',
            
            // 其他敏感
            '反动', '邪教', '分裂', '颠覆'
        ];
    }
    
    /**
     * 清除缓存
     */
    public static function clearCache(): void
    {
        self::$sensitiveWords = null;
        self::$dfaTree = null;
        self::$lastLoadTime = 0;
    }
    
    /**
     * 获取统计信息
     */
    public static function getStats(): array
    {
        return [
            'words_count' => self::$sensitiveWords ? count(self::$sensitiveWords) : 0,
            'cache_valid' => self::$sensitiveWords !== null,
            'last_load_time' => self::$lastLoadTime ? date('Y-m-d H:i:s', self::$lastLoadTime) : 'never',
            'dfa_tree_ready' => self::$dfaTree !== null,
            'service_type' => 'permanent_enabled'
        ];
    }
    
    /**
     * 预热缓存
     */
    public static function warmup(): array
    {
        $words = self::getSensitiveWords();
        $tree = self::getDfaTree($words);
        
        return [
            'words_loaded' => count($words),
            'dfa_tree_ready' => $tree !== null,
            'cache_time' => date('Y-m-d H:i:s', self::$lastLoadTime),
            'service_type' => 'permanent_enabled'
        ];
    }
} 