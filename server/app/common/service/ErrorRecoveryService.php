<?php

namespace app\common\service;

use app\common\exception\RobotRevenueException;
use think\facade\Log;
use think\facade\Cache;

/**
 * 错误监控和恢复服务
 */
class ErrorRecoveryService
{
    // 缓存键前缀
    private const CACHE_PREFIX = 'error_recovery:';
    
    // 错误统计缓存键
    private const ERROR_STATS_KEY = self::CACHE_PREFIX . 'stats:';
    
    // 熔断器缓存键
    private const CIRCUIT_BREAKER_KEY = self::CACHE_PREFIX . 'circuit:';
    
    // 默认配置
    private const DEFAULT_CONFIG = [
        'error_threshold' => 10,        // 错误阈值
        'time_window' => 300,          // 时间窗口（秒）
        'circuit_open_duration' => 60,  // 熔断器开启持续时间（秒）
        'recovery_timeout' => 30,       // 恢复超时时间（秒）
    ];

    /**
     * 记录错误并检查是否需要熔断
     */
    public static function recordError(string $service, \Throwable $exception): bool
    {
        $config = self::getConfig();
        $statsKey = self::ERROR_STATS_KEY . $service;
        $circuitKey = self::CIRCUIT_BREAKER_KEY . $service;
        
        try {
            // 获取当前错误统计
            $stats = Cache::get($statsKey, []);
            $currentTime = time();
            
            // 清理过期的错误记录
            $stats = array_filter($stats, function($timestamp) use ($currentTime, $config) {
                return ($currentTime - $timestamp) <= $config['time_window'];
            });
            
            // 添加新的错误记录
            $stats[] = $currentTime;
            
            // 保存统计数据
            Cache::set($statsKey, $stats, $config['time_window']);
            
            // 检查是否超过错误阈值
            if (count($stats) >= $config['error_threshold']) {
                // 开启熔断器
                Cache::set($circuitKey, [
                    'status' => 'open',
                    'open_time' => $currentTime,
                    'error_count' => count($stats),
                    'last_error' => $exception->getMessage()
                ], $config['circuit_open_duration']);
                
                Log::warning("服务 {$service} 熔断器开启", [
                    'service' => $service,
                    'error_count' => count($stats),
                    'time_window' => $config['time_window'],
                    'threshold' => $config['error_threshold']
                ]);
                
                return true; // 需要熔断
            }
            
            return false; // 不需要熔断
            
        } catch (\Throwable $e) {
            Log::error('错误监控服务异常', [
                'service' => $service,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 检查服务是否被熔断
     */
    public static function isCircuitOpen(string $service): bool
    {
        $circuitKey = self::CIRCUIT_BREAKER_KEY . $service;
        $circuitInfo = Cache::get($circuitKey);
        
        if (!$circuitInfo || $circuitInfo['status'] !== 'open') {
            return false;
        }
        
        $config = self::getConfig();
        $currentTime = time();
        
        // 检查熔断器是否应该进入半开状态
        if (($currentTime - $circuitInfo['open_time']) >= $config['circuit_open_duration']) {
            // 进入半开状态
            Cache::set($circuitKey, [
                'status' => 'half_open',
                'open_time' => $circuitInfo['open_time'],
                'half_open_time' => $currentTime,
                'error_count' => $circuitInfo['error_count']
            ], $config['recovery_timeout']);
            
            Log::info("服务 {$service} 熔断器进入半开状态", [
                'service' => $service,
                'open_duration' => $currentTime - $circuitInfo['open_time']
            ]);
            
            return false; // 半开状态允许尝试
        }
        
        return true; // 仍处于熔断状态
    }

    /**
     * 记录服务恢复成功
     */
    public static function recordSuccess(string $service): void
    {
        $circuitKey = self::CIRCUIT_BREAKER_KEY . $service;
        $statsKey = self::ERROR_STATS_KEY . $service;
        
        try {
            $circuitInfo = Cache::get($circuitKey);
            
            if ($circuitInfo && $circuitInfo['status'] === 'half_open') {
                // 从半开状态恢复到正常状态
                Cache::delete($circuitKey);
                Cache::delete($statsKey);
                
                Log::info("服务 {$service} 熔断器恢复正常", [
                    'service' => $service,
                    'recovery_time' => date('Y-m-d H:i:s')
                ]);
            }
        } catch (\Throwable $e) {
            Log::error('记录服务恢复成功时异常', [
                'service' => $service,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 安全执行服务调用
     */
    public static function safeExecute(string $service, callable $callback, callable $fallback = null)
    {
        // 检查熔断器状态
        if (self::isCircuitOpen($service)) {
            Log::info("服务 {$service} 被熔断，跳过执行", [
                'service' => $service
            ]);
            
            if ($fallback && is_callable($fallback)) {
                return $fallback();
            }
            
            return null;
        }
        
        try {
            $result = $callback();
            
            // 记录成功执行
            self::recordSuccess($service);
            
            return $result;
            
        } catch (RobotRevenueException $e) {
            // 业务异常，记录但不触发熔断（除非是严重的系统异常）
            if (in_array($e->getCode(), [
                RobotRevenueException::DATABASE_ERROR,
                RobotRevenueException::TABLE_NOT_EXISTS
            ])) {
                self::recordError($service, $e);
            }
            
            throw $e;
            
        } catch (\Throwable $e) {
            // 系统异常，记录并可能触发熔断
            self::recordError($service, $e);
            throw $e;
        }
    }

    /**
     * 获取错误统计信息
     */
    public static function getErrorStats(string $service): array
    {
        $statsKey = self::ERROR_STATS_KEY . $service;
        $circuitKey = self::CIRCUIT_BREAKER_KEY . $service;
        
        $stats = Cache::get($statsKey, []);
        $circuitInfo = Cache::get($circuitKey);
        
        $config = self::getConfig();
        $currentTime = time();
        
        // 清理过期的错误记录
        $recentErrors = array_filter($stats, function($timestamp) use ($currentTime, $config) {
            return ($currentTime - $timestamp) <= $config['time_window'];
        });
        
        return [
            'service' => $service,
            'total_errors' => count($stats),
            'recent_errors' => count($recentErrors),
            'error_threshold' => $config['error_threshold'],
            'time_window' => $config['time_window'],
            'circuit_status' => $circuitInfo['status'] ?? 'closed',
            'circuit_info' => $circuitInfo,
            'is_circuit_open' => self::isCircuitOpen($service)
        ];
    }

    /**
     * 重置熔断器
     */
    public static function resetCircuitBreaker(string $service): bool
    {
        try {
            $circuitKey = self::CIRCUIT_BREAKER_KEY . $service;
            $statsKey = self::ERROR_STATS_KEY . $service;
            
            Cache::delete($circuitKey);
            Cache::delete($statsKey);
            
            Log::info("服务 {$service} 熔断器已手动重置", [
                'service' => $service,
                'reset_time' => date('Y-m-d H:i:s')
            ]);
            
            return true;
        } catch (\Throwable $e) {
            Log::error("重置服务 {$service} 熔断器失败", [
                'service' => $service,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取配置
     */
    private static function getConfig(): array
    {
        // 这里可以从配置文件或数据库获取配置
        // 暂时使用默认配置
        return self::DEFAULT_CONFIG;
    }

    /**
     * 批量获取多个服务的状态
     */
    public static function getServicesStatus(array $services): array
    {
        $result = [];
        
        foreach ($services as $service) {
            $result[$service] = self::getErrorStats($service);
        }
        
        return $result;
    }

    /**
     * 清理过期的监控数据
     */
    public static function cleanupExpiredData(): int
    {
        $cleaned = 0;
        
        try {
            // 这里可以实现清理逻辑
            // 由于使用了缓存TTL，大部分数据会自动过期
            // 这个方法主要用于手动清理或定时任务
            
            Log::info('错误监控数据清理完成', [
                'cleaned_count' => $cleaned
            ]);
            
        } catch (\Throwable $e) {
            Log::error('清理错误监控数据失败', [
                'error' => $e->getMessage()
            ]);
        }
        
        return $cleaned;
    }
} 