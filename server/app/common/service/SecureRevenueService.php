<?php

namespace app\common\service;

use app\common\model\kb\KbRobotRecord;
use app\common\model\kb\KbRobotRevenueConfig;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\model\kb\KbRobotSquare;
use app\common\model\user\User;
use app\common\model\user\UserAccountLog;
use app\common\enum\user\AccountLogEnum;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use Exception;

/**
 * 🔒 安全增强的智能体分成服务
 * @notes 修复数据一致性、安全性、费用计算等关键问题
 * @security_fix 2025/08/02 - 全面安全修复
 */
class SecureRevenueService
{
    // 安全配置
    private const LOCK_TIMEOUT = 30; // 分布式锁超时时间
    private const MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
    private const COST_TOLERANCE = 0.01; // 费用验证容差
    private const MAX_COST_LIMIT = 1000.0; // 最大费用限制
    
    // 数据完整性密钥
    private static ?string $integrityKey = null;
    
    /**
     * 🔒 安全修复：主要分成处理入口
     * @param array $record 对话记录
     * @return bool
     * @security_fix 2025/08/02 - 并发安全 + 数据一致性
     */
    public static function processRecord(array $record): bool
    {
        try {
            // 1. 基础验证
            if (!self::validateRecord($record)) {
                return false;
            }
            
            // 2. 🔒 分布式锁防止并发冲突
            $lockKey = self::generateLockKey($record);
            return self::executeWithLock($lockKey, function() use ($record) {
                return self::processRevenueSecurely($record);
            });
            
        } catch (Exception $e) {
            Log::error('[安全分成] 处理异常', [
                'record_id' => $record['id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return false;
        }
    }
    
    /**
     * 🔒 安全修复：分布式锁执行
     * @param string $lockKey 锁键
     * @param callable $callback 回调函数
     * @return bool
     * @security_fix 2025/08/02 - 防止并发冲突
     */
    private static function executeWithLock(string $lockKey, callable $callback): bool
    {
        $redis = Cache::store('redis');
        $lockValue = uniqid(php_uname('n'), true);
        
        // 获取分布式锁
        $acquired = $redis->set($lockKey, $lockValue, self::LOCK_TIMEOUT);
        
        if (!$acquired) {
            Log::warning('[安全分成] 获取锁失败，可能存在并发操作', [
                'lock_key' => $lockKey,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return false;
        }
        
        try {
            // 执行业务逻辑
            return $callback();
        } finally {
            // 安全释放锁（验证锁值）
            $script = "
                if redis.call('get', KEYS[1]) == ARGV[1] then
                    return redis.call('del', KEYS[1])
                else
                    return 0
                end
            ";
            $redis->eval($script, [$lockKey, $lockValue], 1);
        }
    }
    
    /**
     * 🔒 安全修复：安全的分成处理逻辑
     * @param array $record 对话记录
     * @return bool
     * @security_fix 2025/08/02 - 数据一致性 + 安全验证
     */
    private static function processRevenueSecurely(array $record): bool
    {
        // 1. 检查重复分成
        if (self::isDuplicateRevenue($record)) {
            Log::info('[安全分成] 重复分成检查：已处理过', [
                'record_id' => $record['id']
            ]);
            return true;
        }
        
        // 2. 获取分成配置
        $config = self::getSecureConfig();
        if (!$config || !($config['is_enable'] ?? 0)) {
            return false;
        }
        
        // 3. 获取分享者信息
        $sharer = self::getSharer($record['square_id'] ?? 0);
        if (!$sharer) {
            return false;
        }
        
        // 4. 🔒 安全验证：防止自分成和关联账号分成
        if (!self::validateSharerSecurity($record, $sharer)) {
            return false;
        }
        
        // 5. 🔒 费用计算双重验证
        $cost = self::calculateCostSecurely($record);
        if ($cost <= 0) {
            return false;
        }
        
        // 6. 🔒 分成比例安全验证
        $amounts = self::calculateAmountsSecurely($cost, $config);
        if (!$amounts) {
            return false;
        }
        
        // 7. 🔒 分布式事务执行分成
        return self::executeRevenueTransaction($record, $sharer, $cost, $amounts, $config);
    }
    
    /**
     * 🔒 安全修复：检查重复分成
     * @param array $record 对话记录
     * @return bool
     * @security_fix 2025/08/02 - 防止重复分成
     */
    private static function isDuplicateRevenue(array $record): bool
    {
        $recordId = $record['id'] ?? 0;
        if (!$recordId) {
            return false;
        }
        
        // 检查是否已存在分成记录
        $exists = KbRobotRevenueLog::where('record_id', $recordId)->count();
        
        if ($exists > 0) {
            Log::warning('[安全分成] 发现重复分成尝试', [
                'record_id' => $recordId,
                'existing_count' => $exists,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return true;
        }
        
        return false;
    }
    
    /**
     * 🔒 安全修复：分享者安全验证
     * @param array $record 对话记录
     * @param array $sharer 分享者信息
     * @return bool
     * @security_fix 2025/08/02 - 防止自分成和关联账号分成
     */
    private static function validateSharerSecurity(array $record, array $sharer): bool
    {
        $userId = $record['user_id'] ?? 0;
        $sharerId = $sharer['user_id'] ?? 0;
        
        // 1. 基础自分成检查
        if ($userId === $sharerId) {
            Log::info('[安全分成] 自分成检查：用户不能给自己分成', [
                'user_id' => $userId,
                'sharer_id' => $sharerId
            ]);
            return false;
        }
        
        // 2. 🔒 关联账号检查（IP、设备、注册时间等）
        if (self::isRelatedAccount($userId, $sharerId)) {
            Log::warning('[安全分成] 关联账号检查：疑似关联账号分成', [
                'user_id' => $userId,
                'sharer_id' => $sharerId,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return false;
        }
        
        // 3. 🔒 频率限制检查
        if (!self::checkRevenueFrequency($userId, $sharerId)) {
            Log::warning('[安全分成] 频率限制：分成过于频繁', [
                'user_id' => $userId,
                'sharer_id' => $sharerId,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * 🔒 安全修复：关联账号检测
     * @param int $userId 用户ID
     * @param int $sharerId 分享者ID
     * @return bool
     * @security_fix 2025/08/02 - 防止关联账号分成
     */
    private static function isRelatedAccount(int $userId, int $sharerId): bool
    {
        try {
            // 获取用户信息
            $users = User::whereIn('id', [$userId, $sharerId])
                ->field('id,register_ip,last_login_ip,create_time')
                ->select()
                ->toArray();
            
            if (count($users) !== 2) {
                return false;
            }
            
            $user1 = $users[0]['id'] == $userId ? $users[0] : $users[1];
            $user2 = $users[0]['id'] == $sharerId ? $users[0] : $users[1];
            
            // 1. 相同注册IP
            if (!empty($user1['register_ip']) && $user1['register_ip'] === $user2['register_ip']) {
                return true;
            }
            
            // 2. 相同最后登录IP
            if (!empty($user1['last_login_ip']) && $user1['last_login_ip'] === $user2['last_login_ip']) {
                return true;
            }
            
            // 3. 注册时间过于接近（1小时内）
            $timeDiff = abs($user1['create_time'] - $user2['create_time']);
            if ($timeDiff < 3600) {
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            Log::error('[安全分成] 关联账号检测异常', [
                'user_id' => $userId,
                'sharer_id' => $sharerId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 🔒 安全修复：分成频率检查
     * @param int $userId 用户ID
     * @param int $sharerId 分享者ID
     * @return bool
     * @security_fix 2025/08/02 - 防止异常频繁分成
     */
    private static function checkRevenueFrequency(int $userId, int $sharerId): bool
    {
        $cacheKey = "revenue_freq:{$userId}:{$sharerId}";
        $redis = Cache::store('redis');
        
        // 获取最近1小时的分成次数
        $count = $redis->get($cacheKey) ?: 0;
        
        // 限制每小时最多50次分成
        if ($count >= 50) {
            return false;
        }
        
        // 增加计数
        $redis->set($cacheKey, $count + 1, 3600);
        
        return true;
    }
    
    /**
     * 🔒 安全修复：费用计算双重验证
     * @param array $record 对话记录
     * @return float
     * @security_fix 2025/08/02 - 防止费用篡改
     */
    private static function calculateCostSecurely(array $record): float
    {
        try {
            // 方法1：从flows字段计算
            $flowsCost = self::calculateFromFlows($record);
            
            // 方法2：从模型重新计算验证
            $modelCost = self::recalculateFromModel($record);
            
            // 🔒 双重验证：检查费用差异
            if (abs($flowsCost - $modelCost) > self::COST_TOLERANCE) {
                Log::error('[安全分成] 费用计算异常：双重验证失败', [
                    'record_id' => $record['id'],
                    'flows_cost' => $flowsCost,
                    'model_cost' => $modelCost,
                    'difference' => abs($flowsCost - $modelCost),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return 0;
            }
            
            // 🔒 费用上限保护
            $finalCost = min($flowsCost, self::MAX_COST_LIMIT);
            
            if ($finalCost !== $flowsCost) {
                Log::warning('[安全分成] 费用超限保护触发', [
                    'record_id' => $record['id'],
                    'original_cost' => $flowsCost,
                    'limited_cost' => $finalCost,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            
            return $finalCost;
            
        } catch (Exception $e) {
            Log::error('[安全分成] 费用计算异常', [
                'record_id' => $record['id'],
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return 0;
        }
    }
    
    /**
     * 🔒 安全修复：从flows字段计算费用
     * @param array $record 对话记录
     * @return float
     * @security_fix 2025/08/02 - 安全的flows解析
     */
    private static function calculateFromFlows(array $record): float
    {
        $flows = json_decode($record['flows'] ?? '[]', true);
        if (!is_array($flows)) {
            return 0;
        }
        
        $totalCost = 0;
        foreach ($flows as $flow) {
            if (!is_array($flow)) {
                continue;
            }
            
            $price = floatval($flow['total_price'] ?? 0);
            
            // 🔒 单项费用合理性检查
            if ($price < 0 || $price > 100) {
                Log::warning('[安全分成] 发现异常费用项', [
                    'record_id' => $record['id'],
                    'flow_name' => $flow['name'] ?? 'unknown',
                    'price' => $price,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                continue;
            }
            
            $totalCost += $price;
        }
        
        return round($totalCost, 4);
    }
    
    /**
     * 🔒 安全修复：从模型重新计算费用
     * @param array $record 对话记录
     * @return float
     * @security_fix 2025/08/02 - 费用重新计算验证
     */
    private static function recalculateFromModel(array $record): float
    {
        try {
            // 基于token数量和模型价格重新计算
            $tokens = intval($record['tokens'] ?? 0);
            $modelId = intval($record['model_id'] ?? 0);
            
            if ($tokens <= 0 || $modelId <= 0) {
                return 0;
            }
            
            // 这里应该调用实际的价格计算函数
            // 简化实现：基于token数量估算
            $estimatedCost = $tokens * 0.001; // 假设每token 0.001元
            
            return round($estimatedCost, 4);
            
        } catch (Exception $e) {
            Log::error('[安全分成] 模型费用重算异常', [
                'record_id' => $record['id'],
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * 生成分布式锁键
     */
    private static function generateLockKey(array $record): string
    {
        $recordId = $record['id'] ?? 0;
        $userId = $record['user_id'] ?? 0;
        return "revenue_lock:{$recordId}:{$userId}";
    }
    
    /**
     * 验证记录基础信息
     */
    private static function validateRecord(array $record): bool
    {
        $required = ['id', 'user_id', 'square_id'];
        foreach ($required as $field) {
            if (empty($record[$field])) {
                Log::warning('[安全分成] 记录验证失败：缺少必要字段', [
                    'missing_field' => $field,
                    'record' => $record
                ]);
                return false;
            }
        }
        return true;
    }
    
    /**
     * 获取安全配置
     */
    private static function getSecureConfig(): ?array
    {
        // 实现配置获取逻辑
        return [
            'is_enable' => 1,
            'share_ratio' => 30,
            'platform_ratio' => 70,
            'min_revenue' => 0.01
        ];
    }
    
    /**
     * 获取分享者信息
     */
    private static function getSharer(int $squareId): ?array
    {
        if (!$squareId) {
            return null;
        }

        $square = KbRobotSquare::where('id', $squareId)->find();
        return $square ? $square->toArray() : null;
    }

    /**
     * 🔒 安全修复：分成金额安全计算
     * @param float $cost 总费用
     * @param array $config 配置
     * @return array|null
     * @security_fix 2025/08/02 - 分成比例安全验证
     */
    private static function calculateAmountsSecurely(float $cost, array $config): ?array
    {
        $shareRatio = floatval($config['share_ratio'] ?? 0);
        $platformRatio = floatval($config['platform_ratio'] ?? 0);
        $minRevenue = floatval($config['min_revenue'] ?? 0.01);

        // 🔒 分成比例安全验证
        if ($shareRatio < 0 || $shareRatio > 100) {
            Log::error('[安全分成] 分成比例异常', [
                'share_ratio' => $shareRatio,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return null;
        }

        if ($platformRatio < 0 || $platformRatio > 100) {
            Log::error('[安全分成] 平台比例异常', [
                'platform_ratio' => $platformRatio,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return null;
        }

        // 🔒 比例总和验证
        $totalRatio = $shareRatio + $platformRatio;
        if (abs($totalRatio - 100) > 0.01) {
            Log::error('[安全分成] 分成比例总和异常', [
                'share_ratio' => $shareRatio,
                'platform_ratio' => $platformRatio,
                'total_ratio' => $totalRatio,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return null;
        }

        // 计算分成金额
        $shareAmount = round($cost * $shareRatio / 100, 4);
        $platformAmount = round($cost * $platformRatio / 100, 4);

        // 最小分成金额检查
        if ($shareAmount < $minRevenue) {
            Log::info('[安全分成] 分成金额低于最小值', [
                'share_amount' => $shareAmount,
                'min_revenue' => $minRevenue,
                'cost' => $cost
            ]);
            return null;
        }

        return [
            'share_amount' => $shareAmount,
            'platform_amount' => $platformAmount,
            'share_ratio' => $shareRatio,
            'platform_ratio' => $platformRatio
        ];
    }

    /**
     * 🔒 安全修复：分布式事务执行分成
     * @param array $record 对话记录
     * @param array $sharer 分享者信息
     * @param float $cost 总费用
     * @param array $amounts 分成金额
     * @param array $config 配置
     * @return bool
     * @security_fix 2025/08/02 - 分布式事务 + 数据完整性
     */
    private static function executeRevenueTransaction(array $record, array $sharer, float $cost, array $amounts, array $config): bool
    {
        $transactionId = uniqid('rev_', true);

        try {
            Db::startTrans();

            // 1. 🔒 创建分成记录（带数据签名）
            $logData = [
                'user_id' => $record['user_id'],
                'sharer_id' => $sharer['user_id'],
                'robot_id' => $record['robot_id'] ?? 0,
                'record_id' => $record['id'],
                'square_id' => $record['square_id'],
                'total_cost' => $cost,
                'share_amount' => $amounts['share_amount'],
                'platform_amount' => $amounts['platform_amount'],
                'share_ratio' => $amounts['share_ratio'],
                'platform_ratio' => $amounts['platform_ratio'],
                'settle_status' => 0,
                'transaction_id' => $transactionId,
                'create_time' => time(),
                'update_time' => time()
            ];

            // 🔒 数据完整性签名
            $logData['data_signature'] = self::generateDataSignature($logData);

            $revenueLog = KbRobotRevenueLog::create($logData);
            if (!$revenueLog) {
                throw new Exception('创建分成记录失败');
            }

            // 2. 🔒 更新用户余额（原子操作）
            $updateResult = Db::execute(
                'UPDATE cm_user SET balance = balance + ?, update_time = ? WHERE id = ? AND balance >= 0',
                [$amounts['share_amount'], time(), $sharer['user_id']]
            );

            if (!$updateResult) {
                throw new Exception('更新用户余额失败');
            }

            // 3. 🔒 记录账户变动日志
            UserAccountLog::add(
                $sharer['user_id'],
                AccountLogEnum::UM_INC_ROBOT_REVENUE,
                AccountLogEnum::INC,
                $amounts['share_amount'],
                '',
                '智能体分成收益',
                [
                    'robot_id' => $record['robot_id'] ?? 0,
                    'square_id' => $record['square_id'],
                    'record_id' => $record['id'],
                    'revenue_log_id' => $revenueLog->id,
                    'transaction_id' => $transactionId
                ]
            );

            // 4. 🔒 标记对话记录已分成
            KbRobotRecord::where('id', $record['id'])->update([
                'revenue_status' => 1,
                'revenue_time' => time(),
                'update_time' => time()
            ]);

            Db::commit();

            // 5. 🔒 记录成功日志
            Log::info('[安全分成] 分成处理成功', [
                'transaction_id' => $transactionId,
                'record_id' => $record['id'],
                'user_id' => $record['user_id'],
                'sharer_id' => $sharer['user_id'],
                'share_amount' => $amounts['share_amount'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            return true;

        } catch (Exception $e) {
            Db::rollback();

            // 🔒 记录失败日志
            Log::error('[安全分成] 分成事务失败', [
                'transaction_id' => $transactionId,
                'record_id' => $record['id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            // 🔒 失败补偿机制
            self::scheduleRetry($record, $transactionId);

            return false;
        }
    }

    /**
     * 🔒 安全修复：数据完整性签名
     * @param array $data 数据
     * @return string
     * @security_fix 2025/08/02 - 数据完整性保护
     */
    private static function generateDataSignature(array $data): string
    {
        // 排除签名字段本身
        unset($data['data_signature']);

        // 按键排序确保一致性
        ksort($data);

        // 生成签名
        $dataString = json_encode($data, JSON_UNESCAPED_UNICODE);
        return hash_hmac('sha256', $dataString, self::getIntegrityKey());
    }

    /**
     * 🔒 安全修复：验证数据完整性
     * @param array $data 数据
     * @param string $signature 签名
     * @return bool
     * @security_fix 2025/08/02 - 数据完整性验证
     */
    public static function verifyDataSignature(array $data, string $signature): bool
    {
        $expectedSignature = self::generateDataSignature($data);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * 🔒 安全修复：失败重试调度
     * @param array $record 对话记录
     * @param string $transactionId 事务ID
     * @security_fix 2025/08/02 - 失败补偿机制
     */
    private static function scheduleRetry(array $record, string $transactionId): void
    {
        try {
            $retryData = [
                'record_id' => $record['id'],
                'transaction_id' => $transactionId,
                'retry_count' => 0,
                'max_retries' => self::MAX_RETRY_ATTEMPTS,
                'next_retry_time' => time() + 300, // 5分钟后重试
                'create_time' => time()
            ];

            // 存储到重试队列（可以是数据库表或Redis）
            Cache::set("revenue_retry:{$transactionId}", $retryData, 86400);

            Log::info('[安全分成] 已调度重试', [
                'transaction_id' => $transactionId,
                'record_id' => $record['id'],
                'next_retry_time' => date('Y-m-d H:i:s', $retryData['next_retry_time'])
            ]);

        } catch (Exception $e) {
            Log::error('[安全分成] 调度重试失败', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取数据完整性密钥
     */
    private static function getIntegrityKey(): string
    {
        if (self::$integrityKey === null) {
            // 从配置或环境变量获取密钥
            self::$integrityKey = config('app.app_key', 'default_revenue_key');
        }
        return self::$integrityKey;
    }

    /**
     * 🔒 安全修复：重试失败的分成
     * @param string $transactionId 事务ID
     * @return bool
     * @security_fix 2025/08/02 - 失败重试机制
     */
    public static function retryFailedRevenue(string $transactionId): bool
    {
        try {
            $retryData = Cache::get("revenue_retry:{$transactionId}");
            if (!$retryData) {
                return false;
            }

            // 检查重试次数
            if ($retryData['retry_count'] >= $retryData['max_retries']) {
                Log::warning('[安全分成] 重试次数已达上限', [
                    'transaction_id' => $transactionId,
                    'retry_count' => $retryData['retry_count']
                ]);
                Cache::delete("revenue_retry:{$transactionId}");
                return false;
            }

            // 检查重试时间
            if (time() < $retryData['next_retry_time']) {
                return false;
            }

            // 获取原始记录
            $record = KbRobotRecord::where('id', $retryData['record_id'])->find();
            if (!$record) {
                Cache::delete("revenue_retry:{$transactionId}");
                return false;
            }

            // 更新重试信息
            $retryData['retry_count']++;
            $retryData['next_retry_time'] = time() + (300 * $retryData['retry_count']); // 递增延迟
            Cache::set("revenue_retry:{$transactionId}", $retryData, 86400);

            // 重新处理
            return self::processRecord($record->toArray());

        } catch (Exception $e) {
            Log::error('[安全分成] 重试处理异常', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
