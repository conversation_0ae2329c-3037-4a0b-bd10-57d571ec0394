<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\service;

use app\common\enum\user\AccountLogEnum;
use app\common\model\kb\KbRobotRevenueConfig;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\model\kb\KbRobotSquare;
use app\common\model\user\User;
use app\common\model\user\UserAccountLog;
use app\common\exception\RobotRevenueException;
use think\facade\Db;
use think\facade\Log;
use think\db\exception\PDOException;
use think\db\exception\DbException;

/**
 * 智能体分成收益服务类
 */
class RobotRevenueService
{
    /**
     * @notes 处理智能体使用的分成收益
     * @param array $params
     * @return bool
     * @throws RobotRevenueException
     */
    public static function processRevenue(array $params): bool
    {
        // 参数类型安全转换和验证
        $userId = isset($params['user_id']) ? (int)$params['user_id'] : 0;
        $robotId = isset($params['robot_id']) ? (int)$params['robot_id'] : 0;
        $squareId = isset($params['square_id']) ? (int)$params['square_id'] : 0;
        $recordId = isset($params['record_id']) ? (int)$params['record_id'] : 0;
        $totalCost = isset($params['total_cost']) ? (float)$params['total_cost'] : 0.0;

        // 严格的参数验证
        if ($userId <= 0 || $robotId <= 0 || $squareId <= 0 || $recordId <= 0 || $totalCost <= 0) {
            throw RobotRevenueException::invalidParams(
                "无效参数 - 用户ID:{$userId}, 智能体ID:{$robotId}, 广场ID:{$squareId}, 记录ID:{$recordId}, 金额:{$totalCost}"
            );
        }

        try {
            Db::startTrans();

            // 获取分成配置并验证
            $config = self::getValidatedConfig();

            // 验证最小分成金额
            if ($totalCost < $config['min_revenue']) {
                throw RobotRevenueException::insufficientAmount($totalCost, $config['min_revenue']);
            }

            // 验证记录是否已处理过分成
            self::validateRecordNotProcessed($recordId);

            // 获取并验证广场信息
            $square = self::getValidatedSquare($squareId, $robotId);
            $sharerId = $square['user_id'];

            // 防止自己给自己分成
            if ($userId == $sharerId) {
                throw RobotRevenueException::selfRevenue($userId);
            }

            // 验证用户存在性
            self::validateUserExists($userId);
            self::validateUserExists($sharerId);

            // 计算分成金额（使用更安全的计算方式）
            $shareAmount = self::calculateSafeAmount($totalCost, $config['share_ratio']);
            $platformAmount = self::calculateSafeAmount($totalCost, $config['platform_ratio']);

            // 验证计算结果的合理性
            if ($shareAmount + $platformAmount > $totalCost * 1.01) { // 允许1%的浮点误差
                $totalShare = $shareAmount + $platformAmount;
                throw RobotRevenueException::calculationError(
                    "分成计算异常 - 总分成:{$totalShare} > 总消费:{$totalCost}"
                );
            }

            // 创建分成记录
            $revenueLog = self::createRevenueLog([
                'user_id' => $userId,
                'sharer_id' => $sharerId,
                'robot_id' => $robotId,
                'square_id' => $squareId,
                'record_id' => $recordId,
                'total_cost' => $totalCost,
                'share_amount' => $shareAmount,
                'platform_amount' => $platformAmount,
                'share_ratio' => $config['share_ratio'],
                'settle_status' => $config['settle_type'] == 1 ? 1 : 0,
                'settle_time' => $config['settle_type'] == 1 ? time() : null
            ]);

            // 实时结算处理
            if ($config['settle_type'] == 1) {
                self::processRealTimeSettlement($sharerId, $shareAmount, $robotId);
            }

            // 更新统计信息
            self::updateStatistics($squareId, $shareAmount, $recordId, $revenueLog['id']);

            Db::commit();
            
            // 记录成功日志（脱敏处理）
            Log::info('智能体分成收益处理成功', [
                'user_id' => $userId,
                'robot_id' => $robotId,
                'square_id' => $squareId,
                'record_id' => $recordId,
                'share_amount' => $shareAmount,
                'revenue_log_id' => $revenueLog['id'],
                'settle_type' => $config['settle_type']
            ]);

            return true;

        } catch (RobotRevenueException $e) {
            Db::rollback();
            // 业务异常直接抛出，不记录详细日志
            throw $e;
        } catch (PDOException $e) {
            Db::rollback();
            throw RobotRevenueException::databaseError('数据库PDO异常', $e);
        } catch (DbException $e) {
            Db::rollback();
            throw RobotRevenueException::databaseError('数据库操作异常', $e);
        } catch (\Throwable $e) {
            Db::rollback();
            // 记录未预期的异常
            Log::error('智能体分成收益处理出现未预期异常', [
                'exception_class' => get_class($e),
                'exception_message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'params_hash' => md5(json_encode($params)) // 参数hash而不是原始参数
            ]);
            throw RobotRevenueException::databaseError('系统异常，请稍后重试', $e);
        }
    }

    /**
     * @notes 批量结算分成收益（用于定时任务）
     * @param int $batchSize 每批处理数量
     * @return array 返回结算统计信息
     */
    public static function batchSettle(int $batchSize = 100): array
    {
        $totalProcessed = 0;
        $totalAmount = 0;
        $totalSuccess = 0;
        $totalFailed = 0;
        $errors = [];
        $batchCount = 0;
        
        try {
            Log::info('开始执行智能体分成收益批量结算', [
                'batch_size' => $batchSize,
                'start_time' => date('Y-m-d H:i:s')
            ]);
            
            // 检查是否有待结算记录
            $pendingCount = KbRobotRevenueLog::where(['settle_status' => 0])->count();
            if ($pendingCount == 0) {
                Log::info('没有待结算记录，跳过执行');
                return [
                    'success' => true,
                    'total_processed' => 0,
                    'total_success' => 0,
                    'total_failed' => 0,
                    'total_amount' => 0,
                    'batch_count' => 0,
                    'errors' => [],
                    'message' => '没有待结算记录'
                ];
            }
            
            Log::info("发现 {$pendingCount} 条待结算记录，开始分批处理");
            
            do {
                $batchCount++;
                
                // 分批获取待结算记录
                $pendingLogs = KbRobotRevenueLog::where(['settle_status' => 0])
                    ->limit($batchSize)
                    ->select();

                if ($pendingLogs->isEmpty()) {
                    break;
                }

                $currentBatchCount = $pendingLogs->count();
                $batchAmount = 0;
                $batchSuccess = 0;
                $batchFailed = 0;
                
                Log::info("处理第 {$batchCount} 批数据，共 {$currentBatchCount} 条记录");

                // 开启事务处理当前批次
                Db::startTrans();
                
                try {
                    $successIds = [];
                    
                    foreach ($pendingLogs as $log) {
                        try {
                            // 验证数据完整性
                            if (empty($log['sharer_id']) || empty($log['share_amount']) || $log['share_amount'] <= 0) {
                                $errors[] = "记录ID {$log['id']} 数据不完整，跳过处理";
                                $batchFailed++;
                                continue;
                            }
                            
                            // 检查分享者是否存在
                            $sharerExists = User::where(['id' => $log['sharer_id']])->count();
                            if (!$sharerExists) {
                                $errors[] = "记录ID {$log['id']} 分享者用户 {$log['sharer_id']} 不存在";
                                $batchFailed++;
                                continue;
                            }
                            
                            // 给分享者增加余额
                            $updateResult = User::where(['id' => $log['sharer_id']])->inc('balance', $log['share_amount']);
                            
                            if ($updateResult === false) {
                                throw new Exception("更新用户 {$log['sharer_id']} 余额失败");
                            }
                            
                            // 记录分享者收益流水
                            UserAccountLog::add(
                                $log['sharer_id'],
                                AccountLogEnum::UM_INC_ROBOT_REVENUE,
                                AccountLogEnum::INC,
                                $log['share_amount'],
                                0,
                                '智能体分成收益批量结算，来源智能体ID：' . $log['robot_id']
                            );

                            // 更新广场统计信息
                            KbRobotSquare::where(['id' => $log['square_id']])
                                ->inc('total_revenue', $log['share_amount'])
                                ->inc('use_count', 1);
                            
                            $successIds[] = $log['id'];
                            $batchAmount += $log['share_amount'];
                            $batchSuccess++;
                            
                        } catch (Exception $e) {
                            $errors[] = "处理记录ID {$log['id']} 失败: " . $e->getMessage();
                            $batchFailed++;
                            Log::error("批量结算单条记录失败", [
                                'record_id' => $log['id'],
                                'error' => $e->getMessage()
                            ]);
                        }
                    }

                    // 批量更新成功的记录状态
                    if (!empty($successIds)) {
                        KbRobotRevenueLog::batchSettle($successIds);
                    }

                    Db::commit();
                    
                    $totalProcessed += $currentBatchCount;
                    $totalAmount += $batchAmount;
                    $totalSuccess += $batchSuccess;
                    $totalFailed += $batchFailed;
                    
                    Log::info("第 {$batchCount} 批处理完成", [
                        'batch_count' => $currentBatchCount,
                        'batch_success' => $batchSuccess,
                        'batch_failed' => $batchFailed,
                        'batch_amount' => $batchAmount
                    ]);
                    
                } catch (Exception $e) {
                    Db::rollback();
                    $errors[] = "批次 {$batchCount} 处理失败: " . $e->getMessage();
                    $totalFailed += $currentBatchCount;
                    Log::error("批量结算批次失败", [
                        'batch_number' => $batchCount,
                        'batch_count' => $currentBatchCount,
                        'error' => $e->getMessage()
                    ]);
                    break;
                }
                
                // 避免内存占用过多，大批量时稍作休息
                if ($totalProcessed >= $batchSize * 10) {
                    usleep(100000); // 休息0.1秒
                }
                
            } while (!$pendingLogs->isEmpty());
            
            // 记录最终统计
            $result = [
                'success' => $totalFailed == 0,
                'total_processed' => $totalProcessed,
                'total_success' => $totalSuccess,
                'total_failed' => $totalFailed,
                'total_amount' => $totalAmount,
                'batch_count' => $batchCount,
                'errors' => $errors,
                'message' => $totalFailed == 0 ? '批量结算完全成功' : '批量结算部分成功'
            ];
            
            Log::info('智能体分成收益批量结算完成', [
                'result' => $result,
                'end_time' => date('Y-m-d H:i:s')
            ]);
            
            return $result;

        } catch (Exception $e) {
            Log::error('智能体分成收益批量结算异常', [
                'error' => $e->getMessage(),
                'total_processed' => $totalProcessed,
                'batch_count' => $batchCount
            ]);
            
            return [
                'success' => false,
                'total_processed' => $totalProcessed,
                'total_success' => $totalSuccess,
                'total_failed' => $totalFailed,
                'total_amount' => $totalAmount,
                'batch_count' => $batchCount,
                'errors' => array_merge($errors, [$e->getMessage()]),
                'message' => '批量结算执行异常'
            ];
        }
    }

    /**
     * @notes 获取分成配置
     * @return array
     */
    public static function getConfig(): array
    {
        return KbRobotRevenueConfig::getConfig();
    }

    /**
     * @notes 更新分成配置
     * @param array $data
     * @return bool
     */
    public static function updateConfig(array $data): bool
    {
        // 验证配置数据
        if (isset($data['share_ratio'])) {
            if ($data['share_ratio'] < 0 || $data['share_ratio'] > 100) {
                throw new Exception('分成比例必须在0-100之间');
            }
        }

        if (isset($data['min_revenue'])) {
            if ($data['min_revenue'] < 0) {
                throw new Exception('最小分成金额不能小于0');
            }
        }

        return KbRobotRevenueConfig::updateConfig($data);
    }

    /**
     * @notes 检查智能体分成收益相关表是否存在
     * @return bool
     */
    public static function checkTablesExist(): bool
    {
        try {
            // 直接检查配置是否启用
            $config = KbRobotRevenueConfig::getConfig();
            return !empty($config) && $config['is_enable'];
            
        } catch (\Throwable $e) {
            Log::error('检查智能体分成收益表状态失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * 获取并验证配置
     */
    private static function getValidatedConfig(): array
    {
        $config = self::getConfig();
        if (empty($config) || !$config['is_enable']) {
            throw RobotRevenueException::configDisabled();
        }
        return $config;
    }

    /**
     * 验证记录未被处理过
     */
    private static function validateRecordNotProcessed(int $recordId): void
    {
        // 使用模型查询而不是原生SQL，更安全
        $processed = \app\common\model\kb\KbRobotRecord::where([
            ['id', '=', $recordId],
            ['is_revenue_shared', '=', 1]
        ])->count();
        
        if ($processed > 0) {
            throw RobotRevenueException::duplicateProcess($recordId);
        }
    }

    /**
     * 获取并验证广场信息
     */
    private static function getValidatedSquare(int $squareId, int $robotId): array
    {
        $square = KbRobotSquare::where(['id' => $squareId, 'robot_id' => $robotId])
            ->field('id,user_id,robot_id')
            ->find();
        
        if (!$square) {
            throw RobotRevenueException::squareNotExists($squareId);
        }
        
        return $square->toArray();
    }

    /**
     * 验证用户存在
     */
    private static function validateUserExists(int $userId): void
    {
        $exists = User::where(['id' => $userId])->count();
        if (!$exists) {
            throw RobotRevenueException::userNotExists($userId);
        }
    }

    /**
     * 安全计算金额（避免浮点精度问题）
     */
    private static function calculateSafeAmount(float $totalAmount, float $ratio): float
    {
        // 使用整数计算避免浮点精度问题
        $cents = (int)round($totalAmount * 100);
        $shareRatio = (int)round($ratio);
        $shareCents = (int)round(($cents * $shareRatio) / 100);
        
        return $shareCents / 100;
    }

    /**
     * 创建分成日志记录
     */
    private static function createRevenueLog(array $data): array
    {
        try {
            $revenueLog = KbRobotRevenueLog::createRevenue($data);
            return $revenueLog->toArray();
        } catch (\Throwable $e) {
            throw RobotRevenueException::databaseError('创建分成记录失败', $e);
        }
    }

    /**
     * 处理实时结算
     */
    private static function processRealTimeSettlement(int $sharerId, float $shareAmount, int $robotId): void
    {
        try {
            // 给分享者增加余额
            $updateResult = User::where(['id' => $sharerId])->inc('balance', $shareAmount);
            if ($updateResult === false) {
                throw RobotRevenueException::databaseError("更新用户 {$sharerId} 余额失败");
            }
            
            // 记录分享者收益流水
            UserAccountLog::add(
                $sharerId,
                AccountLogEnum::UM_INC_ROBOT_REVENUE,
                AccountLogEnum::INC,
                $shareAmount,
                0,
                '智能体分成收益，来源智能体ID：' . $robotId
            );
        } catch (RobotRevenueException $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw RobotRevenueException::databaseError('实时结算处理失败', $e);
        }
    }

    /**
     * 更新统计信息
     */
    private static function updateStatistics(int $squareId, float $shareAmount, int $recordId, int $revenueLogId): void
    {
        try {
            // 更新广场统计
            KbRobotSquare::where('id', $squareId)
                ->inc('total_revenue', $shareAmount)
                ->inc('use_count', 1);

            // 使用模型更新对话记录标记，更安全
            \app\common\model\kb\KbRobotRecord::where('id', $recordId)
                ->update([
                    'is_revenue_shared' => 1,
                    'revenue_log_id' => $revenueLogId
                ]);
        } catch (\Throwable $e) {
            throw RobotRevenueException::databaseError('更新统计信息失败', $e);
        }
    }
} 