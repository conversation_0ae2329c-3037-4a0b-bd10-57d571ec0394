<?php

namespace app\common\service\ai;

use app\common\cache\KeyPoolCache;
use app\common\enum\ChatEnum;
use app\common\model\chat\Models;
use Exception;

class RankerService
{
    protected array $headers = [];
    protected string $baseUrl = '';
    protected string $apikey = '';
    protected string $model = '';

    /**
     * @notes 初始化
     * @param $models
     * @throws Exception
     */
    public function __construct($models)
    {
        $this->headers = [
            'Authorization: Bearer ',
            'Content-Type: application/json'
        ];

        $rankerModel = Models::checkModels($models);
        $this->model = $rankerModel['model'];
        $this->baseUrl = rtrim($rankerModel['base_url'], '/') . '/v1/rerank';

        $keyCache = new KeyPoolCache();
        $keyInfo = $keyCache->getKey(ChatEnum::MODEL_TYPE_RANKING, $rankerModel['channel']);
        if (empty($keyInfo)) {
            throw new Exception('重排模型密钥池为空');
        }
        $this->apikey = $keyInfo['key'];
        $this->headers[0] .= $this->apikey;
    }

    /**
     * @notes 发送重排请求并自动过滤
     * @param string $query 查询问题
     * @param array $documents 文档列表
     * @param float $threshold 分数阈值
     * @return array
     * @throws Exception
     */
    public function sendAuto(string $query, array $documents, float $threshold = 0.5): array
    {
        if (empty($documents)) {
            return [];
        }

        // 准备文档内容
        $docTexts = [];
        foreach ($documents as $doc) {
            // 组合问题和答案作为文档内容
            $content = '';
            if (!empty($doc['question'])) {
                $content .= $doc['question'];
            }
            if (!empty($doc['answer'])) {
                $content .= ' ' . $doc['answer'];
            }
            $docTexts[] = $content;
        }

        $result = $this->send($query, $docTexts);
        
        if (empty($result['results'])) {
            return [];
        }

        // 按分数过滤和重排序
        $filteredResults = [];
        foreach ($result['results'] as $item) {
            if ($item['relevance_score'] >= $threshold) {
                $index = $item['index'];
                if (isset($documents[$index])) {
                    $doc = $documents[$index];
                    $doc['relevance_score'] = $item['relevance_score'];
                    $filteredResults[] = $doc;
                }
            }
        }

        // 按相关性分数降序排列
        usort($filteredResults, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });

        return $filteredResults;
    }

    /**
     * @notes 发送重排请求
     * @param string $query 查询文本
     * @param array $documents 文档列表
     * @param int $topK 返回前K个结果
     * @return array
     * @throws Exception
     */
    public function send(string $query, array $documents, int $topK = 10): array
    {
        if (empty($query) || empty($documents)) {
            throw new Exception('查询文本和文档列表不能为空');
        }

        $data = [
            'model' => $this->model,
            'query' => $query,
            'documents' => $documents,
            'top_k' => min($topK, count($documents))
        ];

        $response = $this->curl($data);
        
        if (empty($response)) {
            throw new Exception('重排服务响应为空');
        }

        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('重排服务响应格式错误: ' . json_last_error_msg());
        }

        if (isset($result['error'])) {
            throw new Exception('重排服务错误: ' . $result['error']['message']);
        }

        return $result;
    }

    /**
     * @notes 发送HTTP请求
     * @param array $data
     * @return string
     * @throws Exception
     */
    protected function curl(array $data): string
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->headers,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('重排服务请求失败: ' . $error);
        }

        if ($httpCode !== 200) {
            throw new Exception('重排服务HTTP错误: ' . $httpCode);
        }

        return $response;
    }

    /**
     * @notes 批量重排
     * @param array $queries 查询列表
     * @param array $documentsLists 文档列表的列表
     * @return array
     */
    public function batchRerank(array $queries, array $documentsLists): array
    {
        $results = [];
        foreach ($queries as $index => $query) {
            $documents = $documentsLists[$index] ?? [];
            try {
                $results[$index] = $this->sendAuto($query, $documents);
            } catch (Exception $e) {
                $results[$index] = [];
                // 记录错误日志
                trace($e->getMessage(), 'error');
            }
        }
        return $results;
    }
} 