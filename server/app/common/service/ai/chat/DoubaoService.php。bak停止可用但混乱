<?php

namespace app\common\service\ai\chat;

use app\common\cache\KeyPoolCache;
use app\common\enum\ChatEnum;
use app\common\service\ai\ChatService;
use Exception;
use think\facade\Log;
use WpOrg\Requests\Requests;

class DoubaoService
{
    protected array $config            = [];                        // 配置参数
    protected string $channel          = 'doubao';                  // 渠道模型
    protected string $model            = '';                        // 对话模型
    protected string $apiKey           = '';                        // 接口密钥
    protected string $baseUrl          = '';                        // 请求地址
    protected bool $outputStream       = true;                      // 流式输出

    protected int $contextNum          = 0;                         // 上下文数
    protected float $temperature       = 0;                         // 词汇属性
    protected float $frequencyPenalty  = 0;                         // 重复属性
    protected array $messages          = [];                        // 上下文内容

    protected array $headers           = [];                        // 请求头值
    protected string $reasoning        = '';                        // 思考的过程
    protected array $content           = [];                        // 回复的内容
    protected array $usage             = [];                        // 使用Token

    protected mixed $keyPoolServer = null;                          // Key池对象

    /**
     * @notes 初始化
     * @param array $chatConfig
     * @throws Exception
     */
    public function __construct(array $chatConfig)
    {
        // 获取当前模型的渠道
        $this->config  = $chatConfig;
        $this->channel = $chatConfig['channel'];

        // 是否流式输出 (SSE有效)
        $this->outputStream = ($chatConfig['outputStream'] ?? true);

        // 设置基础参数
        // 获取实际的API模型名称
        $this->model = $this->config['api_model'] ?? $this->config['model'] ?? '';
        
        // 如果没有api_model配置，使用默认映射
        if (empty($this->config['api_model'])) {
            $this->model = $this->mapDisplayNameToApiModel($this->config['model'] ?? '');
        }
        $this->contextNum       = (int) ($this->config['context_num']??0);
        $this->temperature      = (float) ($this->config['temperature']??0.9);
        $this->frequencyPenalty = (float) $this->config['frequency_penalty']??0;

        // 获取密钥Key
        $this->keyPoolServer = (new KeyPoolCache($chatConfig['model_id'], ChatEnum::MODEL_TYPE_CHAT));
        $this->apiKey = $this->keyPoolServer->getKey();
        if (isset($chatConfig['check_key']) and $chatConfig['check_key']) {
            if (empty($this->apiKey)) {
                throw new Exception('请在后台配置key');
            }
        }

        // 替换代理域名
        $this->baseUrl = 'https://ark.cn-beijing.volces.com/api/v3';
        if ($this->config['agency_api'] ?? '') {
            $this->baseUrl = $this->config['agency_api'];
        }

        // 设置请求头值
        $this->headers['Content-Type']  = 'application/json';
        $this->headers['Authorization'] = 'Bearer ' . trim($this->apiKey);
    }



    /**
     * @notes HTTP对话请求
     * @param array $messages
     * @return array
     * @throws Exception
     * <AUTHOR>
     */
    public function chatHttpRequest(array $messages): array
    {
        $this->messages = $messages;
        
        // 记录调试日志
        \think\facade\Log::write("豆包chatHttpRequest开始 - 模型: {$this->model}, HTTP同步模式");
        
        // 检测是否为Bot模型（多种检测方式：bot-前缀、联网模式关键词等）
        $isBotModel = str_starts_with($this->model, 'bot-') || 
                     str_contains($this->model, '联网模式') || 
                     str_contains($this->model, '联网') ||
                     str_contains(strtolower($this->model), 'bot');
        
        // 构建API URL
        $url = $isBotModel ? 
            $this->baseUrl . '/bots/chat/completions' :
            $this->baseUrl . '/chat/completions';
        
        // 构建请求数据
        $data = [
            'model'             => $this->model,
            'stream'            => false,
            'messages'          => $messages,
            'temperature'       => $this->temperature,
            'frequency_penalty' => $this->frequencyPenalty
        ];
        
        // Bot模型需要额外的参数
        if ($isBotModel) {
            $data['stream_options'] = ['include_usage' => true];
        }

        // 设置超时时间 - Bot模型可能需要更长时间进行联网检索
        $timeout = $isBotModel ? 600 : 300;
        $options['timeout'] = $timeout;
        
        \think\facade\Log::write("豆包HTTP请求参数: " . json_encode($data, JSON_UNESCAPED_UNICODE));
        
        $response = Requests::post($url, $this->headers, json_encode($data), $options);
        return $this->parseResponseData($response);
    }

    /**
     * @notes SSE对话请求
     * @param array $messages
     * @return DoubaoService
     * @throws Exception
     * <AUTHOR>
     */
    public function chatSseRequest(array $messages): self
    {
        ignore_user_abort(true);
        $this->messages = $messages;
        
        // 记录调试日志
        \think\facade\Log::write("豆包chatSseRequest开始 - 模型: {$this->model}, SSE流式模式");
        
        // 检测是否为Bot模型（多种检测方式：bot-前缀、联网模式关键词等）
        $isBotModel = str_starts_with($this->model, 'bot-') || 
                     str_contains($this->model, '联网模式') || 
                     str_contains($this->model, '联网') ||
                     str_contains(strtolower($this->model), 'bot');
        
        // 构建API URL
        $url = $isBotModel ? 
            $this->baseUrl . '/bots/chat/completions' :
            $this->baseUrl . '/chat/completions';
        
        // 构建请求数据
        $data = [
            'model'             => $this->model,
            'stream'            => true,
            'messages'          => $messages,
            'temperature'       => $this->temperature,
            'frequency_penalty' => $this->frequencyPenalty
        ];
        
        // Bot模型需要额外的参数
        if ($isBotModel) {
            $data['stream_options'] = ['include_usage' => true];
        }

        $response = false;
        $callback = function ($ch, $data) use (&$response){
            $result = @json_decode($data);
            
            // 如果不是第一次，直接处理数据
            if (false !== $response) {
                $this->parseStreamData($data);
                
                // 关键修复：添加连接状态检查，与OpenAI保持一致
                if (!connection_aborted()) {
                    return strlen($data);
                } else {
                    \think\facade\Log::write("豆包SSE检测到连接中断，但数据已累积");
                    return 1; // 即使连接断开，也返回1继续处理
                }
            }

            // 第一次流执行的流程 - 错误处理
            if (isset($result->error)) {
                $error = $this->keyPoolServer->takeDownKey($result->error->message, $this->baseUrl);
                $response = 'doubao:'.$result->error->message ? $error : $result->error->type;
                return 1;
            }

            // 处理流数据并设置状态为true
            $this->parseStreamData($data);
            $response = true;
            return strlen($data);
        };

        $headers = [];
        foreach ($this->headers as $key => $item) {
            $headers[] = $key . ': ' . trim($item);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, $isBotModel ? 600 : 301); // Bot模型更长超时
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);
        
        \think\facade\Log::write("豆包SSE请求参数: " . json_encode($data, JSON_UNESCAPED_UNICODE));
        
        curl_exec($ch);
        curl_close($ch);

        // 记录SSE完成状态
        \think\facade\Log::write("豆包SSE完成 - 回复长度:" . mb_strlen($this->content[0] ?? '') . ", 推理长度:" . mb_strlen($this->reasoning));

        if(true !== $response){
            if ($response === false) {
                throw new Exception('Doubao: 请求出错!');
            } else {
                $error = $this->keyPoolServer->takeDownKey($response, $this->baseUrl);
                throw new Exception($error);
            }
        }
        return $this;
    }
    
    /**
     * @notes 设置紧急保存数据（SSE模式下保持兼容，但实际上SSE数据已在实时累积）
     * @param array $saveData
     * <AUTHOR>
     */
    public function setEmergencySaveData(array $saveData): void
    {
        // SSE模式下数据会在parseStreamData中实时累积到$this->content和$this->reasoning
        // 这个方法保持兼容性，但实际的数据保存依赖于SSE的实时累积机制
        \think\facade\Log::write("豆包SSE模式：数据将通过parseStreamData实时累积");
    }



    /**
     * @notes 获取回复内容
     * @param string $type
     * @return array|string
     * <AUTHOR>
     */
    public function getReplyContent(string $type = 'content'): array|string
    {
        // 思考过程
        if ($type == 'reasoning') {
            return $this->reasoning;
        }
        // 答复内容
        return $this->content;
    }

    /**
     * 获取消耗的tokens
     * <AUTHOR>
     */
    public function getUsage(): array
    {
        $promptContent = '';
        foreach ($this->messages as $item) {
            if (is_array($item['content'])) {
                $promptContent .= $item['content'][0]['text'];
            } else {
                $promptContent .= $item['content'];
            }
            //$promptContent .= "\n\n";
            //$promptContent .= "\n";
        }

        $reply = trim($this->reasoning) . trim($this->content[0]);
        if (!$this->usage) {
            $promptTokens     = gpt_tokenizer_count($promptContent);
            $completionTokens = gpt_tokenizer_count($reply);
            return [
                'prompt_tokens'     => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens'      => $promptTokens + $completionTokens,
                'str_length'        => mb_strlen($promptContent . $reply)
            ] ?? [];
        } else {
            $this->usage['str_length'] = mb_strlen($promptContent . trim($reply));
            return $this->usage;
        }
    }

    /**
     * @notes 解析HTTP数据
     * @param mixed $response
     * @return array
     * @throws Exception
     * <AUTHOR>
     */
    private function parseResponseData(mixed $response): array
    {
        $responseData = json_decode($response->body, true);
        
        \think\facade\Log::write("豆包HTTP响应解析: " . substr($response->body, 0, 500) . "...");
        
        if (isset($responseData['error'])) {
            $message = $responseData['error']['message'];
            $param   = $responseData['error']['param'];
            $type    = $responseData['error']['type'];
            $code    = $responseData['error']['code'];
            $error = $this->keyPoolServer->takeDownKey($message, $this->baseUrl);
            return ChatService::parseReturnError(false, $error, $code, $this->model, $type, $param);
        }

        // 保存token使用信息
        $this->usage = $responseData['usage'] ?? [];
        
        $id = $responseData['id'] ?? '';
        $choice = $responseData['choices'][0] ?? [];
        $message = $choice['message'] ?? [];
        
        // 检测是否为Bot模型（多种检测方式：bot-前缀、联网模式关键词等）
        $isBotModel = str_starts_with($this->model, 'bot-') || 
                     str_contains($this->model, '联网模式') || 
                     str_contains($this->model, '联网') ||
                     str_contains(strtolower($this->model), 'bot');
        
        // Bot模型特殊处理：搜索结果
        if ($isBotModel && isset($message['search_results'])) {
            $searchResults = $message['search_results'];
            \think\facade\Log::write("豆包Bot模型搜索结果: " . json_encode($searchResults, JSON_UNESCAPED_UNICODE));
            
            // 格式化搜索结果为可读文本
            $searchText = "【搜索结果】\n";
            if (is_array($searchResults)) {
                foreach ($searchResults as $result) {
                    $title = $result['title'] ?? '无标题';
                    $url = $result['url'] ?? '';
                    $snippet = $result['snippet'] ?? '';
                    $searchText .= "• {$title}\n";
                    if (!empty($url)) {
                        $searchText .= "  链接: {$url}\n";
                    }
                    if (!empty($snippet)) {
                        $searchText .= "  摘要: {$snippet}\n";
                    }
                    $searchText .= "\n";
                }
            }
            
            // 发送搜索结果到前端
            ChatService::parseReturnSuccess(
                'search',
                $id,
                $searchText,
                0,
                $this->model,
                null,
                $this->outputStream
            );
        }
        
        // Bot模型特殊处理：工具调用
        if ($isBotModel && isset($message['tool_calls'])) {
            $toolCalls = $message['tool_calls'];
            \think\facade\Log::write("豆包Bot模型工具调用: " . json_encode($toolCalls, JSON_UNESCAPED_UNICODE));
            
            // 格式化工具调用为可读文本
            $toolText = "【工具调用】\n";
            if (is_array($toolCalls)) {
                foreach ($toolCalls as $call) {
                    $type = $call['type'] ?? '未知';
                    $function = $call['function'] ?? [];
                    $name = $function['name'] ?? '';
                    $arguments = $function['arguments'] ?? '';
                    
                    $toolText .= "• 工具类型: {$type}\n";
                    if (!empty($name)) {
                        $toolText .= "  函数名: {$name}\n";
                    }
                    if (!empty($arguments)) {
                        $toolText .= "  参数: {$arguments}\n";
                    }
                    $toolText .= "\n";
                }
            }
            
            // 发送工具调用信息到前端
            ChatService::parseReturnSuccess(
                'tool_calls',
                $id,
                $toolText,
                0,
                $this->model,
                null,
                $this->outputStream
            );
        }
        
        // 解析主要回复内容
        $mainContent = $message['content'] ?? '';
        $this->content = [$mainContent];
        
        // 解析推理内容（reasoning）- 针对推理模型如R1
        $reasoning = '';
        if (isset($message['reasoning'])) {
            $reasoning = $message['reasoning'];
        }
        // 某些模型可能使用不同的字段名
        elseif (isset($message['reasoning_content'])) {
            $reasoning = $message['reasoning_content'];
        }
        // 检查是否在其他位置
        elseif (isset($responseData['reasoning'])) {
            $reasoning = $responseData['reasoning'];
        }
        
        $this->reasoning = $reasoning;
        
        // 记录解析结果
        \think\facade\Log::write("豆包HTTP内容解析完成 - 回复长度:" . mb_strlen($mainContent) . ", 推理长度:" . mb_strlen($reasoning) . ", Bot模型:" . ($isBotModel ? '是' : '否'));
        
        // 如果有推理内容，也要发送到前端
        if (!empty($reasoning)) {
            // 先发送推理内容
            ChatService::parseReturnSuccess(
                'reasoning',
                $id,
                $reasoning,
                0,
                $this->model,
                null,
                $this->outputStream
            );
        }
        
        // 发送回复内容
        if (!empty($mainContent)) {
            ChatService::parseReturnSuccess(
                'chat',
                $id,
                $mainContent,
                0,
                $this->model,
                null,
                $this->outputStream
            );
        }
        
        // 最后发送完成信号
        ChatService::parseReturnSuccess(
            'finish',
            $id,
            '',
            0,
            $this->model,
            'stop',
            $this->outputStream
        );
        
        return $responseData;
    }

    /**
     * @notes 解析SSE数据
     * @param $stream
     * <AUTHOR>
     */
    private function parseStreamData($stream): void
    {
        $chatEvent = 'chat';
        $dataLists = explode("\n\n", $stream);
        foreach ($dataLists as $data){
            if(!str_contains($data, 'data:')){
                continue;
            }
            
            if(str_contains($data, 'data: [DONE]') || str_contains($data, 'data:[DONE]')){
                continue;
            }
            
            // 关键修复：豆包API返回的是data:格式（无空格），需要同时处理两种格式
            $data = str_replace(["data:", "data: "], "", $data);
            $data = trim($data);
            $data = json_decode($data, true);
            
            // 解析到数据是空的、可能是数据丢失问题
            if (empty($data) || !is_array($data)) {
                Log::write('豆包模型数据异常:'.$stream);
                continue;
            }

            $id            = $data['id'] ?? '';
            $index         = (int) ($data['choices'][0]['index'] ?? 0);
            $delta         = $data['choices'][0]['delta'] ?? [];
            $streamContent = $delta['content'] ?? '';
            $finishReason  = $data['choices'][0]['finish_reason'] ?? '';
            
            // 检测是否为Bot模型（多种检测方式：bot-前缀、联网模式关键词等）
            $isBotModel = str_starts_with($this->model, 'bot-') || 
                         str_contains($this->model, '联网模式') || 
                         str_contains($this->model, '联网') ||
                         str_contains(strtolower($this->model), 'bot');
            
            // 标记是否处理了特殊内容
            $hasSpecialContent = false;
            
            // Bot模型特殊处理：搜索结果
            if ($isBotModel && isset($delta['search_results'])) {
                $searchResults = $delta['search_results'];
                \think\facade\Log::write("豆包Bot模型SSE搜索结果: " . json_encode($searchResults, JSON_UNESCAPED_UNICODE));
                
                // 格式化搜索结果为可读文本
                $searchText = "【搜索结果】\n";
                if (is_array($searchResults)) {
                    foreach ($searchResults as $result) {
                        $title = $result['title'] ?? '无标题';
                        $url = $result['url'] ?? '';
                        $snippet = $result['snippet'] ?? '';
                        $searchText .= "• {$title}\n";
                        if (!empty($url)) {
                            $searchText .= "  链接: {$url}\n";
                        }
                        if (!empty($snippet)) {
                            $searchText .= "  摘要: {$snippet}\n";
                        }
                        $searchText .= "\n";
                    }
                }
                
                ChatService::parseReturnSuccess(
                    'search',
                    $id,
                    $searchText,
                    $index,
                    $this->model,
                    null,
                    $this->outputStream
                );
                $hasSpecialContent = true;
            }
            
            // Bot模型特殊处理：工具调用
            if ($isBotModel && isset($delta['tool_calls'])) {
                $toolCalls = $delta['tool_calls'];
                \think\facade\Log::write("豆包Bot模型SSE工具调用: " . json_encode($toolCalls, JSON_UNESCAPED_UNICODE));
                
                // 格式化工具调用为可读文本
                $toolText = "【工具调用】\n";
                if (is_array($toolCalls)) {
                    foreach ($toolCalls as $call) {
                        $type = $call['type'] ?? '未知';
                        $function = $call['function'] ?? [];
                        $name = $function['name'] ?? '';
                        $arguments = $function['arguments'] ?? '';
                        
                        $toolText .= "• 工具类型: {$type}\n";
                        if (!empty($name)) {
                            $toolText .= "  函数名: {$name}\n";
                        }
                        if (!empty($arguments)) {
                            $toolText .= "  参数: {$arguments}\n";
                        }
                        $toolText .= "\n";
                    }
                }
                
                ChatService::parseReturnSuccess(
                    'tool_calls',
                    $id,
                    $toolText,
                    $index,
                    $this->model,
                    null,
                    $this->outputStream
                );
                $hasSpecialContent = true;
            }
            
            // Bot模型特殊处理：网络搜索信息
            if ($isBotModel && isset($delta['web_search'])) {
                $webSearch = $delta['web_search'];
                \think\facade\Log::write("豆包Bot模型SSE网络搜索: " . json_encode($webSearch, JSON_UNESCAPED_UNICODE));
                
                // 格式化网络搜索信息为可读文本
                $webSearchText = "【网络搜索】" . (is_string($webSearch) ? $webSearch : json_encode($webSearch, JSON_UNESCAPED_UNICODE)) . "\n";
                
                ChatService::parseReturnSuccess(
                    'web_search',
                    $id,
                    $webSearchText,
                    $index,
                    $this->model,
                    null,
                    $this->outputStream
                );
                $hasSpecialContent = true;
            }

            // 思考链条
            if (isset($delta['reasoning_content'])) {
                $streamContent = $delta['reasoning_content'];
                $chatEvent = 'reasoning';
            }

            // 结束标识
            if('stop' == $finishReason){
                $chatEvent = 'finish';
            }else{
                if (!isset($delta['content']) and !isset($delta['reasoning_content']) 
                    and !isset($delta['search_results']) and !isset($delta['tool_calls']) 
                    and !isset($delta['web_search']) and !$hasSpecialContent)
                {
                    Log::write('响应数据可能丢失:'.$stream);
                    continue;
                }

                if (isset($delta['content']) || isset($delta['reasoning_content'])) {
                    if ($chatEvent != 'reasoning') {
                        $contents = $this->content[$index] ?? '';
                        $this->content[$index] = $contents . $streamContent;
                    } else {
                        $this->reasoning .= $streamContent;
                        
                        // 记录推理内容更新
                        if (mb_strlen($this->reasoning) % 100 < mb_strlen($streamContent)) {
                            \think\facade\Log::write("豆包推理内容更新 - 当前长度: " . mb_strlen($this->reasoning));
                        }
                    }
                }
            }

            if ((!empty($streamContent) || $chatEvent == 'finish') && !$hasSpecialContent) {
            ChatService::parseReturnSuccess(
                $chatEvent,
                $id,
                    $streamContent,
                $index,
                $this->model,
                null,
                $this->outputStream
            );
            }
        }
    }
}