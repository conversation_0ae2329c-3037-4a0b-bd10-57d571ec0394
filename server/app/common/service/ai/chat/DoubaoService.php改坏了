<?php

namespace app\common\service\ai\chat;

use app\common\cache\KeyPoolCache;
use app\common\enum\ChatEnum;
use app\common\service\ai\ChatService;
use Exception;
use think\facade\Log;
use think\facade\Cache;
use WpOrg\Requests\Requests;

class DoubaoService
{
    protected array $config            = [];                        // 配置参数
    protected string $channel          = 'doubao';                  // 渠道模型
    protected string $model            = '';                        // 对话模型
    protected string $apiKey           = '';                        // 接口密钥
    protected string $baseUrl          = '';                        // 请求地址
    protected bool $outputStream       = true;                      // 流式输出
    protected bool $isBotModel         = false;                     // 是否为Bot模型

    protected int $contextNum          = 0;                         // 上下文数
    protected float $temperature       = 0;                         // 词汇属性
    protected float $frequencyPenalty  = 0;                         // 重复属性
    protected array $messages          = [];                        // 上下文内容

    protected array $headers           = [];                        // 请求头值
    protected string $reasoning        = '';                        // 思考的过程
    protected array $content           = [];                        // 回复的内容
    protected array $usage             = [];                        // 使用Token

    protected mixed $keyPoolServer = null;                          // Key池对象

    // 推理内容缓冲相关属性
    private string $reasoningBuffer = '';                           // 推理内容缓冲区
    private int $reasoningChunkCount = 0;                           // 缓冲区块数量
    private float $lastReasoningSendTime = 0;                       // 上次发送推理内容的时间
    private bool $reasoningBufferSent = false;                      // 推理内容是否已发送（防止重复发送）
    private const REASONING_BUFFER_SIZE = 20;                       // 缓冲区大小（字符数）
    private const REASONING_CHUNK_LIMIT = 10;                       // 最大缓冲块数
    private const REASONING_TIMEOUT = 2.0;                          // 缓冲超时时间（秒）
    
    // 安全相关常量
    private const MAX_MESSAGE_LENGTH = 50000;                       // 最大消息长度
    private const MAX_MESSAGES_COUNT = 100;                         // 最大消息数量
    private const RATE_LIMIT_WINDOW = 60;                           // 请求频率限制窗口（秒）
    private const RATE_LIMIT_MAX_REQUESTS = 100;                    // 窗口内最大请求数

    /**
     * @notes 初始化
     * @param array $chatConfig
     * @throws Exception
     */
    public function __construct(array $chatConfig)
    {
        // 获取当前模型的渠道
        $this->config  = $chatConfig;
        $this->channel = $chatConfig['channel'];

        // 是否流式输出 (SSE有效)
        $this->outputStream = ($chatConfig['outputStream'] ?? true);

        // 设置基础参数
        $this->model            = $this->config['model'] ?? '';
        $this->contextNum       = (int) ($this->config['context_num']??0);
        $this->temperature      = (float) ($this->config['temperature']??0.9);
        $this->frequencyPenalty = (float) $this->config['frequency_penalty']??0;

        // 识别Bot模型
        $this->isBotModel = $this->detectBotModel($this->model);
        
        // 安全地记录Bot模型识别结果
        if ($this->isBotModel) {
            $this->logSecurely("识别到Bot模型，将使用Bot API接口");
        }

        // 获取密钥Key
        $this->keyPoolServer = (new KeyPoolCache($chatConfig['model_id'], ChatEnum::MODEL_TYPE_CHAT));
        $this->apiKey = $this->keyPoolServer->getKey();
        if (isset($chatConfig['check_key']) and $chatConfig['check_key']) {
            if (empty($this->apiKey)) {
                throw new Exception('请在后台配置API密钥');
            }
        }

        // 验证API密钥格式
        if (!$this->validateApiKey($this->apiKey)) {
            throw new Exception('API密钥格式无效');
        }

        // 替换代理域名
        $this->baseUrl = 'https://ark.cn-beijing.volces.com/api/v3';
        if ($this->config['agency_api'] ?? '') {
            $this->baseUrl = $this->config['agency_api'];
        }

        // 设置请求头值
        $this->headers['Content-Type']  = 'application/json';
        $this->headers['Authorization'] = 'Bearer ' . trim($this->apiKey);
    }

    /**
     * @notes 验证API密钥格式
     * @param string $apiKey
     * @return bool
     */
    private function validateApiKey(string $apiKey): bool
    {
        if (empty($apiKey)) {
            return false;
        }
        
        // 检查基本格式（长度、字符集）
        if (strlen($apiKey) < 10 || strlen($apiKey) > 200) {
            return false;
        }
        
        // 检查是否包含非法字符
        if (!preg_match('/^[a-zA-Z0-9\-_\.]+$/', $apiKey)) {
            return false;
        }
        
        return true;
    }

    /**
     * @notes 安全地记录日志
     * @param string $message
     * @param array $context
     * @return void
     */
    private function logSecurely(string $message, array $context = []): void
    {
        // 脱敏处理
        $maskedContext = $this->maskSensitiveData($context);
        
        // 记录安全日志
        Log::write("豆包服务 - {$message}" . (empty($maskedContext) ? '' : ' - ' . json_encode($maskedContext)));
    }

    /**
     * @notes 脱敏处理敏感数据
     * @param array $data
     * @return array
     */
    private function maskSensitiveData(array $data): array
    {
        $masked = [];
        foreach ($data as $key => $value) {
            if (in_array($key, ['apiKey', 'authorization', 'token', 'password', 'secret'])) {
                $masked[$key] = $this->maskString($value);
            } elseif (is_array($value)) {
                $masked[$key] = $this->maskSensitiveData($value);
            } else {
                $masked[$key] = $value;
            }
        }
        return $masked;
    }

    /**
     * @notes 字符串脱敏
     * @param string $str
     * @return string
     */
    private function maskString(string $str): string
    {
        if (strlen($str) <= 8) {
            return str_repeat('*', strlen($str));
        }
        return substr($str, 0, 4) . str_repeat('*', strlen($str) - 8) . substr($str, -4);
    }

    /**
     * @notes 验证输入消息
     * @param array $messages
     * @return array
     * @throws Exception
     */
    private function validateMessages(array $messages): array
    {
        if (empty($messages)) {
            throw new Exception('消息不能为空');
        }

        if (count($messages) > self::MAX_MESSAGES_COUNT) {
            throw new Exception('消息数量超过限制');
        }

        foreach ($messages as $index => $message) {
            if (!is_array($message)) {
                throw new Exception("消息格式无效，索引：{$index}");
            }

            if (!isset($message['role']) || !isset($message['content'])) {
                throw new Exception("消息必须包含role和content字段，索引：{$index}");
            }

            if (!in_array($message['role'], ['user', 'assistant', 'system'])) {
                throw new Exception("无效的消息角色，索引：{$index}");
            }

            $contentLength = is_array($message['content']) ? 
                mb_strlen(json_encode($message['content'])) : 
                mb_strlen($message['content']);

            if ($contentLength > self::MAX_MESSAGE_LENGTH) {
                throw new Exception("消息内容过长，索引：{$index}");
            }

            // 检查是否包含潜在的恶意内容
            if ($this->containsMaliciousContent($message['content'])) {
                throw new Exception("消息包含不当内容，索引：{$index}");
            }
        }

        return $messages;
    }

    /**
     * @notes 检查是否包含恶意内容
     * @param mixed $content
     * @return bool
     */
    private function containsMaliciousContent($content): bool
    {
        $contentStr = is_array($content) ? json_encode($content) : (string)$content;
        
        // 检查SQL注入模式
        $sqlPatterns = [
            '/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b/i',
            '/[\'";].*(\bOR\b|\bAND\b).*[\'";]/i',
        ];

        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $contentStr)) {
                return true;
            }
        }

        // 检查XSS模式
        $xssPatterns = [
            '/<script[^>]*>.*?<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
        ];

        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $contentStr)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @notes 检查请求频率限制
     * @return void
     * @throws Exception
     */
    private function checkRateLimit(): void
    {
        try {
            // 尝试使用缓存进行频率限制
            $cacheKey = "doubao_rate_limit:" . ($this->getClientIp() ?: 'unknown');
            $requests = Cache::get($cacheKey, []);
            
            $currentTime = time();
            $windowStart = $currentTime - self::RATE_LIMIT_WINDOW;
            
            // 清理过期请求
            $requests = array_filter($requests, function($timestamp) use ($windowStart) {
                return $timestamp > $windowStart;
            });
            
            if (count($requests) >= self::RATE_LIMIT_MAX_REQUESTS) {
                throw new Exception('请求频率过高，请稍后再试');
            }
            
            // 记录当前请求
            $requests[] = $currentTime;
            Cache::set($cacheKey, $requests, self::RATE_LIMIT_WINDOW);
            
        } catch (Exception $e) {
            // 如果是频率限制异常，直接抛出
            if (strpos($e->getMessage(), '请求频率过高') !== false) {
                throw $e;
            }
            
            // 如果是缓存相关异常，记录日志但继续执行（降级处理）
            $this->logSecurely("缓存服务不可用，跳过频率限制", [
                'error' => $e->getMessage(),
                'environment' => 'docker'
            ]);
            
            // 在Docker环境中，如果缓存不可用，我们简单地跳过频率限制
            // 这样可以确保系统在缓存服务未启动时仍能正常工作
        }
    }

    /**
     * @notes 获取客户端IP
     * @return string
     */
    private function getClientIp(): string
    {
        $headers = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                return trim($ip);
            }
        }
        
        return '';
    }

    /**
     * @notes 检测是否为Bot模型
     * @param string $model
     * @return bool
     */
    private function detectBotModel(string $model): bool
    {
        $modelLower = strtolower($model);
        
        // Bot模型通常以"bot-"开头
        if (str_starts_with($model, 'bot-')) {
            return true;
        }
        
        // 联网模式关键词识别
        $networkKeywords = ['联网', '联网模式', '网络搜索', 'web', 'search'];
        foreach ($networkKeywords as $keyword) {
            if (str_contains($modelLower, strtolower($keyword))) {
                return true;
            }
        }
        
        // DeepSeek模型特殊识别
        if (str_contains($modelLower, 'deepseek')) {
            // deepseek r1 系列模型需要特殊处理（推理时间长）
            if (str_contains($modelLower, 'r1') || str_contains($modelLower, 'reasoning')) {
                return true;
            }
            // deepseek联网模式
            if (str_contains($modelLower, '联网')) {
                return true;
            }
        }
        
        // 其他Bot模型特征识别
        $botKeywords = ['bot', 'agent'];
        foreach ($botKeywords as $keyword) {
            if (str_contains($modelLower, $keyword)) {
                return true;
            }
        }
        
        // 特定模型ID识别
        $knownBotModels = [
            'bot-20250630160952-xphcl',  // 豆包联网检索模型
            // 可以添加更多已知的Bot模型
        ];
        
        return in_array($model, $knownBotModels);
    }

    /**
     * @notes 构建API请求URL
     * @return string
     */
    private function buildApiUrl(): string
    {
        if ($this->isBotModel) {
            // Bot模型使用专门的Bot API接口
            return $this->baseUrl . '/bots/chat/completions';
        } else {
            // 普通模型使用标准接口
            return $this->baseUrl . '/chat/completions';
        }
    }

    /**
     * @notes 构建请求数据
     * @param array $messages
     * @param bool $stream
     * @return array
     */
    private function buildRequestData(array $messages, bool $stream = false): array
    {
        $data = [
            'model'             => $this->model,
            'stream'            => $stream,
            'messages'          => $messages,
            'temperature'       => $this->temperature,
            'frequency_penalty' => $this->frequencyPenalty
        ];

        // Bot模型需要额外的stream_options参数
        if ($this->isBotModel && $stream) {
            $data['stream_options'] = ['include_usage' => true];
        }

        return $data;
    }

    /**
     * @notes HTTP对话请求
     * @param array $messages
     * @return array
     * @throws Exception
     * <AUTHOR>
     */
    public function chatHttpRequest(array $messages): array
    {
        // 检查请求频率限制
        $this->checkRateLimit();
        
        // 验证输入消息
        $messages = $this->validateMessages($messages);
        
        $this->messages = $messages;
        $url = $this->buildApiUrl();
        $data = $this->buildRequestData($messages, false);

        // Bot模型的HTTP请求也需要stream_options
        if ($this->isBotModel) {
            $data['stream_options'] = ['include_usage' => true];
        }

        // 设置超时时间 - 针对不同模型类型优化
        $timeout = 300; // 默认超时
        if ($this->isBotModel) {
            $modelLower = strtolower($this->model);
            if (str_contains($modelLower, 'deepseek') && str_contains($modelLower, 'r1')) {
                // DeepSeek R1 模型需要更长的推理时间
                $timeout = 900; // 15分钟
            } elseif (str_contains($modelLower, 'deepseek') && str_contains($modelLower, '联网')) {
                // DeepSeek 联网模式需要网络搜索时间
                $timeout = 600; // 10分钟
            } else {
                // 其他Bot模型
                $timeout = 600; // 10分钟
            }
        }
        $options['timeout'] = $timeout;
        
        // 安全地记录请求信息
        $this->logSecurely("API请求开始", [
            'model' => $this->model,
            'isBotModel' => $this->isBotModel,
            'messageCount' => count($messages)
        ]);

        try {
            $response = Requests::post($url, $this->headers, json_encode($data), $options);
            return $this->parseResponseData($response);
        } catch (Exception $e) {
            $this->logSecurely("API请求失败", ['error' => $e->getMessage()]);
            throw new Exception('API请求失败，请稍后重试');
        }
    }

    /**
     * @notes SSE对话请求
     * @param array $messages
     * @return DoubaoService
     * @throws Exception
     * <AUTHOR>
     */
    public function chatSseRequest(array $messages): self
    {
        ignore_user_abort(true);
        
        // 检查请求频率限制
        $this->checkRateLimit();
        
        // 验证输入消息
        $messages = $this->validateMessages($messages);
        
        $this->messages = $messages;
        $url = $this->buildApiUrl();
        $data = $this->buildRequestData($messages, true);
        
        // 简化状态重置 - 只重置推理内容
        $this->reasoning = '';
        
        // 安全地记录流式请求信息
        $this->logSecurely("流式请求开始", [
            'model' => $this->model,
            'isBotModel' => $this->isBotModel,
            'messageCount' => count($messages)
        ]);

        $response = true;
        $callback = function ($ch, $data) use (&$content,&$response,&$total){
            // 简化回调处理，避免复杂逻辑导致卡住
            try {
                $result = @json_decode($data);
                if (isset($result->error)) {
                    $error = $this->keyPoolServer->takeDownKey($result->error->message, $this->baseUrl);
                    $response = 'doubao:'.$result->error->message ? $error : $result->error->type;
                    return strlen($data);
                }
                
                // 简化数据处理，避免复杂的解析逻辑
                $this->parseStreamData($data);
                
            } catch (Exception $e) {
                // 记录错误但继续处理
                error_log("DoubaoService callback error: " . $e->getMessage());
            }

            // 客户端没断开
            if(connection_aborted()){
                return 1;
            }

            return strlen($data);
        };

        $headers = [];
        foreach ($this->headers as $key => $item) {
            $headers[] = $key . ': ' . trim($item);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        
        // Docker环境中的SSL设置优化
        if ($this->isBotModel) {
            // Bot模型在Docker环境中需要更宽松的SSL设置以避免连接超时
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            // 添加更多连接选项以提高成功率
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; DoubaoBot/1.0)');
            $this->logSecurely("Bot模型使用宽松SSL设置", ['environment' => 'docker']);
        } else {
            // 普通模型使用标准SSL验证
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        }
        
        curl_setopt($ch, CURLOPT_POST, 1);
        // 设置超时时间 - 针对不同模型类型优化
        $timeout = 301; // 默认超时
        if ($this->isBotModel) {
            $modelLower = strtolower($this->model);
            if (str_contains($modelLower, 'deepseek') && str_contains($modelLower, 'r1')) {
                // DeepSeek R1 模型需要更长的推理时间
                $timeout = 900; // 15分钟
            } elseif (str_contains($modelLower, 'deepseek') && str_contains($modelLower, '联网')) {
                // DeepSeek 联网模式需要网络搜索时间
                $timeout = 600; // 10分钟
            } else {
                // 其他Bot模型
                $timeout = 600; // 10分钟
            }
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);
        curl_exec($ch);
        curl_close($ch);

        if(true !== $response){
            if ($response === false) {
                throw new Exception('网络请求失败，请检查网络连接');
            } else {
                $error = $this->keyPoolServer->takeDownKey($response, $this->baseUrl);
                throw new Exception('API服务暂时不可用，请稍后重试');
            }
        }
        return $this;
    }

    /**
     * @notes 获取回复内容
     * @param string $type
     * @return array|string
     * <AUTHOR>
     */
    public function getReplyContent(string $type = 'content'): array|string
    {
        // 思考过程
        if ($type == 'reasoning') {
            return $this->reasoning;
        }
        // 答复内容
        return $this->content;
    }

    /**
     * 获取消耗的tokens
     * <AUTHOR>
     */
    public function getUsage(): array
    {
        $promptContent = '';
        foreach ($this->messages as $item) {
            if (is_array($item['content'])) {
                $promptContent .= $item['content'][0]['text'];
            } else {
                $promptContent .= $item['content'];
            }
        }

        $reply = trim($this->reasoning) . trim($this->content[0] ?? '');
        if (!$this->usage) {
            $promptTokens     = gpt_tokenizer_count($promptContent);
            $completionTokens = gpt_tokenizer_count($reply);
            return [
                'prompt_tokens'     => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens'      => $promptTokens + $completionTokens,
                'str_length'        => mb_strlen($promptContent . $reply)
            ] ?? [];
        } else {
            $this->usage['str_length'] = mb_strlen($promptContent . trim($reply));
            return $this->usage;
        }
    }

    /**
     * @notes 解析HTTP数据
     * @param mixed $response
     * @return array
     * @throws Exception
     * <AUTHOR>
     */
    private function parseResponseData(mixed $response): array
    {
        if ($response->status_code !== 200) {
            $this->logSecurely("API响应错误", ['status_code' => $response->status_code]);
            throw new Exception("API服务暂时不可用，请稍后重试");
        }

        $responseData = json_decode($response->body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logSecurely("响应解析失败", ['json_error' => json_last_error_msg()]);
            throw new Exception("响应数据格式错误");
        }

        if (isset($responseData['error'])) {
            $message = $responseData['error']['message'] ?? '未知错误';
            $this->logSecurely("API错误", ['error_message' => $message]);
            $error = $this->keyPoolServer->takeDownKey($message, $this->baseUrl);
            return ChatService::parseReturnError(false, 'API服务暂时不可用，请稍后重试', $responseData['error']['code'] ?? '', $this->model, $responseData['error']['type'] ?? '', $responseData['error']['param'] ?? '');
        }

        // 提取响应内容
        $this->content = [$responseData['choices'][0]['message']['content'] ?? ''];
        
        // 提取usage信息
        if (isset($responseData['usage'])) {
            $this->usage = $responseData['usage'];
        }

        return $responseData;
    }

    /**
     * @notes 解析流式数据 - 简化版本，防止卡住
     * @param string $data
     * @return void
     */
    private function parseStreamData(string $data): void
    {
        // 跳过空数据
        if (empty($data)) {
            return;
        }
        
        // 简化数据处理，避免复杂的循环
        $lines = explode("\n", $data);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // 跳过空行和结束标记
            if (empty($line) || $line === 'data: [DONE]') {
                continue;
            }
            
            // 只处理包含数据的行
            if (strpos($line, 'data: ') === 0) {
                $jsonData = substr($line, 6);
                $parsedData = @json_decode($jsonData, true);
                
                // 跳过JSON解析失败的数据
                if (!$parsedData || json_last_error() !== JSON_ERROR_NONE) {
                    continue;
                }
                
                // 安全地处理单个数据包，避免递归调用
                try {
                    $this->processSingleDataPacket($parsedData);
                } catch (Exception $e) {
                    // 记录错误但继续处理其他数据包
                    error_log("数据包处理错误: " . $e->getMessage());
                    continue;
                }
            }
        }
    }
    
    /**
     * @notes 处理单个数据包 - 简化版本
     * @param array $parsedData
     * @return void
     */
    private function processSingleDataPacket(array $parsedData): void
    {
        $id = $parsedData['id'] ?? '';
        $index = (int) ($parsedData['choices'][0]['index'] ?? 0);
        $finishReason = $parsedData['choices'][0]['finish_reason'] ?? '';
        
        // 处理使用量信息
        if (isset($parsedData['usage'])) {
            $this->usage = $parsedData['usage'];
        }
        
        // 简化Bot模型的推理内容处理
        if ($this->isBotModel && isset($parsedData['choices'][0]['delta']['reasoning_content'])) {
            $reasoningContent = $parsedData['choices'][0]['delta']['reasoning_content'];
            if (!empty($reasoningContent)) {
                $this->reasoning .= $reasoningContent;
                
                // 清理并发送推理内容
                $cleanedContent = $this->cleanReasoningContent($reasoningContent);
                if (!empty($cleanedContent)) {
                    // 对深度思考模型(如deepseek r1)使用特殊的事件类型
                    $eventType = 'reasoning';
                    $modelLower = strtolower($this->model);
                    if (str_contains($modelLower, 'deepseek') && str_contains($modelLower, 'r1')) {
                        $eventType = 'thinking'; // 深度思考模型使用thinking事件
                    }
                    
                    ChatService::parseReturnSuccess(
                        $eventType,
                        $id,
                        $cleanedContent,
                        $index,
                        $this->model,
                        $finishReason,
                        $this->outputStream
                    );
                }
            }
            return;
        }
        
        // 处理正常内容
        $streamContent = $parsedData['choices'][0]['delta']['content'] ?? '';
        $chatEvent = ($finishReason === 'stop') ? 'finish' : 'chat';
        
        if (!empty($streamContent)) {
            $contents = $this->content[$index] ?? '';
            $this->content[$index] = $contents . $streamContent;
        }
        
        // 发送流数据
        ChatService::parseReturnSuccess(
            $chatEvent,
            $id,
            $streamContent,
            $index,
            $this->model,
            $finishReason,
            $this->outputStream
        );
    }

    /**
     * @notes 清理推理内容，去除开头的标点符号和空白字符
     * @param string $content
     * @return string
     */
    private function cleanReasoningContent(string $content): string
    {
        // 去除开头和结尾的空白字符
        $cleaned = trim($content);
        
        // 去除开头的常见标点符号
        $cleaned = ltrim($cleaned, '，,。.！!？?；;：:、\n\r\t ');
        
        // 再次去除空白字符
        $cleaned = trim($cleaned);
        
        // 如果内容太短（少于2个字符），可能不是有意义的推理内容
        if (mb_strlen($cleaned) < 2) {
            return '';
        }
        
        // 如果内容只包含标点符号，也认为无效
        if (preg_match('/^[，,。.！!？?；;：:、\s]+$/', $cleaned)) {
            return '';
        }
        
        return $cleaned;
    }
}