<?php

namespace app\common\service\ai\chat;

use app\common\cache\KeyPoolCache;
use app\common\enum\ChatEnum;
use app\common\service\ai\ChatService;
use Exception;
use think\facade\Log;
use WpOrg\Requests\Requests;

class DoubaoService
{
    protected array $config            = [];                        // 配置参数
    protected string $channel          = 'doubao';                  // 渠道模型
    protected string $model            = '';                        // 对话模型
    protected string $apiKey           = '';                        // 接口密钥
    protected string $baseUrl          = '';                        // 请求地址
    protected bool $outputStream       = true;                      // 流式输出
    protected bool $isBotModel         = false;                     // 是否为Bot模型

    protected int $contextNum          = 0;                         // 上下文数
    protected float $temperature       = 0;                         // 词汇属性
    protected float $frequencyPenalty  = 0;                         // 重复属性
    protected array $messages          = [];                        // 上下文内容

    protected array $headers           = [];                        // 请求头值
    protected string $reasoning        = '';                        // 思考的过程
    protected array $content           = [];                        // 回复的内容
    protected array $usage             = [];                        // 使用Token

    protected mixed $keyPoolServer = null;                          // Key池对象

    // 推理内容缓冲相关属性
    private string $reasoningBuffer = '';                           // 推理内容缓冲区
    private int $reasoningChunkCount = 0;                           // 缓冲区块数量
    private float $lastReasoningSendTime = 0;                       // 上次发送推理内容的时间
    private const REASONING_BUFFER_SIZE = 50;                       // 缓冲区大小（字符数）- 增加以减少碎片化
    private const REASONING_CHUNK_LIMIT = 30;                       // 最大缓冲块数 - 增加以减少碎片化
    private const REASONING_TIMEOUT = 3.0;                          // 缓冲超时时间（秒）- 增加以减少碎片化

    /**
     * @notes 初始化
     * @param array $chatConfig
     * @throws Exception
     */
    public function __construct(array $chatConfig)
    {
        // 获取当前模型的渠道
        $this->config  = $chatConfig;
        $this->channel = $chatConfig['channel'];

        // 是否流式输出 (SSE有效)
        $this->outputStream = ($chatConfig['outputStream'] ?? true);

        // 设置基础参数
        $this->model            = $this->config['model'] ?? '';
        $this->contextNum       = (int) ($this->config['context_num']??0);
        $this->temperature      = (float) ($this->config['temperature']??0.9);
        $this->frequencyPenalty = (float) $this->config['frequency_penalty']??0;

        // 识别Bot模型
        $this->isBotModel = $this->detectBotModel($this->model);
        
        // 记录Bot模型识别结果
        if ($this->isBotModel) {
            Log::write("豆包服务 - 识别到Bot模型，将使用Bot API接口");
        }

        // 获取密钥Key
        $this->keyPoolServer = (new KeyPoolCache($chatConfig['model_id'], ChatEnum::MODEL_TYPE_CHAT));
        $this->apiKey = $this->keyPoolServer->getKey();
        if (isset($chatConfig['check_key']) and $chatConfig['check_key']) {
            if (empty($this->apiKey)) {
                throw new Exception('请在后台配置API密钥');
            }
        }

        // 替换代理域名
        $this->baseUrl = 'https://ark.cn-beijing.volces.com/api/v3';
        if ($this->config['agency_api'] ?? '') {
            $this->baseUrl = $this->config['agency_api'];
        }

        // 设置请求头值
        $this->headers['Content-Type']  = 'application/json';
        $this->headers['Authorization'] = 'Bearer ' . trim($this->apiKey);
    }

    /**
     * @notes 检测是否为Bot模型
     * @param string $model
     * @return bool
     */
    private function detectBotModel(string $model): bool
    {
        // Bot模型通常以"bot-"开头
        if (str_starts_with($model, 'bot-')) {
            return true;
        }
        
        // 其他Bot模型特征识别
        $botKeywords = ['bot', 'agent', 'search', 'web'];
        foreach ($botKeywords as $keyword) {
            if (str_contains(strtolower($model), $keyword)) {
                return true;
            }
        }
        
        // 特定模型ID识别
        $knownBotModels = [
            'bot-20250630160952-xphcl',  // 豆包联网检索模型
            // 可以添加更多已知的Bot模型
        ];
        
        return in_array($model, $knownBotModels);
    }

    /**
     * @notes 构建API请求URL
     * @return string
     */
    private function buildApiUrl(): string
    {
        if ($this->isBotModel) {
            // Bot模型使用专门的Bot API接口
            return $this->baseUrl . '/bots/chat/completions';
        } else {
            // 普通模型使用标准接口
            return $this->baseUrl . '/chat/completions';
        }
    }

    /**
     * @notes 构建请求数据
     * @param array $messages
     * @param bool $stream
     * @return array
     */
    private function buildRequestData(array $messages, bool $stream = false): array
    {
        $data = [
            'model'             => $this->model,
            'stream'            => $stream,
            'messages'          => $messages,
            'temperature'       => $this->temperature,
            'frequency_penalty' => $this->frequencyPenalty
        ];

        // Bot模型需要额外的stream_options参数
        if ($this->isBotModel && $stream) {
            $data['stream_options'] = ['include_usage' => true];
        }

        return $data;
    }

    /**
     * @notes HTTP对话请求
     * @param array $messages
     * @return array
     * @throws Exception
     * <AUTHOR>
     */
    public function chatHttpRequest(array $messages): array
    {
        $this->messages = $messages;
        $url = $this->buildApiUrl();
        $data = $this->buildRequestData($messages, false);

        // Bot模型的HTTP请求也需要stream_options
        if ($this->isBotModel) {
            $data['stream_options'] = ['include_usage' => true];
        }

        // 设置超时时间
        $options['timeout'] = $this->isBotModel ? 600 : 300; // Bot模型使用更长超时
        
        // 记录请求信息
        Log::write("豆包API请求 - 模型: {$this->model}, Bot模型: " . ($this->isBotModel ? '是' : '否'));

        try {
            $response = Requests::post($url, $this->headers, json_encode($data), $options);
            return $this->parseResponseData($response);
        } catch (Exception $e) {
            Log::write("豆包API请求失败: " . $e->getMessage());
            throw new Exception('API请求失败，请稍后重试');
        }
    }

    /**
     * @notes SSE对话请求
     * @param array $messages
     * @return DoubaoService
     * @throws Exception
     * <AUTHOR>
     */
    public function chatSseRequest(array $messages): self
    {
        ignore_user_abort(true);
        $this->messages = $messages;
        $url = $this->buildApiUrl();
        $data = $this->buildRequestData($messages, true);

        // 记录流式请求信息
        Log::write("豆包流式请求 - 模型: {$this->model}, Bot模型: " . ($this->isBotModel ? '是' : '否'));

        $response = true;
        $callback = function ($ch, $data) use (&$content,&$response,&$total){
            $result = @json_decode($data);
            if (isset($result->error)) {
                $error = $this->keyPoolServer->takeDownKey($result->error->message, $this->baseUrl);
                $response = 'doubao:'.$result->error->message ? $error : $result->error->type;
            }else{
                $this->parseStreamData($data);
            }

            // 客户端没断开
            if(connection_aborted()){
                return 1;
            }

            return strlen($data);
        };

        $headers = [];
        foreach ($this->headers as $key => $item) {
            $headers[] = $key . ': ' . trim($item);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->isBotModel ? 600 : 301); // Bot模型使用更长超时
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);
        curl_exec($ch);
        curl_close($ch);

        if(true !== $response){
            if ($response === false) {
                throw new Exception('API服务暂时不可用，请稍后重试');
            } else {
                $error = $this->keyPoolServer->takeDownKey($response, $this->baseUrl);
            throw new Exception('API服务暂时不可用，请稍后重试');
        }
        }
        return $this;
    }

    /**
     * @notes 获取回复内容
     * @param string $type
     * @return array|string
     * <AUTHOR>
     */
    public function getReplyContent(string $type = 'content'): array|string
    {
        // 思考过程
        if ($type == 'reasoning') {
            // 🔥 修复：对推理内容进行清理，确保数据库存储的内容也是清理后的
            return $this->cleanReasoningContent($this->reasoning);
        }
        // 答复内容
        return $this->content;
    }

    /**
     * 获取消耗的tokens
     * <AUTHOR>
     */
    public function getUsage(): array
    {
        $promptContent = '';
        foreach ($this->messages as $item) {
            if (is_array($item['content'])) {
                $promptContent .= $item['content'][0]['text'];
            } else {
                $promptContent .= $item['content'];
            }
            //$promptContent .= "\n\n";
            //$promptContent .= "\n";
        }

        $reply = trim($this->reasoning) . trim($this->content[0] ?? '');
        if (!$this->usage) {
            $promptTokens     = gpt_tokenizer_count($promptContent);
            $completionTokens = gpt_tokenizer_count($reply);
            return [
                'prompt_tokens'     => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens'      => $promptTokens + $completionTokens,
                'str_length'        => mb_strlen($promptContent . $reply)
            ] ?? [];
        } else {
            $this->usage['str_length'] = mb_strlen($promptContent . trim($reply));
            return $this->usage;
        }
    }

    /**
     * @notes 解析响应数据
     * @param mixed $response
     * @return array
     * @throws Exception
     */
    private function parseResponseData(mixed $response): array
    {
        if ($response->status_code !== 200) {
            Log::write("豆包API响应错误 - 状态码: {$response->status_code}");
            throw new Exception('API服务暂时不可用，请稍后重试');
        }

        $responseData = json_decode($response->body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::write("豆包API JSON解析失败: " . json_last_error_msg());
            throw new Exception('API服务响应格式异常，请稍后重试');
        }

        if (isset($responseData['error'])) {
            $errorMessage = $responseData['error']['message'] ?? '未知错误';
            
            // 记录错误信息
            Log::write("豆包API返回错误 - 错误码: " . ($responseData['error']['code'] ?? '') . ", 类型: " . ($responseData['error']['type'] ?? ''));
            
            // 处理密钥失效
            $this->keyPoolServer->takeDownKey($errorMessage, $this->baseUrl);
            throw new Exception($errorMessage);
        }

        return $responseData['choices'][0]['message'] ?? [];
    }



    /**
     * @notes 修正后的SSE数据解析方法
     * 支持豆包API的特殊格式和Bot模型特性
     * @param $stream
     * <AUTHOR>
     */
    private function parseStreamData($stream): void
    {
        $lines = explode("\n", $stream);
        
        foreach ($lines as $line) {
            $result = $this->parseStreamLine($line);
            
            if (!$result) {
                continue;
            }
            
            if ($result['type'] === 'done') {
                // 处理结束信号
                Log::write("豆包流式传输结束");
                // 发送剩余的推理内容缓冲区
                if ($this->isBotModel && !empty($this->reasoningBuffer)) {
                    $this->sendReasoningBuffer('', 0, 'stop');
                }
                break;
            }
            
            if ($result['type'] === 'data') {
                $parsedData = $result['json'];
                
                // 确保必要字段存在
                $id = $parsedData['id'] ?? '';
                $index = 0;
                $finishReason = '';
                
                // Bot模型的响应处理
                if ($this->isBotModel) {
                    $this->parseBotModelResponse($parsedData, $id, $index, $finishReason);
                } else {
                    $this->parseNormalModelResponse($parsedData, $id, $index, $finishReason);
                }
            }
        }
    }

    /**
     * @notes 解析流式数据行
     * 支持两种SSE格式
     * @param string $line
     * @return array|null
     */
    private function parseStreamLine(string $line): ?array
    {
        $line = trim($line);
        
        // 支持两种SSE格式
        if (str_starts_with($line, 'data: ')) {
            // 标准SSE格式：data: {...}
            $jsonStr = substr($line, 6);
        } elseif (str_starts_with($line, 'data:')) {
            // 豆包格式：data:{...}（无空格）
            $jsonStr = substr($line, 5);
        } else {
            return null;
        }
        
        // 检查是否是结束信号
        if (str_contains($jsonStr, '[DONE]')) {
            return ['type' => 'done'];
        }
        
        $parsed = json_decode($jsonStr, true);
        if ($parsed) {
            return ['type' => 'data', 'json' => $parsed];
        }
        
        return null;
    }

    /**
     * @notes Bot模型专用响应解析
     * @param array $parsedData
     * @param string $id
     * @param int $index
     * @param string $finishReason
     * @return void
     */
    private function parseBotModelResponse(array $parsedData, string $id, int &$index, string &$finishReason): void
    {
        if (!isset($parsedData['choices']) || !isset($parsedData['choices'][0])) {
            return;
        }

        $choice = $parsedData['choices'][0];
        $index = (int) ($choice['index'] ?? 0);
        $finishReason = $choice['finish_reason'] ?? '';
        $delta = $choice['delta'] ?? [];

        // 处理推理内容（使用缓冲机制）
        if (isset($delta['reasoning_content']) && !empty($delta['reasoning_content'])) {
            $this->handleBotReasoningContent($parsedData, $id, $index, $finishReason);
        }

        // 处理最终回复内容
        if (isset($delta['content']) && !empty($delta['content'])) {
            $content = $delta['content'];
            $this->content[$index] = ($this->content[$index] ?? '') . $content;
            
            ChatService::parseReturnSuccess(
                'chat', 
                $id, 
                $content, 
                $index, 
                $this->model, 
                $finishReason,
                $this->outputStream
            );
        }

        // 处理搜索结果
        if (isset($delta['search_results'])) {
            $searchResults = $delta['search_results'];
            Log::write("豆包Bot模型搜索完成 - 结果数量: " . count($searchResults));
            
            if ($this->outputStream) {
                ChatService::parseReturnSuccess(
                    'search', 
                    $id, 
                    json_encode($searchResults, JSON_UNESCAPED_UNICODE), 
                    $index, 
                    $this->model, 
                    $finishReason
                );
            }
        }

        // 处理工具调用
        if (isset($delta['tool_calls'])) {
            $toolCalls = $delta['tool_calls'];
            if ($this->outputStream) {
                ChatService::parseReturnSuccess(
                    'tool', 
                    $id, 
                    json_encode($toolCalls, JSON_UNESCAPED_UNICODE), 
                    $index, 
                    $this->model, 
                    $finishReason
                );
            }
        }

        // 处理使用统计
        if (isset($parsedData['usage']) || isset($parsedData['bot_usage'])) {
            $usage = $parsedData['usage'] ?? $parsedData['bot_usage'];
            $this->usage = $usage;
            
            if ($this->outputStream) {
                ChatService::parseReturnSuccess(
                    'usage', 
                    $id, 
                    json_encode($usage, JSON_UNESCAPED_UNICODE), 
                    $index, 
                    $this->model, 
                    $finishReason
                );
            }
        }

        // 🔥 修复：处理结束信号，移除重复的sendReasoningBuffer调用
        if ($finishReason === 'stop') {
            Log::write("Bot模型对话结束 - finish_reason: stop");
            // 注意：推理内容的发送已在 handleBotReasoningContent 中处理，这里不需要重复发送
            if ($this->outputStream) {
                ChatService::parseReturnSuccess('finish', $id, '', $index, $this->model, $finishReason);
            }
        }
    }

    /**
     * @notes 处理Bot模型的推理内容（带缓冲机制）
     * @param array $parsedData
     * @param string $id
     * @param int $index
     * @param string $finishReason
     * @return void
     */
    protected function handleBotReasoningContent(array $parsedData, string $id, int $index, ?string $finishReason): void
    {
        if (!isset($parsedData['choices'][0]['delta']['reasoning_content'])) {
            return;
        }

        $reasoningContent = $parsedData['choices'][0]['delta']['reasoning_content'];
        
        // 将推理内容添加到缓冲区
        $this->reasoningBuffer .= $reasoningContent;
        $this->reasoningChunkCount++;
        
        // 🔥 修复：累积时保持原始内容，只在前端显示时清理
        // 直接累积原始内容到数据库存储变量，保留所有标点符号
        $this->reasoning .= $reasoningContent;
        
        // 判断是否应该发送缓冲区内容
        $shouldSend = $this->shouldSendReasoningBuffer();
        
        if ($shouldSend) {
            $this->sendReasoningBuffer($id, $index, $finishReason);
        }
        
        // 如果对话结束，发送剩余缓冲区内容
        if ($finishReason === 'stop' && !empty($this->reasoningBuffer)) {
            $this->sendReasoningBuffer($id, $index, $finishReason);
        }
    }

    /**
     * @notes 判断是否应该发送推理内容缓冲区
     * @return bool
     */
    private function shouldSendReasoningBuffer(): bool
    {
        $bufferLength = mb_strlen($this->reasoningBuffer);
        $currentTime = microtime(true);
        
        // 条件1：缓冲区达到指定大小（增加缓冲区大小以减少碎片化）
        if ($bufferLength >= self::REASONING_BUFFER_SIZE) {
            return true;
        }
        
        // 条件2：遇到语义边界（句号、感叹号、问号、换行符）
        if (preg_match('/[。！？\n\r]$/', $this->reasoningBuffer)) {
            return true;
        }
        
        // 条件3：缓冲块数量达到限制（增加限制以减少碎片化）
        if ($this->reasoningChunkCount >= self::REASONING_CHUNK_LIMIT) {
            return true;
        }
        
        // 条件4：缓冲超时（增加超时时间以减少碎片化）
        if ($this->lastReasoningSendTime > 0 && 
            ($currentTime - $this->lastReasoningSendTime) >= self::REASONING_TIMEOUT) {
            return true;
        }
        
        // 条件5：遇到完整的词汇或短语（新增，减少单字符发送）
        if ($bufferLength >= 5 && preg_match('/[，,、；;]$/', $this->reasoningBuffer)) {
            return true;
        }
        
        return false;
    }

    /**
     * @notes 发送推理内容缓冲区
     * @param string $id
     * @param int $index
     * @param string|null $finishReason
     * @return void
     */
    private function sendReasoningBuffer(string $id, int $index, ?string $finishReason): void
    {
        if (empty($this->reasoningBuffer)) {
            return;
        }
        
        // 清理推理内容：去除开头的标点符号和空白字符
        $cleanedContent = $this->cleanReasoningContent($this->reasoningBuffer);
        
        // 如果清理后内容为空，则不发送
        if (empty($cleanedContent)) {
            Log::write("推理内容清理后为空，跳过发送: '{$this->reasoningBuffer}'");
            $this->reasoningBuffer = '';
            $this->reasoningChunkCount = 0;
            return;
        }
        
        // 发送清理后的推理内容事件
        if ($this->outputStream) {
            ChatService::parseReturnSuccess(
                'reasoning', 
                $id, 
                $cleanedContent, 
                $index, 
                $this->model, 
                $finishReason
            );
        }
        
        Log::write("发送推理内容: 原始='{$this->reasoningBuffer}' -> 清理后='{$cleanedContent}'");
        
        // 清空缓冲区
        $this->reasoningBuffer = '';
        $this->reasoningChunkCount = 0;
        $this->lastReasoningSendTime = microtime(true);
    }

    /**
     * @notes 清理推理内容，去除开头的标点符号和空白字符
     * @param string $content
     * @return string
     */
    protected function cleanReasoningContent(string $content): string
    {
        // 去除开头和结尾的空白字符
        $cleaned = trim($content);
        
        // 去除开头的常见标点符号和特殊字符
        $cleaned = ltrim($cleaned, '，,。.！!？?；;：:、\n\r\t [](){}「」『』""\'\'…—·');
        
        // 再次去除空白字符
        $cleaned = trim($cleaned);
        
        // 如果内容太短（少于1个字符），认为无效
        if (mb_strlen($cleaned) < 1) {
            return '';
        }
        
        // 如果内容只包含标点符号和特殊字符，也认为无效
        if (preg_match('/^[，,。.！!？?；;：:、\s\[\](){}「」『』""\'\'…—·]+$/u', $cleaned)) {
            return '';
        }
        
        // 特殊处理：如果是单个标点符号，直接过滤掉
        if (mb_strlen($cleaned) === 1 && preg_match('/[，,。.！!？?；;：:、\[\](){}「」『』""\'\'…—·]/u', $cleaned)) {
            return '';
        }
        
        return $cleaned;
    }

    /**
     * @notes 普通模型响应解析（保持原有逻辑）
     * @param array $parsedData
     * @param string $id
     * @param int $index
     * @param string $finishReason
     * @return void
     */
    protected function parseNormalModelResponse(array $parsedData, string $id, int &$index, string &$finishReason): void
    {
        $id            = $parsedData['id'] ?? '';
        $index         = (int) ($parsedData['choices'][0]['index'] ?? 0);
        $streamContent = $parsedData['choices'][0]['delta']['content'] ?? '';
        $finishReason  = $parsedData['choices'][0]['finish_reason'] ?? '';

        $chatEvent = 'chat';

        // 思考链条
        if (isset($parsedData['choices'][0]['delta']['reasoning_content'])) {
            $streamContent = $parsedData['choices'][0]['delta']['reasoning_content'];
            $chatEvent = 'reasoning';
            
            // 🔥 修复：普通模式推理内容保持原始格式
            // 直接累积原始内容到数据库存储变量
            $this->reasoning .= $streamContent;
            
            // 普通模式的推理内容通常格式良好，不需要过度清理
            // 只进行基础的空白字符处理，保留标点符号
            $streamContent = trim($streamContent);
            
            // 如果内容为空，则跳过发送
            if (empty($streamContent)) {
                Log::write("普通模型推理内容为空，跳过发送");
                return;
            }
        }

        // 结束标识
        if('stop' == $finishReason){
            $chatEvent = 'finish';
        }else{
            if (!isset($parsedData['choices'][0]['delta']['content']) and
                !isset($parsedData['choices'][0]['delta']['reasoning_content']))
            {
                Log::write('响应数据可能丢失: ' . json_encode($parsedData));
                return;
            }

            if ($chatEvent != 'reasoning') {
                $contents = $this->content[$index] ?? '';
                $this->content[$index] = $contents . $streamContent;
            }
            // 注意：推理内容的累积已在上面处理
        }

        // 给前端发送流数据
        ChatService::parseReturnSuccess(
            $chatEvent,
            $id,
            $streamContent,
            $index,
            $this->model,
            $finishReason,
            $this->outputStream
        );
    }








}