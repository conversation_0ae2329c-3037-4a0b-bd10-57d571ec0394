<?php

namespace app\common\service\ai\chat;

use app\common\cache\KeyPoolCache;
use app\common\enum\ChatEnum;
use app\common\service\ai\ChatService;
use Exception;
use think\facade\Log;
use WpOrg\Requests\Requests;

/**
 * 豆包AI服务类
 * 支持普通对话模型和Bot智能体模型（联网检索）
 * 基于火山方舟大模型服务平台API规范实现
 */
class DoubaoService
{
    protected array $config            = [];                        // 配置参数
    protected string $channel          = 'doubao';                  // 渠道模型
    protected string $model            = '';                        // 对话模型
    protected string $apiKey           = '';                        // 接口密钥
    protected string $baseUrl          = '';                        // 请求地址
    protected bool $outputStream       = true;                      // 流式输出
    protected bool $isBotModel         = false;                     // 是否为Bot模型（联网检索等智能体）

    protected int $contextNum          = 0;                         // 上下文数
    protected float $temperature       = 0;                         // 词汇属性
    protected float $frequencyPenalty  = 0;                         // 重复属性
    protected array $messages          = [];                        // 上下文内容

    protected array $headers           = [];                        // 请求头值
    protected string $reasoning        = '';                        // 思考的过程
    protected array $content           = [];                        // 回复的内容
    protected array $usage             = [];                        // 使用Token

    protected mixed $keyPoolServer = null;                          // Key池对象
    
    // 新增：流式数据监控属性
    protected bool $dataReceived = false;                           // 是否接收到数据
    protected bool $hasReasoningContent = false;                    // 是否接收到推理内容
    protected float $lastDataTime = 0;                              // 最后接收数据时间
    protected int $totalDataReceived = 0;                           // 总接收数据量
    
    // 已移除：推理内容智能缓冲机制（现在直接处理推理内容）
    // 已移除：客户端断开处理机制（现在采用其他模型的简单处理方式）

    /**
     * @notes 初始化
     * @param array $chatConfig
     * @throws Exception
     */
    public function __construct(array $chatConfig)
    {
        // 获取当前模型的渠道
        $this->config  = $chatConfig;
        $this->channel = $chatConfig['channel'];

        // 是否流式输出 (SSE有效)
        $this->outputStream = ($chatConfig['outputStream'] ?? true);

        // 设置基础参数
        $this->model            = $this->config['model'] ?? '';
        $this->contextNum       = (int) ($this->config['context_num']??0);
        $this->temperature      = (float) ($this->config['temperature']??0.9);
        $this->frequencyPenalty = (float) ($this->config['frequency_penalty'] ?? 0);

        // 识别Bot模型（联网检索等智能体模型）
        $this->isBotModel = $this->detectBotModel($this->model);

        // 获取密钥Key
        $this->keyPoolServer = (new KeyPoolCache($chatConfig['model_id'], ChatEnum::MODEL_TYPE_CHAT));
        $this->apiKey = $this->keyPoolServer->getKey();
        if (isset($chatConfig['check_key']) and $chatConfig['check_key']) {
            if (empty($this->apiKey)) {
                throw new Exception('请在后台配置key');
            }
        }

        // 替换代理域名
        $this->baseUrl = 'https://ark.cn-beijing.volces.com/api/v3';
        if ($this->config['agency_api'] ?? '') {
            $this->baseUrl = $this->config['agency_api'];
        }

        // 设置请求头值
        $this->headers['Content-Type']  = 'application/json';
        $this->headers['Authorization'] = 'Bearer ' . trim($this->apiKey);
        
        // 记录Bot模型识别结果到日志
        if ($this->isBotModel) {
            Log::write("识别到Bot模型: {$this->model}，将使用Bot API接口");
        }
    }

    /**
     * @notes 检测是否为Bot模型
     * @param string $model
     * @return bool
     * <AUTHOR>
     */
    private function detectBotModel(string $model): bool
    {
        // Bot模型通常以"bot-"开头，如：bot-20250630160952-xphcl
        if (str_starts_with($model, 'bot-')) {
            return true;
        }
        
        // 也可以根据其他特征识别Bot模型
        $botKeywords = ['bot', 'agent', 'search', 'web'];
        foreach ($botKeywords as $keyword) {
            if (str_contains(strtolower($model), $keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * @notes 构建API请求URL
     * @return string
     * <AUTHOR>
     */
    private function buildApiUrl(): string
    {
        if ($this->isBotModel) {
            // Bot模型使用专门的Bot API接口
            return $this->baseUrl . '/bots/chat/completions';
        } else {
            // 普通模型使用标准接口
            return $this->baseUrl . '/chat/completions';
        }
    }

    /**
     * @notes 构建请求数据
     * @param array $messages
     * @param bool $stream
     * @return array
     * <AUTHOR>
     */
    private function buildRequestData(array $messages, bool $stream = false): array
    {
        $data = [
            'model'             => $this->model,
            'stream'            => $stream,
            'messages'          => $messages,
            'temperature'       => $this->temperature,
            'frequency_penalty' => $this->frequencyPenalty
        ];

        // Bot模型需要额外的stream_options参数
        if ($this->isBotModel && $stream) {
            $data['stream_options'] = ['include_usage' => true];
        }

        return $data;
    }

    /**
     * @notes HTTP对话请求
     * @param array $messages
     * @return array
     * @throws Exception
     * <AUTHOR>
     */
    public function chatHttpRequest(array $messages): array
    {
        $this->messages = $messages;
        $url = $this->buildApiUrl();
        $data = $this->buildRequestData($messages, false);

        // Bot模型的HTTP请求也需要stream_options（即使不是流式）
        if ($this->isBotModel) {
            $data['stream_options'] = ['include_usage' => true];
        }

        // 设置超时时间
        $options['timeout'] = 300;
        
        // 记录请求信息到日志
        Log::write("豆包API请求 - URL: {$url}, 模型: {$this->model}, Bot模型: " . ($this->isBotModel ? '是' : '否'));
        
        $response = Requests::post($url, $this->headers, json_encode($data), $options);
        return $this->parseResponseData($response);
    }

    /**
     * @notes SSE对话请求 - 完全按照OpenAI标准实现
     * @param array $messages
     * @return DoubaoService
     * @throws Exception
     * <AUTHOR>
     */
    public function chatSseRequest(array $messages): self
    {
        ignore_user_abort(true);
        $this->messages = $messages;
        $url = $this->buildApiUrl();
        $data = $this->buildRequestData($messages, true);

        Log::write("豆包SSE请求开始 - 模型: {$this->model}, URL: {$url}");

        // 关键修复：使用闭包变量而不是静态变量，完全按照OpenAI模式
        $response = false;
        $callback = function ($ch, $data) use (&$response) {
            Log::write("豆包streamCallback - 数据长度: " . strlen($data) . ", response状态: " . ($response === false ? 'false' : ($response === true ? 'true' : $response)));
            
            $result = @json_decode($data, true);
            
            // 如果不是第一次，直接处理流数据
            if (false !== $response) {
                Log::write("豆包streamCallback - 非首次调用，开始解析数据");
                $this->parseStreamData($data);
                
                // 客户端没断开时继续接收
                if (!connection_aborted()) {
                    Log::write("豆包streamCallback - 客户端连接正常，继续接收");
                    return strlen($data);
                } else {
                    Log::write("豆包streamCallback - 客户端已断开，停止接收");
                    return 1;
                }
            }

            // 第一次流执行的流程 - 错误处理
            if ($result && isset($result['error'])) {
                Log::write("豆包streamCallback - 检测到API错误: " . json_encode($result['error']));
                $error = $this->keyPoolServer->takeDownKey($result['error']['message'], $url);
                $response = '豆包:' . ($error ?: $result['error']['message']);
                return 1;
            }

            // 首次正常数据处理
            Log::write("豆包streamCallback - 首次调用，解析数据并设置状态为true");
            $this->parseStreamData($data);
            $response = true;
            return strlen($data);
        };

        // 根据模型类型设置不同的超时时间
        $timeout = $this->isBotModel ? 600 : 300;
        $connectTimeout = 30;

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . trim($this->apiKey),
            'Accept: text/event-stream',
            'Cache-Control: no-cache',
            'Connection: keep-alive'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $connectTimeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);

        Log::write("豆包SSE请求 - 开始执行curl_exec，超时设置: {$timeout}秒");
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        Log::write("豆包SSE请求完成 - HTTP状态码: {$httpCode}, response状态: " . ($response === false ? 'false' : ($response === true ? 'true' : $response)));

        // 错误处理 - 完全按照OpenAI模式
        if (true !== $response) {
            if ($response === false) {
                Log::write("豆包SSE请求失败 - response为false，可能是网络错误");
                throw new Exception('豆包请求出错!');
            } else {
                Log::write("豆包SSE请求失败 - response错误信息: {$response}");
                $error = $this->keyPoolServer->takeDownKey($response, $url);
                throw new Exception($error);
            }
        }

        Log::write("豆包SSE请求成功完成");
        return $this;
    }

    /**
     * @notes 获取回复内容
     * @param string $type
     * @return array|string
     * <AUTHOR>
     */
    public function getReplyContent(string $type = 'content'): array|string
    {
        // 思考过程
        if ($type == 'reasoning') {
            // 清理推理内容，确保格式正确
            $cleanReasoning = $this->cleanReasoningContent($this->reasoning);
            
            // 记录推理内容获取状态
            Log::write("豆包getReplyContent - 获取推理内容，原始长度: " . mb_strlen($this->reasoning) . ", 清理后长度: " . mb_strlen($cleanReasoning));
            
            return $cleanReasoning;
        }
        // 答复内容
        return $this->content;
    }

    /**
     * 获取消耗的tokens
     * <AUTHOR>
     */
    public function getUsage(): array
    {
        $promptContent = '';
        foreach ($this->messages as $item) {
            if (is_array($item['content'])) {
                $promptContent .= $item['content'][0]['text'];
            } else {
                $promptContent .= $item['content'];
            }
            //$promptContent .= "\n\n";
            //$promptContent .= "\n";
        }

        $reply = trim($this->reasoning) . trim($this->content[0]);
        if (!$this->usage) {
            $promptTokens     = gpt_tokenizer_count($promptContent);
            $completionTokens = gpt_tokenizer_count($reply);
            return [
                'prompt_tokens'     => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens'      => $promptTokens + $completionTokens,
                'str_length'        => mb_strlen($promptContent . $reply)
            ] ?? [];
        } else {
            $this->usage['str_length'] = mb_strlen($promptContent . trim($reply));
            return $this->usage;
        }
    }

    /**
     * @notes 解析HTTP数据
     * @param mixed $response
     * @return array
     * @throws Exception
     * <AUTHOR>
     */
    private function parseResponseData(mixed $response): array
    {
        $responseData = json_decode($response->body,true);
        if (isset($responseData['error'])) {
            $message = $responseData['error']['message'];
            $param   = $responseData['error']['param'];
            $type    = $responseData['error']['type'];
            $code    = $responseData['error']['code'];
            
            // 记录错误信息到日志
            Log::write("豆包API错误 - 模型: {$this->model}, Bot模型: " . ($this->isBotModel ? '是' : '否') . ", 错误: {$message}");
            
            $error = $this->keyPoolServer->takeDownKey($message, $this->baseUrl);
            return ChatService::parseReturnError(false, $error, $code, $this->model, $type, $param);
        }

        $this->usage   = $responseData['usage'] ?? [];
        $this->content = [$responseData['choices'][0]['message']['content']];
        
        // 记录成功响应到日志
        Log::write("豆包API成功 - 模型: {$this->model}, Bot模型: " . ($this->isBotModel ? '是' : '否') . ", Token使用: " . json_encode($this->usage));
        
        return $responseData;
    }

    /**
     * @notes 解析SSE数据 - 完全按照OpenAI标准实现，添加详细日志
     * @param $stream
     * <AUTHOR>
     */
    private function parseStreamData($stream): void
    {
        Log::write("豆包parseStreamData - 开始解析，数据长度: " . strlen($stream));
        
        $dataLists = explode("\n\n", $stream);
        foreach ($dataLists as $data) {
            if (!str_contains($data, 'data:')) {
                continue;
            }
            
            // 检查结束信号
            if (str_contains($data, 'data: [DONE]') || str_contains($data, '[DONE]')) {
                Log::write("豆包parseStreamData - 检测到结束信号 [DONE]");
                continue;
            }
            
            // 提取JSON数据
            $originalData = $data;
            $data = str_replace("data: ", "", $data);
            $data = str_replace("data:", "", $data);
            $parsedData = json_decode($data, true);
            
            // 解析到数据是空的，跳过处理
            if (empty($parsedData) || !is_array($parsedData)) {
                Log::write("豆包parseStreamData - 数据解析失败，原始数据: " . substr($originalData, 0, 200));
                continue;
            }
            
            Log::write("豆包parseStreamData - 成功解析数据，ID: " . ($parsedData['id'] ?? 'unknown'));
            
            // 处理单个数据块
            $this->processStreamChunk($parsedData);
        }
    }
    
    /**
     * @notes 处理单个流式数据块 - 移除客户端检查，完全按照OpenAI模式
     * @param array $parsedData
     * <AUTHOR>
     */
    private function processStreamChunk(array $parsedData): void
    {
        // 检查是否有错误
        if (isset($parsedData['error'])) {
            Log::write("豆包processStreamChunk - 检测到错误: " . json_encode($parsedData['error']));
            return;
        }

        // 确保必要字段存在
        $id = $parsedData['id'] ?? '';
        
        // 检查基本结构
        if (!isset($parsedData['choices'][0])) {
            Log::write("豆包processStreamChunk - 缺少choices字段，跳过处理");
            return;
        }
        
        $choice = $parsedData['choices'][0];
        $index = (int) ($choice['index'] ?? 0);
        $finishReason = $choice['finish_reason'] ?? '';
        
        Log::write("豆包processStreamChunk - 处理choice，index: {$index}, finishReason: {$finishReason}");
        
        // 处理delta内容
        if (isset($choice['delta'])) {
            $delta = $choice['delta'];
            
            // 处理普通内容 - 移除客户端检查，在callback中统一处理
            if (isset($delta['content']) && !empty($delta['content'])) {
                $streamContent = $delta['content'];
                $chatEvent = 'chat';
                
                // 累积内容
                $contents = $this->content[$index] ?? '';
                $this->content[$index] = $contents . $streamContent;
                
                Log::write("豆包processStreamChunk - 发送chat事件，内容长度: " . mb_strlen($streamContent));
                
                // 发送流式数据
                ChatService::parseReturnSuccess(
                    $chatEvent,
                    $id,
                    $streamContent,
                    $index,
                    $this->model,
                    null,
                    $this->outputStream
                );
            }
            
            // 处理推理内容（深度思考）- 增强版本，确保内容完整保存
            if (isset($delta['reasoning_content']) && !empty($delta['reasoning_content'])) {
                $reasoningContent = $delta['reasoning_content'];
                $chatEvent = 'reasoning';
                
                // 累积推理内容 - 增强版本
                $this->reasoning .= $reasoningContent;
                
                // 设置推理内容状态标志
                $this->hasReasoningContent = true;
                
                Log::write("豆包processStreamChunk - 累积推理内容，当前总长度: " . mb_strlen($this->reasoning) . ", 新增长度: " . mb_strlen($reasoningContent));
                
                // 检查客户端状态 - 如果断开，记录当前推理内容状态
                if (connection_aborted()) {
                    Log::write("豆包推理内容 - 客户端断开时推理内容长度: " . mb_strlen($this->reasoning));
                    return; // 不发送SSE事件，但保留已累积的内容
                }
                
                // 发送推理内容
                ChatService::parseReturnSuccess(
                    $chatEvent,
                    $id,
                    $reasoningContent,
                    $index,
                    $this->model,
                    null,
                    $this->outputStream
                );
            }
        }
        
        // 处理结束信号 - 移除客户端检查，在callback中统一处理
        if ('stop' == $finishReason) {
            $chatEvent = 'finish';
            $this->usage = $parsedData['usage'] ?? [];
            
            Log::write("豆包processStreamChunk - 发送finish事件，usage: " . json_encode($this->usage));
            
            // 发送结束事件
            ChatService::parseReturnSuccess(
                $chatEvent,
                $id,
                '',
                $index,
                $this->model,
                null,
                $this->outputStream
            );
        }
        
        // 处理token使用量
        if (isset($parsedData['usage'])) {
            $this->usage = $parsedData['usage'];
            Log::write("豆包processStreamChunk - 更新usage信息: " . json_encode($this->usage));
        }
    }
    
    /**
     * @notes 尝试修复JSON数据格式问题
     * @param string $data
     * @return string|null
     * <AUTHOR>
     */
    private function tryFixJsonData(string $data): ?string
    {
        // 常见的JSON格式问题修复
        $data = trim($data);
        
        // 移除可能的BOM
        $data = ltrim($data, "\xEF\xBB\xBF");
        
        // 修复可能的转义问题
        $data = str_replace(['\\"', "\\'"], ['"', "'"], $data);
        
        // 尝试修复截断的JSON
        if (!str_ends_with($data, '}') && !str_ends_with($data, ']')) {
            // 如果JSON被截断，尝试添加结束符
            $openBraces = substr_count($data, '{') - substr_count($data, '}');
            $openBrackets = substr_count($data, '[') - substr_count($data, ']');
            
            for ($i = 0; $i < $openBraces; $i++) {
                $data .= '}';
            }
            for ($i = 0; $i < $openBrackets; $i++) {
                $data .= ']';
            }
        }
        
        // 验证修复后的JSON
        $test = json_decode($data, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $data;
        }
        
        return null;
    }
    
    /**
     * @notes 获取模型信息
     * @return array
     * <AUTHOR>
     */
    public function getModelInfo(): array
    {
        return [
            'model' => $this->model,
            'type' => $this->isBotModel ? 'bot' : 'normal',
            'capabilities' => $this->isBotModel ? 
                ['text_generation', 'web_search', 'tool_calling', 'real_time_info'] : 
                ['text_generation', 'creative_writing', 'code_generation'],
            'api_endpoint' => $this->buildApiUrl(),
            'timeout' => $this->isBotModel ? 600 : 301
        ];
    }
    
    /**
     * @notes 健康检查
     * @return bool
     * <AUTHOR>
     */
    public function healthCheck(): bool
    {
        try {
            $testMessages = [
                ['role' => 'user', 'content' => 'Hello']
            ];
            
            $result = $this->chatHttpRequest($testMessages);
            return !empty($this->content);
            
        } catch (\Exception $e) {
            Log::write("健康检查失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 清理推理内容，移除开头的逗号和多余空格
     * @param string $content
     * @return string
     * <AUTHOR>
     */
    private function cleanReasoningContent(string $content): string
    {
        // 移除开头的逗号、空格、换行符
        $content = ltrim($content, " \t\n\r\0\x0B,，");
        
        // 移除开头的多余标点符号
        $content = preg_replace('/^[,，\s]+/u', '', $content);
        
        // 确保内容不为空且有意义
        $content = trim($content);
        
        return $content;
    }

    /**
     * @notes 获取内容保存状态（用于数据库保存检查）
     * @return array
     * <AUTHOR>
     */
    public function getContentSaveStatus(): array
    {
        $replyLength = is_array($this->content) ? mb_strlen($this->content[0] ?? '') : mb_strlen($this->content);
        $reasoningLength = mb_strlen($this->reasoning);
        
        return [
            'has_reply' => $replyLength > 0,
            'has_reasoning' => $reasoningLength > 0,
            'reply_length' => $replyLength,
            'reasoning_length' => $reasoningLength,
            'total_content' => $replyLength + $reasoningLength,
            'save_worthy' => ($replyLength > 0 || $reasoningLength > 0)
        ];
    }
    
    /**
     * @notes 设置聊天参数用于数据保存
     * @param array $params
     * <AUTHOR>
     */
    public function setChatParamsForSave(array $params): void
    {
        $this->chatParams = $params;
        $this->shouldSaveOnDisconnect = true;
        \think\facade\Log::write("豆包Service - 设置聊天参数用于断开保存");
    }
    
    /**
     * @notes 客户端断开时保存数据到数据库
     * <AUTHOR>
     */
    private function saveOnClientDisconnect(): void
    {
        if (!$this->shouldSaveOnDisconnect) {
            return;
        }
        
        try {
            // 检查是否有内容需要保存
            $hasContent = !empty($this->content) || !empty($this->reasoning);
            if (!$hasContent) {
                \think\facade\Log::write("豆包Service断开保存 - 无内容，跳过保存");
                return;
            }
            
            \think\facade\Log::write("豆包Service断开保存 - 开始保存数据，回复长度: " . mb_strlen($this->content) . ", 推理长度: " . mb_strlen($this->reasoning));
            
            // 获取必要的参数
            $userId = $this->chatParams['user_id'] ?? 0;
            $otherId = $this->chatParams['other_id'] ?? 0;
            $categoryId = $this->chatParams['category_id'] ?? 0;
            $type = $this->chatParams['type'] ?? 1;
            $question = $this->chatParams['question'] ?? '';
            $model = $this->chatParams['model'] ?? '';
            
            if (!$userId || !$question) {
                \think\facade\Log::write("豆包Service断开保存 - 缺少必要参数，跳过保存");
                return;
            }
            
            // 准备保存数据
            $saveData = [
                'user_id' => $userId,
                'other_id' => $otherId,
                'category_id' => $categoryId,
                'type' => $type,
                'ask' => $question,
                'reply' => is_array($this->content) ? ($this->content[0] ?? '') : $this->content,
                'reasoning' => $this->reasoning,
                'model' => $model,
                'channel' => 'doubao',
                'create_time' => time(),
                'update_time' => time(),
                'prompt_tokens' => $this->usage['prompt_tokens'] ?? 0,
                'completion_tokens' => $this->usage['completion_tokens'] ?? 0,
                'total_tokens' => $this->usage['total_tokens'] ?? 0,
                'is_show' => 1
            ];
            
            // 直接保存到数据库
            $chatRecord = new \app\common\model\ChatRecord();
            $result = $chatRecord->save($saveData);
            
            if ($result) {
                \think\facade\Log::write("豆包Service断开保存 - 保存成功，记录ID: " . $chatRecord->id);
            } else {
                \think\facade\Log::write("豆包Service断开保存 - 保存失败");
            }
            
        } catch (\Exception $e) {
            \think\facade\Log::write("豆包Service断开保存 - 异常: " . $e->getMessage());
        }
    }
}