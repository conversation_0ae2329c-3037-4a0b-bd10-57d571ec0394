<?php

namespace app\common\service;

use app\common\service\CachedWordsService;
use think\facade\Log;

/**
 * 统一敏感词检测服务
 * 整合知识库和对话的敏感词检测逻辑
 */
class UnifiedSensitiveService
{
    private $cachedWordsService;
    
    public function __construct()
    {
        $this->cachedWordsService = new CachedWordsService();
    }
    
    /**
     * 统一敏感词检测接口
     * @param string $content 待检测内容
     * @param string $type 检测类型：chat(对话) | knowledge(知识库)
     * @return array
     */
    public function checkSensitive($content, $type = 'chat')
    {
        try {
            // 强制启用敏感词检测，不依赖数据库配置
            $this->cachedWordsService->sensitive($content);
            
            // 记录检测日志
            Log::info("敏感词检测", [
                'type' => $type,
                'content_length' => mb_strlen($content),
                'is_sensitive' => false,
                'sensitive_words' => []
            ]);
            
            return [
                'is_sensitive' => false,
                'sensitive_words' => []
            ];
            
        } catch (\Exception $e) {
            // 异常情况下，为了安全起见，认为内容敏感
            Log::error("敏感词检测异常", [
                'type' => $type,
                'error' => $e->getMessage(),
                'content_length' => mb_strlen($content)
            ]);
            
            return [
                'is_sensitive' => true,
                'sensitive_words' => [$e->getMessage()],
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 知识库内容检测（严格模式）
     * @param string $content
     * @return array
     */
    public function checkKnowledgeContent($content)
    {
        return $this->checkSensitive($content, 'knowledge');
    }
    
    /**
     * 对话内容检测
     * @param string $content
     * @return array
     */
    public function checkChatContent($content)
    {
        return $this->checkSensitive($content, 'chat');
    }
    
    /**
     * 批量检测
     * @param array $contents 内容数组
     * @param string $type 检测类型
     * @return array
     */
    public function batchCheck($contents, $type = 'chat')
    {
        $results = [];
        
        foreach ($contents as $key => $content) {
            $results[$key] = $this->checkSensitive($content, $type);
        }
        
        return $results;
    }
} 