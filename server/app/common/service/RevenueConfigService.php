<?php

namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;

/**
 * 🚀 智能体分成配置服务
 * @notes 管理分成模式开关和相关配置
 * @upgrade 2025/08/02 - 支持实时分成和定时任务分成模式切换
 */
class RevenueConfigService
{
    // 分成模式常量
    const MODE_REALTIME = 1;    // 实时分成模式
    const MODE_BATCH = 2;       // 定时任务分成模式
    
    // 配置缓存键
    const CACHE_KEY = 'revenue_config';
    const CACHE_TTL = 300; // 5分钟缓存
    
    /**
     * 🚀 获取分成模式
     * @return int 1=实时分成, 2=定时任务分成
     */
    public static function getRevenueMode(): int
    {
        $config = self::getConfig();
        return intval($config['mode'] ?? self::MODE_REALTIME);
    }
    
    /**
     * 🚀 设置分成模式
     * @param int $mode 分成模式
     * @return bool
     */
    public static function setRevenueMode(int $mode): bool
    {
        if (!in_array($mode, [self::MODE_REALTIME, self::MODE_BATCH])) {
            return false;
        }
        
        $config = self::getConfig();
        $config['mode'] = $mode;
        
        $result = self::setConfig($config);
        
        if ($result) {
            Log::info('[分成配置] 分成模式已切换', [
                'old_mode' => $config['mode'] ?? 'unknown',
                'new_mode' => $mode,
                'mode_name' => self::getModeName($mode),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
        
        return $result;
    }
    
    /**
     * 🚀 获取分成配置
     * @return array
     */
    public static function getConfig(): array
    {
        // 先从缓存获取
        $config = Cache::get(self::CACHE_KEY);
        
        if ($config === null) {
            // 从数据库获取配置
            $config = self::loadConfigFromDatabase();
            
            // 设置缓存
            Cache::set(self::CACHE_KEY, $config, self::CACHE_TTL);
        }
        
        return $config ?: self::getDefaultConfig();
    }
    
    /**
     * 🚀 设置分成配置
     * @param array $config 配置数组
     * @return bool
     */
    public static function setConfig(array $config): bool
    {
        try {
            // 验证配置
            $config = self::validateConfig($config);
            
            // 保存到数据库
            $result = self::saveConfigToDatabase($config);
            
            if ($result) {
                // 清除缓存
                Cache::delete(self::CACHE_KEY);
                
                Log::info('[分成配置] 配置已更新', [
                    'config' => $config,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('[分成配置] 配置更新失败', [
                'error' => $e->getMessage(),
                'config' => $config
            ]);
            return false;
        }
    }
    
    /**
     * 🚀 检查是否为实时分成模式
     * @return bool
     */
    public static function isRealtimeMode(): bool
    {
        return self::getRevenueMode() === self::MODE_REALTIME;
    }
    
    /**
     * 🚀 检查是否为定时任务分成模式
     * @return bool
     */
    public static function isBatchMode(): bool
    {
        return self::getRevenueMode() === self::MODE_BATCH;
    }
    
    /**
     * 🚀 获取模式名称
     * @param int $mode 模式
     * @return string
     */
    public static function getModeName(int $mode): string
    {
        switch ($mode) {
            case self::MODE_REALTIME:
                return '实时分成模式';
            case self::MODE_BATCH:
                return '定时任务分成模式';
            default:
                return '未知模式';
        }
    }
    
    /**
     * 🚀 获取批量处理配置
     * @return array
     */
    public static function getBatchConfig(): array
    {
        $config = self::getConfig();
        
        return [
            'batch_size' => intval($config['batch_size'] ?? 1000),
            'frequency' => intval($config['frequency'] ?? 5), // 分钟
            'parallel' => boolval($config['parallel'] ?? false),
            'max_retry' => intval($config['max_retry'] ?? 3),
            'retry_delay' => intval($config['retry_delay'] ?? 300), // 秒
        ];
    }
    
    /**
     * 🚀 获取默认配置
     * @return array
     */
    private static function getDefaultConfig(): array
    {
        return [
            'mode' => self::MODE_REALTIME, // 默认实时分成模式
            'batch_size' => 1000,
            'frequency' => 5, // 5分钟
            'parallel' => false,
            'max_retry' => 3,
            'retry_delay' => 300,
            'is_enable' => true,
            'share_ratio' => 30,
            'platform_ratio' => 70,
            'min_revenue' => 0.01,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 🚀 从数据库加载配置
     * @return array|null
     */
    private static function loadConfigFromDatabase(): ?array
    {
        try {
            // 这里应该从实际的配置表加载
            // 暂时使用文件存储作为示例
            $configFile = runtime_path() . 'revenue_config.json';
            
            if (file_exists($configFile)) {
                $content = file_get_contents($configFile);
                return json_decode($content, true) ?: null;
            }
            
            return null;
            
        } catch (\Exception $e) {
            Log::error('[分成配置] 从数据库加载配置失败', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 🚀 保存配置到数据库
     * @param array $config 配置数组
     * @return bool
     */
    private static function saveConfigToDatabase(array $config): bool
    {
        try {
            // 这里应该保存到实际的配置表
            // 暂时使用文件存储作为示例
            $configFile = runtime_path() . 'revenue_config.json';
            
            $config['updated_at'] = date('Y-m-d H:i:s');
            
            $result = file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
            
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('[分成配置] 保存配置到数据库失败', [
                'error' => $e->getMessage(),
                'config' => $config
            ]);
            return false;
        }
    }
    
    /**
     * 🚀 验证配置
     * @param array $config 配置数组
     * @return array
     */
    private static function validateConfig(array $config): array
    {
        // 验证分成模式
        if (isset($config['mode'])) {
            $config['mode'] = intval($config['mode']);
            if (!in_array($config['mode'], [self::MODE_REALTIME, self::MODE_BATCH])) {
                $config['mode'] = self::MODE_REALTIME;
            }
        }
        
        // 验证批次大小
        if (isset($config['batch_size'])) {
            $config['batch_size'] = max(100, min(5000, intval($config['batch_size'])));
        }
        
        // 验证频率
        if (isset($config['frequency'])) {
            $config['frequency'] = max(1, min(60, intval($config['frequency'])));
        }
        
        // 验证分成比例
        if (isset($config['share_ratio'])) {
            $config['share_ratio'] = max(0, min(100, floatval($config['share_ratio'])));
        }
        
        if (isset($config['platform_ratio'])) {
            $config['platform_ratio'] = max(0, min(100, floatval($config['platform_ratio'])));
        }
        
        // 验证最小分成金额
        if (isset($config['min_revenue'])) {
            $config['min_revenue'] = max(0, floatval($config['min_revenue']));
        }
        
        return $config;
    }
    
    /**
     * 🚀 获取配置状态信息
     * @return array
     */
    public static function getConfigStatus(): array
    {
        $config = self::getConfig();
        $mode = self::getRevenueMode();
        
        return [
            'mode' => $mode,
            'mode_name' => self::getModeName($mode),
            'is_realtime' => $mode === self::MODE_REALTIME,
            'is_batch' => $mode === self::MODE_BATCH,
            'batch_config' => self::getBatchConfig(),
            'last_updated' => $config['updated_at'] ?? 'unknown',
            'cache_status' => Cache::has(self::CACHE_KEY) ? 'cached' : 'not_cached'
        ];
    }
    
    /**
     * 🚀 清除配置缓存
     * @return bool
     */
    public static function clearCache(): bool
    {
        return Cache::delete(self::CACHE_KEY);
    }
}
