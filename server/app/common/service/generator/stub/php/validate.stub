<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

{NAMESPACE}


use app\common\validate\BaseValidate;


/**
 * {CLASS_COMMENT}
 * Class {UPPER_CAMEL_NAME}Validate
 * @package app\{MODULE_NAME}\validate{PACKAGE_NAME}
 */
class {UPPER_CAMEL_NAME}Validate extends BaseValidate
{

     /**
      * 设置校验规则
      * @var string[]
      */
    protected $rule = [
{RULE}
    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
{FIELD}
    ];


    /**
     * @notes 添加场景
     * @return {UPPER_CAMEL_NAME}Validate
     * <AUTHOR>
     * @date {DATE}
     */
    public function sceneAdd()
    {
        {ADD_PARAMS}
    }


    /**
     * @notes 编辑场景
     * @return {UPPER_CAMEL_NAME}Validate
     * <AUTHOR>
     * @date {DATE}
     */
    public function sceneEdit()
    {
        {EDIT_PARAMS}
    }


    /**
     * @notes 删除场景
     * @return {UPPER_CAMEL_NAME}Validate
     * <AUTHOR>
     * @date {DATE}
     */
    public function sceneDelete()
    {
        return $this->only(['{PK}']);
    }


    /**
     * @notes 详情场景
     * @return {UPPER_CAMEL_NAME}Validate
     * <AUTHOR>
     * @date {DATE}
     */
    public function sceneDetail()
    {
        return $this->only(['{PK}']);
    }

}