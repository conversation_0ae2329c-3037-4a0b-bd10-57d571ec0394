import request from '@/utils/request'

// {COMMENT}列表
export function api{UPPER_CAMEL_NAME}Lists(params: any) {
    return request.get({ url: '/{ROUTE}/lists', params })
}

// 添加{COMMENT}
export function api{UPPER_CAMEL_NAME}Add(params: any) {
    return request.post({ url: '/{ROUTE}/add', params })
}

// 编辑{COMMENT}
export function api{UPPER_CAMEL_NAME}Edit(params: any) {
    return request.post({ url: '/{ROUTE}/edit', params })
}

// 删除{COMMENT}
export function api{UPPER_CAMEL_NAME}Delete(params: any) {
    return request.post({ url: '/{ROUTE}/delete', params })
}

// {COMMENT}详情
export function api{UPPER_CAMEL_NAME}Detail(params: any) {
    return request.get({ url: '/{ROUTE}/detail', params })
}