<?php
/**
 * 安全增强版敏感词缓存服务
 * 包含加密存储、访问控制、防篡改等安全机制
 */

namespace app\common\service;

use app\common\model\SensitiveWord;
use DfaFilter\SensitiveHelper;
use Exception;
use think\facade\Cache;
use think\facade\Log;

class SecureCachedWordsService
{
    // 加密密钥（生产环境应从环境变量获取）
    private static $encryptKey = null;
    
    // 缓存键名（随机化）
    private static $cachePrefix = 'sw_';
    private static $wordsKey = null;
    private static $versionKey = null;
    private static $hashKey = null;
    
    // 缓存时间（秒）
    private static $cacheTime = 3600; // 1小时
    
    // 安全配置
    private static $securityConfig = [
        'enable_encryption' => true,        // 启用加密
        'enable_integrity_check' => true,   // 启用完整性检查
        'enable_access_log' => true,        // 启用访问日志
        'max_cache_size' => 1048576,        // 最大缓存大小 1MB
        'rate_limit' => 100,                // 每分钟最大调用次数
    ];
    
    // 性能统计
    private static $stats = [
        'cache_hits' => 0,
        'cache_misses' => 0,
        'security_violations' => 0,
        'build_time' => 0,
        'detect_time' => 0
    ];
    
    /**
     * 初始化安全配置
     */
    private static function initSecurity(): void
    {
        if (self::$encryptKey === null) {
            // 生成或获取加密密钥
            self::$encryptKey = self::getEncryptionKey();
            
            // 生成随机化的缓存键名
            $keyHash = substr(md5(self::$encryptKey . 'sensitive_words'), 0, 8);
            self::$wordsKey = self::$cachePrefix . 'data_' . $keyHash;
            self::$versionKey = self::$cachePrefix . 'ver_' . $keyHash;
            self::$hashKey = self::$cachePrefix . 'hash_' . $keyHash;
        }
    }
    
    /**
     * 获取加密密钥
     */
    private static function getEncryptionKey(): string
    {
        // 优先从环境变量获取
        $envKey = env('SENSITIVE_WORDS_KEY', '');
        if (!empty($envKey) && strlen($envKey) >= 32) {
            return substr($envKey, 0, 32);
        }
        
        // 从敏感词密钥文件生成
        $keyFile = "extend/sensitive_key.bin";
        if (file_exists($keyFile)) {
            $file = fopen($keyFile, "rb");
            $key = fread($file, 32);
            fclose($file);
            return $key;
        }
        
        // 默认密钥（生产环境应避免使用）
        return hash('sha256', 'default_sensitive_words_key_2024', true);
    }
    
    /**
     * 敏感词检测（安全增强版）
     */
    public static function sensitive(string $content): void
    {
        $startTime = microtime(true);
        
        try {
            // 安全检查
            if (!self::securityCheck($content)) {
                self::$stats['security_violations']++;
                throw new Exception('安全检查失败');
            }
            
            // 获取敏感词数据
            $sensitiveWords = self::getSensitiveWords();
            
            if (empty($sensitiveWords)) {
                return;
            }
            
            // 记录访问日志
            if (self::$securityConfig['enable_access_log']) {
                self::logAccess('sensitive_check', strlen($content));
            }
            
            // 分批检测（避免单次构建过大的DFA树）
            $sensitiveWordArr = array_chunk($sensitiveWords, 20000);
            $sensitiveWordGroup = [];
            
            foreach ($sensitiveWordArr as $sensitiveWordArrValue) {
                $handle = SensitiveHelper::init()->setTree($sensitiveWordArrValue);
                $badWordList = $handle->getBadWord($content);
                $sensitiveWordGroup = array_merge($sensitiveWordGroup, $badWordList);
            }
            
            $sensitiveWordGroup = array_unique($sensitiveWordGroup);
            if (!empty($sensitiveWordGroup)) {
                // 记录敏感词命中日志
                self::logSensitiveHit($sensitiveWordGroup, $content);
                throw new Exception('提问存在敏感词：' . implode(',', $sensitiveWordGroup));
            }
            
        } finally {
            // 记录检测时间
            self::$stats['detect_time'] += (microtime(true) - $startTime) * 1000;
        }
    }
    
    /**
     * 安全检查
     */
    private static function securityCheck(string $content): bool
    {
        // 内容长度检查
        if (strlen($content) > 100000) { // 100KB限制
            Log::warning('敏感词检测：内容过长', ['length' => strlen($content)]);
            return false;
        }
        
        // 频率限制检查
        if (!self::rateLimitCheck()) {
            Log::warning('敏感词检测：频率限制触发');
            return false;
        }
        
        return true;
    }
    
    /**
     * 频率限制检查
     */
    private static function rateLimitCheck(): bool
    {
        $rateLimitKey = 'rate_limit:sensitive:' . md5($_SERVER['REMOTE_ADDR'] ?? 'unknown');
        $currentCount = Cache::get($rateLimitKey, 0);
        
        if ($currentCount >= self::$securityConfig['rate_limit']) {
            return false;
        }
        
        Cache::set($rateLimitKey, $currentCount + 1, 60); // 1分钟窗口
        return true;
    }
    
    /**
     * 获取敏感词数据（安全增强版）
     */
    private static function getSensitiveWords(): array
    {
        self::initSecurity();
        
        // 检查配置
        $isSensitive = ConfigService::get('chat', 'is_sensitive', 1);
        $isSensitiveSystem = ConfigService::get('chat', 'is_sensitive_system', 1);
        
        if (!$isSensitive && !$isSensitiveSystem) {
            return [];
        }
        
        // 生成安全版本号
        $version = self::generateSecureVersion();
        $cachedVersion = Cache::get(self::$versionKey, '');
        
        // 检查缓存是否有效且完整性验证通过
        if ($cachedVersion === $version && self::verifyCacheIntegrity()) {
            $encryptedWords = Cache::get(self::$wordsKey);
            if ($encryptedWords !== false) {
                $words = self::decryptData($encryptedWords);
                if ($words !== false) {
                    self::$stats['cache_hits']++;
                    return $words;
                }
            }
        }
        
        // 缓存失效，重新加载
        self::$stats['cache_misses']++;
        $startTime = microtime(true);
        
        $allWords = [];
        
        // 加载文件敏感词
        if ($isSensitive) {
            $fileWords = self::loadFileWords();
            $allWords = array_merge($allWords, $fileWords);
        }
        
        // 加载数据库敏感词
        if ($isSensitiveSystem) {
            $dbWords = self::loadDatabaseWords();
            $allWords = array_merge($allWords, $dbWords);
        }
        
        // 去重并过滤空值
        $allWords = array_unique(array_filter($allWords));
        
        // 安全检查：缓存大小限制
        $serializedSize = strlen(serialize($allWords));
        if ($serializedSize > self::$securityConfig['max_cache_size']) {
            Log::warning('敏感词缓存：数据过大', ['size' => $serializedSize]);
            // 截断数据或使用文件缓存
            $allWords = array_slice($allWords, 0, 50000); // 限制词汇数量
        }
        
        // 加密存储
        $encryptedWords = self::encryptData($allWords);
        if ($encryptedWords !== false) {
            Cache::set(self::$wordsKey, $encryptedWords, self::$cacheTime);
            Cache::set(self::$versionKey, $version, self::$cacheTime);
            
            // 存储完整性哈希
            if (self::$securityConfig['enable_integrity_check']) {
                $hash = self::generateDataHash($allWords);
                Cache::set(self::$hashKey, $hash, self::$cacheTime);
            }
        }
        
        // 记录构建时间
        self::$stats['build_time'] += (microtime(true) - $startTime) * 1000;
        
        return $allWords;
    }
    
    /**
     * 加密数据
     */
    private static function encryptData(array $data): string|false
    {
        if (!self::$securityConfig['enable_encryption']) {
            return serialize($data);
        }
        
        try {
            $serialized = serialize($data);
            $iv = random_bytes(16);
            $encrypted = openssl_encrypt($serialized, 'aes-256-cbc', self::$encryptKey, OPENSSL_RAW_DATA, $iv);
            
            if ($encrypted === false) {
                return false;
            }
            
            return base64_encode($iv . $encrypted);
        } catch (Exception $e) {
            Log::error('敏感词加密失败', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 🔒 安全修复：解密数据 - 已修复反序列化安全风险
     * @notes 使用安全的反序列化机制，限制允许的类
     * @security_fix 2025/08/02 - 防止对象注入攻击
     */
    private static function decryptData(string $encryptedData): array|false
    {
        if (!self::$securityConfig['enable_encryption']) {
            // 🔒 安全修复：使用安全的反序列化
            return self::safeUnserialize($encryptedData);
        }

        try {
            $data = base64_decode($encryptedData);
            if ($data === false || strlen($data) < 16) {
                return false;
            }

            $iv = substr($data, 0, 16);
            $encrypted = substr($data, 16);

            $decrypted = openssl_decrypt($encrypted, 'aes-256-cbc', self::$encryptKey, OPENSSL_RAW_DATA, $iv);
            if ($decrypted === false) {
                return false;
            }

            // 🔒 安全修复：使用安全的反序列化
            return self::safeUnserialize($decrypted);
        } catch (Exception $e) {
            Log::error('敏感词解密失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 🔒 安全修复：安全的反序列化方法
     * @notes 限制允许反序列化的类，防止对象注入攻击
     * @param string $data 序列化数据
     * @return array|false
     * @security_fix 2025/08/02 - 反序列化安全防护
     */
    private static function safeUnserialize(string $data): array|false
    {
        try {
            // 定义允许反序列化的类白名单
            $allowedClasses = [
                'stdClass',
                'DateTime',
                'DateTimeImmutable',
                // 只允许基础的PHP类，不允许自定义类
            ];

            // 使用安全的反序列化选项
            $result = unserialize($data, [
                'allowed_classes' => $allowedClasses
            ]);

            // 验证反序列化结果必须是数组
            if (!is_array($result)) {
                Log::warning('敏感词反序列化安全警告：结果不是数组', [
                    'result_type' => gettype($result),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return false;
            }

            // 验证数组结构的安全性
            if (!self::validateUnserializedData($result)) {
                Log::warning('敏感词反序列化安全警告：数据结构验证失败', [
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return false;
            }

            return $result;

        } catch (Exception $e) {
            Log::error('安全反序列化失败', [
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return false;
        }
    }

    /**
     * 🔒 安全修复：验证反序列化数据的结构安全性
     * @notes 确保反序列化的数据符合预期格式
     * @param array $data 反序列化后的数据
     * @return bool
     * @security_fix 2025/08/02 - 数据结构安全验证
     */
    private static function validateUnserializedData(array $data): bool
    {
        // 检查数据大小限制
        if (count($data) > 100000) {
            return false;
        }

        // 递归检查数组内容
        return self::validateArrayContent($data, 0);
    }

    /**
     * 🔒 安全修复：递归验证数组内容
     * @notes 防止嵌套过深或包含危险内容
     * @param mixed $data 要验证的数据
     * @param int $depth 当前递归深度
     * @return bool
     * @security_fix 2025/08/02 - 递归数据安全验证
     */
    private static function validateArrayContent($data, int $depth): bool
    {
        // 防止递归过深
        if ($depth > 10) {
            return false;
        }

        if (is_array($data)) {
            foreach ($data as $key => $value) {
                // 验证键的安全性
                if (!is_string($key) && !is_int($key)) {
                    return false;
                }

                // 递归验证值
                if (!self::validateArrayContent($value, $depth + 1)) {
                    return false;
                }
            }
        } elseif (is_string($data)) {
            // 字符串长度限制
            if (strlen($data) > 10000) {
                return false;
            }
        } elseif (!is_scalar($data) && !is_null($data)) {
            // 只允许标量值和null
            return false;
        }

        return true;
    }
    
    /**
     * 生成安全版本号
     */
    private static function generateSecureVersion(): string
    {
        $version = '';
        
        // 文件版本
        $keyFile = "extend/sensitive_key.bin";
        $dataFile = "extend/sensitive_data.bin";
        
        if (file_exists($keyFile) && file_exists($dataFile)) {
            $version .= filemtime($keyFile) . '_' . filemtime($dataFile);
        }
        
        // 数据库版本
        try {
            $lastUpdate = (new SensitiveWord())->where(['status' => 1])->max('update_time');
            $version .= '_' . ($lastUpdate ?: 0);
        } catch (Exception $e) {
            // 忽略数据库错误
        }
        
        // 添加安全盐值
        $salt = substr(self::$encryptKey, 0, 8);
        return hash('sha256', $version . $salt);
    }
    
    /**
     * 生成数据完整性哈希
     */
    private static function generateDataHash(array $data): string
    {
        $content = serialize($data);
        return hash_hmac('sha256', $content, self::$encryptKey);
    }
    
    /**
     * 验证缓存完整性
     */
    private static function verifyCacheIntegrity(): bool
    {
        if (!self::$securityConfig['enable_integrity_check']) {
            return true;
        }
        
        $storedHash = Cache::get(self::$hashKey, '');
        if (empty($storedHash)) {
            return false;
        }
        
        $encryptedWords = Cache::get(self::$wordsKey);
        if ($encryptedWords === false) {
            return false;
        }
        
        $words = self::decryptData($encryptedWords);
        if ($words === false) {
            return false;
        }
        
        $currentHash = self::generateDataHash($words);
        return hash_equals($storedHash, $currentHash);
    }
    
    /**
     * 加载文件敏感词
     */
    private static function loadFileWords(): array
    {
        try {
            $keyFile = "extend/sensitive_key.bin";
            $dataFile = "extend/sensitive_data.bin";
            
            if (!file_exists($keyFile) || !file_exists($dataFile)) {
                return [];
            }
            
            // 读取密钥
            $file = fopen($keyFile, "rb");
            $key = fread($file, 32);
            $iv = fread($file, 16);
            fclose($file);
            
            // 解密数据
            $ciphertext = file_get_contents($dataFile);
            $plaintext = openssl_decrypt($ciphertext, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
            
            if ($plaintext === false) {
                return [];
            }
            
            return explode(PHP_EOL, trim($plaintext));
            
        } catch (Exception $e) {
            Log::error("加载文件敏感词失败", ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 加载数据库敏感词
     */
    private static function loadDatabaseWords(): array
    {
        try {
            $sensitiveWord = (new SensitiveWord())->where(['status' => 1])->column('word');
            $systemSensitiveArr = [];
            
            foreach ($sensitiveWord as $sensitiveWordValue) {
                $systemSensitiveArr = array_merge($systemSensitiveArr, explode('；', $sensitiveWordValue));
            }
            
            return $systemSensitiveArr;
            
        } catch (Exception $e) {
            Log::error("加载数据库敏感词失败", ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 记录访问日志
     */
    private static function logAccess(string $action, int $contentLength): void
    {
        Log::info('敏感词服务访问', [
            'action' => $action,
            'content_length' => $contentLength,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => time()
        ]);
    }
    
    /**
     * 记录敏感词命中日志
     */
    private static function logSensitiveHit(array $words, string $content): void
    {
        Log::warning('敏感词命中', [
            'words' => $words,
            'content_preview' => mb_substr($content, 0, 100),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'timestamp' => time()
        ]);
    }
    
    /**
     * 清理缓存（安全版）
     */
    public static function clearCache(): bool
    {
        self::initSecurity();
        
        try {
            Cache::delete(self::$wordsKey);
            Cache::delete(self::$versionKey);
            Cache::delete(self::$hashKey);
            
            Log::info('敏感词缓存已清理', [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'timestamp' => time()
            ]);
            
            return true;
        } catch (Exception $e) {
            Log::error('清理敏感词缓存失败', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 预热缓存（安全版）
     */
    public static function warmupCache(): array
    {
        $startTime = microtime(true);
        
        // 强制重新加载
        self::clearCache();
        
        $words = self::getSensitiveWords();
        $totalTime = (microtime(true) - $startTime) * 1000;
        
        return [
            'success' => true,
            'word_count' => count($words),
            'build_time' => round($totalTime, 2) . 'ms',
            'cache_size' => self::formatBytes(strlen(serialize($words))),
            'encrypted' => self::$securityConfig['enable_encryption'],
            'integrity_check' => self::$securityConfig['enable_integrity_check']
        ];
    }
    
    /**
     * 获取缓存状态（安全版）
     */
    public static function getCacheStatus(): array
    {
        self::initSecurity();
        
        $wordsExists = Cache::has(self::$wordsKey);
        $versionExists = Cache::has(self::$versionKey);
        $hashExists = Cache::has(self::$hashKey);
        
        $status = [
            'cached' => $wordsExists && $versionExists,
            'version' => Cache::get(self::$versionKey, 'none'),
            'word_count' => 0,
            'cache_size' => '0B',
            'integrity_verified' => false,
            'security_config' => self::$securityConfig,
            'stats' => self::$stats
        ];
        
        if ($wordsExists) {
            $encryptedWords = Cache::get(self::$wordsKey, '');
            $words = self::decryptData($encryptedWords);
            if ($words !== false) {
                $status['word_count'] = count($words);
                $status['cache_size'] = self::formatBytes(strlen(serialize($words)));
                $status['integrity_verified'] = self::verifyCacheIntegrity();
            }
        }
        
        return $status;
    }
    
    /**
     * 获取性能统计
     */
    public static function getStats(): array
    {
        return self::$stats;
    }
    
    /**
     * 重置统计数据
     */
    public static function resetStats(): void
    {
        self::$stats = [
            'cache_hits' => 0,
            'cache_misses' => 0,
            'security_violations' => 0,
            'build_time' => 0,
            'detect_time' => 0
        ];
    }
    
    /**
     * 格式化字节数
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 批量检测多个内容（安全版）
     */
    public static function batchSensitive(array $contents): array
    {
        $results = [];
        
        foreach ($contents as $key => $content) {
            try {
                self::sensitive($content);
                $results[$key] = ['status' => 'pass', 'message' => '检测通过'];
            } catch (Exception $e) {
                $results[$key] = ['status' => 'fail', 'message' => $e->getMessage()];
            }
        }
        
        return $results;
    }
    
    /**
     * 安全配置更新
     */
    public static function updateSecurityConfig(array $config): bool
    {
        try {
            self::$securityConfig = array_merge(self::$securityConfig, $config);
            
            Log::info('敏感词安全配置已更新', [
                'config' => $config,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'timestamp' => time()
            ]);
            
            return true;
        } catch (Exception $e) {
            Log::error('更新敏感词安全配置失败', ['error' => $e->getMessage()]);
            return false;
        }
    }
} 