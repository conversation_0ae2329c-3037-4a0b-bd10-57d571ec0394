<?php

namespace app\common\service;

use app\common\model\auth\Admin;
use app\common\model\user\User;
use think\facade\Log;
use think\facade\Cache;

/**
 * 🔒 智能体分成权限控制服务
 * @notes 提供细粒度的分成功能权限控制
 * @security_fix 2025/08/02 - 权限安全增强
 */
class RevenuePermissionService
{
    // 权限常量
    const PERM_VIEW_ALL_REVENUE = 'revenue.view.all';
    const PERM_VIEW_OWN_REVENUE = 'revenue.view.own';
    const PERM_MODIFY_CONFIG = 'revenue.config.modify';
    const PERM_SETTLE_REVENUE = 'revenue.settle';
    const PERM_EXPORT_REVENUE = 'revenue.export';
    const PERM_DELETE_REVENUE = 'revenue.delete';
    
    // 操作类型
    const ACTION_VIEW = 'view';
    const ACTION_MODIFY = 'modify';
    const ACTION_DELETE = 'delete';
    const ACTION_EXPORT = 'export';
    const ACTION_SETTLE = 'settle';
    
    /**
     * 🔒 安全修复：检查管理员分成权限
     * @param int $adminId 管理员ID
     * @param string $permission 权限标识
     * @param array $context 上下文信息
     * @return bool
     * @security_fix 2025/08/02 - 细粒度权限控制
     */
    public static function checkAdminPermission(int $adminId, string $permission, array $context = []): bool
    {
        try {
            // 获取管理员信息
            $admin = Admin::find($adminId);
            if (!$admin) {
                Log::warning('[分成权限] 管理员不存在', [
                    'admin_id' => $adminId,
                    'permission' => $permission
                ]);
                return false;
            }
            
            // 超级管理员拥有所有权限
            if ($admin->role_id == 1) {
                return true;
            }
            
            // 检查具体权限
            $hasPermission = self::validateSpecificPermission($admin, $permission, $context);
            
            // 记录权限检查日志
            Log::info('[分成权限] 权限检查', [
                'admin_id' => $adminId,
                'admin_account' => $admin->account,
                'permission' => $permission,
                'result' => $hasPermission,
                'context' => $context,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
            return $hasPermission;
            
        } catch (\Exception $e) {
            Log::error('[分成权限] 权限检查异常', [
                'admin_id' => $adminId,
                'permission' => $permission,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 🔒 安全修复：检查用户分成权限
     * @param int $userId 用户ID
     * @param string $action 操作类型
     * @param array $context 上下文信息
     * @return bool
     * @security_fix 2025/08/02 - 用户权限控制
     */
    public static function checkUserPermission(int $userId, string $action, array $context = []): bool
    {
        try {
            // 获取用户信息
            $user = User::find($userId);
            if (!$user) {
                return false;
            }
            
            // 检查用户状态
            if ($user->is_disable) {
                Log::warning('[分成权限] 用户已被禁用', [
                    'user_id' => $userId,
                    'action' => $action
                ]);
                return false;
            }
            
            // 检查操作权限
            $hasPermission = self::validateUserAction($user, $action, $context);
            
            // 记录权限检查
            if (!$hasPermission) {
                Log::warning('[分成权限] 用户权限不足', [
                    'user_id' => $userId,
                    'action' => $action,
                    'context' => $context,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            
            return $hasPermission;
            
        } catch (\Exception $e) {
            Log::error('[分成权限] 用户权限检查异常', [
                'user_id' => $userId,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 🔒 安全修复：检查分成配置修改权限
     * @param int $adminId 管理员ID
     * @param array $configData 配置数据
     * @return bool
     * @security_fix 2025/08/02 - 配置修改权限
     */
    public static function canModifyConfig(int $adminId, array $configData = []): bool
    {
        // 基础权限检查
        if (!self::checkAdminPermission($adminId, self::PERM_MODIFY_CONFIG)) {
            return false;
        }
        
        // 🔒 敏感配置变更需要额外验证
        if (self::isSensitiveConfigChange($configData)) {
            return self::checkSensitiveConfigPermission($adminId, $configData);
        }
        
        return true;
    }
    
    /**
     * 🔒 安全修复：检查分成数据查看权限
     * @param int $adminId 管理员ID
     * @param int $targetUserId 目标用户ID
     * @return bool
     * @security_fix 2025/08/02 - 数据查看权限
     */
    public static function canViewRevenue(int $adminId, int $targetUserId = 0): bool
    {
        // 查看所有用户分成数据
        if (self::checkAdminPermission($adminId, self::PERM_VIEW_ALL_REVENUE)) {
            return true;
        }
        
        // 查看自己的分成数据
        if ($targetUserId > 0 && self::checkAdminPermission($adminId, self::PERM_VIEW_OWN_REVENUE)) {
            $admin = Admin::find($adminId);
            return $admin && $admin->id == $targetUserId;
        }
        
        return false;
    }
    
    /**
     * 🔒 安全修复：检查分成结算权限
     * @param int $adminId 管理员ID
     * @param array $revenueIds 分成记录ID数组
     * @return bool
     * @security_fix 2025/08/02 - 结算权限控制
     */
    public static function canSettleRevenue(int $adminId, array $revenueIds = []): bool
    {
        // 基础权限检查
        if (!self::checkAdminPermission($adminId, self::PERM_SETTLE_REVENUE)) {
            return false;
        }
        
        // 🔒 批量结算数量限制
        if (count($revenueIds) > 1000) {
            Log::warning('[分成权限] 批量结算数量超限', [
                'admin_id' => $adminId,
                'count' => count($revenueIds)
            ]);
            return false;
        }
        
        // 🔒 结算频率限制
        return self::checkSettleFrequency($adminId);
    }
    
    /**
     * 🔒 安全修复：验证具体权限
     * @param Admin $admin 管理员对象
     * @param string $permission 权限标识
     * @param array $context 上下文
     * @return bool
     * @security_fix 2025/08/02 - 权限验证逻辑
     */
    private static function validateSpecificPermission(Admin $admin, string $permission, array $context): bool
    {
        // 这里应该根据实际的权限系统实现
        // 简化实现：基于角色ID判断
        $rolePermissions = [
            1 => ['*'], // 超级管理员
            2 => [self::PERM_VIEW_ALL_REVENUE, self::PERM_SETTLE_REVENUE], // 财务管理员
            3 => [self::PERM_VIEW_OWN_REVENUE], // 普通管理员
        ];
        
        $permissions = $rolePermissions[$admin->role_id] ?? [];
        
        return in_array('*', $permissions) || in_array($permission, $permissions);
    }
    
    /**
     * 🔒 安全修复：验证用户操作权限
     * @param User $user 用户对象
     * @param string $action 操作类型
     * @param array $context 上下文
     * @return bool
     * @security_fix 2025/08/02 - 用户操作权限
     */
    private static function validateUserAction(User $user, string $action, array $context): bool
    {
        switch ($action) {
            case self::ACTION_VIEW:
                // 用户只能查看自己的分成记录
                $targetUserId = $context['target_user_id'] ?? 0;
                return $targetUserId == $user->id;
                
            case self::ACTION_EXPORT:
                // 导出需要VIP权限
                return $user->is_vip;
                
            default:
                return false;
        }
    }
    
    /**
     * 🔒 安全修复：检查是否为敏感配置变更
     * @param array $configData 配置数据
     * @return bool
     * @security_fix 2025/08/02 - 敏感配置识别
     */
    private static function isSensitiveConfigChange(array $configData): bool
    {
        $sensitiveKeys = ['share_ratio', 'platform_ratio', 'min_revenue', 'max_revenue'];
        
        foreach ($sensitiveKeys as $key) {
            if (isset($configData[$key])) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 🔒 安全修复：检查敏感配置权限
     * @param int $adminId 管理员ID
     * @param array $configData 配置数据
     * @return bool
     * @security_fix 2025/08/02 - 敏感配置权限
     */
    private static function checkSensitiveConfigPermission(int $adminId, array $configData): bool
    {
        // 敏感配置变更需要超级管理员权限
        $admin = Admin::find($adminId);
        if (!$admin || $admin->role_id != 1) {
            Log::warning('[分成权限] 敏感配置变更权限不足', [
                'admin_id' => $adminId,
                'role_id' => $admin->role_id ?? 'unknown',
                'config_keys' => array_keys($configData)
            ]);
            return false;
        }
        
        // 🔒 配置变更频率限制
        $cacheKey = "config_change_freq:{$adminId}";
        $changeCount = Cache::get($cacheKey, 0);
        
        if ($changeCount >= 10) { // 每小时最多10次配置变更
            Log::warning('[分成权限] 配置变更频率超限', [
                'admin_id' => $adminId,
                'change_count' => $changeCount
            ]);
            return false;
        }
        
        Cache::set($cacheKey, $changeCount + 1, 3600);
        
        return true;
    }
    
    /**
     * 🔒 安全修复：检查结算频率
     * @param int $adminId 管理员ID
     * @return bool
     * @security_fix 2025/08/02 - 结算频率限制
     */
    private static function checkSettleFrequency(int $adminId): bool
    {
        $cacheKey = "settle_freq:{$adminId}";
        $settleCount = Cache::get($cacheKey, 0);
        
        // 每小时最多结算50次
        if ($settleCount >= 50) {
            Log::warning('[分成权限] 结算频率超限', [
                'admin_id' => $adminId,
                'settle_count' => $settleCount
            ]);
            return false;
        }
        
        Cache::set($cacheKey, $settleCount + 1, 3600);
        
        return true;
    }
    
    /**
     * 🔒 安全修复：记录权限违规行为
     * @param int $adminId 管理员ID
     * @param string $action 违规行为
     * @param array $context 上下文
     * @security_fix 2025/08/02 - 安全审计
     */
    public static function logSecurityViolation(int $adminId, string $action, array $context = []): void
    {
        Log::error('[分成权限] 安全违规行为', [
            'admin_id' => $adminId,
            'action' => $action,
            'context' => $context,
            'ip' => request()->ip() ?? 'unknown',
            'user_agent' => request()->header('User-Agent') ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        // 可以在这里添加更多的安全响应措施
        // 如：临时锁定账号、发送告警通知等
    }
}
