<?php

namespace app\common\service;

use app\common\model\kb\KbRobotRecord;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\model\user\User;
use app\common\model\user\UserAccountLog;
use app\common\enum\user\AccountLogEnum;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use Exception;

/**
 * 🚀 优化的批量分成服务
 * @notes 针对大并发场景优化的定时任务分成实现
 * @performance_optimized 2025/08/02 - 大并发场景优化
 */
class OptimizedBatchRevenueService
{
    // 性能配置
    private const DEFAULT_BATCH_SIZE = 1000;
    private const MAX_BATCH_SIZE = 5000;
    private const PARALLEL_WORKERS = 3;
    private const MEMORY_LIMIT_MB = 512;
    
    // 处理统计
    private static array $stats = [
        'total_processed' => 0,
        'total_success' => 0,
        'total_failed' => 0,
        'start_time' => 0,
        'end_time' => 0
    ];
    
    /**
     * 🚀 主要批量处理入口
     * @param int $limit 处理限制
     * @param bool $parallel 是否并行处理
     * @return array 处理结果
     */
    public static function batchProcessRevenue(int $limit = 0, bool $parallel = false): array
    {
        self::$stats['start_time'] = microtime(true);
        
        try {
            // 获取待处理记录
            $pendingRecords = self::getPendingRecords($limit);
            
            if (empty($pendingRecords)) {
                return self::getProcessingStats();
            }
            
            Log::info('[批量分成] 开始处理', [
                'pending_count' => count($pendingRecords),
                'parallel' => $parallel,
                'memory_usage' => self::getMemoryUsage()
            ]);
            
            // 选择处理策略
            if ($parallel && count($pendingRecords) > 2000) {
                return self::parallelProcess($pendingRecords);
            } else {
                return self::sequentialProcess($pendingRecords);
            }
            
        } catch (Exception $e) {
            Log::error('[批量分成] 处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return self::getProcessingStats();
        } finally {
            self::$stats['end_time'] = microtime(true);
        }
    }
    
    /**
     * 🚀 获取待处理记录
     * @param int $limit 限制数量
     * @return array
     */
    private static function getPendingRecords(int $limit): array
    {
        // 智能批次大小计算
        $batchSize = self::calculateOptimalBatchSize($limit);
        
        // 获取待分成的对话记录
        $records = KbRobotRecord::where([
            'revenue_status' => 0,  // 未分成
            ['square_id', '>', 0],  // 有分享者
            ['total_cost', '>', 0]  // 有费用
        ])
        ->field('id,user_id,square_id,robot_id,total_cost,flows,tokens,model_id,create_time')
        ->limit($batchSize)
        ->order('id ASC')
        ->select()
        ->toArray();
        
        Log::info('[批量分成] 获取待处理记录', [
            'requested_limit' => $limit,
            'batch_size' => $batchSize,
            'found_records' => count($records)
        ]);
        
        return $records;
    }
    
    /**
     * 🚀 计算最优批次大小
     * @param int $requestedLimit 请求的限制
     * @return int
     */
    private static function calculateOptimalBatchSize(int $requestedLimit): int
    {
        if ($requestedLimit > 0) {
            return min($requestedLimit, self::MAX_BATCH_SIZE);
        }
        
        // 基于系统负载动态调整
        $memoryUsage = self::getMemoryUsage();
        $loadAverage = sys_getloadavg()[0] ?? 1.0;
        
        if ($memoryUsage > 80 || $loadAverage > 2.0) {
            return 500; // 高负载时减小批次
        } elseif ($memoryUsage < 50 && $loadAverage < 1.0) {
            return self::MAX_BATCH_SIZE; // 低负载时增大批次
        } else {
            return self::DEFAULT_BATCH_SIZE; // 默认批次
        }
    }
    
    /**
     * 🚀 顺序处理
     * @param array $records 记录数组
     * @return array
     */
    private static function sequentialProcess(array $records): array
    {
        $batchSize = 200; // 每批处理200条
        $batches = array_chunk($records, $batchSize);
        
        foreach ($batches as $batchIndex => $batch) {
            $batchResult = self::processBatch($batch, $batchIndex + 1);
            
            // 更新统计
            self::$stats['total_processed'] += $batchResult['processed'];
            self::$stats['total_success'] += $batchResult['success'];
            self::$stats['total_failed'] += $batchResult['failed'];
            
            // 内存管理
            if (self::getMemoryUsage() > self::MEMORY_LIMIT_MB) {
                gc_collect_cycles();
                Log::info('[批量分成] 执行内存回收', [
                    'batch' => $batchIndex + 1,
                    'memory_before' => self::getMemoryUsage()
                ]);
            }
            
            // 适当休息，避免系统过载
            if (count($batches) > 10 && ($batchIndex + 1) % 10 == 0) {
                usleep(50000); // 休息50ms
            }
        }
        
        return self::getProcessingStats();
    }
    
    /**
     * 🚀 并行处理
     * @param array $records 记录数组
     * @return array
     */
    private static function parallelProcess(array $records): array
    {
        $workerSize = ceil(count($records) / self::PARALLEL_WORKERS);
        $workers = array_chunk($records, $workerSize);
        
        Log::info('[批量分成] 启动并行处理', [
            'total_records' => count($records),
            'workers' => count($workers),
            'worker_size' => $workerSize
        ]);
        
        // 模拟并行处理（实际实现可使用进程或协程）
        foreach ($workers as $workerIndex => $workerRecords) {
            $workerResult = self::processWorker($workerRecords, $workerIndex + 1);
            
            // 合并统计
            self::$stats['total_processed'] += $workerResult['processed'];
            self::$stats['total_success'] += $workerResult['success'];
            self::$stats['total_failed'] += $workerResult['failed'];
        }
        
        return self::getProcessingStats();
    }
    
    /**
     * 🚀 处理单个批次
     * @param array $batch 批次记录
     * @param int $batchNumber 批次号
     * @return array
     */
    private static function processBatch(array $batch, int $batchNumber): array
    {
        $result = ['processed' => 0, 'success' => 0, 'failed' => 0];
        $startTime = microtime(true);
        
        try {
            Db::startTrans();
            
            foreach ($batch as $record) {
                $result['processed']++;
                
                try {
                    // 使用安全分成服务处理单条记录
                    $success = SecureRevenueService::processRecord($record);
                    
                    if ($success) {
                        $result['success']++;
                    } else {
                        $result['failed']++;
                    }
                    
                } catch (Exception $e) {
                    $result['failed']++;
                    Log::error('[批量分成] 单条记录处理失败', [
                        'record_id' => $record['id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            Db::commit();
            
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);
            
            Log::info('[批量分成] 批次处理完成', [
                'batch_number' => $batchNumber,
                'processed' => $result['processed'],
                'success' => $result['success'],
                'failed' => $result['failed'],
                'processing_time' => $processingTime . 'ms',
                'avg_time_per_record' => round($processingTime / $result['processed'], 2) . 'ms'
            ]);
            
        } catch (Exception $e) {
            Db::rollback();
            
            Log::error('[批量分成] 批次处理失败', [
                'batch_number' => $batchNumber,
                'error' => $e->getMessage()
            ]);
            
            // 批次失败时，尝试单条处理
            $result = self::fallbackSingleProcess($batch, $batchNumber);
        }
        
        return $result;
    }
    
    /**
     * 🚀 处理工作进程
     * @param array $records 记录数组
     * @param int $workerNumber 工作进程号
     * @return array
     */
    private static function processWorker(array $records, int $workerNumber): array
    {
        $result = ['processed' => 0, 'success' => 0, 'failed' => 0];
        $startTime = microtime(true);
        
        Log::info('[批量分成] 工作进程开始', [
            'worker' => $workerNumber,
            'records' => count($records)
        ]);
        
        // 分批处理工作进程的记录
        $batches = array_chunk($records, 100);
        
        foreach ($batches as $batchIndex => $batch) {
            $batchResult = self::processBatch($batch, $batchIndex + 1);
            
            $result['processed'] += $batchResult['processed'];
            $result['success'] += $batchResult['success'];
            $result['failed'] += $batchResult['failed'];
        }
        
        $processingTime = round((microtime(true) - $startTime) * 1000, 2);
        
        Log::info('[批量分成] 工作进程完成', [
            'worker' => $workerNumber,
            'processed' => $result['processed'],
            'success' => $result['success'],
            'failed' => $result['failed'],
            'processing_time' => $processingTime . 'ms'
        ]);
        
        return $result;
    }
    
    /**
     * 🚀 回退单条处理
     * @param array $batch 批次记录
     * @param int $batchNumber 批次号
     * @return array
     */
    private static function fallbackSingleProcess(array $batch, int $batchNumber): array
    {
        $result = ['processed' => 0, 'success' => 0, 'failed' => 0];
        
        Log::warning('[批量分成] 启动回退单条处理', [
            'batch_number' => $batchNumber,
            'records' => count($batch)
        ]);
        
        foreach ($batch as $record) {
            $result['processed']++;
            
            try {
                $success = SecureRevenueService::processRecord($record);
                
                if ($success) {
                    $result['success']++;
                } else {
                    $result['failed']++;
                }
                
            } catch (Exception $e) {
                $result['failed']++;
                Log::error('[批量分成] 单条回退处理失败', [
                    'record_id' => $record['id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return $result;
    }
    
    /**
     * 🚀 获取处理统计
     * @return array
     */
    private static function getProcessingStats(): array
    {
        $executionTime = (self::$stats['end_time'] - self::$stats['start_time']) * 1000; // ms
        $recordsPerSecond = $executionTime > 0 ? round(self::$stats['total_processed'] / ($executionTime / 1000), 0) : 0;
        
        return [
            'total_processed' => self::$stats['total_processed'],
            'total_success' => self::$stats['total_success'],
            'total_failed' => self::$stats['total_failed'],
            'success_rate' => self::$stats['total_processed'] > 0 ? 
                round((self::$stats['total_success'] / self::$stats['total_processed']) * 100, 2) : 0,
            'execution_time_ms' => round($executionTime, 2),
            'records_per_second' => $recordsPerSecond,
            'avg_time_per_record_ms' => self::$stats['total_processed'] > 0 ? 
                round($executionTime / self::$stats['total_processed'], 2) : 0,
            'memory_usage_mb' => self::getMemoryUsage(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 获取内存使用情况
     * @return float MB
     */
    private static function getMemoryUsage(): float
    {
        return round(memory_get_usage(true) / 1024 / 1024, 2);
    }
    
    /**
     * 🚀 智能调度处理
     * @return array
     */
    public static function smartScheduleProcess(): array
    {
        // 获取待处理数量
        $pendingCount = KbRobotRecord::where([
            'revenue_status' => 0,
            ['square_id', '>', 0],
            ['total_cost', '>', 0]
        ])->count();
        
        Log::info('[批量分成] 智能调度开始', [
            'pending_count' => $pendingCount,
            'memory_usage' => self::getMemoryUsage(),
            'load_average' => sys_getloadavg()[0] ?? 'unknown'
        ]);
        
        if ($pendingCount == 0) {
            return ['message' => '无待处理记录'];
        }
        
        // 根据待处理数量选择策略
        if ($pendingCount > 50000) {
            // 大量数据：并行处理
            return self::batchProcessRevenue(10000, true);
        } elseif ($pendingCount > 10000) {
            // 中等数据：大批次处理
            return self::batchProcessRevenue(5000, false);
        } elseif ($pendingCount > 1000) {
            // 少量数据：标准处理
            return self::batchProcessRevenue(2000, false);
        } else {
            // 极少数据：小批次处理
            return self::batchProcessRevenue(500, false);
        }
    }
}
