<?php
namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;

/**
 * 优化的缓存服务基类
 * 提供多级缓存、防雪崩、防穿透等功能
 */
class OptimizedCacheService
{
    // 缓存层级
    const LEVEL_MEMORY = 1;  // 内存缓存
    const LEVEL_REDIS = 2;   // Redis缓存
    
    // 默认过期时间
    const DEFAULT_TTL = 3600;
    const RANDOM_TTL_RANGE = 600; // 随机时间范围，防止雪崩
    
    // 内存缓存存储
    private static $memoryCache = [];
    private static $memoryCacheExpire = [];
    
    /**
     * 多级缓存获取
     * @param string $key 缓存键
     * @param mixed $default 默认值
     * @param array $levels 缓存层级
     * @return mixed
     */
    public static function get($key, $default = null, $levels = [self::LEVEL_MEMORY, self::LEVEL_REDIS])
    {
        foreach ($levels as $level) {
            $value = self::getFromLevel($key, $level);
            if ($value !== null) {
                // 如果从低级缓存获取到数据，写入高级缓存
                if ($level > self::LEVEL_MEMORY) {
                    self::setToLevel($key, $value, self::LEVEL_MEMORY, 300);
                }
                return $value;
            }
        }
        return $default;
    }
    
    /**
     * 多级缓存设置
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 过期时间
     * @return bool
     */
    public static function set($key, $value, $ttl = self::DEFAULT_TTL)
    {
        // 添加随机时间防止缓存雪崩
        $randomTtl = $ttl + rand(0, self::RANDOM_TTL_RANGE);
        
        // 写入所有缓存层级
        $memoryResult = self::setToLevel($key, $value, self::LEVEL_MEMORY, min($randomTtl, 1800)); // 内存缓存最多30分钟
        $redisResult = self::setToLevel($key, $value, self::LEVEL_REDIS, $randomTtl);
        
        return $memoryResult || $redisResult;
    }
    
    /**
     * 从指定层级获取缓存
     * @param string $key 缓存键
     * @param int $level 缓存层级
     * @return mixed|null
     */
    private static function getFromLevel($key, $level)
    {
        try {
            switch ($level) {
                case self::LEVEL_MEMORY:
                    return self::getFromMemory($key);
                case self::LEVEL_REDIS:
                    return Cache::get($key);
                default:
                    return null;
            }
        } catch (\Throwable $e) {
            Log::error("缓存读取失败", [
                "key" => $key, 
                "level" => $level, 
                "error" => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 写入指定层级缓存
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $level 缓存层级
     * @param int $ttl 过期时间
     * @return bool
     */
    private static function setToLevel($key, $value, $level, $ttl)
    {
        try {
            switch ($level) {
                case self::LEVEL_MEMORY:
                    return self::setToMemory($key, $value, $ttl);
                case self::LEVEL_REDIS:
                    return Cache::set($key, $value, $ttl);
            }
        } catch (\Throwable $e) {
            Log::error("缓存写入失败", [
                "key" => $key, 
                "level" => $level, 
                "error" => $e->getMessage()
            ]);
        }
        return false;
    }
    
    /**
     * 从内存缓存获取
     * @param string $key
     * @return mixed|null
     */
    private static function getFromMemory($key)
    {
        // 检查是否过期
        if (isset(self::$memoryCacheExpire[$key]) && self::$memoryCacheExpire[$key] < time()) {
            unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
            return null;
        }
        
        return self::$memoryCache[$key] ?? null;
    }
    
    /**
     * 写入内存缓存
     * @param string $key
     * @param mixed $value
     * @param int $ttl
     * @return bool
     */
    private static function setToMemory($key, $value, $ttl)
    {
        // 防止内存缓存过大
        if (count(self::$memoryCache) > 1000) {
            self::cleanupMemoryCache();
        }
        
        self::$memoryCache[$key] = $value;
        self::$memoryCacheExpire[$key] = time() + $ttl;
        
        return true;
    }
    
    /**
     * 清理过期的内存缓存
     */
    private static function cleanupMemoryCache()
    {
        $now = time();
        foreach (self::$memoryCacheExpire as $key => $expireTime) {
            if ($expireTime < $now) {
                unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
            }
        }
        
        // 如果还是太多，删除最老的一半
        if (count(self::$memoryCache) > 500) {
            $keys = array_keys(self::$memoryCache);
            $keysToDelete = array_slice($keys, 0, count($keys) / 2);
            foreach ($keysToDelete as $key) {
                unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
            }
        }
    }
    
    /**
     * 缓存预热
     * @param array $data 预热数据
     * @param string $keyPrefix 键前缀
     * @return int 预热成功数量
     */
    public static function warmup($data, $keyPrefix = "")
    {
        $warmed = 0;
        foreach ($data as $key => $value) {
            $cacheKey = $keyPrefix . $key;
            if (self::set($cacheKey, $value)) {
                $warmed++;
            }
        }
        return $warmed;
    }
    
    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool
     */
    public static function delete($key)
    {
        // 删除内存缓存
        unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
        
        // 删除Redis缓存
        try {
            return Cache::delete($key);
        } catch (\Throwable $e) {
            Log::error("缓存删除失败", ["key" => $key, "error" => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 批量删除缓存
     * @param string $pattern 匹配模式
     * @return int 删除数量
     */
    public static function deletePattern($pattern)
    {
        $deleted = 0;
        
        try {
            // 删除内存缓存中匹配的键
            foreach (array_keys(self::$memoryCache) as $key) {
                if (fnmatch($pattern, $key)) {
                    unset(self::$memoryCache[$key], self::$memoryCacheExpire[$key]);
                    $deleted++;
                }
            }
            
            // Redis模式删除
            if (method_exists(Cache::store("redis"), 'keys')) {
                $keys = Cache::store("redis")->keys($pattern);
                if (!empty($keys)) {
                    Cache::store("redis")->del($keys);
                    $deleted += count($keys);
                }
            }
        } catch (\Throwable $e) {
            Log::error("批量删除缓存失败", ["pattern" => $pattern, "error" => $e->getMessage()]);
        }
        
        return $deleted;
    }
    
    /**
     * 获取缓存统计信息
     * @return array
     */
    public static function getStats()
    {
        return [
            'memory_cache_count' => count(self::$memoryCache),
            'memory_cache_size' => strlen(serialize(self::$memoryCache)),
            'redis_connected' => self::isRedisConnected()
        ];
    }
    
    /**
     * 检查Redis连接状态
     * @return bool
     */
    private static function isRedisConnected()
    {
        try {
            Cache::store("redis")->get("__test_connection__");
            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }
} 