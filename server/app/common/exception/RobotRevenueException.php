<?php

namespace app\common\exception;

use Exception;

/**
 * 智能体分成收益异常类
 */
class RobotRevenueException extends Exception
{
    // 异常代码常量
    public const TABLE_NOT_EXISTS = 1001;
    public const CONFIG_DISABLED = 1002;
    public const INVALID_PARAMS = 1003;
    public const DUPLICATE_PROCESS = 1004;
    public const INSUFFICIENT_AMOUNT = 1005;
    public const USER_NOT_EXISTS = 1006;
    public const ROBOT_NOT_EXISTS = 1007;
    public const SQUARE_NOT_EXISTS = 1008;
    public const SELF_REVENUE = 1009;
    public const CALCULATION_ERROR = 1010;
    public const DATABASE_ERROR = 1011;

    /**
     * 异常消息映射
     */
    protected static array $messageMap = [
        self::TABLE_NOT_EXISTS => '分成收益相关表不存在或功能未启用',
        self::CONFIG_DISABLED => '分成收益功能已禁用',
        self::INVALID_PARAMS => '分成收益处理参数无效',
        self::DUPLICATE_PROCESS => '该记录已经处理过分成收益',
        self::INSUFFICIENT_AMOUNT => '消耗金额低于最小分成标准',
        self::USER_NOT_EXISTS => '用户不存在',
        self::ROBOT_NOT_EXISTS => '智能体不存在',
        self::SQUARE_NOT_EXISTS => '智能体广场信息不存在',
        self::SELF_REVENUE => '不能给自己分成',
        self::CALCULATION_ERROR => '分成金额计算异常',
        self::DATABASE_ERROR => '数据库操作失败',
    ];

    /**
     * 获取格式化的异常消息
     */
    public function getFormattedMessage(): string
    {
        $baseMessage = self::$messageMap[$this->code] ?? '未知错误';
        return $this->message ? "{$baseMessage}: {$this->message}" : $baseMessage;
    }

    /**
     * 创建表不存在异常
     */
    public static function tableNotExists(string $details = ''): self
    {
        return new self($details, self::TABLE_NOT_EXISTS);
    }

    /**
     * 创建配置禁用异常
     */
    public static function configDisabled(string $details = ''): self
    {
        return new self($details, self::CONFIG_DISABLED);
    }

    /**
     * 创建参数无效异常
     */
    public static function invalidParams(string $details = ''): self
    {
        return new self($details, self::INVALID_PARAMS);
    }

    /**
     * 创建重复处理异常
     */
    public static function duplicateProcess(int $recordId): self
    {
        return new self("记录ID: {$recordId}", self::DUPLICATE_PROCESS);
    }

    /**
     * 创建金额不足异常
     */
    public static function insufficientAmount(float $amount, float $minAmount): self
    {
        return new self("当前金额: {$amount}, 最小金额: {$minAmount}", self::INSUFFICIENT_AMOUNT);
    }

    /**
     * 创建用户不存在异常
     */
    public static function userNotExists(int $userId): self
    {
        return new self("用户ID: {$userId}", self::USER_NOT_EXISTS);
    }

    /**
     * 创建智能体不存在异常
     */
    public static function robotNotExists(int $robotId): self
    {
        return new self("智能体ID: {$robotId}", self::ROBOT_NOT_EXISTS);
    }

    /**
     * 创建广场信息不存在异常
     */
    public static function squareNotExists(int $squareId): self
    {
        return new self("广场ID: {$squareId}", self::SQUARE_NOT_EXISTS);
    }

    /**
     * 创建自我分成异常
     */
    public static function selfRevenue(int $userId): self
    {
        return new self("用户ID: {$userId}", self::SELF_REVENUE);
    }

    /**
     * 创建计算错误异常
     */
    public static function calculationError(string $details): self
    {
        return new self($details, self::CALCULATION_ERROR);
    }

    /**
     * 创建数据库错误异常
     */
    public static function databaseError(string $details, \Throwable $previous = null): self
    {
        return new self($details, self::DATABASE_ERROR, $previous);
    }

    /**
     * 获取脱敏的异常信息（用于日志）
     */
    public function getSanitizedContext(): array
    {
        return [
            'exception_class' => static::class,
            'exception_code' => $this->code,
            'exception_message' => $this->getFormattedMessage(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace_hash' => md5($this->getTraceAsString()), // 使用hash代替完整堆栈
        ];
    }
} 