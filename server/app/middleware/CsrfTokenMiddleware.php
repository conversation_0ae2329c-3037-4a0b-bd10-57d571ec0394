<?php

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Session;
use think\facade\Log;

/**
 * CSRF令牌验证中间件
 */
class CsrfTokenMiddleware
{
    /**
     * 需要CSRF验证的HTTP方法
     */
    protected array $verifyMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];
    
    /**
     * 🔒 安全修复：排除CSRF验证的路由 - 已缩小排除范围
     * @notes 只排除必要的路由，提高CSRF保护覆盖率
     * @security_fix 2025/08/02 - 完善CSRF防护
     */
    protected array $except = [
        // 用户认证相关（需要排除以避免循环依赖）
        'api/user/login',
        'api/user/register',
        'api/user/logout',

        // 具体的webhook路由（避免使用通配符）
        'api/webhook/payment/notify',
        'api/webhook/sms/callback',
        'api/webhook/email/callback',

        // 公开API（不需要CSRF保护）
        'api/public/config',
        'api/public/captcha',

        // 第三方回调（特定路径）
        'api/callback/oauth/wechat',
        'api/callback/oauth/qq',
        'api/callback/payment/alipay',
        'api/callback/payment/wechat',
    ];

    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 检查是否需要验证CSRF
        if ($this->shouldVerify($request)) {
            $this->verify($request);
        }

        return $next($request);
    }

    /**
     * 判断是否需要验证CSRF
     */
    protected function shouldVerify(Request $request): bool
    {
        // 只验证指定的HTTP方法
        if (!in_array($request->method(), $this->verifyMethods)) {
            return false;
        }

        // 检查是否在排除列表中
        foreach ($this->except as $pattern) {
            if ($this->matchesPattern($request->pathinfo(), $pattern)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 🔒 安全修复：验证CSRF令牌 - 已增强验证逻辑
     * @notes 增强CSRF令牌验证，添加更多安全检查
     * @security_fix 2025/08/02 - 强化CSRF防护
     */
    protected function verify(Request $request): void
    {
        $token = $this->getTokenFromRequest($request);
        $sessionToken = Session::get('csrf_token');

        // 基础令牌验证
        if (!$token || !$sessionToken || !hash_equals($sessionToken, $token)) {
            $this->logCsrfFailure($request, 'token_mismatch', $token, $sessionToken);
            abort(419, 'CSRF token mismatch');
        }

        // 🔒 安全修复：额外的安全检查

        // 1. 令牌时效性检查
        $tokenTimestamp = Session::get('csrf_token_timestamp');
        if ($tokenTimestamp && (time() - $tokenTimestamp) > 3600) {
            $this->logCsrfFailure($request, 'token_expired', $token, $sessionToken);
            Session::delete('csrf_token');
            Session::delete('csrf_token_timestamp');
            abort(419, 'CSRF token expired');
        }

        // 2. Referer检查（可选，针对敏感操作）
        if ($this->isSensitiveOperation($request)) {
            if (!$this->validateReferer($request)) {
                $this->logCsrfFailure($request, 'invalid_referer', $token, $sessionToken);
                abort(419, 'Invalid referer');
            }
        }

        // 3. 频率限制检查
        if (!$this->checkRateLimit($request)) {
            $this->logCsrfFailure($request, 'rate_limit_exceeded', $token, $sessionToken);
            abort(429, 'Too many requests');
        }
    }

    /**
     * 🔒 安全修复：记录CSRF验证失败
     * @param Request $request 请求对象
     * @param string $reason 失败原因
     * @param string|null $providedToken 提供的令牌
     * @param string|null $sessionToken 会话令牌
     */
    private function logCsrfFailure(Request $request, string $reason, ?string $providedToken, ?string $sessionToken): void
    {
        Log::warning('CSRF令牌验证失败', [
            'reason' => $reason,
            'ip' => $request->ip(),
            'url' => $request->url(),
            'method' => $request->method(),
            'user_agent' => $request->header('User-Agent'),
            'referer' => $request->header('Referer'),
            'token_provided' => !empty($providedToken),
            'token_length' => $providedToken ? strlen($providedToken) : 0,
            'session_token_exists' => !empty($sessionToken),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 🔒 安全修复：检查是否为敏感操作
     * @param Request $request 请求对象
     * @return bool
     */
    private function isSensitiveOperation(Request $request): bool
    {
        $sensitivePatterns = [
            '/admin\/.*\/delete/',
            '/admin\/.*\/update/',
            '/api\/.*\/transfer/',
            '/api\/.*\/payment/',
            '/api\/.*\/withdraw/',
            '/api\/user\/password/',
            '/api\/user\/email/',
            '/api\/user\/phone/',
        ];

        $path = $request->pathinfo();
        foreach ($sensitivePatterns as $pattern) {
            if (preg_match($pattern, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 🔒 安全修复：验证Referer头
     * @param Request $request 请求对象
     * @return bool
     */
    private function validateReferer(Request $request): bool
    {
        $referer = $request->header('Referer');
        if (!$referer) {
            return false;
        }

        $allowedHosts = [
            $request->host(),
            parse_url(config('app.app_host'), PHP_URL_HOST),
        ];

        $refererHost = parse_url($referer, PHP_URL_HOST);
        return in_array($refererHost, array_filter($allowedHosts));
    }

    /**
     * 🔒 安全修复：检查请求频率限制
     * @param Request $request 请求对象
     * @return bool
     */
    private function checkRateLimit(Request $request): bool
    {
        $ip = $request->ip();
        $cacheKey = 'csrf_rate_limit:' . $ip;
        $attempts = cache($cacheKey) ?: 0;

        if ($attempts >= 100) { // 每分钟最多100次CSRF验证
            return false;
        }

        cache($cacheKey, $attempts + 1, 60); // 1分钟过期
        return true;
    }

    /**
     * 从请求中获取CSRF令牌
     */
    protected function getTokenFromRequest(Request $request): ?string
    {
        // 优先从Header中获取
        $token = $request->header('X-CSRF-TOKEN');
        
        if (!$token) {
            // 从表单数据中获取
            $token = $request->post('_token');
        }
        
        if (!$token) {
            // 从GET参数中获取
            $token = $request->get('_token');
        }

        return $token;
    }

    /**
     * 匹配路由模式
     */
    protected function matchesPattern(string $path, string $pattern): bool
    {
        $pattern = str_replace('*', '.*', $pattern);
        return (bool) preg_match('#^' . $pattern . '$#', $path);
    }

    /**
     * 🔒 安全修复：生成CSRF令牌 - 已增强安全性
     * @notes 生成更安全的CSRF令牌，添加时间戳
     * @security_fix 2025/08/02 - 强化令牌生成
     */
    public static function generateToken(): string
    {
        // 生成64字节的随机令牌（更高安全性）
        $token = bin2hex(random_bytes(32));

        // 设置令牌和时间戳
        Session::set('csrf_token', $token);
        Session::set('csrf_token_timestamp', time());

        // 记录令牌生成
        Log::info('CSRF令牌生成', [
            'token_length' => strlen($token),
            'ip' => request()->ip() ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ]);

        return $token;
    }

    /**
     * 🔒 安全修复：刷新CSRF令牌
     * @notes 在敏感操作后刷新令牌，提高安全性
     * @return string 新的CSRF令牌
     */
    public static function refreshToken(): string
    {
        // 删除旧令牌
        Session::delete('csrf_token');
        Session::delete('csrf_token_timestamp');

        // 生成新令牌
        return self::generateToken();
    }

    /**
     * 🔒 安全修复：验证令牌是否有效
     * @param string $token 要验证的令牌
     * @return bool
     */
    public static function validateToken(string $token): bool
    {
        $sessionToken = Session::get('csrf_token');
        $tokenTimestamp = Session::get('csrf_token_timestamp');

        // 基础验证
        if (!$sessionToken || !hash_equals($sessionToken, $token)) {
            return false;
        }

        // 时效性验证
        if ($tokenTimestamp && (time() - $tokenTimestamp) > 3600) {
            Session::delete('csrf_token');
            Session::delete('csrf_token_timestamp');
            return false;
        }

        return true;
    }

    /**
     * 获取当前CSRF令牌
     */
    public static function getToken(): string
    {
        $token = Session::get('csrf_token');
        
        if (!$token) {
            $token = self::generateToken();
        }
        
        return $token;
    }

}