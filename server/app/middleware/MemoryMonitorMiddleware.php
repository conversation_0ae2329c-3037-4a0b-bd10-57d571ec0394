<?php

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Log;

/**
 * Docker环境内存监控中间件
 * 针对PHP 8.0和128MB容器限制优化
 */
class MemoryMonitorMiddleware
{
    // Docker环境内存阈值（适配128MB限制）
    private const WARNING_THRESHOLD = 50 * 1024 * 1024;  // 50MB
    private const CRITICAL_THRESHOLD = 80 * 1024 * 1024; // 80MB
    private const SINGLE_REQUEST_THRESHOLD = 10 * 1024 * 1024; // 10MB
    
    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 请求开始时的内存状态
        $startMemory = memory_get_usage(true);
        $startTime = microtime(true);
        
        // 预处理：清理过期对象和缓存
        $this->preRequestCleanup();
        
        /** @var Response $response */
        $response = $next($request);
        
        // 请求结束时的内存检查
        $endMemory = memory_get_usage(true);
        $endTime = microtime(true);
        $memoryUsed = $endMemory - $startMemory;
        $executionTime = ($endTime - $startTime) * 1000; // 毫秒
        
        // 记录请求内存使用情况
        $this->logMemoryUsage($request, $startMemory, $endMemory, $memoryUsed, $executionTime);
        
        // 内存警告处理
        if ($endMemory > self::WARNING_THRESHOLD) {
            $this->handleMemoryWarning($endMemory, $memoryUsed, $request);
        }
        
        // 智能垃圾回收
        if ($memoryUsed > self::SINGLE_REQUEST_THRESHOLD) {
            $this->performSmartGC($memoryUsed);
        }
        
        // 请求后清理
        $this->postRequestCleanup();
        
        return $response;
    }
    
    /**
     * 请求前清理
     */
    private function preRequestCleanup(): void
    {
        // 清理过期的服务对象池
        if (class_exists('\App\Common\Service\SimpleRevenueService')) {
            // 如果有静态缓存，清理过期项
            $reflection = new \ReflectionClass('\App\Common\Service\SimpleRevenueService');
            if ($reflection->hasProperty('configCache')) {
                $cacheProperty = $reflection->getProperty('configCache');
                $cacheProperty->setAccessible(true);
                $cache = $cacheProperty->getValue();
                
                // 如果缓存过大，清理一半
                if (is_array($cache) && count($cache) > 50) {
                    $cleanedCache = array_slice($cache, -25, null, true);
                    $cacheProperty->setValue(null, $cleanedCache);
                }
            }
        }
    }
    
    /**
     * 记录内存使用情况
     */
    private function logMemoryUsage(Request $request, int $startMemory, int $endMemory, int $memoryUsed, float $executionTime): void
    {
        // 只记录重要的请求或内存使用较大的请求
        if ($memoryUsed > 5 * 1024 * 1024 || $executionTime > 1000) { // >5MB或>1秒
            Log::record([
                'type' => 'memory_monitor',
                'path' => $request->pathinfo(),
                'method' => $request->method(),
                'start_memory_mb' => round($startMemory / 1024 / 1024, 2),
                'end_memory_mb' => round($endMemory / 1024 / 1024, 2),
                'memory_used_mb' => round($memoryUsed / 1024 / 1024, 2),
                'execution_time_ms' => round($executionTime, 2),
                'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'timestamp' => date('Y-m-d H:i:s')
            ], 'info');
        }
    }
    
    /**
     * 处理内存警告
     */
    private function handleMemoryWarning(int $currentMemory, int $memoryUsed, Request $request): void
    {
        $memoryMB = round($currentMemory / 1024 / 1024, 2);
        $usedMB = round($memoryUsed / 1024 / 1024, 2);
        
        Log::warning('内存使用警告', [
            'current_memory_mb' => $memoryMB,
            'request_memory_mb' => $usedMB,
            'path' => $request->pathinfo(),
            'method' => $request->method(),
            'get_params' => $request->get(),
            'post_size' => strlen($request->getInput())
        ]);
        
        // 紧急情况下的内存清理
        if ($currentMemory > self::CRITICAL_THRESHOLD) {
            $this->emergencyCleanup($request);
        }
    }
    
    /**
     * 智能垃圾回收
     */
    private function performSmartGC(int $memoryUsed): void
    {
        $beforeMemory = memory_get_usage(true);
        $cycles = gc_collect_cycles();
        $afterMemory = memory_get_usage(true);
        
        if ($cycles > 0) {
            $freedMemory = $beforeMemory - $afterMemory;
            Log::info('智能垃圾回收', [
                'cycles_collected' => $cycles,
                'memory_freed_mb' => round($freedMemory / 1024 / 1024, 2),
                'trigger_memory_mb' => round($memoryUsed / 1024 / 1024, 2),
                'current_memory_mb' => round($afterMemory / 1024 / 1024, 2)
            ]);
        }
    }
    
    /**
     * 紧急内存清理
     */
    private function emergencyCleanup(Request $request): void
    {
        Log::warning('执行紧急内存清理', [
            'path' => $request->pathinfo(),
            'memory_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
        ]);
        
        // 1. 强制垃圾回收
        gc_collect_cycles();
        
        // 2. 清理Think框架缓存
        if (class_exists('\think\facade\Cache')) {
            try {
                // 只清理内存缓存，不清理Redis缓存
                \think\facade\Cache::clear();
            } catch (\Throwable $e) {
                Log::error('缓存清理失败', ['error' => $e->getMessage()]);
            }
        }
        
        // 3. 清理会话数据（如果内存过高）
        if (memory_get_usage(true) > self::CRITICAL_THRESHOLD) {
            try {
                if (session_status() === PHP_SESSION_ACTIVE) {
                    session_write_close();
                }
            } catch (\Throwable $e) {
                Log::error('会话清理失败', ['error' => $e->getMessage()]);
            }
        }
        
        // 4. 记录清理效果
        $afterCleanup = memory_get_usage(true);
        Log::info('紧急清理完成', [
            'memory_after_cleanup_mb' => round($afterCleanup / 1024 / 1024, 2)
        ]);
    }
    
    /**
     * 请求后清理
     */
    private function postRequestCleanup(): void
    {
        // 清理一些全局变量和临时数据
        if (isset($GLOBALS['temp_data'])) {
            unset($GLOBALS['temp_data']);
        }
        
        // 定期执行完整垃圾回收（每10个请求）
        static $requestCount = 0;
        $requestCount++;
        
        if ($requestCount % 10 === 0) {
            gc_collect_cycles();
        }
    }
    
    /**
     * 获取内存使用统计
     */
    public static function getMemoryStats(): array
    {
        return [
            'current_memory_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'memory_limit' => ini_get('memory_limit'),
            'gc_enabled' => gc_enabled(),
            'php_version' => PHP_VERSION,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Docker环境内存健康检查
     */
    public static function healthCheck(): array
    {
        $currentMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        $status = 'healthy';
        if ($currentMemory > self::CRITICAL_THRESHOLD) {
            $status = 'critical';
        } elseif ($currentMemory > self::WARNING_THRESHOLD) {
            $status = 'warning';
        }
        
        return [
            'status' => $status,
            'current_memory_mb' => round($currentMemory / 1024 / 1024, 2),
            'peak_memory_mb' => round($peakMemory / 1024 / 1024, 2),
            'warning_threshold_mb' => round(self::WARNING_THRESHOLD / 1024 / 1024, 2),
            'critical_threshold_mb' => round(self::CRITICAL_THRESHOLD / 1024 / 1024, 2),
            'recommendations' => $status !== 'healthy' ? [
                'consider_increasing_memory_limit',
                'check_for_memory_leaks',
                'optimize_batch_processing',
                'enable_opcache'
            ] : []
        ];
    }
} 