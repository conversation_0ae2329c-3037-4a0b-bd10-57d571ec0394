<?php

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Cache;
use think\facade\Log;

/**
 * 速率限制中间件
 */
class RateLimitMiddleware
{
    /**
     * 默认配置
     */
    protected array $config = [
        'max_attempts' => 60,        // 最大尝试次数
        'decay_minutes' => 1,        // 时间窗口（分钟）
        'prefix' => 'rate_limit:',   // 缓存前缀
    ];

    /**
     * 特殊路由的限制规则
     */
    protected array $rules = [
        'api/robot/chat' => [
            'max_attempts' => 30,
            'decay_minutes' => 1,
        ],
        'api/robot/share' => [
            'max_attempts' => 10,
            'decay_minutes' => 1,
        ],
        'api/user/login' => [
            'max_attempts' => 5,
            'decay_minutes' => 15,
        ],
    ];

    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        $key = $this->resolveRequestSignature($request);
        $rule = $this->getRule($request);
        
        $maxAttempts = $rule['max_attempts'];
        $decayMinutes = $rule['decay_minutes'];
        
        $attempts = $this->attempts($key);
        
        if ($attempts >= $maxAttempts) {
            $this->handleTooManyAttempts($request, $key, $maxAttempts, $decayMinutes);
        }
        
        $this->hit($key, $decayMinutes);
        
        $response = $next($request);
        
        return $this->addHeaders(
            $response,
            $maxAttempts,
            $attempts + 1,
            $this->availableAt($key, $decayMinutes)
        );
    }

    /**
     * 获取请求标识
     */
    protected function resolveRequestSignature(Request $request): string
    {
        $route = $request->pathinfo();
        $ip = $request->ip();
        $userId = $request->header('user-id', '');
        
        // 如果有用户ID，使用用户ID，否则使用IP
        $identifier = $userId ?: $ip;
        
        return $this->config['prefix'] . md5($route . '|' . $identifier);
    }

    /**
     * 获取路由对应的限制规则
     */
    protected function getRule(Request $request): array
    {
        $route = $request->pathinfo();
        
        foreach ($this->rules as $pattern => $rule) {
            if ($this->matchesPattern($route, $pattern)) {
                return array_merge($this->config, $rule);
            }
        }
        
        return $this->config;
    }

    /**
     * 匹配路由模式
     */
    protected function matchesPattern(string $path, string $pattern): bool
    {
        $pattern = str_replace('*', '.*', $pattern);
        return (bool) preg_match('#^' . $pattern . '$#', $path);
    }

    /**
     * 获取当前尝试次数
     */
    protected function attempts(string $key): int
    {
        return Cache::get($key, 0);
    }

    /**
     * 增加尝试次数
     */
    protected function hit(string $key, int $decayMinutes): void
    {
        $attempts = $this->attempts($key) + 1;
        Cache::set($key, $attempts, $decayMinutes * 60);
    }

    /**
     * 处理请求过多的情况
     */
    protected function handleTooManyAttempts(Request $request, string $key, int $maxAttempts, int $decayMinutes): void
    {
        $retryAfter = $this->availableAt($key, $decayMinutes);
        
        Log::warning('速率限制触发', [
            'ip' => $request->ip(),
            'route' => $request->pathinfo(),
            'method' => $request->method(),
            'user_agent' => $request->header('User-Agent'),
            'user_id' => $request->header('user-id', ''),
            'max_attempts' => $maxAttempts,
            'retry_after' => $retryAfter
        ]);
        
        $response = Response::create([
            'code' => 429,
            'msg' => '请求过于频繁，请稍后再试',
            'data' => [
                'retry_after' => $retryAfter
            ]
        ], 'json', 429);
        
        $response->header([
            'Retry-After' => $retryAfter,
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => 0,
            'X-RateLimit-Reset' => time() + $retryAfter,
        ]);
        
        $response->send();
        exit;
    }

    /**
     * 计算下次可用时间
     */
    protected function availableAt(string $key, int $decayMinutes): int
    {
        $ttl = Cache::ttl($key);
        return $ttl > 0 ? $ttl : 0;
    }

    /**
     * 添加限制头部信息
     */
    protected function addHeaders(Response $response, int $maxAttempts, int $attempts, int $retryAfter): Response
    {
        $remaining = max(0, $maxAttempts - $attempts);
        
        $response->header([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remaining,
            'X-RateLimit-Reset' => time() + $retryAfter,
        ]);
        
        return $response;
    }

    /**
     * 清除限制（用于管理员重置）
     */
    public static function clear(string $identifier, string $route = '*'): bool
    {
        try {
            if ($route === '*') {
                // 清除用户所有限制
                $pattern = (new self())->config['prefix'] . '*' . $identifier . '*';
            } else {
                // 清除特定路由限制
                $key = (new self())->config['prefix'] . md5($route . '|' . $identifier);
                Cache::delete($key);
            }
            
            Log::info('速率限制已清除', [
                'identifier' => $identifier,
                'route' => $route
            ]);
            
            return true;
        } catch (\Throwable $e) {
            Log::error('清除速率限制失败', [
                'identifier' => $identifier,
                'route' => $route,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取用户当前限制状态
     */
    public static function getStatus(string $identifier, string $route): array
    {
        $instance = new self();
        $key = $instance->config['prefix'] . md5($route . '|' . $identifier);
        $rule = $instance->config; // 这里可以根据路由获取具体规则
        
        $attempts = Cache::get($key, 0);
        $ttl = Cache::ttl($key);
        
        return [
            'max_attempts' => $rule['max_attempts'],
            'current_attempts' => $attempts,
            'remaining' => max(0, $rule['max_attempts'] - $attempts),
            'reset_time' => $ttl > 0 ? time() + $ttl : 0,
            'is_limited' => $attempts >= $rule['max_attempts']
        ];
    }
} 