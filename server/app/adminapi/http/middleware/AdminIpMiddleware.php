<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

declare (strict_types=1);

namespace app\adminapi\http\middleware;

use Closure;
use think\facade\Config;

/**
 * 后台IP限制中间件
 * Class AdminIpMiddleware
 * @package app\adminapi\http\middleware
 */
class AdminIpMiddleware
{
    /**
     * @notes IP限制验证
     * @param $request
     * @param Closure $next
     * @return mixed
     * <AUTHOR>
     * @date 2024/12/26
     */
    public function handle($request, Closure $next): mixed
    {
        // 获取IP限制配置
        $ipConfig = Config::get('project.admin_login');
        
        // 如果IP限制功能未开启，直接通过
        if (empty($ipConfig['ip_restrictions']) || $ipConfig['ip_restrictions'] != 1) {
            return $next($request);
        }
        
        // 获取客户端IP
        $clientIp = $this->getClientIp($request);
        
        // 获取允许的IP列表
        $allowedIps = $ipConfig['allowed_ips'] ?? [];
        
        // 检查IP是否在允许列表中
        if (!$this->isIpAllowed($clientIp, $allowedIps)) {
            abort(404, '页面不存在');
        }
        
        return $next($request);
    }
    
    /**
     * @notes 获取客户端真实IP
     * @param $request
     * @return string
     * <AUTHOR>
     * @date 2024/12/26
     */
    private function getClientIp($request): string
    {
        // 优先获取代理服务器传递的真实IP
        $headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP', 
            'HTTP_CLIENT_IP',
        ];
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        // 如果没有代理IP，使用直接连接IP
        return $request->ip();
    }
    
    /**
     * @notes 检查IP是否被允许访问
     * @param string $clientIp
     * @param array $allowedIps
     * @return bool
     * <AUTHOR>
     * @date 2024/12/26
     */
    private function isIpAllowed(string $clientIp, array $allowedIps): bool
    {
        // 如果没有配置允许的IP列表，默认拒绝
        if (empty($allowedIps)) {
            return false;
        }
        
        foreach ($allowedIps as $allowedIp) {
            $allowedIp = trim($allowedIp);
            
            // 处理localhost特殊情况
            if ($allowedIp === 'localhost' && in_array($clientIp, ['127.0.0.1', '::1'])) {
                return true;
            }
            
            // 精确匹配
            if ($clientIp === $allowedIp) {
                return true;
            }
            
            // 支持IP段匹配 (例如: ***********/24)
            if (strpos($allowedIp, '/') !== false && $this->isIpInRange($clientIp, $allowedIp)) {
                return true;
            }
            
            // 支持通配符匹配 (例如: 192.168.1.*)
            if (strpos($allowedIp, '*') !== false) {
                $pattern = '/^' . str_replace(['.', '*'], ['\.', '\d+'], $allowedIp) . '$/';
                if (preg_match($pattern, $clientIp)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * @notes 检查IP是否在指定的IP段内
     * @param string $ip
     * @param string $range CIDR格式的IP段 (例如: ***********/24)
     * @return bool
     * <AUTHOR>
     * @date 2024/12/26
     */
    private function isIpInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return false;
        }
        
        list($rangeIp, $netmask) = explode('/', $range, 2);
        $rangeDecimal = ip2long($rangeIp);
        $ipDecimal = ip2long($ip);
        
        if ($rangeDecimal === false || $ipDecimal === false || !is_numeric($netmask)) {
            return false;
        }
        
        $netmask = (int)$netmask;
        if ($netmask < 0 || $netmask > 32) {
            return false;
        }
        
        $wildcardDecimal = pow(2, (32 - $netmask)) - 1;
        $netmaskDecimal = ~ $wildcardDecimal;
        
        return (($ipDecimal & $netmaskDecimal) == ($rangeDecimal & $netmaskDecimal));
    }
} 