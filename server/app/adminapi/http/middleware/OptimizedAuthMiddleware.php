<?php
/**
 * 优化版权限验证中间件
 * 实现中期优化: 三级缓存、性能监控、批量权限验证
 * 
 * 优化特性:
 * 1. 三级缓存架构: 进程缓存 -> Redis缓存 -> 数据库查询
 * 2. 智能缓存管理: 自动过期清理、大小限制、命中率统计
 * 3. 性能监控: 详细记录每次权限检查的性能数据
 * 4. 缓存预热: 支持预加载常用权限数据
 * 5. 错误处理: 完善的异常处理和降级机制
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */

declare(strict_types=1);

namespace app\adminapi\http\middleware;

use app\common\cache\AdminAuthCache;
use app\common\service\JsonService;
use Closure;
use think\helper\Str;
use think\facade\Cache;
use think\facade\Log;

class OptimizedAuthMiddleware
{
    // 进程级缓存
    private static array $processCache = [];
    private static array $processCacheTime = [];
    private static int $processCacheMaxSize = 1000;
    private static int $processCacheTTL = 300; // 5分钟
    
    // 性能统计
    private static array $performanceStats = [
        'total_checks' => 0,
        'process_cache_hits' => 0,
        'redis_cache_hits' => 0,
        'database_queries' => 0,
        'total_time' => 0,
        'average_time' => 0
    ];
    
    /**
     * 优化版权限验证处理器
     * 
     * @param $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next): mixed
    {
        $startTime = microtime(true);
        self::$performanceStats['total_checks']++;
        
        try {
            // 不登录访问，无需权限验证
            if ($request->controllerObject->isNotNeedLogin()) {
                return $next($request);
            }

            // 系统默认超级管理员，无需权限验证
            if (1 === $request->adminInfo['root']) {
                return $next($request);
            }

            $adminId = $request->adminInfo['admin_id'];
            $accessUri = strtolower($request->controller() . '/' . $request->action());
            
            // 执行权限验证
            $hasPermission = $this->checkPermissionOptimized($adminId, $accessUri);
            
            if (!$hasPermission) {
                return JsonService::fail('权限不足，无法访问或操作');
            }
            
            return $next($request);
            
        } finally {
            // 记录性能数据
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime) * 1000; // 转换为毫秒
            self::$performanceStats['total_time'] += $executionTime;
            self::$performanceStats['average_time'] = self::$performanceStats['total_time'] / self::$performanceStats['total_checks'];
            
            // 定期输出性能统计
            if (self::$performanceStats['total_checks'] % 100 === 0) {
                $this->logPerformanceStats();
            }
        }
    }
    
    /**
     * 优化版权限检查
     * 使用三级缓存提升性能
     * 
     * @param int $adminId
     * @param string $accessUri
     * @return bool
     */
    private function checkPermissionOptimized(int $adminId, string $accessUri): bool
    {
        // 第一级: 进程缓存检查 (最快)
        $processKey = "admin_perm_{$adminId}_{$accessUri}";
        if ($this->getFromProcessCache($processKey) !== null) {
            self::$performanceStats['process_cache_hits']++;
            return $this->getFromProcessCache($processKey);
        }
        
        // 第二级: Redis缓存检查 (中等速度)
        $redisKey = "auth:admin_permission:{$adminId}:{$accessUri}";
        $redisCacheResult = $this->getFromRedisCache($redisKey);
        if ($redisCacheResult !== null) {
            self::$performanceStats['redis_cache_hits']++;
            // 同步到进程缓存
            $this->setToProcessCache($processKey, $redisCacheResult);
            return $redisCacheResult;
        }
        
        // 第三级: 数据库查询 (最慢)
        self::$performanceStats['database_queries']++;
        $result = $this->checkPermissionFromDatabase($adminId, $accessUri);
        
        // 缓存结果到Redis和进程缓存
        $this->setToRedisCache($redisKey, $result, 900); // 15分钟
        $this->setToProcessCache($processKey, $result);
        
        return $result;
    }
    
    /**
     * 从数据库检查权限
     * 
     * @param int $adminId
     * @param string $accessUri
     * @return bool
     */
    private function checkPermissionFromDatabase(int $adminId, string $accessUri): bool
    {
        try {
            $adminAuthCache = new AdminAuthCache($adminId);

            // 获取全部路由 (使用批量缓存优化)
            $allUri = $this->getAllUriCached($adminAuthCache);
            $allUri = $this->formatUrl($allUri);

            // 判断该当前访问的uri是否存在，不存在无需验证
            if (!in_array($accessUri, $allUri)) {
                return true;
            }

            // 获取当前管理员拥有的路由权限 (使用批量缓存优化)
            $adminUris = $this->getAdminUriCached($adminAuthCache);
            $adminUris = $this->formatUrl($adminUris);

            return in_array($accessUri, $adminUris);
            
        } catch (\Exception $e) {
            Log::error('权限验证数据库查询失败: ' . $e->getMessage(), [
                'admin_id' => $adminId,
                'access_uri' => $accessUri
            ]);
            // 降级处理: 发生错误时拒绝访问
            return false;
        }
    }
    
    /**
     * 获取全部路由 (带缓存优化)
     * 
     * @param AdminAuthCache $adminAuthCache
     * @return array
     */
    private function getAllUriCached(AdminAuthCache $adminAuthCache): array
    {
        $cacheKey = 'auth:all_uri';
        
        // 尝试从进程缓存获取
        if ($this->getFromProcessCache($cacheKey) !== null) {
            return $this->getFromProcessCache($cacheKey);
        }
        
        // 尝试从Redis缓存获取
        $cached = $this->getFromRedisCache($cacheKey);
        if ($cached !== null) {
            $this->setToProcessCache($cacheKey, $cached);
            return $cached;
        }
        
        // 从数据库获取
        $result = $adminAuthCache->getAllUri();
        
        // 缓存结果
        $this->setToRedisCache($cacheKey, $result, 1800); // 30分钟
        $this->setToProcessCache($cacheKey, $result);
        
        return $result;
    }
    
    /**
     * 获取管理员权限路由 (带缓存优化)
     * 
     * @param AdminAuthCache $adminAuthCache
     * @return array
     */
    private function getAdminUriCached(AdminAuthCache $adminAuthCache): array
    {
        $cacheKey = "auth:admin_uri:{$adminAuthCache->adminId}";
        
        // 尝试从进程缓存获取
        if ($this->getFromProcessCache($cacheKey) !== null) {
            return $this->getFromProcessCache($cacheKey);
        }
        
        // 尝试从Redis缓存获取
        $cached = $this->getFromRedisCache($cacheKey);
        if ($cached !== null) {
            $this->setToProcessCache($cacheKey, $cached);
            return $cached;
        }
        
        // 从数据库获取
        $result = $adminAuthCache->getAdminUri() ?? [];
        
        // 缓存结果
        $this->setToRedisCache($cacheKey, $result, 600); // 10分钟
        $this->setToProcessCache($cacheKey, $result);
        
        return $result;
    }
    
    /**
     * 从进程缓存获取数据
     * 
     * @param string $key
     * @return mixed|null
     */
    private function getFromProcessCache(string $key)
    {
        // 检查缓存是否存在且未过期
        if (isset(self::$processCache[$key]) && isset(self::$processCacheTime[$key])) {
            if (time() - self::$processCacheTime[$key] < self::$processCacheTTL) {
                return self::$processCache[$key];
            } else {
                // 过期删除
                unset(self::$processCache[$key], self::$processCacheTime[$key]);
            }
        }
        
        return null;
    }
    
    /**
     * 设置进程缓存
     * 
     * @param string $key
     * @param mixed $value
     */
    private function setToProcessCache(string $key, $value): void
    {
        // 检查缓存大小限制
        if (count(self::$processCache) >= self::$processCacheMaxSize) {
            $this->cleanProcessCache();
        }
        
        self::$processCache[$key] = $value;
        self::$processCacheTime[$key] = time();
    }
    
    /**
     * 从Redis缓存获取数据
     * 
     * @param string $key
     * @return mixed|null
     */
    private function getFromRedisCache(string $key)
    {
        try {
            $cached = Cache::get($key);
            return $cached ?: null;
        } catch (\Exception $e) {
            Log::warning('Redis缓存读取失败: ' . $e->getMessage(), ['key' => $key]);
            return null;
        }
    }
    
    /**
     * 设置Redis缓存
     * 
     * @param string $key
     * @param mixed $value
     * @param int $ttl
     */
    private function setToRedisCache(string $key, $value, int $ttl): void
    {
        try {
            Cache::set($key, $value, $ttl);
        } catch (\Exception $e) {
            Log::warning('Redis缓存设置失败: ' . $e->getMessage(), [
                'key' => $key,
                'ttl' => $ttl
            ]);
        }
    }
    
    /**
     * 清理进程缓存
     * 移除最旧的50%数据
     */
    private function cleanProcessCache(): void
    {
        $currentTime = time();
        $cleaned = 0;
        $targetCleanCount = intval(self::$processCacheMaxSize * 0.5);
        
        // 按时间排序，删除最旧的数据
        asort(self::$processCacheTime);
        
        foreach (self::$processCacheTime as $key => $time) {
            if ($cleaned >= $targetCleanCount) {
                break;
            }
            
            unset(self::$processCache[$key], self::$processCacheTime[$key]);
            $cleaned++;
        }
        
        Log::info("进程缓存清理完成", [
            'cleaned_count' => $cleaned,
            'remaining_count' => count(self::$processCache)
        ]);
    }
    
    /**
     * 格式化URL
     * 
     * @param array $data
     * @return array
     */
    private function formatUrl(array $data): array
    {
        return array_map(function ($item) {
            return strtolower(Str::camel($item));
        }, $data);
    }
    
    /**
     * 记录性能统计
     */
    private function logPerformanceStats(): void
    {
        $stats = self::$performanceStats;
        $hitRate = $stats['total_checks'] > 0 ? 
            round((($stats['process_cache_hits'] + $stats['redis_cache_hits']) / $stats['total_checks']) * 100, 2) : 0;
        
        Log::info('认证中间件性能统计', [
            'total_checks' => $stats['total_checks'],
            'process_cache_hits' => $stats['process_cache_hits'],
            'redis_cache_hits' => $stats['redis_cache_hits'],
            'database_queries' => $stats['database_queries'],
            'cache_hit_rate' => $hitRate . '%',
            'average_time_ms' => round($stats['average_time'], 2),
            'process_cache_size' => count(self::$processCache)
        ]);
    }
    
    /**
     * 预热缓存
     * 预加载常用的权限数据
     * 
     * @param array $adminIds 管理员ID列表
     */
    public static function warmupCache(array $adminIds = []): void
    {
        Log::info('开始权限缓存预热', ['admin_count' => count($adminIds)]);
        
        foreach ($adminIds as $adminId) {
            try {
                $adminAuthCache = new AdminAuthCache($adminId);
                
                // 预加载管理员权限
                $adminUris = $adminAuthCache->getAdminUri() ?? [];
                $cacheKey = "auth:admin_uri:{$adminId}";
                Cache::set($cacheKey, $adminUris, 600);
                
                // 预加载到进程缓存
                self::$processCache[$cacheKey] = $adminUris;
                self::$processCacheTime[$cacheKey] = time();
                
                Log::info("管理员权限缓存预热完成", [
                    'admin_id' => $adminId,
                    'uri_count' => count($adminUris)
                ]);
                
            } catch (\Exception $e) {
                Log::error("管理员权限缓存预热失败", [
                    'admin_id' => $adminId,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        // 预加载全局路由
        try {
            $adminAuthCache = new AdminAuthCache(1); // 使用超级管理员获取全部路由
            $allUris = $adminAuthCache->getAllUri();
            $cacheKey = 'auth:all_uri';
            Cache::set($cacheKey, $allUris, 1800);
            
            self::$processCache[$cacheKey] = $allUris;
            self::$processCacheTime[$cacheKey] = time();
            
            Log::info("全局路由缓存预热完成", ['uri_count' => count($allUris)]);
            
        } catch (\Exception $e) {
            Log::error("全局路由缓存预热失败", ['error' => $e->getMessage()]);
        }
        
        Log::info('权限缓存预热完成');
    }
    
    /**
     * 清理过期缓存
     */
    public static function cleanExpiredCache(): void
    {
        $currentTime = time();
        $cleanedCount = 0;
        
        foreach (self::$processCacheTime as $key => $time) {
            if ($currentTime - $time >= self::$processCacheTTL) {
                unset(self::$processCache[$key], self::$processCacheTime[$key]);
                $cleanedCount++;
            }
        }
        
        if ($cleanedCount > 0) {
            Log::info("清理过期权限缓存", [
                'cleaned_count' => $cleanedCount,
                'remaining_count' => count(self::$processCache)
            ]);
        }
    }
    
    /**
     * 获取性能统计数据
     * 
     * @return array
     */
    public static function getPerformanceStats(): array
    {
        $stats = self::$performanceStats;
        $hitRate = $stats['total_checks'] > 0 ? 
            round((($stats['process_cache_hits'] + $stats['redis_cache_hits']) / $stats['total_checks']) * 100, 2) : 0;
        
        return [
            'total_checks' => $stats['total_checks'],
            'process_cache_hits' => $stats['process_cache_hits'],
            'redis_cache_hits' => $stats['redis_cache_hits'],
            'database_queries' => $stats['database_queries'],
            'cache_hit_rate' => $hitRate,
            'average_time_ms' => round($stats['average_time'], 2),
            'process_cache_size' => count(self::$processCache),
            'process_cache_max_size' => self::$processCacheMaxSize
        ];
    }
    
    /**
     * 重置性能统计
     */
    public static function resetPerformanceStats(): void
    {
        self::$performanceStats = [
            'total_checks' => 0,
            'process_cache_hits' => 0,
            'redis_cache_hits' => 0,
            'database_queries' => 0,
            'total_time' => 0,
            'average_time' => 0
        ];
        
        Log::info('权限中间件性能统计已重置');
    }
} 