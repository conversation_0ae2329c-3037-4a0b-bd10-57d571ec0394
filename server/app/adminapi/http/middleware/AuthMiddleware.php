<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

declare (strict_types=1);

namespace app\adminapi\http\middleware;

use app\common\{
    cache\AdminAuthCache,
    service\JsonService
};
use Closure;
use think\helper\Str;
use think\facade\Log;

/**
 * 权限验证中间件
 * Class AuthMiddleware
 * @package app\adminapi\http\middleware
 */
class AuthMiddleware
{
    /**
     * @notes 权限验证
     * @param $request
     * @param Closure $next
     * @return mixed
     * <AUTHOR>
     * @date 2021/7/2 19:29
     */
    public function handle($request, Closure $next): mixed
    {
        //不登录访问，无需权限验证
        if ($request->controllerObject->isNotNeedLogin()) {
            return $next($request);
        }

        //系统默认超级管理员，无需权限验证
        if (1 === $request->adminInfo['root']) {
            return $next($request);
        }

        $adminAuthCache = new AdminAuthCache($request->adminInfo['admin_id']);

        // 当前访问路径
        $accessUri = strtolower($request->controller() . '/' . $request->action());
        // 全部路由
        $allUri = $this->formatUrl($adminAuthCache->getAllUri());

        // 🔒 安全修复：实施默认拒绝策略，防止权限绕过
        if (!in_array($accessUri, $allUri)) {
            // 记录可疑的未注册URI访问尝试
            Log::warning('管理后台安全警告：访问未注册URI', [
                'uri' => $accessUri,
                'admin_id' => $request->adminInfo['admin_id'] ?? 'unknown',
                'admin_account' => $request->adminInfo['account'] ?? 'unknown',
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'referer' => $request->header('Referer'),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            // 默认拒绝策略：未注册的URI一律拒绝访问
            return JsonService::fail('访问的接口不存在或无权限访问');
        }

        // 当前管理员拥有的路由权限
        $AdminUris = $adminAuthCache->getAdminUri() ?? [];
        $AdminUris = $this->formatUrl($AdminUris);

        if (in_array($accessUri, $AdminUris)) {
            return $next($request);
        }
        return JsonService::fail('权限不足，无法访问或操作');
    }

    /**
     * @notes 格式化URL
     * @param array $data
     * @return array
     * <AUTHOR>
     * @date 2022/7/7 15:39
     */
    public function formatUrl(array $data): array
    {
        return array_map(function ($item) {
            return strtolower(Str::camel($item));
        }, $data);
    }
}