<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\logic;

use app\common\logic\BaseLogic;
use app\common\model\file\File;
use app\common\model\file\FileCate;
use app\common\service\ConfigService;
use app\common\service\storage\Driver as StorageDriver;

/**
 * 文件逻辑层
 * Class FileLogic
 * @package app\adminapi\logic
 */
class FileLogic extends BaseLogic
{
    /**
     * @notes 移动文件
     * @param $params
     * <AUTHOR>
     * @date 2021/7/28 15:29
     */
    public static function move($params)
    {
        (new File())->whereIn('id', $params['ids'])
            ->update([
                'cid' => $params['cid'],
                'update_time' => time()
            ]);
    }

    /**
     * @notes 重命名文件
     * @param $params
     * <AUTHOR>
     * @date 2021/7/29 17:16
     */
    public static function rename($params)
    {
        (new File())->where('id', $params['id'])
            ->update([
                'name' => $params['name'],
                'update_time' => time()
            ]);
    }

    /**
     * @notes 批量删除文件
     * @param $params
     * @throws @\think\Exception
     * @throws @\think\db\exception\DataNotFoundException
     * @throws @\think\db\exception\DbException
     * @throws @\think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/28 15:41
     */
    public static function delete($params)
    {
        $result = (new File())->whereIn('id', $params['ids'])->select();
        $StorageDriver = new StorageDriver([
            'default' => ConfigService::get('storage', 'default', 'local'),
            'engine'  => ConfigService::get('storage') ?? ['local'=>[]],
        ]);
        foreach ($result as $item) {
            $StorageDriver->delete($item['uri']);
        }
        File::destroy($params['ids']);
    }

    /**
     * @notes 添加文件分类
     * @param $params
     * <AUTHOR>
     * @date 2021/7/28 11:32
     */
    public static function addCate($params)
    {
        FileCate::create([
            'type' => $params['type'],
            'pid' => $params['pid'],
            'name' => $params['name']
        ]);
    }

    /**
     * @notes 编辑文件分类
     * @param $params
     * <AUTHOR>
     * @date 2021/7/28 14:03
     */
    public static function editCate($params)
    {
        FileCate::update([
            'name' => $params['name'],
            'update_time' => time()
        ], ['id' => $params['id']]);
    }

    /**
     * @notes 删除文件分类
     * @param $params
     * <AUTHOR>
     * @date 2021/7/28 14:21
     */
    public static function delCate($params)
    {
        FileCate::destroy($params['id']);
    }
}