<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\logic\knowledge;
use app\common\model\knowledge\ExampleContent;

/**
 * 示例库内容逻辑类
 * Class ExampleContentLogic
 * @package app\adminapi\logic\knowledge
 */
class ExampleContentLogic
{
    /**
     * @notes 添加示例库内容
     * @param array $post
     * @return bool
     * <AUTHOR>
     */
    public function add(array $post): bool
    {
        ExampleContent::create($post);
        return true;
    }

    /**
     * @notes 修改示例库内容
     * @param array $post
     * @return bool
     * <AUTHOR>
     */
    public function edit(array $post): bool
    {
        ExampleContent::update($post);
        return true;
    }

    /**
     * @notes 删除示例库内容
     * @param $id
     * @return bool
     * <AUTHOR>
     */
    public function del($id): bool
    {
        ExampleContent::where(['id' => $id])->delete();
        return true;
    }

    /**
     * @notes 获取示例库内容详情
     * @param int $id
     * @return array
     * <AUTHOR>
     */
    public function detail(int $id): array
    {
        $detail = ExampleContent::withoutField('delete_time,update_time')->findOrEmpty($id)->toArray();
        return $detail;
    }

    /**
     * @notes 修改状态
     * @param int $id
     * @return bool
     * <AUTHOR>
     */
    public function status(int $id): bool
    {
        $exampleContent = ExampleContent::where(['id' => $id])->findOrEmpty();
        if ($exampleContent->isEmpty()) {
            return true;
        }
        $exampleContent->status = $exampleContent->status ? 0 : 1;
        $exampleContent->save();
        return true;
    }
    
    /**
     * @notes 根据类别ID获取示例列表
     * @param int $categoryId
     * @return array
     * <AUTHOR>
     */
    public function getListByCategoryId(int $categoryId): array
    {
        $lists = ExampleContent::where([
                'category_id' => $categoryId,
                'status' => 1
            ])
            ->field('id,title,question,answer')
            ->order('sort', 'asc')
            ->select()
            ->toArray();
        
        return ['lists' => $lists];
    }
    
    /**
     * @notes 获取所有示例库类别及内容
     * @return array
     * <AUTHOR>
     */
    public function getAllExamples(): array
    {
        // 先获取所有开启状态的类别
        $categories = \think\facade\Db::table('cm_example_category')
            ->where('status', 1)
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            })
            ->field('id, name')
            ->order('sort', 'desc')
            ->select()
            ->toArray();
        
        if (empty($categories)) {
            return [];
        }
        
        // 获取所有开启状态的示例内容
        $examples = ExampleContent::where(['status' => 1])
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            })
            ->field('id,category_id,title,question,answer')
            ->select()
            ->toArray();
        
        // 将示例内容按类别分组
        $examplesByCategory = [];
        foreach ($examples as $example) {
            $categoryId = $example['category_id'];
            if (!isset($examplesByCategory[$categoryId])) {
                $examplesByCategory[$categoryId] = [];
            }
            $examplesByCategory[$categoryId][] = [
                'id' => $example['id'],
                'title' => $example['title'],
                'question' => $example['question'],
                'answer' => $example['answer']
            ];
        }
        
        // 构建结果数组
        $result = [];
        foreach ($categories as $category) {
            $categoryId = $category['id'];
            $result[] = [
                'id' => $categoryId,
                'name' => $category['name'],
                'examples' => $examplesByCategory[$categoryId] ?? []
            ];
        }
        
        return $result;
    }
} 