<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\logic\knowledge;
use app\common\model\knowledge\ExampleCategory;
use app\common\model\knowledge\ExampleContent;

/**
 * 示例库类别逻辑类
 * Class ExampleCategoryLogic
 * @package app\adminapi\logic\knowledge
 */
class ExampleCategoryLogic
{
    /**
     * @notes 添加示例库类别
     * @param array $post
     * @return bool
     * <AUTHOR>
     */
    public function add(array $post): bool
    {
        ExampleCategory::create($post);
        return true;
    }

    /**
     * @notes 修改示例库类别
     * @param array $post
     * @return bool|string
     * <AUTHOR>
     */
    public function edit(array $post): bool|string
    {
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        try {
            // 严格验证ID参数
            if (empty($post['id']) || !is_numeric($post['id']) || $post['id'] <= 0) {
                return '缺少有效的ID参数';
            }
            
            $id = (int)$post['id'];
            
            // 先检查记录是否存在
            $exists = \think\facade\Db::table($prefix.'example_category')
                ->where('id', '=', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (empty($exists)) {
                return '要编辑的数据不存在或已被删除';
            }
            
            // 验证其他必要参数
            if (empty($post['name'])) {
                return '请填写类别名称';
            }
            
            // 构建更新数据
            $updateData = [
                'name' => $post['name'],
                'sort' => isset($post['sort']) ? (int)$post['sort'] : 0,
                'status' => isset($post['status']) ? (int)$post['status'] : 1,
                'update_time' => time()
            ];
            
            // 执行更新
            $result = \think\facade\Db::table($prefix.'example_category')
                ->where('id', '=', $id)
                ->update($updateData);
                
            if ($result !== false) {
                return true;
            }
            
            return '编辑失败，请稍后再试';
        } catch (\Exception $e) {
            return '编辑失败: ' . $e->getMessage();
        }
    }

    /**
     * @notes 删除示例库类别
     * @param $id
     * @return bool|string
     * <AUTHOR>
     */
    public function del($id)
    {
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        try {
            // 检查ID是否有效
            if (empty($id) || !is_numeric($id) || $id <= 0) {
                return '无效的ID参数';
            }
            
            $id = intval($id);
            
            // 检查记录是否存在
            $exists = \think\facade\Db::table($prefix.'example_category')
                ->where('id', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
                
            if (!$exists) {
                return '要删除的类别不存在或已被删除';
            }
            
            // 检查是否有关联的示例内容
            $contentCount = \think\facade\Db::table($prefix.'example_content')
                ->where('category_id', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->count();
                
            if ($contentCount > 0) {
                return '该类别下有示例内容，不能删除';
            }
            
            // 执行软删除
            $deleteTime = time();
            $result = \think\facade\Db::table($prefix.'example_category')
                ->where('id', $id)
                ->update(['delete_time' => $deleteTime]);
            
            if ($result) {
                return true;
            }
            
            return '删除失败，请稍后再试';
        } catch (\Exception $e) {
            return '删除失败: ' . $e->getMessage();
        }
    }

    /**
     * @notes 获取示例库类别详情
     * @param int $id
     * @return array
     * <AUTHOR>
     */
    public function detail(int $id): array
    {
        try {
            // 严格验证ID
            if (empty($id) || !is_numeric($id) || $id <= 0) {
                return [];
            }
            
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 查询详情数据
            $detail = \think\facade\Db::table($prefix.'example_category')
                ->alias('ec')
                ->where('ec.id', '=', $id)
                ->where(function($query) {
                    $query->where('ec.delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('ec.delete_time');
                          });
                })
                ->find();
                
            if (empty($detail)) {
                return [];
            }
            
            // 移除不需要的字段
            if (isset($detail['delete_time'])) unset($detail['delete_time']);
            if (isset($detail['update_time'])) unset($detail['update_time']);
            
            // 确保ID被正确返回和类型转换
            $detail['id'] = (int)$detail['id'];
            
            return $detail;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * @notes 修改状态
     * @param int $id
     * @return bool
     * <AUTHOR>
     */
    public function status(int $id): bool
    {
        $exampleCategory = ExampleCategory::where(['id' => $id])->findOrEmpty();
        if ($exampleCategory->isEmpty()) {
            return true;
        }
        $exampleCategory->status = $exampleCategory->status ? 0 : 1;
        $exampleCategory->save();
        return true;
    }
    
    /**
     * @notes 获取类别下拉列表
     * @return array
     * <AUTHOR>
     */
    public function getSelectList(): array
    {
        $lists = ExampleCategory::where(['status' => 1])
            ->field('id, name')
            ->order('sort', 'asc')
            ->select()
            ->toArray();
        return ['lists' => $lists];
    }
} 