<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\logic\kb;
// 移除模型引用，直接使用数据库操作
// use app\common\model\knowledge\Template;

/**
 * 模板库逻辑类
 * Class TemplateLogic
 * @package app\adminapi\logic\kb
 */
class TemplateLogic
{
    /**
     * @notes 添加模板
     * @param array $post
     * @return bool
     * <AUTHOR>
     */
    public function add(array $post): bool
    {
        try {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 构建插入数据
            $insertData = [
                'category_id' => (int)$post['category_id'],
                'name' => $post['name'],
                'description' => $post['description'] ?? '',
                'download_url' => $post['download_url'],
                'file_size' => $post['file_size'] ?? '',
                'file_type' => $post['file_type'] ?? '',
                'sort' => isset($post['sort']) ? (int)$post['sort'] : 0,
                'status' => isset($post['status']) ? (int)$post['status'] : 1,
                'download_count' => 0,
                'create_time' => time(),
                'update_time' => null,
                'delete_time' => null
            ];
            
            // 执行插入操作
            $result = \think\facade\Db::table($prefix.'template')->insert($insertData);
            
            return $result !== false;
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('模板添加失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 编辑模板
     * @param array $post
     * @return bool|string
     * <AUTHOR>
     */
    public function edit(array $post): bool|string
    {
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        try {
            // 严格验证ID参数
            if (empty($post['id']) || !is_numeric($post['id']) || $post['id'] <= 0) {
                return '缺少有效的ID参数';
            }
            
            $id = (int)$post['id'];
            
            // 先检查记录是否存在
            $exists = \think\facade\Db::table($prefix.'template')
                ->where('id', '=', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (empty($exists)) {
                return '要编辑的模板不存在或已被删除';
            }
            
            // 验证其他必要参数
            if (empty($post['name'])) {
                return '请填写模板名称';
            }
            
            if (empty($post['download_url'])) {
                return '请填写下载链接地址';
            }
            
            // 构建更新数据
            $updateData = [
                'category_id' => (int)$post['category_id'],
                'name' => $post['name'],
                'description' => $post['description'] ?? '',
                'download_url' => $post['download_url'],
                'file_size' => $post['file_size'] ?? '',
                'file_type' => $post['file_type'] ?? '',
                'sort' => isset($post['sort']) ? (int)$post['sort'] : 0,
                'status' => isset($post['status']) ? (int)$post['status'] : 1,
                'update_time' => time()
            ];
            
            // 执行更新，使用参数化查询防止SQL注入
            $result = \think\facade\Db::table($prefix.'template')
                ->where('id', '=', $id)
                ->update($updateData);
                
            if ($result !== false) {
                return true;
            }
            
            return '编辑失败，请稍后再试';
        } catch (\Exception $e) {
            return '编辑失败: ' . $e->getMessage();
        }
    }

    /**
     * @notes 删除模板
     * @param $id
     * @return bool|string
     * <AUTHOR>
     */
    public function del($id)
    {
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        try {
            // 检查ID是否有效
            if (empty($id) || !is_numeric($id) || $id <= 0) {
                return '无效的ID参数';
            }
            
            $id = intval($id);
            
            // 检查记录是否存在
            $exists = \think\facade\Db::table($prefix.'template')
                ->where('id', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
                
            if (!$exists) {
                return '要删除的模板不存在或已被删除';
            }
            
            // 执行软删除，使用参数化查询
            $deleteTime = time();
            $result = \think\facade\Db::table($prefix.'template')
                ->where('id', $id)
                ->update(['delete_time' => $deleteTime]);
            
            if ($result) {
                return true;
            }
            
            return '删除失败，请稍后再试';
        } catch (\Exception $e) {
            return '删除失败: ' . $e->getMessage();
        }
    }

    /**
     * @notes 获取模板详情
     * @param int $id
     * @return array
     * <AUTHOR>
     */
    public function detail(int $id): array
    {
        try {
            // 严格验证ID
            if (empty($id) || !is_numeric($id) || $id <= 0) {
                return [];
            }
            
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 查询详情数据
            $detail = \think\facade\Db::table($prefix.'template')
                ->alias('t')
                ->where('t.id', '=', $id)
                ->where(function($query) {
                    $query->where('t.delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('t.delete_time');
                          });
                })
                ->find();
                
            if (empty($detail)) {
                return [];
            }
            
            // 移除不需要的字段
            if (isset($detail['delete_time'])) unset($detail['delete_time']);
            if (isset($detail['update_time'])) unset($detail['update_time']);
            
            // 确保ID被正确返回和类型转换
            $detail['id'] = (int)$detail['id'];
            $detail['category_id'] = (int)$detail['category_id'];
            $detail['sort'] = (int)$detail['sort'];
            $detail['status'] = (int)$detail['status'];
            $detail['download_count'] = (int)$detail['download_count'];
            
            return $detail;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * @notes 修改状态
     * @param int $id
     * @return bool
     * <AUTHOR>
     */
    public function status(int $id): bool
    {
        try {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 先查询当前状态
            $template = \think\facade\Db::table($prefix.'template')
                ->where('id', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
                
            if (empty($template)) {
                return false;
            }
            
            // 切换状态
            $newStatus = $template['status'] ? 0 : 1;
            
            // 更新状态
            $result = \think\facade\Db::table($prefix.'template')
                ->where('id', $id)
                ->update([
                    'status' => $newStatus,
                    'update_time' => time()
                ]);
                
            return $result !== false;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * @notes 根据类别获取模板列表（用于PC/H5端）
     * @param int $categoryId
     * @return array
     * <AUTHOR>
     */
    public function getListByCategoryId(int $categoryId): array
    {
        try {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            $templates = \think\facade\Db::table($prefix.'template')
                ->alias('t')
                ->leftJoin($prefix.'example_category c', 'c.id = t.category_id')
                ->where('t.category_id', $categoryId)
                ->where('t.status', 1)
                ->where(function($query) {
                    $query->where('t.delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('t.delete_time');
                          });
                })
                ->field('t.id,t.name,t.description,t.download_url,t.file_size,t.file_type,t.download_count,c.name as category_name')
                ->order('t.sort', 'desc')
                ->select()
                ->toArray();
                
            return $templates;
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * @notes 获取所有模板（按类别分组，用于PC/H5端）
     * @return array
     * <AUTHOR>
     */
    public function getAllTemplates(): array
    {
        try {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            $templates = \think\facade\Db::table($prefix.'template')
                ->alias('t')
                ->leftJoin($prefix.'example_category c', 'c.id = t.category_id')
                ->where('t.status', 1)
                ->where(function($query) {
                    $query->where('t.delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('t.delete_time');
                          });
                })
                ->field('t.id,t.category_id,t.name,t.description,t.download_url,t.file_size,t.file_type,t.download_count,c.name as category_name')
                ->order('t.sort', 'desc')
                ->select()
                ->toArray();
                
            $result = [];
            foreach ($templates as $template) {
                $categoryId = $template['category_id'];
                $categoryName = $template['category_name'] ?? '';
                
                if (!isset($result[$categoryId])) {
                    $result[$categoryId] = [
                        'id' => $categoryId,
                        'name' => $categoryName,
                        'templates' => []
                    ];
                }
                
                $result[$categoryId]['templates'][] = [
                    'id' => $template['id'],
                    'name' => $template['name'],
                    'description' => $template['description'],
                    'download_url' => $template['download_url'],
                    'file_size' => $template['file_size'],
                    'file_type' => $template['file_type'],
                    'download_count' => $template['download_count']
                ];
            }
            
            return array_values($result);
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * @notes 增加下载次数
     * @param int $id
     * @return bool
     * <AUTHOR>
     */
    public function increaseDownloadCount(int $id): bool
    {
        try {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 先查询模板是否存在且状态正常
            $template = \think\facade\Db::table($prefix.'template')
                ->where('id', $id)
                ->where('status', 1)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
                
            if (!empty($template)) {
                // 增加下载次数
                \think\facade\Db::table($prefix.'template')
                    ->where('id', $id)
                    ->inc('download_count')
                    ->update(['update_time' => time()]);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
} 