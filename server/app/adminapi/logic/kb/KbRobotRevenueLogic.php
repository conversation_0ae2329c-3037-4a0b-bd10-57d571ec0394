<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\logic\kb;

use app\common\logic\BaseLogic;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\service\RobotRevenueService;
use Exception;

/**
 * 智能体分成收益管理逻辑类
 */
class KbRobotRevenueLogic extends BaseLogic
{
    /**
     * @notes 获取分成收益统计
     * @return array
     */
    public static function getStatistics(): array
    {
        try {
            // 总分成统计
            $totalStats = KbRobotRevenueLog::where(['settle_status' => 1])
                ->field([
                    'COUNT(*) as total_count',
                    'SUM(total_cost) as total_cost',
                    'SUM(share_amount) as total_share_amount',
                    'SUM(platform_amount) as total_platform_amount'
                ])->find();

            // 今日分成统计
            $todayStats = KbRobotRevenueLog::where(['settle_status' => 1])
                ->whereDay('create_time')
                ->field([
                    'COUNT(*) as today_count',
                    'SUM(total_cost) as today_cost',
                    'SUM(share_amount) as today_share_amount',
                    'SUM(platform_amount) as today_platform_amount'
                ])->find();

            // 待结算统计
            $pendingStats = KbRobotRevenueLog::where(['settle_status' => 0])
                ->field([
                    'COUNT(*) as pending_count',
                    'SUM(share_amount) as pending_amount'
                ])->find();

            return [
                'total' => [
                    'count' => $totalStats['total_count'] ?? 0,
                    'total_cost' => $totalStats['total_cost'] ?? 0,
                    'share_amount' => $totalStats['total_share_amount'] ?? 0,
                    'platform_amount' => $totalStats['total_platform_amount'] ?? 0
                ],
                'today' => [
                    'count' => $todayStats['today_count'] ?? 0,
                    'total_cost' => $todayStats['today_cost'] ?? 0,
                    'share_amount' => $todayStats['today_share_amount'] ?? 0,
                    'platform_amount' => $todayStats['today_platform_amount'] ?? 0
                ],
                'pending' => [
                    'count' => $pendingStats['pending_count'] ?? 0,
                    'amount' => $pendingStats['pending_amount'] ?? 0
                ]
            ];
        } catch (Exception $e) {
            self::setError($e->getMessage());
            return [];
        }
    }

    /**
     * @notes 批量结算分成收益
     * @return array
     */
    public static function batchSettle(): array
    {
        try {
            $result = RobotRevenueService::batchSettle();
            
            if (!$result['success'] && !empty($result['errors'])) {
                // 如果有错误，设置错误信息
                $errorMessage = '批量结算部分失败：' . implode('; ', array_slice($result['errors'], 0, 3));
                if (count($result['errors']) > 3) {
                    $errorMessage .= '...等' . count($result['errors']) . '个错误';
                }
                self::setError($errorMessage);
            }
            
            return $result;
        } catch (Exception $e) {
            self::setError($e->getMessage());
            return [
                'success' => false,
                'total_processed' => 0,
                'total_success' => 0,
                'total_failed' => 0,
                'total_amount' => 0,
                'batch_count' => 0,
                'errors' => [$e->getMessage()],
                'message' => '批量结算执行异常'
            ];
        }
    }

    /**
     * @notes 获取分成配置
     * @return array
     */
    public static function getConfig(): array
    {
        return RobotRevenueService::getConfig();
    }

    /**
     * @notes 设置分成配置
     * @param array $params
     * @return bool
     */
    public static function setConfig(array $params): bool
    {
        try {
            $result = RobotRevenueService::updateConfig($params);
            if (!$result) {
                throw new Exception('配置保存失败');
            }
            return true;
        } catch (Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
} 