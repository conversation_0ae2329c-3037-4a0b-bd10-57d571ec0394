<?php

namespace app\adminapi\logic\kb;

use app\common\logic\BaseLogic;
use app\common\model\kb\KbRobotEditLog;
use app\common\model\kb\KbRobot;
use app\common\model\user\User;

/**
 * 智能体编辑日志管理逻辑类
 */
class KbRobotEditLogLogic extends BaseLogic
{
    /**
     * @notes 编辑日志列表
     * @param array $params
     * @return array
     * <AUTHOR> Assistant
     */
    public static function lists(array $params): array
    {
        $pageNo = intval($params['page_no'] ?? 1);
        $pageSize = intval($params['page_size'] ?? 25);
        
        $where = [];
        
        // 按智能体ID筛选
        if (!empty($params['robot_id'])) {
            $where[] = ['log.robot_id', '=', intval($params['robot_id'])];
        }
        
        // 按用户筛选
        if (!empty($params['user_keyword'])) {
            $where[] = ['u.nickname|u.account|u.mobile', 'like', '%' . $params['user_keyword'] . '%'];
        }
        
        // 按智能体名称筛选
        if (!empty($params['robot_keyword'])) {
            $where[] = ['r.name', 'like', '%' . $params['robot_keyword'] . '%'];
        }
        
        // 按编辑类型筛选
        if (isset($params['edit_type']) && $params['edit_type'] !== '') {
            $where[] = ['log.edit_type', '=', intval($params['edit_type'])];
        }
        
        // 按是否自动下架筛选
        if (isset($params['is_auto_offline']) && $params['is_auto_offline'] !== '') {
            $where[] = ['log.is_auto_offline', '=', intval($params['is_auto_offline'])];
        }
        
        // 按时间范围筛选
        if (!empty($params['start_time'])) {
            $where[] = ['log.create_time', '>=', strtotime($params['start_time'])];
        }
        if (!empty($params['end_time'])) {
            $where[] = ['log.create_time', '<=', strtotime($params['end_time'])];
        }
        
        $model = new KbRobotEditLog();
        $lists = $model->alias('log')
            ->field([
                'log.id',
                'log.robot_id',
                'log.user_id',
                'log.edit_type',
                'log.before_data',
                'log.after_data',
                'log.is_auto_offline',
                'log.offline_reason',
                'log.create_time',
                'r.name as robot_name',
                'r.image as robot_image',
                'u.nickname as user_nickname',
                'u.account as user_account',
                'u.avatar as user_avatar'
            ])
            ->leftJoin('kb_robot r', 'r.id = log.robot_id')
            ->leftJoin('user u', 'u.id = log.user_id')
            ->where($where)
            ->order('log.create_time', 'desc')
            ->paginate([
                'page' => $pageNo,
                'list_rows' => $pageSize,
                'var_page' => 'page'
            ]);
        
        $data = $lists->toArray();
        
        // 处理数据
        foreach ($data['data'] as &$item) {
            // 格式化时间 - 确保时间戳是整数类型
            $timestamp = is_numeric($item['create_time']) ? intval($item['create_time']) : time();
            $item['create_time_text'] = date('Y-m-d H:i:s', $timestamp);
            
            // 编辑类型文本
            $item['edit_type_text'] = KbRobotEditLog::getEditTypeText($item['edit_type']);
            
            // 是否自动下架文本
            $item['is_auto_offline_text'] = $item['is_auto_offline'] ? '是' : '否';
            
            // 处理JSON数据
            if ($item['before_data']) {
                $item['before_data'] = json_decode($item['before_data'], true);
            }
            if ($item['after_data']) {
                $item['after_data'] = json_decode($item['after_data'], true);
            }
            
            // 用户头像处理
            if ($item['user_avatar']) {
                try {
                    $item['user_avatar'] = \app\common\service\FileService::getFileUrl($item['user_avatar']);
                } catch (Exception $e) {
                    $item['user_avatar'] = '';
                }
            }

            // 智能体图片处理
            if ($item['robot_image']) {
                try {
                    $item['robot_image'] = \app\common\service\FileService::getFileUrl($item['robot_image']);
                } catch (Exception $e) {
                    $item['robot_image'] = '';
                }
            }
        }
        
        return [
            'page_no' => $pageNo,
            'page_size' => $pageSize,
            'count' => $data['total'],
            'lists' => $data['data']
        ];
    }
    
    /**
     * @notes 编辑日志详情
     * @param int $id
     * @return array
     * <AUTHOR> Assistant
     */
    public static function detail(int $id): array
    {
        $model = new KbRobotEditLog();
        $detail = $model->alias('log')
            ->field([
                'log.*',
                'r.name as robot_name',
                'r.image as robot_image',
                'r.intro as robot_intro',
                'u.nickname as user_nickname',
                'u.account as user_account',
                'u.avatar as user_avatar'
            ])
            ->leftJoin('kb_robot r', 'r.id = log.robot_id')
            ->leftJoin('user u', 'u.id = log.user_id')
            ->where('log.id', $id)
            ->findOrEmpty()
            ->toArray();
        
        if (empty($detail)) {
            return [];
        }
        
        // 格式化时间 - 确保时间戳是整数类型
        $timestamp = is_numeric($detail['create_time']) ? intval($detail['create_time']) : time();
        $detail['create_time_text'] = date('Y-m-d H:i:s', $timestamp);
        
        // 编辑类型文本
        $detail['edit_type_text'] = KbRobotEditLog::getEditTypeText($detail['edit_type']);
        
        // 是否自动下架文本
        $detail['is_auto_offline_text'] = $detail['is_auto_offline'] ? '是' : '否';
        
        // 处理JSON数据
        if (!empty($detail['before_data'])) {
            $detail['before_data'] = json_decode($detail['before_data'], true) ?: [];
        } else {
            $detail['before_data'] = [];
        }

        if (!empty($detail['after_data'])) {
            $detail['after_data'] = json_decode($detail['after_data'], true) ?: [];
        } else {
            $detail['after_data'] = [];
        }
        
        // 用户头像处理
        if ($detail['user_avatar']) {
            try {
                $detail['user_avatar'] = \app\common\service\FileService::getFileUrl($detail['user_avatar']);
            } catch (Exception $e) {
                $detail['user_avatar'] = '';
            }
        }

        // 智能体图片处理
        if ($detail['robot_image']) {
            try {
                $detail['robot_image'] = \app\common\service\FileService::getFileUrl($detail['robot_image']);
            } catch (Exception $e) {
                $detail['robot_image'] = '';
            }
        }
        
        return $detail;
    }
    
    /**
     * @notes 获取统计数据
     * @param array $params
     * @return array
     * <AUTHOR> Assistant
     */
    public static function statistics(array $params = []): array
    {
        $model = new KbRobotEditLog();
        
        // 总编辑次数
        $totalEdits = $model->count();
        
        // 自动下架次数
        $autoOfflineCount = $model->where('is_auto_offline', 1)->count();
        
        // 今日编辑次数
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));
        $todayEdits = $model->where('create_time', 'between', [$todayStart, $todayEnd])->count();
        
        // 本周编辑次数
        $weekStart = strtotime('this week Monday');
        $weekEdits = $model->where('create_time', '>=', $weekStart)->count();
        
        // 按编辑类型统计
        $editTypeStats = $model->field('edit_type, count(*) as count')
            ->group('edit_type')
            ->select()
            ->toArray();
        
        $editTypeData = [];
        foreach ($editTypeStats as $stat) {
            $editTypeData[] = [
                'type' => $stat['edit_type'],
                'type_text' => KbRobotEditLog::getEditTypeText($stat['edit_type']),
                'count' => $stat['count']
            ];
        }
        
        return [
            'total_edits' => $totalEdits,
            'auto_offline_count' => $autoOfflineCount,
            'today_edits' => $todayEdits,
            'week_edits' => $weekEdits,
            'edit_type_stats' => $editTypeData
        ];
    }
}
