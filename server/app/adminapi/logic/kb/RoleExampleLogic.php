<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\logic\kb;

use app\common\model\robot\RoleExample;
use app\common\model\knowledge\ExampleCategory;
use app\common\logic\BaseLogic;

/**
 * 智能体角色示例逻辑层
 * Class RoleExampleLogic
 * @package app\adminapi\logic\kb
 */
class RoleExampleLogic extends BaseLogic
{
    /**
     * @notes 获取角色示例列表
     * @param array $params
     * @return array
     * <AUTHOR>
     */
    public static function lists(array $params): array
    {
        $where = [];
        
        // 标题搜索
        if (!empty($params['title'])) {
            $where[] = ['title', 'like', '%' . $params['title'] . '%'];
        }
        
        // 类别筛选
        if (!empty($params['category_id'])) {
            $where[] = ['category_id', '=', $params['category_id']];
        }
        
        // 状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $where[] = ['status', '=', $params['status']];
        }
        
        // 构建查询
        $query = \think\facade\Db::table('cm_role_example')
            ->alias('re')
            ->leftJoin('cm_example_category ec', 'ec.id = re.category_id')
            ->where($where)
            ->where(function($query) {
                $query->where('re.delete_time', '=', 0)
                    ->whereOr(function($q) {
                        $q->whereNull('re.delete_time');
                    });
            });
            
        // 获取总数
        $count = $query->count();
        
        // 获取列表数据
        $lists = $query->field('re.*, ec.name as category_name')
            ->order('re.sort desc, re.id desc')
            ->page(intval($params['page_no']), intval($params['page_size']))
            ->select()
            ->toArray();
            
        // 格式化时间
        foreach ($lists as &$item) {
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['update_time'] = $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '';
        }
        
        return [
            'count' => $count,
            'lists' => $lists,
            'page_no' => $params['page_no'],
            'page_size' => $params['page_size']
        ];
    }
    
    /**
     * @notes 获取角色示例详情
     * @param int $id
     * @return array|false
     * <AUTHOR>
     */
    public static function detail(int $id)
    {
        $roleExample = \think\facade\Db::table('cm_role_example')
            ->alias('re')
            ->leftJoin('cm_example_category ec', 'ec.id = re.category_id')
            ->field('re.*, ec.name as category_name')
            ->where('re.id', '=', $id)
            ->where(function($query) {
                $query->where('re.delete_time', '=', 0)
                    ->whereOr(function($q) {
                        $q->whereNull('re.delete_time');
                    });
            })
            ->find();
        
        if (empty($roleExample)) {
            self::setError('角色示例不存在');
            return false;
        }
        
        return $roleExample;
    }
    
    /**
     * @notes 添加角色示例
     * @param array $params
     * @return bool
     * <AUTHOR>
     */
    public static function add(array $params): bool
    {
        $data = [
            'category_id' => $params['category_id'],
            'title' => $params['title'],
            'content' => $params['content'],
            'description' => $params['description'] ?? '',
            'sort' => $params['sort'] ?? 0,
            'status' => $params['status'] ?? 1,
            'create_time' => time()
        ];
        
        $result = RoleExample::create($data);
        if (!$result) {
            self::setError('添加失败');
            return false;
        }
        
        return true;
    }
    
    /**
     * @notes 编辑角色示例
     * @param array $params
     * @return bool
     * <AUTHOR>
     */
    public static function edit(array $params): bool
    {
        // 检查记录是否存在
        $exists = \think\facade\Db::table('cm_role_example')
            ->where('id', '=', $params['id'])
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                    ->whereOr(function($q) {
                        $q->whereNull('delete_time');
                    });
            })
            ->find();
        
        if (empty($exists)) {
            self::setError('角色示例不存在');
            return false;
        }
        
        $data = [
            'category_id' => $params['category_id'],
            'title' => $params['title'],
            'content' => $params['content'],
            'description' => $params['description'] ?? '',
            'sort' => $params['sort'] ?? 0,
            'status' => $params['status'] ?? 1,
            'update_time' => time()
        ];
        
        // 更新数据
        $result = \think\facade\Db::table('cm_role_example')
            ->where('id', '=', $params['id'])
            ->update($data);
            
        if ($result === false) {
            self::setError('编辑失败');
            return false;
        }
        
        return true;
    }
    
    /**
     * @notes 删除角色示例
     * @param int|array $id
     * @return bool
     * <AUTHOR>
     */
    public static function del($id): bool
    {
        if (is_array($id)) {
            // 批量删除，先检查所有ID是否存在
            $existingIds = \think\facade\Db::table('cm_role_example')
                ->whereIn('id', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                        ->whereOr(function($q) {
                            $q->whereNull('delete_time');
                        });
                })
                ->column('id');
            
            if (empty($existingIds)) {
                self::setError('角色示例不存在');
                return false;
            }
            
            // 只删除存在的记录
            $result = \think\facade\Db::table('cm_role_example')
                ->whereIn('id', $existingIds)
                ->update(['delete_time' => time()]);
        } else {
            // 单个删除，先检查是否存在
            $exists = \think\facade\Db::table('cm_role_example')
                ->where('id', '=', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                        ->whereOr(function($q) {
                            $q->whereNull('delete_time');
                        });
                })
                ->find();
            
            if (empty($exists)) {
                self::setError('角色示例不存在');
                return false;
            }
            
            $result = \think\facade\Db::table('cm_role_example')
                ->where('id', '=', $id)
                ->update(['delete_time' => time()]);
        }
        
        if ($result === false) {
            self::setError('删除失败');
            return false;
        }
        
        return true;
    }
    
    /**
     * @notes 修改角色示例状态
     * @param array $params
     * @return bool
     * <AUTHOR>
     */
    public static function status(array $params): bool
    {
        // 查询当前记录
        $roleExample = \think\facade\Db::table('cm_role_example')
            ->where('id', '=', $params['id'])
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                    ->whereOr(function($q) {
                        $q->whereNull('delete_time');
                    });
            })
            ->find();
        
        if (empty($roleExample)) {
            self::setError('角色示例不存在');
            return false;
        }
        
        // 切换状态
        $newStatus = $roleExample['status'] ? 0 : 1;
        
        // 更新状态
        $result = \think\facade\Db::table('cm_role_example')
            ->where('id', '=', $params['id'])
            ->update([
                'status' => $newStatus,
                'update_time' => time()
            ]);
            
        if ($result === false) {
            self::setError('状态修改失败');
            return false;
        }
        
        return true;
    }
    
    /**
     * @notes 获取示例类别列表
     * @return array
     * <AUTHOR>
     */
    public static function getCategoryList(): array
    {
        $lists = \think\facade\Db::table('cm_example_category')
            ->where('status', '=', 1)
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                    ->whereOr(function($q) {
                        $q->whereNull('delete_time');
                    });
            })
            ->order('sort desc, id desc')
            ->select()
            ->toArray();
        
        return ['lists' => $lists];
    }
    
    /**
     * @notes 根据类别ID获取角色示例列表
     * @param int $categoryId
     * @return array
     * <AUTHOR>
     */
    public static function getListByCategoryId(int $categoryId): array
    {
        $lists = \think\facade\Db::table('cm_role_example')
            ->where([
                'category_id' => $categoryId,
                'status' => 1
            ])
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                    ->whereOr(function($q) {
                        $q->whereNull('delete_time');
                    });
            })
            ->order('sort desc, id desc')
            ->select()
            ->toArray();
        
        return ['lists' => $lists];
    }
    
    /**
     * @notes 获取所有角色示例（按类别分组）
     * @return array
     * <AUTHOR>
     */
    public static function getAllRoleExamples(): array
    {
        // 获取所有启用的类别
        $categories = \think\facade\Db::table('cm_example_category')
            ->where('status', 1)
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                    ->whereOr(function($q) {
                        $q->whereNull('delete_time');
                    });
            })
            ->order('sort desc, id desc')
            ->select()
            ->toArray();
        
        if (empty($categories)) {
            return ['lists' => []];
        }
        
        // 获取所有角色示例
        $examples = \think\facade\Db::table('cm_role_example')
            ->where('status', 1)
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                    ->whereOr(function($q) {
                        $q->whereNull('delete_time');
                    });
            })
            ->order('sort desc, id desc')
            ->select()
            ->toArray();
        
        if (empty($examples)) {
            return ['lists' => []];
        }
        
        // 将示例按类别分组
        $examplesByCategory = [];
        foreach ($examples as $example) {
            $categoryId = $example['category_id'];
            if (!isset($examplesByCategory[$categoryId])) {
                $examplesByCategory[$categoryId] = [];
            }
            $examplesByCategory[$categoryId][] = $example;
        }
        
        // 构建结果
        $result = [];
        foreach ($categories as $category) {
            $categoryId = $category['id'];
            if (isset($examplesByCategory[$categoryId]) && !empty($examplesByCategory[$categoryId])) {
                $result[] = [
                    'category' => $category,
                    'examples' => $examplesByCategory[$categoryId]
                ];
            }
        }
        
        return ['lists' => $result];
    }
} 