<?php
/**
 * 模型删除前的会员套餐限制检查服务
 */

namespace app\adminapi\logic\setting\ai;

use app\common\model\chat\Models;
use app\common\model\chat\ModelsCost;
use app\common\model\member\MemberPackage;
use app\common\model\member\MemberPackageApply;

class ModelDeletionChecker
{
    /**
     * 检查大模型是否可以删除
     * @param int $modelId 大模型ID
     * @return array 检查结果
     */
    public static function checkMainModelDeletion(int $modelId): array
    {
        $result = [
            'can_delete' => true,
            'message' => '',
            'affected_packages' => [],
            'affected_sub_models' => [],
            'suggestions' => []
        ];

        // 1. 获取该大模型下的所有子模型
        $subModels = ModelsCost::where(['model_id' => $modelId])
            ->field('id,name,alias,status')
            ->select()
            ->toArray();

        if (empty($subModels)) {
            return $result; // 没有子模型，可以直接删除
        }

        $result['affected_sub_models'] = $subModels;

        // 2. 检查这些子模型是否被会员套餐使用
        $subModelIds = array_column($subModels, 'id');
        
        // 检查基于大模型ID的限制（旧方案兼容）
        $packageApplies = MemberPackageApply::alias('mpa')
            ->join('member_package mp', 'mpa.package_id = mp.id')
            ->where(['mpa.channel' => $modelId, 'mpa.type' => 1]) // type=1表示对话模型
            ->field('mpa.id,mpa.package_id,mpa.status,mpa.day_limit,mp.name as package_name,mpa.sub_model_id')
            ->select()
            ->toArray();

        // 检查基于子模型ID的限制（新方案）
        $subModelApplies = MemberPackageApply::alias('mpa')
            ->join('member_package mp', 'mpa.package_id = mp.id')
            ->whereIn('mpa.sub_model_id', $subModelIds)
            ->where(['mpa.type' => 1])
            ->field('mpa.id,mpa.package_id,mpa.sub_model_id,mpa.status,mpa.day_limit,mp.name as package_name')
            ->select()
            ->toArray();

        $allAffectedApplies = array_merge($packageApplies, $subModelApplies);

        if (!empty($allAffectedApplies)) {
            $result['can_delete'] = false;
            $result['affected_packages'] = $allAffectedApplies;
            
            // 按套餐分组
            $packageGroups = [];
            foreach ($allAffectedApplies as $apply) {
                $packageGroups[$apply['package_id']][] = $apply;
            }

            $packageNames = array_unique(array_column($allAffectedApplies, 'package_name'));
            
            $result['message'] = sprintf(
                '该模型正在被 %d 个会员套餐使用：%s。请先在会员等级管理中取消这些套餐对该模型的限制配置，然后再进行删除操作。',
                count($packageGroups),
                implode('、', $packageNames)
            );

            $result['suggestions'] = [
                '1. 进入【营销中心】→【会员等级管理】',
                '2. 逐个编辑以下套餐，取消对该模型的限制：',
            ];

            foreach ($packageGroups as $packageId => $applies) {
                $packageName = $applies[0]['package_name'];
                $modelCount = count($applies);
                $result['suggestions'][] = "   - {$packageName}（涉及 {$modelCount} 个模型限制）";
            }

            $result['suggestions'][] = '3. 保存套餐配置后，即可删除该模型';
        }

        return $result;
    }

    /**
     * 检查子模型是否可以删除
     * @param int $subModelId 子模型ID
     * @return array 检查结果
     */
    public static function checkSubModelDeletion(int $subModelId): array
    {
        $result = [
            'can_delete' => true,
            'message' => '',
            'affected_packages' => [],
            'suggestions' => []
        ];

        // 获取子模型信息
        $subModel = ModelsCost::where(['id' => $subModelId])
            ->field('id,model_id,name,alias')
            ->find();

        if (!$subModel) {
            return $result;
        }

        // 检查是否被会员套餐直接限制（新方案）
        $directApplies = MemberPackageApply::alias('mpa')
            ->join('member_package mp', 'mpa.package_id = mp.id')
            ->where(['mpa.sub_model_id' => $subModelId])
            ->field('mpa.id,mpa.package_id,mpa.status,mpa.day_limit,mp.name as package_name')
            ->select()
            ->toArray();

        if (!empty($directApplies)) {
            $result['can_delete'] = false;
            $result['affected_packages'] = $directApplies;
            
            $packageNames = array_unique(array_column($directApplies, 'package_name'));
            
            $result['message'] = sprintf(
                '子模型"%s"正在被 %d 个会员套餐使用：%s。请先取消相关限制配置后再删除。',
                $subModel['alias'] ?: $subModel['name'],
                count($packageNames),
                implode('、', $packageNames)
            );

            $result['suggestions'] = [
                '1. 进入【营销中心】→【会员等级管理】',
                '2. 编辑相关套餐，取消对该子模型的限制',
                '3. 保存配置后即可删除该子模型'
            ];
        }

        return $result;
    }
} 