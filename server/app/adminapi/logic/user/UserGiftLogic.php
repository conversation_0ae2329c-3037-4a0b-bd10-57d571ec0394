<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\logic\user;

use app\common\logic\BaseLogic;
use app\common\model\UserGiftLog;
use app\common\model\UserGiftConfig;
use app\common\model\user\User;
use app\common\model\user\UserAccountLog;
use app\common\enum\user\AccountLogEnum;
use app\adminapi\lists\user\UserGiftRecordsLists;
use think\facade\Db;
use Exception;

/**
 * 用户赠送业务逻辑
 */
class UserGiftLogic extends BaseLogic
{
    /**
     * 获取赠送记录详情
     */
    public static function detail($id)
    {
        $giftLog = UserGiftLog::with(['fromUser', 'toUser', 'admin'])->find($id);
        if (!$giftLog) {
            self::setError('赠送记录不存在');
            return false;
        }
        
        $data = $giftLog->toArray();
        
        // 格式化数据
        $data['create_time'] = date('Y-m-d H:i:s', $data['create_time']);
        $data['update_time'] = $data['update_time'] ? date('Y-m-d H:i:s', $data['update_time']) : '';
        $data['gift_amount'] = number_format($data['gift_amount'], 7);
        
        // 用户信息
        $data['from_user_info'] = [
            'id' => $data['from_user']['id'] ?? 0,
            'nickname' => $data['from_user']['nickname'] ?? '用户已删除',
            'avatar' => $data['from_user']['avatar'] ?? '',
            'sn' => $data['from_user']['sn'] ?? ''
        ];
        
        $data['to_user_info'] = [
            'id' => $data['to_user']['id'] ?? 0,
            'nickname' => $data['to_user']['nickname'] ?? '用户已删除',
            'avatar' => $data['to_user']['avatar'] ?? '',
            'sn' => $data['to_user']['sn'] ?? ''
        ];
        
        // 操作管理员信息
        $data['admin_info'] = [
            'id' => $data['admin']['id'] ?? 0,
            'name' => $data['admin']['name'] ?? ''
        ];
        
        // 状态信息
        $statusMap = [
            UserGiftLog::STATUS_SUCCESS => '成功',
            UserGiftLog::STATUS_FAILED => '失败',
            UserGiftLog::STATUS_REVOKED => '已撤回'
        ];
        $data['status_text'] = $statusMap[$data['status']] ?? '未知';
        $data['can_revoke'] = $data['status'] == UserGiftLog::STATUS_SUCCESS;
        
        unset($data['from_user'], $data['to_user'], $data['admin']);
        
        return $data;
    }

    /**
     * 撤回赠送
     */
    public static function revoke($id, $adminId, $remark = '')
    {
        try {
            $giftLog = UserGiftLog::find($id);
            if (!$giftLog) {
                self::setError('赠送记录不存在');
                return false;
            }
            
            if ($giftLog->status != UserGiftLog::STATUS_SUCCESS) {
                self::setError('只能撤回成功状态的赠送记录');
                return false;
            }
            
            // 检查用户是否存在
            $fromUser = User::find($giftLog->from_user_id);
            $toUser = User::find($giftLog->to_user_id);
            
            if (!$fromUser || !$toUser) {
                self::setError('用户不存在，无法撤回');
                return false;
            }
            
            // 检查接收者余额是否足够
            if ($toUser->balance < $giftLog->gift_amount) {
                self::setError('接收者余额不足，无法撤回');
                return false;
            }
            
            Db::startTrans();
            try {
                // 1. 更新记录状态
                $giftLog->save([
                    'status' => UserGiftLog::STATUS_REVOKED,
                    'admin_id' => $adminId,
                    'remark' => $remark,
                    'update_time' => time()
                ]);
                
                // 2. 恢复用户余额
                $fromUser->inc('balance', $giftLog->gift_amount);
                $toUser->dec('balance', $giftLog->gift_amount);
                
                // 3. 记录账户流水
                UserAccountLog::add(
                    $fromUser->id,
                    AccountLogEnum::UM_INC_ADMIN_ADJUST,
                    AccountLogEnum::INC,
                    $giftLog->gift_amount,
                    $giftLog->gift_sn,
                    '管理员撤回赠送',
                    ['admin_id' => $adminId, 'remark' => $remark]
                );
                
                UserAccountLog::add(
                    $toUser->id,
                    AccountLogEnum::UM_DEC_ADMIN_ADJUST,
                    AccountLogEnum::DEC,
                    $giftLog->gift_amount,
                    $giftLog->gift_sn,
                    '管理员撤回赠送',
                    ['admin_id' => $adminId, 'remark' => $remark]
                );
                
                Db::commit();
                return true;
                
            } catch (Exception $e) {
                Db::rollback();
                self::setError('撤回失败：' . $e->getMessage());
                return false;
            }
            
        } catch (Exception $e) {
            self::setError('操作失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取赠送配置
     */
    public static function getConfig()
    {
        try {
            $config = UserGiftConfig::getConfig();
            return $config->toArray();
        } catch (Exception $e) {
            self::setError('获取配置失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 保存赠送配置
     */
    public static function saveConfig($params, $adminId)
    {
        try {
            // 数据验证
            if ($params['min_gift_amount'] > $params['max_gift_amount']) {
                self::setError('最小赠送金额不能大于最大赠送金额');
                return false;
            }
            
            if ($params['daily_gift_limit'] > $params['daily_receive_limit']) {
                self::setError('每日赠送限额不能大于每日接收限额');
                return false;
            }
            
            $result = UserGiftConfig::updateConfig($params);
            
            if ($result) {
                // 记录操作日志
                self::recordOperationLog($adminId, '修改赠送配置', $params);
                return true;
            } else {
                self::setError('配置保存失败');
                return false;
            }
            
        } catch (Exception $e) {
            self::setError('保存失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取统计数据
     */
    public static function getStatistics($params = [])
    {
        try {
            // 获取今日统计
            $today = date('Y-m-d');
            $todayStart = strtotime($today . ' 00:00:00');
            $todayEnd = strtotime($today . ' 23:59:59');
            
            // 今日赠送总额和笔数
            $todayStats = Db::name('user_gift_log')
                ->where('create_time', 'between', [$todayStart, $todayEnd])
                ->whereNull('delete_time')
                ->field('COUNT(*) as count, SUM(gift_amount) as amount')
                ->find();
            
            $todayGiftAmount = $todayStats['amount'] ? number_format($todayStats['amount'], 2) : '0.00';
            $todayGiftCount = $todayStats['count'] ?: 0;
            
            // 参与用户数（去重）
            $totalUsers = Db::name('user_gift_log')
                ->whereNull('delete_time')
                ->group('from_user_id')
                ->count();
            
            // 平均赠送金额
            $avgAmount = $todayGiftCount > 0 ? number_format($todayStats['amount'] / $todayGiftCount, 2) : '0.00';
            
            // 赠送排行榜（按总金额）
            $giftRanking = Db::name('user_gift_log')
                ->alias('gl')
                ->leftJoin('user u', 'gl.from_user_id = u.id')
                ->whereNull('gl.delete_time')
                ->field('gl.from_user_id as user_id, u.nickname, SUM(gl.gift_amount) as amount')
                ->group('gl.from_user_id')
                ->order('amount DESC')
                ->limit(10)
                ->select();
            
            // 格式化赠送排行榜
            $giftRanking = array_map(function($item) {
                return [
                    'user_id' => $item['user_id'],
                    'nickname' => $item['nickname'],
                    'amount' => number_format($item['amount'], 2)
                ];
            }, $giftRanking->toArray());
            
            // 接收排行榜（按总金额）
            $receiveRanking = Db::name('user_gift_log')
                ->alias('gl')
                ->leftJoin('user u', 'gl.to_user_id = u.id')
                ->whereNull('gl.delete_time')
                ->field('gl.to_user_id as user_id, u.nickname, SUM(gl.gift_amount) as amount')
                ->group('gl.to_user_id')
                ->order('amount DESC')
                ->limit(10)
                ->select();
            
            // 格式化接收排行榜
            $receiveRanking = array_map(function($item) {
                return [
                    'user_id' => $item['user_id'],
                    'nickname' => $item['nickname'],
                    'amount' => number_format($item['amount'], 2)
                ];
            }, $receiveRanking->toArray());
            
            // 最近记录
            $recentRecords = Db::name('user_gift_log')
                ->alias('gl')
                ->leftJoin('user fu', 'gl.from_user_id = fu.id')
                ->leftJoin('user tu', 'gl.to_user_id = tu.id')
                ->whereNull('gl.delete_time')
                ->field('gl.*, fu.nickname as from_user_nickname, tu.nickname as to_user_nickname')
                ->order('gl.create_time DESC')
                ->limit(10)
                ->select();
            
            // 格式化最近记录
            $recentRecords = array_map(function($item) {
                return [
                    'id' => $item['id'],
                    'gift_sn' => $item['gift_sn'],
                    'from_user_id' => $item['from_user_id'],
                    'to_user_id' => $item['to_user_id'],
                    'from_user_nickname' => $item['from_user_nickname'],
                    'to_user_nickname' => $item['to_user_nickname'],
                    'gift_amount' => number_format($item['gift_amount'], 2),
                    'create_time' => date('Y-m-d H:i:s', $item['create_time']),
                    'status' => 1
                ];
            }, $recentRecords->toArray());
            
            return [
                'stats' => [
                    'todayGiftAmount' => $todayGiftAmount,
                    'todayGiftCount' => (string)$todayGiftCount,
                    'totalUsers' => (string)$totalUsers,
                    'avgAmount' => $avgAmount
                ],
                'giftRanking' => $giftRanking,
                'receiveRanking' => $receiveRanking,
                'recentRecords' => $recentRecords
            ];
            
        } catch (Exception $e) {
            self::setError('获取统计数据失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 导出赠送记录
     */
    public static function exportRecords($params)
    {
        try {
            // 创建导出文件
            $filename = '赠送记录_' . date('YmdHis') . '.csv';
            $filepath = public_path() . 'uploads/export/' . $filename;
            
            // 确保目录存在
            $dir = dirname($filepath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            
            // 查询数据
            $recordsList = new UserGiftRecordsLists();
            $recordsList->setParams($params);
            $recordsList->setLimitStart(1);
            $recordsList->setLimitCount(10000); // 限制导出1万条
            
            $data = $recordsList->lists();
            
            // 创建CSV文件
            $fp = fopen($filepath, 'w');
            fputcsv($fp, [
                '流水号', '赠送者ID', '赠送者昵称', '接收者ID', '接收者昵称',
                '赠送金额', '赠送留言', '状态', '创建时间', '备注'
            ]);
            
            foreach ($data['data'] as $item) {
                fputcsv($fp, [
                    $item['gift_sn'],
                    $item['from_user_info']['id'],
                    $item['from_user_info']['nickname'],
                    $item['to_user_info']['id'],
                    $item['to_user_info']['nickname'],
                    $item['gift_amount'],
                    $item['gift_message'],
                    $item['status_text'],
                    $item['create_time'],
                    $item['remark']
                ]);
            }
            
            fclose($fp);
            
            return [
                'filename' => $filename,
                'filepath' => '/uploads/export/' . $filename,
                'total' => count($data['data'])
            ];
            
        } catch (Exception $e) {
            self::setError('导出失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取趋势数据
     */
    private static function getTrendData($startTime, $endTime)
    {
        $days = [];
        $amounts = [];
        $counts = [];
        
        $start = strtotime($startTime);
        $end = strtotime($endTime);
        
        for ($i = $start; $i <= $end; $i += 86400) {
            $date = date('Y-m-d', $i);
            $dayStart = strtotime($date . ' 00:00:00');
            $dayEnd = strtotime($date . ' 23:59:59');
            
            $dayCount = UserGiftLog::where('status', UserGiftLog::STATUS_SUCCESS)
                ->where('create_time', 'between', [$dayStart, $dayEnd])
                ->count();
                
            $dayAmount = UserGiftLog::where('status', UserGiftLog::STATUS_SUCCESS)
                ->where('create_time', 'between', [$dayStart, $dayEnd])
                ->sum('gift_amount');
            
            $days[] = date('m-d', $i);
            $counts[] = $dayCount;
            $amounts[] = round($dayAmount, 2);
        }
        
        return [
            'days' => $days,
            'counts' => $counts,
            'amounts' => $amounts
        ];
    }

    /**
     * 获取排行榜数据
     */
    private static function getRankingData($startTime, $endTime)
    {
        $startTimestamp = strtotime($startTime);
        $endTimestamp = strtotime($endTime . ' 23:59:59');
        
        // 赠送排行榜
        $giftRanking = UserGiftLog::alias('gl')
            ->leftJoin('user u', 'gl.from_user_id = u.id')
            ->field('gl.from_user_id, u.nickname, u.avatar, SUM(gl.gift_amount) as total_amount, COUNT(*) as total_count')
            ->where('gl.status', UserGiftLog::STATUS_SUCCESS)
            ->where('gl.create_time', 'between', [$startTimestamp, $endTimestamp])
            ->group('gl.from_user_id')
            ->order('total_amount DESC')
            ->limit(10)
            ->select()
            ->toArray();
        
        // 接收排行榜
        $receiveRanking = UserGiftLog::alias('gl')
            ->leftJoin('user u', 'gl.to_user_id = u.id')
            ->field('gl.to_user_id, u.nickname, u.avatar, SUM(gl.gift_amount) as total_amount, COUNT(*) as total_count')
            ->where('gl.status', UserGiftLog::STATUS_SUCCESS)
            ->where('gl.create_time', 'between', [$startTimestamp, $endTimestamp])
            ->group('gl.to_user_id')
            ->order('total_amount DESC')
            ->limit(10)
            ->select()
            ->toArray();
        
        return [
            'gift_ranking' => $giftRanking,
            'receive_ranking' => $receiveRanking
        ];
    }

    /**
     * 获取异常数据
     */
    private static function getAbnormalData($startTime, $endTime)
    {
        $startTimestamp = strtotime($startTime);
        $endTimestamp = strtotime($endTime . ' 23:59:59');
        
        // 大额赠送记录（超过500）
        $largeAmountLogs = UserGiftLog::alias('gl')
            ->leftJoin('user from_user', 'gl.from_user_id = from_user.id')
            ->leftJoin('user to_user', 'gl.to_user_id = to_user.id')
            ->field('gl.*, from_user.nickname as from_user_nickname, to_user.nickname as to_user_nickname')
            ->where('gl.gift_amount', '>', 500)
            ->where('gl.create_time', 'between', [$startTimestamp, $endTimestamp])
            ->order('gl.gift_amount DESC')
            ->limit(20)
            ->select()
            ->toArray();
        
        // 频繁操作用户（当天赠送超过5次）
        $frequentUsers = UserGiftLog::alias('gl')
            ->leftJoin('user u', 'gl.from_user_id = u.id')
            ->field('gl.from_user_id, u.nickname, COUNT(*) as gift_count')
            ->where('gl.create_time', 'between', [$startTimestamp, $endTimestamp])
            ->group('gl.from_user_id')
            ->having('gift_count > 5')
            ->order('gift_count DESC')
            ->limit(20)
            ->select()
            ->toArray();
        
        return [
            'large_amount_logs' => $largeAmountLogs,
            'frequent_users' => $frequentUsers
        ];
    }

    /**
     * 记录操作日志
     */
    private static function recordOperationLog($adminId, $action, $data = [])
    {
        // 这里可以记录操作日志，根据具体需求实现
        // 例如记录到 operation_log 表
    }
} 