<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\controller\knowledge;
use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\knowledge\ExampleContentLists;
use app\adminapi\logic\knowledge\ExampleContentLogic;
use app\adminapi\validate\knowledge\ExampleContentValidate;

/**
 * 示例库内容控制器
 * Class ExampleContentController
 * @package app\adminapi\controller\knowledge
 */
class ExampleContentController extends BaseAdminController
{
    /**
     * @notes 获取示例库内容列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function lists()
    {
        $lists = new ExampleContentLists();
        $data = $this->dataLists($lists);
        
        // 额外返回类别列表
        $data['data']['category_lists'] = $lists->getCategoryList();
        return $data;
    }

    /**
     * @notes 添加示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function add()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('add');
        $result = (new ExampleContentLogic())->add($params);
        if ($result === true) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 编辑示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function edit()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('edit');
        $result = (new ExampleContentLogic())->edit($params);
        if ($result === true) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 删除示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function delete()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('delete');
        $result = (new ExampleContentLogic())->del($params['id']);
        if ($result === true) {
            return $this->success('删除成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 获取示例库内容详情
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function detail()
    {
        $params = (new ExampleContentValidate())->goCheck('id');
        $result = (new ExampleContentLogic())->detail($params['id']);
        return $this->success('获取成功', $result);
    }

    /**
     * @notes 修改状态
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function status()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('status');
        (new ExampleContentLogic())->status($params['id']);
        return $this->success('修改成功', [], 1, 1);
    }
    
    /**
     * @notes 获取所有示例库类别及内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getAllExamples()
    {
        $result = (new ExampleContentLogic())->getAllExamples();
        return $this->success('获取成功', $result);
    }
} 