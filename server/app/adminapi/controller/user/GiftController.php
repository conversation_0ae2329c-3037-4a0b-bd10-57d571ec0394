<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\controller\user;

use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\user\UserGiftRecordsLists;
use app\adminapi\logic\user\UserGiftLogic;
use app\adminapi\validate\user\UserGiftValidate;

/**
 * 用户赠送管理控制器
 */
class GiftController extends BaseAdminController
{
    /**
     * 获取赠送记录列表
     */
    public function records()
    {
        try {
            // 简化版本，返回空列表
            return $this->data([
                'data' => [],
                'total' => 0,
                'current_page' => 1,
                'per_page' => 15,
                'last_page' => 1
            ]);
        } catch (Exception $e) {
            return $this->fail('获取列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取赠送记录详情
     */
    public function detail()
    {
        $params = (new UserGiftValidate())->goCheck('detail');
        $detail = UserGiftLogic::detail($params['id']);
        if ($detail === false) {
            return $this->fail(UserGiftLogic::getError());
        }
        return $this->data($detail);
    }

    /**
     * 撤回赠送记录
     */
    public function revoke()
    {
        $params = (new UserGiftValidate())->goCheck('revoke');
        $result = UserGiftLogic::revoke(
            $params['id'], 
            $this->adminInfo['id'], 
            $params['remark'] ?? ''
        );
        
        if ($result === false) {
            return $this->fail(UserGiftLogic::getError());
        }
        
        return $this->success('撤回成功');
    }

    /**
     * 导出赠送记录
     */
    public function export()
    {
        $params = (new UserGiftValidate())->goCheck('export');
        $result = UserGiftLogic::exportRecords($params);
        
        if ($result === false) {
            return $this->fail(UserGiftLogic::getError());
        }
        
        return $this->data($result);
    }

    /**
     * 获取赠送配置
     */
    public function getConfig()
    {
        $config = UserGiftLogic::getConfig();
        return $this->data($config);
    }

    /**
     * 保存赠送配置
     */
    public function saveConfig()
    {
        try {
            $params = $this->request->post();
            $result = UserGiftLogic::saveConfig($params, $this->adminInfo['admin_id']);
            
            if ($result === false) {
                return $this->fail(UserGiftLogic::getError());
            }
            
            return $this->success('保存成功');
        } catch (Exception $e) {
            return $this->fail('保存失败：' . $e->getMessage());
        }
    }

    /**
     * 获取统计数据
     */
    public function getStatistics()
    {
        try {
            $params = $this->request->param();
            $statistics = UserGiftLogic::getStatistics($params);
            
            if ($statistics === false) {
                return $this->fail(UserGiftLogic::getError());
            }
            
            return $this->data($statistics);
        } catch (Exception $e) {
            return $this->fail('获取统计数据失败：' . $e->getMessage());
        }
    }


} 