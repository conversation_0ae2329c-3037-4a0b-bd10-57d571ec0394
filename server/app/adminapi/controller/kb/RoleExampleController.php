<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\controller\kb;
use app\adminapi\controller\BaseAdminController;
use app\adminapi\logic\kb\RoleExampleLogic;
use app\adminapi\validate\kb\RoleExampleValidate;

/**
 * 智能体角色示例控制器
 * Class RoleExampleController
 * @package app\adminapi\controller\kb
 */
class RoleExampleController extends BaseAdminController
{
    /**
     * @notes 获取角色示例列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleLists()
    {
        $params = $this->request->get();
        $result = RoleExampleLogic::lists($params);
        return $this->data($result);
    }
    
    /**
     * @notes 获取角色示例详情
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleDetail()
    {
        $id = $this->request->get('id/d', 0);
        if (empty($id)) {
            return $this->fail('参数错误');
        }
        
        $result = RoleExampleLogic::detail($id);
        if ($result === false) {
            return $this->fail(RoleExampleLogic::getError());
        }
        
        return $this->data($result);
    }
    
    /**
     * @notes 添加角色示例
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleAdd()
    {
        $params = $this->request->post();
        
        // 数据验证
        $validate = new RoleExampleValidate();
        if (!$validate->scene('add')->check($params)) {
            return $this->fail($validate->getError());
        }
        
        $result = RoleExampleLogic::add($params);
        if ($result === false) {
            return $this->fail(RoleExampleLogic::getError());
        }
        
        return $this->success('添加成功');
    }
    
    /**
     * @notes 编辑角色示例
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleEdit()
    {
        $params = $this->request->post();
        
        // 数据验证
        $validate = new RoleExampleValidate();
        if (!$validate->scene('edit')->check($params)) {
            return $this->fail($validate->getError());
        }
        
        $result = RoleExampleLogic::edit($params);
        if ($result === false) {
            return $this->fail(RoleExampleLogic::getError());
        }
        
        return $this->success('编辑成功');
    }
    
    /**
     * @notes 删除角色示例
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleDel()
    {
        $params = $this->request->post();
        
        // 处理ID参数，支持单个ID和数组ID
        $id = $params['id'] ?? null;
        
        if (empty($id)) {
            return $this->fail('参数错误：ID不能为空');
        }
        
        // 处理不同格式的ID参数
        if (is_array($id)) {
            // 如果是数组，过滤掉无效值
            $id = array_filter($id, function($item) {
                return is_numeric($item) && intval($item) > 0;
            });
            
            if (empty($id)) {
                return $this->fail('参数错误：无效的ID数组');
            }
            
            // 转换为整数数组
            $id = array_map('intval', $id);
            
            // 如果只有一个元素，转为单个ID
            if (count($id) == 1) {
                $id = $id[0];
            }
        } else {
            // 单个ID，转换为整数
            $id = intval($id);
            if ($id <= 0) {
                return $this->fail('参数错误：无效的ID');
            }
        }
        
        $result = RoleExampleLogic::del($id);
        if ($result === false) {
            return $this->fail(RoleExampleLogic::getError());
        }
        
        return $this->success('删除成功');
    }
    
    /**
     * @notes 修改角色示例状态
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleStatus()
    {
        $params = $this->request->post();
        
        if (empty($params['id'])) {
            return $this->fail('参数错误');
        }
        
        $result = RoleExampleLogic::status($params);
        if ($result === false) {
            return $this->fail(RoleExampleLogic::getError());
        }
        
        return $this->success('状态修改成功');
    }
    
    /**
     * @notes 获取示例类别列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleCategoryList()
    {
        $result = RoleExampleLogic::getCategoryList();
        return $this->data($result);
    }
    
    /**
     * @notes 根据类别ID获取角色示例列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleListByCategory()
    {
        $categoryId = $this->request->get('category_id/d', 0);
        if (empty($categoryId)) {
            return $this->fail('参数错误');
        }
        
        $result = RoleExampleLogic::getListByCategoryId($categoryId);
        return $this->data($result);
    }
    
    /**
     * @notes 获取所有角色示例（按类别分组）
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function roleExampleAll()
    {
        $result = RoleExampleLogic::getAllRoleExamples();
        return $this->data($result);
    }
} 