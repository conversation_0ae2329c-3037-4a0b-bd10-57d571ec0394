<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\controller\kb;
use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\knowledge\ExampleContentLists;
use app\adminapi\logic\knowledge\ExampleContentLogic;
use app\adminapi\validate\knowledge\ExampleContentValidate;

/**
 * 示例库内容控制器
 * Class ExampleController
 * @package app\adminapi\controller\kb
 */
class ExampleController extends BaseAdminController
{
    /**
     * @notes 获取示例库内容列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function lists()
    {
        // 获取请求参数
        $pageNo = $this->request->get('page_no', 1);
        $pageSize = $this->request->get('page_size', 15);
        $offset = ($pageNo - 1) * $pageSize;
        
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        // 构建查询条件
        $where = [];
        
        // 查询所有未删除记录 - 兼容NULL值
        $where[] = function($query) {
            $query->where('EC.delete_time', '=', 0)
                  ->whereOr(function($q) {
                      $q->whereNull('EC.delete_time');
                  });
        };
        
        // 状态筛选
        $status = $this->request->get('status');
        if ($status !== null && $status !== '') {
            $where['EC.status'] = $status;
        }
        
        // 类别筛选
        $categoryId = $this->request->get('category_id');
        if ($categoryId) {
            $where['EC.category_id'] = $categoryId;
        }
        
        // 关键词搜索
        $keyword = $this->request->get('keyword');
        if ($keyword) {
            $where[] = ['EC.title|EC.question|EC.answer', 'like', "%{$keyword}%"];
        }
        
        // 获取数据列表
        $lists = \think\facade\Db::table($prefix.'example_content')
            ->alias('EC')
            ->leftJoin($prefix.'example_category C', 'C.id = EC.category_id')
            ->where($where)
            ->field('EC.*, C.name as category_name')
            ->limit($offset, $pageSize)
            ->order(['EC.sort' => 'desc', 'EC.id' => 'asc'])
            ->select()
            ->toArray();
        
        // 处理列表数据
        foreach ($lists as &$item) {
            $item['status_desc'] = $item['status'] == 1 ? '开启' : '关闭';
            $item['question_brief'] = mb_substr($item['question'], 0, 50) . (mb_strlen($item['question']) > 50 ? '...' : '');
            $item['answer_brief'] = mb_substr($item['answer'], 0, 50) . (mb_strlen($item['answer']) > 50 ? '...' : '');
            
            // 格式化创建时间
            if (isset($item['create_time']) && is_numeric($item['create_time'])) {
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            }
        }
        
        // 获取总数
        $count = \think\facade\Db::table($prefix.'example_content')
            ->alias('EC')
            ->where($where)
            ->count();
        
        // 获取类别列表
        $categoryList = \think\facade\Db::table($prefix.'example_category')
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            })
            ->where('status', 1)
            ->field('id, name')
            ->select()
            ->toArray();
        
        return $this->success('获取成功', [
            'lists' => $lists,
            'count' => $count,
            'page_no' => $pageNo,
            'page_size' => $pageSize,
            'category_lists' => $categoryList
        ]);
    }

    /**
     * @notes 添加示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function add()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('add');
        
        // 检查类别是否存在
        if (!empty($params['category_id'])) {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            $category = \think\facade\Db::table($prefix.'example_category')
                ->where('id', $params['category_id'])
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (empty($category)) {
                return $this->fail('选择的类别不存在或已被删除');
            }
        }
        
        $result = (new ExampleContentLogic())->add($params);
        if ($result === true) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 获取示例库内容详情
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function detail()
    {
        try {
            // 多种方式获取ID参数，优先级：GET > POST > param
            $id = $this->request->get('id', 0);
            if (!$id) {
                $id = $this->request->post('id', 0);
            }
            if (!$id) {
                $id = $this->request->param('id', 0);
            }
            
            // 转换为整数并严格验证
            $id = intval($id);
            
            if ($id <= 0) {
                return $this->fail('缺少有效的ID参数');
            }
            
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 查询详情
            $detail = \think\facade\Db::table($prefix.'example_content')
                ->where('id', '=', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (empty($detail)) {
                return $this->fail('未找到相关数据');
            }
            
            // 确保ID被正确返回和类型转换
            $detail['id'] = (int)$detail['id'];
            
            return $this->success('获取成功', $detail);
        } catch (\Exception $e) {
            return $this->fail('获取详情失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 编辑示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function edit()
    {
        try {
            $params = $this->request->post();
            
            // 严格验证ID参数
            if (empty($params['id']) || !is_numeric($params['id']) || $params['id'] <= 0) {
                return $this->fail('缺少有效的ID参数');
            }
            
            $id = (int)$params['id'];
            
            // 验证其他必要参数
            if (empty($params['title']) || empty($params['category_id']) || 
                !isset($params['question']) || !isset($params['answer'])) {
                return $this->fail('请填写完整的表单信息');
            }
            
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 先检查记录是否存在
            $exists = \think\facade\Db::table($prefix.'example_content')
                ->where('id', '=', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (empty($exists)) {
                return $this->fail('要编辑的数据不存在或已被删除');
            }
            
            // 构建更新数据
            $updateData = [
                'category_id' => (int)$params['category_id'],
                'title' => $params['title'],
                'question' => $params['question'],
                'answer' => $params['answer'],
                'sort' => isset($params['sort']) ? (int)$params['sort'] : 0,
                'status' => isset($params['status']) ? (int)$params['status'] : 1,
                'update_time' => time()
            ];
            
            // 执行更新
            $result = \think\facade\Db::table($prefix.'example_content')
                ->where('id', '=', $id)
                ->update($updateData);
            
            if ($result !== false) {
                return $this->success('编辑成功', ['id' => $id], 1, 1);
            }
            
            return $this->fail('编辑失败，请稍后再试');
        } catch (\Exception $e) {
            return $this->fail('编辑失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 删除示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function del()
    {
        $params = $this->request->post();
        
        // 严格验证ID参数
        if (empty($params['id']) || !is_numeric($params['id']) || $params['id'] <= 0) {
            return $this->fail('缺少有效的ID参数');
        }
        
        $id = intval($params['id']);
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        try {
            // 先检查记录是否存在
            $exists = \think\facade\Db::table($prefix.'example_content')
                ->where('id', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (!$exists) {
                return $this->fail('要删除的数据不存在或已被删除');
            }
            
            // 执行软删除
            $deleteTime = time();
            $result = \think\facade\Db::table($prefix.'example_content')
                ->where('id', $id)
                ->update(['delete_time' => $deleteTime]);
            
            if ($result) {
                return $this->success('删除成功', [], 1, 1);
            }
            
            return $this->fail('删除失败，请稍后再试');
        } catch (\Exception $e) {
            return $this->fail('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 修改状态
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function status()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('status');
        (new ExampleContentLogic())->status($params['id']);
        return $this->success('修改成功', [], 1, 1);
    }
    
    /**
     * @notes 根据类别获取示例列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getListByCategoryId()
    {
        $params = (new ExampleContentValidate())->goCheck('category_id');
        $result = (new ExampleContentLogic())->getListByCategoryId($params['category_id']);
        return $this->success('获取成功', $result);
    }
    
    /**
     * @notes 获取所有示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getAllExamples()
    {
        $result = (new ExampleContentLogic())->getAllExamples();
        return $this->success('获取成功', $result);
    }

    /**
     * @notes 获取类别下拉列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getCategoryList()
    {
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        // 查询所有开启状态的类别
        $lists = \think\facade\Db::table($prefix.'example_category')
            ->where('status', 1)
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            })
            ->field('id, name')
            ->order('sort', 'desc')
            ->select()
            ->toArray();
        
        return $this->success('获取成功', [
            'lists' => $lists
        ]);
    }
} 