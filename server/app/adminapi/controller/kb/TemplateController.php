<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\controller\kb;
use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\kb\TemplateLists;
use app\adminapi\logic\kb\TemplateLogic;
use app\adminapi\validate\kb\TemplateValidate;

/**
 * 模板库控制器
 * Class TemplateController
 * @package app\adminapi\controller\kb
 */
class TemplateController extends BaseAdminController
{
    /**
     * @notes 获取模板库列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function lists()
    {
        // 使用统一的参数名称：page_no、page_size，避免前后端不匹配
        $pageNo = $this->request->get('page_no', 1);
        $pageSize = $this->request->get('page_size', 15);
        
        return $this->dataLists(new TemplateLists());
    }

    /**
     * @notes 添加模板库
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function add()
    {
        $params = (new TemplateValidate())->post()->goCheck('add');
        
        // 检查类别是否存在
        if (!empty($params['category_id'])) {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            $category = \think\facade\Db::table($prefix.'example_category')
                ->where('id', $params['category_id'])
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (empty($category)) {
                return $this->fail('选择的类别不存在或已被删除');
            }
        }
        
        $result = (new TemplateLogic())->add($params);
        if ($result === true) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 获取模板库详情
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function detail()
    {
        try {
            // 多种方式获取ID参数，优先级：GET > POST > param
            $id = $this->request->get('id', 0);
            if (!$id) {
                $id = $this->request->post('id', 0);
            }
            if (!$id) {
                $id = $this->request->param('id', 0);
            }
            
            // 转换为整数并严格验证
            $id = intval($id);
            
            if ($id <= 0) {
                return $this->fail('缺少有效的ID参数');
            }
            
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 查询详情
            $detail = \think\facade\Db::table($prefix.'template')
                ->where('id', '=', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (empty($detail)) {
                return $this->fail('未找到相关数据');
            }
            
            // 确保ID被正确返回和类型转换
            $detail['id'] = (int)$detail['id'];
            $detail['category_id'] = (int)$detail['category_id'];
            $detail['sort'] = (int)$detail['sort'];
            $detail['status'] = (int)$detail['status'];
            $detail['download_count'] = (int)$detail['download_count'];
            
            return $this->success('获取成功', $detail);
        } catch (\Exception $e) {
            return $this->fail('获取详情失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 编辑模板库
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function edit()
    {
        try {
            $params = $this->request->post();
            
            // 严格验证ID参数
            if (empty($params['id']) || !is_numeric($params['id']) || $params['id'] <= 0) {
                return $this->fail('缺少有效的ID参数');
            }
            
            $id = (int)$params['id'];
            
            // 验证其他必要参数
            if (empty($params['name']) || empty($params['category_id']) || 
                empty($params['download_url'])) {
                return $this->fail('请填写完整的表单信息');
            }
            
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 先检查记录是否存在
            $exists = \think\facade\Db::table($prefix.'template')
                ->where('id', '=', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (empty($exists)) {
                return $this->fail('要编辑的数据不存在或已被删除');
            }
            
            // 构建更新数据
            $updateData = [
                'category_id' => (int)$params['category_id'],
                'name' => $params['name'],
                'description' => $params['description'] ?? '',
                'download_url' => $params['download_url'],
                'file_size' => $params['file_size'] ?? '',
                'file_type' => $params['file_type'] ?? '',
                'sort' => isset($params['sort']) ? (int)$params['sort'] : 0,
                'status' => isset($params['status']) ? (int)$params['status'] : 1,
                'update_time' => time()
            ];
            
            // 执行更新，使用参数化查询防止SQL注入
            $result = \think\facade\Db::table($prefix.'template')
                ->where('id', '=', $id)
                ->update($updateData);
            
            if ($result !== false) {
                return $this->success('编辑成功', ['id' => $id], 1, 1);
            }
            
            return $this->fail('编辑失败，请稍后再试');
        } catch (\Exception $e) {
            return $this->fail('编辑失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 删除模板库
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function del()
    {
        $params = $this->request->post();
        
        // 严格验证ID参数
        if (empty($params['id']) || !is_numeric($params['id']) || $params['id'] <= 0) {
            return $this->fail('缺少有效的ID参数');
        }
        
        $id = intval($params['id']);
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        try {
            // 先检查记录是否存在
            $exists = \think\facade\Db::table($prefix.'template')
                ->where('id', $id)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
            
            if (!$exists) {
                return $this->fail('要删除的数据不存在或已被删除');
            }
            
            // 执行软删除，使用参数化查询
            $deleteTime = time();
            $result = \think\facade\Db::table($prefix.'template')
                ->where('id', $id)
                ->update(['delete_time' => $deleteTime]);
            
            if ($result) {
                return $this->success('删除成功', [], 1, 1);
            }
            
            return $this->fail('删除失败，请稍后再试');
        } catch (\Exception $e) {
            return $this->fail('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 修改状态
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function status()
    {
        $params = (new TemplateValidate())->post()->goCheck('status');
        (new TemplateLogic())->status($params['id']);
        return $this->success('修改成功', [], 1, 1);
    }
    
    /**
     * @notes 根据类别获取模板列表（用于PC/H5端）
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getListByCategoryId()
    {
        $categoryId = $this->request->get('category_id', 0);
        if (!$categoryId) {
            return $this->fail('缺少类别ID参数');
        }
        
        $result = (new TemplateLogic())->getListByCategoryId($categoryId);
        return $this->success('获取成功', $result);
    }
    
    /**
     * @notes 获取所有模板（按类别分组，用于PC/H5端）
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getAllTemplates()
    {
        $result = (new TemplateLogic())->getAllTemplates();
        return $this->success('获取成功', $result);
    }
    
    /**
     * @notes 获取类别下拉列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getCategoryList()
    {
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        // 查询所有开启状态的类别
        $lists = \think\facade\Db::table($prefix.'example_category')
            ->where('status', 1)
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            })
            ->field('id, name')
            ->order('sort', 'desc')
            ->select()
            ->toArray();
        
        return $this->success('获取成功', [
            'lists' => $lists
        ]);
    }
    
    /**
     * @notes 下载模板（记录下载次数）
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function download()
    {
        $id = $this->request->get('id', 0);
        if (!$id) {
            return $this->fail('缺少模板ID参数');
        }
        
        // 增加下载次数
        (new TemplateLogic())->increaseDownloadCount($id);
        
        // 获取模板详情
        $detail = (new TemplateLogic())->detail($id);
        if (empty($detail)) {
            return $this->fail('模板不存在或已被删除');
        }
        
        return $this->success('获取成功', [
            'download_url' => $detail['download_url'],
            'name' => $detail['name']
        ]);
    }
} 