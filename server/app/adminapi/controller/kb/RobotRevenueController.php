<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\controller\kb;

use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\kb\KbRobotRevenueLists;
use app\adminapi\logic\kb\KbRobotRevenueLogic;
use think\response\Json;

/**
 * 智能体分成收益管理
 */
class RobotRevenueController extends BaseAdminController
{
    /**
     * @notes 分成收益列表
     * @return Json
     */
    public function lists(): Json
    {
        return $this->dataLists((new KbRobotRevenueLists()));
    }

    /**
     * @notes 分成收益统计
     * @return Json
     */
    public function statistics(): Json
    {
        $result = KbRobotRevenueLogic::getStatistics();
        return $this->data($result);
    }

    /**
     * @notes 批量结算分成收益
     * @return Json
     */
    public function batchSettle(): Json
    {
        $result = KbRobotRevenueLogic::batchSettle();
        
        if (!$result['success']) {
            // 部分失败或完全失败
            if (($result['total_success'] ?? 0) > 0) {
                $message = "批量结算部分成功：成功 " . ($result['total_success'] ?? 0) . " 笔，失败 " . ($result['total_failed'] ?? 0) . " 笔";
                return $this->success($message, $result, 1, 1);
            } else {
                $message = KbRobotRevenueLogic::getError() ?: '批量结算失败';
                return $this->fail($message, $result);
            }
        }
        
        // 完全成功
        if (($result['total_processed'] ?? 0) == 0) {
            $message = "没有待结算记录";
        } else {
            $message = "批量结算成功：处理 " . ($result['total_processed'] ?? 0) . " 笔记录，结算金额 " . number_format($result['total_amount'] ?? 0, 4);
            if (($result['batch_count'] ?? 0) > 1) {
                $message .= "，共 " . ($result['batch_count'] ?? 0) . " 批次";
            }
        }
        
        return $this->success($message, $result, 1, 1);
    }

    /**
     * @notes 获取分成配置
     * @return Json
     */
    public function getConfig(): Json
    {
        $result = KbRobotRevenueLogic::getConfig();
        return $this->data($result);
    }

    /**
     * @notes 设置分成配置
     * @return Json
     */
    public function setConfig(): Json
    {
        $params = $this->request->post();
        $result = KbRobotRevenueLogic::setConfig($params);
        if ($result === false) {
            return $this->fail(KbRobotRevenueLogic::getError());
        }
        return $this->success('配置保存成功', [], 1, 1);
    }
} 