<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\controller\kb;
use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\knowledge\ExampleCategoryLists;
use app\adminapi\logic\knowledge\ExampleCategoryLogic;
use app\adminapi\validate\knowledge\ExampleCategoryValidate;

/**
 * 示例库类别控制器
 * Class ExampleCategoryController
 * @package app\adminapi\controller\kb
 */
class ExampleCategoryController extends BaseAdminController
{
    /**
     * @notes 获取示例库类别列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function lists()
    {
        $lists = new ExampleCategoryLists();
        $result = $lists->lists();
        $count = $lists->count();
        
        // 获取分页参数
        $pageNo = $this->request->get('page_no', 1);
        $pageSize = $this->request->get('page_size', 15);
        
        return $this->success('获取成功', [
            'list' => $result,
            'count' => $count,
            'page_no' => $pageNo,
            'page_size' => $pageSize,
        ]);
    }

    /**
     * @notes 添加示例库类别
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function add()
    {
        $params = (new ExampleCategoryValidate())->post()->goCheck('add');
        $result = (new ExampleCategoryLogic())->add($params);
        if ($result === true) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 编辑示例库类别
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function edit()
    {
        $params = (new ExampleCategoryValidate())->post()->goCheck('edit');
        $result = (new ExampleCategoryLogic())->edit($params);
        if ($result === true) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 删除示例库类别
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function del()
    {
        $params = (new ExampleCategoryValidate())->post()->goCheck('delete');
        $result = (new ExampleCategoryLogic())->del($params['id']);
        if ($result === true) {
            return $this->success('删除成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 获取示例库类别详情
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function detail()
    {
        $params = (new ExampleCategoryValidate())->goCheck('id');
        $result = (new ExampleCategoryLogic())->detail($params['id']);
        return $this->success('获取成功', $result);
    }

    /**
     * @notes 修改状态
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function status()
    {
        $params = (new ExampleCategoryValidate())->post()->goCheck('status');
        (new ExampleCategoryLogic())->status($params['id']);
        return $this->success('修改成功', [], 1, 1);
    }
    
    /**
     * @notes 获取类别下拉列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getSelectList()
    {
        $result = (new ExampleCategoryLogic())->getSelectList();
        return $this->success('获取成功', $result);
    }
} 