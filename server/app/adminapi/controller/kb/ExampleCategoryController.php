<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\controller\kb;
use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\knowledge\ExampleCategoryLists;
use app\adminapi\logic\knowledge\ExampleCategoryLogic;
use app\adminapi\validate\knowledge\ExampleCategoryValidate;

/**
 * 示例库类别控制器
 * Class ExampleCategoryController
 * @package app\adminapi\controller\kb
 */
class ExampleCategoryController extends BaseAdminController
{
    /**
     * @notes 获取示例库类别列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function lists()
    {
        // 获取请求参数
        $pageNo = $this->request->get('page_no', 1);
        $pageSize = $this->request->get('page_size', 15);
        
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        // 构建查询条件
        $where = [];
        
        // 查询所有未删除记录 - 兼容NULL值
        $where[] = function($query) {
            $query->where('delete_time', '=', 0)
                  ->whereOr(function($q) {
                      $q->whereNull('delete_time');
                  });
        };
        
        // 状态筛选
        $status = $this->request->get('status');
        if ($status !== null && $status !== '') {
            $where['status'] = $status;
        }
        
        // 关键词搜索
        $keyword = $this->request->get('keyword');
        if ($keyword) {
            $where[] = ['name', 'like', "%{$keyword}%"];
        }
        
        // 获取数据列表
        $lists = \think\facade\Db::table($prefix.'example_category')
            ->where($where)
            ->order(['sort' => 'desc', 'id' => 'asc'])
            ->select()
            ->toArray();
        
        // 获取每个类别的示例数量并格式化数据
        foreach ($lists as &$item) {
            $item['status_desc'] = $item['status'] == 1 ? '开启' : '关闭';
            
            // 格式化创建时间
            if (isset($item['create_time']) && is_numeric($item['create_time'])) {
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            }
            
            // 获取关联内容数量
            $contentCount = \think\facade\Db::table($prefix.'example_content')
                ->where('category_id', $item['id'])
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->count();
            $item['example_content_count'] = $contentCount;
        }
        
        // 获取总数
        $count = count($lists);
        
        return $this->success('获取成功', [
            'lists' => $lists,
            'count' => $count,
            'page_no' => $pageNo,
            'page_size' => $pageSize
        ]);
    }

    /**
     * @notes 添加示例库类别
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function add()
    {
        $params = (new ExampleCategoryValidate())->post()->goCheck('add');
        $result = (new ExampleCategoryLogic())->add($params);
        if ($result === true) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 编辑示例库类别
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function edit()
    {
        $params = (new ExampleCategoryValidate())->post()->goCheck('edit');
        $result = (new ExampleCategoryLogic())->edit($params);
        if ($result === true) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 删除示例库类别
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function del()
    {
        $params = (new ExampleCategoryValidate())->post()->goCheck('delete');
        $result = (new ExampleCategoryLogic())->del($params['id']);
        if ($result === true) {
            return $this->success('删除成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 获取示例库类别详情
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function detail()
    {
        try {
            // 多种方式获取ID参数，优先级：GET > POST > param
            $id = $this->request->get('id', 0);
            if (!$id) {
                $id = $this->request->post('id', 0);
            }
            if (!$id) {
                $id = $this->request->param('id', 0);
            }
            
            // 转换为整数并严格验证
            $id = intval($id);
            
            if ($id <= 0) {
                return $this->fail('缺少有效的ID参数');
            }
            
            $result = (new ExampleCategoryLogic())->detail($id);
            
            if (empty($result)) {
                return $this->fail('数据不存在或已被删除');
            }
            
            // 确保返回的ID为整数类型
            $result['id'] = (int)$result['id'];
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->fail('获取详情失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 修改状态
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function status()
    {
        $params = (new ExampleCategoryValidate())->post()->goCheck('status');
        (new ExampleCategoryLogic())->status($params['id']);
        return $this->success('修改成功', [], 1, 1);
    }
    
    /**
     * @notes 获取类别下拉列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getSelectList()
    {
        // 使用固定的表前缀cm_
        $prefix = 'cm_';
        
        // 查询所有开启状态的类别
        $lists = \think\facade\Db::table($prefix.'example_category')
            ->where('status', 1)
            ->where(function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            })
            ->field('id, name')
            ->order('sort', 'desc')
            ->select()
            ->toArray();
        
        return $this->success('获取成功', [
            'lists' => $lists
        ]);
    }
} 