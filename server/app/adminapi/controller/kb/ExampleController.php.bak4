<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\controller\kb;
use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\knowledge\ExampleContentLists;
use app\adminapi\logic\knowledge\ExampleContentLogic;
use app\adminapi\validate\knowledge\ExampleContentValidate;

/**
 * 示例库内容控制器
 * Class ExampleController
 * @package app\adminapi\controller\kb
 */
class ExampleController extends BaseAdminController
{
    /**
     * @notes 获取示例库内容列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function lists()
    {
        try {
            // 获取请求参数
            $pageNo = $this->request->get('page_no', 1);
            $pageSize = $this->request->get('page_size', 15);
            $offset = ($pageNo - 1) * $pageSize;
            
            // 构建查询条件
            $where = [];
            
            // 状态筛选
            $status = $this->request->get('status');
            if ($status !== null && $status !== '') {
                $where['EC.status'] = $status;
            }
            
            // 类别筛选
            $categoryId = $this->request->get('category_id');
            if ($categoryId) {
                $where['EC.category_id'] = $categoryId;
            }
            
            // 关键词搜索
            $keyword = $this->request->get('keyword');
            if ($keyword) {
                $where[] = ['EC.title|EC.question|EC.answer', 'like', "%{$keyword}%"];
            }
            
            // 获取数据列表
            $lists = \app\common\model\knowledge\ExampleContent::getList($where, $pageSize, $offset);
            
            // 处理列表数据
            foreach ($lists as &$item) {
                $item['status_desc'] = $item['status'] == 1 ? '开启' : '关闭';
                $item['question_brief'] = mb_substr($item['question'], 0, 50) . (mb_strlen($item['question']) > 50 ? '...' : '');
                $item['answer_brief'] = mb_substr($item['answer'], 0, 50) . (mb_strlen($item['answer']) > 50 ? '...' : '');
            }
            
            // 获取总数
            $count = \app\common\model\knowledge\ExampleContent::where($where)->count();
            
            // 获取类别列表
            $categoryList = \app\common\model\knowledge\ExampleCategory::getList(['status' => 1]);
            
            // 调试信息
            trace('ExampleContent list count: ' . count($lists), 'debug');
            trace('ExampleContent total count: ' . $count, 'debug');
            trace('ExampleContent category count: ' . count($categoryList), 'debug');
            
            return $this->success('获取成功', [
                'list' => $lists,
                'count' => $count,
                'page_no' => $pageNo,
                'page_size' => $pageSize,
                'category_lists' => $categoryList,
                'debug_info' => [
                    'where' => $where,
                    'lists_count' => count($lists),
                    'category_count' => count($categoryList)
                ]
            ]);
        } catch (\Exception $e) {
            return $this->fail('示例内容列表加载失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 添加示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function add()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('add');
        $result = (new ExampleContentLogic())->add($params);
        if ($result === true) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 编辑示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function edit()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('edit');
        $result = (new ExampleContentLogic())->edit($params);
        if ($result === true) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 删除示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function del()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('delete');
        $result = (new ExampleContentLogic())->del($params['id']);
        if ($result === true) {
            return $this->success('删除成功', [], 1, 1);
        }
        return $this->fail($result);
    }

    /**
     * @notes 获取示例库内容详情
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function detail()
    {
        $params = (new ExampleContentValidate())->goCheck('id');
        $result = (new ExampleContentLogic())->detail($params['id']);
        return $this->success('获取成功', $result);
    }

    /**
     * @notes 修改状态
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function status()
    {
        $params = (new ExampleContentValidate())->post()->goCheck('status');
        (new ExampleContentLogic())->status($params['id']);
        return $this->success('修改成功', [], 1, 1);
    }
    
    /**
     * @notes 根据类别获取示例列表
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getListByCategoryId()
    {
        $params = (new ExampleContentValidate())->goCheck('category_id');
        $result = (new ExampleContentLogic())->getListByCategoryId($params['category_id']);
        return $this->success('获取成功', $result);
    }
    
    /**
     * @notes 获取所有示例库内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getAllExamples()
    {
        $result = (new ExampleContentLogic())->getAllExamples();
        return $this->success('获取成功', $result);
    }
} 