<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\lists\knowledge;
use app\adminapi\lists\BaseAdminDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\lists\ListsExcelInterface;
use app\common\model\knowledge\ExampleCategory;

/**
 * 示例库类别列表类
 * Class ExampleCategoryLists
 * @package app\adminapi\lists\knowledge
 */
class ExampleCategoryLists extends BaseAdminDataLists implements ListsSearchInterface,ListsExcelInterface
{
    /**
     * @notes 实现数据列表
     * @return array
     * <AUTHOR>
     */
    public function lists(): array
    {
        // 调试信息
        trace('ExampleCategory searchWhere: ' . json_encode($this->searchWhere), 'debug');
        
        $categoryLists = ExampleCategory::where($this->searchWhere)
            ->withCount('exampleContent')
            ->withoutField('update_time,delete_time')
            ->order(['sort'=>'desc','id'=>'asc'])
            ->select()
            ->toArray();

        foreach ($categoryLists as &$item) {
            $item['status_desc'] = $item['status'] == 1 ? '开启' : '关闭';
        }

        return $categoryLists;
    }

    /**
     * @notes 实现数据列表记录数
     * @return int
     * <AUTHOR>
     */
    public function count(): int
    {
        return ExampleCategory::where($this->searchWhere)->count();
    }

    /**
     * @notes 设置搜索条件
     * @return array
     * <AUTHOR>
     */
    public function setSearch(): array
    {
        return [
            '%like%' => ['name'],
            '=' => ['status']
        ];
    }

    /**
     * @notes 导出文件名
     * @return string
     * <AUTHOR>
     */
    public function setFileName(): string
    {
        return '示例库类别列表';
    }

    /**
     * @notes 导出字段
     * @return string[]
     * <AUTHOR>
     */
    public function setExcelFields(): array
    {
        return [
            'name' => '类别名称',
            'example_content_count' => '示例数量',
            'status_desc' => '状态',
            'sort' => '排序',
            'create_time' => '创建时间',
        ];
    }
} 