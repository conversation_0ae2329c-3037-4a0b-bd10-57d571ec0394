<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\lists\knowledge;
use app\adminapi\lists\BaseAdminDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\lists\ListsExcelInterface;
use app\common\model\knowledge\ExampleContent;
use app\common\model\knowledge\ExampleCategory;

/**
 * 示例库内容列表类
 * Class ExampleContentLists
 * @package app\adminapi\lists\knowledge
 */
class ExampleContentLists extends BaseAdminDataLists implements ListsSearchInterface,ListsExcelInterface
{
    /**
     * @notes 实现数据列表
     * @return array
     * <AUTHOR>
     */
    public function lists(): array
    {
        // 修改连接方式，使用LEFT JOIN确保即使没有关联数据也能获取内容
        $contentLists = ExampleContent::alias('EC')
            ->leftJoin('example_category C', 'C.id = EC.category_id')
            ->where($this->searchWhere)
            ->field('EC.*, C.name as category_name')
            ->limit($this->limitOffset, $this->limitLength)
            ->order(['EC.sort'=>'desc','EC.id'=>'asc'])
            ->select()
            ->toArray();

        foreach ($contentLists as &$item) {
            $item['status_desc'] = $item['status'] == 1 ? '开启' : '关闭';
            // 确保category_name字段存在
            if (!isset($item['category_name']) || empty($item['category_name'])) {
                $item['category_name'] = '未分类';
            }
            // 截断太长的问题和答案内容，仅用于列表显示
            $item['question_brief'] = mb_substr($item['question'], 0, 50) . (mb_strlen($item['question']) > 50 ? '...' : '');
            $item['answer_brief'] = mb_substr($item['answer'], 0, 50) . (mb_strlen($item['answer']) > 50 ? '...' : '');
        }

        return $contentLists;
    }

    /**
     * @notes 实现数据列表记录数
     * @return int
     * <AUTHOR>
     */
    public function count(): int
    {
        return ExampleContent::alias('EC')
            ->leftJoin('example_category C', 'C.id = EC.category_id')
            ->where($this->searchWhere)
            ->count();
    }

    /**
     * @notes 设置搜索条件
     * @return array
     * <AUTHOR>
     */
    public function setSearch(): array
    {
        return [
            '%like%' => ['EC.title', 'EC.question', 'EC.answer'],
            '=' => ['EC.status', 'EC.category_id']
        ];
    }

    /**
     * @notes 获取类别列表
     * @return array
     * <AUTHOR>
     */
    public function getCategoryList(): array
    {
        return ExampleCategory::where(['status' => 1])
            ->field('id,name')
            ->select()
            ->toArray();
    }

    /**
     * @notes 导出文件名
     * @return string
     * <AUTHOR>
     */
    public function setFileName(): string
    {
        return '示例库内容列表';
    }

    /**
     * @notes 导出字段
     * @return string[]
     * <AUTHOR>
     */
    public function setExcelFields(): array
    {
        return [
            'title' => '示例标题',
            'category_name' => '所属类别',
            'question' => '问题内容',
            'answer' => '答案内容',
            'status_desc' => '状态',
            'sort' => '排序',
            'create_time' => '创建时间',
        ];
    }
} 