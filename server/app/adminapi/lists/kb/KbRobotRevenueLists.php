<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\lists\kb;

use app\adminapi\lists\BaseAdminDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\service\FileService;

/**
 * 智能体分成收益列表
 */
class KbRobotRevenueLists extends BaseAdminDataLists implements ListsSearchInterface
{
    /**
     * @notes 列表
     * @return array
     */
    public function lists(): array
    {
        $lists = KbRobotRevenueLog::alias('rrl')
            ->field([
                'rrl.id', 'rrl.user_id', 'rrl.sharer_id', 'rrl.robot_id', 'rrl.square_id',
                'rrl.total_cost', 'rrl.share_amount', 'rrl.platform_amount', 'rrl.share_ratio',
                'rrl.settle_status', 'rrl.settle_time', 'rrl.create_time',
                'u1.nickname as user_nickname', 'u1.avatar as user_avatar',
                'u2.nickname as sharer_nickname', 'u2.avatar as sharer_avatar',
                'kr.name as robot_name', 'kr.image as robot_image'
            ])
            ->leftJoin('user u1', 'u1.id = rrl.user_id')
            ->leftJoin('user u2', 'u2.id = rrl.sharer_id')
            ->leftJoin('kb_robot kr', 'kr.id = rrl.robot_id')
            ->where($this->searchWhere)
            ->order('rrl.id desc')
            ->limit($this->limitOffset, $this->limitLength)
            ->select()
            ->toArray();

        foreach ($lists as &$item) {
            $item['user_avatar'] = FileService::getFileUrl($item['user_avatar']);
            $item['sharer_avatar'] = FileService::getFileUrl($item['sharer_avatar']);
            $item['robot_image'] = FileService::getFileUrl($item['robot_image']);
            $item['settle_status_text'] = $item['settle_status'] ? '已结算' : '待结算';
            
            // 处理settle_time：支持时间戳和字符串时间格式
            if ($item['settle_time']) {
                if (is_numeric($item['settle_time'])) {
                    // 时间戳格式
                    $item['settle_time_text'] = date('Y-m-d H:i:s', intval($item['settle_time']));
                } elseif (is_string($item['settle_time']) && strtotime($item['settle_time'])) {
                    // 字符串时间格式，验证有效性后使用
                    $item['settle_time_text'] = $item['settle_time'];
                } else {
                    $item['settle_time_text'] = '-';
                }
            } else {
                $item['settle_time_text'] = '-';
            }
            
            // 处理create_time：支持时间戳和字符串时间格式
            if (is_numeric($item['create_time'])) {
                // 时间戳格式
                $item['create_time_text'] = date('Y-m-d H:i:s', intval($item['create_time']));
            } elseif (is_string($item['create_time']) && strtotime($item['create_time'])) {
                // 字符串时间格式，验证有效性后使用
                $item['create_time_text'] = $item['create_time'];
            } else {
                // 其他情况使用原值或默认值
                $item['create_time_text'] = is_string($item['create_time']) ? $item['create_time'] : '-';
            }
        }

        return $lists;
    }

    /**
     * @notes 统计
     * @return int
     */
    public function count(): int
    {
        return KbRobotRevenueLog::alias('rrl')
            ->leftJoin('user u1', 'u1.id = rrl.user_id')
            ->leftJoin('user u2', 'u2.id = rrl.sharer_id')
            ->leftJoin('kb_robot kr', 'kr.id = rrl.robot_id')
            ->where($this->searchWhere)
            ->count();
    }

    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        $allowSearch = ['user_nickname', 'sharer_nickname', 'robot_name', 'settle_status', 'start_time', 'end_time'];
        return array_intersect_key($this->params, array_flip($allowSearch));
    }

    /**
     * @notes 搜索
     * @return array
     */
    public function queryWhere(): array
    {
        $where = [];
        
        // 使用者昵称搜索
        if (!empty($this->params['user_nickname'])) {
            $where[] = ['u1.nickname', 'like', '%' . $this->params['user_nickname'] . '%'];
        }
        
        // 分享者昵称搜索
        if (!empty($this->params['sharer_nickname'])) {
            $where[] = ['u2.nickname', 'like', '%' . $this->params['sharer_nickname'] . '%'];
        }
        
        // 智能体名称搜索
        if (!empty($this->params['robot_name'])) {
            $where[] = ['kr.name', 'like', '%' . $this->params['robot_name'] . '%'];
        }
        
        // 结算状态筛选
        if (isset($this->params['settle_status']) && $this->params['settle_status'] !== '') {
            $where[] = ['rrl.settle_status', '=', $this->params['settle_status']];
        }
        
        // 时间范围筛选
        if (!empty($this->params['start_time'])) {
            $where[] = ['rrl.create_time', '>=', strtotime($this->params['start_time'])];
        }
        
        if (!empty($this->params['end_time'])) {
            $where[] = ['rrl.create_time', '<=', strtotime($this->params['end_time'] . ' 23:59:59')];
        }
        
        return $where;
    }
} 