<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\lists\kb;
use app\adminapi\lists\BaseAdminDataLists;
// 移除模型引用，直接使用数据库操作
// use app\common\model\knowledge\Template;

/**
 * 模板库列表
 * Class TemplateLists
 * @package app\adminapi\lists\kb
 */
class TemplateLists extends BaseAdminDataLists
{
    /**
     * @notes 设置搜索条件
     * @return array
     * <AUTHOR>
     */
    public function setSearch(): array
    {
        $allowSearch = ['name', 'category_id', 'status', 'file_type'];
        return array_intersect(array_keys($this->params), $allowSearch);
    }

    /**
     * @notes 实现数据列表
     * @return array
     * <AUTHOR>
     */
    public function lists(): array
    {
        try {
            // 使用固定的表前缀cm_，避免前缀问题
            $prefix = 'cm_';
            
            // 构建查询条件
            $where = [];
            
            // 查询所有未删除记录 - 兼容NULL值
            $where[] = function($query) {
                $query->where('T.delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('T.delete_time');
                      });
            };
            
            // 状态筛选
            if (isset($this->params['status']) && $this->params['status'] !== '') {
                $where['T.status'] = $this->params['status'];
            }
            
            // 类别筛选
            if (isset($this->params['category_id']) && $this->params['category_id'] !== '') {
                $where['T.category_id'] = $this->params['category_id'];
            }
            
            // 文件类型筛选
            if (isset($this->params['file_type']) && $this->params['file_type'] !== '') {
                $where['T.file_type'] = $this->params['file_type'];
            }
            
            // 关键词搜索
            if (isset($this->params['name']) && $this->params['name'] !== '') {
                $where[] = ['T.name|T.description', 'like', '%' . $this->params['name'] . '%'];
            }
            
            // 查询模板列表，关联类别信息
            $templateLists = \think\facade\Db::table($prefix.'template')
                ->alias('T')
                ->leftJoin($prefix.'example_category C', 'C.id = T.category_id')
                ->where($where)
                ->field('T.*, C.name as category_name')
                ->limit($this->limitOffset, $this->limitLength)
                ->order(['T.sort' => 'desc', 'T.id' => 'desc'])
                ->select()
                ->toArray();

            foreach ($templateLists as &$item) {
                $item['status_desc'] = $item['status'] == 1 ? '开启' : '关闭';
                
                // 确保category_name字段存在
                if (!isset($item['category_name']) || empty($item['category_name'])) {
                    $item['category_name'] = '未分类';
                }
                
                // 格式化创建时间
                if (isset($item['create_time']) && is_numeric($item['create_time'])) {
                    $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                }
                
                // 文件大小处理
                $item['file_size'] = $item['file_size'] ?: '';
                
                // 下载次数格式化
                $item['download_count'] = (int)$item['download_count'];
            }

            return $templateLists;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     */
    public function count(): int
    {
        try {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 构建查询条件
            $where = [];
            
            // 查询所有未删除记录 - 兼容NULL值
            $where[] = function($query) {
                $query->where('delete_time', '=', 0)
                      ->whereOr(function($q) {
                          $q->whereNull('delete_time');
                      });
            };
            
            // 状态筛选
            if (isset($this->params['status']) && $this->params['status'] !== '') {
                $where['status'] = $this->params['status'];
            }
            
            // 类别筛选
            if (isset($this->params['category_id']) && $this->params['category_id'] !== '') {
                $where['category_id'] = $this->params['category_id'];
            }
            
            // 文件类型筛选
            if (isset($this->params['file_type']) && $this->params['file_type'] !== '') {
                $where['file_type'] = $this->params['file_type'];
            }
            
            // 关键词搜索
            if (isset($this->params['name']) && $this->params['name'] !== '') {
                $where[] = ['name|description', 'like', '%' . $this->params['name'] . '%'];
            }
            
            return \think\facade\Db::table($prefix.'template')
                ->where($where)
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }
} 