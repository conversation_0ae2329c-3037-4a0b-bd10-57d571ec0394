<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\lists\user;

use app\adminapi\lists\BaseAdminDataLists;
use app\common\model\user\User;
use app\common\lists\ListsSearchInterface;

/**
 * 用户赠送记录列表
 */
class UserGiftRecordsLists extends BaseAdminDataLists implements ListsSearchInterface
{
    /**
     * 设置搜索条件
     */
    public function setSearch(): array
    {
        $where = [];
        
        // 赠送者用户ID搜索
        if (!empty($this->params['from_user_id'])) {
            $where[] = ['gl.from_user_id', '=', $this->params['from_user_id']];
        }
        
        // 接收者用户ID搜索
        if (!empty($this->params['to_user_id'])) {
            $where[] = ['gl.to_user_id', '=', $this->params['to_user_id']];
        }
        
        // 状态筛选
        if (isset($this->params['status']) && $this->params['status'] !== '') {
            $where[] = ['gl.status', '=', $this->params['status']];
        }
        
        // 金额范围筛选
        if (!empty($this->params['min_amount'])) {
            $where[] = ['gl.gift_amount', '>=', $this->params['min_amount']];
        }
        if (!empty($this->params['max_amount'])) {
            $where[] = ['gl.gift_amount', '<=', $this->params['max_amount']];
        }
        
        // 时间范围筛选
        if (!empty($this->params['start_time'])) {
            $where[] = ['gl.create_time', '>=', strtotime($this->params['start_time'])];
        }
        if (!empty($this->params['end_time'])) {
            $where[] = ['gl.create_time', '<=', strtotime($this->params['end_time'] . ' 23:59:59')];
        }
        
        // 流水号搜索
        if (!empty($this->params['gift_sn'])) {
            $where[] = ['gl.gift_sn', 'like', '%' . $this->params['gift_sn'] . '%'];
        }
        
        return $where;
    }

    /**
     * 获取列表
     */
    public function lists(): array
    {
        $field = [
            'gl.id', 'gl.gift_sn', 'gl.from_user_id', 'gl.to_user_id',
            'gl.gift_amount', 'gl.gift_message', 'gl.status', 'gl.remark',
            'gl.admin_id', 'gl.create_time', 'gl.update_time',
            'from_user.nickname as from_user_nickname',
            'from_user.avatar as from_user_avatar',
            'from_user.sn as from_user_sn',
            'to_user.nickname as to_user_nickname', 
            'to_user.avatar as to_user_avatar',
            'to_user.sn as to_user_sn',
            'admin.name as admin_name'
        ];

        $lists = $this->createModel()
            ->alias('gl')
            ->field($field)
            ->leftJoin('user from_user', 'gl.from_user_id = from_user.id')
            ->leftJoin('user to_user', 'gl.to_user_id = to_user.id')
            ->leftJoin('admin admin', 'gl.admin_id = admin.id')
            ->where($this->setSearch())
            ->whereNull('gl.delete_time')
            ->order('gl.id', 'desc')
            ->paginate([
                'list_rows' => $this->pageSize,
                'page' => $this->pageNo,
            ])->toArray();

        foreach ($lists['data'] as &$item) {
            // 格式化时间
            $item['create_time'] = is_numeric($item['create_time']) ? 
                date('Y-m-d H:i:s', $item['create_time']) : $item['create_time'];
            $item['update_time'] = ($item['update_time'] && is_numeric($item['update_time'])) ? 
                date('Y-m-d H:i:s', $item['update_time']) : '';
            
            // 状态文本
            $item['status_text'] = $this->getStatusText($item['status']);
            
            // 格式化金额
            $item['gift_amount'] = number_format($item['gift_amount'], 7);
            
            // 处理用户信息
            $item['from_user_info'] = [
                'id' => $item['from_user_id'],
                'nickname' => $item['from_user_nickname'] ?: '用户已删除',
                'avatar' => $item['from_user_avatar'] ?: '',
                'sn' => $item['from_user_sn'] ?: ''
            ];
            
            $item['to_user_info'] = [
                'id' => $item['to_user_id'],
                'nickname' => $item['to_user_nickname'] ?: '用户已删除',
                'avatar' => $item['to_user_avatar'] ?: '',
                'sn' => $item['to_user_sn'] ?: ''
            ];
            
            // 操作记录
            $item['operator_info'] = [
                'admin_id' => $item['admin_id'],
                'admin_name' => $item['admin_name'] ?: ''
            ];
            
            // 是否可撤回（只有成功状态的记录可以撤回）
            $item['can_revoke'] = $item['status'] == 1;
            
            // 清理不需要的字段
            unset($item['from_user_nickname'], $item['from_user_avatar'], $item['from_user_sn']);
            unset($item['to_user_nickname'], $item['to_user_avatar'], $item['to_user_sn']);
            unset($item['admin_name']);
        }

        return $lists;
    }

    /**
     * 获取数量
     */
    public function count(): int
    {
        return $this->createModel()
            ->alias('gl')
            ->leftJoin('user from_user', 'gl.from_user_id = from_user.id')
            ->leftJoin('user to_user', 'gl.to_user_id = to_user.id')
            ->where($this->setSearch())
            ->whereNull('gl.delete_time')
            ->count();
    }

    /**
     * 创建模型
     */
    public function createModel()
    {
        return new \app\common\model\UserGiftLog();
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            1 => '成功',
            2 => '失败', 
            3 => '已撤回'
        ];
        
        return $statusMap[$status] ?? '未知';
    }

    /**
     * 获取统计数据
     */
    public function getStatistics($params = [])
    {
        $where = [];
        
        // 时间范围
        if (!empty($params['start_time'])) {
            $where[] = ['create_time', '>=', strtotime($params['start_time'])];
        }
        if (!empty($params['end_time'])) {
            $where[] = ['create_time', '<=', strtotime($params['end_time'] . ' 23:59:59')];
        }
        
        $model = $this->createModel();
        
        // 总赠送次数
        $totalCount = $model->where($where)->where(['status' => 1])->count();
        
        // 总赠送金额
        $totalAmount = $model->where($where)->where(['status' => 1])->sum('gift_amount');
        
        // 参与用户数（赠送者）
        $giftUserCount = $model->where($where)->where(['status' => 1])
            ->group('from_user_id')->count();
            
        // 参与用户数（接收者）
        $receiveUserCount = $model->where($where)->where(['status' => 1])
            ->group('to_user_id')->count();
        
        // 平均赠送金额
        $avgAmount = $totalCount > 0 ? $totalAmount / $totalCount : 0;
        
        // 成功率
        $failCount = $model->where($where)->where(['status' => 2])->count();
        $successRate = ($totalCount + $failCount) > 0 ? 
            $totalCount / ($totalCount + $failCount) * 100 : 0;
        
        return [
            'total_count' => $totalCount,
            'total_amount' => number_format($totalAmount, 7),
            'gift_user_count' => $giftUserCount,
            'receive_user_count' => $receiveUserCount,
            'avg_amount' => number_format($avgAmount, 7),
            'success_rate' => number_format($successRate, 2)
        ];
    }
} 