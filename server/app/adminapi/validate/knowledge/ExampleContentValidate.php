<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\validate\knowledge;
use app\common\validate\BaseValidate;

/**
 * 示例库内容验证器类
 * Class ExampleContentValidate
 * @package app\adminapi\validate\knowledge
 */
class ExampleContentValidate extends BaseValidate
{
    protected $rule = [
        'id' => 'require|integer',
        'category_id' => 'require|integer',
        'title' => 'require|max:200',
        'question' => 'require',
        'answer' => 'require',
        'sort' => 'integer',
        'status' => 'require|in:0,1',
    ];

    protected $message = [
        'id.require' => '请选择示例内容',
        'id.integer' => '示例内容ID必须是整数',
        'category_id.require' => '请选择所属类别',
        'category_id.integer' => '类别ID必须是整数',
        'title.require' => '请输入示例标题',
        'title.max' => '示例标题不能超过200个字符',
        'question.require' => '请输入问题内容',
        'answer.require' => '请输入答案内容',
        'sort.integer' => '排序必须是整数',
        'status.require' => '请选择状态',
        'status.in' => '状态值错误',
    ];

    protected $scene = [
        'id' => ['id'],
        'add' => ['category_id', 'title', 'question', 'answer', 'sort', 'status'],
        'edit' => ['id', 'category_id', 'title', 'question', 'answer', 'sort', 'status'],
        'delete' => ['id'],
        'status' => ['id', 'status'],
    ];
} 