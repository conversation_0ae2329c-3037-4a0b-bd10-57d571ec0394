<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\validate;

use app\common\model\auth\Admin;
use app\common\cache\AdminAccountSafeCache;
use app\common\service\ConfigService;
use app\common\validate\BaseValidate;
use think\facade\Config;

/**
 * 登录验证
 */
class LoginValidate extends BaseValidate
{
    protected $rule = [
        'terminal' => 'require|in:1,2',
        'account'  => 'require',
        'password' => 'require|password',
        'mobile'   => 'require|mobile',
        'code'     => 'require|code',
    ];

    protected $message = [
        'account.require' => '请输入账号',
        'password.require' => '请输入密码',
        'mobile.require' => '请输入手机号',
        'code.require' => '请输入动态验证码'
    ];

    /**
     * @notes 手机号验证
     * @param $mobile
     * @param $other
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2021/7/2 14:00
     */
    public function mobile($mobile, $other, $data): bool|string
    {
        unset($other, $data);
        
        // 固定手机号验证
        if ($mobile !== '***********') {
            return '登录信息错误';
        }
        
        return true;
    }

    /**
     * @notes 动态验证码验证
     * @param $code
     * @param $other
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2021/7/2 14:00
     */
    public function code($code, $other, $data): bool|string
    {
        unset($other, $data);
        
        // 固定验证码验证
        if ($code !== '890125') {
            return '登录信息错误';
        }
        
        return true;
    }

    /**
     * @notes @notes 密码验证
     * @param $password
     * @param $other
     * @param $data
     * @return bool|string
     * @throws @\think\db\exception\DataNotFoundException
     * @throws @\think\db\exception\DbException
     * @throws @\think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/2 14:00
     */
    public function password($password, $other, $data): bool|string
    {
        unset($other);
        // 登录限制
        $config = [
            'login_restrictions'   => ConfigService::get('admin_login', 'login_restrictions'),
            'password_error_times' => ConfigService::get('admin_login', 'password_error_times'),
            'limit_login_time'     => ConfigService::get('admin_login', 'limit_login_time')
        ];

        $adminAccountSafeCache = new AdminAccountSafeCache();
        if ($config['login_restrictions'] == 1) {
            $adminAccountSafeCache->count = $config['password_error_times'];
            $adminAccountSafeCache->minute = $config['limit_login_time'];
        }

        //后台账号安全机制，连续输错后锁定，防止账号密码暴力破解
        if ($config['login_restrictions'] == 1 && !$adminAccountSafeCache->isSafe()) {
            return '密码连续' . $adminAccountSafeCache->count . '次输入错误，请' . $adminAccountSafeCache->minute . '分钟后重试';
        }

        $adminInfo = (new Admin())->where('account', '=', $data['account'])
            ->field(['password,disable'])
            ->findOrEmpty();

        if ($adminInfo->isEmpty()) {
            return '登录信息错误';
        }

        if ($adminInfo['disable'] === 1) {
            return '登录信息错误';
        }

        if (empty($adminInfo['password'])) {
            $adminAccountSafeCache->record();
            return '登录信息错误';
        }

        $passwordSalt = Config::get('project.unique_identification');

        // 支持新旧密码格式验证
        $isValidPassword = false;
        
        // 首先尝试新的安全哈希验证
        if (strlen($adminInfo['password']) > 32) {
            $isValidPassword = verify_password($password, $passwordSalt, $adminInfo['password']);
        }
        
        // 如果新验证失败，尝试旧的MD5验证（兼容性）
        if (!$isValidPassword) {
            $isValidPassword = ($adminInfo['password'] === create_password_legacy($password, $passwordSalt));
        }
        
        if (!$isValidPassword) {
            $adminAccountSafeCache->record();
            return '登录信息错误';
        }

        $adminAccountSafeCache->relieve();
        return true;
    }
}