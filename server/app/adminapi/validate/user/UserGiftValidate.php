<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\validate\user;

use app\common\validate\BaseValidate;

/**
 * 用户赠送验证器
 */
class UserGiftValidate extends BaseValidate
{
    protected $rule = [
        'id' => 'require|integer|gt:0',
        'gift_sn' => 'require|alphaNum|length:32',
        'from_user_id' => 'require|integer|gt:0',
        'to_user_id' => 'require|integer|gt:0',
        'gift_amount' => 'require|float|gt:0',
        'gift_message' => 'max:500',
        'status' => 'in:1,2,3',
        'remark' => 'max:300',
        'start_time' => 'date',
        'end_time' => 'date',
        'page' => 'integer|gt:0',
        'limit' => 'integer|between:1,100',
        'is_enable' => 'in:0,1',
        'min_gift_amount' => 'require|float|egt:0',
        'max_gift_amount' => 'require|float|gt:0',
        'daily_gift_limit' => 'require|float|egt:0',
        'daily_receive_limit' => 'require|float|egt:0',
        'gift_times_limit' => 'require|integer|egt:0',
        'receive_times_limit' => 'require|integer|egt:0',
        'friend_only' => 'in:0,1',
        'need_verify' => 'in:0,1',
        'type' => 'alphaNum'
    ];

    protected $message = [
        'id.require' => '记录ID不能为空',
        'id.integer' => '记录ID必须为整数',
        'id.gt' => '记录ID必须大于0',
        'gift_sn.require' => '赠送流水号不能为空',
        'gift_sn.alphaNum' => '赠送流水号格式错误',
        'gift_sn.length' => '赠送流水号长度必须为32位',
        'from_user_id.require' => '赠送者用户ID不能为空',
        'from_user_id.integer' => '赠送者用户ID必须为整数',
        'from_user_id.gt' => '赠送者用户ID必须大于0',
        'to_user_id.require' => '接收者用户ID不能为空',
        'to_user_id.integer' => '接收者用户ID必须为整数',
        'to_user_id.gt' => '接收者用户ID必须大于0',
        'gift_amount.require' => '赠送金额不能为空',
        'gift_amount.float' => '赠送金额必须为数字',
        'gift_amount.gt' => '赠送金额必须大于0',
        'gift_message.max' => '赠送留言不能超过500个字符',
        'status.in' => '状态值不正确',
        'remark.max' => '备注信息不能超过300个字符',
        'start_time.date' => '开始时间格式错误',
        'end_time.date' => '结束时间格式错误',
        'page.integer' => '页码必须为整数',
        'page.gt' => '页码必须大于0',
        'limit.integer' => '每页数量必须为整数',
        'limit.between' => '每页数量必须在1-100之间',
        'is_enable.in' => '功能开关值不正确',
        'min_gift_amount.require' => '最小赠送金额不能为空',
        'min_gift_amount.float' => '最小赠送金额必须为数字',
        'min_gift_amount.egt' => '最小赠送金额不能为负数',
        'max_gift_amount.require' => '最大赠送金额不能为空',
        'max_gift_amount.float' => '最大赠送金额必须为数字',
        'max_gift_amount.gt' => '最大赠送金额必须大于0',
        'daily_gift_limit.require' => '每日赠送限额不能为空',
        'daily_gift_limit.float' => '每日赠送限额必须为数字',
        'daily_gift_limit.egt' => '每日赠送限额不能为负数',
        'daily_receive_limit.require' => '每日接收限额不能为空',
        'daily_receive_limit.float' => '每日接收限额必须为数字',
        'daily_receive_limit.egt' => '每日接收限额不能为负数',
        'gift_times_limit.require' => '每日赠送次数限制不能为空',
        'gift_times_limit.integer' => '每日赠送次数限制必须为整数',
        'gift_times_limit.egt' => '每日赠送次数限制不能为负数',
        'receive_times_limit.require' => '每日接收次数限制不能为空',
        'receive_times_limit.integer' => '每日接收次数限制必须为整数',
        'receive_times_limit.egt' => '每日接收次数限制不能为负数',
        'friend_only.in' => '好友限制开关值不正确',
        'need_verify.in' => '审核开关值不正确'
    ];

    /**
     * 赠送记录详情验证
     */
    public function detail()
    {
        return $this->only(['id']);
    }

    /**
     * 撤回赠送验证
     */
    public function revoke()
    {
        return $this->only(['id', 'remark']);
    }

    /**
     * 赠送记录列表验证
     */
    public function records()
    {
        return $this->only([
            'from_user_id', 'to_user_id', 'status', 'gift_amount',
            'start_time', 'end_time', 'page', 'limit'
        ]);
    }

    /**
     * 赠送配置保存验证
     */
    public function config()
    {
        $rules = [
            'is_enable', 'min_gift_amount', 'max_gift_amount',
            'daily_gift_limit', 'daily_receive_limit',
            'gift_times_limit', 'receive_times_limit',
            'friend_only', 'need_verify'
        ];
        
        // 添加自定义验证规则
        $this->extend('checkAmountLogic', function ($value, $rule, $data = []) {
            if (isset($data['min_gift_amount']) && isset($data['max_gift_amount'])) {
                if ($data['min_gift_amount'] > $data['max_gift_amount']) {
                    return '最小赠送金额不能大于最大赠送金额';
                }
            }
            return true;
        });
        
        $this->extend('checkLimitLogic', function ($value, $rule, $data = []) {
            if (isset($data['daily_gift_limit']) && isset($data['daily_receive_limit'])) {
                if ($data['daily_gift_limit'] > $data['daily_receive_limit']) {
                    return '每日赠送限额不能大于每日接收限额';
                }
            }
            return true;
        });
        
        // 添加验证规则
        $this->rule['max_gift_amount'][] = 'checkAmountLogic';
        $this->rule['daily_receive_limit'][] = 'checkLimitLogic';
        
        return $this->only($rules);
    }

    /**
     * 导出验证
     */
    public function export()
    {
        return $this->only([
            'from_user_id', 'to_user_id', 'status',
            'start_time', 'end_time'
        ]);
    }

    /**
     * 统计验证
     */
    public function statistics()
    {
        return $this->only(['start_time', 'end_time', 'type']);
    }
} 