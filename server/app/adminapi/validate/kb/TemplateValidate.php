<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\validate\kb;
use app\common\validate\BaseValidate;

/**
 * 模板库验证器
 * Class TemplateValidate  
 * @package app\adminapi\validate\knowledge
 */
class TemplateValidate extends BaseValidate
{
    protected $rule = [
        'id' => 'require|integer',
        'category_id' => 'require|integer',
        'name' => 'require|length:1,200',
        'description' => 'length:0,1000',
        'download_url' => 'require|url|length:1,500',
        'file_size' => 'length:0,50',
        'file_type' => 'length:0,50',
        'sort' => 'integer',
        'status' => 'require|in:0,1',
    ];

    protected $message = [
        'id.require' => '缺少模板ID',
        'id.integer' => '模板ID必须为整数',
        'category_id.require' => '请选择所属类别',
        'category_id.integer' => '类别ID必须为整数',
        'name.require' => '请输入模板名称',
        'name.length' => '模板名称长度须在1-200字符之间',
        'description.length' => '模板描述长度不能超过1000字符',
        'download_url.require' => '请输入下载链接地址',
        'download_url.url' => '下载链接格式不正确',
        'download_url.length' => '下载链接长度不能超过500字符',
        'file_size.length' => '文件大小描述长度不能超过50字符',
        'file_type.length' => '文件类型长度不能超过50字符',
        'sort.integer' => '排序必须为整数',
        'status.require' => '请选择状态',
        'status.in' => '状态值不正确',
    ];

    public function sceneAdd()
    {
        return $this->remove('id', true);
    }

    public function sceneEdit()
    {
        return $this->only(['id', 'category_id', 'name', 'description', 'download_url', 'file_size', 'file_type', 'sort', 'status']);
    }

    public function sceneDetail()
    {
        return $this->only(['id']);
    }

    public function sceneDelete()
    {
        return $this->only(['id']);
    }

    public function sceneStatus()
    {
        return $this->only(['id']);
    }
} 