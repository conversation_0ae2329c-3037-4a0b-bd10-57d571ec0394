<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\adminapi\validate\kb;

use app\common\validate\BaseValidate;

/**
 * 智能体角色示例验证器
 * Class RoleExampleValidate
 * @package app\adminapi\validate\kb
 */
class RoleExampleValidate extends BaseValidate
{
    protected $rule = [
        'id' => 'require|integer|gt:0',
        'category_id' => 'require|integer|gt:0',
        'title' => 'require|length:1,200',
        'content' => 'require|length:10,2000',
        'description' => 'length:0,500',
        'sort' => 'integer|egt:0',
        'status' => 'in:0,1'
    ];

    protected $message = [
        'id.require' => '参数错误',
        'id.integer' => '参数错误',
        'id.gt' => '参数错误',
        'category_id.require' => '请选择所属类别',
        'category_id.integer' => '类别参数错误',
        'category_id.gt' => '类别参数错误',
        'title.require' => '请输入示例标题',
        'title.length' => '示例标题长度须在1-200字符之间',
        'content.require' => '请输入角色设定内容',
        'content.length' => '角色设定内容长度须在10-2000字符之间',
        'description.length' => '示例描述长度不能超过500字符',
        'sort.integer' => '排序必须为整数',
        'sort.egt' => '排序不能小于0',
        'status.in' => '状态值错误'
    ];

    /**
     * @notes 添加场景
     * @return RoleExampleValidate
     * <AUTHOR>
     */
    public function sceneAdd()
    {
        return $this->only(['category_id', 'title', 'content', 'description', 'sort', 'status']);
    }

    /**
     * @notes 编辑场景
     * @return RoleExampleValidate
     * <AUTHOR>
     */
    public function sceneEdit()
    {
        return $this->only(['id', 'category_id', 'title', 'content', 'description', 'sort', 'status']);
    }

    /**
     * @notes 详情场景
     * @return RoleExampleValidate
     * <AUTHOR>
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

    /**
     * @notes 删除场景
     * @return RoleExampleValidate
     * <AUTHOR>
     */
    public function sceneDel()
    {
        return $this->only(['id']);
    }
} 