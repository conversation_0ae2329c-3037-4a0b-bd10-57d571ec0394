<?php
declare(strict_types=1);

namespace app\api\logic;

use app\common\model\user\User;
use app\common\model\UserGiftLog;
use app\common\model\UserGiftConfig;
use app\common\model\user\UserAccountLog;
use app\common\enum\user\AccountLogEnum;
use app\common\service\ConfigService;
use app\common\service\FileService;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 用户赠送逻辑类
 */
class UserGiftLogic
{
    /**
     * 获取赠送配置
     */
    public static function getGiftConfig(): array
    {
        $config = UserGiftConfig::find(1);
        if (!$config) {
            return [
                'is_enable' => 1,
                'min_gift_amount' => 1.0,
                'max_gift_amount' => 1000.0,
                'daily_gift_limit' => 100.0,
                'daily_receive_limit' => 500.0,
                'gift_times_limit' => 10,
                'receive_times_limit' => 20,
                'friend_only' => 0,
                'need_verify' => 0
            ];
        }
        return $config->toArray();
    }
    
    /**
     * 获取用户赠送记录
     */
    public static function getUserRecords(array $params): array
    {
        try {
            $userId = $params['user_id'];
            $page = intval($params['page'] ?? $params['page_no'] ?? 1);
            $limit = intval($params['limit'] ?? $params['page_size'] ?? 20);
            $type = $params['type'] ?? 'all'; // all, send, receive
            
            // 使用原生SQL查询避免ORM问题
            $where = '';
            $whereParams = [];
            
            if ($type == 'send') {
                $where = 'WHERE from_user_id = ?';
                $whereParams = [$userId];
            } elseif ($type == 'receive') {
                $where = 'WHERE to_user_id = ?';
                $whereParams = [$userId];
            } else {
                $where = 'WHERE (from_user_id = ? OR to_user_id = ?)';
                $whereParams = [$userId, $userId];
            }
            
            // 添加软删除条件
            $where .= ' AND delete_time IS NULL';
            
            // 获取总数
            $countSql = "SELECT COUNT(*) as count FROM cm_user_gift_log {$where}";
            $countResult = Db::query($countSql, $whereParams);
            $count = $countResult[0]['count'] ?? 0;
            
            // 获取列表数据
            $offset = ($page - 1) * $limit;
            $listSql = "SELECT * FROM cm_user_gift_log {$where} ORDER BY create_time DESC LIMIT {$offset}, {$limit}";
            $lists = Db::query($listSql, $whereParams);
            
            // 处理列表数据
            foreach ($lists as &$item) {
                $item['type'] = ($item['from_user_id'] == $userId) ? 'send' : 'receive';
                $item['from_user_nickname'] = '用户' . $item['from_user_id'];
                $item['to_user_nickname'] = '用户' . $item['to_user_id'];
                $item['from_user_avatar'] = '';
                $item['to_user_avatar'] = '';
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            }
            
            return [
                'lists' => $lists,
                'count' => $count,
                'page' => $page,
                'limit' => $limit
            ];
            
        } catch (\Exception $e) {
            Log::error('获取赠送记录失败：' . $e->getMessage());
            return [
                'lists' => [],
                'count' => 0,
                'page' => intval($params['page'] ?? $params['page_no'] ?? 1),
                'limit' => intval($params['limit'] ?? $params['page_size'] ?? 20)
            ];
        }
    }
    
    /**
     * 根据用户ID获取用户信息（只支持通过用户编号sn字段查询，带频次限制）
     */
    public static function getUserById($userSn, int $currentUserId): array
    {
        if (empty($userSn)) {
            throw new \Exception('请输入用户ID');
        }
        
        // 验证用户ID格式（8位数字）
        if (!preg_match('/^\d{8}$/', $userSn)) {
            throw new \Exception('请输入正确的用户ID（8位数字）');
        }
        
        // 检查搜索频次限制
        self::checkSearchLimit($currentUserId);
        
        $user = User::where('sn', $userSn)->find();
        if (!$user) {
            throw new \Exception('未查询到用户信息，请确认用户ID是否正确');
        }
        
        // 不能给自己赠送
        if ($user['id'] == $currentUserId) {
            throw new \Exception('不能给自己赠送');
        }
        
        // 检查用户状态
        if ($user['is_disable']) {
            throw new \Exception('该用户已被禁用，无法赠送');
        }
        
        return [
            'id' => $user['id'],
            'sn' => $user['sn'],
            'nickname' => $user['nickname'],
            'avatar' => $user['avatar'] ? FileService::getFileUrl($user['avatar']) : ''
        ];
    }
    
    /**
     * 检查搜索频次限制
     */
    private static function checkSearchLimit(int $userId): void
    {
        // 使用安全的缓存键，防止缓存键冲突
        $userCacheKey = "user_search_limit_" . hash('sha256', $userId . config('app.app_key', 'default'));
        $ipCacheKey = "search_ip_limit_" . hash('sha256', request()->ip());
        
        $searchData = Cache::get($userCacheKey, [
            'count' => 0,
            'last_time' => 0
        ]);
        
        $ipSearchData = Cache::get($ipCacheKey, [
            'count' => 0,
            'last_time' => 0
        ]);
        
        $now = time();
        $searchLimit = 3; // 降低到每分钟最多搜索3次
        $timeWindow = 60; // 时间窗口60秒
        $minInterval = 10; // 增加到最小间隔10秒
        $ipSearchLimit = 10; // IP级别限制：每分钟最多10次
        
        // IP级别频率检查
        if ($now - $ipSearchData['last_time'] <= $timeWindow && $ipSearchData['count'] >= $ipSearchLimit) {
            self::logSecurityEvent('ip_search_limit_exceeded', [
                'ip' => request()->ip(),
                'count' => $ipSearchData['count']
            ]);
            throw new \Exception('搜索过于频繁，请稍后再试');
        }
        
        // 重置时间窗口
        if ($now - $searchData['last_time'] > $timeWindow) {
            $searchData['count'] = 0;
        }
        
        if ($now - $ipSearchData['last_time'] > $timeWindow) {
            $ipSearchData['count'] = 0;
        }
        
        // 检查搜索间隔
        if ($now - $searchData['last_time'] < $minInterval) {
            self::logSecurityEvent('user_search_interval_violation', [
                'user_id' => $userId,
                'interval' => $now - $searchData['last_time']
            ]);
            throw new \Exception('搜索过于频繁，请稍后再试');
        }
        
        // 检查搜索次数限制
        if ($searchData['count'] >= $searchLimit) {
            self::logSecurityEvent('user_search_limit_exceeded', [
                'user_id' => $userId,
                'count' => $searchData['count']
            ]);
            throw new \Exception('搜索次数过多，请1分钟后再试');
        }
        
        // 更新搜索记录
        $searchData['count']++;
        $searchData['last_time'] = $now;
        
        $ipSearchData['count'] = ($now - $ipSearchData['last_time'] > $timeWindow) ? 1 : $ipSearchData['count'] + 1;
        $ipSearchData['last_time'] = $now;
        
        // 缓存搜索记录，设置过期时间为时间窗口的2倍
        Cache::set($userCacheKey, $searchData, $timeWindow * 2);
        Cache::set($ipCacheKey, $ipSearchData, $timeWindow * 2);
    }
    
    /**
     * 获取最近赠送用户
     */
    public static function getRecentGiftUsers(int $userId): array
    {
        try {
            $recentUsers = UserGiftLog::with(['toUser'])
                ->where('from_user_id', $userId)
                ->where('status', UserGiftLog::STATUS_SUCCESS)
                ->order('create_time', 'desc')
                ->limit(10)
                ->group('to_user_id')
                ->select();
            
            $users = [];
            foreach ($recentUsers as $log) {
                if ($log->toUser) {
                    $users[] = [
                        'id' => $log->toUser->id,
                        'nickname' => $log->toUser->nickname,
                        'avatar' => $log->toUser->avatar ? FileService::getFileUrl($log->toUser->avatar) : ''
                    ];
                }
            }
            
            return $users;
        } catch (\Exception $e) {
            Log::warning('获取最近赠送用户失败：' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取用户赠送统计
     */
    public static function getUserStatistics(int $userId): array
    {
        try {
            // 本月统计
            $monthStart = strtotime(date('Y-m-01 00:00:00'));
            $monthEnd = strtotime(date('Y-m-t 23:59:59'));
            
            $monthSend = UserGiftLog::where('from_user_id', $userId)
                ->where('status', UserGiftLog::STATUS_SUCCESS)
                ->where('create_time', 'between', [$monthStart, $monthEnd])
                ->sum('gift_amount');
                
            $monthReceive = UserGiftLog::where('to_user_id', $userId)
                ->where('status', UserGiftLog::STATUS_SUCCESS)
                ->where('create_time', 'between', [$monthStart, $monthEnd])
                ->sum('gift_amount');
            
            // 总计统计
            $totalSend = UserGiftLog::where('from_user_id', $userId)
                ->where('status', UserGiftLog::STATUS_SUCCESS)
                ->sum('gift_amount');
                
            $totalReceive = UserGiftLog::where('to_user_id', $userId)
                ->where('status', UserGiftLog::STATUS_SUCCESS)
                ->sum('gift_amount');
            
            // 今日统计
            $dailyGiftAmount = UserGiftLog::getTodayGiftAmount($userId);
            $dailyReceiveAmount = UserGiftLog::getTodayReceiveAmount($userId);
            $dailyGiftTimes = UserGiftLog::getTodayGiftTimes($userId);
            $dailyReceiveTimes = UserGiftLog::getTodayReceiveTimes($userId);
            
            return [
                'monthSend' => (float)$monthSend,
                'monthReceive' => (float)$monthReceive,
                'totalSend' => (float)$totalSend,
                'totalReceive' => (float)$totalReceive,
                'dailyGiftAmount' => (float)$dailyGiftAmount,
                'dailyReceiveAmount' => (float)$dailyReceiveAmount,
                'dailyGiftTimes' => (int)$dailyGiftTimes,
                'dailyReceiveTimes' => (int)$dailyReceiveTimes
            ];
            
        } catch (\Exception $e) {
            Log::warning('获取用户赠送统计失败：' . $e->getMessage());
            return [
                'monthSend' => 0.0,
                'monthReceive' => 0.0,
                'totalSend' => 0.0,
                'totalReceive' => 0.0,
                'dailyGiftAmount' => 0.0,
                'dailyReceiveAmount' => 0.0,
                'dailyGiftTimes' => 0,
                'dailyReceiveTimes' => 0
            ];
        }
    }
    
    /**
     * 执行赠送操作
     */
    public static function executeGift(array $params): array
    {
        // 添加分布式锁防止并发操作
        $lockKey = "gift_lock_{$params['from_user_id']}_{$params['to_user_id']}_" . time();
        $lockValue = uniqid();
        
        try {
            // 尝试获取分布式锁，30秒超时
            $lockAcquired = \think\facade\Cache::store('redis')->setex($lockKey, 30, $lockValue);
            if (!$lockAcquired) {
                return ['success' => false, 'message' => '操作过于频繁，请稍后再试'];
            }
            
            // 1. 参数验证
            $validated = self::validateGiftParams($params);
            if (!$validated['success']) {
                return $validated;
            }
            
            // 使用清理后的参数
            $cleanParams = $validated['params'] ?? $params;
            
            // 2. 获取配置
            $config = self::getGiftConfig();
            if (!$config['is_enable']) {
                return ['success' => false, 'message' => '赠送功能已关闭'];
            }
            
            // 3. 业务规则检查
            $checkResult = self::checkGiftRules($cleanParams, $config);
            if (!$checkResult['success']) {
                return $checkResult;
            }
            
            // 4. 执行赠送事务（使用安全的事务处理）
            return self::processGiftTransactionSafe($cleanParams, $config);
            
        } catch (\Exception $e) {
            // 记录安全事件
            self::logSecurityEvent('gift_operation_error', [
                'user_id' => $params['from_user_id'],
                'error' => $e->getMessage()
            ]);
            Log::error('赠送操作异常：' . $e->getMessage());
            return ['success' => false, 'message' => '系统繁忙，请稍后重试'];
        } finally {
            // 释放分布式锁
            if (isset($lockAcquired) && $lockAcquired) {
                $currentValue = \think\facade\Cache::store('redis')->get($lockKey);
                if ($currentValue === $lockValue) {
                    \think\facade\Cache::store('redis')->delete($lockKey);
                }
            }
        }
    }
    
    /**
     * 参数验证
     */
    private static function validateGiftParams(array $params): array
    {
        // 严格的参数类型检查
        if (!isset($params['from_user_id']) || !is_int($params['from_user_id']) || $params['from_user_id'] <= 0) {
            return ['success' => false, 'message' => '参数错误'];
        }
        
        if (!isset($params['to_user_id']) || !is_int($params['to_user_id']) || $params['to_user_id'] <= 0) {
            return ['success' => false, 'message' => '参数错误'];
        }
        
        // 金额验证 - 使用bcmath避免浮点数精度问题
        if (!isset($params['gift_amount']) || !is_numeric($params['gift_amount'])) {
            return ['success' => false, 'message' => '赠送金额无效'];
        }
        
        // 检查金额范围，防止溢出和负数
        if (bccomp($params['gift_amount'], '0', 7) <= 0) {
            return ['success' => false, 'message' => '赠送金额必须大于0'];
        }
        
        if (bccomp($params['gift_amount'], '999999.9999999', 7) > 0) {
            return ['success' => false, 'message' => '赠送金额过大'];
        }
        
        // 不能给自己赠送
        if ($params['from_user_id'] == $params['to_user_id']) {
            return ['success' => false, 'message' => '不能给自己赠送'];
        }
        
        // 留言内容过滤和验证
        if (isset($params['gift_message'])) {
            // 过滤HTML标签和特殊字符
            $params['gift_message'] = trim(strip_tags($params['gift_message']));
            $params['gift_message'] = htmlspecialchars($params['gift_message'], ENT_QUOTES, 'UTF-8');
            
            // 检查长度
            if (mb_strlen($params['gift_message'], 'UTF-8') > 500) {
                return ['success' => false, 'message' => '留言内容过长'];
            }
            
            // 检查是否包含敏感词（使用简化版本，只读取文件敏感词库）
            if (class_exists('\app\common\service\SimpleSensitiveService')) {
                try {
                    \app\common\service\PermanentSensitiveService::sensitive($params['gift_message']);
                } catch (\Exception $e) {
                    return ['success' => false, 'message' => '留言内容包含敏感词'];
                }
            }
        } else {
            $params['gift_message'] = '';
        }
        
        return ['success' => true, 'params' => $params];
    }
    
    /**
     * 业务规则检查（预检查，主要检查在事务中重新验证）
     */
    private static function checkGiftRules(array $params, array $config): array
    {
        // 1. 金额范围检查
        if (bccomp($params['gift_amount'], $config['min_gift_amount'], 7) < 0 || 
            bccomp($params['gift_amount'], $config['max_gift_amount'], 7) > 0) {
            return ['success' => false, 'message' => '赠送金额超出限制范围'];
        }
        
        // 2. 基础用户状态检查（在事务中会重新检查）
        $fromUser = User::where('id', $params['from_user_id'])->find();
        $toUser = User::where('id', $params['to_user_id'])->find();
        
        if (!$fromUser || !$toUser) {
            return ['success' => false, 'message' => '用户不存在或已被禁用'];
        }
        
        if ($fromUser['is_disable'] || $toUser['is_disable']) {
            return ['success' => false, 'message' => '用户已被禁用'];
        }
        
        // 3. 预检查余额（在事务中会使用行锁重新检查）
        if (bccomp($fromUser['balance'], $params['gift_amount'], 7) < 0) {
            return ['success' => false, 'message' => '余额不足'];
        }
        
        // 4. 每日限额检查（使用当前时间）
        $today = date('Y-m-d');
        $dailyGiftAmount = self::getDailyGiftAmount($params['from_user_id'], $today);
        $dailyReceiveAmount = self::getDailyReceiveAmount($params['to_user_id'], $today);
        
        if (bcadd($dailyGiftAmount, $params['gift_amount'], 7) > $config['daily_gift_limit']) {
            // 记录限额超出事件
            self::logSecurityEvent('daily_gift_limit_exceeded', [
                'user_id' => $params['from_user_id'],
                'current_amount' => $dailyGiftAmount,
                'attempt_amount' => $params['gift_amount'],
                'limit' => $config['daily_gift_limit']
            ]);
            return ['success' => false, 'message' => '超过每日赠送限额'];
        }
        
        if (bcadd($dailyReceiveAmount, $params['gift_amount'], 7) > $config['daily_receive_limit']) {
            self::logSecurityEvent('daily_receive_limit_exceeded', [
                'user_id' => $params['to_user_id'],
                'current_amount' => $dailyReceiveAmount,
                'attempt_amount' => $params['gift_amount'],
                'limit' => $config['daily_receive_limit']
            ]);
            return ['success' => false, 'message' => '对方超过每日接收限额'];
        }
        
        // 5. 每日次数检查
        $dailyGiftTimes = self::getDailyGiftTimes($params['from_user_id'], $today);
        $dailyReceiveTimes = self::getDailyReceiveTimes($params['to_user_id'], $today);
        
        if ($dailyGiftTimes >= $config['gift_times_limit']) {
            self::logSecurityEvent('daily_gift_times_exceeded', [
                'user_id' => $params['from_user_id'],
                'current_times' => $dailyGiftTimes,
                'limit' => $config['gift_times_limit']
            ]);
            return ['success' => false, 'message' => '超过每日赠送次数限制'];
        }
        
        if ($dailyReceiveTimes >= $config['receive_times_limit']) {
            self::logSecurityEvent('daily_receive_times_exceeded', [
                'user_id' => $params['to_user_id'],
                'current_times' => $dailyReceiveTimes,
                'limit' => $config['receive_times_limit']
            ]);
            return ['success' => false, 'message' => '对方超过每日接收次数限制'];
        }
        
        return ['success' => true];
    }
    
    /**
     * 处理赠送事务
     */
    private static function processGiftTransaction(array $params, array $config): array
    {
        Db::startTrans();
        try {
            // 1. 生成赠送流水号
            $giftSn = self::generateGiftSn();
            
            // 2. 使用原生SQL直接更新用户余额
            $fromUserUpdate = Db::execute(
                'UPDATE cm_user SET balance = balance - ? WHERE id = ? AND balance >= ?',
                [$params['gift_amount'], $params['from_user_id'], $params['gift_amount']]
            );
            
            if (!$fromUserUpdate) {
                throw new \Exception('赠送者余额更新失败或余额不足');
            }
            
            $toUserUpdate = Db::execute(
                'UPDATE cm_user SET balance = balance + ? WHERE id = ?',
                [$params['gift_amount'], $params['to_user_id']]
            );
            
            if (!$toUserUpdate) {
                throw new \Exception('接收者余额更新失败');
            }
            
            // 3. 创建赠送记录
            $giftLog = UserGiftLog::create([
                'gift_sn' => $giftSn,
                'from_user_id' => $params['from_user_id'],
                'to_user_id' => $params['to_user_id'],
                'gift_amount' => $params['gift_amount'],
                'gift_message' => $params['gift_message'] ?? '',
                'status' => 1,
                'create_time' => time()
            ]);
            
            // 4. 记录账户流水
            UserAccountLog::add(
                $params['from_user_id'],
                AccountLogEnum::UM_DEC_GIFT_SEND,
                AccountLogEnum::DEC,
                $params['gift_amount'],
                $giftSn,
                '赠送灵感值',
                ['to_user_id' => $params['to_user_id'], 'message' => $params['gift_message']]
            );
            
            UserAccountLog::add(
                $params['to_user_id'],
                AccountLogEnum::UM_INC_GIFT_RECEIVE,
                AccountLogEnum::INC,
                $params['gift_amount'],
                $giftSn,
                '接收赠送灵感值',
                ['from_user_id' => $params['from_user_id'], 'message' => $params['gift_message']]
            );
            
            Db::commit();
            
            return [
                'success' => true,
                'message' => '赠送成功',
                'data' => [
                    'gift_sn' => $giftSn,
                    'gift_id' => $giftLog->id
                ]
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('赠送事务失败：' . $e->getMessage());
            return ['success' => false, 'message' => '赠送失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 安全的事务处理方法（防止竞态条件）
     */
    private static function processGiftTransactionSafe(array $params, array $config): array
    {
        Db::startTrans();
        try {
            // 1. 生成赠送流水号
            $giftSn = self::generateGiftSn();
            
            // 2. 使用行级锁重新验证用户状态和余额
            $fromUser = Db::table('cm_user')
                ->where('id', $params['from_user_id'])
                ->lock(true) // 行级锁
                ->find();
                
            if (!$fromUser) {
                throw new \Exception('赠送者用户不存在');
            }
            
            if ($fromUser['is_disable']) {
                throw new \Exception('赠送者账户已被禁用');
            }
            
            // 使用bccomp进行精确的余额比较
            if (bccomp($fromUser['balance'], $params['gift_amount'], 7) < 0) {
                throw new \Exception('余额不足');
            }
            
            $toUser = Db::table('cm_user')
                ->where('id', $params['to_user_id'])
                ->lock(true) // 行级锁
                ->find();
                
            if (!$toUser) {
                throw new \Exception('接收者用户不存在');
            }
            
            if ($toUser['is_disable']) {
                throw new \Exception('接收者账户已被禁用');
            }
            
            // 3. 原子性更新用户余额
            $fromUserUpdate = Db::execute(
                'UPDATE cm_user SET balance = balance - ? WHERE id = ? AND balance >= ?',
                [$params['gift_amount'], $params['from_user_id'], $params['gift_amount']]
            );
            
            if (!$fromUserUpdate) {
                throw new \Exception('余额更新失败，可能余额不足');
            }
            
            $toUserUpdate = Db::execute(
                'UPDATE cm_user SET balance = balance + ? WHERE id = ?',
                [$params['gift_amount'], $params['to_user_id']]
            );
            
            if (!$toUserUpdate) {
                throw new \Exception('接收者余额更新失败');
            }
            
            // 4. 创建赠送记录
            $giftLog = UserGiftLog::create([
                'gift_sn' => $giftSn,
                'from_user_id' => $params['from_user_id'],
                'to_user_id' => $params['to_user_id'],
                'gift_amount' => $params['gift_amount'],
                'gift_message' => $params['gift_message'] ?? '',
                'status' => 1,
                'create_time' => time()
            ]);
            
            // 5. 记录账户流水
            UserAccountLog::add(
                $params['from_user_id'],
                AccountLogEnum::UM_DEC_GIFT_SEND,
                AccountLogEnum::DEC,
                $params['gift_amount'],
                $giftSn,
                '赠送灵感值',
                ['to_user_id' => $params['to_user_id'], 'message' => $params['gift_message']]
            );
            
            UserAccountLog::add(
                $params['to_user_id'],
                AccountLogEnum::UM_INC_GIFT_RECEIVE,
                AccountLogEnum::INC,
                $params['gift_amount'],
                $giftSn,
                '接收赠送灵感值',
                ['from_user_id' => $params['from_user_id'], 'message' => $params['gift_message']]
            );
            
            Db::commit();
            
            // 记录成功事件
            self::logSecurityEvent('gift_success', [
                'from_user_id' => $params['from_user_id'],
                'to_user_id' => $params['to_user_id'],
                'amount' => $params['gift_amount'],
                'gift_sn' => $giftSn
            ]);
            
            return [
                'success' => true,
                'message' => '赠送成功',
                'data' => [
                    'gift_sn' => $giftSn,
                    'gift_id' => $giftLog->id
                ]
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            
            // 记录失败事件
            self::logSecurityEvent('gift_transaction_failed', [
                'from_user_id' => $params['from_user_id'],
                'to_user_id' => $params['to_user_id'],
                'amount' => $params['gift_amount'],
                'error' => $e->getMessage()
            ]);
            
            Log::error('安全事务处理失败：' . $e->getMessage());
            return ['success' => false, 'message' => self::getSafeErrorMessage($e)];
        }
    }

    /**
     * 记录安全事件
     */
    private static function logSecurityEvent(string $event, array $data): void
    {
        try {
            $logData = [
                'event' => $event,
                'user_id' => $data['user_id'] ?? ($data['from_user_id'] ?? 0),
                'ip' => request()->ip(),
                'user_agent' => request()->header('user-agent'),
                'timestamp' => time(),
                'data' => $data
            ];
            
            // 使用专门的安全日志通道
            Log::channel('security')->warning("赠送安全事件: {$event}", $logData);
        } catch (\Exception $e) {
            // 记录日志失败不应该影响主流程
            Log::error('安全事件记录失败：' . $e->getMessage());
        }
    }

    /**
     * 获取安全的错误信息（防止信息泄露）
     */
    private static function getSafeErrorMessage(\Exception $e): string
    {
        $message = $e->getMessage();
        
        // 允许的安全错误信息
        $safeMessages = [
            '余额不足',
            '用户不存在',
            '账户已被禁用',
            '赠送金额超出限制',
            '超过每日赠送限额',
            '超过每日接收限额',
            '超过每日赠送次数限制',
            '超过每日接收次数限制',
            '不能给自己赠送',
            '赠送功能已关闭'
        ];
        
        foreach ($safeMessages as $safe) {
            if (strpos($message, $safe) !== false) {
                return $safe;
            }
        }
        
        // 记录详细错误到日志，但只返回通用错误给用户
        Log::error('赠送操作详细错误：' . $message);
        return '系统繁忙，请稍后重试';
    }
    
    /**
     * 生成赠送流水号
     */
    private static function generateGiftSn(): string
    {
        // 使用更安全的随机数生成方法
        $timestamp = date('YmdHis');
        $microtime = sprintf('%06d', microtime(true) * 1000000 % 1000000);
        $random = sprintf('%06d', random_int(0, 999999));
        
        return 'GFT' . $timestamp . $microtime . $random;
    }
    
    /**
     * 获取每日赠送金额
     */
    private static function getDailyGiftAmount(int $userId, string $date): float
    {
        $startTime = strtotime($date . ' 00:00:00');
        $endTime = strtotime($date . ' 23:59:59');
        
        $amount = UserGiftLog::where('from_user_id', $userId)
            ->where('status', 1)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->sum('gift_amount');
            
        return (float)$amount;
    }
    
    /**
     * 获取每日接收金额
     */
    private static function getDailyReceiveAmount(int $userId, string $date): float
    {
        $startTime = strtotime($date . ' 00:00:00');
        $endTime = strtotime($date . ' 23:59:59');
        
        $amount = UserGiftLog::where('to_user_id', $userId)
            ->where('status', 1)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->sum('gift_amount');
            
        return (float)$amount;
    }
    
    /**
     * 获取每日赠送次数
     */
    private static function getDailyGiftTimes(int $userId, string $date): int
    {
        $startTime = strtotime($date . ' 00:00:00');
        $endTime = strtotime($date . ' 23:59:59');
        
        $count = UserGiftLog::where('from_user_id', $userId)
            ->where('status', 1)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->count();
            
        return (int)$count;
    }
    
    /**
     * 获取每日接收次数
     */
    private static function getDailyReceiveTimes(int $userId, string $date): int
    {
        $startTime = strtotime($date . ' 00:00:00');
        $endTime = strtotime($date . ' 23:59:59');
        
        $count = UserGiftLog::where('to_user_id', $userId)
            ->where('status', 1)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->count();
            
        return (int)$count;
    }
} 