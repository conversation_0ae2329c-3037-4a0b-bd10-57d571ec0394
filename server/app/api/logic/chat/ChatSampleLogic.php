<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\api\logic\chat;

use app\common\logic\BaseLogic;
use app\common\model\chat\ChatCategory;
use think\db\Query;

/**
 * 问题示例逻辑类
 */
class ChatSampleLogic extends BaseLogic
{
    /**
     * @notes 问题示例列表
     * @return array
     * @throws @\think\db\exception\DataNotFoundException
     * @throws @\think\db\exception\DbException
     * @throws @\think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2023/4/18 17:56
     */
    public function samplesLists(): array
    {
        return (new ChatCategory())
            ->with(['sample' => function(Query $query) {
                $query->field('id,content,category_id')
                    ->where(['status'=>1])
                    ->order(['sort'=>'desc','id'=>'desc'])
                    ->select();
            }])
            ->order(['sort'=>'desc','id'=>'desc'])
            ->where(['status'=>1])
            ->field('id,name,image')
            ->select()
            ->toArray();
    }
}