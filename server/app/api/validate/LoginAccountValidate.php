<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\api\validate;

use app\common\cache\UserAccountSafeCache;
use app\common\enum\LoginEnum;
use app\common\enum\notice\NoticeEnum;
use app\common\enum\user\UserTerminalEnum;
use app\common\enum\YesNoEnum;
use app\common\service\ConfigService;
use app\common\service\EmailService;
use app\common\service\sms\SmsDriver;
use app\common\validate\BaseValidate;
use think\facade\Log;
use app\common\model\user\User;
use think\facade\Config;

/**
 * 账号密码登录校验
 */
class LoginAccountValidate extends BaseValidate
{
    protected $rule = [
        'terminal' => 'require|in:' . UserTerminalEnum::WECHAT_MMP . ',' . UserTerminalEnum::WECHAT_OA . ','
            . UserTerminalEnum::H5 . ',' . UserTerminalEnum::PC . ',' . UserTerminalEnum::IOS .
            ',' . UserTerminalEnum::ANDROID,
        'scene' => 'require|in:' . LoginEnum::MOBILE_PASSWORD . ',' . LoginEnum::MOBILE_CAPTCHA .','.LoginEnum::EMAIL_CAPTCHA. ',' . LoginEnum::EMAIL_PASSWORD . '|checkConfig',
    ];

    protected $message = [
        'terminal.require' => '终端参数缺失',
        'terminal.in'      => '终端参数状态值不正确',
        'scene.require'    => '场景不能为空',
        'scene.in'         => '场景值错误'
    ];

    /**
     * @notes 登录场景相关校验
     * @param $scene
     * @param $rule
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2022/9/15 14:37
     */
    public function checkConfig($scene, $rule, $data): bool|string
    {
        unset($rule);
        $config = ConfigService::get('login', 'login_way');
        $config[] = LoginEnum::MOBILE_CAPTCHA;//增加一个手机号验证码登录方式
        $config[] = LoginEnum::EMAIL_CAPTCHA;//增加一个手机号验证码登录方式
        if (!in_array($scene, $config)) {
            return '不支持的登录方式';
        }

        // 手机号密码登录
        if (LoginEnum::MOBILE_PASSWORD == $scene) {
            if (!isset($data['mobile']) || $data['mobile'] == '') {
                return '请输入手机号';
            }
            if (!isset($data['password']) || $data['password'] == '') {
                return '请输入密码';
            }
            return $this->checkPassword($data['password'], [], $data);
        }

        // 手机验证码登录
        if (LoginEnum::MOBILE_CAPTCHA == $scene) {
            if (!isset($data['mobile']) || $data['mobile'] == '') {
                return '请输入手机号';
            }
            if (!isset($data['code']) || $data['code'] == '') {
                return '请输入手机验证码';
            }
            $user = (new User())->where(['mobile' => $data['mobile']])->findOrEmpty();
            if ($user->isEmpty()) {
                // 安全优化：使用统一的错误提示，防止账号枚举攻击
                return '验证码错误或账号不存在，请检查后重试';
            }
            return $this->checkCode($data['code'], [], $data);
        }

        // 邮箱密码登录
        if (LoginEnum::EMAIL_PASSWORD == $scene) {
            if (!isset($data['email']) || $data['email'] == '') {
                return '请输入邮箱';
            }
            if (!isset($data['password']) || $data['password'] == '') {
                return '请输入密码';
            }
            return $this->checkPassword($data['password'], [], $data);
        }
        // 邮箱验证码登录
        if (LoginEnum::EMAIL_CAPTCHA == $scene) {
            if (!isset($data['email']) || $data['email'] == '') {
                return '请输入邮箱';
            }
            if (!isset($data['code']) || $data['code'] == '') {
                return '请输入验证码';
            }
            return $this->checkEmailCode($data['code'], [], $data);
        }

        return true;
    }

    /**
     * @notes 登录密码校验
     * @param $password
     * @param $other
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2022/9/15 14:39
     */
    public function checkPassword($password, $other, $data): bool|string
    {
        unset($other);

        // 🔒 安全修复：增强登录安全机制

        // 1. IP锁定检查
        $ipLockResult = $this->checkIpLock();
        if ($ipLockResult !== true) {
            return $ipLockResult;
        }

        // 2. 账号安全机制，连续输错后锁定，防止账号密码暴力破解
        $userAccountSafeCache = new UserAccountSafeCache();
        if (!$userAccountSafeCache->isSafe()) {
            // 记录账号锁定事件
            Log::warning('账号安全锁定触发', [
                'fail_count' => $userAccountSafeCache->count,
                'lock_minutes' => $userAccountSafeCache->minute,
                'ip' => request()->ip(),
                'user_agent' => request()->header('User-Agent'),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            return '密码连续' . $userAccountSafeCache->count . '次输入错误，请' . $userAccountSafeCache->minute . '分钟后重试';
        }

        // 3. 验证码检查（失败次数达到3次后要求验证码）
        $captchaResult = $this->checkCaptchaRequired($userAccountSafeCache, $data);
        if ($captchaResult !== true) {
            return $captchaResult;
        }

        $where = [];
        if ($data['scene'] == LoginEnum::MOBILE_PASSWORD) {
            // 手机号密码登录
            $where = ['mobile' => $data['mobile']];
        }
        if ($data['scene'] == LoginEnum::EMAIL_PASSWORD) {
            // 邮箱密码登录
            $where = ['email' => $data['email']];
        }
        $userInfo = (new User())
            ->where($where)
            ->field(['id,password,is_disable'])
            ->findOrEmpty();
        if ($userInfo->isEmpty()) {
            // 安全优化：使用统一的错误提示，防止账号枚举攻击
            return '账号或密码错误，请检查后重试';
        }

        if ($userInfo['is_disable'] === YesNoEnum::YES) {
            return '用户已禁用';
        }

        if (empty($userInfo['password'])) {
//            $userAccountSafeCache->record();
            return '该账号未设置密码，请使用验证码登录';
        }

        $passwordSalt = Config::get('project.unique_identification');
        
        // 支持新旧密码格式验证
        $isValidPassword = false;
        
        // 首先尝试新的安全哈希验证
        if (strlen($userInfo['password']) > 32) {
            $isValidPassword = verify_password($password, $passwordSalt, $userInfo['password']);
        }
        
        // 如果新验证失败，尝试旧的MD5验证（兼容性）
        if (!$isValidPassword) {
            $isValidPassword = ($userInfo['password'] === create_password_legacy($password, $passwordSalt));
        }
        
        if (!$isValidPassword) {
            $userAccountSafeCache->record();
            // 🔒 安全修复：记录IP级别的登录失败
            $this->recordIpLoginFailure('password_error');
            // 安全优化：使用统一的错误提示，防止账号枚举攻击
            return '账号或密码错误，请检查后重试';
        }

        // 密码验证成功，清除安全记录
        $userAccountSafeCache->relieve();

        // 记录成功登录
        Log::info('用户登录成功', [
            'user_id' => $userInfo['id'],
            'ip' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'timestamp' => date('Y-m-d H:i:s')
        ]);

        return true;
    }

    /**
     * @notes 校验验证码
     * @param $code
     * @param $rule
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2021/8/25 15:43
     */
    public function checkCode($code, $rule, $data): bool|string
    {
        unset($rule);
        $smsDriver = new SmsDriver();
        $result = $smsDriver->verify($data['mobile'], $code, NoticeEnum::LOGIN_CAPTCHA);
        if ($result) {
            return true;
        }
        return '验证码错误';
    }

    /**
     * @notes 校验验证码
     * @param $code
     * @param $rule
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2021/8/25 15:43
     */
    public function checkEmailCode($code, $rule, $data): bool|string
    {
        $result = (new EmailService())
            ->verify($data['email'], $data['code'], NoticeEnum::LOGIN_CAPTCHA);
        if ($result) {
            return true;
        }
        return '验证码错误';
    }

    /**
     * 🔒 安全修复：IP锁定检查
     * @notes 检查IP是否被锁定，防止同一IP大量尝试登录
     * @return bool|string
     * @security_fix 2025/08/02 - IP级别的登录保护
     */
    private function checkIpLock(): bool|string
    {
        $ip = request()->ip();
        $cacheKey = 'ip_login_lock:' . $ip;
        $lockInfo = cache($cacheKey);

        if ($lockInfo) {
            $lockTime = $lockInfo['lock_time'] ?? 0;
            $failCount = $lockInfo['fail_count'] ?? 0;
            $lockDuration = min(30 * pow(2, $failCount - 10), 1440); // 最长锁定24小时

            if (time() - $lockTime < $lockDuration * 60) {
                Log::warning('IP登录锁定触发', [
                    'ip' => $ip,
                    'fail_count' => $failCount,
                    'lock_duration' => $lockDuration,
                    'remaining_time' => $lockDuration * 60 - (time() - $lockTime),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);

                return "IP地址已被锁定{$lockDuration}分钟，请稍后重试";
            }
        }

        return true;
    }

    /**
     * 🔒 安全修复：验证码要求检查
     * @notes 失败次数达到阈值后要求验证码
     * @param object $userAccountSafeCache 用户安全缓存
     * @param array $data 请求数据
     * @return bool|string
     * @security_fix 2025/08/02 - 验证码防护机制
     */
    private function checkCaptchaRequired($userAccountSafeCache, $data): bool|string
    {
        // 获取当前失败次数
        $failCount = $userAccountSafeCache->getFailCount();

        // 失败3次后要求验证码
        if ($failCount >= 3) {
            // 检查是否提供了验证码
            if (empty($data['captcha'])) {
                Log::warning('登录需要验证码但未提供', [
                    'fail_count' => $failCount,
                    'ip' => request()->ip(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return '登录失败次数过多，请输入验证码';
            }

            // 验证验证码
            if (!$this->verifyCaptcha($data['captcha'])) {
                Log::warning('登录验证码验证失败', [
                    'fail_count' => $failCount,
                    'ip' => request()->ip(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return '验证码错误，请重新输入';
            }
        }

        return true;
    }

    /**
     * 🔒 安全修复：验证码验证
     * @notes 验证图形验证码
     * @param string $captcha 验证码
     * @return bool
     * @security_fix 2025/08/02 - 图形验证码验证
     */
    private function verifyCaptcha($captcha): bool
    {
        // 获取session中的验证码
        $sessionCaptcha = session('captcha');

        if (empty($sessionCaptcha)) {
            return false;
        }

        // 验证码不区分大小写
        $result = strtolower($captcha) === strtolower($sessionCaptcha);

        // 验证后清除session中的验证码（一次性使用）
        session('captcha', null);

        if ($result) {
            Log::info('验证码验证成功', [
                'ip' => request()->ip(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }

        return $result;
    }

    /**
     * 🔒 安全修复：记录IP登录失败
     * @notes 记录IP级别的登录失败，用于IP锁定
     * @param string $reason 失败原因
     * @security_fix 2025/08/02 - IP失败记录
     */
    private function recordIpLoginFailure($reason = 'password_error'): void
    {
        $ip = request()->ip();
        $cacheKey = 'ip_login_lock:' . $ip;
        $lockInfo = cache($cacheKey) ?: ['fail_count' => 0, 'first_fail_time' => time()];

        $lockInfo['fail_count']++;
        $lockInfo['last_fail_time'] = time();
        $lockInfo['lock_time'] = time();

        // IP失败记录保存24小时
        cache($cacheKey, $lockInfo, 24 * 3600);

        Log::warning('IP登录失败记录', [
            'ip' => $ip,
            'fail_count' => $lockInfo['fail_count'],
            'reason' => $reason,
            'user_agent' => request()->header('User-Agent'),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}