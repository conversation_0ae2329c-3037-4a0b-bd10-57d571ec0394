<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\api\validate;

use app\common\enum\notice\NoticeEnum;
use app\common\model\user\User;
use app\common\service\ConfigService;
use app\common\service\EmailService;
use app\common\service\sms\SmsDriver;
use app\common\validate\BaseValidate;
use think\facade\Log;

/**
 * 注册验证器
 */
class RegisterValidate extends BaseValidate
{
    /**
     * 🔒 安全修复：增强密码复杂度要求
     * 要求至少8位，包含大小写字母、数字和特殊字符
     */
    protected $regex = [
        'password' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/'
    ];

    protected $rule = [
        'channel'          => 'require',
        'scene'            => 'require|in:1,2|checkCode',
        'mobile'           => 'requireIf:scene,1|mobile|checkMobile',
        'email'            => 'requireIf:scene,2|email|checkEmail',
        'password'         => 'require|length:8,20|regex:password|checkWeakPassword',
        'password_confirm' => 'require|confirm'
    ];

    protected $message = [
        'channel.require'          => '注册来源参数缺失',
        'scene.require'            => '注册场景缺失',
        'scene.in'                 => '注册场景值错误',
        'mobile.requireIf'         => '请输入手机号',
        'mobile.mobile'            => '手机号错误',
        'email.requireIf'          => '请输入邮箱',
        'email.email'              => '邮箱错误',
        'password.require'         => '请输入密码',
        'password.length'          => '密码长度须在8-20位字符',
        'password.regex'           => '密码必须包含大小写字母、数字和特殊字符(@$!%*?&)',
        'password_confirm.require' => '请确认密码',
        'password_confirm.confirm' => '两次输入的密码不一致'
    ];

    /**
     * @notes 校验验证码
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2023/7/19 10:26 上午
     */
    public function checkCode($value, $rule, $data): bool|string
    {
        unset($value);
        unset($rule);
        $register_sms_verify = ConfigService::get('login', 'register_sms_verify', config('project.login.register_sms_verify'));
        if ($register_sms_verify == 1) {
            if (!isset($data['code']) || $data['code'] == '') {
                return '请输入验证码';
            }

            // 短信验证码
            if ($data['scene'] == 1) {
                $result = (new SmsDriver())->verify($data['mobile'], $data['code'], NoticeEnum::REGISTER_CAPTCHA);
                if (!$result) {
                    return '验证码错误';
                }
            }

            // 邮箱验证码
            if ($data['scene'] == 2) {
                $result = (new EmailService())->verify($data['email'], $data['code'], NoticeEnum::REGISTER_CAPTCHA);
                if (!$result) {
                    return '验证码错误';
                }
            }
        }

        return true;
    }

    /**
     * @notes 校验手机号
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2023/7/27 10:23 上午
     */
    public function checkMobile($value, $rule, $data): bool|string
    {
        unset($rule);
        if (isset($data['mobile']) && $data['mobile'] != '') {
            $user = (new User())->where(['mobile'=>$value])->findOrEmpty();
            if (!$user->isEmpty()) {
                return '手机号已被注册，请重新输入';
            }
        }

        return true;
    }

    /**
     * @notes 校验邮箱
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2023/7/27 10:23 上午
     */
    public function checkEmail($value, $rule, $data): bool|string
    {
        unset($rule);
        if (isset($data['email']) && $data['email'] != '') {
            $user = (new User())->where(['email'=>$value])->findOrEmpty();
            if (!$user->isEmpty()) {
                return '邮箱已被注册，请重新输入';
            }
        }

        return true;
    }

    /**
     * 🔒 安全修复：弱密码检查
     * @notes 检查密码是否为常见弱密码
     * @param string $password 密码
     * @param mixed $other 其他参数
     * @param array $data 数据
     * @return bool|string
     * @security_fix 2025/08/02 - 防止使用常见弱密码
     */
    public function checkWeakPassword($password, $other, $data): bool|string
    {
        unset($other, $data);

        // 常见弱密码列表
        $weakPasswords = [
            '12345678', '123456789', '1234567890',
            'password', 'Password', 'PASSWORD',
            'password123', 'Password123', 'PASSWORD123',
            'qwerty123', 'Qwerty123', 'QWERTY123',
            'abc12345', 'Abc12345', 'ABC12345',
            'admin123', 'Admin123', 'ADMIN123',
            '88888888', '66666666', '11111111',
            'a1234567', 'A1234567', 'abcd1234',
            'test1234', 'Test1234', 'TEST1234',
            'user1234', 'User1234', 'USER1234',
            'welcome123', 'Welcome123', 'WELCOME123',
            'iloveyou', 'Iloveyou', 'ILOVEYOU',
            'sunshine', 'Sunshine', 'SUNSHINE',
            'princess', 'Princess', 'PRINCESS',
            'football', 'Football', 'FOOTBALL',
            'baseball', 'Baseball', 'BASEBALL',
            'superman', 'Superman', 'SUPERMAN',
            'trustno1', 'Trustno1', 'TRUSTNO1'
        ];

        // 检查是否为弱密码
        if (in_array($password, $weakPasswords)) {
            // 记录弱密码尝试
            Log::warning('用户尝试使用弱密码注册', [
                'password_pattern' => substr($password, 0, 3) . '***',
                'ip' => request()->ip(),
                'user_agent' => request()->header('User-Agent'),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            return '密码过于简单，请使用更复杂的密码组合';
        }

        // 检查密码是否包含连续字符
        if (preg_match('/(.)\1{2,}/', $password)) {
            Log::warning('用户尝试使用连续字符密码', [
                'ip' => request()->ip(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return '密码不能包含3个或以上连续相同字符';
        }

        // 检查密码是否为纯数字序列
        if (preg_match('/^(012|123|234|345|456|567|678|789|890|987|876|765|654|543|432|321|210)/', $password)) {
            Log::warning('用户尝试使用数字序列密码', [
                'ip' => request()->ip(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return '密码不能使用连续的数字序列';
        }

        // 检查密码是否为键盘序列
        $keyboardPatterns = ['qwerty', 'asdfgh', 'zxcvbn', 'qwertyuiop', 'asdfghjkl', 'zxcvbnm'];
        foreach ($keyboardPatterns as $pattern) {
            if (stripos($password, $pattern) !== false) {
                Log::warning('用户尝试使用键盘序列密码', [
                    'pattern' => $pattern,
                    'ip' => request()->ip(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                return '密码不能包含键盘序列字符';
            }
        }

        // 记录密码强度验证通过
        Log::info('密码强度验证通过', [
            'password_length' => strlen($password),
            'ip' => request()->ip(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);

        return true;
    }
}