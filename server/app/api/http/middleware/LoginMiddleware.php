<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\http\middleware;

use app\common\cache\UserTokenCache;
use app\common\service\JsonService;
use app\api\service\UserTokenService;
use Closure;
use think\facade\Config;
use think\facade\Log;

class LoginMiddleware
{
    // 进程级缓存，避免同一进程内重复验证相同token
    private static $processCache = [];
    private static $processCacheExpire = [];
    
    // 缓存配置
    private const PROCESS_CACHE_TTL = 60; // 进程缓存1分钟
    private const MAX_PROCESS_CACHE = 100; // 最大进程缓存数量

    /**
     * @notes 登录验证（优化版）
     * @param $request
     * @param Closure $next
     * @return mixed
     * @throws @\think\db\exception\DataNotFoundException
     * @throws @\think\db\exception\DbException
     * @throws @\think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/1 17:33
     */
    public function handle($request, Closure $next): mixed
    {
        $startTime = microtime(true);
        
        try {
            $token = $request->header('token');
            
            // 判断接口是否免登录
            $isNotNeedLogin = $request->controllerObject->isNotNeedLogin();
            
            // 不直接判断$isNotNeedLogin结果,使不需要登录的接口通过,为了兼容某些接口可以登录或不登录访问
            if (empty($token) && !$isNotNeedLogin) {
                //没有token并且该地址需要登录才能访问, 指定show为0，前端不弹出此报错
                return JsonService::fail('请求参数缺token', [], 0, 0);
            }

            // 安装检测（优化：只检查一次）
            static $installChecked = null;
            if ($installChecked === null) {
                $installChecked = file_exists(root_path() . '/' . 'config/install.lock');
            }
            
            if (!$installChecked) {
                return $next($request);
            }

            $userInfo = null;
            
            // 如果有token，进行用户信息获取
            if (!empty($token)) {
                // 1. 先从进程缓存获取（最快）
                $userInfo = $this->getFromProcessCache($token);
                
                // 2. 如果进程缓存没有，从UserTokenCache获取
                if ($userInfo === null) {
                    $userInfo = (new UserTokenCache())->getUserInfo($token);
                    
                    // 写入进程缓存
                    if ($userInfo) {
                        $this->setToProcessCache($token, $userInfo);
                    }
                }
            }

            // 验证用户信息
            if (empty($userInfo) && !$isNotNeedLogin) {
                // token过期无效并且该地址需要登录才能访问
                return JsonService::fail('登录超时，请重新登录', [], -1, 0);
            }

            // token临近过期,自动续期（优化：减少续期检查频率）
            if ($userInfo && $this->shouldRenewToken($userInfo)) {
                $this->handleTokenRenewal($token, $userInfo);
            }

            // 给request赋值,用于控制器
            $request->userInfo = $userInfo;
            $request->userId = $userInfo['user_id'] ?? 0;

            return $next($request);
            
        } catch (\Throwable $e) {
            Log::error('登录中间件异常', [
                'error' => $e->getMessage(),
                'token' => !empty($token) ? substr($token, 0, 10) . '...' : 'empty',
                'trace' => $e->getTraceAsString()
            ]);
            
            // 异常时根据是否需要登录返回不同响应
            if (!$isNotNeedLogin) {
                return JsonService::fail('登录验证失败，请重新登录', [], -1, 0);
            }
            
            // 不需要登录的接口，允许继续访问
            $request->userInfo = null;
            $request->userId = 0;
            return $next($request);
            
        } finally {
            // 记录性能统计（仅在开发环境）
            if (config('app.app_debug')) {
                $duration = (microtime(true) - $startTime) * 1000;
                if ($duration > 50) { // 超过50ms记录日志
                    Log::info('登录中间件性能', [
                        'duration' => round($duration, 2) . 'ms',
                        'has_token' => !empty($token),
                        'need_login' => !$isNotNeedLogin
                    ]);
                }
            }
        }
    }
    
    /**
     * 从进程缓存获取用户信息
     */
    private function getFromProcessCache(string $token): ?array
    {
        // 清理过期缓存
        $this->cleanupProcessCache();
        
        $cacheKey = 'token_' . md5($token);
        
        // 检查是否过期
        if (isset(self::$processCacheExpire[$cacheKey]) && 
            self::$processCacheExpire[$cacheKey] < time()) {
            unset(self::$processCache[$cacheKey], self::$processCacheExpire[$cacheKey]);
            return null;
        }
        
        return self::$processCache[$cacheKey] ?? null;
    }
    
    /**
     * 写入进程缓存
     */
    private function setToProcessCache(string $token, array $userInfo): void
    {
        $cacheKey = 'token_' . md5($token);
        
        // 防止缓存过大
        if (count(self::$processCache) >= self::MAX_PROCESS_CACHE) {
            $this->cleanupProcessCache(true);
        }
        
        self::$processCache[$cacheKey] = $userInfo;
        self::$processCacheExpire[$cacheKey] = time() + self::PROCESS_CACHE_TTL;
    }
    
    /**
     * 清理进程缓存
     */
    private function cleanupProcessCache(bool $force = false): void
    {
        $now = time();
        $cleaned = 0;
        
        foreach (self::$processCacheExpire as $key => $expireTime) {
            if ($expireTime < $now || $force) {
                unset(self::$processCache[$key], self::$processCacheExpire[$key]);
                $cleaned++;
                
                // 强制清理时只清理一半
                if ($force && $cleaned >= self::MAX_PROCESS_CACHE / 2) {
                    break;
                }
            }
        }
    }
    
    /**
     * 判断是否需要续期token
     */
    private function shouldRenewToken(array $userInfo): bool
    {
        if (!isset($userInfo['expire_time'])) {
            return false;
        }
        
        // 获取临近过期自动续期时长
        $beExpireDuration = Config::get('project.user_token.be_expire_duration', 3600);
        
        // 检查是否需要续期
        return time() > ($userInfo['expire_time'] - $beExpireDuration);
    }
    
    /**
     * 处理token续期
     */
    private function handleTokenRenewal(string $token, array $userInfo): void
    {
        try {
            // 使用续期锁防止重复续期
            $renewKey = 'renewing_' . md5($token);
            
            // 简单的续期锁（避免同时多个请求续期）
            if (isset(self::$processCache[$renewKey])) {
                return; // 已经在续期中
            }
            
            // 设置续期锁
            self::$processCache[$renewKey] = time();
            self::$processCacheExpire[$renewKey] = time() + 300; // 5分钟锁
            
            $result = UserTokenService::overtimeToken($token);
            
            // 续期失败(数据表被删除导致)
            if (empty($result)) {
                Log::warning('Token续期失败', [
                    'user_id' => $userInfo['user_id'] ?? 'unknown',
                    'token' => substr($token, 0, 10) . '...'
                ]);
            } else {
                // 清理进程缓存中的旧数据，强制重新获取
                $cacheKey = 'token_' . md5($token);
                unset(self::$processCache[$cacheKey], self::$processCacheExpire[$cacheKey]);
                
                Log::info('Token续期成功', [
                    'user_id' => $userInfo['user_id'] ?? 'unknown'
                ]);
            }
            
            // 清理续期锁
            unset(self::$processCache[$renewKey], self::$processCacheExpire[$renewKey]);
            
        } catch (\Throwable $e) {
            Log::error('Token续期异常', [
                'error' => $e->getMessage(),
                'user_id' => $userInfo['user_id'] ?? 'unknown'
            ]);
            
            // 清理续期锁
            $renewKey = 'renewing_' . md5($token);
            unset(self::$processCache[$renewKey], self::$processCacheExpire[$renewKey]);
        }
    }
    
    /**
     * 获取中间件性能统计
     */
    public static function getStats(): array
    {
        return [
            'process_cache_count' => count(self::$processCache),
            'process_cache_size' => strlen(serialize(self::$processCache))
        ];
    }
}