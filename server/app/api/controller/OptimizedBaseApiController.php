<?php
/**
 * 优化版API基础控制器
 * 实现中期优化: 响应缓存、速率限制、批量数据处理、性能监控
 * 
 * 优化特性:
 * 1. 响应缓存机制: 智能缓存API响应，减少重复计算
 * 2. 速率限制控制: 防止接口被恶意调用，保护系统稳定性
 * 3. 批量数据处理: 优化大数据量请求的处理效率
 * 4. 并发安全更新: 防止并发请求导致的数据不一致
 * 5. 数据预加载: 预加载常用数据，提升响应速度
 * 6. 性能监控: 详细记录接口性能指标
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */

namespace app\api\controller;

use app\common\controller\BaseLikeAdminController;
use think\facade\Cache;
use think\facade\Log;
use think\Response;

class OptimizedBaseApiController extends BaseLikeAdminController
{
    protected int $userId = 0;
    protected int $terminal = 0;
    protected array $userInfo = [];
    
    // 缓存配置
    protected bool $enableResponseCache = true;
    protected int $defaultCacheTime = 300; // 5分钟
    protected array $cacheableActions = ['index', 'list', 'detail'];
    
    // 速率限制配置
    protected bool $enableRateLimit = true;
    protected int $defaultRateLimit = 100; // 每分钟100次
    protected array $rateLimitRules = [];
    
    // 性能监控
    private static array $performanceStats = [
        'total_requests' => 0,
        'cache_hits' => 0,
        'rate_limited' => 0,
        'total_time' => 0,
        'average_time' => 0
    ];
    
    public function initialize()
    {
        $startTime = microtime(true);
        
        // 基础初始化
        parent::initialize();
        $this->terminal = intval($this->request->header('terminal', 4));
        if (isset($this->request->userInfo) && $this->request->userInfo) {
            $this->userInfo = $this->request->userInfo;
            $this->userId = $this->request->userInfo['user_id'];
        }
        
        // 性能统计
        self::$performanceStats['total_requests']++;
        
        // 速率限制检查
        if ($this->enableRateLimit && !$this->checkRateLimit()) {
            self::$performanceStats['rate_limited']++;
            throw new \Exception('请求频率过高，请稍后再试', 429);
        }
        
        // 记录初始化时间
        $endTime = microtime(true);
        $initTime = ($endTime - $startTime) * 1000;
        self::$performanceStats['total_time'] += $initTime;
        self::$performanceStats['average_time'] = self::$performanceStats['total_time'] / self::$performanceStats['total_requests'];
    }
    
    /**
     * 优化版响应方法
     * 支持缓存、压缩、性能监控
     * 
     * @param mixed $data
     * @param string $message
     * @param int $code
     * @param bool $useCache
     * @param int $cacheTime
     * @return Response
     */
    protected function success($data = [], string $message = '操作成功', int $code = 200, bool $useCache = null, int $cacheTime = null): Response
    {
        $startTime = microtime(true);
        
        try {
            // 判断是否使用缓存
            $useCache = $useCache ?? $this->shouldUseCache();
            $cacheTime = $cacheTime ?? $this->defaultCacheTime;
            
            // 尝试从缓存获取响应
            if ($useCache) {
                $cacheKey = $this->generateResponseCacheKey();
                $cachedResponse = $this->getFromCache($cacheKey);
                
                if ($cachedResponse !== null) {
                    self::$performanceStats['cache_hits']++;
                    return json($cachedResponse);
                }
            }
            
            // 处理数据
            $processedData = $this->processResponseData($data);
            
            // 构建响应数据
            $responseData = [
                'code' => $code,
                'message' => $message,
                'data' => $processedData,
                'timestamp' => time(),
                'request_id' => $this->generateRequestId()
            ];
            
            // 缓存响应
            if ($useCache) {
                $this->setToCache($cacheKey, $responseData, $cacheTime);
            }
            
            return json($responseData);
            
        } finally {
            // 记录响应时间
            $endTime = microtime(true);
            $responseTime = ($endTime - $startTime) * 1000;
            $this->recordPerformance($responseTime);
        }
    }
    
    /**
     * 批量数据获取方法
     * 优化大数据量查询性能
     * 
     * @param array $ids
     * @param string $model
     * @param array $fields
     * @param bool $useCache
     * @return array
     */
    protected function getBatchData(array $ids, string $model, array $fields = ['*'], bool $useCache = true): array
    {
        if (empty($ids)) {
            return [];
        }
        
        $cacheKey = "batch_data:" . md5($model . implode(',', $ids) . implode(',', $fields));
        
        // 尝试从缓存获取
        if ($useCache) {
            $cached = $this->getFromCache($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }
        
        // 分批查询，避免SQL语句过长
        $batchSize = 100;
        $results = [];
        
        for ($i = 0; $i < count($ids); $i += $batchSize) {
            $batchIds = array_slice($ids, $i, $batchSize);
            
            $batchResults = $model::whereIn('id', $batchIds)
                ->field($fields)
                ->select()
                ->toArray();
                
            $results = array_merge($results, $batchResults);
        }
        
        // 缓存结果
        if ($useCache) {
            $this->setToCache($cacheKey, $results, 600); // 10分钟
        }
        
        return $results;
    }
    
    /**
     * 并发安全的数据更新
     * 使用分布式锁防止并发冲突
     * 
     * @param string $lockKey
     * @param callable $updateCallback
     * @param int $lockTime
     * @return mixed
     */
    protected function safeUpdate(string $lockKey, callable $updateCallback, int $lockTime = 10)
    {
        $redisKey = "lock:" . $lockKey;
        $redis = Cache::store('redis')->handler();
        
        // 获取分布式锁
        $acquired = $redis->set($redisKey, time(), ['nx', 'ex' => $lockTime]);
        
        if (!$acquired) {
            throw new \Exception('操作正在进行中，请稍后重试');
        }
        
        try {
            return $updateCallback();
        } finally {
            // 释放锁
            $redis->del($redisKey);
        }
    }
    
    /**
     * 数据预加载
     * 预加载用户常用数据
     * 
     * @param int $userId
     * @return array
     */
    protected function preloadUserData(int $userId): array
    {
        $cacheKey = "user_preload_data:{$userId}";
        
        // 尝试从缓存获取
        $cached = $this->getFromCache($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        // 预加载用户基础信息、权限、配置等
        $preloadData = [
            'user_info' => $this->userInfo,
            'user_permissions' => $this->getUserPermissions($userId),
            'user_config' => $this->getUserConfig($userId),
            'common_data' => $this->getCommonData()
        ];
        
        // 缓存预加载数据
        $this->setToCache($cacheKey, $preloadData, 900); // 15分钟
        
        return $preloadData;
    }
    
    /**
     * 检查速率限制
     * 
     * @return bool
     */
    private function checkRateLimit(): bool
    {
        $clientId = $this->getClientId();
        $action = $this->request->action();
        
        // 获取限制规则
        $limit = $this->rateLimitRules[$action] ?? $this->defaultRateLimit;
        
        $key = "rate_limit:{$clientId}:{$action}";
        $current = Cache::get($key, 0);
        
        if ($current >= $limit) {
            return false;
        }
        
        // 增加计数
        Cache::set($key, $current + 1, 60); // 1分钟窗口
        
        return true;
    }
    
    /**
     * 判断是否应该使用缓存
     * 
     * @return bool
     */
    private function shouldUseCache(): bool
    {
        if (!$this->enableResponseCache) {
            return false;
        }
        
        $action = $this->request->action();
        return in_array($action, $this->cacheableActions);
    }
    
    /**
     * 生成响应缓存键
     * 
     * @return string
     */
    private function generateResponseCacheKey(): string
    {
        $controller = $this->request->controller();
        $action = $this->request->action();
        $params = $this->request->param();
        $userId = $this->userId;
        
        // 排序参数确保一致性
        ksort($params);
        
        return "response_cache:{$controller}:{$action}:{$userId}:" . md5(serialize($params));
    }
    
    /**
     * 处理响应数据
     * 可以在子类中重写以实现特殊处理
     * 
     * @param mixed $data
     * @return mixed
     */
    protected function processResponseData($data)
    {
        // 如果是大数组，可以进行分页处理
        if (is_array($data) && count($data) > 1000) {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $offset = ($page - 1) * $limit;
            
            return [
                'list' => array_slice($data, $offset, $limit),
                'total' => count($data),
                'page' => $page,
                'limit' => $limit
            ];
        }
        
        return $data;
    }
    
    /**
     * 从缓存获取数据
     * 
     * @param string $key
     * @return mixed|null
     */
    private function getFromCache(string $key)
    {
        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::warning('缓存读取失败', ['key' => $key, 'error' => $e->getMessage()]);
            return null;
        }
    }
    
    /**
     * 设置缓存数据
     * 
     * @param string $key
     * @param mixed $value
     * @param int $ttl
     */
    private function setToCache(string $key, $value, int $ttl): void
    {
        try {
            Cache::set($key, $value, $ttl);
        } catch (\Exception $e) {
            Log::warning('缓存设置失败', ['key' => $key, 'ttl' => $ttl, 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * 获取客户端ID
     * 
     * @return string
     */
    private function getClientId(): string
    {
        // 优先使用用户ID，其次使用IP地址
        if ($this->userId > 0) {
            return "user_{$this->userId}";
        }
        
        return "ip_" . $this->request->ip();
    }
    
    /**
     * 生成请求ID
     * 
     * @return string
     */
    private function generateRequestId(): string
    {
        return md5(uniqid('req_', true) . microtime(true));
    }
    
    /**
     * 获取用户权限
     * 
     * @param int $userId
     * @return array
     */
    private function getUserPermissions(int $userId): array
    {
        // 这里应该调用具体的权限服务
        return [];
    }
    
    /**
     * 获取用户配置
     * 
     * @param int $userId
     * @return array
     */
    private function getUserConfig(int $userId): array
    {
        // 这里应该调用具体的配置服务
        return [];
    }
    
    /**
     * 获取公共数据
     * 
     * @return array
     */
    private function getCommonData(): array
    {
        // 这里应该返回系统公共数据
        return [];
    }
    
    /**
     * 记录性能数据
     * 
     * @param float $responseTime
     */
    private function recordPerformance(float $responseTime): void
    {
        // 定期记录性能日志
        if (self::$performanceStats['total_requests'] % 100 === 0) {
            $this->logPerformanceStats();
        }
    }
    
    /**
     * 记录性能统计日志
     */
    private function logPerformanceStats(): void
    {
        $stats = self::$performanceStats;
        $cacheHitRate = $stats['total_requests'] > 0 ? 
            round(($stats['cache_hits'] / $stats['total_requests']) * 100, 2) : 0;
        
        Log::info('API接口性能统计', [
            'total_requests' => $stats['total_requests'],
            'cache_hits' => $stats['cache_hits'],
            'cache_hit_rate' => $cacheHitRate . '%',
            'rate_limited' => $stats['rate_limited'],
            'average_time_ms' => round($stats['average_time'], 2)
        ]);
    }
    
    /**
     * 获取性能统计数据
     * 
     * @return array
     */
    public static function getPerformanceStats(): array
    {
        $stats = self::$performanceStats;
        $cacheHitRate = $stats['total_requests'] > 0 ? 
            round(($stats['cache_hits'] / $stats['total_requests']) * 100, 2) : 0;
        
        return [
            'total_requests' => $stats['total_requests'],
            'cache_hits' => $stats['cache_hits'],
            'cache_hit_rate' => $cacheHitRate,
            'rate_limited' => $stats['rate_limited'],
            'average_time_ms' => round($stats['average_time'], 2)
        ];
    }
} 