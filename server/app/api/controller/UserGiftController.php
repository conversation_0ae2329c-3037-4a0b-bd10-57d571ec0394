<?php
declare(strict_types=1);

namespace app\api\controller;

use app\api\logic\UserGiftLogic;
use think\response\Json;

/**
 * 用户赠送控制器
 */
class UserGiftController extends BaseApiController
{
    /**
     * 执行赠送
     */
    public function gift(): Json
    {
        // 获取参数
        $toUserId = intval($this->request->param('to_user_id', 0)); // 前端传的是数据库ID
        $giftAmount = intval($this->request->param('gift_amount', 0)); // 限制为整数
        $giftMessage = $this->request->param('gift_message', '');
        
        // 验证参数
        if ($toUserId <= 0) {
            return $this->fail('请选择接收用户');
        }
        
        if ($giftAmount <= 0) {
            return $this->fail('请输入正确的赠送金额');
        }
        
        // 不能给自己赠送
        if ($toUserId == $this->userId) {
            return $this->fail('不能给自己赠送');
        }
        
        $params = [
            'from_user_id' => $this->userId,
            'to_user_id' => $toUserId,
            'gift_amount' => $giftAmount,
            'gift_message' => $giftMessage
        ];
        
        $result = UserGiftLogic::executeGift($params);
        
        if ($result['success']) {
            return $this->success($result['message'], $result['data'] ?? []);
        } else {
            return $this->fail($result['message']);
        }
    }
    
    /**
     * 获取赠送记录
     */
    public function records(): Json
    {
        try {
            $userId = $this->userId;
            
            // 严格的参数验证，防止SQL注入
            $page = max(1, min(1000, intval($this->request->param('page_no', 1))));
            $limit = max(1, min(100, intval($this->request->param('page_size', 20))));
            $type = $this->request->param('type', 'all');
            
            // 验证type参数，只允许特定值
            $allowedTypes = ['all', 'send', 'receive'];
            if (!in_array($type, $allowedTypes)) {
                return $this->fail('参数错误');
            }
            
            // 直接在控制器中使用原生SQL
            $where = '';
            $whereParams = [];
            
            if ($type == 'send') {
                $where = 'WHERE gl.from_user_id = ?';
                $whereParams = [$userId];
            } elseif ($type == 'receive') {
                $where = 'WHERE gl.to_user_id = ?';
                $whereParams = [$userId];
            } else {
                $where = 'WHERE (gl.from_user_id = ? OR gl.to_user_id = ?)';
                $whereParams = [$userId, $userId];
            }
            
            $where .= ' AND gl.delete_time IS NULL';
            
            // 获取总数
            $countSql = "SELECT COUNT(*) as count FROM cm_user_gift_log gl {$where}";
            $countResult = \think\facade\Db::query($countSql, $whereParams);
            $count = $countResult[0]['count'] ?? 0;
            
            // 安全地计算offset，使用参数化查询
            $offset = ($page - 1) * $limit;
            
            // 获取列表数据，联表查询用户信息 - 使用完全参数化查询
            $listSql = "SELECT 
                gl.*,
                fu.nickname as from_user_nickname,
                fu.avatar as from_user_avatar,
                fu.sn as from_user_sn,
                tu.nickname as to_user_nickname,
                tu.avatar as to_user_avatar,
                tu.sn as to_user_sn
                FROM cm_user_gift_log gl 
                LEFT JOIN cm_user fu ON gl.from_user_id = fu.id 
                LEFT JOIN cm_user tu ON gl.to_user_id = tu.id 
                {$where} ORDER BY gl.create_time DESC LIMIT ?, ?";
            
            // 合并所有参数，包括LIMIT参数
            $allParams = array_merge($whereParams, [$offset, $limit]);
            $lists = \think\facade\Db::query($listSql, $allParams);
            
            // 处理数据
            foreach ($lists as &$item) {
                $item['type'] = ($item['from_user_id'] == $userId) ? 'send' : 'receive';
                // 确保用户昵称有默认值
                $item['from_user_nickname'] = $item['from_user_nickname'] ?: ('用户' . $item['from_user_id']);
                $item['to_user_nickname'] = $item['to_user_nickname'] ?: ('用户' . $item['to_user_id']);
                // 确保头像有默认值
                $item['from_user_avatar'] = $item['from_user_avatar'] ?: '';
                $item['to_user_avatar'] = $item['to_user_avatar'] ?: '';
                // 格式化时间
                $item['create_time'] = is_numeric($item['create_time']) ? 
                    date('Y-m-d H:i:s', $item['create_time']) : $item['create_time'];
                    
                // 过滤敏感信息
                $item['gift_message'] = htmlspecialchars($item['gift_message'] ?? '', ENT_QUOTES, 'UTF-8');
            }
            
            $result = [
                'lists' => $lists,
                'count' => intval($count),
                'page' => $page,
                'limit' => $limit
            ];
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            \think\facade\Log::error('获取赠送记录失败：' . $e->getMessage());
            return $this->fail('获取记录失败，请稍后重试');
        }
    }
    
    /**
     * 获取赠送配置
     */
    public function config(): Json
    {
        $config = UserGiftLogic::getGiftConfig();
        return $this->success('获取成功', $config);
    }
    
    /**
     * 根据用户ID获取用户信息（只支持通过用户编号sn字段查询）
     */
    public function getUserById(): Json
    {
        // 只支持user_sn参数，确保只能通过用户编号查询
        $userSn = $this->request->param('user_sn', '');
        $user = UserGiftLogic::getUserById($userSn, $this->userId);
        return $this->success('获取成功', $user);
    }
    
    /**
     * 获取最近赠送用户
     */
    public function recentUsers(): Json
    {
        $users = UserGiftLogic::getRecentGiftUsers($this->userId);
        return $this->success('获取成功', $users);
    }
    
    /**
     * 获取用户赠送统计
     */
    public function statistics(): Json
    {
        $stats = UserGiftLogic::getUserStatistics($this->userId);
        return $this->success('获取成功', $stats);
    }
    
    /**
     * 测试方法 - 验证代码是否被执行（安全版本）
     */
    public function test(): Json
    {
        // 安全的日志记录方式
        $logDir = runtime_path() . 'log/test/';
        if (!is_dir($logDir)) {
            @mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . 'controller_' . date('Ymd') . '.log';
        $logContent = date('Y-m-d H:i:s') . " 测试方法被调用\n";
        
        // 使用安全的文件写入
        if (is_writable(dirname($logFile))) {
            @file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
        }
        
        return $this->success('测试成功', ['timestamp' => time()]);
    }
} 