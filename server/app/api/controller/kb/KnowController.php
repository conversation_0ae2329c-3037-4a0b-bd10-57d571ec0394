<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\api\controller\kb;

use app\api\controller\BaseApiController;
use app\api\lists\kb\KbFilesLists;
use app\api\lists\kb\KbKnowLists;
use app\api\logic\kb\KbKnowLogic;
use app\api\validate\kb\KbKnowValidate;
use Exception;
use think\db\exception\DbException;
use think\response\Json;

/**
 * 知识库管理
 */
class KnowController extends BaseApiController
{
    public array $notNeedLogin = ['lists'];

    /**
     * @notes 所有知识库
     * @return Json
     * <AUTHOR>
     */
    public function all(): Json
    {
        $lists = KbKnowLogic::all($this->userId);
        return $this->data($lists);
    }

    /**
     * @notes 知识库列表
     * @return Json
     * <AUTHOR>
     */
    public function lists(): Json
    {
        return $this->dataLists((new KbKnowLists()));
    }

    /**
     * @notes 知识库详情
     * @return Json
     * @throws Exception
     * <AUTHOR>
     */
    public function detail(): Json
    {
        $params = (new KbKnowValidate())->get()->goCheck('id');
        $results = KbKnowLogic::detail(intval($params['id']), $this->userId);
        if (!$results) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->data($results);
    }

    /**
     * @notes 知识库创建
     * @return Json
     */
    public function add(): Json
    {
        $params = (new KbKnowValidate())->post()->goCheck('add');
        $results = KbKnowLogic::add($params, $this->userId);
        if ($results === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('创建成功', $results, 1, 1);
    }

    /**
     * @notes 知识库编辑
     * @return Json
     * <AUTHOR>
     */
    public function edit(): Json
    {
        $params = (new KbKnowValidate())->post()->goCheck('edit');
        $results = KbKnowLogic::edit($params, $this->userId);
        if ($results === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('编辑成功', [], 1, 1);
    }

    /**
     * @notes 知识库删除
     * @return Json
     * <AUTHOR>
     */
    public function del(): Json
    {
        $params = (new KbKnowValidate())->post()->goCheck('id');
        $results = KbKnowLogic::del(intval($params['id']), $this->userId);
        if ($results === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('删除成功', [], 1, 1);
    }

    /**
     * @notes 知识库转移
     * @return Json
     * <AUTHOR>
     */
    public function transfer(): Json
    {
        $params = (new KbKnowValidate())->post()->goCheck('kid');
        $type     = trim($this->request->post('type', ''));
        $toUserSn = trim($this->request->post('sn', 0));

        $results = KbKnowLogic::transfer(intval($params['kb_id']), $type, $this->userId, $toUserSn);
        if ($results === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('转移成功', [], 1, 1);
    }

    /**
     * @notes 文件列表
     * @return Json
     * <AUTHOR>
     */
    public function files(): Json
    {
        (new KbKnowValidate())->get()->goCheck('kid');
        return $this->dataLists((new KbFilesLists()));
    }

    /**
     * @notes 文件命名
     * @return Json
     * <AUTHOR>
     */
    public function fileRename(): Json
    {
        $params = (new KbKnowValidate())->post()->goCheck('rename');
        $results = KbKnowLogic::fileRename(intval($params['fd_id']), $params['name'], $this->userId);
        if ($results === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('命名成功', [], 1, 1);
    }

    /**
     * @notes 文件移除
     * @return Json
     * <AUTHOR>
     */
    public function fileRemove(): Json
    {
        $params = (new KbKnowValidate())->post()->goCheck('fid');
        $results = KbKnowLogic::fileRemove(intval($params['fd_id']), $this->userId, $params);
        if ($results === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('移除成功', [], 1, 1);
    }

    /**
     * @notes 共享用户筛选
     * @return Json
     * @throws DbException
     * <AUTHOR>
     */
    public function teamUsers(): Json
    {
        $lists = KbKnowLogic::teamUsers($this->request->get(), $this->userId);
        return $this->success('OK', $lists);
    }

    /**
     * @notes 团队成员列表
     * @return Json
     * @throws @\think\db\exception\DataNotFoundException
     * @throws @\think\db\exception\DbException
     * @throws @\think\db\exception\ModelNotFoundException
     * <AUTHOR>
     */
    public function teamLists(): Json
    {
        $kbId = intval($this->request->get('kb_id', 0));
        $lists = KbKnowLogic::teamLists($kbId);
        if ($lists === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('OK', $lists);
    }

    /**
     * @notes 团队成员添加
     * @return Json
     * <AUTHOR>
     */
    public function teamAdd(): Json
    {
        $result = KbKnowLogic::teamAdd($this->request->post(), $this->userId);
        if ($result === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('添加成功', [], 1, 1);
    }

    /**
     * notes 团队成员编辑
     * @return Json
     * <AUTHOR>
     */
    public function teamEdit(): Json
    {
        $id = intval($this->request->post('id'));
        $power = intval($this->request->post('power'));

        $result = KbKnowLogic::teamEdit($id, $power, $this->userId);
        if ($result === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('更新成功', [], 1, 1);
    }

    /**
     * @notes 团队成员删除
     * @return Json
     * <AUTHOR>
     */
    public function teamDel(): Json
    {
        $id = intval($this->request->post('id'));
        $result = KbKnowLogic::teamDel($id, $this->userId);
        if ($result === false) {
            return $this->fail(KbKnowLogic::getError());
        }
        return $this->success('删除成功', [], 1, 1);
    }

    /**
     * @notes 获取所有示例库类别及内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getAllExamples()
    {
        try {
            // 先获取所有开启状态的类别
            $categories = \think\facade\Db::table('cm_example_category')
                ->where('status', 1)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->field('id, name')
                ->order('sort', 'desc')
                ->select()
                ->toArray();
            
            if (empty($categories)) {
                return $this->success('获取成功', []);
            }
            
            // 获取所有开启状态的示例内容
            $examples = \think\facade\Db::table('cm_example_content')
                ->where('status', 1)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->field('id,category_id,title,question,answer')
                ->select()
                ->toArray();
            
            // 将示例内容按类别分组
            $examplesByCategory = [];
            foreach ($examples as $example) {
                $categoryId = $example['category_id'];
                if (!isset($examplesByCategory[$categoryId])) {
                    $examplesByCategory[$categoryId] = [];
                }
                $examplesByCategory[$categoryId][] = [
                    'id' => $example['id'],
                    'title' => $example['title'],
                    'question' => $example['question'],
                    'answer' => $example['answer']
                ];
            }
            
            // 构建结果数组
            $result = [];
            foreach ($categories as $category) {
                $categoryId = $category['id'];
                $result[] = [
                    'id' => $categoryId,
                    'name' => $category['name'],
                    'examples' => $examplesByCategory[$categoryId] ?? []
                ];
            }
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->fail('获取示例库数据失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 检查知识库关联智能体状态
     * @return Json
     * <AUTHOR>
     */
    public function checkRelatedRobots(): Json
    {
        $params = (new KbKnowValidate())->post()->goCheck('id');
        $result = \app\api\logic\kb\KbTeachLogic::checkRelatedRobots(intval($params['know_id']));
        return $this->success('查询成功', $result);
    }
}