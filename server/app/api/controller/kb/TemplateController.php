<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\api\controller\kb;
use app\api\controller\BaseApiController;

/**
 * 模板库控制器（用户端）
 * Class TemplateController
 * @package app\api\controller\kb
 */
class TemplateController extends BaseApiController
{
    /**
     * @notes 获取所有模板（按类别分组，用于PC/H5端）
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getAllTemplates()
    {
        try {
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            $templates = \think\facade\Db::table($prefix.'template')
                ->alias('t')
                ->leftJoin($prefix.'example_category c', 'c.id = t.category_id')
                ->where('t.status', 1)
                ->where(function($query) {
                    $query->where('t.delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('t.delete_time');
                          });
                })
                ->field('t.id,t.category_id,t.name,t.description,t.download_url,t.file_size,t.file_type,t.download_count,c.name as category_name')
                ->order('t.sort', 'desc')
                ->select()
                ->toArray();
                
            $result = [];
            foreach ($templates as $template) {
                $categoryId = $template['category_id'];
                $categoryName = $template['category_name'] ?? '';
                
                if (!isset($result[$categoryId])) {
                    $result[$categoryId] = [
                        'id' => $categoryId,
                        'name' => $categoryName,
                        'templates' => []
                    ];
                }
                
                $result[$categoryId]['templates'][] = [
                    'id' => $template['id'],
                    'name' => $template['name'],
                    'description' => $template['description'],
                    'download_url' => $template['download_url'],
                    'file_size' => $template['file_size'],
                    'file_type' => $template['file_type'],
                    'download_count' => $template['download_count']
                ];
            }
            
            return $this->success('获取成功', array_values($result));
        } catch (\Exception $e) {
            return $this->fail('获取模板库数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * @notes 根据类别获取模板列表（用于PC/H5端）
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getListByCategoryId()
    {
        try {
            $categoryId = $this->request->get('category_id', 0);
            if (!$categoryId) {
                return $this->fail('缺少类别ID参数');
            }
            
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            $templates = \think\facade\Db::table($prefix.'template')
                ->alias('t')
                ->leftJoin($prefix.'example_category c', 'c.id = t.category_id')
                ->where('t.category_id', $categoryId)
                ->where('t.status', 1)
                ->where(function($query) {
                    $query->where('t.delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('t.delete_time');
                          });
                })
                ->field('t.id,t.name,t.description,t.download_url,t.file_size,t.file_type,t.download_count,c.name as category_name')
                ->order('t.sort', 'desc')
                ->select()
                ->toArray();
                
            return $this->success('获取成功', $templates);
        } catch (\Exception $e) {
            return $this->fail('获取模板列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * @notes 下载模板（记录下载次数）
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function download()
    {
        try {
            $id = $this->request->get('id', 0);
            if (!$id) {
                return $this->fail('缺少模板ID参数');
            }
            
            // 使用固定的表前缀cm_
            $prefix = 'cm_';
            
            // 获取模板详情
            $detail = \think\facade\Db::table($prefix.'template')
                ->where('id', '=', $id)
                ->where('status', 1)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->find();
                
            if (empty($detail)) {
                return $this->fail('模板不存在或已被删除');
            }
            
            // 增加下载次数
            \think\facade\Db::table($prefix.'template')
                ->where('id', $id)
                ->inc('download_count', 1)
                ->update();
            
            return $this->success('获取成功', [
                'download_url' => $detail['download_url'],
                'name' => $detail['name']
            ]);
        } catch (\Exception $e) {
            return $this->fail('下载模板失败: ' . $e->getMessage());
        }
    }
} 