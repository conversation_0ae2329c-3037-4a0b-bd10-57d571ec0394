<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\api\controller\kb;
use app\api\controller\BaseApiController;

/**
 * 示例库控制器
 * Class ExampleController
 * @package app\api\controller\kb
 */
class ExampleController extends BaseApiController
{
    /**
     * @notes 获取所有示例库类别及内容
     * @return \think\response\Json
     * <AUTHOR>
     */
    public function getAllExamples()
    {
        try {
            // 先获取所有开启状态的类别
            $categories = \think\facade\Db::table('cm_example_category')
                ->where('status', 1)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->field('id, name')
                ->order('sort', 'desc')
                ->select()
                ->toArray();
            
            if (empty($categories)) {
                return $this->success('获取成功', []);
            }
            
            // 获取所有开启状态的示例内容
            $examples = \think\facade\Db::table('cm_example_content')
                ->where('status', 1)
                ->where(function($query) {
                    $query->where('delete_time', '=', 0)
                          ->whereOr(function($q) {
                              $q->whereNull('delete_time');
                          });
                })
                ->field('id,category_id,title,question,answer')
                ->select()
                ->toArray();
            
            // 将示例内容按类别分组
            $examplesByCategory = [];
            foreach ($examples as $example) {
                $categoryId = $example['category_id'];
                if (!isset($examplesByCategory[$categoryId])) {
                    $examplesByCategory[$categoryId] = [];
                }
                $examplesByCategory[$categoryId][] = [
                    'id' => $example['id'],
                    'title' => $example['title'],
                    'question' => $example['question'],
                    'answer' => $example['answer']
                ];
            }
            
            // 构建结果数组
            $result = [];
            foreach ($categories as $category) {
                $categoryId = $category['id'];
                $result[] = [
                    'id' => $categoryId,
                    'name' => $category['name'],
                    'examples' => $examplesByCategory[$categoryId] ?? []
                ];
            }
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->fail('获取示例库数据失败: ' . $e->getMessage());
        }
    }
} 