<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\api\lists\kb;

use app\api\lists\BaseApiDataLists;
use app\common\model\kb\KbRobot;
use app\common\model\kb\KbRobotShareLog;

/**
 * 机器人列表
 */
class KbRobotLists extends BaseApiDataLists
{
    public function where(): array
    {
        $where = [];
        if (isset($this->params['type']) && is_numeric($this->params['type'])) {
            $where[] = ['is_public', '=', intval($this->params['type'])];
        }
        if (!empty($this->params['keyword']) && $this->params['keyword']) {
            $where[] = ['name', 'like', '%'.$this->params['keyword'].'%'];
        }
        return $where;
    }

    /**
     * @notes 列表
     * @return array
     * @throws @\think\db\exception\DataNotFoundException
     * @throws @\think\db\exception\DbException
     * @throws @\think\db\exception\ModelNotFoundException
     * <AUTHOR>
     */
    public function lists(): array
    {
        $model = new KbRobot();
        $lists =  $model
            ->field(['id,image,name,intro,is_public,is_enable,create_time'])
            ->where(['user_id'=>$this->userId])
            ->where($this->where())
            ->order('id desc')
            ->limit($this->limitOffset, $this->limitLength)
            ->select()
            ->toArray();

        // 🔒 安全优化：获取已分享的智能体信息（包括审核状态和拒绝原因）
        $shareRobotInfo = \think\facade\Db::table('cm_kb_robot_square')
            ->where('user_id', $this->userId)
            ->whereNull('delete_time')
            // 🔒 安全优化：只查询必要字段，避免泄露其他用户信息
            ->column('verify_status,verify_result', 'robot_id');
            
        foreach ($lists as $key =>$list){
            // 🔒 安全优化：清理和验证返回数据
            $lists[$key] = array_intersect_key($list, array_flip([
                'id', 'image', 'name', 'intro', 'is_public', 'is_enable', 'create_time'
            ]));
            
            $lists[$key]['is_share'] = 0;
            $lists[$key]['share_status'] = 0; // 0-未分享, 1-审核中, 2-审核通过, 3-审核拒绝
            $lists[$key]['verify_result'] = '';
            
            if(isset($shareRobotInfo[$list['id']])){
                $shareInfo = $shareRobotInfo[$list['id']];
                $verifyStatus = intval($shareInfo['verify_status']);
                
                $lists[$key]['is_share'] = 1;
                
                if($verifyStatus == 0) {
                    $lists[$key]['share_status'] = 1; // 审核中
                } elseif($verifyStatus == 1) {
                    $lists[$key]['share_status'] = 2; // 审核通过
                } elseif($verifyStatus == 2) {
                    $lists[$key]['share_status'] = 3; // 审核拒绝
                    // 🔒 安全优化：清理拒绝原因中的敏感信息
                    $verifyResult = $shareInfo['verify_result'] ?? '';
                    $lists[$key]['verify_result'] = htmlspecialchars(strip_tags($verifyResult), ENT_QUOTES, 'UTF-8');
                }
            }
        }
        return $lists;
    }

    /**
     * @notes 统计
     * @return int
     * @throws @\think\db\exception\DbException
     * <AUTHOR>
     */
    public function count(): int
    {
        $model = new KbRobot();
        return $model
            ->where(['user_id'=>$this->userId])
            ->where($this->where())
            ->count();
    }
}