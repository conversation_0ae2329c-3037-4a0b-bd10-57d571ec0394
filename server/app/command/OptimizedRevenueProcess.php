<?php

namespace app\command;

use app\common\service\OptimizedBatchRevenueService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Log;

/**
 * 🚀 优化的智能体分成定时任务
 * @notes 针对大并发场景优化的定时任务实现
 * @performance_optimized 2025/08/02
 */
class OptimizedRevenueProcess extends Command
{
    protected function configure()
    {
        $this->setName('revenue:process')
            ->setDescription('优化的智能体分成批量处理任务')
            ->addArgument('mode', Argument::OPTIONAL, '处理模式: auto|batch|parallel', 'auto')
            ->addOption('limit', 'l', Option::VALUE_OPTIONAL, '处理记录数限制', 0)
            ->addOption('parallel', 'p', Option::VALUE_NONE, '启用并行处理')
            ->addOption('detail', null, Option::VALUE_NONE, '详细输出')
            ->addOption('dry-run', 'd', Option::VALUE_NONE, '试运行模式');
    }

    protected function execute(Input $input, Output $output)
    {
        $startTime = microtime(true);
        $mode = $input->getArgument('mode');
        $limit = (int)$input->getOption('limit');
        $parallel = $input->getOption('parallel');
        $verbose = $input->getOption('detail');
        $dryRun = $input->getOption('dry-run');

        // 输出任务开始信息
        $output->writeln('<info>🚀 优化的智能体分成批量处理任务开始</info>');
        $output->writeln("执行时间: " . date('Y-m-d H:i:s'));
        $output->writeln("处理模式: {$mode}");
        $output->writeln("记录限制: " . ($limit > 0 ? $limit : '无限制'));
        $output->writeln("并行处理: " . ($parallel ? '启用' : '禁用'));
        $output->writeln("试运行: " . ($dryRun ? '是' : '否'));
        $output->writeln('');

        try {
            if ($dryRun) {
                $result = $this->dryRunProcess($output, $verbose);
            } else {
                $result = $this->executeProcess($mode, $limit, $parallel, $output, $verbose);
            }

            // 计算执行时间
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);

            // 输出结果统计
            $this->outputResults($result, $executionTime, $output);

            // 记录任务执行日志
            Log::info('[定时任务] 优化分成处理完成', [
                'mode' => $mode,
                'limit' => $limit,
                'parallel' => $parallel,
                'result' => $result,
                'execution_time_ms' => $executionTime
            ]);

            return 0;

        } catch (\Exception $e) {
            $output->writeln('<error>❌ 任务执行失败: ' . $e->getMessage() . '</error>');
            
            Log::error('[定时任务] 优化分成处理失败', [
                'mode' => $mode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 1;
        }
    }

    /**
     * 执行处理
     */
    private function executeProcess(string $mode, int $limit, bool $parallel, Output $output, bool $verbose): array
    {
        switch ($mode) {
            case 'auto':
                if ($verbose) {
                    $output->writeln('<comment>🤖 使用智能调度模式</comment>');
                }
                return OptimizedBatchRevenueService::smartScheduleProcess();

            case 'batch':
                if ($verbose) {
                    $output->writeln('<comment>📦 使用批量处理模式</comment>');
                }
                return OptimizedBatchRevenueService::batchProcessRevenue($limit, false);

            case 'parallel':
                if ($verbose) {
                    $output->writeln('<comment>⚡ 使用并行处理模式</comment>');
                }
                return OptimizedBatchRevenueService::batchProcessRevenue($limit, true);

            default:
                // 根据参数决定
                if ($verbose) {
                    $output->writeln('<comment>🔧 使用参数配置模式</comment>');
                }
                return OptimizedBatchRevenueService::batchProcessRevenue($limit, $parallel);
        }
    }

    /**
     * 试运行处理
     */
    private function dryRunProcess(Output $output, bool $verbose): array
    {
        $output->writeln('<comment>🧪 试运行模式 - 不会实际处理数据</comment>');

        // 获取待处理记录统计
        $pendingCount = \app\common\model\kb\KbRobotRecord::where([
            'revenue_status' => 0,
            ['square_id', '>', 0],
            ['total_cost', '>', 0]
        ])->count();

        if ($verbose) {
            $output->writeln("待处理记录数: {$pendingCount}");
        }

        // 模拟处理结果
        $estimatedTime = $pendingCount * 0.6; // ms
        $estimatedThroughput = $pendingCount > 0 ? round($pendingCount / ($estimatedTime / 1000), 0) : 0;

        return [
            'total_processed' => $pendingCount,
            'total_success' => $pendingCount,
            'total_failed' => 0,
            'success_rate' => 100.0,
            'execution_time_ms' => $estimatedTime,
            'records_per_second' => $estimatedThroughput,
            'avg_time_per_record_ms' => 0.6,
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'timestamp' => date('Y-m-d H:i:s'),
            'dry_run' => true
        ];
    }

    /**
     * 输出结果统计
     */
    private function outputResults(array $result, float $executionTime, Output $output): void
    {
        $output->writeln('');
        $output->writeln('<info>📊 处理结果统计</info>');
        $output->writeln('----------------------------------------');
        
        if (isset($result['dry_run']) && $result['dry_run']) {
            $output->writeln('<comment>⚠️  这是试运行结果，未实际处理数据</comment>');
        }

        $output->writeln("总处理记录: {$result['total_processed']}");
        $output->writeln("成功处理: {$result['total_success']}");
        $output->writeln("失败记录: {$result['total_failed']}");
        $output->writeln("成功率: {$result['success_rate']}%");
        $output->writeln("执行时间: {$result['execution_time_ms']}ms");
        $output->writeln("处理速度: {$result['records_per_second']}条/秒");
        $output->writeln("平均耗时: {$result['avg_time_per_record_ms']}ms/条");
        $output->writeln("内存使用: {$result['memory_usage_mb']}MB");

        // 性能评估
        if ($result['records_per_second'] > 1500) {
            $output->writeln('<info>🚀 性能评估: 优秀</info>');
        } elseif ($result['records_per_second'] > 1000) {
            $output->writeln('<comment>⚡ 性能评估: 良好</comment>');
        } elseif ($result['records_per_second'] > 500) {
            $output->writeln('<comment>📈 性能评估: 一般</comment>');
        } else {
            $output->writeln('<error>⚠️  性能评估: 需要优化</error>');
        }

        // 成功率评估
        if ($result['success_rate'] >= 99) {
            $output->writeln('<info>✅ 成功率: 优秀</info>');
        } elseif ($result['success_rate'] >= 95) {
            $output->writeln('<comment>✅ 成功率: 良好</comment>');
        } elseif ($result['success_rate'] >= 90) {
            $output->writeln('<comment>⚠️  成功率: 一般</comment>');
        } else {
            $output->writeln('<error>❌ 成功率: 需要检查</error>');
        }

        $output->writeln('');
        $output->writeln('<info>✅ 任务执行完成</info>');
    }

    /**
     * 获取系统状态
     */
    private function getSystemStatus(): array
    {
        return [
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'memory_peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'load_average' => sys_getloadavg()[0] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}
