<?php

namespace app\command;

use app\common\enum\RevenueStatusEnum;
use app\common\model\kb\KbRobotRecord;
use app\common\model\kb\KbRobotRevenueConfig;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\model\kb\KbRobotSquare;
use app\common\model\user\User;
use app\common\model\user\UserAccountLog;
use app\common\enum\user\AccountLogEnum;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 优化版智能体分成定时任务处理命令
 * 采用批量事务优化，显著提升处理性能
 *
 * 缓存优化特性：
 * - 随机TTL偏移防止缓存雪崩
 * - 分布式锁防止缓存击穿
 * - 空结果缓存防止缓存穿透
 * - 异常处理和降级机制
 * - 缓存预热和监控
 */
class OptimizedRevenueSettle extends Command
{
    // 缓存配置常量
    private const CACHE_PREFIX = 'revenue:sharer:';
    private const CACHE_TTL_BASE = 3600; // 1小时基础TTL
    private const CACHE_TTL_VARIANCE = 300; // ±5分钟随机偏移
    private const CACHE_NULL_TTL = 300; // 空结果缓存5分钟
    private const CACHE_LOCK_TTL = 30; // 分布式锁30秒
    private const CACHE_LOCK_PREFIX = 'revenue:lock:';

    // 监控配置
    private const MAX_CACHE_ITEMS = 10000; // 最大缓存条目数
    private const MEMORY_WARNING_THRESHOLD = 50 * 1024 * 1024; // 50MB内存警告阈值

    // 缓存统计
    private $cacheStats = [
        'hit_count' => 0,
        'miss_count' => 0,
        'error_count' => 0,
        'lock_wait_count' => 0,
        'null_cache_count' => 0
    ];
    protected function configure()
    {
        $this->setName('optimized_revenue_settle')
            ->setDescription('优化版智能体分成定时任务处理')
            ->addArgument('limit', Argument::OPTIONAL, '单次处理记录数量', 1000)
            ->addOption('debug', 'd', Option::VALUE_NONE, '调试模式')
            ->addOption('stats', 's', Option::VALUE_NONE, '显示统计信息')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制执行')
            ->addOption('batch-size', 'b', Option::VALUE_REQUIRED, '批处理大小', 25)
            ->addOption('max-retry', 'r', Option::VALUE_REQUIRED, '最大重试次数', 3)
            ->addOption('use-cache', 'c', Option::VALUE_NONE, '启用缓存优化')
            ->addOption('cache-warmup', 'w', Option::VALUE_NONE, '启用缓存预热')
            ->addOption('benchmark', null, Option::VALUE_NONE, '性能基准测试模式');
    }

    /**
     * 执行命令
     */
    protected function execute(Input $input, Output $output)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        $limit = (int)($input->getArgument('limit') ?: 1000);
        $isDebug = $input->hasOption('debug');
        $showStats = $input->hasOption('stats');
        $isForce = $input->hasOption('force');
        $batchSize = (int)($input->getOption('batch-size') ?: 25);
        $maxRetry = (int)($input->getOption('max-retry') ?: 3);
        $useCache = $input->hasOption('use-cache');
        $cacheWarmup = $input->hasOption('cache-warmup');
        $isBenchmark = $input->hasOption('benchmark');

        $output->writeln('<info>🚀 优化版智能体分成定时任务开始执行</info>');
        
        if ($isDebug) {
            $output->writeln("<comment>调试模式已启用</comment>");
            $output->writeln("<comment>处理限制: {$limit} 条</comment>");
            $output->writeln("<comment>批处理大小: {$batchSize} 条</comment>");
            $output->writeln("<comment>最大重试: {$maxRetry} 次</comment>");
            $output->writeln("<comment>缓存优化: " . ($useCache ? '启用' : '禁用') . "</comment>");
            $output->writeln("<comment>缓存预热: " . ($cacheWarmup ? '启用' : '禁用') . "</comment>");
        }

        try {
            // 1. 检查分成配置
            if (!$isForce) {
                $config = $this->getRevenueConfig();
                if (!$config || !$config['is_enable']) {
                    $output->writeln('<comment>⚠️ 智能体分成功能未开启，跳过执行</comment>');
                    return 0;
                }
            }

            // 2. 显示统计信息
            if ($showStats) {
                $stats = $this->getStatistics();
                $output->writeln('<comment>📊 当前统计信息:</comment>');
                $output->writeln("  - 待分成记录: {$stats['pending']} 条");
                $output->writeln("  - 分成失败记录: {$stats['failed']} 条");
                $output->writeln("  - 今日已处理: {$stats['today_processed']} 条");
                $output->writeln("  - 今日分成金额: {$stats['today_amount']} 元");
            }

            // 3. 缓存预热
            if ($useCache && $cacheWarmup) {
                $output->writeln('<comment>🔥 执行缓存预热...</comment>');
                $this->warmupCache();
            }

            // 4. 执行优化版批量分成处理
            $result = $this->batchProcessOptimized($limit, $batchSize, $maxRetry, $useCache, $isBenchmark);
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $memoryUsage = round((memory_get_peak_usage(true) - $startMemory) / 1024 / 1024, 2);
            
            if ($result['success']) {
                $output->writeln('<info>✅ 优化版智能体分成定时任务执行成功</info>');
                $output->writeln("<comment>📊 执行统计:</comment>");
                $output->writeln("  - 处理记录数: {$result['total_processed']} 条");
                $output->writeln("  - 分成成功: {$result['total_success']} 条");
                $output->writeln("  - 分成失败: {$result['total_failed']} 条");
                $output->writeln("  - 跳过处理: {$result['total_skipped']} 条");
                $output->writeln("  - 分成金额: {$result['total_amount']} 元");
                $output->writeln("  - 批次数量: {$result['batch_count']} 批");
                $output->writeln("  - 事务数量: {$result['transaction_count']} 个");
                $output->writeln("  - 执行时间: {$executionTime}ms");
                $output->writeln("  - 内存使用: {$memoryUsage}MB");
                
                // 性能统计
                if ($result['total_processed'] > 0) {
                    $avgTimePerRecord = round($executionTime / $result['total_processed'], 2);
                    $recordsPerSec = round($result['total_processed'] / ($executionTime / 1000), 0);
                    $output->writeln("  - 平均处理时间: {$avgTimePerRecord}ms/条");
                    $output->writeln("  - 处理速度: {$recordsPerSec} 条/秒");
                    
                    // 性能对比
                    $oldSpeed = 13.3; // 原有处理速度
                    $improvement = round(($recordsPerSec / $oldSpeed - 1) * 100, 1);
                    $output->writeln("  - 性能提升: {$improvement}%");
                }
                
                // 缓存统计
                if ($useCache && isset($result['cache_stats'])) {
                    $cacheHitRate = round($result['cache_stats']['hit_rate'] * 100, 1);
                    $output->writeln("  - 缓存命中率: {$cacheHitRate}%");
                    $output->writeln("  - 缓存节省查询: {$result['cache_stats']['saved_queries']} 次");
                    $output->writeln("  - 缓存异常次数: {$result['cache_stats']['cache_errors']} 次");

                    if ($result['cache_stats']['memory_warning']) {
                        $output->writeln("  - ⚠️ 缓存内存使用超过警告阈值");
                    }

                    // 显示详细缓存统计
                    $cacheStats = $this->getCacheStatistics();
                    if ($isDebug) {
                        $output->writeln("  - 缓存详细统计:");
                        $output->writeln("    * 命中次数: {$cacheStats['hit_count']}");
                        $output->writeln("    * 未命中次数: {$cacheStats['miss_count']}");
                        $output->writeln("    * 锁等待次数: {$cacheStats['lock_wait_count']}");
                        $output->writeln("    * 空结果缓存次数: {$cacheStats['null_cache_count']}");
                    }
                }
                
                if (!empty($result['errors']) && $isDebug) {
                    $output->writeln('<comment>⚠️ 错误信息:</comment>');
                    foreach ($result['errors'] as $error) {
                        $output->writeln("  - {$error}");
                    }
                }
                
                Log::info('[优化分成] 智能体分成定时任务执行成功', [
                    'execution_time' => $executionTime . 'ms',
                    'memory_usage' => $memoryUsage . 'MB',
                    'limit' => $limit,
                    'batch_size' => $batchSize,
                    'use_cache' => $useCache,
                    'result' => $result
                ]);
                
                return 0;
                
            } else {
                $output->writeln("<error>❌ 优化版智能体分成定时任务执行失败</error>");
                if (!empty($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        $output->writeln("<error>  - {$error}</error>");
                    }
                }
                
                Log::error('[优化分成] 智能体分成定时任务执行失败', [
                    'execution_time' => $executionTime . 'ms',
                    'errors' => $result['errors']
                ]);
                
                return 1;
            }
            
        } catch (\Throwable $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            $output->writeln("<error>❌ 优化版智能体分成定时任务执行异常: {$e->getMessage()}</error>");
            
            Log::error('[优化分成] 智能体分成定时任务异常', [
                'error' => $e->getMessage(),
                'execution_time' => $executionTime . 'ms',
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }

    /**
     * 优化版批量处理待分成记录
     */
    private function batchProcessOptimized(int $limit, int $batchSize, int $maxRetry, bool $useCache, bool $isBenchmark): array
    {
        $result = [
            'success' => false,
            'total_processed' => 0,
            'total_success' => 0,
            'total_failed' => 0,
            'total_skipped' => 0,
            'total_amount' => 0,
            'batch_count' => 0,
            'transaction_count' => 0,
            'errors' => [],
            'cache_stats' => [
                'hit_rate' => 0,
                'saved_queries' => 0
            ]
        ];
        
        try {
            // 1. 获取待分成记录
            $records = $this->getPendingRevenueRecords($limit, $maxRetry);
            if (empty($records)) {
                $result['success'] = true;
                return $result;
            }
            
            // 2. 预处理：按分享者分组并缓存信息
            $groupedData = $this->preProcessRecords($records, $useCache);
            $result['cache_stats'] = $groupedData['cache_stats'];
            
            // 3. 批量处理每个分享者的记录
            foreach ($groupedData['groups'] as $sharerId => $group) {
                $batchResult = $this->processSharerRecordsOptimized(
                    $group['records'], 
                    $group['sharer'], 
                    $batchSize,
                    $isBenchmark
                );
                
                $result['total_processed'] += $batchResult['processed'];
                $result['total_success'] += $batchResult['success'];
                $result['total_failed'] += $batchResult['failed'];
                $result['total_skipped'] += $batchResult['skipped'];
                $result['total_amount'] += $batchResult['amount'];
                $result['batch_count'] += $batchResult['batches'];
                $result['transaction_count'] += $batchResult['transactions'];
                
                if (!empty($batchResult['errors'])) {
                    $result['errors'] = array_merge($result['errors'], $batchResult['errors']);
                }
            }
            
            $result['success'] = true;
            
        } catch (\Throwable $e) {
            $result['errors'][] = $e->getMessage();
            Log::error('[优化分成] 批量处理异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        
        return $result;
    }

    /**
     * 预处理记录：按分享者分组并缓存信息
     */
    private function preProcessRecords(array $records, bool $useCache): array
    {
        $cacheStats = ['hit_rate' => 0, 'saved_queries' => 0];
        
        // 1. 提取所有分享者ID
        $sharerIds = array_unique(array_column($records, 'square_id'));
        
        // 2. 批量获取分享者信息（使用缓存优化）
        $sharers = [];
        $cacheHits = 0;
        
        if ($useCache) {
            // 检查缓存内存使用情况
            $estimatedMemory = count($sharerIds) * 291; // 每条记录291字节
            if ($estimatedMemory > self::MEMORY_WARNING_THRESHOLD) {
                Log::warning('[优化分成] 缓存内存使用可能超过阈值', [
                    'estimated_memory' => $estimatedMemory,
                    'threshold' => self::MEMORY_WARNING_THRESHOLD
                ]);
            }

            // 使用优化的缓存获取方法
            foreach ($sharerIds as $sharerId) {
                $sharerInfo = $this->getSharerInfoWithOptimizedCache($sharerId);
                if ($sharerInfo) {
                    $sharers[$sharerId] = $sharerInfo;
                    if (isset($sharerInfo['_from_cache']) && $sharerInfo['_from_cache']) {
                        $cacheHits++;
                        unset($sharers[$sharerId]['_from_cache']); // 清理标记
                    }
                }
            }

            $cacheStats['hit_rate'] = count($sharerIds) > 0 ? $cacheHits / count($sharerIds) : 0;
            $cacheStats['saved_queries'] = $cacheHits;
            $cacheStats['cache_errors'] = $this->cacheStats['error_count'];
            $cacheStats['memory_warning'] = $estimatedMemory > self::MEMORY_WARNING_THRESHOLD;
        } else {
            // 批量查询所有分享者信息
            $sharerList = KbRobotSquare::whereIn('id', $sharerIds)->select();
            foreach ($sharerList as $sharer) {
                $sharers[$sharer['id']] = $sharer->toArray();
            }
        }
        
        // 3. 按分享者分组记录
        $groups = [];
        foreach ($records as $record) {
            $sharerId = $record['square_id'];
            if (isset($sharers[$sharerId])) {
                if (!isset($groups[$sharerId])) {
                    $groups[$sharerId] = [
                        'sharer' => $sharers[$sharerId],
                        'records' => []
                    ];
                }
                $groups[$sharerId]['records'][] = $record;
            }
        }
        
        return [
            'groups' => $groups,
            'cache_stats' => $cacheStats
        ];
    }

    /**
     * 优化版处理单个分享者的记录
     */
    private function processSharerRecordsOptimized(array $records, array $sharerInfo, int $batchSize, bool $isBenchmark): array
    {
        $result = [
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'amount' => 0,
            'batches' => 0,
            'transactions' => 0,
            'errors' => []
        ];

        // 按批次处理记录
        $batches = array_chunk($records, $batchSize);
        $result['batches'] = count($batches);

        foreach ($batches as $batchIndex => $batch) {
            try {
                $batchResult = $this->processBatchOptimized($batch, $sharerInfo, $batchIndex + 1, $isBenchmark);

                $result['processed'] += $batchResult['processed'];
                $result['success'] += $batchResult['success'];
                $result['failed'] += $batchResult['failed'];
                $result['skipped'] += $batchResult['skipped'];
                $result['amount'] += $batchResult['amount'];
                $result['transactions'] += $batchResult['transactions'];

                if (!empty($batchResult['errors'])) {
                    $result['errors'] = array_merge($result['errors'], $batchResult['errors']);
                }

            } catch (\Exception $e) {
                $result['errors'][] = "批次 " . ($batchIndex + 1) . " 处理异常: " . $e->getMessage();

                // 批次失败时，标记该批次所有记录为重试
                foreach ($batch as $record) {
                    $this->markRecordForRetry($record['id'], $e->getMessage());
                    $result['failed']++;
                }
            }
        }

        return $result;
    }

    /**
     * 优化版批次处理 - 使用单个大事务
     */
    private function processBatchOptimized(array $records, array $sharerInfo, int $batchIndex, bool $isBenchmark): array
    {
        $result = [
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'amount' => 0,
            'transactions' => 1,
            'errors' => []
        ];

        if ($isBenchmark) {
            $batchStartTime = microtime(true);
        }

        // 开启大事务
        Db::startTrans();

        try {
            // 1. 预处理：计算所有分成数据
            $revenueData = [];
            $accountLogs = [];
            $recordUpdates = [];
            $totalShareAmount = 0;
            $config = $this->getRevenueConfig();

            foreach ($records as $record) {
                $result['processed']++;

                // 计算分成金额
                $shareRatio = floatval($config['share_ratio'] ?? 15) / 100;
                $shareAmount = round($record['revenue_base_cost'] * $shareRatio, 4);

                // 检查最小分成金额
                if ($shareAmount < floatval($config['min_revenue'] ?? 0.01)) {
                    $result['skipped']++;
                    $recordUpdates[] = [
                        'id' => $record['id'],
                        'status' => RevenueStatusEnum::SKIPPED,
                        'error' => '分成金额低于最小值'
                    ];
                    continue;
                }

                $totalShareAmount += $shareAmount;
                $platformAmount = round($record['revenue_base_cost'] * (1 - $shareRatio), 4);

                // 准备分成记录数据
                $revenueData[] = [
                    'user_id' => $record['user_id'],
                    'sharer_id' => $sharerInfo['user_id'],
                    'robot_id' => $record['robot_id'],
                    'square_id' => $record['square_id'],
                    'record_id' => $record['id'],
                    'total_cost' => $record['revenue_base_cost'],
                    'share_amount' => $shareAmount,
                    'platform_amount' => $platformAmount,
                    'share_ratio' => $config['share_ratio'],
                    'settle_status' => 1,
                    'settle_time' => time(),
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 准备账户日志数据
                $accountLogs[] = [
                    'user_id' => $sharerInfo['user_id'],
                    'action' => AccountLogEnum::UM_INC_ROBOT_REVENUE,
                    'change_type' => AccountLogEnum::INC,
                    'change_amount' => $shareAmount,
                    'left_amount' => 0, // 将在批量更新后计算
                    'source_sn' => '',
                    'remark' => '智能体分成收益',
                    'extra' => json_encode([
                        'robot_id' => $record['robot_id'],
                        'square_id' => $record['square_id'],
                        'record_id' => $record['id']
                    ]),
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 准备记录更新数据
                $recordUpdates[] = [
                    'id' => $record['id'],
                    'status' => RevenueStatusEnum::SUCCESS,
                    'error' => null
                ];

                $result['success']++;
                $result['amount'] += $shareAmount;
            }

            // 2. 批量执行数据库操作
            if (!empty($revenueData)) {
                // 批量插入分成记录
                Db::table('cm_kb_robot_revenue_log')->insertAll($revenueData);

                // 更新用户余额
                if ($totalShareAmount > 0) {
                    User::where('id', $sharerInfo['user_id'])
                        ->inc('balance', $totalShareAmount)
                        ->update(['update_time' => time()]);
                }

                // 批量插入账户日志
                if (!empty($accountLogs)) {
                    // 获取更新后的用户余额
                    $userBalance = User::where('id', $sharerInfo['user_id'])->value('balance');
                    foreach ($accountLogs as &$log) {
                        $log['left_amount'] = $userBalance;
                    }
                    Db::table('cm_user_account_log')->insertAll($accountLogs);
                }

                // 批量更新记录状态
                foreach ($recordUpdates as $update) {
                    KbRobotRecord::where('id', $update['id'])
                        ->update([
                            'is_revenue_shared' => $update['status'],
                            'revenue_process_time' => time(),
                            'revenue_error' => $update['error']
                        ]);
                }
            }

            // 提交事务
            Db::commit();

            if ($isBenchmark) {
                $batchTime = round((microtime(true) - $batchStartTime) * 1000, 2);
                Log::info("[优化分成] 批次 {$batchIndex} 处理完成", [
                    'batch_size' => count($records),
                    'processing_time' => $batchTime . 'ms',
                    'success' => $result['success'],
                    'skipped' => $result['skipped'],
                    'amount' => $result['amount']
                ]);
            }

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            $result['errors'][] = "批次 {$batchIndex} 事务异常: " . $e->getMessage();
            $result['failed'] = $result['processed'];
            $result['success'] = 0;
            $result['amount'] = 0;

            Log::error("[优化分成] 批次 {$batchIndex} 处理失败", [
                'error' => $e->getMessage(),
                'batch_size' => count($records),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            throw $e;
        }

        return $result;
    }

    /**
     * 标记记录为重试
     */
    private function markRecordForRetry(int $recordId, string $error): void
    {
        try {
            KbRobotRecord::where('id', $recordId)
                ->inc('revenue_retry_count', 1)
                ->update([
                    'revenue_error' => $error,
                    'update_time' => time()
                ]);
        } catch (\Exception $e) {
            Log::error('[优化分成] 标记重试失败', [
                'record_id' => $recordId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取待分成记录
     */
    private function getPendingRevenueRecords(int $limit, int $maxRetry): array
    {
        return KbRobotRecord::where([
                ['is_revenue_shared', '=', RevenueStatusEnum::PENDING],
                ['square_id', '>', 0],
                ['revenue_base_cost', '>', 0]
            ])
            ->where('revenue_retry_count', '<', $maxRetry)
            ->order('create_time ASC')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 获取分成配置
     */
    private function getRevenueConfig(): ?array
    {
        try {
            $config = KbRobotRevenueConfig::order('id desc')->find();
            return $config ? $config->toArray() : null;
        } catch (\Exception $e) {
            Log::error('[优化分成] 获取分成配置异常', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取统计信息
     */
    private function getStatistics(): array
    {
        try {
            $today = date('Y-m-d');
            $todayStart = strtotime($today . ' 00:00:00');
            $todayEnd = strtotime($today . ' 23:59:59');

            return [
                'pending' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::PENDING)
                    ->where('square_id', '>', 0)
                    ->count(),
                'failed' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::FAILED)
                    ->count(),
                'today_processed' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::SUCCESS)
                    ->where('revenue_process_time', '>=', $todayStart)
                    ->where('revenue_process_time', '<=', $todayEnd)
                    ->count(),
                'today_amount' => KbRobotRevenueLog::where('create_time', '>=', $todayStart)
                    ->where('create_time', '<=', $todayEnd)
                    ->sum('share_amount')
            ];
        } catch (\Exception $e) {
            Log::error('[优化分成] 获取统计信息异常', [
                'error' => $e->getMessage()
            ]);
            return [
                'pending' => 0,
                'failed' => 0,
                'today_processed' => 0,
                'today_amount' => 0
            ];
        }
    }

    /**
     * 优化版分享者信息缓存获取 - 实施所有缓存优化方案
     */
    private function getSharerInfoWithOptimizedCache(int $sharerId): ?array
    {
        $cacheKey = self::CACHE_PREFIX . $sharerId;

        try {
            // 1. 尝试从缓存获取
            $cached = $this->safeGetCache($cacheKey);

            if ($cached !== null) {
                // 缓存命中
                $this->cacheStats['hit_count']++;

                if ($cached === 'NULL') {
                    // 空结果缓存命中
                    $this->cacheStats['null_cache_count']++;
                    return null;
                }

                $cached['_from_cache'] = true;
                return $cached;
            }

            // 2. 缓存未命中，使用分布式锁防止缓存击穿
            $lockKey = self::CACHE_LOCK_PREFIX . $sharerId;
            $lockAcquired = false;

            try {
                // 尝试获取分布式锁
                $lockAcquired = $this->acquireDistributedLock($lockKey);

                if ($lockAcquired) {
                    // 获得锁，再次检查缓存（双重检查）
                    $cached = $this->safeGetCache($cacheKey);
                    if ($cached !== null) {
                        if ($cached === 'NULL') {
                            return null;
                        }
                        $cached['_from_cache'] = true;
                        return $cached;
                    }

                    // 查询数据库
                    $sharerInfo = KbRobotSquare::where('id', $sharerId)->find();

                    if ($sharerInfo) {
                        $sharerArray = $sharerInfo->toArray();

                        // 使用随机TTL防止缓存雪崩
                        $ttl = $this->getRandomTTL();
                        $this->safeSetCache($cacheKey, $sharerArray, $ttl);

                        return $sharerArray;
                    } else {
                        // 缓存空结果防止缓存穿透
                        $this->safeSetCache($cacheKey, 'NULL', self::CACHE_NULL_TTL);
                        return null;
                    }
                } else {
                    // 未获得锁，等待并重试
                    $this->cacheStats['lock_wait_count']++;
                    usleep(50000); // 等待50ms

                    $retryResult = $this->safeGetCache($cacheKey);
                    if ($retryResult !== null && $retryResult !== 'NULL') {
                        $retryResult['_from_cache'] = true;
                        return $retryResult;
                    } elseif ($retryResult === 'NULL') {
                        return null;
                    }

                    // 仍然没有缓存，直接查询数据库（降级处理）
                    $sharerInfo = KbRobotSquare::where('id', $sharerId)->find();
                    return $sharerInfo ? $sharerInfo->toArray() : null;
                }

            } finally {
                // 释放分布式锁
                if ($lockAcquired) {
                    $this->releaseDistributedLock($lockKey);
                }
            }

        } catch (\Exception $e) {
            // 缓存异常，降级到数据库查询
            $this->cacheStats['error_count']++;
            $this->cacheStats['miss_count']++;

            Log::warning('[优化分成] 缓存操作异常，降级到数据库查询', [
                'sharer_id' => $sharerId,
                'error' => $e->getMessage()
            ]);

            try {
                $sharerInfo = KbRobotSquare::where('id', $sharerId)->find();
                return $sharerInfo ? $sharerInfo->toArray() : null;
            } catch (\Exception $dbException) {
                Log::error('[优化分成] 数据库查询也失败', [
                    'sharer_id' => $sharerId,
                    'cache_error' => $e->getMessage(),
                    'db_error' => $dbException->getMessage()
                ]);
                return null;
            }
        }
    }

    /**
     * 获取随机TTL防止缓存雪崩
     */
    private function getRandomTTL(): int
    {
        return self::CACHE_TTL_BASE + rand(-self::CACHE_TTL_VARIANCE, self::CACHE_TTL_VARIANCE);
    }

    /**
     * 安全的缓存获取 - 异常处理和降级
     */
    private function safeGetCache(string $key)
    {
        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            $this->cacheStats['error_count']++;
            Log::warning('[优化分成] 缓存读取失败', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 安全的缓存设置 - 异常处理和降级
     */
    private function safeSetCache(string $key, $value, int $ttl): bool
    {
        try {
            return Cache::set($key, $value, $ttl);
        } catch (\Exception $e) {
            $this->cacheStats['error_count']++;
            Log::warning('[优化分成] 缓存写入失败，降级处理', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取分布式锁 - 使用Redis兼容的方式
     */
    private function acquireDistributedLock(string $lockKey): bool
    {
        try {
            // 检查锁是否已存在
            if (Cache::get($lockKey) !== null) {
                return false;
            }

            // 尝试设置锁
            $result = Cache::set($lockKey, time(), self::CACHE_LOCK_TTL);

            // 双重检查，防止竞态条件
            if ($result) {
                $lockValue = Cache::get($lockKey);
                return $lockValue !== null;
            }

            return false;
        } catch (\Exception $e) {
            Log::warning('[优化分成] 获取分布式锁失败', [
                'lock_key' => $lockKey,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 释放分布式锁
     */
    private function releaseDistributedLock(string $lockKey): void
    {
        try {
            Cache::delete($lockKey);
        } catch (\Exception $e) {
            Log::warning('[优化分成] 释放分布式锁失败', [
                'lock_key' => $lockKey,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 缓存预热 - 提高缓存命中率
     */
    private function warmupCache(): void
    {
        try {
            // 获取最近活跃的分享者ID
            $activeSharerIds = Db::table('cm_kb_robot_record')
                ->where('create_time', '>', time() - 86400) // 最近24小时
                ->where('square_id', '>', 0)
                ->group('square_id')
                ->limit(1000) // 限制预热数量
                ->column('square_id');

            if (empty($activeSharerIds)) {
                Log::info('[优化分成] 没有需要预热的缓存');
                return;
            }

            // 批量查询分享者信息
            $sharers = KbRobotSquare::whereIn('id', $activeSharerIds)->select();

            $warmedCount = 0;
            // 预热缓存
            foreach ($sharers as $sharer) {
                $cacheKey = self::CACHE_PREFIX . $sharer['id'];
                $ttl = $this->getRandomTTL();

                if ($this->safeSetCache($cacheKey, $sharer->toArray(), $ttl)) {
                    $warmedCount++;
                }
            }

            Log::info('[优化分成] 缓存预热完成', [
                'warmed_count' => $warmedCount,
                'total_active' => count($activeSharerIds)
            ]);

        } catch (\Exception $e) {
            Log::error('[优化分成] 缓存预热异常', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取缓存统计信息
     */
    private function getCacheStatistics(): array
    {
        $total = $this->cacheStats['hit_count'] + $this->cacheStats['miss_count'];
        $hitRate = $total > 0 ? round(($this->cacheStats['hit_count'] / $total) * 100, 2) : 0;

        return [
            'hit_rate' => $hitRate,
            'hit_count' => $this->cacheStats['hit_count'],
            'miss_count' => $this->cacheStats['miss_count'],
            'error_count' => $this->cacheStats['error_count'],
            'lock_wait_count' => $this->cacheStats['lock_wait_count'],
            'null_cache_count' => $this->cacheStats['null_cache_count'],
            'total_requests' => $total
        ];
    }
}
