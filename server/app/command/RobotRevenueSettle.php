<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\command;

use app\common\service\SimpleRevenueService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Log;

/**
 * 智能体分成收益结算定时任务（每日结算专用）
 */
class RobotRevenueSettle extends Command
{
    protected function configure()
    {
        $this->setName('robot_revenue_settle')
            ->setDescription('智能体分成收益每日结算')
            ->addArgument('limit', Argument::OPTIONAL, '处理记录数量限制', 200)
            ->addOption('debug', 'd', Option::VALUE_NONE, '调试模式')
            ->addOption('stats', 's', Option::VALUE_NONE, '显示统计信息')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制执行')
            ->addOption('turbo', 't', Option::VALUE_NONE, '🚀 极速模式：针对大量数据优化')
            ->addOption('max-time', null, Option::VALUE_REQUIRED, '最大执行时间（秒）', 3600)
            ->addOption('memory-limit', null, Option::VALUE_REQUIRED, '内存限制（MB）', 512);
    }

    /**
     * 执行命令
     */
    protected function execute(Input $input, Output $output)
    {
        $startTime = microtime(true);
        $limit = (int)($input->getArgument('limit') ?: 200);
        $isDebug = $input->hasOption('debug');
        $showStats = $input->hasOption('stats');
        $isForce = $input->hasOption('force');
        $isTurbo = $input->hasOption('turbo');
        $maxTime = (int)($input->getOption('max-time') ?: 3600);
        $memoryLimit = (int)($input->getOption('memory-limit') ?: 512);

        // 输出开始信息
        $output->writeln("<info>🤖 智能体分成收益每日结算任务开始</info>");
        $output->writeln("<comment>处理限制: {$limit} 条记录</comment>");
        $output->writeln("<comment>调试模式: " . ($isDebug ? '开启' : '关闭') . "</comment>");
        $output->writeln("<comment>极速模式: " . ($isTurbo ? '开启' : '关闭') . "</comment>");
        $output->writeln("<comment>最大执行时间: {$maxTime} 秒</comment>");
        $output->writeln("<comment>内存限制: {$memoryLimit} MB</comment>");

        try {
            // 🚀 设置执行环境优化
            if ($isTurbo) {
                // 极速模式配置
                ini_set('memory_limit', $memoryLimit . 'M');
                ini_set('max_execution_time', $maxTime);
                $limit = max($limit, 1000); // 极速模式最小1000条/批
                $output->writeln("<info>🚀 极速模式已启用，批次调整为: {$limit} 条</info>");
            }
            
            // 检查分成配置（可选检查，不强制要求）
            if (!$isForce) {
                $config = SimpleRevenueService::getConfig();
                if (!$config || !$config['is_enable']) {
                    $output->writeln('<comment>⚠️ 智能体分成功能未开启，跳过执行</comment>');
                    Log::info('[定时结算] 智能体分成功能未开启，跳过执行');
                    return 0;
                }
                
                $settleType = intval($config['settle_type'] ?? 1);
                if ($settleType == 1 && !$isForce) {
                    $output->writeln('<comment>⚠️ 当前为实时结算模式，跳过定时结算（使用 --force 强制执行）</comment>');
                    Log::info('[定时结算] 当前为实时结算模式，跳过定时结算', ['settle_type' => $settleType]);
                    return 0;
                }
            }

            // 显示统计信息
            if ($showStats) {
                $stats = SimpleRevenueService::getStats();
                $output->writeln('<info>📊 系统统计信息:</info>');
                $output->writeln("  - 总对话记录: {$stats['total_records']} 条");
                $output->writeln("  - 已处理记录: {$stats['processed_records']} 条");
                $output->writeln("  - 分成记录总数: {$stats['total_revenue_logs']} 条");
                $output->writeln("  - 待结算记录: {$stats['pending_settle_logs']} 条");
                $output->writeln("  - 已结算记录: {$stats['settled_logs']} 条");
                
                // 🚀 大数据量预估
                if ($stats['pending_settle_logs'] > 10000) {
                    $estimatedBatches = ceil($stats['pending_settle_logs'] / $limit);
                    $estimatedTime = $isTurbo ? 
                        round($estimatedBatches * 0.1, 1) . '分钟（预估，极速模式）' :
                        round($estimatedBatches * 0.2, 1) . '分钟（预估，普通模式）';
                    $output->writeln("<comment>⏱️  预计处理时间: {$estimatedTime}</comment>");
                    $output->writeln("<comment>📦 预计批次数: {$estimatedBatches} 批</comment>");
                }
            }

            // 🚀 大数据量处理警告和确认
            $pendingCount = \app\common\model\kb\KbRobotRevenueLog::where(['settle_status' => 0])->count();
            if ($pendingCount > 100000 && !$isTurbo) {
                $output->writeln("<comment>⚠️  检测到大量待处理数据 ({$pendingCount} 条)，建议使用极速模式: --turbo</comment>");
                $output->writeln("<comment>   极速模式可将处理时间从几小时缩短到几十分钟</comment>");
            }

            // 执行批量结算
            $output->writeln('<info>🚀 开始执行批量结算...</info>');
            
            $result = SimpleRevenueService::batchSettlePending($limit);
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            if ($result['success']) {
                $output->writeln('<info>✅ 智能体分成收益结算执行成功</info>');
                $output->writeln("<comment>📊 执行统计:</comment>");
                $output->writeln("  - 处理记录数: {$result['total_processed']} 条");
                $output->writeln("  - 成功结算: {$result['total_success']} 条");
                $output->writeln("  - 结算失败: {$result['total_failed']} 条");
                $output->writeln("  - 结算金额: {$result['total_amount']} 电力值");
                $output->writeln("  - 批次数量: {$result['batch_count']} 批");
                $output->writeln("  - 执行时间: {$executionTime}ms");
                $output->writeln("  - 内存使用: {$result['memory_usage']}MB");
                
                // 🚀 性能统计
                if ($result['total_processed'] > 0) {
                    $avgTimePerRecord = round($executionTime / $result['total_processed'], 2);
                    $recordsPerSec = round($result['total_processed'] / ($executionTime / 1000), 0);
                    $output->writeln("  - 平均处理时间: {$avgTimePerRecord}ms/条");
                    $output->writeln("  - 处理速度: {$recordsPerSec} 条/秒");
                }
                
                if (!empty($result['errors']) && $isDebug) {
                    $output->writeln('<comment>⚠️ 错误信息:</comment>');
                    foreach ($result['errors'] as $error) {
                        $output->writeln("  - {$error}");
                    }
                }
                
                Log::info('[定时结算] 智能体分成收益结算定时任务执行成功', [
                    'execution_time' => $executionTime . 'ms',
                    'limit' => $limit,
                    'turbo_mode' => $isTurbo,
                    'result' => $result
                ]);
                
                return 0;
            } else {
                $output->writeln('<error>❌ 智能体分成收益结算执行失败</error>');
                $output->writeln("<error>错误信息: {$result['message']}</error>");
                
                if (!empty($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        $output->writeln("<error>  - {$error}</error>");
                    }
                }
                
                Log::error('[定时结算] 智能体分成收益结算定时任务执行失败', [
                    'execution_time' => $executionTime . 'ms',
                    'limit' => $limit,
                    'turbo_mode' => $isTurbo,
                    'result' => $result
                ]);
                
                throw new \Exception("执行失败：{$result['message']}");
            }
            
        } catch (\Throwable $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            $output->writeln("<error>❌ 智能体分成收益结算执行异常: {$e->getMessage()}</error>");
            
            Log::error('[定时结算] 智能体分成收益结算定时任务异常', [
                'error' => $e->getMessage(),
                'execution_time' => $executionTime . 'ms',
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'limit' => $limit,
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
} 