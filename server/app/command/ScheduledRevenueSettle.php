<?php

namespace app\command;

use app\common\enum\RevenueStatusEnum;
use app\common\model\kb\KbRobotRecord;
use app\common\model\kb\KbRobotRevenueConfig;
use app\common\model\kb\KbRobotRevenueLog;
use app\common\model\kb\KbRobotSquare;
use app\common\model\user\User;
use app\common\model\user\UserAccountLog;
use app\common\enum\user\AccountLogEnum;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

/**
 * 智能体分成定时任务处理命令
 * 处理待分成记录，批量执行分成操作
 */
class ScheduledRevenueSettle extends Command
{
    protected function configure()
    {
        $this->setName('scheduled_revenue_settle')
            ->setDescription('智能体分成定时任务处理')
            ->addArgument('limit', Argument::OPTIONAL, '单次处理记录数量', 200)
            ->addOption('debug', 'd', Option::VALUE_NONE, '调试模式')
            ->addOption('stats', 's', Option::VALUE_NONE, '显示统计信息')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制执行（忽略配置检查）')
            ->addOption('batch-size', 'b', Option::VALUE_REQUIRED, '批处理大小', 50)
            ->addOption('max-retry', 'r', Option::VALUE_REQUIRED, '最大重试次数', 3);
    }

    /**
     * 执行命令
     */
    protected function execute(Input $input, Output $output)
    {
        $startTime = microtime(true);
        $limit = (int)($input->getArgument('limit') ?: 200);
        $isDebug = $input->hasOption('debug');
        $showStats = $input->hasOption('stats');
        $isForce = $input->hasOption('force');
        $batchSize = (int)($input->getOption('batch-size') ?: 50);
        $maxRetry = (int)($input->getOption('max-retry') ?: 3);

        $output->writeln('<info>🚀 智能体分成定时任务开始执行</info>');
        
        if ($isDebug) {
            $output->writeln("<comment>调试模式已启用</comment>");
            $output->writeln("<comment>处理限制: {$limit} 条</comment>");
            $output->writeln("<comment>批处理大小: {$batchSize} 条</comment>");
            $output->writeln("<comment>最大重试: {$maxRetry} 次</comment>");
        }

        try {
            // 1. 检查分成配置
            if (!$isForce) {
                $config = $this->getRevenueConfig();
                if (!$config || !$config['is_enable']) {
                    $output->writeln('<comment>⚠️ 智能体分成功能未开启，跳过执行</comment>');
                    Log::info('[定时分成] 智能体分成功能未开启，跳过执行');
                    return 0;
                }
            }

            // 2. 显示统计信息
            if ($showStats) {
                $stats = $this->getStatistics();
                $output->writeln('<comment>📊 当前统计信息:</comment>');
                $output->writeln("  - 待分成记录: {$stats['pending']} 条");
                $output->writeln("  - 分成失败记录: {$stats['failed']} 条");
                $output->writeln("  - 今日已处理: {$stats['today_processed']} 条");
                $output->writeln("  - 今日分成金额: {$stats['today_amount']} 元");
            }

            // 3. 执行批量分成处理
            $result = $this->batchProcessPendingRevenue($limit, $batchSize, $maxRetry);
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            if ($result['success']) {
                $output->writeln('<info>✅ 智能体分成定时任务执行成功</info>');
                $output->writeln("<comment>📊 执行统计:</comment>");
                $output->writeln("  - 处理记录数: {$result['total_processed']} 条");
                $output->writeln("  - 分成成功: {$result['total_success']} 条");
                $output->writeln("  - 分成失败: {$result['total_failed']} 条");
                $output->writeln("  - 跳过处理: {$result['total_skipped']} 条");
                $output->writeln("  - 分成金额: {$result['total_amount']} 元");
                $output->writeln("  - 批次数量: {$result['batch_count']} 批");
                $output->writeln("  - 执行时间: {$executionTime}ms");
                $output->writeln("  - 内存使用: {$result['memory_usage']}MB");
                
                // 性能统计
                if ($result['total_processed'] > 0) {
                    $avgTimePerRecord = round($executionTime / $result['total_processed'], 2);
                    $recordsPerSec = round($result['total_processed'] / ($executionTime / 1000), 0);
                    $output->writeln("  - 平均处理时间: {$avgTimePerRecord}ms/条");
                    $output->writeln("  - 处理速度: {$recordsPerSec} 条/秒");
                }
                
                if (!empty($result['errors']) && $isDebug) {
                    $output->writeln('<comment>⚠️ 错误信息:</comment>');
                    foreach ($result['errors'] as $error) {
                        $output->writeln("  - {$error}");
                    }
                }
                
                Log::info('[定时分成] 智能体分成定时任务执行成功', [
                    'execution_time' => $executionTime . 'ms',
                    'limit' => $limit,
                    'batch_size' => $batchSize,
                    'result' => $result
                ]);
                
                return 0;
                
            } else {
                $output->writeln("<error>❌ 智能体分成定时任务执行失败</error>");
                if (!empty($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        $output->writeln("<error>  - {$error}</error>");
                    }
                }
                
                Log::error('[定时分成] 智能体分成定时任务执行失败', [
                    'execution_time' => $executionTime . 'ms',
                    'errors' => $result['errors']
                ]);
                
                return 1;
            }
            
        } catch (\Throwable $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            $output->writeln("<error>❌ 智能体分成定时任务执行异常: {$e->getMessage()}</error>");
            
            Log::error('[定时分成] 智能体分成定时任务异常', [
                'error' => $e->getMessage(),
                'execution_time' => $executionTime . 'ms',
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }

    /**
     * 批量处理待分成记录
     */
    private function batchProcessPendingRevenue(int $limit, int $batchSize, int $maxRetry): array
    {
        $result = [
            'success' => false,
            'total_processed' => 0,
            'total_success' => 0,
            'total_failed' => 0,
            'total_skipped' => 0,
            'total_amount' => 0,
            'batch_count' => 0,
            'errors' => [],
            'memory_usage' => 0
        ];
        
        try {
            // 1. 查询待分成记录
            $pendingRecords = $this->getPendingRevenueRecords($limit, $maxRetry);
            if (empty($pendingRecords)) {
                $result['success'] = true;
                return $result;
            }
            
            // 2. 按批次处理
            $batches = array_chunk($pendingRecords, $batchSize);
            $result['batch_count'] = count($batches);
            
            foreach ($batches as $batchIndex => $batch) {
                $batchResult = $this->processBatch($batch, $batchIndex + 1);
                
                $result['total_processed'] += $batchResult['processed'];
                $result['total_success'] += $batchResult['success'];
                $result['total_failed'] += $batchResult['failed'];
                $result['total_skipped'] += $batchResult['skipped'];
                $result['total_amount'] += $batchResult['amount'];
                
                if (!empty($batchResult['errors'])) {
                    $result['errors'] = array_merge($result['errors'], $batchResult['errors']);
                }
                
                // 内存清理
                unset($batch, $batchResult);
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
            
            $result['success'] = true;
            $result['memory_usage'] = round(memory_get_peak_usage(true) / 1024 / 1024, 2);
            
        } catch (\Throwable $e) {
            $result['errors'][] = $e->getMessage();
            Log::error('[定时分成] 批量处理异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        
        return $result;
    }

    /**
     * 获取待分成记录
     */
    private function getPendingRevenueRecords(int $limit, int $maxRetry): array
    {
        return KbRobotRecord::where([
                ['is_revenue_shared', '=', RevenueStatusEnum::PENDING],
                ['square_id', '>', 0],
                ['revenue_base_cost', '>', 0]
            ])
            ->where('revenue_retry_count', '<', $maxRetry)
            ->order('create_time ASC')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 处理单个批次
     */
    private function processBatch(array $records, int $batchIndex): array
    {
        $result = [
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'amount' => 0,
            'errors' => []
        ];
        
        Log::info("[定时分成] 开始处理第{$batchIndex}批次", [
            'batch_size' => count($records),
            'batch_index' => $batchIndex
        ]);
        
        foreach ($records as $record) {
            $processResult = $this->processRecord($record);
            
            $result['processed']++;
            
            if ($processResult['status'] === 'success') {
                $result['success']++;
                $result['amount'] += $processResult['amount'];
            } elseif ($processResult['status'] === 'failed') {
                $result['failed']++;
                if (!empty($processResult['error'])) {
                    $result['errors'][] = "记录ID {$record['id']}: {$processResult['error']}";
                }
            } elseif ($processResult['status'] === 'skipped') {
                $result['skipped']++;
            }
        }
        
        Log::info("[定时分成] 第{$batchIndex}批次处理完成", [
            'processed' => $result['processed'],
            'success' => $result['success'],
            'failed' => $result['failed'],
            'skipped' => $result['skipped'],
            'amount' => $result['amount']
        ]);
        
        return $result;
    }

    /**
     * 处理单条记录
     */
    private function processRecord(array $record): array
    {
        try {
            // 1. 获取分享者信息
            $squareInfo = KbRobotSquare::where('id', $record['square_id'])->find();
            if (!$squareInfo) {
                return $this->markRecordSkipped($record['id'], '分享信息不存在');
            }

            // 2. 获取分成配置
            $config = $this->getRevenueConfig();
            if (!$config || !$config['is_enable']) {
                return $this->markRecordSkipped($record['id'], '分成功能未启用');
            }

            // 3. 计算分成金额
            $shareRatio = floatval($config['share_ratio'] ?? 15) / 100;
            $shareAmount = round($record['revenue_base_cost'] * $shareRatio, 4);
            $platformAmount = round($record['revenue_base_cost'] * (1 - $shareRatio), 4);

            // 4. 检查最小分成金额
            if ($shareAmount < floatval($config['min_revenue'] ?? 0.01)) {
                return $this->markRecordSkipped($record['id'], '分成金额低于最小值');
            }

            // 5. 执行分成操作
            $this->executeRevenue($record, $squareInfo, $shareAmount, $platformAmount, $config);

            return [
                'status' => 'success',
                'amount' => $shareAmount
            ];

        } catch (\Exception $e) {
            // 增加重试次数
            $retryCount = intval($record['revenue_retry_count']) + 1;

            if ($retryCount >= 3) {
                // 超过最大重试次数，标记为失败
                return $this->markRecordFailed($record['id'], $e->getMessage());
            } else {
                // 标记为重试
                KbRobotRecord::where('id', $record['id'])
                    ->update([
                        'revenue_retry_count' => $retryCount,
                        'revenue_error' => $e->getMessage()
                    ]);

                return [
                    'status' => 'retry',
                    'error' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * 执行分成操作
     */
    private function executeRevenue(array $record, $squareInfo, float $shareAmount, float $platformAmount, array $config): void
    {
        // 开启事务
        Db::startTrans();

        try {
            // 1. 创建分成记录
            $revenueData = [
                'user_id' => $record['user_id'],
                'sharer_id' => $squareInfo['user_id'],
                'robot_id' => $record['robot_id'],
                'square_id' => $record['square_id'],
                'record_id' => $record['id'],
                'total_cost' => $record['revenue_base_cost'],
                'share_amount' => $shareAmount,
                'platform_amount' => $platformAmount,
                'share_ratio' => $config['share_ratio'],
                'settle_status' => 1, // 定时结算
                'settle_time' => time(),
                'create_time' => time(),
                'update_time' => time()
            ];

            $revenueLog = KbRobotRevenueLog::create($revenueData);

            // 2. 更新分享者余额
            User::where('id', $squareInfo['user_id'])
                ->inc('balance', $shareAmount)
                ->update(['update_time' => time()]);

            // 3. 记录余额变动日志
            UserAccountLog::add(
                $squareInfo['user_id'],
                AccountLogEnum::UM_INC_ROBOT_REVENUE,
                AccountLogEnum::INC,
                $shareAmount,
                '',
                '智能体分成收益',
                [
                    'robot_id' => $record['robot_id'],
                    'square_id' => $record['square_id'],
                    'record_id' => $record['id']
                ]
            );

            // 4. 标记记录已分成
            KbRobotRecord::where('id', $record['id'])
                ->update([
                    'is_revenue_shared' => RevenueStatusEnum::SUCCESS,
                    'revenue_log_id' => $revenueLog->id,
                    'revenue_process_time' => time(),
                    'revenue_error' => null
                ]);

            // 提交事务
            Db::commit();

            Log::info('[定时分成] 分成处理成功', [
                'record_id' => $record['id'],
                'sharer_id' => $squareInfo['user_id'],
                'share_amount' => $shareAmount,
                'base_cost' => $record['revenue_base_cost']
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 标记记录为跳过
     */
    private function markRecordSkipped(int $recordId, string $reason): array
    {
        KbRobotRecord::where('id', $recordId)
            ->update([
                'is_revenue_shared' => RevenueStatusEnum::SKIPPED,
                'revenue_process_time' => time(),
                'revenue_error' => $reason
            ]);

        Log::info('[定时分成] 记录标记为跳过', [
            'record_id' => $recordId,
            'reason' => $reason
        ]);

        return [
            'status' => 'skipped',
            'reason' => $reason
        ];
    }

    /**
     * 标记记录为失败
     */
    private function markRecordFailed(int $recordId, string $error): array
    {
        KbRobotRecord::where('id', $recordId)
            ->update([
                'is_revenue_shared' => RevenueStatusEnum::FAILED,
                'revenue_process_time' => time(),
                'revenue_error' => $error
            ]);

        Log::error('[定时分成] 记录标记为失败', [
            'record_id' => $recordId,
            'error' => $error
        ]);

        return [
            'status' => 'failed',
            'error' => $error
        ];
    }

    /**
     * 获取分成配置
     */
    private function getRevenueConfig(): ?array
    {
        try {
            $config = KbRobotRevenueConfig::order('id desc')->find();
            return $config ? $config->toArray() : null;
        } catch (\Exception $e) {
            Log::error('[定时分成] 获取分成配置异常', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取统计信息
     */
    private function getStatistics(): array
    {
        try {
            $today = date('Y-m-d');
            $todayStart = strtotime($today . ' 00:00:00');
            $todayEnd = strtotime($today . ' 23:59:59');

            return [
                'pending' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::PENDING)
                    ->where('square_id', '>', 0)
                    ->count(),
                'failed' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::FAILED)
                    ->count(),
                'today_processed' => KbRobotRecord::where('is_revenue_shared', RevenueStatusEnum::SUCCESS)
                    ->where('revenue_process_time', '>=', $todayStart)
                    ->where('revenue_process_time', '<=', $todayEnd)
                    ->count(),
                'today_amount' => KbRobotRevenueLog::where('create_time', '>=', $todayStart)
                    ->where('create_time', '<=', $todayEnd)
                    ->sum('share_amount')
            ];
        } catch (\Exception $e) {
            Log::error('[定时分成] 获取统计信息异常', [
                'error' => $e->getMessage()
            ]);
            return [
                'pending' => 0,
                'failed' => 0,
                'today_processed' => 0,
                'today_amount' => 0
            ];
        }
    }
}
