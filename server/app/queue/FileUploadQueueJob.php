<?php
// +----------------------------------------------------------------------
// | AI项目文件上传队列处理器
// +----------------------------------------------------------------------

namespace app\queue;

use app\common\service\AsyncUploadService;
use app\common\service\ConfigService;
use app\common\service\storage\Driver as StorageDriver;
use Exception;
use think\facade\Log;
use think\queue\Job;

/**
 * 文件上传队列处理器
 * 异步处理文件上传任务，提升用户响应速度
 */
class FileUploadQueueJob
{
    /**
     * 处理单个文件上传任务
     */
    public function fire(Job $job, array $data): void
    {
        $startTime = microtime(true);
        
        try {
            echo "\n[" . date('Y-m-d H:i:s') . "] 开始处理文件上传任务: {$data['task_id']}\n";
            
            // 验证任务数据
            if (!isset($data['task_id'], $data['temp_file_path'], $data['target_path'])) {
                throw new Exception('任务数据不完整');
            }
            
            $taskId = $data['task_id'];
            $tempFilePath = $data['temp_file_path'];
            $targetPath = $data['target_path'];
            
            // 检查临时文件是否存在
            if (!file_exists($tempFilePath)) {
                throw new Exception('临时文件不存在: ' . $tempFilePath);
            }
            
            // 获取存储配置
            $config = [
                'default' => ConfigService::get('storage', 'default', 'local'),
                'engine'  => ConfigService::get('storage') ?? ['local' => []],
            ];
            
            // 执行文件上传
            $storageDriver = new StorageDriver($config);
            
            // 设置文件信息
            $fileInfo = $this->getFileInfoFromPath($tempFilePath);
            
            // 上传到目标位置
            if (!$this->uploadFromTemp($storageDriver, $tempFilePath, $targetPath)) {
                throw new Exception('文件上传失败: ' . $storageDriver->getError());
            }
            
            // 计算处理时间
            $processingTime = (microtime(true) - $startTime) * 1000;
            
            // 标记任务完成
            AsyncUploadService::markTaskCompleted($taskId, [
                'success' => true,
                'file_info' => [
                    'uri' => $targetPath,
                    'size' => $fileInfo['size'],
                ],
                'processing_time' => round($processingTime, 2),
            ]);
            
            // 清理临时文件
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }
            
            echo "任务完成: {$taskId}, 耗时: " . round($processingTime, 2) . "ms\n";
            
            // 删除队列任务
            $job->delete();
            
        } catch (Exception $e) {
            $processingTime = (microtime(true) - $startTime) * 1000;
            
            Log::error('文件上传队列处理失败: ' . $e->getMessage(), [
                'task_id' => $data['task_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'processing_time' => round($processingTime, 2),
            ]);
            
            // 标记任务失败
            if (isset($data['task_id'])) {
                AsyncUploadService::markTaskCompleted($data['task_id'], [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'processing_time' => round($processingTime, 2),
                ]);
            }
            
            // 清理临时文件
            if (isset($data['temp_file_path']) && file_exists($data['temp_file_path'])) {
                unlink($data['temp_file_path']);
            }
            
            echo "任务失败: {$data['task_id']}, 错误: {$e->getMessage()}\n";
            
            // 重试机制
            if ($job->attempts() < 3) {
                $job->release(30); // 30秒后重试
            } else {
                $job->delete(); // 超过重试次数，删除任务
            }
        }
    }
    
    /**
     * 处理批量文件上传任务
     */
    public function batchFire(Job $job, array $data): void
    {
        $startTime = microtime(true);
        
        try {
            echo "\n[" . date('Y-m-d H:i:s') . "] 开始处理批量文件上传任务\n";
            
            if (!isset($data['tasks']) || !is_array($data['tasks'])) {
                throw new Exception('批量任务数据不完整');
            }
            
            $successCount = 0;
            $failCount = 0;
            $tasks = $data['tasks'];
            
            foreach ($tasks as $task) {
                try {
                    // 处理单个任务
                    $this->processSingleTask($task);
                    $successCount++;
                    
                } catch (Exception $e) {
                    Log::error('批量上传中单个任务失败: ' . $e->getMessage(), [
                        'task_id' => $task['task_id'] ?? 'unknown',
                    ]);
                    $failCount++;
                }
            }
            
            $processingTime = (microtime(true) - $startTime) * 1000;
            
            echo "批量任务完成: 成功 {$successCount}, 失败 {$failCount}, 耗时: " . round($processingTime, 2) . "ms\n";
            
            // 删除队列任务
            $job->delete();
            
        } catch (Exception $e) {
            Log::error('批量文件上传队列处理失败: ' . $e->getMessage());
            
            // 重试机制
            if ($job->attempts() < 3) {
                $job->release(60); // 60秒后重试
            } else {
                $job->delete();
            }
        }
    }
    
    /**
     * 处理单个任务（内部方法）
     */
    private function processSingleTask(array $taskData): void
    {
        $startTime = microtime(true);
        
        $taskId = $taskData['task_id'];
        $tempFilePath = $taskData['temp_file']['temp_path'];
        $targetPath = $taskData['temp_file']['target_path'];
        
        // 检查临时文件
        if (!file_exists($tempFilePath)) {
            throw new Exception('临时文件不存在: ' . $tempFilePath);
        }
        
        // 获取存储配置
        $config = [
            'default' => ConfigService::get('storage', 'default', 'local'),
            'engine'  => ConfigService::get('storage') ?? ['local' => []],
        ];
        
        $storageDriver = new StorageDriver($config);
        
        // 执行上传
        if (!$this->uploadFromTemp($storageDriver, $tempFilePath, $targetPath)) {
            throw new Exception('文件上传失败: ' . $storageDriver->getError());
        }
        
        $processingTime = (microtime(true) - $startTime) * 1000;
        $fileInfo = $this->getFileInfoFromPath($tempFilePath);
        
        // 标记完成
        AsyncUploadService::markTaskCompleted($taskId, [
            'success' => true,
            'file_info' => [
                'uri' => $targetPath,
                'size' => $fileInfo['size'],
            ],
            'processing_time' => round($processingTime, 2),
        ]);
        
        // 清理临时文件
        if (file_exists($tempFilePath)) {
            unlink($tempFilePath);
        }
    }
    
    /**
     * 从临时文件上传到目标位置
     */
    private function uploadFromTemp(StorageDriver $driver, string $tempPath, string $targetPath): bool
    {
        try {
            // 创建目标目录
            $targetDir = dirname($targetPath);
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
            }
            
            // 移动文件到目标位置
            if (copy($tempPath, $targetPath)) {
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            Log::error('文件移动失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 从文件路径获取文件信息
     */
    private function getFileInfoFromPath(string $filePath): array
    {
        if (!file_exists($filePath)) {
            return ['size' => 0];
        }
        
        return [
            'size' => filesize($filePath),
            'name' => basename($filePath),
            'ext' => pathinfo($filePath, PATHINFO_EXTENSION),
        ];
    }
    
    /**
     * 任务失败处理
     */
    public function failed(array $data, Exception $e): void
    {
        Log::error('文件上传队列任务最终失败: ' . $e->getMessage(), [
            'task_data' => $data,
            'error' => $e->getMessage(),
        ]);
        
        // 清理临时文件
        if (isset($data['temp_file_path']) && file_exists($data['temp_file_path'])) {
            unlink($data['temp_file_path']);
        }
        
        // 标记任务失败
        if (isset($data['task_id'])) {
            AsyncUploadService::markTaskCompleted($data['task_id'], [
                'success' => false,
                'error' => '队列处理失败: ' . $e->getMessage(),
                'processing_time' => 0,
            ]);
        }
    }
} 