<?php
/**
 * 优化版基础队列服务
 * 实现中期优化: 连接池管理、批量处理、内存优化、性能监控
 * 
 * 优化特性:
 * 1. 连接池管理: 复用Redis连接，减少连接开销
 * 2. 批量处理机制: 批量推入和获取队列任务
 * 3. 内存优化: 智能内存管理和垃圾回收
 * 4. 性能监控: 详细的队列性能统计
 * 5. 错误处理: 完善的异常处理和重试机制
 * 6. 队列预热: 预加载队列配置和连接
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */

namespace app\queue;

use think\facade\Cache;
use think\facade\Log;

class OptimizedBaseQueue
{
    // 队列名称常量
    private static string $EM_JOB = 'emJob'; // 向量任务
    private static string $QA_JOB = 'qaJob'; // QA的任务
    private static string $FILE_JOB = 'fileJob'; // 文件处理任务
    private static string $AUDIT_JOB = 'auditJob'; // 审核任务
    
    // 连接池配置
    private static array $connectionPool = [];
    private static int $maxConnections = 10;
    private static int $connectionTimeout = 30;
    
    // 批量处理配置
    private static int $batchSize = 50;
    private static int $maxBatchWaitTime = 1000; // 1秒
    
    // 性能统计
    private static array $performanceStats = [
        'total_pushes' => 0,
        'total_pops' => 0,
        'batch_operations' => 0,
        'connection_reuses' => 0,
        'total_time' => 0,
        'average_time' => 0,
        'memory_peak' => 0
    ];
    
    // 内存管理
    private static int $memoryLimit = 100 * 1024 * 1024; // 100MB
    private static int $gcThreshold = 1000; // 每1000次操作执行一次GC
    private static int $operationCount = 0;
    
    /**
     * 优化版向量任务队列推入
     */
    public static function pushEM(array $data): void
    {
        self::pushOptimized(self::$EM_JOB, $data);
    }
    
    /**
     * 优化版QA拆分队列推入
     */
    public static function pushQA(array $data): void
    {
        self::pushOptimized(self::$QA_JOB, $data);
    }
    
    /**
     * 新增文件处理队列推入
     */
    public static function pushFile(array $data): void
    {
        self::pushOptimized(self::$FILE_JOB, $data);
    }
    
    /**
     * 新增审核队列推入
     */
    public static function pushAudit(array $data): void
    {
        self::pushOptimized(self::$AUDIT_JOB, $data);
    }
    
    /**
     * 批量推入队列任务
     */
    public static function batchPush(string $queueName, array $dataList): bool
    {
        if (empty($dataList)) {
            return true;
        }
        
        $startTime = microtime(true);
        
        try {
            $redis = self::getRedisConnection();
            $queueKey = '{queues:' . $queueName . '}';
            
            // 使用Redis管道批量推入
            $pipe = $redis->multi();
            foreach ($dataList as $data) {
                $jobData = json_encode([
                    'job' => $queueName === self::$EM_JOB ? 'app\queue\EmQueueJob' : 
                            ($queueName === self::$QA_JOB ? 'app\queue\QaQueueJob' : 
                            ($queueName === self::$FILE_JOB ? 'app\queue\FileQueueJob' : 'app\queue\AuditQueueJob')),
                    'data' => $data,
                    'id' => uniqid(),
                    'attempts' => 0,
                    'reserved_at' => null,
                    'available_at' => time(),
                    'created_at' => time()
                ]);
                $pipe->rPush($queueKey, $jobData);
            }
            $pipe->exec();
            
            // 更新统计
            self::$performanceStats['total_pushes'] += count($dataList);
            self::$performanceStats['batch_operations']++;
            
            self::recordPerformance($startTime);
            self::checkMemoryAndGC();
            
            Log::info("批量推入队列成功", [
                'queue' => $queueName,
                'count' => count($dataList),
                'time_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("批量推入队列失败", [
                'queue' => $queueName,
                'count' => count($dataList),
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 批量获取队列任务
     */
    public static function batchPop(string $queueName, int $count = null): array
    {
        $count = $count ?? self::$batchSize;
        $startTime = microtime(true);
        
        try {
            $redis = self::getRedisConnection();
            $queueKey = '{queues:' . $queueName . '}';
            
            $jobs = [];
            for ($i = 0; $i < $count; $i++) {
                $jobData = $redis->lPop($queueKey);
                if ($jobData === false) {
                    break;
                }
                $jobs[] = json_decode($jobData, true);
            }
            
            // 更新统计
            self::$performanceStats['total_pops'] += count($jobs);
            if (count($jobs) > 1) {
                self::$performanceStats['batch_operations']++;
            }
            
            self::recordPerformance($startTime);
            self::checkMemoryAndGC();
            
            return $jobs;
            
        } catch (\Exception $e) {
            Log::error("批量获取队列任务失败", [
                'queue' => $queueName,
                'count' => $count,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * 优化版队列推入
     */
    private static function pushOptimized(string $queueName, array $data): void
    {
        $startTime = microtime(true);
        
        try {
            // 使用连接池获取Redis连接
            $redis = self::getRedisConnection();
            
            // 构建任务数据
            $jobClass = match($queueName) {
                self::$EM_JOB => 'app\queue\EmQueueJob',
                self::$QA_JOB => 'app\queue\QaQueueJob',
                self::$FILE_JOB => 'app\queue\FileQueueJob',
                self::$AUDIT_JOB => 'app\queue\AuditQueueJob',
                default => 'app\queue\BaseQueueJob'
            };
            
            // 使用应用的队列系统
            app('queue')->push($jobClass, $data, $queueName);
            
            // 更新统计
            self::$performanceStats['total_pushes']++;
            self::recordPerformance($startTime);
            self::checkMemoryAndGC();
            
        } catch (\Exception $e) {
            Log::error("队列推入失败", [
                'queue' => $queueName,
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取向量队列长度 (优化版)
     */
    public static function getEmbJobLength(): int
    {
        return self::getQueueLengthOptimized(self::$EM_JOB);
    }
    
    /**
     * 获取QA队列长度 (优化版)
     */
    public static function getQaJobLength(): int
    {
        return self::getQueueLengthOptimized(self::$QA_JOB);
    }
    
    /**
     * 获取文件处理队列长度
     */
    public static function getFileJobLength(): int
    {
        return self::getQueueLengthOptimized(self::$FILE_JOB);
    }
    
    /**
     * 获取审核队列长度
     */
    public static function getAuditJobLength(): int
    {
        return self::getQueueLengthOptimized(self::$AUDIT_JOB);
    }
    
    /**
     * 优化版队列长度查询
     */
    private static function getQueueLengthOptimized(string $queueName): int
    {
        $startTime = microtime(true);
        
        try {
            // 使用缓存减少频繁查询
            $cacheKey = "queue_length:{$queueName}";
            $cached = Cache::get($cacheKey);
            
            if ($cached !== false) {
                return (int)$cached;
            }
            
            $redis = self::getRedisConnection();
            $length = $redis->lLen('{queues:' . $queueName . '}') ?? 0;
            
            // 缓存结果1秒
            Cache::set($cacheKey, $length, 1);
            
            self::recordPerformance($startTime);
            return $length;
            
        } catch (\Exception $e) {
            Log::error("获取队列长度失败", [
                'queue' => $queueName,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * 清空向量队列 (优化版)
     */
    public static function clearEmbQueue(): void
    {
        self::clearQueueOptimized(self::$EM_JOB);
    }
    
    /**
     * 清空QA队列 (优化版)
     */
    public static function clearQaQueue(): void
    {
        self::clearQueueOptimized(self::$QA_JOB);
    }
    
    /**
     * 清空文件处理队列
     */
    public static function clearFileQueue(): void
    {
        self::clearQueueOptimized(self::$FILE_JOB);
    }
    
    /**
     * 清空审核队列
     */
    public static function clearAuditQueue(): void
    {
        self::clearQueueOptimized(self::$AUDIT_JOB);
    }
    
    /**
     * 优化版清空队列
     */
    private static function clearQueueOptimized(string $queueName): void
    {
        $startTime = microtime(true);
        
        try {
            $redis = self::getRedisConnection();
            $redis->del('{queues:' . $queueName . '}');
            
            // 清理相关缓存
            Cache::delete("queue_length:{$queueName}");
            
            self::recordPerformance($startTime);
            
            Log::info("队列清空成功", ['queue' => $queueName]);
            
        } catch (\Exception $e) {
            Log::error("清空队列失败", [
                'queue' => $queueName,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取Redis连接 (连接池管理)
     */
    private static function getRedisConnection()
    {
        $connectionId = 'redis_' . getmypid();
        
        // 检查连接池中是否有可用连接
        if (isset(self::$connectionPool[$connectionId])) {
            $connection = self::$connectionPool[$connectionId];
            
            // 检查连接是否有效
            try {
                $connection->ping();
                self::$performanceStats['connection_reuses']++;
                return $connection;
            } catch (\Exception $e) {
                // 连接无效，移除并重新创建
                unset(self::$connectionPool[$connectionId]);
            }
        }
        
        // 创建新连接
        try {
            $redis = Cache::store('redis')->handler();
            
            // 限制连接池大小
            if (count(self::$connectionPool) >= self::$maxConnections) {
                // 移除最旧的连接
                array_shift(self::$connectionPool);
            }
            
            self::$connectionPool[$connectionId] = $redis;
            return $redis;
            
        } catch (\Exception $e) {
            Log::error("Redis连接失败", ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * 记录性能数据
     */
    private static function recordPerformance(float $startTime): void
    {
        $executionTime = (microtime(true) - $startTime) * 1000;
        self::$performanceStats['total_time'] += $executionTime;
        
        $totalOperations = self::$performanceStats['total_pushes'] + self::$performanceStats['total_pops'];
        if ($totalOperations > 0) {
            self::$performanceStats['average_time'] = self::$performanceStats['total_time'] / $totalOperations;
        }
        
        // 记录内存峰值
        $currentMemory = memory_get_peak_usage(true);
        if ($currentMemory > self::$performanceStats['memory_peak']) {
            self::$performanceStats['memory_peak'] = $currentMemory;
        }
    }
    
    /**
     * 检查内存使用并执行垃圾回收
     */
    private static function checkMemoryAndGC(): void
    {
        self::$operationCount++;
        
        $currentMemory = memory_get_usage(true);
        
        // 内存使用超过限制或操作次数达到阈值时执行GC
        if ($currentMemory > self::$memoryLimit || self::$operationCount >= self::$gcThreshold) {
            gc_collect_cycles();
            self::$operationCount = 0;
            
            $afterMemory = memory_get_usage(true);
            $freed = $currentMemory - $afterMemory;
            
            if ($freed > 1024 * 1024) { // 释放超过1MB时记录
                Log::info("队列内存回收", [
                    'before_mb' => round($currentMemory / 1024 / 1024, 2),
                    'after_mb' => round($afterMemory / 1024 / 1024, 2),
                    'freed_mb' => round($freed / 1024 / 1024, 2)
                ]);
            }
        }
    }
    
    /**
     * 获取队列状态信息
     */
    public static function getQueueStatus(): array
    {
        return [
            'queues' => [
                'em_job' => self::getEmbJobLength(),
                'qa_job' => self::getQaJobLength(),
                'file_job' => self::getFileJobLength(),
                'audit_job' => self::getAuditJobLength()
            ],
            'connections' => [
                'pool_size' => count(self::$connectionPool),
                'max_connections' => self::$maxConnections,
                'reuse_count' => self::$performanceStats['connection_reuses']
            ],
            'performance' => self::getPerformanceStats(),
            'memory' => [
                'current_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
                'peak_mb' => round(self::$performanceStats['memory_peak'] / 1024 / 1024, 2),
                'limit_mb' => round(self::$memoryLimit / 1024 / 1024, 2)
            ]
        ];
    }
    
    /**
     * 获取性能统计数据
     */
    public static function getPerformanceStats(): array
    {
        return [
            'total_pushes' => self::$performanceStats['total_pushes'],
            'total_pops' => self::$performanceStats['total_pops'],
            'batch_operations' => self::$performanceStats['batch_operations'],
            'connection_reuses' => self::$performanceStats['connection_reuses'],
            'average_time_ms' => round(self::$performanceStats['average_time'], 2),
            'memory_peak_mb' => round(self::$performanceStats['memory_peak'] / 1024 / 1024, 2)
        ];
    }
    
    /**
     * 重置性能统计
     */
    public static function resetPerformanceStats(): void
    {
        self::$performanceStats = [
            'total_pushes' => 0,
            'total_pops' => 0,
            'batch_operations' => 0,
            'connection_reuses' => 0,
            'total_time' => 0,
            'average_time' => 0,
            'memory_peak' => 0
        ];
        
        Log::info('队列性能统计已重置');
    }
    
    /**
     * 队列预热
     */
    public static function warmupQueues(): void
    {
        Log::info('开始队列预热');
        
        try {
            // 预创建Redis连接
            self::getRedisConnection();
            
            // 预加载队列长度到缓存
            $queues = [self::$EM_JOB, self::$QA_JOB, self::$FILE_JOB, self::$AUDIT_JOB];
            foreach ($queues as $queue) {
                self::getQueueLengthOptimized($queue);
            }
            
            Log::info('队列预热完成', [
                'connections' => count(self::$connectionPool),
                'queues_warmed' => count($queues)
            ]);
            
        } catch (\Exception $e) {
            Log::error('队列预热失败', ['error' => $e->getMessage()]);
        }
    }
} 