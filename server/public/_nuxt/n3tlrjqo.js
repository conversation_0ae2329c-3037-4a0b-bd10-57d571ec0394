import{_ as u}from"./CN8DIg3d.js";import{E as d}from"./Dkrb28GR.js";import{E as x}from"./DfSuJ7Ej.js";import{b as y,_ as h}from"./B1MekKW7.js";/* empty css        */import k from"./D8eSb8Nc.js";import v from"./DnEAXNab.js";import{e as w}from"./CyaqLv7U.js";import{l as E,j as R,M as m,N as p,Z as t,a0 as r,u as i,O as n}from"./Dp9aCaJ6.js";import"./CPkX1WPy.js";import"./wW1KxDBP.js";import"./t2WiU-SJ.js";import"./uE1Ww79i.js";import"./DlAUqK2U.js";import"./Df0xfART.js";import"./DCTLXrZ8.js";import"./D2wP0d1N.js";import"./C1GDMjBs.js";import"./DeQZaZZG.js";import"./9Bti1uB6.js";import"./DDaEm-_F.js";import"./CG7WIFG0.js";import"./l0sNRNKZ.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./QLtE1ufq.js";import"./CcPlX2kz.js";import"./Bu_nKEGp.js";import"./CEphZ9XW.js";import"./BB8haoms.js";import"./VzZmvtWk.js";import"./DF2d2iYd.js";import"./vclUXml2.js";import"./DlKZEFPo.js";import"./Bwx2-5pK.js";import"./GQTyu-ru.js";import"./Cv6HhfEG.js";import"./DmvaW7ga.js";import"./BMW57zJa.js";import"./AOe4nXt1.js";/* empty css        *//* empty css        */import"./DjwCd26w.js";import"./97m9rtmw.js";import"./IgeL0vc_.js";/* empty css        */import"./DtTKtiSt.js";const g={key:0,class:"h-full p-[16px] flex"},N={class:"flex-1 min-w-0 h-full pl-[16px]"},B={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Bt=E({__name:"index",setup(j){const l=y(),e=R();return(C,o)=>{const a=u,_=d,c=x,f=h;return m(),p("div",null,[t(f,{name:"default"},{default:r(()=>[i(l).config.switch.music_status?(m(),p("div",g,[t(k,{onUpdate:o[0]||(o[0]=I=>{var s;return(s=i(e))==null?void 0:s.refresh()})}),n("div",N,[t(a,null,{default:r(()=>[t(v,{ref_key:"recordRef",ref:e},null,512)]),_:1})])])):(m(),p("div",B,[t(c,null,{icon:r(()=>[t(_,{class:"w-[150px] dark:opacity-60",src:i(w)},null,8,["src"])]),title:r(()=>o[1]||(o[1]=[n("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Bt as default};
