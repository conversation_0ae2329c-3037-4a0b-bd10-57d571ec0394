import{E as c}from"./DTIryUWu.js";import{E as g}from"./CBkeCdOF.js";import{E as x}from"./D9I8-w1V.js";import{b as y,v as D}from"./Br7V4jS9.js";/* empty css        *//* empty css        */import{r as V,x as E,y as w,f as e,e as v}from"./C9pl8Zxy.js";import L from"./DjZf-7g6.js";import b from"./B2Dn2RdI.js";import{_ as k}from"./Dz2L06Vd.js";import S from"./Ch9PtMrX.js";import A from"./BZgIgA30.js";import P from"./B1HOaPHs.js";import{D as z}from"./BbrLZXHg.js";import{DrawModeEnum as i}from"./tONJIxwY.js";import{l as U,F as B,u as o,M as p,N as a,Z as r,a0 as l,O as s,aa as M}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./B4zJW-6l.js";import"./B9mz6c9C.js";import"./CCBJtVSv.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./CQG-tpkc.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./WAceTjf6.js";import"./dsHDlwYU.js";import"./CXUUfEOz.js";import"./9Bti1uB6.js";/* empty css        */import"./VFxRtqRv.js";import"./Cs-EFu39.js";/* empty css        */import"./BqjG40R6.js";import"./DgYHmeWQ.js";import"./C9kcCN0o.js";import"./1QakEyj_.js";import"./Dg3tPGYu.js";import"./Cpg3PDWZ.js";import"./Cgao8iUq.js";import"./BKFgJjkk.js";import"./Cu5sLRaN.js";import"./Cv6HhfEG.js";import"./C5S6gXlf.js";import"./BXXFZfct.js";import"./LFZ9apcG.js";/* empty css        *//* empty css        */import"./1sgdir_0.js";import"./BhyLIuCu.js";import"./HqXe62RH.js";import"./DjwCd26w.js";import"./DDCcsBP0.js";import"./CcPlX2kz.js";import"./6hAOkTHE.js";import"./BVqQ2B7o.js";import"./BWdDF8rn.js";import"./X0nPH4HO.js";const q={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},C={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},F={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},I=U({__name:"dalle",setup(O){const n=y();return B(()=>{V({draw_api:i.DALLE3,draw_model:i.DALLE3,action:"generate",prompt:"",negative_prompt:"",size:"1024x1024"}),E.model=i.DALLE3,w()}),(R,t)=>{const d=c,u=g,f=x,_=D;return o(n).config.switch.dalle3_status?(p(),a("div",q,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:l(()=>[s("div",C,[r(L,{modelValue:o(e).prompt,"onUpdate:modelValue":t[0]||(t[0]=m=>o(e).prompt=m),model:o(i).DALLE3},null,8,["modelValue","model"]),r(S,{modelValue:o(e).size,"onUpdate:modelValue":t[1]||(t[1]=m=>o(e).size=m)},null,8,["modelValue"]),r(A,{modelValue:o(e).style,"onUpdate:modelValue":t[2]||(t[2]=m=>o(e).style=m)},null,8,["modelValue"]),r(P,{modelValue:o(e).quality,"onUpdate:modelValue":t[3]||(t[3]=m=>o(e).quality=m)},null,8,["modelValue"])]),r(k)]),_:1}),M(r(b,{"element-loading-text":"正在加载数据..."},null,512),[[_,o(v)]])])):(p(),a("div",F,[r(f,null,{icon:l(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(z)},null,8,["src"])]),title:l(()=>t[4]||(t[4]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),Ho=N(I,[["__scopeId","data-v-9879be3f"]]);export{Ho as default};
