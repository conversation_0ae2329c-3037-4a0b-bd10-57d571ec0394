import{f as F,E as R,v as L}from"./DNaNbs6R.js";import{E as U,a as z}from"./CUCgy8P_.js";import{E as A,a as M}from"./CSbJ_DKr.js";import{_ as Z}from"./CJcXdMmM.js";import{_ as G}from"./DaE5R0H9.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import{u as H}from"./67xbGseh.js";import{_ as J}from"./B1Lzljws.js";import{_ as K}from"./DucnOpAE.js";import{o as Q,q as W,s as X}from"./Bo3PTL3c.js";import{l as Y,C as ee,r as te,j as E,b as oe,c as ae,M as d,N as v,O as i,Z as t,a0 as a,a6 as m,u as l,aa as ne,a1 as le,a7 as $,y as se}from"./Dp9aCaJ6.js";const ie={class:"p-main h-full flex flex-col"},re={class:"flex justify-between"},pe={class:"flex"},ce={class:"flex-1 min-h-0"},de={class:"flex items-center"},me=["onClick"],fe=["onClick"],_e={key:1},ue={class:"flex justify-end mt-4"},je=Y({__name:"record",props:{appId:{}},setup(V){const C=V,{appId:g}=ee(C),f=te({robot_id:g,is_feedback:-1}),_=E(),u=E(),y=n=>{_.value.open(n,"查看回复")},B=n=>{_.value.open(n,"反馈内容")},{pager:p,getLists:c,resetPage:D}=H({fetchFun:Q,params:f}),b=oe([]),S=n=>{b.value=n.map(e=>e.id)},w=async n=>{await F.confirm("确定要删除？"),await W({ids:n,robot_id:g.value}),c()},P=async n=>{var e;await X(n),(e=u.value)==null||e.close()};return c(),ae(()=>C.appId,()=>{D()}),(n,e)=>{const j=R,k=U,I=z,s=A,N=Z,h=R,T=M,O=G,q=L;return d(),v("div",ie,[i("div",re,[t(j,{disabled:l(b).length<=0,class:"!mb-4",onClick:e[0]||(e[0]=o=>w(l(b)))},{default:a(()=>e[3]||(e[3]=[m(" 批量删除 ")])),_:1},8,["disabled"]),i("div",pe,[e[4]||(e[4]=i("div",{class:"pt-[6px] px-[10px]"},"问题反馈",-1)),t(I,{modelValue:l(f).is_feedback,"onUpdate:modelValue":e[1]||(e[1]=o=>l(f).is_feedback=o),style:{width:"240px"},onChange:l(c)},{default:a(()=>[t(k,{label:"全部",value:-1}),t(k,{label:"未反馈",value:0}),t(k,{label:"已反馈",value:1})]),_:1},8,["modelValue","onChange"])])]),i("div",ce,[ne((d(),le(T,{height:"100%",size:"large",data:l(p).lists,onSelectionChange:S},{default:a(()=>[t(s,{type:"selection",width:"55"}),t(s,{label:"ID",prop:"id","min-width":"80"}),t(s,{label:"用户信息","min-width":"180"},{default:a(({row:o})=>{var r;return[i("div",de,[i("span",null,$((r=o.user)==null?void 0:r.nickname),1)])]}),_:1}),t(s,{label:"提问",prop:"ask","min-width":"300"},{default:a(({row:o})=>[t(N,{content:o.ask,line:2,teleported:!0,effect:"light"},null,8,["content"])]),_:1}),t(s,{label:"回答",prop:"answer","min-width":"300"},{default:a(({row:o})=>[i("span",{class:"line-clamp-2 cursor-pointer",onClick:r=>y(o.reply)},$(o.reply),9,me)]),_:1}),t(s,{label:"用户内容",prop:"feedback","min-width":"100"},{default:a(({row:o})=>[o.feedback?(d(),v("span",{key:0,class:"line-clamp-2 cursor-pointer text-primary",onClick:r=>B(o.feedback)},"反馈内容",8,fe)):(d(),v("span",_e,"-"))]),_:1}),t(s,{label:"提问时间",prop:"create_time","min-width":"180","show-tooltip-when-overflow":""}),t(s,{label:"操作",width:"200",fixed:"right"},{default:a(({row:o})=>[t(h,{type:"primary",link:"",onClick:r=>{var x;return(x=l(u))==null?void 0:x.open(o)}},{default:a(()=>e[5]||(e[5]=[m(" 修正 ")])),_:2},1032,["onClick"]),t(h,{type:"primary",link:"",onClick:r=>y(o.reply)},{default:a(()=>e[6]||(e[6]=[m(" 查看回复 ")])),_:2},1032,["onClick"]),t(h,{type:"danger",link:"",onClick:r=>w([o.id])},{default:a(()=>e[7]||(e[7]=[m(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[q,l(p).loading]])]),i("div",ue,[t(O,{modelValue:l(p),"onUpdate:modelValue":e[2]||(e[2]=o=>se(p)?p.value=o:null),onChange:l(c)},null,8,["modelValue","onChange"])]),t(J,{ref_key:"popRef",ref:_},null,512),t(K,{ref_key:"correctRef",ref:u,onConfirm:P},null,512)])}}});export{je as _};
