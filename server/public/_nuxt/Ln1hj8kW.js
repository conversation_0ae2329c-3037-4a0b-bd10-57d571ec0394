import{E as x,a as g}from"./C3WqRr9V.js";import{a as k}from"./DyU4wb-Q.js";import{b as v}from"./BNnETjxs.js";import y from"./J5-8TFr_.js";import{l as w,r as B,j as U,b as V,M as o,N as i,O as D,Z as E,a0 as s,_ as S,aq as h,u as a,a1 as n,a3 as C}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./hdrcCGKH.js";import"./DUH6hp3a.js";import"./BolOpO9r.js";import"./DCa8BdSG.js";import"./DCTLXrZ8.js";import"./C27mmGOG.js";import"./BDGxxzzg.js";import"./9Bti1uB6.js";import"./BpzzwpsP.js";import"./C3h7pD7s.js";import"./9MAnF5ll.js";import"./D4s3zOA5.js";import"./CH_T-XC0.js";import"./CQqsfOf-.js";import"./D-tOg06u.js";import"./WW2eh4dw.js";import"./Dt_kzei6.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DXx1A7ie.js";import"./DagqXfuI.js";/* empty css        */import"./CmLVN9Ir.js";import"./BRo9nl0A.js";import"./BVx1xlji.js";import"./0qQUtt94.js";import"./C-srSIka.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */const q={class:"p-main flex h-full flex-col"},R=w({__name:"setUp",emits:["update"],setup(T,{emit:l}){const c=l,d=k(),r=B({current:"baseSetting",lists:[{type:"baseSetting",name:"基础信息",component:U(y)}]}),m=V({}),p=async()=>{m.value=await v({id:d.query.id})},u=()=>{p(),c("update")};return p(),(j,e)=>{const _=g,f=x;return o(),i("div",q,[e[1]||(e[1]=D("div",{class:"text-xl font-medium"},"知识库设置",-1)),E(f,{class:"flex-1 min-h-0",modelValue:a(r).current,"onUpdate:modelValue":e[0]||(e[0]=t=>a(r).current=t)},{default:s(()=>[(o(!0),i(S,null,h(a(r).lists,(t,b)=>(o(),n(_,{label:t.name,name:t.type,key:b},{default:s(()=>[(o(),n(C(t.component),{data:a(m),onUpdate:u},null,40,["data"]))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])}}}),yt=N(R,[["__scopeId","data-v-4da5484e"]]);export{yt as default};
