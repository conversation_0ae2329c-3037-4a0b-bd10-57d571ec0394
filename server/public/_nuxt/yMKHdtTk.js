import{E as S}from"./oNDQuFBQ.js";import{E as P}from"./dYUcznge.js";import{j as R,b as $,f as j,E as N}from"./CylNgAGi.js";import{P as V}from"./D-bImviZ.js";/* empty css        *//* empty css        */import{u as q}from"./Bi0_2T0f.js";import{d as z}from"./iRXjSIie.js";import{Q as L}from"./Bg2e09vA.js";import{l as T,j as f,b as x,m as v,M as D,N as Q,Z as s,a0 as r,O as e,u as o,a6 as h,a7 as l}from"./Dp9aCaJ6.js";import{_ as A}from"./DlAUqK2U.js";import"./Za7Ic34B.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */const H={class:"share-pop"},M={class:"flex flex-col"},O={class:"flex-1 min-h-0"},U={class:"flex justify-center bg-[#F2F3F6] rounded-t-[10px] overflow-hidden"},W=["src"],Z={class:"px-[16px] mt-[12px]"},G={class:"title text-[16px] font-medium text-[#101010] line-clamp-2"},J={class:"px-[15px]"},K={class:"flex items-center px-[16px] pt-[0px] pb-[30px]"},X={class:"flex items-center"},Y=["src"],ee={class:"ml-2"},oe={class:"mt-[16px] font-medium text-[#101010] text-xl"},te={class:"mt-[10px] text-primary"},se={class:"ml-auto h-[80px] p-[5px] bg-white rounded-[5px]"},ne={class:"flex px-[10px]"},re=T({__name:"posterPop",setup(ae,{expose:g}){const d=f(),m=f(),a=R(),{getWebsiteConfig:b,config:i}=$(),n=x({}),y=p=>{d.value.open(),n.value=p},k=v(()=>`${i.current_domain}/mobile/packages/pages/robot_square/robot_square?type=2?user_sn=${a.userInfo.sn}`),{copy:w}=q(),E=v(()=>`${i.current_domain}/robot_square?type=1&user_sn=${a.userInfo.sn}`),_=x(!1),B=async()=>{try{_.value=!0,await z(m.value)}catch{j.msgError("下载失败，请重试")}finally{_.value=!1}};return g({open:y}),(p,t)=>{const C=S,F=P,u=N,I=V;return D(),Q("div",H,[s(I,{ref_key:"popRef",ref:d,width:"auto",title:"生成海报","append-to-body":!1,"click-modal-close":!0,confirmButtonText:"",cancelButtonText:""},{footer:r(()=>[e("div",ne,[s(u,{class:"flex-1",text:"",bg:"",plain:"",size:"large",onClick:t[0]||(t[0]=c=>o(w)(o(E)))},{default:r(()=>t[1]||(t[1]=[h(" 复制链接 ")])),_:1}),s(u,{class:"flex-1",type:"primary",size:"large",onClick:B},{default:r(()=>t[2]||(t[2]=[h(" 下载海报 ")])),_:1})])]),default:r(()=>[e("div",M,[e("div",O,[s(F,null,{default:r(()=>{var c;return[e("div",{ref_key:"posterRef",ref:m,class:"w-[430px] bg-[#F8F8FB] rounded-[10px]"},[e("div",U,[e("img",{class:"w-full object-contain",src:o(n).thumbnail||o(n).image,alt:""},null,8,W)]),e("div",Z,[e("div",G,l(((c=o(n))==null?void 0:c.prompts_cn)||o(n).original_prompts.prompt),1)]),e("div",J,[s(C,{"border-style":"solid"})]),e("div",K,[e("div",null,[e("div",X,[e("img",{class:"w-[45px] h-[45px] rounded-full",src:o(a).userInfo.avatar},null,8,Y),e("div",ee,l(o(a).userInfo.nickname),1)]),e("div",oe,l(o(b).pc_title),1),e("div",te,l(o(i).current_domain),1)]),e("div",se,[s(L,{value:o(k),size:80,margin:1},null,8,["value"])])])],512)]}),_:1})])])]),_:1},512)])}}}),ke=A(re,[["__scopeId","data-v-f25850a2"]]);export{ke as default};
