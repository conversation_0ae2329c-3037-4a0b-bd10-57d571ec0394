import{E as V}from"./Bwa6YuoW.js";import{_ as B}from"./BWJbVfKg.js";import{_ as D}from"./Bj9H9xtu.js";import{E as L}from"./Dxaq3UbQ.js";import{_ as T}from"./p4SUCGLV.js";import{_ as N}from"./CN8DIg3d.js";import{a as M,l as O,b as P}from"./BBthjZaB.js";/* empty css        *//* empty css        *//* empty css        */import{u as U}from"./BzHpYkC6.js";import z from"./CWVahwz4.js";import{_ as F}from"./Da6Fv53w.js";import{_ as J}from"./pemHTQmi.js";import{g as Z}from"./Bo3PTL3c.js";import{u as j}from"./U_0JrR5o.js";import{l as G,b as d,ak as H,c as K,M as a,N as f,Z as m,a0 as s,O as e,u as o,y as w,_ as Q,aq as W,a1 as h,a4 as y,a7 as X}from"./Dp9aCaJ6.js";import{_ as Y}from"./DlAUqK2U.js";import"./vwcCUF2-.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";import"./C3aKVaZD.js";import"./9Bti1uB6.js";import"./B8jTR_IR.js";import"./CEhNSbAj.js";import"./BeV_2vTx.js";/* empty css        */import"./Dmdd9l2n.js";import"./Cv6HhfEG.js";import"./BZZdiSEe.js";/* empty css        */import"./CNnkmNvM.js";import"./l0U8_5Lf.js";import"./D0csLVr8.js";import"./DNtYbVbo.js";import"./hxgYBnNa.js";import"./DO7Ufrqu.js";import"./DJi2sD98.js";import"./6TYmzuF5.js";import"./DiPtOrlW.js";import"./Bw4533_S.js";import"./DSvHt5sr.js";import"./bBYYqU90.js";import"./DQFSO0Sy.js";import"./DlKZEFPo.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./BNnETjxs.js";import"./TEHDjiqz.js";import"./BQ3f__3e.js";import"./CxNhmRDt.js";import"./Gdh9lYbA.js";import"./ND-j-PcX.js";/* empty css        */import"./CTRAaZiO.js";import"./Du5U7mLs.js";import"./BM3RG-Py.js";import"./C-xknDPi.js";import"./BikmaNnr.js";import"./EdzuQeTd.js";/* empty css        *//* empty css        */import"./Bc7PmBal.js";import"./CqA78EX3.js";import"./DOz3g2Dn.js";import"./D7EZVpG5.js";import"./C-hk8zs7.js";import"./JOeTFF2G.js";import"./DBBnJi1E.js";import"./B4Y9LdPA.js";/* empty css        */import"./C6_W4ts7.js";import"./6QKgWKWo.js";import"./CFb5H7Rv.js";import"./HjBqiulG.js";import"./CRHv1E5O.js";import"./BlCn8106.js";import"./C98sO_nA.js";/* empty css        *//* empty css        */import"./ChrTBnYp.js";import"./67xbGseh.js";import"./CMwgH_RS.js";import"./BZk0F2e5.js";import"./DqnO1mED.js";import"./PBk_9JBI.js";import"./CcPlX2kz.js";import"./Dp1MVQvw.js";import"./Bc-QJOSl.js";import"./BFXl1OtE.js";import"./CudszxeR.js";import"./CpdZDTXi.js";import"./DPDd7wth.js";import"./HuHZjP7V.js";import"./BbR66-Fp.js";import"./CT4myWQ1.js";import"./DUs7AF4Y.js";import"./1eiY8EyW.js";import"./MMIm7DkW.js";import"./BRYfqKvJ.js";import"./CSM-R2bE.js";import"./DMQWfDSH.js";import"./C-5rXrue.js";import"./BKq__9dH.js";import"./BuBpo4Nc.js";import"./Cpg3PDWZ.js";const tt={class:"h-full flex"},ot=["onClick"],rt=["src"],et={class:"ml-[8px] line-clamp-1"},it={class:"flex items-center cursor-pointer"},pt={class:"text-xl flex-1 min-w-0"},mt={class:"sm:h-full py-[16px] pr-[16px] flex flex-col sm:flex-row flex-1 min-w-0"},nt={class:"sm:h-full flex-1 min-w-0 min-h-0 bg-body rounded-[12px]"},at=G({__name:"setting",async setup(st){let c,x;const i=M(),u=O();P();const v=j();v.getRobot();const _=d(!1),l=d(i.query.id),{data:b,refresh:k}=([c,x]=H(()=>U(()=>Z({id:l.value}),{transform(t){return(t==null?void 0:t.category_id)===0&&(t.category_id=""),t},default(){return{}},lazy:!0},"$3nIwi7TB6J")),c=await c,x(),c),p=d("edit"),q=[{name:"智能体设置",icon:"el-icon-Setting",key:"edit"},{name:"发布智能体",key:"release",icon:"el-icon-Position"},{name:"对话数据",key:"dialogue",icon:"el-icon-ChatDotRound"},{name:"立即对话",key:"chat",icon:"el-icon-ChatLineRound"}],S=t=>{switch(t){case"chat":u.push({path:"/application/chat",query:{id:l.value}});break;default:u.replace({path:i.path,query:{...i.query,currentTab:t}})}},C=async t=>{_.value=!1,t!=i.query.id&&(l.value=t,await k(),u.replace({path:i.path,query:{...i.query,id:t}}))};return K(()=>i.query,t=>{p.value=t.currentTab||"edit"},{immediate:!0}),(t,n)=>{const g=V,R=B,A=D,E=L,$=T,I=N;return a(),f("div",tt,[m($,{modelValue:o(p),"onUpdate:modelValue":[n[1]||(n[1]=r=>w(p)?p.value=r:null),S],"menu-list":q,"back-path":"/application/layout/robot"},{title:s(()=>[e("div",null,[m(E,{placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:o(_),"onUpdate:visible":n[0]||(n[0]=r=>w(_)?_.value=r:null)},{reference:s(()=>[e("div",it,[e("div",pt,[m(R,{content:o(b).name,teleported:!0,effect:"light"},null,8,["content"])]),m(A,{name:"el-icon-ArrowDown"})])]),default:s(()=>[e("div",null,[m(g,{style:{height:"250px"}},{default:s(()=>[(a(!0),f(Q,null,W(o(v).robotLists,r=>(a(),f("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-primary-light-9 px-[10px] my-[5px] rounded-[12px] hover:text-primary",key:r.id,onClick:lt=>C(r.id)},[e("img",{class:"rounded-[50%] w-[28px] h-[28px] flex-none",src:r.image,alt:""},null,8,rt),e("div",et,X(r.name),1)],8,ot))),128))]),_:1})])]),_:1},8,["visible"])])]),_:1},8,["modelValue"]),e("div",mt,[e("div",nt,[o(p)==="edit"?(a(),h(z,{key:0,"model-value":o(b),onSuccess:n[2]||(n[2]=r=>o(u).push("/application/layout/robot"))},null,8,["model-value"])):y("",!0),m(I,null,{default:s(()=>[o(p)==="release"?(a(),h(g,{key:0},{default:s(()=>[m(F,{"app-id":o(l)},null,8,["app-id"])]),_:1})):y("",!0)]),_:1}),o(p)==="dialogue"?(a(),h(J,{key:1,"app-id":o(l)},null,8,["app-id"])):y("",!0)])])])}}}),ur=Y(at,[["__scopeId","data-v-21d9b61a"]]);export{ur as default};
