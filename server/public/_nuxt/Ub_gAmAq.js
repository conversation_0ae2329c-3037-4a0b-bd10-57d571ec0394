import{E as s}from"./DlvGt6PY.js";import{_ as u}from"./BUuWSjTP.js";import"./BsMqt_su.js";/* empty css        */import{l as m,m as d,M as e,N as y,O as f,a1 as n,a2 as p,u as g}from"./Dp9aCaJ6.js";const h={style:{position:"absolute",inset:"0px","user-select":"none"}},b=m({__name:"index",props:{preview:{type:Boolean,default:!0},ratio:{type:Array,default:()=>[1,1]},alt:{type:String,default:""},type:{type:String,default:"image"},src:{type:String,default:""},thumbnail:{type:String,default:""},lazy:{type:Boolean,default:!0},fit:{type:String,default:"contain"}},setup(t){const l=t,o=d(()=>{const[a,i]=l.ratio;return`width: 100%;padding-bottom: ${i/a*100}%;`});return(a,i)=>{const r=s,c=u;return e(),y("div",{style:p([{position:"relative"},g(o)])},[f("div",h,[t.type==="image"?(e(),n(r,{key:0,class:"!block h-[100%]",src:t.thumbnail||t.src,alt:t.alt||t.src,lazy:t.lazy,fit:t.fit,"preview-src-list":t.preview?[t.src]:[]},null,8,["src","alt","lazy","fit","preview-src-list"])):(e(),n(c,{key:1,src:t.src,width:"100%",height:"100%",controlBtns:["audioTrack","quality","volume","fullScreen"]},null,8,["src"]))])],4)}}});export{b as _};
