import{l as w,b as c,c as $,F as b,M as a,N as l,O as s,_ as j,aq as E,a7 as _,k as S,w as B,Z as h,a4 as v,a0 as y,u as x,a2 as L}from"./Dp9aCaJ6.js";import{_ as k}from"./DlAUqK2U.js";import{b as z,E as A}from"./DyU4wb-Q.js";import{_ as N}from"./BrOxmQbV.js";import{_ as O}from"./Cv1u9LLW.js";const q={class:"typer inline-block"},C={class:"typer-content inline-block"},M={class:"typer-dynamic"},V={class:"cut"},F=w({__name:"index",props:{color:{type:String,default:"#000000"},textTips:{type:[Array,Object],default:()=>[]}},setup(g){const p=g,r=c([]),i=c("AI智能聊天系统、AI绘画、大模型知识库训练开发"),e=c([]),o=c(0);$(()=>p.textTips,t=>{console.log("textTips"),i.value=t},{immediate:!0}),b(()=>{u()});const u=()=>{e.value=i.value.split("");for(let t=0;t<e.value.length;t++)setTimeout(f(t),t*200)},m=()=>{const t=e.value.length;for(let n=0;n<t;n++)setTimeout(d(n),n*100)},f=t=>()=>{const n=e.value.length;r.value.push(e.value[t]),t==n-1&&setTimeout(m,1500)},d=t=>()=>{r.value.pop(e.value[t]),r.value.length==0&&(o.value>=p.textTips.length-1?o.value=0:o.value++,setTimeout(u,500))};return(t,n)=>(a(),l("div",q,[s("div",C,[s("p",M,[s("span",V,[(a(!0),l(j,null,E(r.value,(T,I)=>(a(),l("span",{class:"word",key:I},_(T),1))),128)),n[0]||(n[0]=s("span",{class:"typer-cursor"},null,-1))])])])]))}}),R=k(F,[["__scopeId","data-v-fd65b48f"]]),U={class:"max-w-[1200px] mx-auto h-full flex flex-col justify-center"},D={class:"flex justify-between items-center"},H={class:"flex flex-col items-stretch justify-center h-full sm:py-[80px] py-[30px] mx-[20px]"},P={key:0,class:"font-medium sm:text-[45px] text-[30px] text-left"},W={class:"hidden"},Z={key:1,class:"max-w-[610px] text-left text-lg sm:my-[40px] my-[20px]"},G={key:0},J={class:"ml-4"},K={class:"flex-none"},Q=["src"],X=w({__name:"title",props:{prop:{}},setup(g){const{getImageUrl:p}=z(),r=c(510),i=()=>{const o=window.innerWidth/3;o<500||(r.value=o)};return b(()=>{window==null||window.addEventListener("resize",i)}),S(()=>{window==null||window.removeEventListener("resize",i)}),B(i),(e,o)=>{var d,t;const u=R,m=A,f=N;return a(),l("div",{class:"bg-center home-title mb-[30px]",style:L({height:`${x(r)}px`,paddingTop:"var(--header-height)",backgroundImage:`url(${x(p)(e.prop.bgImage)})`})},[s("div",U,[s("div",D,[s("div",H,[e.prop.title?(a(),l("h1",P,[h(u,{textTips:e.prop.title},null,8,["textTips"])])):v("",!0),s("h1",W,_(e.prop.title),1),e.prop.desc?(a(),l("p",Z,_(e.prop.desc),1)):v("",!0),s("div",null,[e.prop.isShowBtn?(a(),l("div",G,[h(f,{to:{path:(d=e.prop.link)==null?void 0:d.path,query:(t=e.prop.link)==null?void 0:t.query}},{default:y(()=>[h(m,{type:"primary",class:"enter-btn hover-to-right",size:"large"},{default:y(()=>[o[0]||(o[0]=s("div",{class:"flex justify-center items-center w-[50px] h-[50px] rounded-full bg-white"},[s("img",{src:O,class:"w-[24px] h-[24px] round-btn",alt:""})],-1)),s("span",J,_(e.prop.btnText),1)]),_:1})]),_:1},8,["to"])])):v("",!0)])]),s("div",K,[s("img",{src:x(p)(e.prop.rightImage),class:"w-[600px]",alt:""},null,8,Q)])])])],4)}}}),Y=k(X,[["__scopeId","data-v-ff8a63d3"]]),re=Object.freeze(Object.defineProperty({__proto__:null,default:Y},Symbol.toStringTag,{value:"Module"}));export{re as _};
