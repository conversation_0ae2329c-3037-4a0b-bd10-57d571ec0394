import{cn as f,o as v}from"./B5S_Er7H.js";import"./DP2rzg_V.js";import{l as V,r as h,M as l,a1 as w,a0 as c,O as s,N as o,aq as z,u as r,_ as k,X as i,a7 as b}from"./Dp9aCaJ6.js";import{_ as y}from"./DlAUqK2U.js";const C={class:"flex-1 min-w-0 overflow-hidden"},B={class:"flex flex-wrap mx-[-6px] mb-[-10px]"},S=["onClick"],g={class:"flex justify-center items-center h-[20px]"},q={class:"text-base text-[#101010] dark:text-white mt-[4px] size-scale"},E=V({__name:"video-size",props:{modelValue:{default:"1:1"}},emits:["update:modelValue"],setup(p,{emit:n}){const u=n,d=p,{modelValue:a}=f(d,u),x=h({lists:[{scaleValue:"1:1",class:"w-[20px] h-[20px]"},{scaleValue:"3:4",class:"w-[15px] h-[20px]"},{scaleValue:"4:3",class:"w-[20px] h-[15px]"},{scaleValue:"9:16",class:"w-[13px] h-[20px]"},{scaleValue:"16:9",class:"w-[20px] h-[12px]"}]});return a.value="1:1",(F,t)=>{const _=v;return l(),w(_,{prop:"scale",required:""},{label:c(()=>t[0]||(t[0]=[s("span",{class:"font-bold text-tx-primary"}," 生成尺寸 ",-1)])),default:c(()=>[s("div",C,[s("div",B,[(l(!0),o(k,null,z(r(x).lists,(e,m)=>(l(),o("div",{key:m,class:"w-[33.3%] px-[6px]"},[s("div",{class:i(["picture-size cursor-pointer text-center hover:text-primary",{"picture-size-active":r(a)==(e==null?void 0:e.scaleValue),"picture-size-disable":!(e!=null&&e.scaleValue)}]),onClick:I=>a.value=e.scaleValue},[s("div",g,[s("div",{class:i(["rect",e.class])},null,2)]),s("div",q,b(e.scaleValue),1)],10,S)]))),128))])])]),_:1})}}}),L=y(E,[["__scopeId","data-v-ea183e0a"]]);export{L as default};
