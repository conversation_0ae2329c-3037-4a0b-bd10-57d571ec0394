import{_ as O}from"./uE1Ww79i.js";import{E as P}from"./DTtBEUup.js";import{E as Q}from"./Df0xfART.js";import{h as X,f as Z,e as G,E as H,v as J}from"./B1MekKW7.js";import{E as K}from"./DeQZaZZG.js";import"./CIrs9wkW.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{isSameFile as W}from"./BiHhwkbt.js";import{r as I,a as Y,b as ee}from"./Bz0w0rwk.js";import{s as te}from"./C4qLKnCc.js";import{_ as ae}from"./D4Ojqqen.js";import{l as oe,b as f,j as se,M as d,N as p,aa as le,u as s,Z as l,a0 as x,O as a,a6 as D,a7 as k,_ as L,aq as $,y as ne,a4 as ie,X as ce}from"./Dp9aCaJ6.js";import{_ as re}from"./DlAUqK2U.js";import"./DVLEc6gw.js";import"./DmvaW7ga.js";import"./DCTLXrZ8.js";import"./DjadXojw.js";import"./Cpg3PDWZ.js";const de={class:"h-full flex flex-col min-h-0"},pe={class:"py-[16px]"},me={class:"el-upload__text"},ue={class:"el-upload__text"},_e={key:0,class:"grid grid-cols-2 gap-4 flex-1 min-h-[500px]"},fe={style:{"border-right":"1px solid #eeeeee"}},ve={class:"mt-4 max-w-[500px]"},he=["onClick"],xe={class:"ml-2"},ge={class:"closeIcon ml-auto opacity-0 transition duration-300 flex items-center"},we={class:"mt-4"},ke={class:"flex"},ye={class:"mt-2"},be={class:"flex flex-col"},Ce={class:"text-lg"},Ee={class:"flex-auto mt-2 h-[100px]"},Ve=oe({__name:"doc",props:{modelValue:{}},emits:["update:modelValue"],setup(N,{expose:S,emit:U}){const n=X(N,"modelValue",U),y=[".txt",".docx",".pdf",".md"],g=y.join(", "),c=f([]),b=se(),w=f(!1),m=f(-1);f("");const v=f(512),q=async({raw:e})=>{var t,o;try{if(e){const i="."+((t=e.name.split(".").pop())==null?void 0:t.toLowerCase());if(!y.includes(i))throw`不支持的文件类型，请上传 ${g} 格式的文件`;w.value=!0,await W(e,c.value);const h=await B(e);if(!h)throw"解析结果为空，已自动忽略";n.value.push({name:e.name,path:"",data:[]}),e.data=h,c.value.push(e),E(c.value.length-1),C()}}catch(i){Z.msgError(i)}finally{w.value=!1,(o=b.value)==null||o.clearFiles()}},C=()=>{n.value.forEach(e=>{e.data=[];const t=c.value.findIndex(i=>i.name==e.name);te({text:c.value[t].data,chunkLen:v.value}).forEach(i=>{e.data.push({q:i,a:""})})})},B=async e=>{const t=e.name.substring(e.name.lastIndexOf(".")+1);let o="";switch(t){case"md":case"txt":o=await I(e);break;case"pdf":o=await ee(e);break;case"doc":case"docx":o=await Y(e);break;default:o=await I(e);break}return o},R=async e=>{n.value[m.value].data.splice(e,1)},T=e=>{n.value.splice(e,1),c.value.splice(e,1)},E=e=>{m.value=e};return S({clearFiles:()=>{c.value=[]}}),(e,t)=>{var V;const o=O,i=P,h=Q,j=G,z=H,A=K,M=J;return d(),p("div",de,[le((d(),p("div",pe,[l(i,{ref_key:"uploadRef",ref:b,drag:"","on-change":q,"auto-upload":!1,"show-file-list":!1,accept:s(g),multiple:!0,limit:50},{default:x(()=>[a("div",me,[l(o,{name:"el-icon-Upload"}),t[1]||(t[1]=D(" 拖拽文件至此，或点击")),t[2]||(t[2]=a("em",null," 选择文件 ",-1))]),a("div",ue,"支持 "+k(s(g))+" 文件",1)]),_:1},8,["accept"])])),[[M,s(w)]]),s(n).length>0?(d(),p("div",_e,[a("div",fe,[a("div",ve,[(d(!0),p(L,null,$(s(n),(u,r)=>(d(),p("div",{key:r,class:ce(["fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer hover:bg-page transition duration-300",{"bg-page":s(m)==r}]),onClick:_=>E(r)},[l(o,{name:"el-icon-Folder",size:16,color:"#ffc94d"}),a("div",xe,k(u.name),1),a("div",ge,[l(o,{name:"el-icon-DeleteFilled",onClick:_=>T(r)},null,8,["onClick"])])],10,he))),128))]),a("div",we,[a("div",ke,[t[3]||(t[3]=a("div",null,"分段长度",-1)),l(h,{content:"按结束符号进行分段。我们建议您的文档应合理的使用标点符号，以确保每个完整的句子长度不要超过该值中文文档建议400~1000英文文档建议600~1200",placement:"top"},{default:x(()=>[a("span",null,[l(o,{name:"el-icon-QuestionFilled"})])]),_:1})]),l(j,{class:"mt-2 !w-[300px]",modelValue:s(v),"onUpdate:modelValue":t[0]||(t[0]=u=>ne(v)?v.value=u:null)},null,8,["modelValue"]),a("div",ye,[l(z,{type:"primary",onClick:C},{default:x(()=>t[4]||(t[4]=[D("重新预览")])),_:1})])])]),a("div",be,[a("div",Ce," 分段预览（"+k((V=s(n)[s(m)])==null?void 0:V.data.length)+"组） ",1),a("div",Ee,[l(A,{height:"100%"},{default:x(()=>{var u;return[(d(!0),p(L,null,$((u=s(n)[s(m)])==null?void 0:u.data,(r,_)=>(d(),p("div",{class:"bg-page rounded p-[10px] mt-2",key:_},[l(ae,{index:_,name:s(n)[s(m)].name,data:r.q,"onUpdate:data":F=>r.q=F,onDelete:F=>R(_)},null,8,["index","name","data","onUpdate:data","onDelete"])]))),128))]}),_:1})])])])):ie("",!0)])}}}),He=re(Ve,[["__scopeId","data-v-c44a92bc"]]);export{He as default};
