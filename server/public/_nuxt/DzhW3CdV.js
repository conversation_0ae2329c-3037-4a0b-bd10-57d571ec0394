import{E as $,a as E}from"./f1oA63J4.js";import{j as B,b as N,dj as L,E as R}from"./BsMqt_su.js";import{_ as U}from"./mtkgvlCx.js";/* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{u as D}from"./67xbGseh.js";import{_ as I}from"./FZOZgguZ.js";import{l as M,b as v,m as b,j as q,F as z,M as m,N as u,O as t,a7 as n,u as o,_ as A,aq as O,Z as s,a0 as i,X as k,a6 as P,y as X,a1 as Z,a4 as G,n as C}from"./Dp9aCaJ6.js";import{_ as H}from"./DlAUqK2U.js";import"./Cpwj6wt2.js";import"./D1YDx4Yr.js";import"./DCTLXrZ8.js";import"./BcmWjB0N.js";import"./BATbmIFb.js";import"./DugIyZO8.js";import"./CpvOqAuN.js";import"./BPgEaOor.js";import"./CTbdiA5k.js";import"./Bz4jch3v.js";import"./Dbma4Whm.js";import"./Cv6HhfEG.js";/* empty css        */import"./Ds7ZwOv-.js";import"./CHWQlbQz.js";import"./9Bti1uB6.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        */const J={class:"p-[20px] flex bg-body rounded-[12px] flex-col h-full"},K={class:"grid grid-cols-2 md:grid-cols-2 gap-4 bg-page py-[20px] rounded-lg flex-none"},Q={class:"flex flex-col items-center justify-center"},W={class:"font-medium text-[25px] text-[#0256FF]"},Y={class:"mt-2"},tt={class:"flex flex-col items-center justify-center"},et={class:"font-medium text-[25px] text-[#0256FF]"},ot={class:"flex mt-4 flex-none"},st={class:"p-[8px] flex justify-around bg-page rounded-[10px] font-medium"},at=["onClick"],nt={class:"mt-4 flex-1 min-h-0 flex flex-col"},lt={class:"flex-1 min-h-0"},it={class:"flex justify-end mt-4"},pt=M({__name:"balance",setup(rt){const{userInfo:f}=B(),x=N(),c=v({type:1}),h=b(()=>"数量"),d=v(!1),y=q(),w=b(()=>[{name:`${x.getTokenUnit}明细`,type:1},{name:"智能体明细",type:2}]),j=async r=>{c.value.type=r,await C(),_()},{pager:p,getLists:_}=D({fetchFun:L,params:c.value}),F=async r=>{d.value=!0,await C(),y.value.open(r)};return z(()=>{_()}),(r,a)=>{const l=$,S=R,T=E,V=U;return m(),u("div",J,[t("div",K,[t("div",Q,[t("div",W,n(o(f).balance),1),t("div",Y,n(o(x).getTokenUnit)+"数量",1)]),t("div",tt,[t("div",et,n(o(f).robot_num),1),a[2]||(a[2]=t("div",{class:"mt-2"},"智能体",-1))])]),t("div",ot,[t("div",st,[(m(!0),u(A,null,O(o(w),(e,g)=>(m(),u("div",{class:k([{isSelect:o(c).type==e.type},"px-[15px] md:px-[30px] py-[10px] cursor-pointer"]),key:g,onClick:mt=>j(e.type)},[t("span",null,n(e.name),1)],10,at))),128))])]),t("div",nt,[t("div",lt,[s(T,{data:o(p).lists,height:"100%"},{default:i(()=>[s(l,{label:"订单编号",prop:"sn","min-width":"150"}),s(l,{label:"变动类型",prop:"change_type","min-width":"150"}),s(l,{label:"智能体/应用名",prop:"robot_name","min-width":"150"},{default:i(({row:e})=>[t("div",null,n(e.robot_name||"-"),1)]),_:1}),s(l,{label:"操作时间",prop:"create_time","min-width":"150"}),s(l,{label:`变动${o(h)}`,prop:"change_amount","min-width":"100"},{default:i(({row:e})=>[t("div",{class:k({"text-danger":e.action==2})},[t("span",null,n(e.action==1?"+":"-"),1),t("span",null,n(e.change_amount),1)],2)]),_:1},8,["label"]),s(l,{label:"操作","min-width":"80"},{default:i(({row:e})=>[s(S,{onClick:g=>F(e.id),link:"",type:"primary"},{default:i(()=>a[3]||(a[3]=[P(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),t("div",it,[s(V,{modelValue:o(p),"onUpdate:modelValue":a[0]||(a[0]=e=>X(p)?p.value=e:null),onChange:o(_)},null,8,["modelValue","onChange"])])]),o(d)?(m(),Z(I,{key:0,type:o(h),ref_key:"popRef",ref:y,onClose:a[1]||(a[1]=e=>d.value=!1)},null,8,["type"])):G("",!0)])}}}),Ot=H(pt,[["__scopeId","data-v-b0e2ee91"]]);export{Ot as default};
