import{E as _}from"./CYs1NzuK.js";import{cn as f}from"./CLVDtxqA.js";/* empty css        *//* empty css        */import{_ as v}from"./kv4RgIMK.js";import{l as h,r as V,M as a,N as o,Z as w,O as s,_ as z,aq as k,u as l,a1 as b,a0 as C,X as r,a7 as c}from"./Dp9aCaJ6.js";import{_ as L}from"./DlAUqK2U.js";import"./DTPcp5_i.js";import"./DCTLXrZ8.js";import"./DssWEouO.js";import"./JBMcerbz.js";import"./9Bti1uB6.js";import"./C2uyROqA.js";const g={class:"mt-[15px]"},y={class:"flex flex-wrap"},B=["onClick"],E={class:"flex justify-center items-center mt-[10px] h-[20px]"},N={class:"text-base text-[#101010] dark:text-white mt-[4px] size-scale"},S={class:"text-xs text-[#798696] dark:text-white mt-[4px] size-name"},$=h({__name:"dalle-picture-size",props:{modelValue:{default:"512x512"}},emits:["update:modelValue"],setup(i,{emit:n}){const p=n,u=i,{modelValue:t}=f(u,p),x=V({lists:[{name:"头像图",scaleLabel:"1:1",scaleValue:"1024x1024",class:"w-[20px] h-[20px]"},{name:"媒体配图",scaleLabel:"3:4",scaleValue:"1024x1792",class:"w-[15px] h-[20px]"},{name:"文章配图",scaleLabel:"4:3",scaleValue:"1792x1024",class:"w-[20px] h-[15px]"}]});return t.value="1024x1024",(q,D)=>{const d=_;return a(),o("div",g,[w(v,{title:"图片尺寸",tips:"",required:""}),s("div",y,[(a(!0),o(z,null,k(l(x).lists,(e,m)=>(a(),b(d,{key:m,placement:"bottom",width:150,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:`分辨率：${e.scaleValue}px`},{reference:C(()=>[s("div",{class:r(["picture-size cursor-pointer text-center hover:text-primary",{"picture-size-active":l(t)==(e==null?void 0:e.scaleValue),"picture-size-disable":!(e!=null&&e.scaleValue)}]),onClick:M=>t.value=e.scaleValue},[s("div",E,[s("div",{class:r(["rect",e.class])},null,2)]),s("div",N,c(e.scaleLabel),1),s("div",S,c(e.name),1)],10,B)]),_:2},1032,["content"]))),128))])])}}}),R=L($,[["__scopeId","data-v-055ff91f"]]);export{R as default};
