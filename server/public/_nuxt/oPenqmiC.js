import{a as f,E as V}from"./DLovRgFC.js";import{cn as y}from"./DjCZV6kq.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{a as v}from"./B6RwwKVV.js";import{_ as j}from"./CbyB5uyr.js";import{l as x,M as o,N as a,Z as m,O as k,a0 as B,_ as E,aq as g,u as s,y as M,a1 as N}from"./Dp9aCaJ6.js";import{_ as S}from"./DlAUqK2U.js";import"./DiWwZzDD.js";import"./DCTLXrZ8.js";import"./DZCM63qd.js";import"./Bi_VJcob.js";import"./Cv6HhfEG.js";import"./CDkfsEbM.js";import"./CQg2_aaP.js";import"./B2TveoJZ.js";import"./ChpRm8Gn.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";import"./B-qcbZcc.js";import"./B9cNZX9s.js";import"./CIYOwJ14.js";import"./9Bti1uB6.js";/* empty css        */const b={class:"mj-styles"},h=x({__name:"mj-styles",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(p,{emit:i}){const n=i,c=p,{modelValue:t}=y(c,n);return(w,r)=>{const u=V,d=f;return o(),a("div",b,[m(j,{title:"风格选择",tips:"指定midjourney的渲染风格"}),k("div",null,[m(d,{modelValue:s(t),"onUpdate:modelValue":r[0]||(r[0]=e=>M(t)?t.value=e:null),placeholder:"请选择版本",class:"w-full mt-[8px]",size:"large"},{default:B(()=>{var e;return[(o(!0),a(E,null,g((e=s(v))==null?void 0:e.mj_style,(l,_)=>(o(),N(u,{key:l,label:l,value:_},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])])])}}}),le=S(h,[["__scopeId","data-v-dc4ec9a9"]]);export{le as default};
