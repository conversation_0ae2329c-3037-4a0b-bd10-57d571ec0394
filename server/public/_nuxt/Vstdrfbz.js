import{E as me}from"./C1aUxTFE.js";import{b as X,i as fe,bB as Q,bC as ve,bD as ye,bE as J,j as _e,f as se,E as O,v as he,m as ge,h as we,bx as ke,bF as xe,e as be}from"./BQ-RMI0l.js";/* empty css        */import{l as S,aA as Ce,M as e,N as r,O as n,a4 as p,X as F,a6 as k,a7 as x,u as o,a1 as h,a0 as i,V as T,a2 as Z,Z as y,_ as B,aq as U,b as E,j as K,r as $e,m as Y,aa as Ee,y as ne,n as Ie,a9 as Ae,w as Be}from"./Dp9aCaJ6.js";import{_ as G}from"./DlAUqK2U.js";import{_ as le}from"./C9-ovotH.js";import{E as ee}from"./DdkLcgv7.js";import{_ as Pe}from"./DfVmRZTU.js";import{E as Se}from"./C-ql7yOt.js";import{_ as H}from"./B_BP6MI7.js";/* empty css        */import{u as Ve}from"./Bb2-23m7.js";import{u as ae}from"./CxQjGp2g.js";import{E as te}from"./L7ewHh_h.js";import{E as oe}from"./De57gGYj.js";/* empty css        *//* empty css        */import{c as De}from"./DwFObZc_.js";import{g as Re}from"./B_1915px.js";import{d as Ue}from"./D49putpZ.js";import{Q as qe}from"./Bg2e09vA.js";import{i as je}from"./B7SD2TCw.js";import{E as Le}from"./C2S7-d6r.js";import"./B36mVPSB.js";const Te=["src"],Ne={key:0,class:"h-[20px] mb-[10px] text-tx-secondary text-xs"},Me={class:"overflow-x-auto"},ze=S({__name:"item",props:{type:{default:"left"},avatar:{default:""},bg:{default:""},color:{default:"black"},time:{default:""},modelName:{default:""}},setup(m){Ce(l=>({75584782:l.color}));const f=X();return(l,c)=>{const v=me;return e(),r("div",{class:"chat-msg-item",style:Z(l.bg?`--item-bg: ${l.bg}`:"")},[n("div",{class:F(`chat-msg-item__${l.type}`)},[l.avatar?(e(),r("img",{key:0,class:"chat-msg-item__avatar",src:l.avatar},null,8,Te)):p("",!0),n("div",{class:F([`chat-msg-item__${l.type}-wrap`,{"has-time":l.time}])},[l.time?(e(),r("div",Ne,[k(x(l.time)+" ",1),l.modelName&&o(f).getChatConfig.is_show_model?(e(),h(v,{key:0,class:"ml-2",type:"success",style:{"--el-tag-border-color":"transparent"}},{default:i(()=>[k(x(l.modelName),1)]),_:1})):p("",!0)])):p("",!0),n("div",Me,[n("div",{class:F(`chat-msg-item__${l.type}-content`)},[T(l.$slots,"default",{},void 0,!0)],2)]),n("div",null,[T(l.$slots,"actions",{},void 0,!0)])],2)],2),T(l.$slots,"outer_actions",{},void 0,!0)],4)}}}),wo=G(ze,[["__scopeId","data-v-525ad2b6"]]),We="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAgCAYAAABgrToAAAACJElEQVRYR+2YMWsUURSFz3m7s+nskjUIQSutbMRi7WzUVjSadMHCbVLkByjmLygaCVYWRqMEUhkFS9Gg0cJfYCPZjYUQFbPs+I7c2R1Q2ZjZfRNYYS4MAzPv3vnmvDvL3kMA2Hl5/CjLI9ckf4ZwY3Zt15C+gfwIao3So0rt3XsJtPUk9M/cAW6y9ap2DIyfAjgCwANwGeoYiEFtk/5e5CvXeer1D2neATcGgiTZM4+t9RNLEKcBtAFEGeBsiRWzl7EoSXo+8rV9gWc/fDc1B1VSEoEnDpj0KTB33tS26DGaEezvZQZpRxmODyoT5+vwBwS3zeTcT4yjTdZNJEiPSykk1bjZX6HeD/WQJ1zUApgq2w+etcsniBuAVlH9vELOx6Yo1VywgkmTB4X1kEGGhyAtg/Ecq3NNqnknDwVTrNBaactEts88OHs5b8Bw/Tof4M+kr4WrwwhoL9n5uRPWhxWwsxPEl+EGNMacP5I8evCPGgVgqKSFgoWCoQqE5hc9WCgYqkBoftGDeSiYz1/+UJLe+foftvh2A2B1fwQIrapkaFoDcK4PVyH0qVnyU4fjGdW4NQ2WlgDE5hLkMoJmQdh9zW9Dk59K5lhtLjyE01TX/jDILP5MGEbvbFPOJroIXvc5PjvTBbx7GM4vAjjd9WdSc2g/IPaqaTv5Aq58haP1TSb2Au20GGErvgTxIqiTAA7tVSnn+2Z9vAXdCsa4bD6Nsf0C/gYA5PMzcW0AAAAASUVORK5CYII=",Fe={class:"h-[65vh]"},Ye={class:"font-medium text-tx-primary"},Ge={class:"text-muted text-sm whitespace-pre-wrap"},Qe=S({__name:"context-popup",props:{context:{default:()=>[]}},emits:["close"],setup(m,{emit:f}){const l=f;return(c,v)=>{const _=te,g=oe;return e(),h(g,{title:`对话上下文(${c.context.length}条)`,"model-value":!0,width:"700px",onClose:v[0]||(v[0]=w=>l("close"))},{default:i(()=>[n("div",Fe,[y(_,null,{default:i(()=>[(e(!0),r(B,null,U(c.context,(w,b)=>(e(),r("div",{key:b,class:"py-[6px] px-[10px] border border-solid border-br-light rounded mb-[10px]"},[n("div",Ye,x(w.role),1),n("div",Ge,x(w.content),1)]))),128))]),_:1})])]),_:1},8,["title"])}}}),Ze={class:"h-[65vh]"},Oe={key:0,class:"text-muted text-sm whitespace-pre-wrap"},Ke={key:1,class:"text-muted text-sm whitespace-pre-wrap"},He=S({__name:"quote-popup",props:{quotes:{default:()=>[]}},emits:["close"],setup(m,{emit:f}){const l=f;return(c,v)=>{const _=te,g=oe;return e(),h(g,{title:`知识库引用(${c.quotes.length}条)`,"model-value":!0,width:"700px",onClose:v[0]||(v[0]=w=>l("close"))},{default:i(()=>[n("div",Ze,[y(_,null,{default:i(()=>[(e(!0),r(B,null,U(c.quotes,(w,b)=>(e(),r("div",{key:b,class:"py-[6px] px-[10px] border border-solid border-br-light rounded mb-[10px]"},[w.question?(e(),r("div",Oe,x(w.question),1)):p("",!0),w.answer?(e(),r("div",Ke,x(w.answer),1)):p("",!0)]))),128))]),_:1})])]),_:1},8,["title"])}}}),Je=fe({id:"chatStore",state:()=>{const m=Q(ve,"");return m.value||(m.value=ye()),{uuid:m.value||"",token:"",channelId:""}},getters:{isAuth:m=>!!m.token},actions:{init(m){this.channelId=m;const f=Q(J,{});this.token=f.value[this.channelId]||""},clearAuth(){const m=Q(J,{});delete m.value[this.channelId],this.token=""},async auth(m=""){const f=await De({password:m,channel:this.channelId,identity:this.uuid});this.token=f.token;const l=Q(J,{});l.value=Object.assign({[this.channelId]:f.token},l.value)}}}),Xe={key:0,class:"flex mb-[16px] bg-body rounded-[5px] p-[10px] max-w-[300px] items-center"},et={class:"line-clamp-2 flex-1 min-w-0 ml-[6px] text-tx-primary",style:{"word-break":"break-word"}},tt=S({__name:"record-image",props:{url:{},name:{}},setup(m){return(f,l)=>{const c=ee;return f.url?(e(),r("div",Xe,[y(c,{class:"flex-none w-[40px] h-[40px]",src:f.url,"preview-src-list":[f.url],"hide-on-click-modal":!0},null,8,["src","preview-src-list"]),n("div",et,x(f.name),1)])):p("",!0)}}}),ot={class:"text-info line-clamp-2 flex-1 min-w-0 ml-[6px]",style:{"word-break":"break-word"}},st=S({__name:"record-file",props:{url:{},name:{}},setup(m){const f=m,l=async()=>{window.open(f.url,"_blank")};return(c,v)=>c.url?(e(),r("div",{key:0,class:"flex mb-[16px] bg-white rounded-[5px] p-[10px] max-w-[300px] items-center cursor-pointer",onClick:l},[n("div",ot,x(c.name),1)])):p("",!0)}}),nt={class:"h-[70vh]"},lt={class:"poster-bg flex flex-col"},at=["src"],it={class:"px-[20px] pt-[135px]"},rt={class:"bg-white rounded-lg p-[15px] text-tx-primary"},ut={class:"flex justify-end text-[16px] items-baseline"},dt={class:"bg-[#066cff] px-[10px] py-[6px] text-white",style:{"border-radius":"8px 0 8px 8px"}},pt={class:"line-clamp-2"},ct={class:"text-[14px]"},mt={class:"px-[20px] pt-[20px]"},ft={class:"flex items-center justify-between"},vt={class:"flex items-center"},yt=["src"],_t={class:"ml-[10px] text-[16px]"},ht={class:"line-clamp-2"},gt={key:0},wt={class:"w-[120px] flex flex-col justify-center items-end"},kt={class:"flex justify-end mt-[10px]"},xt=S({__name:"dialog-poster",setup(m,{expose:f}){const l=X(),c=E(!1),v=E(!1),_=E({}),g=E(!1),w=_e(),b=K(),P=$e({title:"",content:""}),N=async()=>{var $;g.value=!0;try{const{data:t}=await ge({id:12});_.value=(($=JSON.parse(t)[0])==null?void 0:$.content)||{}}finally{g.value=!1}},M=Y(()=>`${window.origin}/mobile?user_sn=${w.userInfo.sn}`),{copy:q}=ae(),z=Y(()=>`${window.origin}/?user_sn=${w.userInfo.sn}`),I=async()=>{try{v.value=!0,await Ue(b.value)}catch{se.msgError("下载失败，请重试")}finally{v.value=!1}};return f({open:$=>{N(),c.value=!0,P.title=$.title,P.content=je($.content)?[$.content]:$.content}}),($,t)=>{const a=le,W=te,L=H,V=O,D=oe,R=he;return e(),h(D,{modelValue:o(c),"onUpdate:modelValue":t[1]||(t[1]=s=>ne(c)?c.value=s:null),title:"生成海报","show-close":"",width:"430px"},{default:i(()=>[n("div",nt,[Ee((e(),h(W,null,{default:i(()=>[o(g)?p("",!0):(e(),r("div",{key:0,ref_key:"posterRef",ref:b,class:"poster overflow-hidden pb-[10px] rounded-lg"},[n("div",lt,[n("img",{class:"w-full",src:o(_).default==2?o(l).getImageUrl(o(_).posterUrl):o(_).poster==1?o(l).getImageUrl(o(_).defaultUrl1):o(l).getImageUrl(o(_).defaultUrl2),style:Z({background:o(_).bgColor}),alt:""},null,12,at),n("div",{class:"flex-1 min-h-0",style:Z({background:o(_).bgColor})},null,4)]),n("div",{class:"w-full h-full poster-contain1 bg-[#BBBBBB]",style:Z({color:o(_).textColor})},[n("div",it,[n("div",rt,[n("div",ut,[n("span",dt,[n("span",pt,x(o(P).title),1)])]),n("span",ct,[(e(!0),r(B,null,U(o(P).content,(s,u)=>(e(),r("div",{key:u,class:F(["mb-[15px] mt-4 p-[10px] bg-[#f0f5fe] text-tx-primary",{"pt-[15px]":u>0}]),style:{"border-radius":"0 8px 8px 8px"}},[y(a,{content:s,"line-numbers":!0,"line-clamp":o(_).showContentType==1?o(_).contentNum:0,theme:"light"},null,8,["content","line-clamp"])],2))),128))])])]),n("div",mt,[n("div",ft,[n("div",vt,[n("img",{src:o(w).userInfo.avatar,alt:"",class:"w-[60px] h-[60px] rounded-full"},null,8,yt),n("div",_t,[n("div",ht,x(o(w).userInfo.nickname),1),o(_).showData==1?(e(),r("div",gt,x(o(_).data),1)):p("",!0)])]),n("div",wt,[y(qe,{value:o(M),size:85,margin:1},null,8,["value"]),t[2]||(t[2]=n("div",{class:"text-xs mt-2"}," 长按识别二维码 ",-1))])])])],4)],512))]),_:1})),[[R,o(g)]])]),n("div",kt,[y(V,{round:"",onClick:I,loading:o(v)},{icon:i(()=>[y(L,{name:"el-icon-Download"})]),default:i(()=>[t[3]||(t[3]=k(" 下载 "))]),_:1},8,["loading"]),y(V,{round:"",onClick:t[0]||(t[0]=s=>o(q)(o(z)))},{icon:i(()=>[y(L,{name:"el-icon-DocumentCopy"})]),default:i(()=>[t[4]||(t[4]=k(" 复制链接 "))]),_:1})])]),_:1},8,["modelValue"])}}}),bt=G(xt,[["__scopeId","data-v-4178ddb0"]]),Ct={class:"chat-content"},$t={class:"chat-text"},Et={key:0},It={key:3,class:"flex flex-wrap mx-[-5px]"},At={key:4,class:"flex flex-wrap mx-[-5px]"},Bt={class:"w-[120px] h-[120px] mx-[5px] mt-[10px]"},Pt={key:5,class:"mt-[15px]"},St={class:"line-clamp-1 mr-[10px] text-tx-primary"},Vt={key:0,class:"mt-[10px]"},Dt=S({__name:"content",props:{type:{default:"text"},recordList:{default:()=>[]},content:{default:""},context:{default:()=>[]},quotes:{default:()=>[]},images:{default:()=>[]},videos:{default:()=>[]},files:{default:()=>[]},filesPlugin:{default:()=>[]},typing:{type:Boolean,default:!1},lineNumbers:{type:Boolean,default:!0},showRewrite:{type:Boolean,default:!1},showCopy:{type:Boolean,default:!1},showContext:{type:Boolean,default:!1},showQuote:{type:Boolean,default:!1},showVoice:{type:Boolean,default:!1},showPoster:{type:Boolean,default:!1},recordId:{default:0},index:{default:0},recordType:{default:1},channel:{default:""},userId:{default:""}},emits:["click-custom-link","rewrite"],setup(m,{emit:f}){const l=f;Je();const c=m,v=E(!1),_=E(!1),g=E(!1),w=async()=>(await Re({records_id:c.recordId,content:c.index,type:c.recordType},{Authorization:c.channel,Identity:c.userId})).file,b=Y(()=>c.images.map(({url:t})=>t)),{play:P,audioPlaying:N,pause:M,audioLoading:q}=Ve(),{copy:z}=ae(),I=t=>{l("click-custom-link",t)},j=K(),$=async()=>{const t=c.recordList.filter(a=>a.id==c.recordId);if(t.length!=2){se.msgError("上下文数据不对～");return}g.value=!0,await Ie(),j.value.open({title:t[0].content,content:t[1].content})};return(t,a)=>{var u;const W=le,L=ee,V=Pe,D=Se,R=H,s=O;return e(),r("div",Ct,[n("div",$t,[t.filesPlugin.length?(e(),r("div",Et,[(e(!0),r(B,null,U(t.filesPlugin,(d,C)=>(e(),r(B,{key:C},[d.type=="image"||d.type=="10"?(e(),h(tt,{key:0,url:d.url,name:d.name},null,8,["url","name"])):d.type=="file"||d.type=="30"?(e(),h(st,{key:1,url:d.url,name:d.name},null,8,["url","name"])):p("",!0)],64))),128))])):p("",!0),t.type==="html"?(e(),h(W,{key:1,content:t.content,"line-numbers":t.lineNumbers,typing:t.typing,style:{color:"inherit"},onClickCustomLink:a[0]||(a[0]=d=>I(d))},null,8,["content","line-numbers","typing"])):t.type==="text"?(e(),r("div",{key:2,class:F(["break-all text-lg",{"wait-typing":t.typing}]),style:{"word-wrap":"break-word"}},x(t.content),3)):p("",!0),o(b).length?(e(),r("div",It,[(e(!0),r(B,null,U(o(b),(d,C)=>(e(),h(L,{key:C,"preview-src-list":o(b),"preview-teleported":!0,infinite:!1,"initial-index":C,"hide-on-click-modal":!0,class:"w-[120px] h-[120px] mx-[5px] mt-[10px]",src:d,fit:"cover"},null,8,["preview-src-list","initial-index","src"]))),128))])):p("",!0),t.videos.length?(e(),r("div",At,[n("div",Bt,[(e(!0),r(B,null,U(t.videos,(d,C)=>(e(),h(V,{"file-size":"120px",key:C,uri:d.url,type:"video"},null,8,["uri"]))),128))])])):p("",!0),(u=t.files)!=null&&u.length?(e(),r("div",Pt,[(e(!0),r(B,null,U(t.files,(d,C)=>(e(),r("div",{key:C,class:"flex mb-[10px] items-center"},[a[10]||(a[10]=n("img",{class:"w-[18px] h-[14px] mr-2",src:We},null,-1)),n("div",St,x(d.name),1),y(D,{href:d.url,target:"_blank",type:"primary"},{default:i(()=>a[9]||(a[9]=[k(" 下载 ")])),_:2},1032,["href"])]))),128))])):p("",!0)]),!t.typing&&(t.showCopy||t.showVoice||t.showQuote||t.showContext)?(e(),r("div",Vt,[t.showRewrite?(e(),h(s,{key:0,link:"",onClick:a[1]||(a[1]=d=>l("rewrite"))},{icon:i(()=>[y(R,{name:"el-icon-RefreshLeft"})]),default:i(()=>[a[11]||(a[11]=k(" 重新回答 "))]),_:1})):p("",!0),t.showCopy?(e(),h(s,{key:1,link:"",onClick:a[2]||(a[2]=d=>o(z)(t.content))},{icon:i(()=>[y(R,{name:"el-icon-CopyDocument"})]),default:i(()=>[a[12]||(a[12]=k(" 复制 "))]),_:1})):p("",!0),t.showVoice?(e(),r(B,{key:2},[o(N)?(e(),h(s,{key:0,link:"",onClick:o(M)},{icon:i(()=>[y(R,{name:"local-icon-audio_voice"})]),default:i(()=>[a[13]||(a[13]=k(" 停止 "))]),_:1},8,["onClick"])):(e(),h(s,{key:1,link:"",loading:o(q),onClick:a[3]||(a[3]=d=>o(P)(w))},{icon:i(()=>[y(R,{name:"local-icon-audio_voice"})]),default:i(()=>[a[14]||(a[14]=k(" 朗读 "))]),_:1},8,["loading"]))],64)):p("",!0),t.showPoster?(e(),h(s,{key:3,link:"",onClick:$},{icon:i(()=>[y(R,{name:"el-icon-Picture"})]),default:i(()=>[a[15]||(a[15]=k(" 生成海报 "))]),_:1})):p("",!0),t.quotes.length&&t.showQuote?(e(),h(s,{key:4,link:"",type:"primary",onClick:a[4]||(a[4]=d=>_.value=!0)},{default:i(()=>[k(x(t.quotes.length)+"条引用 ",1)]),_:1})):p("",!0),t.context.length&&t.showContext?(e(),h(s,{key:5,link:"",type:"primary",onClick:a[5]||(a[5]=d=>v.value=!0)},{default:i(()=>[k(x(t.context.length)+"条上下文 ",1)]),_:1})):p("",!0)])):p("",!0),o(v)?(e(),h(Qe,{key:1,context:t.context,onClose:a[6]||(a[6]=d=>v.value=!1)},null,8,["context"])):p("",!0),o(_)?(e(),h(He,{key:2,quotes:t.quotes,onClose:a[7]||(a[7]=d=>_.value=!1)},null,8,["quotes"])):p("",!0),o(g)?(e(),h(bt,{key:3,ref_key:"posterRef",ref:j,onClose:a[8]||(a[8]=d=>g.value=!1)},null,512)):p("",!0)])}}}),ko=G(Dt,[["__scopeId","data-v-815bad9f"]]),Rt=S({props:{showClose:{type:Boolean,default:!0}},emits:["close"],setup(m,{emit:f}){return{handleClose:()=>{f("close")}}}}),Ut={class:"del-wrap"};function qt(m,f,l,c,v,_){const g=H;return e(),r("div",Ut,[T(m.$slots,"default",{},void 0,!0),m.showClose?(e(),r("div",{key:0,class:"icon-close",onClick:f[0]||(f[0]=Ae((...w)=>m.handleClose&&m.handleClose(...w),["stop"]))},[y(g,{size:12,name:"el-icon-CloseBold"})])):p("",!0)])}const jt=G(Rt,[["render",qt],["__scopeId","data-v-cad697f2"]]),Lt={class:"chat-action"},Tt={key:0,class:"flex items-center pt-[10px] justify-center"},Nt={class:"p-[10px]"},Mt={class:"flex items-center pb-3 gap-3"},zt={class:"flex h-12 bg-page px-2 rounded-[12px] items-center max-w-[400px]"},Wt={class:"line-clamp-2 ml-[10px] flex-1 min-w-0",style:{"word-break":"break-word"}},Ft={class:"mb-[10px] flex flex-wrap items-center"},Yt={key:0,class:"mr-[10px]"},Gt={class:"py-[3px] mr-[-7px] input-suffix"},Qt=S({__name:"index",props:{placeholder:{default:"请输入问题"},loading:{type:Boolean,default:!1},showManual:{type:Boolean,default:!1},showPause:{type:Boolean,default:!0},showClear:{type:Boolean,default:!0},showContinue:{type:Boolean,default:!1},showFileUpload:{type:Boolean,default:!1},menus:{default:()=>[]},btnColor:{default:"#fff"},filePlugin:{default:()=>({})}},emits:["clear","pause","continue","blur","focus","enter","update:filePlugin"],setup(m,{expose:f,emit:l}){const c=m,v=l,_=X(),g=E(""),w=K(),b=K(),P=E(!1),N=()=>{P.value=!1,v("blur")},M=()=>{P.value=!0,v("focus")},q=E(!1),z=Y(()=>`${c.placeholder} ${_.isMobile?"":"（Shift + Enter）= 换行"}`),I=we(c,"filePlugin",v),j=E(!1),$=s=>{I.value.url=s.uri,I.value.name=s.name},t=async s=>{j.value=!0;try{return await xe("image",{file:s.file,name:"file",header:{}})}finally{j.value=!1}},a=s=>{if(!(s.shiftKey&&s.keyCode===13)&&!q.value&&s.keyCode===13)return v("enter",g.value),s.preventDefault()},W=Y(()=>_.isMobile?{maxRows:4,minRows:1}:{maxRows:6,minRows:1}),L=E(!1),{height:V}=ke(w);let D=0;return Be(()=>{D===0&&(D=V.value),V.value>D&&(L.value=!0),g.value===""&&V.value>D&&(L.value=!1)}),f({setInputValue:(s="")=>{g.value=s},focus:()=>{var s;return(s=b.value)==null?void 0:s.focus()},blur:()=>{var s;return(s=b.value)==null?void 0:s.blur()}}),(s,u)=>{const d=H,C=O,ie=ee,re=jt,ue=O,de=Le,pe=be;return e(),r("div",Lt,[s.showPause||s.showContinue?(e(),r("div",Tt,[s.loading?(e(),h(C,{key:0,plain:"",onClick:u[0]||(u[0]=A=>v("pause"))},{icon:i(()=>[y(d,{name:"el-icon-VideoPause"})]),default:i(()=>[u[8]||(u[8]=k(" 停止 "))]),_:1})):s.showContinue?(e(),h(C,{key:1,plain:"",onClick:u[1]||(u[1]=A=>v("continue"))},{icon:i(()=>[y(d,{name:"el-icon-VideoPlay"})]),default:i(()=>[u[9]||(u[9]=k(" 继续 "))]),_:1})):p("",!0)])):p("",!0),n("div",Nt,[n("div",Mt,[o(I).url?(e(),h(re,{key:0,onClose:u[2]||(u[2]=A=>o(I).url="")},{default:i(()=>[n("div",zt,[y(ie,{src:o(I).url,"preview-src-list":[o(I).url],"hide-on-click-modal":!0,class:"w-8 h-8 flex-none"},null,8,["src","preview-src-list"]),n("span",Wt,x(o(I).name),1)])]),_:1})):p("",!0),T(s.$slots,"file-list",{},void 0,!0)]),n("div",Ft,[T(s.$slots,"btn",{},void 0,!0),s.showFileUpload?(e(),r("div",Yt,[y(de,{ref:"uploadRef","show-file-list":!1,accept:".jpg,.png,.jpeg",multiple:!1,"on-success":$,"http-request":t},{trigger:i(()=>[y(ue,{plain:"",class:"!rounded-[8px]",loading:o(j)},{icon:i(()=>[y(d,{name:"el-icon-Upload"})]),default:i(()=>[u[10]||(u[10]=k(" 上传图片 "))]),_:1},8,["loading"])]),_:1},512)])):p("",!0),s.showClear?(e(),h(C,{key:1,disabled:s.loading,class:"!rounded-[8px]",plain:"",onClick:u[3]||(u[3]=A=>v("clear"))},{icon:i(()=>[y(d,{name:"el-icon-Delete"})]),default:i(()=>[u[11]||(u[11]=k(" 清空 "))]),_:1},8,["disabled"])):p("",!0),(e(!0),r(B,null,U(s.menus,(A,ce)=>(e(),h(C,{color:s.btnColor,style:{"--el-button-disabled-text-color":`var(
                            --el-button-text-color
                        )`},disabled:s.loading,onClick:Zt=>v("enter",A.keyword),key:ce},{default:i(()=>[k(x(A.keyword),1)]),_:2},1032,["color","disabled","onClick"]))),128))]),n("div",{ref_key:"textContainerRef",ref:w,class:"chat-input relative text-container flex items-center"},[y(pe,{ref_key:"textareaRef",ref:b,modelValue:o(g),"onUpdate:modelValue":u[4]||(u[4]=A=>ne(g)?g.value=A:null),autosize:o(W),type:"textarea",placeholder:o(z),resize:"none",onCompositionstart:u[5]||(u[5]=A=>q.value=!0),onCompositionend:u[6]||(u[6]=A=>q.value=!1),onKeydown:a,onBlur:N,onFocus:M},null,8,["modelValue","autosize","placeholder"]),n("div",Gt,[y(C,{disabled:!o(g)||s.loading,type:"primary",style:{height:"40px"},onClick:u[7]||(u[7]=A=>v("enter",o(g)))},{default:i(()=>[y(d,{name:"el-icon-Promotion",size:20}),u[12]||(u[12]=k(" 发送 "))]),_:1},8,["disabled"])])],512)])])}}}),xo=G(Qt,[["__scopeId","data-v-db73beda"]]);export{wo as _,ko as a,jt as b,xo as c};
