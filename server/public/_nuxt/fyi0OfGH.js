import{_ as f}from"./DMHEbzLi.js";import{_ as x}from"./0Wys-gaz.js";import{E as b}from"./DAgm18qP.js";import{u as v,a as h}from"./DioAmjF5.js";import{u as k}from"./Bb2-23m7.js";import{l as C,M as t,N as a,u as o,_ as g,O as i,Z as s,X as w,a0 as y}from"./Dp9aCaJ6.js";import{_ as B}from"./DlAUqK2U.js";import"./CfiDmG6E.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CiYvFM4x.js";import"./Dh2YTmLQ.js";import"./yC1JkMCN.js";const V={key:0,class:"px-[10px]"},E={class:"flex-1 min-w-0 mx-[5px]"},N=C({__name:"select-dub",setup($){const e=v(),{changeTabs:r}=h(),{play:l,audioPlaying:n,pause:m}=k(),p=()=>{e.dub.VoiceUrl?n.value?m():l(e.dub.VoiceUrl):r("dub")},u=()=>{e.dub={}};return(z,D)=>{const c=f,_=x,d=b;return t(),a("div",{class:"h-[40px] px-[10px] max-w-[220px] flex items-center shadow-[0_2px_6px_#ebefff] rounded-full bg-white cursor-pointer",onClick:p},[o(e).dub.Voice?(t(),a(g,{key:1},[i("div",{class:w(["flex",{playing:o(n)}])},[s(c,{name:"local-icon-dub",size:"18"})],2),i("div",E,[s(_,{content:`${o(e).dub.Name}-${o(e).dub.Desc}`},null,8,["content"])]),s(d,{link:"",onClick:u},{default:y(()=>[s(c,{name:"el-icon-Close",size:"20"})]),_:1})],64)):(t(),a("div",V,"选择配音"))])}}}),H=B(N,[["__scopeId","data-v-ad3e9966"]]);export{H as default};
