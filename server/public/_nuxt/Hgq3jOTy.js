import{H as V,l as U,m as d,b as S,c as F,F as _,M as i,N as v,O as T,u as a,X as u,ai as j,a1 as c,a0 as p,a3 as y,a4 as r,a7 as E,Z as Q,V as K,a2 as x,a9 as ee,n as ae,aF as z}from"./Dp9aCaJ6.js";import{K as ie,cs as te,al as I,X as ne,Y as se,ap as C,aq as w,Z as N,aM as P,aS as B,aO as le,S as oe,M as ce,aP as re,aN as ue,aw as de,J as h,cg as ve,O as fe,$ as pe,aR as he,U as me,P as ye}from"./CzTOiozM.js";const be=ie({modelValue:{type:[Boolean,String,Number],default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},size:{type:String,validator:te},width:{type:[String,Number],default:""},inlinePrompt:{type:Boolean,default:!1},inactiveActionIcon:{type:I},activeActionIcon:{type:I},activeIcon:{type:I},inactiveIcon:{type:I},activeText:{type:String,default:""},inactiveText:{type:String,default:""},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},name:{type:String,default:""},validateEvent:{type:Boolean,default:!0},beforeChange:{type:ne(Function)},id:String,tabindex:{type:[String,Number]},label:{type:String,default:void 0},...se(["ariaLabel"])}),ge={[C]:l=>w(l)||V(l)||N(l),[P]:l=>w(l)||V(l)||N(l),[B]:l=>w(l)||V(l)||N(l)},ke=["onClick"],Ie=["id","aria-checked","aria-disabled","aria-label","name","true-value","false-value","disabled","tabindex","onKeydown"],we=["aria-hidden"],Ve=["aria-hidden"],Se=["aria-hidden"],L="ElSwitch",Te=U({name:L}),Ee=U({...Te,props:be,emits:ge,setup(l,{expose:$,emit:f}){const t=l,{formItem:b}=le(),H=oe(),n=ce("switch"),{inputId:R}=re(t,{formItemContext:b}),g=ue(d(()=>t.loading)),A=S(t.modelValue!==!1),m=S(),X=S(),Z=d(()=>[n.b(),n.m(H.value),n.is("disabled",g.value),n.is("checked",s.value)]),q=d(()=>[n.e("label"),n.em("label","left"),n.is("active",!s.value)]),G=d(()=>[n.e("label"),n.em("label","right"),n.is("active",s.value)]),J=d(()=>({width:pe(t.width)}));F(()=>t.modelValue,()=>{A.value=!0});const M=d(()=>A.value?t.modelValue:!1),s=d(()=>M.value===t.activeValue);[t.activeValue,t.inactiveValue].includes(M.value)||(f(C,t.inactiveValue),f(P,t.inactiveValue),f(B,t.inactiveValue)),F(s,e=>{var o;m.value.checked=e,t.validateEvent&&((o=b==null?void 0:b.validate)==null||o.call(b,"change").catch(Y=>he()))});const k=()=>{const e=s.value?t.inactiveValue:t.activeValue;f(C,e),f(P,e),f(B,e),ae(()=>{m.value.checked=s.value})},O=()=>{if(g.value)return;const{beforeChange:e}=t;if(!e){k();return}const o=e();[z(o),w(o)].includes(!0)||me(L,"beforeChange must return type `Promise<boolean>` or `boolean`"),z(o)?o.then(D=>{D&&k()}).catch(D=>{}):o&&k()},W=()=>{var e,o;(o=(e=m.value)==null?void 0:e.focus)==null||o.call(e)};return _(()=>{m.value.checked=s.value}),de({from:"label",replacement:"aria-label",version:"2.8.0",scope:"el-switch",ref:"https://element-plus.org/en-US/component/switch.html"},d(()=>!!t.label)),$({focus:W,checked:s}),(e,o)=>(i(),v("div",{class:u(a(Z)),onClick:ee(O,["prevent"])},[T("input",{id:a(R),ref_key:"input",ref:m,class:u(a(n).e("input")),type:"checkbox",role:"switch","aria-checked":a(s),"aria-disabled":a(g),"aria-label":e.label||e.ariaLabel,name:e.name,"true-value":e.activeValue,"false-value":e.inactiveValue,disabled:a(g),tabindex:e.tabindex,onChange:k,onKeydown:j(O,["enter"])},null,42,Ie),!e.inlinePrompt&&(e.inactiveIcon||e.inactiveText)?(i(),v("span",{key:0,class:u(a(q))},[e.inactiveIcon?(i(),c(a(h),{key:0},{default:p(()=>[(i(),c(y(e.inactiveIcon)))]),_:1})):r("v-if",!0),!e.inactiveIcon&&e.inactiveText?(i(),v("span",{key:1,"aria-hidden":a(s)},E(e.inactiveText),9,we)):r("v-if",!0)],2)):r("v-if",!0),T("span",{ref_key:"core",ref:X,class:u(a(n).e("core")),style:x(a(J))},[e.inlinePrompt?(i(),v("div",{key:0,class:u(a(n).e("inner"))},[e.activeIcon||e.inactiveIcon?(i(),c(a(h),{key:0,class:u(a(n).is("icon"))},{default:p(()=>[(i(),c(y(a(s)?e.activeIcon:e.inactiveIcon)))]),_:1},8,["class"])):e.activeText||e.inactiveText?(i(),v("span",{key:1,class:u(a(n).is("text")),"aria-hidden":!a(s)},E(a(s)?e.activeText:e.inactiveText),11,Ve)):r("v-if",!0)],2)):r("v-if",!0),T("div",{class:u(a(n).e("action"))},[e.loading?(i(),c(a(h),{key:0,class:u(a(n).is("loading"))},{default:p(()=>[Q(a(ve))]),_:1},8,["class"])):a(s)?K(e.$slots,"active-action",{key:1},()=>[e.activeActionIcon?(i(),c(a(h),{key:0},{default:p(()=>[(i(),c(y(e.activeActionIcon)))]),_:1})):r("v-if",!0)]):a(s)?r("v-if",!0):K(e.$slots,"inactive-action",{key:2},()=>[e.inactiveActionIcon?(i(),c(a(h),{key:0},{default:p(()=>[(i(),c(y(e.inactiveActionIcon)))]),_:1})):r("v-if",!0)])],2)],6),!e.inlinePrompt&&(e.activeIcon||e.activeText)?(i(),v("span",{key:1,class:u(a(G))},[e.activeIcon?(i(),c(a(h),{key:0},{default:p(()=>[(i(),c(y(e.activeIcon)))]),_:1})):r("v-if",!0),!e.activeIcon&&e.activeText?(i(),v("span",{key:1,"aria-hidden":!a(s)},E(e.activeText),9,Se)):r("v-if",!0)],2)):r("v-if",!0)],10,ke))}});var Ne=fe(Ee,[["__file","switch.vue"]]);const Be=ye(Ne);export{Be as E};
