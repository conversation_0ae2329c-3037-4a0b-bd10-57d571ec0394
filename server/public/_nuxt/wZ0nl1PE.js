import{_ as $,a as P}from"./CxfT0z4n.js";import{E as j}from"./L7ewHh_h.js";import{u as D,_ as F}from"./gexBVUvT.js";import{_ as U}from"./B_BP6MI7.js";import{E as V}from"./BzMu647W.js";import{j as G,v as q}from"./BQ-RMI0l.js";/* empty css        */import{b as H}from"./Bu_nKEGp.js";import{l as O,j as b,b as S,r as X,F as Z,c as A,k as J,M as a,N as n,O as o,_ as K,aq as Q,aa as w,u as r,Z as p,a0 as W,a4 as Y,X as tt,a7 as et}from"./Dp9aCaJ6.js";import{_ as st}from"./DlAUqK2U.js";import"./DdkLcgv7.js";import"./Ci86EFhe.js";import"./BBQxRZuk.js";import"./BOZQs96k.js";import"./DhTLUUeS.js";import"./DCTLXrZ8.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./D49putpZ.js";import"./djnMiA6p.js";import"./C1aUxTFE.js";import"./Cv6HhfEG.js";import"./DpOQaFAg.js";import"./IZ5UIo3-.js";import"./DzZ0m6Bt.js";/* empty css        *//* empty css        */import"./DjwCd26w.js";import"./B3UVgHox.js";import"./De57gGYj.js";import"./9Bti1uB6.js";/* empty css        */import"./CcPlX2kz.js";import"./CMYCTDrJ.js";import"./8yhmlZGP.js";import"./DlKZEFPo.js";const ot={class:"h-full bg-body rounded-[12px] p-[16px] flex flex-col"},it={class:"flex-1 min-h-0 flex flex-col"},at={class:"category-lists"},nt=["onClick"],lt={class:"flex-1 min-h-0"},ct={key:0,class:"h-full flex flex-col"},rt={class:"flex-1 min-h-0 flex"},pt={class:"flex-1 min-w-0 h-full"},mt={"infinite-scroll-distance":"50"},dt={class:"mt-[16px]"},ut={key:1,class:"h-full flex flex-col items-center justify-center"},ft={class:"text-tx-secondary"},_t=O({__name:"index",setup(gt,{expose:N}){G();const i=b(),k=[{name:"全部",type:-1},{name:"生成中",type:1},{name:"生成成功",type:2},{name:"生成失败",type:3}],{getMusic:v,currentId:u,pause:R}=D(),m=S(-1),t=X({pageNo:1,count:0,pageSize:15,loading:!0,lists:[]}),f=b(),y=S(0),T=async()=>{clearTimeout(f.value);const s=t.lists.filter(e=>e.status===1).map(e=>e.id);s.length>0&&(f.value=setTimeout(()=>{g()},6e3)),s.length!==y.value&&v(),y.value=s.length},_=async()=>{try{const s=await H({status:m.value,page_no:t.pageNo,page_size:t.pageSize});t.count=s.count,t.pageNo===1&&(t.lists=[]),t.lists.push(...s.lists)}catch{}finally{t.loading=!1,T()}};v();const z=()=>{t.count>=t.pageNo*t.pageSize&&(t.pageNo++,_())},g=async()=>{t.pageSize=t.pageNo*t.pageSize,t.pageNo=1,await _()},x=async()=>{t.loading=!0,t.pageSize=15,t.pageNo=1,await _()},h=()=>{const s=t.lists.find(e=>e.status===2);s&&(u.value=s.id)},B=async s=>{R(),m.value=s,u.value=-1,await x(),h()};return N({refresh:async()=>{m.value=-1,await g(),i.value.setScrollTop(0)}}),Z(async()=>{await x(),h()}),A(u,s=>{if(i.value){const e=document.getElementById(`music-item-${s}`);if(!e)return;const l=e==null?void 0:e.getBoundingClientRect(),c=i.value.wrapRef.getBoundingClientRect();l.top<c.top&&i.value.setScrollTop(e==null?void 0:e.offsetTop),l.bottom>c.bottom&&i.value.setScrollTop((e==null?void 0:e.offsetTop)-c.height+l.height)}}),J(()=>{clearTimeout(f.value)}),(s,e)=>{const l=$,c=j,C=P,M=F,E=U,I=V,L=q;return a(),n("div",ot,[e[2]||(e[2]=o("div",{class:"text-2xl font-bold border-b border-solid border-br-light pb-[16px]"}," 生成记录 ",-1)),o("div",it,[o("div",at,[(a(),n(K,null,Q(k,(d,vt)=>o("div",{key:d.type},[o("div",{class:tt(["category-item bg-white",{"is-active":r(m)===d.type}]),onClick:yt=>B(d.type)},et(d.name),11,nt)])),64))]),w((a(),n("div",lt,[r(t).lists.length?(a(),n("div",ct,[o("div",rt,[o("div",pt,[p(c,{ref_key:"scrollBarRef",ref:i},{default:W(()=>[w((a(),n("div",mt,[p(l,{"music-list":r(t).lists,onUpdate:g},null,8,["music-list"])])),[[I,z]])]),_:1},512)]),p(C)]),o("div",dt,[p(M,{ref:"musicPlayerRef",class:"bg-page rounded-[12px]"},null,512)])])):r(t).loading?Y("",!0):(a(),n("div",ut,[o("div",ft,[p(E,{size:45,name:"local-icon-music1"})]),e[0]||(e[0]=o("div",{class:"my-[10px]"},"当前还没有音乐哦",-1)),e[1]||(e[1]=o("div",{class:"text-tx-secondary text-sm"}," 在左侧输入描述，创建你的作品吧！ ",-1))]))])),[[L,r(t).loading]])])])}}}),se=st(_t,[["__scopeId","data-v-1898024f"]]);export{se as default};
