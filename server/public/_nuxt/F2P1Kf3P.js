import{_ as D}from"./BWd1nnnI.js";import{E as L}from"./CMsEk3FA.js";import{E as R}from"./BSocHp_O.js";import{E as H}from"./dIlF1NYp.js";import{E as j}from"./C7WF4igz.js";import{cn as q}from"./DNaNbs6R.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{u as F}from"./B5Q3FFxa.js";import{_ as I}from"./CIwHQzCS.js";import{ModelEnums as v,TypeEnums as f}from"./BLV0QRdm.js";import{l as P,b as E,m as M,c as W,M as d,N as y,Z as s,a0 as a,O as t,X as T,a7 as b,u as l,a1 as X,y as h,a2 as Y,_ as k,aq as Z,a6 as G}from"./Dp9aCaJ6.js";const J={class:"flex items-center justify-center"},K={class:"ml-1"},Q={class:"py-[10px]"},ee={key:1,class:"flex items-center"},te={class:"flex items-center cursor-pointer"},le={class:"flex text-primary"},oe={class:"px-[6px]"},se={class:"py-[10px]"},ae=["onMouseover"],ne={class:"py-[6px]"},re={class:"border-l border-solid border-br-light ml-[8px] pl-[8px]"},Me=P({__name:"search-model",props:{type:{},mode:{default:"segmented"},model:{}},emits:["update:model","update:type"],setup(V,{emit:C}){const B=V,N=C,[S,x]=F(),{type:c,model:n}=q(B,N),i=E(!1),m=E(""),p=[{label:"基础",value:v.BASE,icon:"local-icon-search_base"},{label:"增强",value:v.ENHANCE,icon:"local-icon-search_copilot",desc:"检索更多网页，提供更全面个性化答案"},{label:"研究",value:v.STUDY,icon:"local-icon-search_research",desc:"结构更细致，内容更深入。自动总结大纲和图谱，答案更清晰"}],w=[{label:"全网",value:f.ALL},{label:"文档",value:f.DOC},{label:"学术",value:f.SCHOLAR}],O=M(()=>w.find(o=>o.value==c.value)||{}),g=M(()=>p.find(o=>o.value==n.value)||{});return W(i,()=>{m.value=""}),(u,o)=>{const _=D,U=L,$=R,z=H,A=j;return d(),y(k,null,[s(l(S),null,{default:a(({item:e,select:r})=>[t("div",J,[t("span",{class:T({"text-primary":!r})},[s(_,{size:"15",name:e.icon},null,8,["name"])],2),t("div",K,b(e.label),1)])]),_:1}),u.mode=="segmented"?(d(),X($,{key:0,modelValue:l(n),"onUpdate:modelValue":o[0]||(o[0]=e=>h(n)?n.value=e:null),options:p,style:Y({width:`${p.length*90}px`,"--el-border-radius-base":"10px","--el-segmented-color":"var(--el-text-color-primary)"})},{default:a(({item:e})=>[s(U,{effect:"dark",content:e.desc,disabled:!e.desc,placement:"top"},{default:a(()=>[t("div",Q,[s(l(x),{item:e,select:e.value==l(n)},null,8,["item","select"])])]),_:2},1032,["content","disabled"])]),_:1},8,["modelValue","style"])):(d(),y("div",ee,[s(z,{placement:"bottom",trigger:"click",width:120,"popper-style":{minWidth:"120px",padding:0},visible:l(i),"onUpdate:visible":o[2]||(o[2]=e=>h(i)?i.value=e:null)},{reference:a(()=>[t("div",te,[t("span",le,[s(_,{name:l(g).icon},null,8,["name"])]),t("span",oe,b(l(g).label),1),s(_,{name:"el-icon-CaretBottom"})])]),default:a(()=>[t("div",se,[(d(),y(k,null,Z(p,e=>t("div",{key:e.value,class:T({"bg-primary-light-9":l(m)==e.value}),onMouseover:r=>m.value=e.value},[s(I,{model:e.value,type:l(c),"onUpdate:type":[o[1]||(o[1]=r=>h(c)?c.value=r:null),r=>n.value=e.value],trigger:"hover",placement:"right"},{item:a(({icon:r,label:ce})=>[t("div",ne,[s(l(x),{class:"px-[10px]",item:e,select:!1},null,8,["item"])])]),_:2},1032,["model","type","onUpdate:type"])],42,ae)),64))])]),_:1},8,["visible"]),t("div",re,[s(A,{type:"primary",size:"small"},{default:a(()=>[G(b(l(O).label),1)]),_:1})])]))],64)}}});export{Me as _};
