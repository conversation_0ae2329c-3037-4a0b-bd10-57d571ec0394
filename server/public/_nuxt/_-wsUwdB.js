import{E as j}from"./BNkFx8AD.js";import{E as z,v as A,f as M}from"./B5S_Er7H.js";import{E as q,a as H}from"./Dg0Y74mO.js";import{_ as O}from"./C8ZjkaO0.js";import{E as Z,a as G,b as J}from"./BgJCO7ll.js";import{_ as K}from"./CcbLKbjB.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import{u as Q}from"./67xbGseh.js";import{_ as W}from"./CyL3Z9ij.js";import{_ as X}from"./Chbk99gu.js";import{_ as Y}from"./C11IzZAw.js";import{w as ee,y as te,A as ae,z as oe,x as ne}from"./Bo3PTL3c.js";import{l as se,j as y,C as le,r as ie,c as re,M as h,N as me,Z as t,O as c,a0 as o,a6 as r,aa as pe,u as d,a1 as de,y as _e,_ as fe}from"./Dp9aCaJ6.js";const ce={class:"mt-4"},ue={class:"mt-4"},ge={class:"flex items-center"},ye={class:"flex justify-end mt-4"},Ue=se({__name:"oa",props:{appId:{}},emits:["back"],setup(R,{emit:E}){const w=R,x=E,u=y(),_=y(),k=y(),{appId:S}=le(w),f=ie({robot_id:S,type:3}),{pager:m,getLists:s}=Q({fetchFun:ee,params:f});s();const $=async a=>{await M.confirm("确定删除？"),await ne({id:a,type:f.type}),s()},B=(a,e)=>{switch(a){case"delete":$(e.id);break;case"edit":v(e);break;case"usage":V(e)}},v=a=>{var n;let e=null;a&&(e={id:a.id,name:a.name,password:a.secret}),(n=u.value)==null||n.open(e)},I=async(a,e)=>{var n;await(e=="add"?te({...a,...f}):ae({id:a.id,name:a.name,password:a.password})),(n=u.value)==null||n.close(),s()},V=a=>{var e,n;(e=_.value)==null||e.open(),(n=_.value)==null||n.setFormData(a)},D=async a=>{var e;await oe({...a,...f}),(e=_.value)==null||e.close(),s()};return re(()=>w.appId,()=>{s()}),(a,e)=>{const n=j,l=z,p=q,N=O,g=Z,P=G,T=J,F=H,L=K,U=A;return h(),me(fe,null,[t(n,{content:"发布微信公众号",onBack:e[0]||(e[0]=i=>x("back"))}),c("div",ce,[t(l,{type:"primary",onClick:e[1]||(e[1]=i=>v())},{default:o(()=>e[3]||(e[3]=[r(" 创建链接 ")])),_:1})]),c("div",ue,[pe((h(),de(F,{data:d(m).lists,size:"large"},{default:o(()=>[t(p,{label:"apikey",prop:"apikey","min-width":"200"}),t(p,{label:"分享名称",prop:"name","min-width":"180","show-tooltip-when-overflow":""}),t(p,{label:"访问密码",prop:"secret","min-width":"120"}),t(p,{label:"最后使用时间",prop:"use_time","min-width":"180"}),t(p,{label:"操作","min-width":"150",fixed:"right"},{default:o(({row:i})=>[c("div",ge,[t(l,{type:"primary",link:"",onClick:C=>{var b;return(b=d(k))==null?void 0:b.open(i.apikey)}},{default:o(()=>e[4]||(e[4]=[r(" 公众号配置 ")])),_:2},1032,["onClick"]),t(T,{class:"ml-[10px]",onCommand:C=>B(C,i)},{dropdown:o(()=>[t(P,null,{default:o(()=>[t(g,{command:"edit"},{default:o(()=>[t(l,{type:"primary",link:""},{default:o(()=>e[6]||(e[6]=[r(" 编辑 ")])),_:1})]),_:1}),t(g,{command:"usage"},{default:o(()=>[t(l,{type:"primary",link:""},{default:o(()=>e[7]||(e[7]=[r(" 用量设置 ")])),_:1})]),_:1}),t(g,{command:"delete"},{default:o(()=>[t(l,{type:"danger",link:""},{default:o(()=>e[8]||(e[8]=[r(" 删除 ")])),_:1})]),_:1})]),_:1})]),default:o(()=>[t(l,{type:"primary",link:""},{default:o(()=>[e[5]||(e[5]=r(" 更多 ")),t(N,{name:"el-icon-ArrowDown"})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[U,d(m).loading]]),c("div",ye,[t(L,{modelValue:d(m),"onUpdate:modelValue":e[2]||(e[2]=i=>_e(m)?m.value=i:null),onChange:d(s)},null,8,["modelValue","onChange"])])]),t(W,{ref_key:"createShareRef",ref:u,isShowChatType:!1,onConfirm:I},null,512),t(X,{ref_key:"usageSettingsRef",ref:_,onConfirm:D},null,512),t(Y,{ref_key:"oaConfigRef",ref:k},null,512)],64)}}});export{Ue as _};
