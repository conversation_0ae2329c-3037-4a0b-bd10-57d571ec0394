import{E as b,a as k,b as w,d as E,c as C}from"./BU8eL0b4.js";import{b as M,j as z,cR as A,bw as B,cs as D,dy as c}from"./B5S_Er7H.js";import F from"./CWy7b5cc.js";import H from"./hmX2zmjB.js";import{_ as L}from"./DX8FEhqh.js";import{_ as N}from"./q094uUzC.js";import R from"./BN47M2xe.js";import{_ as V}from"./CHELiId6.js";import{l as I,m as P,F as T,ar as j,M as m,a1 as a,a0 as e,Z as t,V as p,a4 as s,as as U,X as W,u as n,a2 as X}from"./Dp9aCaJ6.js";import{_ as Z}from"./DlAUqK2U.js";import"./u53ZDtaC.js";import"./DuLVFwrw.js";import"./wD_qwxyk.js";import"./CN8DIg3d.js";import"./C8ZjkaO0.js";import"./D2fgxdmN.js";/* empty css        */import"./D7GZITCA.js";import"./BNZYpAgs.js";import"./Dv--ZGyq.js";import"./DbRYflOL.js";import"./DCTLXrZ8.js";import"./BgJCO7ll.js";import"./DySxZBuW.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./Cwajr1uY.js";import"./pttW598Y.js";import"./DtKGz7pg.js";import"./CnBUfao0.js";import"./Cx3Arr0P.js";import"./CacOB3dg.js";import"./GM0TYtnE.js";import"./Tox-HrLw.js";/* empty css        */import"./ChwnGhEb.js";import"./BE7GAo-z.js";import"./Cr5mhGPR.js";import"./DnwAmygV.js";import"./CqoCrSl4.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";/* empty css        */import"./Bn8r6RG7.js";import"./BMOEXvyH.js";import"./BPrqYaQ_.js";import"./WBO1DCcA.js";import"./IItDHZjE.js";/* empty css        */import"./l0sNRNKZ.js";import"./D1Rl-T7B.js";import"./B8Nk-MqZ.js";import"./DBEz5tOh.js";import"./BRLWcgZ9.js";import"./6j5TCJew.js";import"./emCILI1b.js";import"./Cv6HhfEG.js";import"./BuewU-zc.js";import"./CJKnabG6.js";import"./Db173UeR.js";import"./Di6b97gu.js";import"./BzS1o01N.js";import"./BmXQ27ak.js";import"./BMw6WEOu.js";/* empty css        *//* empty css        *//* empty css        */import"./DfJK8teh.js";import"./CndViIJ9.js";import"./COuBjszG.js";import"./BldjV937.js";import"./JUoEhkUF.js";import"./BPme_Qo5.js";import"./CD0i1So6.js";import"./DnZ4-MhY.js";import"./DLt6fKEW.js";const q=I({__name:"default",setup(G){const f=M(),h=z(),d=A(),g=B();P(()=>f.isMobile?{"--header-height":"50px","--main-padding":"12px"}:{"--main-padding":"15px"});const{height:l}=D(),u=()=>{g.value=d.isDark,d.setTheme()};return T(()=>{u()}),j(()=>{u()}),(o,J)=>{const y=b,v=k,S=w,$=E,i=C;return m(),a(i,{class:"bg-body h-full layout-default",style:X([{height:`${n(l)=="Infinity"?"100vh":n(l)+"px"}`}])},{default:e(()=>[t(y,{height:"var(--header-height)",style:{padding:"0"}},{default:e(()=>[t(F,null,{default:e(()=>{var r;return[(r=o.$slots)!=null&&r.header?p(o.$slots,"header",{key:0},void 0,!0):s("",!0)]}),_:3})]),_:3}),t(i,{class:"min-h-0"},{default:e(()=>{var r;return[t(v,{width:"auto",class:"!overflow-visible"},{default:e(()=>{var _;return[t(H,null,U({_:2},[(_=o.$slots)!=null&&_.aside?{name:"aside",fn:e(()=>[p(o.$slots,"aside",{},void 0,!0)]),key:"0"}:void 0]),1024)]}),_:3}),t(i,{class:W(["overflow-hidden layout-bg rounded-[12px]",{"":(r=o.$slots)==null?void 0:r.aside,"!rounded-none ":(o._.provides[c]||o.$route).meta.hiddenRounded}])},{default:e(()=>[t(S,{class:"scrollbar",style:{padding:"0"}},{default:e(()=>[p(o.$slots,"default",{},void 0,!0)]),_:3}),(o._.provides[c]||o.$route).meta.hiddenFooter?s("",!0):(m(),a($,{key:0,height:"auto"},{default:e(()=>[t(L)]),_:1}))]),_:3},8,["class"])]}),_:3}),n(h).showLogin?(m(),a(N,{key:0})):s("",!0),t(R),t(V)]),_:3},8,["style"])}}}),St=Z(q,[["__scopeId","data-v-1a51c74d"]]);export{St as default};
