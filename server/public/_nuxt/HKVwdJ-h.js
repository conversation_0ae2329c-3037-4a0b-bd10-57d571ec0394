import{K as V,X as S,a4 as F,ap as A,aM as H,Z as j,b0 as g,M as w,O as M,aG as G,a8 as W,J as X,P as Z,Q as q}from"./BsMqt_su.js";import{H as z,I as J,b as K,c as L,q as Q,m as r,l as _,M as T,N as B,V as N,X as m,u as e,i as U,O as y,Z as E,a0 as $,ai as R,a9 as Y,aa as ee,ab as se,a6 as ae,a7 as te}from"./Dp9aCaJ6.js";import{_ as le}from"./BeID4H0V.js";const x=s=>j(s)||z(s)||J(s),oe=V({accordion:Boolean,modelValue:{type:S([Array,String,Number]),default:()=>F([])}}),ne={[A]:x,[H]:x},D=Symbol("collapseContextKey"),ie=(s,n)=>{const t=K(g(s.modelValue)),i=l=>{t.value=l;const o=s.accordion?t.value[0]:t.value;n(A,o),n(H,o)},a=l=>{if(s.accordion)i([t.value[0]===l?"":l]);else{const o=[...t.value],c=o.indexOf(l);c>-1?o.splice(c,1):o.push(l),i(o)}};return L(()=>s.modelValue,()=>t.value=g(s.modelValue),{deep:!0}),Q(D,{activeNames:t,handleItemClick:a}),{activeNames:t,setActiveNames:i}},ce=()=>{const s=w("collapse");return{rootKls:r(()=>s.b())}},re=_({name:"ElCollapse"}),de=_({...re,props:oe,emits:ne,setup(s,{expose:n,emit:t}){const i=s,{activeNames:a,setActiveNames:l}=ie(i,t),{rootKls:o}=ce();return n({activeNames:a,setActiveNames:l}),(c,u)=>(T(),B("div",{class:m(e(o))},[N(c.$slots,"default")],2))}});var ue=M(de,[["__file","collapse.vue"]]);const pe=V({title:{type:String,default:""},name:{type:S([String,Number]),default:void 0},disabled:Boolean}),me=s=>{const n=U(D),{namespace:t}=w("collapse"),i=K(!1),a=K(!1),l=G(),o=r(()=>l.current++),c=r(()=>{var C;return(C=s.name)!=null?C:`${t.value}-id-${l.prefix}-${e(o)}`}),u=r(()=>n==null?void 0:n.activeNames.value.includes(e(c)));return{focusing:i,id:o,isActive:u,handleFocus:()=>{setTimeout(()=>{a.value?a.value=!1:i.value=!0},50)},handleHeaderClick:()=>{s.disabled||(n==null||n.handleItemClick(e(c)),i.value=!1,a.value=!0)},handleEnterClick:()=>{n==null||n.handleItemClick(e(c))}}},ve=(s,{focusing:n,isActive:t,id:i})=>{const a=w("collapse"),l=r(()=>[a.b("item"),a.is("active",e(t)),a.is("disabled",s.disabled)]),o=r(()=>[a.be("item","header"),a.is("active",e(t)),{focusing:e(n)&&!s.disabled}]),c=r(()=>[a.be("item","arrow"),a.is("active",e(t))]),u=r(()=>a.be("item","wrap")),v=r(()=>a.be("item","content")),f=r(()=>a.b(`content-${e(i)}`)),b=r(()=>a.b(`head-${e(i)}`));return{arrowKls:c,headKls:o,rootKls:l,itemWrapperKls:u,itemContentKls:v,scopedContentId:f,scopedHeadId:b}},fe=["id","aria-expanded","aria-controls","aria-describedby","tabindex"],be=["id","aria-hidden","aria-labelledby"],Ce=_({name:"ElCollapseItem"}),he=_({...Ce,props:pe,setup(s,{expose:n}){const t=s,{focusing:i,id:a,isActive:l,handleFocus:o,handleHeaderClick:c,handleEnterClick:u}=me(t),{arrowKls:v,headKls:f,rootKls:b,itemWrapperKls:C,itemContentKls:P,scopedContentId:I,scopedHeadId:k}=ve(t,{focusing:i,isActive:l,id:a});return n({isActive:l}),(h,d)=>(T(),B("div",{class:m(e(b))},[y("button",{id:e(k),class:m(e(f)),"aria-expanded":e(l),"aria-controls":e(I),"aria-describedby":e(I),tabindex:h.disabled?-1:0,type:"button",onClick:d[0]||(d[0]=(...p)=>e(c)&&e(c)(...p)),onKeydown:d[1]||(d[1]=R(Y((...p)=>e(u)&&e(u)(...p),["stop","prevent"]),["space","enter"])),onFocus:d[2]||(d[2]=(...p)=>e(o)&&e(o)(...p)),onBlur:d[3]||(d[3]=p=>i.value=!1)},[N(h.$slots,"title",{},()=>[ae(te(h.title),1)]),E(e(X),{class:m(e(v))},{default:$(()=>[E(e(W))]),_:1},8,["class"])],42,fe),E(e(le),null,{default:$(()=>[ee(y("div",{id:e(I),role:"region",class:m(e(C)),"aria-hidden":!e(l),"aria-labelledby":e(k)},[y("div",{class:m(e(P))},[N(h.$slots,"default")],2)],10,be),[[se,e(l)]])]),_:3})],2))}});var O=M(he,[["__file","collapse-item.vue"]]);const Ee=Z(ue,{CollapseItem:O}),Ke=q(O);export{Ke as E,Ee as a};
