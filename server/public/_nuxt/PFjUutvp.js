import{c as s}from"./BYCNz_16.js";import{_ as r}from"./DlAUqK2U.js";import{N as o,O as e,M as c}from"./Dp9aCaJ6.js";import"./BBthjZaB.js";const l={},a={class:"h-full flex flex-col w-full items-center justify-center py-[100px] bg-body"};function m(n,t){return c(),o("div",a,t[0]||(t[0]=[e("img",{class:"w-[120px] h-[120px]",src:s,alt:""},null,-1),e("div",{class:"my-[16px] font-medium"},"生成结果会在显示这",-1),e("div",{class:"text-tx-regular text-sm"}," 在左侧填好必要的信息， 点击【生成】按钮，静待生成结果，一般在30秒内搞定 ",-1)]))}const d=r(l,[["render",m]]);export{d as default};
