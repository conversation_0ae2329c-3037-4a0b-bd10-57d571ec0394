import{b as _,cm as p,_ as d}from"./DNaNbs6R.js";import u from"./Bk9g7MLx.js";import{l as c,b as m,M as r,N as i,Z as t,a0 as f,O as e,u as x,y as h}from"./Dp9aCaJ6.js";import{_ as v}from"./DlAUqK2U.js";import"./BWd1nnnI.js";const N={class:"flex h-full"},V={class:"h-full px-[16px] py-[16px]"},y={class:"flex-1 min-w-0 h-full"},b={class:"mx-auto py-[16px] rounded-lg pr-[16px] h-full"},w=c({__name:"index",setup(B){_();const o=m(!1);return(g,s)=>{const a=p,n=d;return r(),i("div",null,[t(n,{name:"default"},{default:f(()=>[e("div",N,[e("div",V,[t(u,{modelValue:x(o),"onUpdate:modelValue":s[0]||(s[0]=l=>h(o)?o.value=l:null)},null,8,["modelValue"])]),e("div",y,[e("div",b,[t(a)])])])]),_:1})])}}}),E=v(w,[["__scopeId","data-v-fbfc5138"]]);export{E as default};
