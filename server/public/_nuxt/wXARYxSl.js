import{E as H}from"./L7ewHh_h.js";import{_ as J}from"./B_BP6MI7.js";import{E as M,e as K,s as Q}from"./BQ-RMI0l.js";/* empty css        */import{u as R}from"./67xbGseh.js";import{u as W}from"./CcPlX2kz.js";import{l as Y,b as _,r as ee,M as r,N as c,Z as i,a0 as n,O as t,u as o,a6 as A,a4 as E,_ as U,aq as I,a7 as g,X as se,a9 as m}from"./Dp9aCaJ6.js";import{g as te,p as le}from"./DwFObZc_.js";import{E as h}from"./CKM_VzYi.js";import{E as oe}from"./t-5G02zu.js";import{E as ie}from"./De57gGYj.js";import{E as N}from"./C9VRQ5aC.js";import{E as ae}from"./BXLvtum9.js";import{_ as ne}from"./DlAUqK2U.js";import"./DpOQaFAg.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";import"./DhTLUUeS.js";import"./BHIHQvwt.js";const de={class:"locality-draw-popup"},re={class:"w-1/2"},ce={class:"px-4"},ue={class:"flex flex-col"},pe={key:0,style:{height:"500px"},class:"flex justify-center items-center text-tx-placeholder"},ve={class:"mt-4"},xe={class:"my-2 px-4"},fe=["onClick"],_e={class:"ml-2 text-xs"},me={key:0,class:"text-tx-placeholder"},he={class:"flex flex-col px-4"},ke={class:"text-base"},ge={class:"flex items-center"},ye={class:"ml-2 text-tx-regular text-xs"},Ce={class:"flex items-center"},we={class:"cursor-pointer"},Ee=["onClick"],Ve=["onClick"],Pe=["onClick"],Se=["onClick"],$e={class:"text-xs"},ze=["onClick"],Me={class:"flex justify-end"},Ae=Y({__name:"add-user",props:{id:{type:Number,default:0}},emits:["success"],setup(B,{expose:L,emit:T}){const D={1:"可管理",2:"可编辑",3:"可查看"},F=T,V=B,x=_(!1),y=_(!1),C=_(!1),P=_(-1),w=ee({keyword:"",kb_id:V.id,page_type:0}),a=_([]),{pager:u,getLists:b,resetPage:S,resetParams:Ue}=R({fetchFun:te,params:w}),j=()=>{S(),b()},$=(l,e)=>{if(l.is_added)return;const f=a.value.findIndex(k=>k.id===l.id);f!==-1?(l.permission=0,a.value.splice(f,1)):l.is_added||(l.permission=3,a.value.push(l)),e==="box"?l.isSelected=l.isSelected?0:1:l.isSelected=l.isSelected?1:0},q=l=>{u.lists.forEach(e=>{e.is_added||(e.isSelected=l?1:0,e.permission=l?3:0)}),a.value=l?u.lists.filter(e=>!e.is_added):[]},O=l=>{a.value=a.value.filter(e=>e.id!==l.id),u.lists.find(e=>e.id===l.id).isSelected=!1},X=l=>{P.value=l,C.value=!0},z=()=>{C.value=!1},p=(l,e)=>{l.permission=e,z()},{lockFn:Z,isLock:G}=W(async()=>{const l={};a.value.forEach(e=>{l[e.sn]=e.permission}),await le({kb_id:V.id,users:l}),F("success"),x.value=!1});return L({show:()=>{x.value=!0,S(),a.value=[],y.value=!1}}),(l,e)=>{const f=H,k=J;return r(),c("div",de,[i(o(ie),{modelValue:x.value,"onUpdate:modelValue":e[3]||(e[3]=s=>x.value=s),width:"980px",class:"!rounded-[12px]",center:!0,draggable:!0,"destroy-on-close":!0,"close-on-click-modal":!1},{header:n(()=>e[4]||(e[4]=[t("div",{class:"w-full text-left"},[t("div",{class:"text-lg font-medium"},"添加成员")],-1)])),footer:n(()=>[t("div",Me,[i(o(M),{onClick:e[2]||(e[2]=s=>x.value=!1)},{default:n(()=>e[8]||(e[8]=[A("取消")])),_:1}),i(o(M),{type:"primary",loading:o(G),onClick:o(Z)},{default:n(()=>e[9]||(e[9]=[A(" 确认 ")])),_:1},8,["loading","onClick"])])]),default:n(()=>[t("div",{class:"flex",onClick:z},[t("div",re,[t("div",ce,[i(o(K),{modelValue:w.keyword,"onUpdate:modelValue":e[0]||(e[0]=s=>w.keyword=s),style:{width:"100%"},size:"large",placeholder:"搜索成员","prefix-icon":o(Q),onInput:j},null,8,["modelValue","prefix-icon"])]),i(f,{height:"500px"},{default:n(()=>[t("div",ue,[o(u).lists.length===0?(r(),c("div",pe," 请搜索成员添加 ")):E("",!0),t("div",ve,[t("div",xe,[i(o(h),{modelValue:y.value,"onUpdate:modelValue":e[1]||(e[1]=s=>y.value=s),"true-value":1,"false-value":0,label:"全选",size:"large",onChange:q},null,8,["modelValue"])]),o(u).lists.length!==0?(r(!0),c(U,{key:0},I(o(u).lists,s=>(r(),c("div",{class:se(["my-4 mr-4 py-2 px-4 flex items-center cursor-pointer hover:bg-primary-light-9 rounded-[12px]",{"!cursor-not-allowed":s.is_added}]),key:s.id,onClick:v=>$(s,"box")},[i(o(h),{modelValue:s.isSelected,"onUpdate:modelValue":v=>s.isSelected=v,"true-value":1,"false-value":0,label:"",size:"large",disabled:s.is_added,onClick:m(v=>$(s,"checkbox"),["stop"])},null,8,["modelValue","onUpdate:modelValue","disabled","onClick"]),i(o(N),{src:s.avatar,size:"26",class:"flex-none ml-2"},null,8,["src"]),t("div",_e,[t("span",null,g(s.nickname),1),s.is_added?(r(),c("div",me," 已添加 ")):E("",!0)])],10,fe))),128)):E("",!0)])])]),_:1})]),i(o(oe),{direction:"vertical",style:{height:"500px"}}),i(f,{height:"500px",class:"w-1/2"},{default:n(()=>[t("div",he,[t("div",ke," 已选择："+g(a.value.length)+" 个 ",1),(r(!0),c(U,null,I(a.value,(s,v)=>(r(),c("div",{class:"mt-4 py-2 px-4 flex items-center justify-between cursor-pointer hover:bg-primary-light-9 rounded-[12px]",key:s.id},[t("div",ge,[i(o(N),{src:s.avatar,size:"26",class:"flex-none ml-2"},null,8,["src"]),t("div",ye,g(s.nickname),1)]),t("div",Ce,[i(o(ae),{placement:"bottom-end",width:380,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:C.value&&P.value===v},{reference:n(()=>[t("div",{class:"flex items-center cursor-pointer",onClick:m(d=>X(v),["stop"])},[t("span",$e,g(D[s.permission]),1),i(k,{name:"el-icon-ArrowDown"})],8,Se)]),default:n(()=>[t("div",we,[t("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(s,2)},[e[5]||(e[5]=t("div",{style:{width:"320px"}},[t("div",{class:"text-base text-tx-primary"}," 可编辑 "),t("div",{class:"text-xs text-tx-placeholder mt-2"}," 只能操作数据学习，增删改查自己的数据，不能修改他人 ")],-1)),i(o(h),{"model-value":s.permission,"true-value":2,label:"",size:"large",onClick:m(d=>p(s,2),["stop"])},null,8,["model-value","true-value","onClick"])],8,Ee),t("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(s,3)},[e[6]||(e[6]=t("div",{style:{width:"320px"}},[t("div",{class:"text-base text-tx-primary"}," 可查看 "),t("div",{class:"text-xs text-tx-placeholder mt-2"}," 查看知识库所有数据 ")],-1)),i(o(h),{"model-value":s.permission,"true-value":3,label:"",size:"large",onClick:m(d=>p(s,3),["stop"])},null,8,["model-value","true-value","onClick"])],8,Ve),t("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(s,1)},[e[7]||(e[7]=t("div",{style:{width:"320px"}},[t("div",{class:"text-base text-tx-primary"}," 可管理 "),t("div",{class:"text-xs text-tx-placeholder mt-2"}," 管理整个知识库数据和信息 ")],-1)),i(o(h),{"model-value":s.permission,"true-value":1,label:"",size:"large",onClick:m(d=>p(s,1),["stop"])},null,8,["model-value","true-value","onClick"])],8,Pe)])]),_:2},1032,["visible"]),t("div",{class:"flex items-center ml-6",onClick:d=>O(s)},[i(k,{name:"el-icon-CloseBold"})],8,ze)])]))),128))])]),_:1})])]),_:1},8,["modelValue"])])}}}),We=ne(Ae,[["__scopeId","data-v-e9085491"]]);export{We as default};
