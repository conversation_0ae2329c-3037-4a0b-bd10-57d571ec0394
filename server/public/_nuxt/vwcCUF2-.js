import{aC as kt,X as I,W as Oe,aq as jt,aD as yt,aE as mn,aF as hn,aG as yn,K as ee,Z as Ze,O as se,M as Se,aH as Ft,aj as $e,aI as _t,Y as Dt,a6 as bn,aJ as bt,ay as wn,P as Lt,af as wt,aK as On,ar as Tn}from"./BBthjZaB.js";import{m as A,U as De,c as _,F as qe,a as En,u as c,j as Cn,b as j,o as Me,ar as Rn,q as Ce,l as L,V as ie,i as le,M as Z,N as He,X as Nt,a2 as An,R as Je,aa as $t,au as Pn,J as xn,_ as Sn,ad as Mn,av as In,Z as Re,a1 as ce,a0 as te,W as Xe,a4 as Ae,I as Bn,t as we,ab as kn,ac as jn,at as Fn,E as _n,d as Dn,a7 as Ln}from"./Dp9aCaJ6.js";import{i as Nn}from"./DCTLXrZ8.js";const Y=(e,t,{checkForDefaultPrevented:n=!0}={})=>r=>{const a=e==null?void 0:e(r);if(n===!1||!a)return t==null?void 0:t(r)},Vr=e=>t=>t.pointerType==="mouse"?e(t):void 0,$n=kt({type:I(Boolean),default:null}),Hn=kt({type:I(Function)}),Wn=e=>{const t=`update:${e}`,n=`onUpdate:${e}`,o=[t],r={[e]:$n,[n]:Hn};return{useModelToggle:({indicator:i,toggleReason:u,shouldHideWhenRouteChanges:s,shouldProceed:l,onShow:f,onHide:d})=>{const y=En(),{emit:g}=y,m=y.props,v=A(()=>De(m[n])),T=A(()=>m[e]===null),p=O=>{i.value!==!0&&(i.value=!0,u&&(u.value=O),De(f)&&f(O))},w=O=>{i.value!==!1&&(i.value=!1,u&&(u.value=O),De(d)&&d(O))},E=O=>{if(m.disabled===!0||De(l)&&!l())return;const R=v.value&&Oe;R&&g(t,!0),(T.value||!R)&&p(O)},h=O=>{if(m.disabled===!0||!Oe)return;const R=v.value&&Oe;R&&g(t,!1),(T.value||!R)&&w(O)},C=O=>{jt(O)&&(m.disabled&&O?v.value&&g(t,!1):i.value!==O&&(O?p():w()))},P=()=>{i.value?h():E()};return _(()=>m[e],C),s&&y.appContext.config.globalProperties.$route!==void 0&&_(()=>({...y.proxy.$route}),()=>{s.value&&i.value&&h()}),qe(()=>{C(m[e])}),{hide:h,show:E,toggle:P,hasUpdateHandler:v}},useModelToggleProps:r,useModelToggleEmits:o}};var H="top",K="bottom",U="right",W="left",Ye="auto",Ie=[H,K,U,W],fe="start",Pe="end",qn="clippingParents",Ht="viewport",be="popper",Kn="reference",Ot=Ie.reduce(function(e,t){return e.concat([t+"-"+fe,t+"-"+Pe])},[]),Qe=[].concat(Ie,[Ye]).reduce(function(e,t){return e.concat([t,t+"-"+fe,t+"-"+Pe])},[]),Un="beforeRead",zn="read",Vn="afterRead",Zn="beforeMain",Jn="main",Gn="afterMain",Xn="beforeWrite",Yn="write",Qn="afterWrite",eo=[Un,zn,Vn,Zn,Jn,Gn,Xn,Yn,Qn];function G(e){return e?(e.nodeName||"").toLowerCase():null}function z(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function de(e){var t=z(e).Element;return e instanceof t||e instanceof Element}function q(e){var t=z(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function et(e){if(typeof ShadowRoot>"u")return!1;var t=z(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function to(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},r=t.attributes[n]||{},a=t.elements[n];!q(a)||!G(a)||(Object.assign(a.style,o),Object.keys(r).forEach(function(i){var u=r[i];u===!1?a.removeAttribute(i):a.setAttribute(i,u===!0?"":u)}))})}function no(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var r=t.elements[o],a=t.attributes[o]||{},i=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]),u=i.reduce(function(s,l){return s[l]="",s},{});!q(r)||!G(r)||(Object.assign(r.style,u),Object.keys(a).forEach(function(s){r.removeAttribute(s)}))})}}var Wt={name:"applyStyles",enabled:!0,phase:"write",fn:to,effect:no,requires:["computeStyles"]};function J(e){return e.split("-")[0]}var ae=Math.max,We=Math.min,ve=Math.round;function ge(e,t){t===void 0&&(t=!1);var n=e.getBoundingClientRect(),o=1,r=1;if(q(e)&&t){var a=e.offsetHeight,i=e.offsetWidth;i>0&&(o=ve(n.width)/i||1),a>0&&(r=ve(n.height)/a||1)}return{width:n.width/o,height:n.height/r,top:n.top/r,right:n.right/o,bottom:n.bottom/r,left:n.left/o,x:n.left/o,y:n.top/r}}function tt(e){var t=ge(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function qt(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&et(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function Q(e){return z(e).getComputedStyle(e)}function oo(e){return["table","td","th"].indexOf(G(e))>=0}function ne(e){return((de(e)?e.ownerDocument:e.document)||window.document).documentElement}function Ke(e){return G(e)==="html"?e:e.assignedSlot||e.parentNode||(et(e)?e.host:null)||ne(e)}function Tt(e){return!q(e)||Q(e).position==="fixed"?null:e.offsetParent}function ro(e){var t=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,n=navigator.userAgent.indexOf("Trident")!==-1;if(n&&q(e)){var o=Q(e);if(o.position==="fixed")return null}var r=Ke(e);for(et(r)&&(r=r.host);q(r)&&["html","body"].indexOf(G(r))<0;){var a=Q(r);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return r;r=r.parentNode}return null}function Be(e){for(var t=z(e),n=Tt(e);n&&oo(n)&&Q(n).position==="static";)n=Tt(n);return n&&(G(n)==="html"||G(n)==="body"&&Q(n).position==="static")?t:n||ro(e)||t}function nt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Te(e,t,n){return ae(e,We(t,n))}function ao(e,t,n){var o=Te(e,t,n);return o>n?n:o}function Kt(){return{top:0,right:0,bottom:0,left:0}}function Ut(e){return Object.assign({},Kt(),e)}function zt(e,t){return t.reduce(function(n,o){return n[o]=e,n},{})}var io=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,Ut(typeof e!="number"?e:zt(e,Ie))};function so(e){var t,n=e.state,o=e.name,r=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,u=J(n.placement),s=nt(u),l=[W,U].indexOf(u)>=0,f=l?"height":"width";if(!(!a||!i)){var d=io(r.padding,n),y=tt(a),g=s==="y"?H:W,m=s==="y"?K:U,v=n.rects.reference[f]+n.rects.reference[s]-i[s]-n.rects.popper[f],T=i[s]-n.rects.reference[s],p=Be(a),w=p?s==="y"?p.clientHeight||0:p.clientWidth||0:0,E=v/2-T/2,h=d[g],C=w-y[f]-d[m],P=w/2-y[f]/2+E,O=Te(h,P,C),R=s;n.modifiersData[o]=(t={},t[R]=O,t.centerOffset=O-P,t)}}function lo(e){var t=e.state,n=e.options,o=n.element,r=o===void 0?"[data-popper-arrow]":o;r!=null&&(typeof r=="string"&&(r=t.elements.popper.querySelector(r),!r)||!qt(t.elements.popper,r)||(t.elements.arrow=r))}var uo={name:"arrow",enabled:!0,phase:"main",fn:so,effect:lo,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function me(e){return e.split("-")[1]}var po={top:"auto",right:"auto",bottom:"auto",left:"auto"};function co(e){var t=e.x,n=e.y,o=window,r=o.devicePixelRatio||1;return{x:ve(t*r)/r||0,y:ve(n*r)/r||0}}function Et(e){var t,n=e.popper,o=e.popperRect,r=e.placement,a=e.variation,i=e.offsets,u=e.position,s=e.gpuAcceleration,l=e.adaptive,f=e.roundOffsets,d=e.isFixed,y=i.x,g=y===void 0?0:y,m=i.y,v=m===void 0?0:m,T=typeof f=="function"?f({x:g,y:v}):{x:g,y:v};g=T.x,v=T.y;var p=i.hasOwnProperty("x"),w=i.hasOwnProperty("y"),E=W,h=H,C=window;if(l){var P=Be(n),O="clientHeight",R="clientWidth";if(P===z(n)&&(P=ne(n),Q(P).position!=="static"&&u==="absolute"&&(O="scrollHeight",R="scrollWidth")),P=P,r===H||(r===W||r===U)&&a===Pe){h=K;var k=d&&P===C&&C.visualViewport?C.visualViewport.height:P[O];v-=k-o.height,v*=s?1:-1}if(r===W||(r===H||r===K)&&a===Pe){E=U;var x=d&&P===C&&C.visualViewport?C.visualViewport.width:P[R];g-=x-o.width,g*=s?1:-1}}var M=Object.assign({position:u},l&&po),D=f===!0?co({x:g,y:v}):{x:g,y:v};if(g=D.x,v=D.y,s){var S;return Object.assign({},M,(S={},S[h]=w?"0":"",S[E]=p?"0":"",S.transform=(C.devicePixelRatio||1)<=1?"translate("+g+"px, "+v+"px)":"translate3d("+g+"px, "+v+"px, 0)",S))}return Object.assign({},M,(t={},t[h]=w?v+"px":"",t[E]=p?g+"px":"",t.transform="",t))}function fo(e){var t=e.state,n=e.options,o=n.gpuAcceleration,r=o===void 0?!0:o,a=n.adaptive,i=a===void 0?!0:a,u=n.roundOffsets,s=u===void 0?!0:u,l={placement:J(t.placement),variation:me(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Et(Object.assign({},l,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:s})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Et(Object.assign({},l,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var Vt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:fo,data:{}},Le={passive:!0};function vo(e){var t=e.state,n=e.instance,o=e.options,r=o.scroll,a=r===void 0?!0:r,i=o.resize,u=i===void 0?!0:i,s=z(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&l.forEach(function(f){f.addEventListener("scroll",n.update,Le)}),u&&s.addEventListener("resize",n.update,Le),function(){a&&l.forEach(function(f){f.removeEventListener("scroll",n.update,Le)}),u&&s.removeEventListener("resize",n.update,Le)}}var Zt={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:vo,data:{}},go={left:"right",right:"left",bottom:"top",top:"bottom"};function Ne(e){return e.replace(/left|right|bottom|top/g,function(t){return go[t]})}var mo={start:"end",end:"start"};function Ct(e){return e.replace(/start|end/g,function(t){return mo[t]})}function ot(e){var t=z(e),n=t.pageXOffset,o=t.pageYOffset;return{scrollLeft:n,scrollTop:o}}function rt(e){return ge(ne(e)).left+ot(e).scrollLeft}function ho(e){var t=z(e),n=ne(e),o=t.visualViewport,r=n.clientWidth,a=n.clientHeight,i=0,u=0;return o&&(r=o.width,a=o.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(i=o.offsetLeft,u=o.offsetTop)),{width:r,height:a,x:i+rt(e),y:u}}function yo(e){var t,n=ne(e),o=ot(e),r=(t=e.ownerDocument)==null?void 0:t.body,a=ae(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),i=ae(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),u=-o.scrollLeft+rt(e),s=-o.scrollTop;return Q(r||n).direction==="rtl"&&(u+=ae(n.clientWidth,r?r.clientWidth:0)-a),{width:a,height:i,x:u,y:s}}function at(e){var t=Q(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+o)}function Jt(e){return["html","body","#document"].indexOf(G(e))>=0?e.ownerDocument.body:q(e)&&at(e)?e:Jt(Ke(e))}function Ee(e,t){var n;t===void 0&&(t=[]);var o=Jt(e),r=o===((n=e.ownerDocument)==null?void 0:n.body),a=z(o),i=r?[a].concat(a.visualViewport||[],at(o)?o:[]):o,u=t.concat(i);return r?u:u.concat(Ee(Ke(i)))}function Ge(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function bo(e){var t=ge(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Rt(e,t){return t===Ht?Ge(ho(e)):de(t)?bo(t):Ge(yo(ne(e)))}function wo(e){var t=Ee(Ke(e)),n=["absolute","fixed"].indexOf(Q(e).position)>=0,o=n&&q(e)?Be(e):e;return de(o)?t.filter(function(r){return de(r)&&qt(r,o)&&G(r)!=="body"}):[]}function Oo(e,t,n){var o=t==="clippingParents"?wo(e):[].concat(t),r=[].concat(o,[n]),a=r[0],i=r.reduce(function(u,s){var l=Rt(e,s);return u.top=ae(l.top,u.top),u.right=We(l.right,u.right),u.bottom=We(l.bottom,u.bottom),u.left=ae(l.left,u.left),u},Rt(e,a));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function Gt(e){var t=e.reference,n=e.element,o=e.placement,r=o?J(o):null,a=o?me(o):null,i=t.x+t.width/2-n.width/2,u=t.y+t.height/2-n.height/2,s;switch(r){case H:s={x:i,y:t.y-n.height};break;case K:s={x:i,y:t.y+t.height};break;case U:s={x:t.x+t.width,y:u};break;case W:s={x:t.x-n.width,y:u};break;default:s={x:t.x,y:t.y}}var l=r?nt(r):null;if(l!=null){var f=l==="y"?"height":"width";switch(a){case fe:s[l]=s[l]-(t[f]/2-n[f]/2);break;case Pe:s[l]=s[l]+(t[f]/2-n[f]/2);break}}return s}function xe(e,t){t===void 0&&(t={});var n=t,o=n.placement,r=o===void 0?e.placement:o,a=n.boundary,i=a===void 0?qn:a,u=n.rootBoundary,s=u===void 0?Ht:u,l=n.elementContext,f=l===void 0?be:l,d=n.altBoundary,y=d===void 0?!1:d,g=n.padding,m=g===void 0?0:g,v=Ut(typeof m!="number"?m:zt(m,Ie)),T=f===be?Kn:be,p=e.rects.popper,w=e.elements[y?T:f],E=Oo(de(w)?w:w.contextElement||ne(e.elements.popper),i,s),h=ge(e.elements.reference),C=Gt({reference:h,element:p,strategy:"absolute",placement:r}),P=Ge(Object.assign({},p,C)),O=f===be?P:h,R={top:E.top-O.top+v.top,bottom:O.bottom-E.bottom+v.bottom,left:E.left-O.left+v.left,right:O.right-E.right+v.right},k=e.modifiersData.offset;if(f===be&&k){var x=k[r];Object.keys(R).forEach(function(M){var D=[U,K].indexOf(M)>=0?1:-1,S=[H,K].indexOf(M)>=0?"y":"x";R[M]+=x[S]*D})}return R}function To(e,t){t===void 0&&(t={});var n=t,o=n.placement,r=n.boundary,a=n.rootBoundary,i=n.padding,u=n.flipVariations,s=n.allowedAutoPlacements,l=s===void 0?Qe:s,f=me(o),d=f?u?Ot:Ot.filter(function(m){return me(m)===f}):Ie,y=d.filter(function(m){return l.indexOf(m)>=0});y.length===0&&(y=d);var g=y.reduce(function(m,v){return m[v]=xe(e,{placement:v,boundary:r,rootBoundary:a,padding:i})[J(v)],m},{});return Object.keys(g).sort(function(m,v){return g[m]-g[v]})}function Eo(e){if(J(e)===Ye)return[];var t=Ne(e);return[Ct(e),t,Ct(t)]}function Co(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var r=n.mainAxis,a=r===void 0?!0:r,i=n.altAxis,u=i===void 0?!0:i,s=n.fallbackPlacements,l=n.padding,f=n.boundary,d=n.rootBoundary,y=n.altBoundary,g=n.flipVariations,m=g===void 0?!0:g,v=n.allowedAutoPlacements,T=t.options.placement,p=J(T),w=p===T,E=s||(w||!m?[Ne(T)]:Eo(T)),h=[T].concat(E).reduce(function(oe,X){return oe.concat(J(X)===Ye?To(t,{placement:X,boundary:f,rootBoundary:d,padding:l,flipVariations:m,allowedAutoPlacements:v}):X)},[]),C=t.rects.reference,P=t.rects.popper,O=new Map,R=!0,k=h[0],x=0;x<h.length;x++){var M=h[x],D=J(M),S=me(M)===fe,N=[H,K].indexOf(D)>=0,F=N?"width":"height",B=xe(t,{placement:M,boundary:f,rootBoundary:d,altBoundary:y,padding:l}),b=N?S?U:W:S?K:H;C[F]>P[F]&&(b=Ne(b));var $=Ne(b),V=[];if(a&&V.push(B[D]<=0),u&&V.push(B[b]<=0,B[$]<=0),V.every(function(oe){return oe})){k=M,R=!1;break}O.set(M,V)}if(R)for(var ke=m?3:1,Ue=function(oe){var X=h.find(function(Fe){var ye=O.get(Fe);if(ye)return ye.slice(0,oe).every(function(ue){return ue})});if(X)return k=X,"break"},he=ke;he>0;he--){var je=Ue(he);if(je==="break")break}t.placement!==k&&(t.modifiersData[o]._skip=!0,t.placement=k,t.reset=!0)}}var Ro={name:"flip",enabled:!0,phase:"main",fn:Co,requiresIfExists:["offset"],data:{_skip:!1}};function At(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Pt(e){return[H,U,K,W].some(function(t){return e[t]>=0})}function Ao(e){var t=e.state,n=e.name,o=t.rects.reference,r=t.rects.popper,a=t.modifiersData.preventOverflow,i=xe(t,{elementContext:"reference"}),u=xe(t,{altBoundary:!0}),s=At(i,o),l=At(u,r,a),f=Pt(s),d=Pt(l);t.modifiersData[n]={referenceClippingOffsets:s,popperEscapeOffsets:l,isReferenceHidden:f,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":d})}var Po={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ao};function xo(e,t,n){var o=J(e),r=[W,H].indexOf(o)>=0?-1:1,a=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,i=a[0],u=a[1];return i=i||0,u=(u||0)*r,[W,U].indexOf(o)>=0?{x:u,y:i}:{x:i,y:u}}function So(e){var t=e.state,n=e.options,o=e.name,r=n.offset,a=r===void 0?[0,0]:r,i=Qe.reduce(function(f,d){return f[d]=xo(d,t.rects,a),f},{}),u=i[t.placement],s=u.x,l=u.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=l),t.modifiersData[o]=i}var Mo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:So};function Io(e){var t=e.state,n=e.name;t.modifiersData[n]=Gt({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}var Xt={name:"popperOffsets",enabled:!0,phase:"read",fn:Io,data:{}};function Bo(e){return e==="x"?"y":"x"}function ko(e){var t=e.state,n=e.options,o=e.name,r=n.mainAxis,a=r===void 0?!0:r,i=n.altAxis,u=i===void 0?!1:i,s=n.boundary,l=n.rootBoundary,f=n.altBoundary,d=n.padding,y=n.tether,g=y===void 0?!0:y,m=n.tetherOffset,v=m===void 0?0:m,T=xe(t,{boundary:s,rootBoundary:l,padding:d,altBoundary:f}),p=J(t.placement),w=me(t.placement),E=!w,h=nt(p),C=Bo(h),P=t.modifiersData.popperOffsets,O=t.rects.reference,R=t.rects.popper,k=typeof v=="function"?v(Object.assign({},t.rects,{placement:t.placement})):v,x=typeof k=="number"?{mainAxis:k,altAxis:k}:Object.assign({mainAxis:0,altAxis:0},k),M=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,D={x:0,y:0};if(P){if(a){var S,N=h==="y"?H:W,F=h==="y"?K:U,B=h==="y"?"height":"width",b=P[h],$=b+T[N],V=b-T[F],ke=g?-R[B]/2:0,Ue=w===fe?O[B]:R[B],he=w===fe?-R[B]:-O[B],je=t.elements.arrow,oe=g&&je?tt(je):{width:0,height:0},X=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Kt(),Fe=X[N],ye=X[F],ue=Te(0,O[B],oe[B]),un=E?O[B]/2-ke-ue-Fe-x.mainAxis:Ue-ue-Fe-x.mainAxis,pn=E?-O[B]/2+ke+ue+ye+x.mainAxis:he+ue+ye+x.mainAxis,ze=t.elements.arrow&&Be(t.elements.arrow),cn=ze?h==="y"?ze.clientTop||0:ze.clientLeft||0:0,ut=(S=M==null?void 0:M[h])!=null?S:0,fn=b+un-ut-cn,dn=b+pn-ut,pt=Te(g?We($,fn):$,b,g?ae(V,dn):V);P[h]=pt,D[h]=pt-b}if(u){var ct,vn=h==="x"?H:W,gn=h==="x"?K:U,re=P[C],_e=C==="y"?"height":"width",ft=re+T[vn],dt=re-T[gn],Ve=[H,W].indexOf(p)!==-1,vt=(ct=M==null?void 0:M[C])!=null?ct:0,gt=Ve?ft:re-O[_e]-R[_e]-vt+x.altAxis,mt=Ve?re+O[_e]+R[_e]-vt-x.altAxis:dt,ht=g&&Ve?ao(gt,re,mt):Te(g?gt:ft,re,g?mt:dt);P[C]=ht,D[C]=ht-re}t.modifiersData[o]=D}}var jo={name:"preventOverflow",enabled:!0,phase:"main",fn:ko,requiresIfExists:["offset"]};function Fo(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function _o(e){return e===z(e)||!q(e)?ot(e):Fo(e)}function Do(e){var t=e.getBoundingClientRect(),n=ve(t.width)/e.offsetWidth||1,o=ve(t.height)/e.offsetHeight||1;return n!==1||o!==1}function Lo(e,t,n){n===void 0&&(n=!1);var o=q(t),r=q(t)&&Do(t),a=ne(t),i=ge(e,r),u={scrollLeft:0,scrollTop:0},s={x:0,y:0};return(o||!o&&!n)&&((G(t)!=="body"||at(a))&&(u=_o(t)),q(t)?(s=ge(t,!0),s.x+=t.clientLeft,s.y+=t.clientTop):a&&(s.x=rt(a))),{x:i.left+u.scrollLeft-s.x,y:i.top+u.scrollTop-s.y,width:i.width,height:i.height}}function No(e){var t=new Map,n=new Set,o=[];e.forEach(function(a){t.set(a.name,a)});function r(a){n.add(a.name);var i=[].concat(a.requires||[],a.requiresIfExists||[]);i.forEach(function(u){if(!n.has(u)){var s=t.get(u);s&&r(s)}}),o.push(a)}return e.forEach(function(a){n.has(a.name)||r(a)}),o}function $o(e){var t=No(e);return eo.reduce(function(n,o){return n.concat(t.filter(function(r){return r.phase===o}))},[])}function Ho(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function Wo(e){var t=e.reduce(function(n,o){var r=n[o.name];return n[o.name]=r?Object.assign({},r,o,{options:Object.assign({},r.options,o.options),data:Object.assign({},r.data,o.data)}):o,n},{});return Object.keys(t).map(function(n){return t[n]})}var xt={placement:"bottom",modifiers:[],strategy:"absolute"};function St(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function it(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,r=t.defaultOptions,a=r===void 0?xt:r;return function(i,u,s){s===void 0&&(s=a);var l={placement:"bottom",orderedModifiers:[],options:Object.assign({},xt,a),modifiersData:{},elements:{reference:i,popper:u},attributes:{},styles:{}},f=[],d=!1,y={state:l,setOptions:function(v){var T=typeof v=="function"?v(l.options):v;m(),l.options=Object.assign({},a,l.options,T),l.scrollParents={reference:de(i)?Ee(i):i.contextElement?Ee(i.contextElement):[],popper:Ee(u)};var p=$o(Wo([].concat(o,l.options.modifiers)));return l.orderedModifiers=p.filter(function(w){return w.enabled}),g(),y.update()},forceUpdate:function(){if(!d){var v=l.elements,T=v.reference,p=v.popper;if(St(T,p)){l.rects={reference:Lo(T,Be(p),l.options.strategy==="fixed"),popper:tt(p)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(R){return l.modifiersData[R.name]=Object.assign({},R.data)});for(var w=0;w<l.orderedModifiers.length;w++){if(l.reset===!0){l.reset=!1,w=-1;continue}var E=l.orderedModifiers[w],h=E.fn,C=E.options,P=C===void 0?{}:C,O=E.name;typeof h=="function"&&(l=h({state:l,options:P,name:O,instance:y})||l)}}}},update:Ho(function(){return new Promise(function(v){y.forceUpdate(),v(l)})}),destroy:function(){m(),d=!0}};if(!St(i,u))return y;y.setOptions(s).then(function(v){!d&&s.onFirstUpdate&&s.onFirstUpdate(v)});function g(){l.orderedModifiers.forEach(function(v){var T=v.name,p=v.options,w=p===void 0?{}:p,E=v.effect;if(typeof E=="function"){var h=E({state:l,name:T,instance:y,options:w}),C=function(){};f.push(h||C)}})}function m(){f.forEach(function(v){return v()}),f=[]}return y}}it();var qo=[Zt,Xt,Vt,Wt];it({defaultModifiers:qo});var Ko=[Zt,Xt,Vt,Wt,Mo,Ro,jo,uo,Po],Uo=it({defaultModifiers:Ko});const zo=(e,t,n={})=>{const o={name:"updateState",enabled:!0,phase:"write",fn:({state:s})=>{const l=Vo(s);Object.assign(i.value,l)},requires:["computeStyles"]},r=A(()=>{const{onFirstUpdate:s,placement:l,strategy:f,modifiers:d}=c(n);return{onFirstUpdate:s,placement:l||"bottom",strategy:f||"absolute",modifiers:[...d||[],o,{name:"applyStyles",enabled:!1}]}}),a=Cn(),i=j({styles:{popper:{position:c(r).strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),u=()=>{a.value&&(a.value.destroy(),a.value=void 0)};return _(r,s=>{const l=c(a);l&&l.setOptions(s)},{deep:!0}),_([e,t],([s,l])=>{u(),!(!s||!l)&&(a.value=Uo(s,l,c(r)))}),Me(()=>{u()}),{state:A(()=>{var s;return{...((s=c(a))==null?void 0:s.state)||{}}}),styles:A(()=>c(i).styles),attributes:A(()=>c(i).attributes),update:()=>{var s;return(s=c(a))==null?void 0:s.update()},forceUpdate:()=>{var s;return(s=c(a))==null?void 0:s.forceUpdate()},instanceRef:A(()=>c(a))}};function Vo(e){const t=Object.keys(e.elements),n=yt(t.map(r=>[r,e.styles[r]||{}])),o=yt(t.map(r=>[r,e.attributes[r]]));return{styles:n,attributes:o}}function Mt(){let e;const t=(o,r)=>{n(),e=window.setTimeout(o,r)},n=()=>window.clearTimeout(e);return mn(()=>n()),{registerTimeout:t,cancelTimeout:n}}let It;const Yt=()=>{const e=hn(),t=yn(),n=A(()=>`${e.value}-popper-container-${t.prefix}`),o=A(()=>`#${n.value}`);return{id:n,selector:o}},Zo=e=>{const t=document.createElement("div");return t.id=e,document.body.appendChild(t),t},Jo=()=>{const{id:e,selector:t}=Yt();return Rn(()=>{Oe&&!It&&!document.body.querySelector(t.value)&&(It=Zo(e.value))}),{id:e,selector:t}},Go=ee({showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0}}),Xo=({showAfter:e,hideAfter:t,autoClose:n,open:o,close:r})=>{const{registerTimeout:a}=Mt(),{registerTimeout:i,cancelTimeout:u}=Mt();return{onOpen:f=>{a(()=>{o(f);const d=c(n);Ze(d)&&d>0&&i(()=>{r(f)},d)},c(e))},onClose:f=>{u(),a(()=>{r(f)},c(t))}}},Qt=Symbol("elForwardRef"),Yo=e=>{Ce(Qt,{setForwardRef:n=>{e.value=n}})},Qo=e=>({mounted(t){e(t)},updated(t){e(t)},unmounted(){e(null)}}),st=Symbol("popper"),en=Symbol("popperContent"),er=["dialog","grid","group","listbox","menu","navigation","tooltip","tree"],tn=ee({role:{type:String,values:er,default:"tooltip"}}),tr=L({name:"ElPopper",inheritAttrs:!1}),nr=L({...tr,props:tn,setup(e,{expose:t}){const n=e,o=j(),r=j(),a=j(),i=j(),u=A(()=>n.role),s={triggerRef:o,popperInstanceRef:r,contentRef:a,referenceRef:i,role:u};return t(s),Ce(st,s),(l,f)=>ie(l.$slots,"default")}});var or=se(nr,[["__file","popper.vue"]]);const nn=ee({arrowOffset:{type:Number,default:5}}),rr=L({name:"ElPopperArrow",inheritAttrs:!1}),ar=L({...rr,props:nn,setup(e,{expose:t}){const n=e,o=Se("popper"),{arrowOffset:r,arrowRef:a,arrowStyle:i}=le(en,void 0);return _(()=>n.arrowOffset,u=>{r.value=u}),Me(()=>{a.value=void 0}),t({arrowRef:a}),(u,s)=>(Z(),He("span",{ref_key:"arrowRef",ref:a,class:Nt(c(o).e("arrow")),style:An(c(i)),"data-popper-arrow":""},null,6))}});var ir=se(ar,[["__file","arrow.vue"]]);const sr="ElOnlyChild",lr=L({name:sr,setup(e,{slots:t,attrs:n}){var o;const r=le(Qt),a=Qo((o=r==null?void 0:r.setForwardRef)!=null?o:Je);return()=>{var i;const u=(i=t.default)==null?void 0:i.call(t,n);if(!u||u.length>1)return null;const s=on(u);return s?$t(Pn(s,n),[[a]]):null}}});function on(e){if(!e)return null;const t=e;for(const n of t){if(xn(n))switch(n.type){case In:continue;case Mn:case"svg":return Bt(n);case Sn:return on(n.children);default:return n}return Bt(n)}return null}function Bt(e){const t=Se("only-child");return Re("span",{class:t.e("content")},[e])}const rn=ee({virtualRef:{type:I(Object)},virtualTriggering:Boolean,onMouseenter:{type:I(Function)},onMouseleave:{type:I(Function)},onClick:{type:I(Function)},onKeydown:{type:I(Function)},onFocus:{type:I(Function)},onBlur:{type:I(Function)},onContextmenu:{type:I(Function)},id:String,open:Boolean}),ur=L({name:"ElPopperTrigger",inheritAttrs:!1}),pr=L({...ur,props:rn,setup(e,{expose:t}){const n=e,{role:o,triggerRef:r}=le(st,void 0);Yo(r);const a=A(()=>u.value?n.id:void 0),i=A(()=>{if(o&&o.value==="tooltip")return n.open&&n.id?n.id:void 0}),u=A(()=>{if(o&&o.value!=="tooltip")return o.value}),s=A(()=>u.value?`${n.open}`:void 0);let l;return qe(()=>{_(()=>n.virtualRef,f=>{f&&(r.value=Ft(f))},{immediate:!0}),_(r,(f,d)=>{l==null||l(),l=void 0,$e(f)&&(["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"].forEach(y=>{var g;const m=n[y];m&&(f.addEventListener(y.slice(2).toLowerCase(),m),(g=d==null?void 0:d.removeEventListener)==null||g.call(d,y.slice(2).toLowerCase(),m))}),l=_([a,i,u,s],y=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((g,m)=>{_t(y[m])?f.removeAttribute(g):f.setAttribute(g,y[m])})},{immediate:!0})),$e(d)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(y=>d.removeAttribute(y))},{immediate:!0})}),Me(()=>{l==null||l(),l=void 0}),t({triggerRef:r}),(f,d)=>f.virtualTriggering?Ae("v-if",!0):(Z(),ce(c(lr),Xe({key:0},f.$attrs,{"aria-controls":c(a),"aria-describedby":c(i),"aria-expanded":c(s),"aria-haspopup":c(u)}),{default:te(()=>[ie(f.$slots,"default")]),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}});var cr=se(pr,[["__file","trigger.vue"]]);const fr=["fixed","absolute"],dr=ee({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:I(Array),default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:Qe,default:"bottom"},popperOptions:{type:I(Object),default:()=>({})},strategy:{type:String,values:fr,default:"absolute"}}),an=ee({...dr,id:String,style:{type:I([String,Array,Object])},className:{type:I([String,Array,Object])},effect:{type:String,default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:{type:Boolean,default:!1},trapping:{type:Boolean,default:!1},popperClass:{type:I([String,Array,Object])},popperStyle:{type:I([String,Array,Object])},referenceEl:{type:I(Object)},triggerTargetEl:{type:I(Object)},stopPopperMouseEvent:{type:Boolean,default:!0},virtualTriggering:Boolean,zIndex:Number,...Dt(["ariaLabel"])}),vr={mouseenter:e=>e instanceof MouseEvent,mouseleave:e=>e instanceof MouseEvent,focus:()=>!0,blur:()=>!0,close:()=>!0},gr=(e,t=[])=>{const{placement:n,strategy:o,popperOptions:r}=e,a={placement:n,strategy:o,...r,modifiers:[...hr(e),...t]};return yr(a,r==null?void 0:r.modifiers),a},mr=e=>{if(Oe)return Ft(e)};function hr(e){const{offset:t,gpuAcceleration:n,fallbackPlacements:o}=e;return[{name:"offset",options:{offset:[0,t??12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:o}},{name:"computeStyles",options:{gpuAcceleration:n}}]}function yr(e,t){t&&(e.modifiers=[...e.modifiers,...t??[]])}const br=0,wr=e=>{const{popperInstanceRef:t,contentRef:n,triggerRef:o,role:r}=le(st,void 0),a=j(),i=j(),u=A(()=>({name:"eventListeners",enabled:!!e.visible})),s=A(()=>{var p;const w=c(a),E=(p=c(i))!=null?p:br;return{name:"arrow",enabled:!Nn(w),options:{element:w,padding:E}}}),l=A(()=>({onFirstUpdate:()=>{m()},...gr(e,[c(s),c(u)])})),f=A(()=>mr(e.referenceEl)||c(o)),{attributes:d,state:y,styles:g,update:m,forceUpdate:v,instanceRef:T}=zo(f,n,l);return _(T,p=>t.value=p),qe(()=>{_(()=>{var p;return(p=c(f))==null?void 0:p.getBoundingClientRect()},()=>{m()})}),{attributes:d,arrowRef:a,contentRef:n,instanceRef:T,state:y,styles:g,role:r,forceUpdate:v,update:m}},Or=(e,{attributes:t,styles:n,role:o})=>{const{nextZIndex:r}=bn(),a=Se("popper"),i=A(()=>c(t).popper),u=j(Ze(e.zIndex)?e.zIndex:r()),s=A(()=>[a.b(),a.is("pure",e.pure),a.is(e.effect),e.popperClass]),l=A(()=>[{zIndex:c(u)},c(n).popper,e.popperStyle||{}]),f=A(()=>o.value==="dialog"?"false":void 0),d=A(()=>c(n).arrow||{});return{ariaModal:f,arrowStyle:d,contentAttrs:i,contentClass:s,contentStyle:l,contentZIndex:u,updateZIndex:()=>{u.value=Ze(e.zIndex)?e.zIndex:r()}}},Tr=(e,t)=>{const n=j(!1),o=j();return{focusStartRef:o,trapped:n,onFocusAfterReleased:l=>{var f;((f=l.detail)==null?void 0:f.focusReason)!=="pointer"&&(o.value="first",t("blur"))},onFocusAfterTrapped:()=>{t("focus")},onFocusInTrap:l=>{e.visible&&!n.value&&(l.target&&(o.value=l.target),n.value=!0)},onFocusoutPrevented:l=>{e.trapping||(l.detail.focusReason==="pointer"&&l.preventDefault(),n.value=!1)},onReleaseRequested:()=>{n.value=!1,t("close")}}},Er=L({name:"ElPopperContent"}),Cr=L({...Er,props:an,emits:vr,setup(e,{expose:t,emit:n}){const o=e,{focusStartRef:r,trapped:a,onFocusAfterReleased:i,onFocusAfterTrapped:u,onFocusInTrap:s,onFocusoutPrevented:l,onReleaseRequested:f}=Tr(o,n),{attributes:d,arrowRef:y,contentRef:g,styles:m,instanceRef:v,role:T,update:p}=wr(o),{ariaModal:w,arrowStyle:E,contentAttrs:h,contentClass:C,contentStyle:P,updateZIndex:O}=Or(o,{styles:m,attributes:d,role:T}),R=le(bt,void 0),k=j();Ce(en,{arrowStyle:E,arrowRef:y,arrowOffset:k}),R&&(R.addInputId||R.removeInputId)&&Ce(bt,{...R,addInputId:Je,removeInputId:Je});let x;const M=(S=!0)=>{p(),S&&O()},D=()=>{M(!1),o.visible&&o.focusOnShow?a.value=!0:o.visible===!1&&(a.value=!1)};return qe(()=>{_(()=>o.triggerTargetEl,(S,N)=>{x==null||x(),x=void 0;const F=c(S||g.value),B=c(N||g.value);$e(F)&&(x=_([T,()=>o.ariaLabel,w,()=>o.id],b=>{["role","aria-label","aria-modal","id"].forEach(($,V)=>{_t(b[V])?F.removeAttribute($):F.setAttribute($,b[V])})},{immediate:!0})),B!==F&&$e(B)&&["role","aria-label","aria-modal","id"].forEach(b=>{B.removeAttribute(b)})},{immediate:!0}),_(()=>o.visible,D,{immediate:!0})}),Me(()=>{x==null||x(),x=void 0}),t({popperContentRef:g,popperInstanceRef:v,updatePopper:M,contentStyle:P}),(S,N)=>(Z(),He("div",Xe({ref_key:"contentRef",ref:g},c(h),{style:c(P),class:c(C),tabindex:"-1",onMouseenter:N[0]||(N[0]=F=>S.$emit("mouseenter",F)),onMouseleave:N[1]||(N[1]=F=>S.$emit("mouseleave",F))}),[Re(c(wn),{trapped:c(a),"trap-on-focus-in":!0,"focus-trap-el":c(g),"focus-start-el":c(r),onFocusAfterTrapped:c(u),onFocusAfterReleased:c(i),onFocusin:c(s),onFocusoutPrevented:c(l),onReleaseRequested:c(f)},{default:te(()=>[ie(S.$slots,"default")]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusin","onFocusoutPrevented","onReleaseRequested"])],16))}});var Rr=se(Cr,[["__file","content.vue"]]);const Ar=Lt(or),lt=Symbol("elTooltip"),sn=ee({...Go,...an,appendTo:{type:I([String,Object])},content:{type:String,default:""},rawContent:{type:Boolean,default:!1},persistent:Boolean,visible:{type:I(Boolean),default:null},transition:String,teleported:{type:Boolean,default:!0},disabled:Boolean,...Dt(["ariaLabel"])}),ln=ee({...rn,disabled:Boolean,trigger:{type:I([String,Array]),default:"hover"},triggerKeys:{type:I(Array),default:()=>[wt.enter,wt.space]}}),{useModelToggleProps:Pr,useModelToggleEmits:xr,useModelToggle:Sr}=Wn("visible"),Mr=ee({...tn,...Pr,...sn,...ln,...nn,showArrow:{type:Boolean,default:!0}}),Ir=[...xr,"before-show","before-hide","show","hide","open","close"],Br=(e,t)=>Bn(e)?e.includes(t):e===t,pe=(e,t,n)=>o=>{Br(c(e),t)&&n(o)},kr=L({name:"ElTooltipTrigger"}),jr=L({...kr,props:ln,setup(e,{expose:t}){const n=e,o=Se("tooltip"),{controlled:r,id:a,open:i,onOpen:u,onClose:s,onToggle:l}=le(lt,void 0),f=j(null),d=()=>{if(c(r)||n.disabled)return!0},y=we(n,"trigger"),g=Y(d,pe(y,"hover",u)),m=Y(d,pe(y,"hover",s)),v=Y(d,pe(y,"click",h=>{h.button===0&&l(h)})),T=Y(d,pe(y,"focus",u)),p=Y(d,pe(y,"focus",s)),w=Y(d,pe(y,"contextmenu",h=>{h.preventDefault(),l(h)})),E=Y(d,h=>{const{code:C}=h;n.triggerKeys.includes(C)&&(h.preventDefault(),l(h))});return t({triggerRef:f}),(h,C)=>(Z(),ce(c(cr),{id:c(a),"virtual-ref":h.virtualRef,open:c(i),"virtual-triggering":h.virtualTriggering,class:Nt(c(o).e("trigger")),onBlur:c(p),onClick:c(v),onContextmenu:c(w),onFocus:c(T),onMouseenter:c(g),onMouseleave:c(m),onKeydown:c(E)},{default:te(()=>[ie(h.$slots,"default")]),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"]))}});var Fr=se(jr,[["__file","trigger.vue"]]);const _r=L({name:"ElTooltipContent",inheritAttrs:!1}),Dr=L({..._r,props:sn,setup(e,{expose:t}){const n=e,{selector:o}=Yt(),r=Se("tooltip"),a=j(null),i=j(!1),{controlled:u,id:s,open:l,trigger:f,onClose:d,onOpen:y,onShow:g,onHide:m,onBeforeShow:v,onBeforeHide:T}=le(lt,void 0),p=A(()=>n.transition||`${r.namespace.value}-fade-in-linear`),w=A(()=>n.persistent);Me(()=>{i.value=!0});const E=A(()=>c(w)?!0:c(l)),h=A(()=>n.disabled?!1:c(l)),C=A(()=>n.appendTo||o.value),P=A(()=>{var b;return(b=n.style)!=null?b:{}}),O=A(()=>!c(l)),R=()=>{m()},k=()=>{if(c(u))return!0},x=Y(k,()=>{n.enterable&&c(f)==="hover"&&y()}),M=Y(k,()=>{c(f)==="hover"&&d()}),D=()=>{var b,$;($=(b=a.value)==null?void 0:b.updatePopper)==null||$.call(b),v==null||v()},S=()=>{T==null||T()},N=()=>{g(),B=On(A(()=>{var b;return(b=a.value)==null?void 0:b.popperContentRef}),()=>{if(c(u))return;c(f)!=="hover"&&d()})},F=()=>{n.virtualTriggering||d()};let B;return _(()=>c(l),b=>{b||B==null||B()},{flush:"post"}),_(()=>n.content,()=>{var b,$;($=(b=a.value)==null?void 0:b.updatePopper)==null||$.call(b)}),t({contentRef:a}),(b,$)=>(Z(),ce(Fn,{disabled:!b.teleported,to:c(C)},[Re(jn,{name:c(p),onAfterLeave:R,onBeforeEnter:D,onAfterEnter:N,onBeforeLeave:S},{default:te(()=>[c(E)?$t((Z(),ce(c(Rr),Xe({key:0,id:c(s),ref_key:"contentRef",ref:a},b.$attrs,{"aria-label":b.ariaLabel,"aria-hidden":c(O),"boundaries-padding":b.boundariesPadding,"fallback-placements":b.fallbackPlacements,"gpu-acceleration":b.gpuAcceleration,offset:b.offset,placement:b.placement,"popper-options":b.popperOptions,strategy:b.strategy,effect:b.effect,enterable:b.enterable,pure:b.pure,"popper-class":b.popperClass,"popper-style":[b.popperStyle,c(P)],"reference-el":b.referenceEl,"trigger-target-el":b.triggerTargetEl,visible:c(h),"z-index":b.zIndex,onMouseenter:c(x),onMouseleave:c(M),onBlur:F,onClose:c(d)}),{default:te(()=>[i.value?Ae("v-if",!0):ie(b.$slots,"default",{key:0})]),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onClose"])),[[kn,c(h)]]):Ae("v-if",!0)]),_:3},8,["name"])],8,["disabled","to"]))}});var Lr=se(Dr,[["__file","content.vue"]]);const Nr=["innerHTML"],$r={key:1},Hr=L({name:"ElTooltip"}),Wr=L({...Hr,props:Mr,emits:Ir,setup(e,{expose:t,emit:n}){const o=e;Jo();const r=Tn(),a=j(),i=j(),u=()=>{var p;const w=c(a);w&&((p=w.popperInstanceRef)==null||p.update())},s=j(!1),l=j(),{show:f,hide:d,hasUpdateHandler:y}=Sr({indicator:s,toggleReason:l}),{onOpen:g,onClose:m}=Xo({showAfter:we(o,"showAfter"),hideAfter:we(o,"hideAfter"),autoClose:we(o,"autoClose"),open:f,close:d}),v=A(()=>jt(o.visible)&&!y.value);Ce(lt,{controlled:v,id:r,open:_n(s),trigger:we(o,"trigger"),onOpen:p=>{g(p)},onClose:p=>{m(p)},onToggle:p=>{c(s)?m(p):g(p)},onShow:()=>{n("show",l.value)},onHide:()=>{n("hide",l.value)},onBeforeShow:()=>{n("before-show",l.value)},onBeforeHide:()=>{n("before-hide",l.value)},updatePopper:u}),_(()=>o.disabled,p=>{p&&s.value&&(s.value=!1)});const T=p=>{var w,E;const h=(E=(w=i.value)==null?void 0:w.contentRef)==null?void 0:E.popperContentRef,C=(p==null?void 0:p.relatedTarget)||document.activeElement;return h&&h.contains(C)};return Dn(()=>s.value&&d()),t({popperRef:a,contentRef:i,isFocusInsideContent:T,updatePopper:u,onOpen:g,onClose:m,hide:d}),(p,w)=>(Z(),ce(c(Ar),{ref_key:"popperRef",ref:a,role:p.role},{default:te(()=>[Re(Fr,{disabled:p.disabled,trigger:p.trigger,"trigger-keys":p.triggerKeys,"virtual-ref":p.virtualRef,"virtual-triggering":p.virtualTriggering},{default:te(()=>[p.$slots.default?ie(p.$slots,"default",{key:0}):Ae("v-if",!0)]),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),Re(Lr,{ref_key:"contentRef",ref:i,"aria-label":p.ariaLabel,"boundaries-padding":p.boundariesPadding,content:p.content,disabled:p.disabled,effect:p.effect,enterable:p.enterable,"fallback-placements":p.fallbackPlacements,"hide-after":p.hideAfter,"gpu-acceleration":p.gpuAcceleration,offset:p.offset,persistent:p.persistent,"popper-class":p.popperClass,"popper-style":p.popperStyle,placement:p.placement,"popper-options":p.popperOptions,pure:p.pure,"raw-content":p.rawContent,"reference-el":p.referenceEl,"trigger-target-el":p.triggerTargetEl,"show-after":p.showAfter,strategy:p.strategy,teleported:p.teleported,transition:p.transition,"virtual-triggering":p.virtualTriggering,"z-index":p.zIndex,"append-to":p.appendTo},{default:te(()=>[ie(p.$slots,"content",{},()=>[p.rawContent?(Z(),He("span",{key:0,innerHTML:p.content},null,8,Nr)):(Z(),He("span",$r,Ln(p.content),1))]),p.showArrow?(Z(),ce(c(ir),{key:0,"arrow-offset":p.arrowOffset},null,8,["arrow-offset"])):Ae("v-if",!0)]),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])]),_:3},8,["role"]))}});var qr=se(Wr,[["__file","tooltip.vue"]]);const Zr=Lt(qr);export{Zr as E,lr as O,lt as T,sn as a,Qe as b,Y as c,ln as u,Vr as w};
