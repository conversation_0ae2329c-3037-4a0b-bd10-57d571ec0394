import{a as te,E as se}from"./CUCgy8P_.js";import{K as S,M as I,O as B,P as K,X as x,a4 as $,Z as R,e as le,E as ae}from"./DNaNbs6R.js";import{l as V,m as b,q as oe,M as y,a1 as E,a0 as i,V as F,X as L,u as a,a2 as M,a3 as A,i as ne,J as ue,j as ie,b as w,c as re,O as p,Z as u,y as h,N as U,aq as q,_ as D,a6 as de}from"./Dp9aCaJ6.js";import{_ as pe}from"./BWd1nnnI.js";import{_ as me}from"./BOuMW84o.js";import{_ as ce}from"./DzdJ9Qqg.js";import{P as fe}from"./BFjeuWRo.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{u as _e}from"./CcPlX2kz.js";import{n as ve,o as ye,p as be,q as ge}from"./BNnETjxs.js";const J=Symbol("rowContextKey"),we=["start","center","end","space-around","space-between","space-evenly"],xe=["top","middle","bottom"],$e=S({tag:{type:String,default:"div"},gutter:{type:Number,default:0},justify:{type:String,values:we,default:"start"},align:{type:String,values:xe}}),Ee=V({name:"ElRow"}),Ve=V({...Ee,props:$e,setup(C){const o=C,m=I("row"),r=b(()=>o.gutter);oe(J,{gutter:r});const g=b(()=>{const e={};return o.gutter&&(e.marginRight=e.marginLeft=`-${o.gutter/2}px`),e}),c=b(()=>[m.b(),m.is(`justify-${o.justify}`,o.justify!=="start"),m.is(`align-${o.align}`,!!o.align)]);return(e,d)=>(y(),E(A(e.tag),{class:L(a(c)),style:M(a(g))},{default:i(()=>[F(e.$slots,"default")]),_:3},8,["class","style"]))}});var Ce=B(Ve,[["__file","row.vue"]]);const Ne=K(Ce),he=S({tag:{type:String,default:"div"},span:{type:Number,default:24},offset:{type:Number,default:0},pull:{type:Number,default:0},push:{type:Number,default:0},xs:{type:x([Number,Object]),default:()=>$({})},sm:{type:x([Number,Object]),default:()=>$({})},md:{type:x([Number,Object]),default:()=>$({})},lg:{type:x([Number,Object]),default:()=>$({})},xl:{type:x([Number,Object]),default:()=>$({})}}),je=V({name:"ElCol"}),ke=V({...je,props:he,setup(C){const o=C,{gutter:m}=ne(J,{gutter:b(()=>0)}),r=I("col"),g=b(()=>{const e={};return m.value&&(e.paddingLeft=e.paddingRight=`${m.value/2}px`),e}),c=b(()=>{const e=[];return["span","offset","pull","push"].forEach(n=>{const f=o[n];R(f)&&(n==="span"?e.push(r.b(`${o[n]}`)):f>0&&e.push(r.b(`${n}-${o[n]}`)))}),["xs","sm","md","lg","xl"].forEach(n=>{R(o[n])?e.push(r.b(`${n}-${o[n]}`)):ue(o[n])&&Object.entries(o[n]).forEach(([f,N])=>{e.push(f!=="span"?r.b(`${n}-${f}-${N}`):r.b(`${n}-${N}`))})}),m.value&&e.push(r.is("guttered")),[r.b(),e]});return(e,d)=>(y(),E(A(e.tag),{class:L(a(c)),style:M(a(g))},{default:i(()=>[F(e.$slots,"default")]),_:3},8,["class","style"]))}});var Oe=B(ke,[["__file","col.vue"]]);const Pe=K(Oe),Re={class:"mb-4"},Ue={class:"grid grid-cols-2 gap-x-[20px]"},qe={class:"mt-4"},De={class:"flex flex-col items-center justify-center"},Se={class:"mt-4"},Ie={class:"mt-4"},We=V({__name:"editPop",emits:["success","close"],setup(C,{expose:o,emit:m}){const r=m,g=ie(),c=w(""),e=w({kb_id:"",fd_id:"",question:"",answer:"",files:[],images:[],video:[],uuid:""}),d=w(""),_=w(""),n=w([]),f=w([]),N=b(()=>{if(!d.value)return[];const s=f.value.find(t=>t.id===d.value);return s?s.examples:[]}),T=()=>{_.value=""},X=()=>{const s=f.value.find(t=>t.id===d.value);if(s){const t=s.examples.find(v=>v.id===_.value);t&&(e.value.question=t.question,e.value.answer=t.answer)}},Z=async()=>{try{const s=await ve();f.value=s,n.value=s.map(t=>({id:t.id,name:t.name}))}catch(s){console.error("获取示例库数据失败:",s)}};re(c,s=>{e.value.video=[{url:s,name:""}]});const{lockFn:z}=_e(async()=>{e.value.uuid?await ye({...e.value}):await be({...e.value}),r("success")}),G=async()=>{var t;const s=await ge({uuid:e.value.uuid});Object.keys(e.value).map(v=>{e.value[v]=s[v]}),c.value=((t=s.video[0])==null?void 0:t.url)||""};return o({open:s=>{g.value.open(),d.value="",_.value="",[e.value.kb_id,e.value.fd_id,e.value.uuid]=[s.kb_id,s.fd_id,s.uuid||""],s.hasOwnProperty("uuid")&&G(),Z()}}),(s,t)=>{const v=se,j=te,k=Pe,H=Ne,O=le,Q=pe,P=me,W=ce,Y=ae,ee=fe;return y(),E(ee,{ref_key:"popRef",ref:g,title:"录入数据",width:"800px",async:"",onConfirm:a(z),onClose:t[7]||(t[7]=l=>s.$emit("close"))},{default:i(()=>[p("div",null,[p("div",Re,[u(H,{gutter:20},{default:i(()=>[u(k,{span:12},{default:i(()=>[u(j,{modelValue:a(d),"onUpdate:modelValue":t[0]||(t[0]=l=>h(d)?d.value=l:null),placeholder:"请选择示例类别",clearable:"",class:"w-full",onChange:T},{default:i(()=>[(y(!0),U(D,null,q(a(n),l=>(y(),E(v,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(k,{span:12},{default:i(()=>[u(j,{modelValue:a(_),"onUpdate:modelValue":t[1]||(t[1]=l=>h(_)?_.value=l:null),placeholder:"请选择具体示例",clearable:"",class:"w-full",disabled:!a(d),onChange:X,filterable:""},{default:i(()=>[(y(!0),U(D,null,q(a(N),l=>(y(),E(v,{key:l.id,label:l.title,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),p("div",Ue,[u(O,{modelValue:a(e).question,"onUpdate:modelValue":t[2]||(t[2]=l=>a(e).question=l),type:"textarea",placeholder:"请输入文档内容，你可以理解为提问的问题（必填）",rows:"10"},null,8,["modelValue"]),u(O,{modelValue:a(e).answer,"onUpdate:modelValue":t[3]||(t[3]=l=>a(e).answer=l),type:"textarea",placeholder:"请填入补充内容，你可以理解为问题的答案",rows:"10"},null,8,["modelValue"])]),p("div",qe,[u(P,{files:a(e).images,"onUpdate:files":t[4]||(t[4]=l=>a(e).images=l),type:"image","list-type":"picture-card",limit:9,multiple:"","show-file-list":""},{default:i(()=>[p("div",De,[u(Q,{name:"el-icon-Plus",size:20}),t[8]||(t[8]=p("div",{class:"text-info mt-2 text-sm"},"上传图片",-1))])]),_:1},8,["files"]),t[9]||(t[9]=p("div",{class:"form-tips"},"最多上传9张",-1))]),p("div",Se,[u(W,{modelValue:a(c),"onUpdate:modelValue":t[5]||(t[5]=l=>h(c)?c.value=l:null),size:"80px"},null,8,["modelValue"]),t[10]||(t[10]=p("div",{class:"form-tips"},"格式为MP4，大小不能超过20M",-1))]),p("div",Ie,[u(P,{files:a(e).files,"onUpdate:files":t[6]||(t[6]=l=>a(e).files=l),type:"file","show-file-list":""},{tip:i(()=>t[12]||(t[12]=[p("div",{class:"el-upload__tip"}," 支持上传PDF、docx、excel、等文件格式 ",-1)])),default:i(()=>[u(Y,null,{default:i(()=>t[11]||(t[11]=[de("上传附件")])),_:1})]),_:1},8,["files"])])])]),_:1},8,["onConfirm"])}}});export{We as _};
