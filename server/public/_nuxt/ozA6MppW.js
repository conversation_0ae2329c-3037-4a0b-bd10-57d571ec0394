import{b as y,m as v,l as P,c as E,aA as H,M as w,N as b,_ as B,aq as I,a2 as N,O as K,V as T}from"./Dp9aCaJ6.js";import{a0 as j,bv as R}from"./Br7V4jS9.js";import{_ as X}from"./DlAUqK2U.js";const Y=({breakpoints:t,wrapperWidth:e,gutter:n,hasAroundGutter:r,initWidth:o})=>{const s=Object.keys(t).map(c=>Number(c)).sort((c,m)=>c-m);let a=e,i=!1;for(const c of s)if(e<=c){a=c,i=!0;break}if(!i)return o;const u=t[a].rowPerView;return r?(e-n)/u-n:(e-(u-1)*n)/u};function L(t){const e=y(0),n=y(null);j(n,a=>{const i=a[0],{width:u}=i.contentRect;e.value=u});const r=v(()=>Y({wrapperWidth:e.value,breakpoints:t.breakpoints,gutter:t.gutter,hasAroundGutter:t.hasAroundGutter,initWidth:t.width})),o=v(()=>{const a=t.hasAroundGutter?-t.gutter:t.gutter;return Math.floor((e.value+a)/(r.value+t.gutter))}),s=v(()=>{const a=t.hasAroundGutter?t.gutter:-t.gutter,i=o.value*(r.value+t.gutter)+a;return(e.value-i)/2});return{waterfallWrapper:n,wrapperWidth:e,colWidth:r,cols:o,offsetX:s}}function G(t,e){return new RegExp(`(^|\\s)${e}(\\s|$)`).test(t.className)}function k(t,e){if(G(t,e))return;const n=t.className.split(/\s+/);n.push(e),t.className=n.join(" ")}const _=(()=>{const t={webkit:"webkitTransform",Moz:"MozTransform",O:"OTransform",ms:"msTransform",standard:"transform"};{const e=document.createElement("div").style;for(const n in t){const r=t[n];if(e[r]!==void 0)return n}}return!1})();function d(t){return _===!1?!1:_==="standard"?t:_+t.charAt(0).toUpperCase()+t.substr(1)}const A=d("transform"),x=d("animation-duration"),D=d("animation-delay"),V=d("transition"),W=d("animation-fill-mode");function F(t,e,n,r,o){const s=y([]),a=y(0),i=l=>{const f=t.hasAroundGutter?l+1:l;return t.gutter*f+e.value*l+r.value},u=()=>{s.value=new Array(n.value).fill(t.hasAroundGutter?t.gutter:0)},c=q(t);return{wrapperHeight:a,layoutHandle:async()=>{u();const l=[];if(o&&o.value&&o.value.childNodes.forEach(f=>{f.className==="waterfall-item"&&l.push(f)}),l.length===0)return!1;for(let f=0;f<l.length;f++){const h=l[f],$=Math.min.apply(null,s.value),C=s.value.indexOf($),M=i(C),g=h.style;A&&(g[A]=`translate3d(${M}px,${$}px, 0)`),g.width=`${e.value}px`;const{height:O}=h.getBoundingClientRect();s.value[C]+=O+t.gutter,c(h,()=>{V&&(g[V]=`transform ${t.animationDuration/1e3}s`)})}a.value=Math.max.apply(null,s.value)}}}function q(t){return(e,n)=>{const r=e.firstChild;if(r&&!G(r,t.animationPrefix)){const o=`${t.animationDuration/1e3}s`,s=`${t.animationDelay/1e3}s`,a=r.style;a.visibility="visible",x&&(a[x]=o),D&&(a[D]=s),W&&(a[W]="both"),k(r,t.animationPrefix),k(r,t.animationEffect),n&&setTimeout(()=>{n()},t.animationDuration+t.animationDelay)}}}const S=P({props:{list:{type:Array,default:()=>[]},rowKey:{type:String,default:"id"},imgSelector:{type:String,default:"src"},width:{type:Number,default:200},breakpoints:{type:Object,default:()=>({1200:{rowPerView:3},800:{rowPerView:2},500:{rowPerView:1}})},gutter:{type:Number,default:10},hasAroundGutter:{type:Boolean,default:!0},animationPrefix:{type:String,default:"animate__animated"},animationEffect:{type:String,default:"fadeIn"},animationDuration:{type:Number,default:1e3},animationDelay:{type:Number,default:300},backgroundColor:{type:String,default:"#fff"},delay:{type:Number,default:300}},setup(t){const{waterfallWrapper:e,wrapperWidth:n,colWidth:r,cols:o,offsetX:s}=L(t),{wrapperHeight:a,layoutHandle:i}=F(t,r,o,s,e),u=R(()=>{i()},t.delay);return E(()=>[n,r,t.list],()=>{n.value>0&&u()},{deep:!0}),{colWidth:r,waterfallWrapper:e,wrapperHeight:a,getKey:(m,l)=>m[t.rowKey]||l,renderer:u}}}),p=()=>{H(t=>({"4507f2cc":t.backgroundColor}))},z=S.setup;S.setup=z?(t,e)=>(p(),z(t,e)):p;const U={class:"waterfall-card"};function J(t,e,n,r,o,s){return w(),b("div",{ref:"waterfallWrapper",class:"waterfall-list",style:N({height:`${t.wrapperHeight}px`})},[(w(!0),b(B,null,I(t.list,(a,i)=>(w(),b("div",{key:t.getKey(a,i),class:"waterfall-item",style:N({"--col-width":t.colWidth})},[K("div",U,[T(t.$slots,"item",{item:a,index:i},void 0,!0)])],4))),128))],4)}const et=X(S,[["render",J],["__scopeId","data-v-300c382b"]]);export{et as W};
