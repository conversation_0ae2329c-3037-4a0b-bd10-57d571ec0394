import{E as l}from"./DySxZBuW.js";import{b as c,_}from"./B5S_Er7H.js";/* empty css        */import u from"./CQe-ZETi.js";import{l as d,M as t,N as r,Z as p,a0 as m,O as i,_ as f,aq as x,u as e,a1 as h,a3 as k,a4 as v}from"./Dp9aCaJ6.js";import"./B4kN1zKe.js";import"./Cx3Arr0P.js";import"./CacOB3dg.js";import"./GM0TYtnE.js";import"./Tox-HrLw.js";import"./B8Nk-MqZ.js";import"./DuLVFwrw.js";import"./DBEz5tOh.js";/* empty css        */import"./DlAUqK2U.js";import"./BFUNNUBD.js";import"./Dc-0_M5P.js";import"./u53ZDtaC.js";import"./D5Z8Hj7a.js";import"./BPrqYaQ_.js";import"./WBO1DCcA.js";import"./DbRYflOL.js";import"./DCTLXrZ8.js";import"./IItDHZjE.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./BN6JKpcQ.js";import"./wD_qwxyk.js";import"./CN8DIg3d.js";import"./C8ZjkaO0.js";import"./D2fgxdmN.js";/* empty css        */import"./D7GZITCA.js";import"./BNZYpAgs.js";import"./Dv--ZGyq.js";import"./BgJCO7ll.js";import"./9Bti1uB6.js";/* empty css        */import"./Cwajr1uY.js";import"./pttW598Y.js";import"./DtKGz7pg.js";import"./CnBUfao0.js";import"./ChwnGhEb.js";import"./BE7GAo-z.js";import"./Cr5mhGPR.js";import"./DnwAmygV.js";import"./CqoCrSl4.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";import"./CNj1LJtr.js";import"./Cv1u9LLW.js";import"./CPVCwPzv.js";const N={class:"h-full"},S={class:"index"},Eo=d({__name:"index",setup(g){const a=c();return(y,B)=>{const n=l,s=_;return t(),r("div",null,[p(s,{name:"single-row"},{default:m(()=>[i("div",N,[p(n,null,{default:m(()=>[i("div",S,[(t(!0),r(f,null,x(e(a).pageIndex,o=>(t(),r("div",{key:o.id},[o.isShow?(t(),h(k(e(u)[o.name]),{key:0,prop:o.prop},null,8,["prop"])):v("",!0)]))),128))])]),_:1})])]),_:1})])}}});export{Eo as default};
