import{_ as t}from"./29s2fwiz.js";import{_ as s}from"./CgfYZQ7P.js";import{_ as d}from"./7VZD0pqa.js";import{_ as m}from"./D_21DFo-.js";import{_ as c}from"./CnVBXluB.js";import{l,b as u,M as e,N as _,u as o,a1 as r,a4 as i}from"./Dp9aCaJ6.js";const k={class:"p-main"},v=l({__name:"index",props:{appId:{}},setup(f){const a=u(),n=(p="")=>{a.value=p};return(p,y)=>(e(),_("div",k,[o(a)?i("",!0):(e(),r(c,{key:0,onClickItem:n})),o(a)==="web"?(e(),r(t,{key:1,"app-id":p.appId,onBack:n},null,8,["app-id"])):i("",!0),o(a)==="js"?(e(),r(d,{key:2,"app-id":p.appId,onBack:n},null,8,["app-id"])):i("",!0),o(a)==="oa"?(e(),r(m,{key:3,"app-id":p.appId,onBack:n},null,8,["app-id"])):i("",!0),o(a)==="api"?(e(),r(s,{key:4,type:4,"app-id":p.appId,onBack:n},null,8,["app-id"])):i("",!0),o(a)==="qwx"?(e(),r(s,{key:5,type:5,"app-id":p.appId,onBack:n},null,8,["app-id"])):i("",!0),o(a)==="yd"?(e(),r(s,{key:6,type:7,"app-id":p.appId,onBack:n},null,8,["app-id"])):i("",!0)]))}});export{v as _};
