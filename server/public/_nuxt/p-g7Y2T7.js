import{E as c}from"./CH0sQbfA.js";import{E as g}from"./CSeNE23V.js";import{E as x}from"./Dn0oQilg.js";import{b as y,v as D}from"./CzTOiozM.js";/* empty css        *//* empty css        */import{r as V,x as E,y as w,f as e,e as v}from"./1vm1IX2-.js";import L from"./DEgZFsq8.js";import b from"./Cd8JjsjM.js";import{_ as k}from"./FkGrpNeb.js";import S from"./DdsozZpI.js";import A from"./Cd6wDLUk.js";import P from"./CxXvfFUF.js";import{D as z}from"./CtFV0l7v.js";import{DrawModeEnum as i}from"./tONJIxwY.js";import{l as U,F as B,u as o,M as p,N as a,Z as r,a0 as l,O as s,aa as M}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./n7JpCWQv.js";import"./v-ANcAMI.js";import"./Cfoh3Adv.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./C_7xENts.js";import"./CaD5i18d.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CbXcIWFQ.js";import"./CLJ-nna1.js";import"./DflfJMcq.js";import"./9Bti1uB6.js";/* empty css        */import"./B4_CawNZ.js";import"./Pei5_z3G.js";/* empty css        */import"./CiHt1yKq.js";import"./4kyntJ1V.js";import"./CN93n0nx.js";import"./Doy9WyQ7.js";import"./Cj46UrnO.js";import"./Cpg3PDWZ.js";import"./DyTr6Gxj.js";import"./Dbnqlp4D.js";import"./CLdudPaH.js";import"./Cv6HhfEG.js";import"./CmG-gwTN.js";import"./D13MzIoa.js";import"./CQleycmY.js";/* empty css        *//* empty css        */import"./1sgdir_0.js";import"./CWd238n9.js";import"./m1W7XKYc.js";import"./DjwCd26w.js";import"./BGN3kN5U.js";import"./CcPlX2kz.js";import"./Dtb5JqrO.js";import"./BVqQ2B7o.js";import"./BWdDF8rn.js";import"./yL9UVO8F.js";const q={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},C={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},F={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},I=U({__name:"dalle",setup(O){const n=y();return B(()=>{V({draw_api:i.DALLE3,draw_model:i.DALLE3,action:"generate",prompt:"",negative_prompt:"",size:"1024x1024"}),E.model=i.DALLE3,w()}),(R,t)=>{const d=c,u=g,f=x,_=D;return o(n).config.switch.dalle3_status?(p(),a("div",q,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:l(()=>[s("div",C,[r(L,{modelValue:o(e).prompt,"onUpdate:modelValue":t[0]||(t[0]=m=>o(e).prompt=m),model:o(i).DALLE3},null,8,["modelValue","model"]),r(S,{modelValue:o(e).size,"onUpdate:modelValue":t[1]||(t[1]=m=>o(e).size=m)},null,8,["modelValue"]),r(A,{modelValue:o(e).style,"onUpdate:modelValue":t[2]||(t[2]=m=>o(e).style=m)},null,8,["modelValue"]),r(P,{modelValue:o(e).quality,"onUpdate:modelValue":t[3]||(t[3]=m=>o(e).quality=m)},null,8,["modelValue"])]),r(k)]),_:1}),M(r(b,{"element-loading-text":"正在加载数据..."},null,512),[[_,o(v)]])])):(p(),a("div",F,[r(f,null,{icon:l(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(z)},null,8,["src"])]),title:l(()=>t[4]||(t[4]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),Ho=N(I,[["__scopeId","data-v-9879be3f"]]);export{Ho as default};
