import{E as f}from"./L7ewHh_h.js";import{b as _,E as x,dz as v,dA as b}from"./BQ-RMI0l.js";/* empty css        */import{E as g}from"./De57gGYj.js";import{l as C,b as E,m as u,c as N,M as T,a1 as w,a0 as n,O as l,Z as p,a6 as B,u as V}from"./Dp9aCaJ6.js";import{_ as h}from"./DlAUqK2U.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";const y=["innerHTML"],S={class:"flex-1 flex justify-center items-center bg-body pt-[20px]"},k=C({__name:"index",setup(D){const i=_(),o=E(!1),c=u(()=>i.getBulletinConfig.bulletin_content),d=u(()=>i.getBulletinConfig.is_bulletin),m=t=>{const e=v(b),s=new Date().toDateString(),a=!e.value||e.value!==s;return a&&t&&(e.value=s),a};return N(()=>d.value,t=>{t&&m(t)&&(o.value=!0)},{deep:!0,immediate:!0}),(t,e)=>{const s=f,a=x;return T(),w(V(g),{modelValue:o.value,"onUpdate:modelValue":e[1]||(e[1]=r=>o.value=r),width:"600","append-to-body":""},{header:n(()=>e[2]||(e[2]=[l("div",{class:"text-lg text-center font-medium"},"公告",-1)])),default:n(()=>[p(s,{"max-height":"400px"},{default:n(()=>[l("div",{class:"richText",innerHTML:c.value},null,8,y)]),_:1}),l("div",S,[p(a,{type:"primary",size:"large",onClick:e[0]||(e[0]=r=>o.value=!1)},{default:n(()=>e[3]||(e[3]=[B(" 我知道了 ")])),_:1})])]),_:1},8,["modelValue"])}}}),K=h(k,[["__scopeId","data-v-cac163f6"]]);export{K as default};
