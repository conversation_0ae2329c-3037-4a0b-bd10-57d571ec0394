import{h as z,e as F,o as P,E as I,p as h}from"./DAgm18qP.js";import{_ as D}from"./DMHEbzLi.js";import{_ as M}from"./BCVRiZaz.js";import"./DP2rzg_V.js";/* empty css        */import{P as j}from"./CVKgQtoK.js";import{l as O,j as w,M as m,a1 as V,a0 as a,Z as s,u as t,a4 as f,N as x,_ as v,O as p,a6 as T}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./j7hld3TB.js";import"./BVL3rOLc.js";import"./B4CR6vtE.js";import"./CXZvQ2S9.js";import"./TR-nE6Zk.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */const Z={class:"flex-1"},$={class:"flex-1"},A={class:"max-w-[600px]"},ie=O({__name:"edit-qa",props:{modelValue:{},title:{},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","confirm"],setup(b,{expose:q,emit:B}){const E=b,_=B,c=w(),d=w(),o=z(E,"modelValue",_),R={question:[{validator(l,e,i,r,u){e?i():o.value.type===1?i("请输入内容"):o.value.type===2&&i("请输入问题")}}],answer:[{validator(l,e,i,r,u){e?i():i("请输入答案")}}]},U=()=>{var l;(l=d.value)==null||l.open()},g=()=>{var l;(l=d.value)==null||l.close()},k=async()=>{var l;await((l=c.value)==null?void 0:l.validate()),_("confirm")};return q({open:U,close:g}),(l,e)=>{const i=F,r=P,u=D,y=M,C=I,N=h;return m(),V(j,{ref_key:"popupRef",ref:d,title:l.title,width:"800px","destroy-on-close":!0,async:"",onConfirm:k},{default:a(()=>[s(N,{ref_key:"formRef",ref:c,model:t(o),rules:R,"label-width":"100px",disabled:l.disabled},{default:a(()=>[t(o).type===1?(m(),V(r,{key:0,label:"内容",prop:"question"},{default:a(()=>[s(i,{modelValue:t(o).question,"onUpdate:modelValue":e[0]||(e[0]=n=>t(o).question=n),placeholder:"请输入内容",type:"textarea",resize:"none",rows:20,clearable:""},null,8,["modelValue"])]),_:1})):f("",!0),t(o).type===2?(m(),x(v,{key:1},[s(r,{label:"提问问题",prop:"question"},{default:a(()=>[s(i,{modelValue:t(o).question,"onUpdate:modelValue":e[1]||(e[1]=n=>t(o).question=n),placeholder:"请输入问题",type:"textarea",resize:"none",rows:6,maxlength:"600","show-word-limit":"",clearable:""},null,8,["modelValue"])]),_:1}),s(r,{label:"问题答案",prop:"answer"},{default:a(()=>[s(i,{modelValue:t(o).answer,"onUpdate:modelValue":e[2]||(e[2]=n=>t(o).answer=n),placeholder:"请输入答案",type:"textarea",resize:"none",rows:20,clearable:""},null,8,["modelValue"])]),_:1}),t(o).type===2&&t(o).method===1?(m(),x(v,{key:0},[s(r,{label:"上传图片"},{default:a(()=>[p("div",Z,[p("div",null,[s(y,{files:t(o).images,"onUpdate:files":e[3]||(e[3]=n=>t(o).images=n),type:"image","list-type":"picture-card",limit:9,multiple:"","show-file-list":""},{default:a(()=>[s(u,{name:"el-icon-Plus",size:20})]),_:1},8,["files"])]),e[5]||(e[5]=p("div",{class:"form-tips"},"最多支持上传 9 张图",-1))])]),_:1}),s(r,{label:"上传附件"},{default:a(()=>[p("div",$,[p("div",A,[s(y,{files:t(o).files,"onUpdate:files":e[4]||(e[4]=n=>t(o).files=n),type:"file","show-file-list":""},{tip:a(()=>e[7]||(e[7]=[p("div",{class:"el-upload__tip"}," 支持上传PDF、docx、excel、等文件格式 ",-1)])),default:a(()=>[s(C,null,{default:a(()=>e[6]||(e[6]=[T("上传附件")])),_:1})]),_:1},8,["files"])])])]),_:1})],64)):f("",!0)],64)):f("",!0)]),_:1},8,["model","disabled"])]),_:1},8,["title"])}}});export{ie as default};
