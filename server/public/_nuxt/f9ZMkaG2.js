import{_ as X}from"./C8ZjkaO0.js";import{K as F,aM as W,Z as q,M as H,O as J,al as Y,J as I,bJ as ee,T as se,P as te,Q as ae}from"./B5S_Er7H.js";import{u as ne}from"./BuewU-zc.js";import{l as k,c as V,q as ie,M as n,N as f,V as w,X as r,u as e,a as K,b as z,i as re,F as oe,o as le,m as c,r as ce,a4 as E,O as C,a2 as D,a1 as u,a0 as $,a3 as ue,Z as M,a7 as P,a6 as A,aq as pe,_ as ve}from"./Dp9aCaJ6.js";import{useSearch as de}from"./DIy4dbf4.js";import{TypeEnums as T}from"./BLV0QRdm.js";const me=F({space:{type:[Number,String],default:""},active:{type:Number,default:0},direction:{type:String,default:"horizontal",values:["horizontal","vertical"]},alignCenter:{type:Boolean},simple:{type:Boolean},finishStatus:{type:String,values:["wait","process","finish","error","success"],default:"finish"},processStatus:{type:String,values:["wait","process","finish","error","success"],default:"process"}}),fe={[W]:(S,d)=>[S,d].every(q)},Se=k({name:"ElSteps"}),_e=k({...Se,props:me,emits:fe,setup(S,{emit:d}){const s=S,l=H("steps"),{children:p,addChild:v,removeChild:a}=ne(K(),"ElStep");return V(p,()=>{p.value.forEach((i,o)=>{i.setIndex(o)})}),ie("ElSteps",{props:s,steps:p,addStep:v,removeStep:a}),V(()=>s.active,(i,o)=>{d(W,i,o)}),(i,o)=>(n(),f("div",{class:r([e(l).b(),e(l).m(i.simple?"simple":i.direction)])},[w(i.$slots,"default")],2))}});var ye=J(_e,[["__file","steps.vue"]]);const he=F({title:{type:String,default:""},icon:{type:Y},description:{type:String,default:""},status:{type:String,values:["","wait","process","finish","error","success"],default:""}}),ge=k({name:"ElStep"}),Ce=k({...ge,props:he,setup(S){const d=S,s=H("step"),l=z(-1),p=z({}),v=z(""),a=re("ElSteps"),i=K();oe(()=>{V([()=>a.props.active,()=>a.props.processStatus,()=>a.props.finishStatus],([t])=>{U(t)},{immediate:!0})}),le(()=>{a.removeStep(O.uid)});const o=c(()=>d.status||v.value),N=c(()=>{const t=a.steps.value[l.value-1];return t?t.currentStatus:"wait"}),h=c(()=>a.props.alignCenter),_=c(()=>a.props.direction==="vertical"),m=c(()=>a.props.simple),b=c(()=>a.steps.value.length),L=c(()=>{var t;return((t=a.steps.value[b.value-1])==null?void 0:t.uid)===(i==null?void 0:i.uid)}),g=c(()=>m.value?"":a.props.space),j=c(()=>[s.b(),s.is(m.value?"simple":a.props.direction),s.is("flex",L.value&&!g.value&&!h.value),s.is("center",h.value&&!_.value&&!m.value)]),G=c(()=>{const t={flexBasis:q(g.value)?`${g.value}px`:g.value?g.value:`${100/(b.value-(h.value?0:1))}%`};return _.value||L.value&&(t.maxWidth=`${100/b.value}%`),t}),Q=t=>{l.value=t},R=t=>{const y=t==="wait",B={transitionDelay:`${y?"-":""}${150*l.value}ms`},x=t===a.props.processStatus||y?0:100;B.borderWidth=x&&!m.value?"1px":0,B[a.props.direction==="vertical"?"height":"width"]=`${x}%`,p.value=B},U=t=>{t>l.value?v.value=a.props.finishStatus:t===l.value&&N.value!=="error"?v.value=a.props.processStatus:v.value="wait";const y=a.steps.value[l.value-1];y&&y.calcProgress(v.value)},O=ce({uid:i.uid,currentStatus:o,setIndex:Q,calcProgress:R});return a.addStep(O),(t,y)=>(n(),f("div",{style:D(e(G)),class:r(e(j))},[E(" icon & line "),C("div",{class:r([e(s).e("head"),e(s).is(e(o))])},[e(m)?E("v-if",!0):(n(),f("div",{key:0,class:r(e(s).e("line"))},[C("i",{class:r(e(s).e("line-inner")),style:D(p.value)},null,6)],2)),C("div",{class:r([e(s).e("icon"),e(s).is(t.icon||t.$slots.icon?"icon":"text")])},[w(t.$slots,"icon",{},()=>[t.icon?(n(),u(e(I),{key:0,class:r(e(s).e("icon-inner"))},{default:$(()=>[(n(),u(ue(t.icon)))]),_:1},8,["class"])):e(o)==="success"?(n(),u(e(I),{key:1,class:r([e(s).e("icon-inner"),e(s).is("status")])},{default:$(()=>[M(e(ee))]),_:1},8,["class"])):e(o)==="error"?(n(),u(e(I),{key:2,class:r([e(s).e("icon-inner"),e(s).is("status")])},{default:$(()=>[M(e(se))]),_:1},8,["class"])):e(m)?E("v-if",!0):(n(),f("div",{key:3,class:r(e(s).e("icon-inner"))},P(l.value+1),3))])],2)],2),E(" title & description "),C("div",{class:r(e(s).e("main"))},[C("div",{class:r([e(s).e("title"),e(s).is(e(o))])},[w(t.$slots,"title",{},()=>[A(P(t.title),1)])],2),e(m)?(n(),f("div",{key:0,class:r(e(s).e("arrow"))},null,2)):(n(),f("div",{key:1,class:r([e(s).e("description"),e(s).is(e(o))])},[w(t.$slots,"description",{},()=>[A(P(t.description),1)])],2))],2)],6))}});var Z=J(Ce,[["__file","item.vue"]]);const $e=te(ye,{Step:Z}),ke=ae(Z),ze=k({__name:"steps",setup(S){const{options:d,result:s}=de(),l=c(()=>{switch(d.value.type){case T.ALL:return"全网";case T.DOC:return"文档";case T.SCHOLAR:return"学术"}}),p=c(()=>[{title:"问题分析"},{title:`${l.value}搜索`},{title:"整理答案"},{title:"完成"}]);return(v,a)=>{const i=X,o=ke,N=$e;return n(),u(N,{active:e(s).status,style:{"max-width":"500px"},"align-center":"","process-status":"finish"},{default:$(()=>[(n(!0),f(ve,null,pe(e(p),(h,_)=>(n(),u(o,{key:_,title:h.title},{icon:$(()=>[e(s).status===_?(n(),u(i,{key:0,name:"el-icon-Loading",style:{animation:"loading-rotate 2s linear infinite"},size:24})):e(s).status>_?(n(),u(i,{key:1,name:"el-icon-SuccessFilled",size:24})):(n(),u(i,{key:2,name:"local-icon-circular",size:24}))]),_:2},1032,["title"]))),128))]),_:1},8,["active"])}}});export{ze as _};
