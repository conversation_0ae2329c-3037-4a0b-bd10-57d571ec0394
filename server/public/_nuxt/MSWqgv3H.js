import{h as D,e as I,o as N,E as U,p as L,f as v}from"./B1MekKW7.js";import{_ as $}from"./uE1Ww79i.js";import"./DP2rzg_V.js";/* empty css        */import{u as z}from"./CcPlX2kz.js";import{w as M}from"./C4bZp7qs.js";import{l as O,b as H,M as s,N as u,O as m,Z as a,a0 as r,u as c,y as R,a6 as k,_ as y,aq as V,a7 as x,a1 as S}from"./Dp9aCaJ6.js";const T={class:"web-page"},Z={class:"py-4"},j={class:"flex-1"},A={class:"mb-2 text-tx-primary font-medium text-lg"},ae=O({__name:"web-page",props:{modelValue:{}},emits:["update:modelValue"],setup(E,{emit:w}){const o=D(E,"modelValue",w),t=H(""),g=async l=>{await v.confirm(`确定删除：${l.name}？`);const e=o.value.indexOf(l);e!==-1&&o.value.splice(e,1)},{lockFn:b,isLock:h}=z(async()=>{if(!t.value)return v.msgError("请输入网页链接");const l=await M({url:t.value.split(`
`).filter(Boolean)});o.value=[...l.map(e=>({data:[{a:"",q:e.content}],path:"",name:e.url})),...o.value],t.value=""});return(l,e)=>{const p=I,d=N,_=U,B=L,C=$;return s(),u("div",T,[m("div",Z,[a(B,null,{default:r(()=>[a(d,null,{default:r(()=>[m("div",j,[a(p,{modelValue:c(t),"onUpdate:modelValue":e[0]||(e[0]=n=>R(t)?t.value=n:null),placeholder:"请输入要解析的网页链接，添加多个请按回车键分隔",type:"textarea",resize:"none",rows:6},null,8,["modelValue"])])]),_:1}),a(d,null,{default:r(()=>[a(_,{type:"primary",loading:c(h),onClick:c(b)},{default:r(()=>e[1]||(e[1]=[k(" 解析 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1}),m("div",null,[(s(!0),u(y,null,V(c(o),(n,f)=>(s(),u("div",{key:f,class:"mb-4"},[m("div",A,[k(" #"+x(f+1)+" "+x(n.name)+" ",1),a(_,{link:"",type:"primary"},{default:r(()=>[a(C,{name:"el-icon-Delete",onClick:i=>g(n)},null,8,["onClick"])]),_:2},1024)]),(s(!0),u(y,null,V(n.data,(i,F)=>(s(),S(p,{key:F,modelValue:i.q,"onUpdate:modelValue":q=>i.q=q,placeholder:"文件内容，空内容会自动省略",type:"textarea",resize:"none",rows:15},null,8,["modelValue","onUpdate:modelValue"]))),128))]))),128))])])])}}});export{ae as _};
