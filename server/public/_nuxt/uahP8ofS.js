/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function bs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const J={},$t=[],Fe=()=>{},_a=()=>!1,An=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ri=e=>e.startsWith("onUpdate:"),se=Object.assign,Ni=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ta=Object.prototype.hasOwnProperty,ee=(e,t)=>Ta.call(e,t),z=Array.isArray,zt=e=>Zt(e)==="[object Map]",Lt=e=>Zt(e)==="[object Set]",pr=e=>Zt(e)==="[object Date]",xa=e=>Zt(e)==="[object RegExp]",W=e=>typeof e=="function",oe=e=>typeof e=="string",Ge=e=>typeof e=="symbol",ne=e=>e!==null&&typeof e=="object",Di=e=>(ne(e)||W(e))&&W(e.then)&&W(e.catch),oo=Object.prototype.toString,Zt=e=>oo.call(e),Ea=e=>Zt(e).slice(8,-1),ws=e=>Zt(e)==="[object Object]",Bi=e=>oe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,jt=bs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Ss=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ca=/-(\w)/g,me=Ss(e=>e.replace(Ca,(t,n)=>n?n.toUpperCase():"")),Pa=/\B([A-Z])/g,Pe=Ss(e=>e.replace(Pa,"-$1").toLowerCase()),On=Ss(e=>e.charAt(0).toUpperCase()+e.slice(1)),un=Ss(e=>e?`on${On(e)}`:""),Te=(e,t)=>!Object.is(e,t),Gt=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},lo=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ts=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ns=e=>{const t=oe(e)?Number(e):NaN;return isNaN(t)?e:t};let hr;const _s=()=>hr||(hr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),Ma="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol",Aa=bs(Ma);function In(e){if(z(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],i=oe(s)?Ra(s):In(s);if(i)for(const r in i)t[r]=i[r]}return t}else if(oe(e)||ne(e))return e}const Oa=/;(?![^(]*\))/g,Ia=/:([^]+)/,La=/\/\*[^]*?\*\//g;function Ra(e){const t={};return e.replace(La,"").split(Oa).forEach(n=>{if(n){const s=n.split(Ia);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Ln(e){let t="";if(oe(e))t=e;else if(z(e))for(let n=0;n<e.length;n++){const s=Ln(e[n]);s&&(t+=s+" ")}else if(ne(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Na(e){if(!e)return null;let{class:t,style:n}=e;return t&&!oe(t)&&(e.class=Ln(t)),n&&(e.style=In(n)),e}const Da="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ba=bs(Da);function ao(e){return!!e||e===""}function Fa(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=vt(e[s],t[s]);return n}function vt(e,t){if(e===t)return!0;let n=pr(e),s=pr(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Ge(e),s=Ge(t),n||s)return e===t;if(n=z(e),s=z(t),n||s)return n&&s?Fa(e,t):!1;if(n=ne(e),s=ne(t),n||s){if(!n||!s)return!1;const i=Object.keys(e).length,r=Object.keys(t).length;if(i!==r)return!1;for(const o in e){const a=e.hasOwnProperty(o),l=t.hasOwnProperty(o);if(a&&!l||!a&&l||!vt(e[o],t[o]))return!1}}return String(e)===String(t)}function Ts(e,t){return e.findIndex(n=>vt(n,t))}const co=e=>!!(e&&e.__v_isRef===!0),fo=e=>oe(e)?e:e==null?"":z(e)||ne(e)&&(e.toString===oo||!W(e.toString))?co(e)?fo(e.value):JSON.stringify(e,uo,2):String(e),uo=(e,t)=>co(t)?uo(e,t.value):zt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,i],r)=>(n[js(s,r)+" =>"]=i,n),{})}:Lt(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>js(n))}:Ge(t)?js(t):ne(t)&&!z(t)&&!ws(t)?String(t):t,js=(e,t="")=>{var n;return Ge(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let _e;class Fi{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=_e,!t&&_e&&(this.index=(_e.scopes||(_e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=_e;try{return _e=this,t()}finally{_e=n}}}on(){_e=this}off(){_e=this.parent}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function Va(e){return new Fi(e)}function po(){return _e}function Ha(e,t=!1){_e&&_e.cleanups.push(e)}let re;const Gs=new WeakSet;class bn{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,_e&&_e.active&&_e.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Gs.has(this)&&(Gs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||go(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,gr(this),mo(this);const t=re,n=je;re=this,je=!0;try{return this.fn()}finally{vo(this),re=t,je=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ki(t);this.deps=this.depsTail=void 0,gr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Gs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ai(this)&&this.run()}get dirty(){return ai(this)}}let ho=0,dn,pn;function go(e,t=!1){if(e.flags|=8,t){e.next=pn,pn=e;return}e.next=dn,dn=e}function Vi(){ho++}function Hi(){if(--ho>0)return;if(pn){let t=pn;for(pn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;dn;){let t=dn;for(dn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function mo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function vo(e){let t,n=e.depsTail,s=n;for(;s;){const i=s.prevDep;s.version===-1?(s===n&&(n=i),ki(s),ka(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=i}e.deps=t,e.depsTail=n}function ai(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(yo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function yo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===wn))return;e.globalVersion=wn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!ai(e)){e.flags&=-3;return}const n=re,s=je;re=e,je=!0;try{mo(e);const i=e.fn(e._value);(t.version===0||Te(i,e._value))&&(e._value=i,t.version++)}catch(i){throw t.version++,i}finally{re=n,je=s,vo(e),e.flags&=-3}}function ki(e,t=!1){const{dep:n,prevSub:s,nextSub:i}=e;if(s&&(s.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)ki(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function ka(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}function $a(e,t){e.effect instanceof bn&&(e=e.effect.fn);const n=new bn(e);t&&se(n,t);try{n.run()}catch(i){throw n.stop(),i}const s=n.run.bind(n);return s.effect=n,s}function za(e){e.effect.stop()}let je=!0;const bo=[];function wt(){bo.push(je),je=!1}function St(){const e=bo.pop();je=e===void 0?!0:e}function gr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=re;re=void 0;try{t()}finally{re=n}}}let wn=0;class ja{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class xs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!re||!je||re===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==re)n=this.activeLink=new ja(re,this),re.deps?(n.prevDep=re.depsTail,re.depsTail.nextDep=n,re.depsTail=n):re.deps=re.depsTail=n,wo(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=re.depsTail,n.nextDep=void 0,re.depsTail.nextDep=n,re.depsTail=n,re.deps===n&&(re.deps=s)}return n}trigger(t){this.version++,wn++,this.notify(t)}notify(t){Vi();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Hi()}}}function wo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)wo(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const ss=new WeakMap,Et=Symbol(""),ci=Symbol(""),Sn=Symbol("");function ye(e,t,n){if(je&&re){let s=ss.get(e);s||ss.set(e,s=new Map);let i=s.get(n);i||(s.set(n,i=new xs),i.map=s,i.key=n),i.track()}}function et(e,t,n,s,i,r){const o=ss.get(e);if(!o){wn++;return}const a=l=>{l&&l.trigger()};if(Vi(),t==="clear")o.forEach(a);else{const l=z(e),f=l&&Bi(n);if(l&&n==="length"){const c=Number(s);o.forEach((u,p)=>{(p==="length"||p===Sn||!Ge(p)&&p>=c)&&a(u)})}else switch((n!==void 0||o.has(void 0))&&a(o.get(n)),f&&a(o.get(Sn)),t){case"add":l?f&&a(o.get("length")):(a(o.get(Et)),zt(e)&&a(o.get(ci)));break;case"delete":l||(a(o.get(Et)),zt(e)&&a(o.get(ci)));break;case"set":zt(e)&&a(o.get(Et));break}}Hi()}function Ga(e,t){const n=ss.get(e);return n&&n.get(t)}function Bt(e){const t=Q(e);return t===e?t:(ye(t,"iterate",Sn),Re(e)?t:t.map(be))}function Es(e){return ye(e=Q(e),"iterate",Sn),e}const Ua={__proto__:null,[Symbol.iterator](){return Us(this,Symbol.iterator,be)},concat(...e){return Bt(this).concat(...e.map(t=>z(t)?Bt(t):t))},entries(){return Us(this,"entries",e=>(e[1]=be(e[1]),e))},every(e,t){return Je(this,"every",e,t,void 0,arguments)},filter(e,t){return Je(this,"filter",e,t,n=>n.map(be),arguments)},find(e,t){return Je(this,"find",e,t,be,arguments)},findIndex(e,t){return Je(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Je(this,"findLast",e,t,be,arguments)},findLastIndex(e,t){return Je(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Je(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ws(this,"includes",e)},indexOf(...e){return Ws(this,"indexOf",e)},join(e){return Bt(this).join(e)},lastIndexOf(...e){return Ws(this,"lastIndexOf",e)},map(e,t){return Je(this,"map",e,t,void 0,arguments)},pop(){return on(this,"pop")},push(...e){return on(this,"push",e)},reduce(e,...t){return mr(this,"reduce",e,t)},reduceRight(e,...t){return mr(this,"reduceRight",e,t)},shift(){return on(this,"shift")},some(e,t){return Je(this,"some",e,t,void 0,arguments)},splice(...e){return on(this,"splice",e)},toReversed(){return Bt(this).toReversed()},toSorted(e){return Bt(this).toSorted(e)},toSpliced(...e){return Bt(this).toSpliced(...e)},unshift(...e){return on(this,"unshift",e)},values(){return Us(this,"values",be)}};function Us(e,t,n){const s=Es(e),i=s[t]();return s!==e&&!Re(e)&&(i._next=i.next,i.next=()=>{const r=i._next();return r.value&&(r.value=n(r.value)),r}),i}const Wa=Array.prototype;function Je(e,t,n,s,i,r){const o=Es(e),a=o!==e&&!Re(e),l=o[t];if(l!==Wa[t]){const u=l.apply(e,r);return a?be(u):u}let f=n;o!==e&&(a?f=function(u,p){return n.call(this,be(u),p,e)}:n.length>2&&(f=function(u,p){return n.call(this,u,p,e)}));const c=l.call(o,f,s);return a&&i?i(c):c}function mr(e,t,n,s){const i=Es(e);let r=n;return i!==e&&(Re(e)?n.length>3&&(r=function(o,a,l){return n.call(this,o,a,l,e)}):r=function(o,a,l){return n.call(this,o,be(a),l,e)}),i[t](r,...s)}function Ws(e,t,n){const s=Q(e);ye(s,"iterate",Sn);const i=s[t](...n);return(i===-1||i===!1)&&As(n[0])?(n[0]=Q(n[0]),s[t](...n)):i}function on(e,t,n=[]){wt(),Vi();const s=Q(e)[t].apply(e,n);return Hi(),St(),s}const Ka=bs("__proto__,__v_isRef,__isVue"),So=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ge));function qa(e){Ge(e)||(e=String(e));const t=Q(this);return ye(t,"has",e),t.hasOwnProperty(e)}class _o{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(i?r?Mo:Po:r?Co:Eo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=z(t);if(!i){let l;if(o&&(l=Ua[n]))return l;if(n==="hasOwnProperty")return qa}const a=Reflect.get(t,n,he(t)?t:s);return(Ge(n)?So.has(n):Ka(n))||(i||ye(t,"get",n),r)?a:he(a)?o&&Bi(n)?a:a.value:ne(a)?i?$i(a):Ps(a):a}}class To extends _o{constructor(t=!1){super(!1,t)}set(t,n,s,i){let r=t[n];if(!this._isShallow){const l=yt(r);if(!Re(s)&&!yt(s)&&(r=Q(r),s=Q(s)),!z(t)&&he(r)&&!he(s))return l?!1:(r.value=s,!0)}const o=z(t)&&Bi(n)?Number(n)<t.length:ee(t,n),a=Reflect.set(t,n,s,he(t)?t:i);return t===Q(i)&&(o?Te(s,r)&&et(t,"set",n,s):et(t,"add",n,s)),a}deleteProperty(t,n){const s=ee(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&s&&et(t,"delete",n,void 0),i}has(t,n){const s=Reflect.has(t,n);return(!Ge(n)||!So.has(n))&&ye(t,"has",n),s}ownKeys(t){return ye(t,"iterate",z(t)?"length":Et),Reflect.ownKeys(t)}}class xo extends _o{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ya=new To,Xa=new xo,Ja=new To(!0),Za=new xo(!0),fi=e=>e,Vn=e=>Reflect.getPrototypeOf(e);function Qa(e,t,n){return function(...s){const i=this.__v_raw,r=Q(i),o=zt(r),a=e==="entries"||e===Symbol.iterator&&o,l=e==="keys"&&o,f=i[e](...s),c=n?fi:t?ui:be;return!t&&ye(r,"iterate",l?ci:Et),{next(){const{value:u,done:p}=f.next();return p?{value:u,done:p}:{value:a?[c(u[0]),c(u[1])]:c(u),done:p}},[Symbol.iterator](){return this}}}}function Hn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ec(e,t){const n={get(i){const r=this.__v_raw,o=Q(r),a=Q(i);e||(Te(i,a)&&ye(o,"get",i),ye(o,"get",a));const{has:l}=Vn(o),f=t?fi:e?ui:be;if(l.call(o,i))return f(r.get(i));if(l.call(o,a))return f(r.get(a));r!==o&&r.get(i)},get size(){const i=this.__v_raw;return!e&&ye(Q(i),"iterate",Et),Reflect.get(i,"size",i)},has(i){const r=this.__v_raw,o=Q(r),a=Q(i);return e||(Te(i,a)&&ye(o,"has",i),ye(o,"has",a)),i===a?r.has(i):r.has(i)||r.has(a)},forEach(i,r){const o=this,a=o.__v_raw,l=Q(a),f=t?fi:e?ui:be;return!e&&ye(l,"iterate",Et),a.forEach((c,u)=>i.call(r,f(c),f(u),o))}};return se(n,e?{add:Hn("add"),set:Hn("set"),delete:Hn("delete"),clear:Hn("clear")}:{add(i){!t&&!Re(i)&&!yt(i)&&(i=Q(i));const r=Q(this);return Vn(r).has.call(r,i)||(r.add(i),et(r,"add",i,i)),this},set(i,r){!t&&!Re(r)&&!yt(r)&&(r=Q(r));const o=Q(this),{has:a,get:l}=Vn(o);let f=a.call(o,i);f||(i=Q(i),f=a.call(o,i));const c=l.call(o,i);return o.set(i,r),f?Te(r,c)&&et(o,"set",i,r):et(o,"add",i,r),this},delete(i){const r=Q(this),{has:o,get:a}=Vn(r);let l=o.call(r,i);l||(i=Q(i),l=o.call(r,i)),a&&a.call(r,i);const f=r.delete(i);return l&&et(r,"delete",i,void 0),f},clear(){const i=Q(this),r=i.size!==0,o=i.clear();return r&&et(i,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=Qa(i,e,t)}),n}function Cs(e,t){const n=ec(e,t);return(s,i,r)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?s:Reflect.get(ee(n,i)&&i in s?n:s,i,r)}const tc={get:Cs(!1,!1)},nc={get:Cs(!1,!0)},sc={get:Cs(!0,!1)},ic={get:Cs(!0,!0)},Eo=new WeakMap,Co=new WeakMap,Po=new WeakMap,Mo=new WeakMap;function rc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function oc(e){return e.__v_skip||!Object.isExtensible(e)?0:rc(Ea(e))}function Ps(e){return yt(e)?e:Ms(e,!1,Ya,tc,Eo)}function Ao(e){return Ms(e,!1,Ja,nc,Co)}function $i(e){return Ms(e,!0,Xa,sc,Po)}function lc(e){return Ms(e,!0,Za,ic,Mo)}function Ms(e,t,n,s,i){if(!ne(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=i.get(e);if(r)return r;const o=oc(e);if(o===0)return e;const a=new Proxy(e,o===2?s:n);return i.set(e,a),a}function ht(e){return yt(e)?ht(e.__v_raw):!!(e&&e.__v_isReactive)}function yt(e){return!!(e&&e.__v_isReadonly)}function Re(e){return!!(e&&e.__v_isShallow)}function As(e){return e?!!e.__v_raw:!1}function Q(e){const t=e&&e.__v_raw;return t?Q(t):e}function Oo(e){return!ee(e,"__v_skip")&&Object.isExtensible(e)&&lo(e,"__v_skip",!0),e}const be=e=>ne(e)?Ps(e):e,ui=e=>ne(e)?$i(e):e;function he(e){return e?e.__v_isRef===!0:!1}function fe(e){return Lo(e,!1)}function Io(e){return Lo(e,!0)}function Lo(e,t){return he(e)?e:new ac(e,t)}class ac{constructor(t,n){this.dep=new xs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Q(t),this._value=n?t:be(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Re(t)||yt(t);t=s?t:Q(t),Te(t,n)&&(this._rawValue=t,this._value=s?t:be(t),this.dep.trigger())}}function cc(e){e.dep&&e.dep.trigger()}function Os(e){return he(e)?e.value:e}function fc(e){return W(e)?e():Os(e)}const uc={get:(e,t,n)=>t==="__v_raw"?e:Os(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const i=e[t];return he(i)&&!he(n)?(i.value=n,!0):Reflect.set(e,t,n,s)}};function zi(e){return ht(e)?e:new Proxy(e,uc)}class dc{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new xs,{get:s,set:i}=t(n.track.bind(n),n.trigger.bind(n));this._get=s,this._set=i}get value(){return this._value=this._get()}set value(t){this._set(t)}}function Ro(e){return new dc(e)}function pc(e){const t=z(e)?new Array(e.length):{};for(const n in e)t[n]=No(e,n);return t}class hc{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Ga(Q(this._object),this._key)}}class gc{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function mc(e,t,n){return he(e)?e:W(e)?new gc(e):ne(e)&&arguments.length>1?No(e,t,n):fe(e)}function No(e,t,n){const s=e[t];return he(s)?s:new hc(e,t,n)}class vc{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new xs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=wn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&re!==this)return go(this,!0),!0}get value(){const t=this.dep.track();return yo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function yc(e,t,n=!1){let s,i;return W(e)?s=e:(s=e.get,i=e.set),new vc(s,i,n)}const bc={GET:"get",HAS:"has",ITERATE:"iterate"},wc={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},kn={},is=new WeakMap;let ct;function Sc(){return ct}function Do(e,t=!1,n=ct){if(n){let s=is.get(n);s||is.set(n,s=[]),s.push(e)}}function _c(e,t,n=J){const{immediate:s,deep:i,once:r,scheduler:o,augmentJob:a,call:l}=n,f=g=>i?g:Re(g)||i===!1||i===0?tt(g,1):tt(g);let c,u,p,h,v=!1,b=!1;if(he(e)?(u=()=>e.value,v=Re(e)):ht(e)?(u=()=>f(e),v=!0):z(e)?(b=!0,v=e.some(g=>ht(g)||Re(g)),u=()=>e.map(g=>{if(he(g))return g.value;if(ht(g))return f(g);if(W(g))return l?l(g,2):g()})):W(e)?t?u=l?()=>l(e,2):e:u=()=>{if(p){wt();try{p()}finally{St()}}const g=ct;ct=c;try{return l?l(e,3,[h]):e(h)}finally{ct=g}}:u=Fe,t&&i){const g=u,_=i===!0?1/0:i;u=()=>tt(g(),_)}const I=po(),T=()=>{c.stop(),I&&I.active&&Ni(I.effects,c)};if(r&&t){const g=t;t=(..._)=>{g(..._),T()}}let x=b?new Array(e.length).fill(kn):kn;const d=g=>{if(!(!(c.flags&1)||!c.dirty&&!g))if(t){const _=c.run();if(i||v||(b?_.some((P,B)=>Te(P,x[B])):Te(_,x))){p&&p();const P=ct;ct=c;try{const B=[_,x===kn?void 0:b&&x[0]===kn?[]:x,h];l?l(t,3,B):t(...B),x=_}finally{ct=P}}}else c.run()};return a&&a(d),c=new bn(u),c.scheduler=o?()=>o(d,!1):d,h=g=>Do(g,!1,c),p=c.onStop=()=>{const g=is.get(c);if(g){if(l)l(g,4);else for(const _ of g)_();is.delete(c)}},t?s?d(!0):x=c.run():o?o(d.bind(null,!0),!0):c.run(),T.pause=c.pause.bind(c),T.resume=c.resume.bind(c),T.stop=T,T}function tt(e,t=1/0,n){if(t<=0||!ne(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,he(e))tt(e.value,t,n);else if(z(e))for(let s=0;s<e.length;s++)tt(e[s],t,n);else if(Lt(e)||zt(e))e.forEach(s=>{tt(s,t,n)});else if(ws(e)){for(const s in e)tt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&tt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Bo=[];function Tc(e){Bo.push(e)}function xc(){Bo.pop()}function Ec(e,t){}const Cc={SETUP_FUNCTION:0,0:"SETUP_FUNCTION",RENDER_FUNCTION:1,1:"RENDER_FUNCTION",NATIVE_EVENT_HANDLER:5,5:"NATIVE_EVENT_HANDLER",COMPONENT_EVENT_HANDLER:6,6:"COMPONENT_EVENT_HANDLER",VNODE_HOOK:7,7:"VNODE_HOOK",DIRECTIVE_HOOK:8,8:"DIRECTIVE_HOOK",TRANSITION_HOOK:9,9:"TRANSITION_HOOK",APP_ERROR_HANDLER:10,10:"APP_ERROR_HANDLER",APP_WARN_HANDLER:11,11:"APP_WARN_HANDLER",FUNCTION_REF:12,12:"FUNCTION_REF",ASYNC_COMPONENT_LOADER:13,13:"ASYNC_COMPONENT_LOADER",SCHEDULER:14,14:"SCHEDULER",COMPONENT_UPDATE:15,15:"COMPONENT_UPDATE",APP_UNMOUNT_CLEANUP:16,16:"APP_UNMOUNT_CLEANUP"},Pc={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Qt(e,t,n,s){try{return s?e(...s):e()}catch(i){Rt(i,t,n)}}function He(e,t,n,s){if(W(e)){const i=Qt(e,t,n,s);return i&&Di(i)&&i.catch(r=>{Rt(r,t,n)}),i}if(z(e)){const i=[];for(let r=0;r<e.length;r++)i.push(He(e[r],t,n,s));return i}}function Rt(e,t,n,s=!0){const i=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||J;if(t){let a=t.parent;const l=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let u=0;u<c.length;u++)if(c[u](e,l,f)===!1)return}a=a.parent}if(r){wt(),Qt(r,null,10,[e,l,f]),St();return}}Mc(e,n,i,s,o)}function Mc(e,t,n,s=!0,i=!1){if(i)throw e;console.error(e)}const xe=[];let Ke=-1;const Ut=[];let ft=null,Vt=0;const Fo=Promise.resolve();let rs=null;function Rn(e){const t=rs||Fo;return e?t.then(this?e.bind(this):e):t}function Ac(e){let t=Ke+1,n=xe.length;for(;t<n;){const s=t+n>>>1,i=xe[s],r=Tn(i);r<e||r===e&&i.flags&2?t=s+1:n=s}return t}function ji(e){if(!(e.flags&1)){const t=Tn(e),n=xe[xe.length-1];!n||!(e.flags&2)&&t>=Tn(n)?xe.push(e):xe.splice(Ac(t),0,e),e.flags|=1,Vo()}}function Vo(){rs||(rs=Fo.then(Ho))}function _n(e){z(e)?Ut.push(...e):ft&&e.id===-1?ft.splice(Vt+1,0,e):e.flags&1||(Ut.push(e),e.flags|=1),Vo()}function vr(e,t,n=Ke+1){for(;n<xe.length;n++){const s=xe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;xe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function os(e){if(Ut.length){const t=[...new Set(Ut)].sort((n,s)=>Tn(n)-Tn(s));if(Ut.length=0,ft){ft.push(...t);return}for(ft=t,Vt=0;Vt<ft.length;Vt++){const n=ft[Vt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ft=null,Vt=0}}const Tn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ho(e){try{for(Ke=0;Ke<xe.length;Ke++){const t=xe[Ke];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Qt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ke<xe.length;Ke++){const t=xe[Ke];t&&(t.flags&=-2)}Ke=-1,xe.length=0,os(),rs=null,(xe.length||Ut.length)&&Ho()}}let Ht,$n=[];function ko(e,t){var n,s;Ht=e,Ht?(Ht.enabled=!0,$n.forEach(({event:i,args:r})=>Ht.emit(i,...r)),$n=[]):typeof window<"u"&&window.HTMLElement&&!((s=(n=window.navigator)==null?void 0:n.userAgent)!=null&&s.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(r=>{ko(r,t)}),setTimeout(()=>{Ht||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,$n=[])},3e3)):$n=[]}let pe=null,Is=null;function xn(e){const t=pe;return pe=e,Is=e&&e.type.__scopeId||null,t}function Oc(e){Is=e}function Ic(){Is=null}const Lc=e=>Gi;function Gi(e,t=pe,n){if(!t||e._n)return e;const s=(...i)=>{s._d&&bi(-1);const r=xn(t);let o;try{o=e(...i)}finally{xn(r),s._d&&bi(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function Rc(e,t){if(pe===null)return e;const n=Bn(pe),s=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[r,o,a,l=J]=t[i];r&&(W(r)&&(r={mounted:r,updated:r}),r.deep&&tt(o),s.push({dir:r,instance:n,value:o,oldValue:void 0,arg:a,modifiers:l}))}return e}function qe(e,t,n,s){const i=e.dirs,r=t&&t.dirs;for(let o=0;o<i.length;o++){const a=i[o];r&&(a.oldValue=r[o].value);let l=a.dir[s];l&&(wt(),He(l,n,8,[e.el,a,e,t]),St())}}const $o=Symbol("_vte"),zo=e=>e.__isTeleport,hn=e=>e&&(e.disabled||e.disabled===""),yr=e=>e&&(e.defer||e.defer===""),br=e=>typeof SVGElement<"u"&&e instanceof SVGElement,wr=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,di=(e,t)=>{const n=e&&e.to;return oe(n)?t?t(n):null:n},jo={name:"Teleport",__isTeleport:!0,process(e,t,n,s,i,r,o,a,l,f){const{mc:c,pc:u,pbc:p,o:{insert:h,querySelector:v,createText:b,createComment:I}}=f,T=hn(t.props);let{shapeFlag:x,children:d,dynamicChildren:g}=t;if(e==null){const _=t.el=b(""),P=t.anchor=b("");h(_,n,s),h(P,n,s);const B=(S,w)=>{x&16&&(i&&i.isCE&&(i.ce._teleportTarget=S),c(d,S,w,i,r,o,a,l))},A=()=>{const S=t.target=di(t.props,v),w=Go(S,t,b,h);S&&(o!=="svg"&&br(S)?o="svg":o!=="mathml"&&wr(S)&&(o="mathml"),T||(B(S,w),Xn(t,!1)))};T&&(B(n,P),Xn(t,!0)),yr(t.props)?ue(()=>{A(),t.el.__isMounted=!0},r):A()}else{if(yr(t.props)&&!e.el.__isMounted){ue(()=>{jo.process(e,t,n,s,i,r,o,a,l,f),delete e.el.__isMounted},r);return}t.el=e.el,t.targetStart=e.targetStart;const _=t.anchor=e.anchor,P=t.target=e.target,B=t.targetAnchor=e.targetAnchor,A=hn(e.props),S=A?n:P,w=A?_:B;if(o==="svg"||br(P)?o="svg":(o==="mathml"||wr(P))&&(o="mathml"),g?(p(e.dynamicChildren,g,S,i,r,o,a),er(e,t,!0)):l||u(e,t,S,w,i,r,o,a,!1),T)A?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):zn(t,n,_,f,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const M=t.target=di(t.props,v);M&&zn(t,M,null,f,0)}else A&&zn(t,P,B,f,1);Xn(t,T)}},remove(e,t,n,{um:s,o:{remove:i}},r){const{shapeFlag:o,children:a,anchor:l,targetStart:f,targetAnchor:c,target:u,props:p}=e;if(u&&(i(f),i(c)),r&&i(l),o&16){const h=r||!hn(p);for(let v=0;v<a.length;v++){const b=a[v];s(b,t,n,h,!!b.dynamicChildren)}}},move:zn,hydrate:Nc};function zn(e,t,n,{o:{insert:s},m:i},r=2){r===0&&s(e.targetAnchor,t,n);const{el:o,anchor:a,shapeFlag:l,children:f,props:c}=e,u=r===2;if(u&&s(o,t,n),(!u||hn(c))&&l&16)for(let p=0;p<f.length;p++)i(f[p],t,n,2);u&&s(a,t,n)}function Nc(e,t,n,s,i,r,{o:{nextSibling:o,parentNode:a,querySelector:l,insert:f,createText:c}},u){const p=t.target=di(t.props,l);if(p){const h=hn(t.props),v=p._lpa||p.firstChild;if(t.shapeFlag&16)if(h)t.anchor=u(o(e),t,a(e),n,s,i,r),t.targetStart=v,t.targetAnchor=v&&o(v);else{t.anchor=o(e);let b=v;for(;b;){if(b&&b.nodeType===8){if(b.data==="teleport start anchor")t.targetStart=b;else if(b.data==="teleport anchor"){t.targetAnchor=b,p._lpa=t.targetAnchor&&o(t.targetAnchor);break}}b=o(b)}t.targetAnchor||Go(p,t,c,f),u(v&&o(v),t,p,n,s,i,r)}Xn(t,h)}return t.anchor&&o(t.anchor)}const Dc=jo;function Xn(e,t){const n=e.ctx;if(n&&n.ut){let s,i;for(t?(s=e.el,i=e.anchor):(s=e.targetStart,i=e.targetAnchor);s&&s!==i;)s.nodeType===1&&s.setAttribute("data-v-owner",n.uid),s=s.nextSibling;n.ut()}}function Go(e,t,n,s){const i=t.targetStart=n(""),r=t.targetAnchor=n("");return i[$o]=r,e&&(s(i,e),s(r,e)),r}const ut=Symbol("_leaveCb"),jn=Symbol("_enterCb");function Ui(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Nt(()=>{e.isMounted=!0}),tn(()=>{e.isUnmounting=!0}),e}const Be=[Function,Array],Wi={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Be,onEnter:Be,onAfterEnter:Be,onEnterCancelled:Be,onBeforeLeave:Be,onLeave:Be,onAfterLeave:Be,onLeaveCancelled:Be,onBeforeAppear:Be,onAppear:Be,onAfterAppear:Be,onAppearCancelled:Be},Uo=e=>{const t=e.subTree;return t.component?Uo(t.component):t},Bc={name:"BaseTransition",props:Wi,setup(e,{slots:t}){const n=ke(),s=Ui();return()=>{const i=t.default&&Ls(t.default(),!0);if(!i||!i.length)return;const r=Wo(i),o=Q(e),{mode:a}=o;if(s.isLeaving)return Ks(r);const l=Sr(r);if(!l)return Ks(r);let f=qt(l,o,s,n,u=>f=u);l.type!==ce&&it(l,f);let c=n.subTree&&Sr(n.subTree);if(c&&c.type!==ce&&!ze(l,c)&&Uo(n).type!==ce){let u=qt(c,o,s,n);if(it(c,u),a==="out-in"&&l.type!==ce)return s.isLeaving=!0,u.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete u.afterLeave,c=void 0},Ks(r);a==="in-out"&&l.type!==ce?u.delayLeave=(p,h,v)=>{const b=qo(s,c);b[String(c.key)]=c,p[ut]=()=>{h(),p[ut]=void 0,delete f.delayedLeave,c=void 0},f.delayedLeave=()=>{v(),delete f.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return r}}};function Wo(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==ce){t=n;break}}return t}const Ko=Bc;function qo(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function qt(e,t,n,s,i){const{appear:r,mode:o,persisted:a=!1,onBeforeEnter:l,onEnter:f,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:p,onLeave:h,onAfterLeave:v,onLeaveCancelled:b,onBeforeAppear:I,onAppear:T,onAfterAppear:x,onAppearCancelled:d}=t,g=String(e.key),_=qo(n,e),P=(S,w)=>{S&&He(S,s,9,w)},B=(S,w)=>{const M=w[1];P(S,w),z(S)?S.every(C=>C.length<=1)&&M():S.length<=1&&M()},A={mode:o,persisted:a,beforeEnter(S){let w=l;if(!n.isMounted)if(r)w=I||l;else return;S[ut]&&S[ut](!0);const M=_[g];M&&ze(e,M)&&M.el[ut]&&M.el[ut](),P(w,[S])},enter(S){let w=f,M=c,C=u;if(!n.isMounted)if(r)w=T||f,M=x||c,C=d||u;else return;let L=!1;const U=S[jn]=Y=>{L||(L=!0,Y?P(C,[S]):P(M,[S]),A.delayedLeave&&A.delayedLeave(),S[jn]=void 0)};w?B(w,[S,U]):U()},leave(S,w){const M=String(e.key);if(S[jn]&&S[jn](!0),n.isUnmounting)return w();P(p,[S]);let C=!1;const L=S[ut]=U=>{C||(C=!0,w(),U?P(b,[S]):P(v,[S]),S[ut]=void 0,_[M]===e&&delete _[M])};_[M]=e,h?B(h,[S,L]):L()},clone(S){const w=qt(S,t,n,s,i);return i&&i(w),w}};return A}function Ks(e){if(Nn(e))return e=Ye(e),e.children=null,e}function Sr(e){if(!Nn(e))return zo(e.type)&&e.children?Wo(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&W(n.default))return n.default()}}function it(e,t){e.shapeFlag&6&&e.component?(e.transition=t,it(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ls(e,t=!1,n){let s=[],i=0;for(let r=0;r<e.length;r++){let o=e[r];const a=n==null?o.key:String(n)+String(o.key!=null?o.key:r);o.type===ge?(o.patchFlag&128&&i++,s=s.concat(Ls(o.children,t,a))):(t||o.type!==ce)&&s.push(a!=null?Ye(o,{key:a}):o)}if(i>1)for(let r=0;r<s.length;r++)s[r].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Ki(e,t){return W(e)?se({name:e.name},t,{setup:e}):e}function Fc(){const e=ke();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function qi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Vc(e){const t=ke(),n=Io(null);if(t){const i=t.refs===J?t.refs={}:t.refs;Object.defineProperty(i,e,{enumerable:!0,get:()=>n.value,set:r=>n.value=r})}return n}function En(e,t,n,s,i=!1){if(z(e)){e.forEach((v,b)=>En(v,t&&(z(t)?t[b]:t),n,s,i));return}if(gt(s)&&!i){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&En(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?Bn(s.component):s.el,o=i?null:r,{i:a,r:l}=e,f=t&&t.r,c=a.refs===J?a.refs={}:a.refs,u=a.setupState,p=Q(u),h=u===J?()=>!1:v=>ee(p,v);if(f!=null&&f!==l&&(oe(f)?(c[f]=null,h(f)&&(u[f]=null)):he(f)&&(f.value=null)),W(l))Qt(l,a,12,[o,c]);else{const v=oe(l),b=he(l);if(v||b){const I=()=>{if(e.f){const T=v?h(l)?u[l]:c[l]:l.value;i?z(T)&&Ni(T,r):z(T)?T.includes(r)||T.push(r):v?(c[l]=[r],h(l)&&(u[l]=c[l])):(l.value=[r],e.k&&(c[e.k]=l.value))}else v?(c[l]=o,h(l)&&(u[l]=o)):b&&(l.value=o,e.k&&(c[e.k]=o))};o?(I.id=-1,ue(I,n)):I()}}}let _r=!1;const Ft=()=>{_r||(console.error("Hydration completed but contains mismatches."),_r=!0)},Hc=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",kc=e=>e.namespaceURI.includes("MathML"),Gn=e=>{if(e.nodeType===1){if(Hc(e))return"svg";if(kc(e))return"mathml"}},kt=e=>e.nodeType===8;function $c(e){const{mt:t,p:n,o:{patchProp:s,createText:i,nextSibling:r,parentNode:o,remove:a,insert:l,createComment:f}}=e,c=(d,g)=>{if(!g.hasChildNodes()){n(null,d,g),os(),g._vnode=d;return}u(g.firstChild,d,null,null,null),os(),g._vnode=d},u=(d,g,_,P,B,A=!1)=>{A=A||!!g.dynamicChildren;const S=kt(d)&&d.data==="[",w=()=>b(d,g,_,P,B,S),{type:M,ref:C,shapeFlag:L,patchFlag:U}=g;let Y=d.nodeType;g.el=d,U===-2&&(A=!1,g.dynamicChildren=null);let $=null;switch(M){case mt:Y!==3?g.children===""?(l(g.el=i(""),o(d),d),$=d):$=w():(d.data!==g.children&&(Ft(),d.data=g.children),$=r(d));break;case ce:x(d)?($=r(d),T(g.el=d.content.firstChild,d,_)):Y!==8||S?$=w():$=r(d);break;case Mt:if(S&&(d=r(d),Y=d.nodeType),Y===1||Y===3){$=d;const X=!g.children.length;for(let j=0;j<g.staticCount;j++)X&&(g.children+=$.nodeType===1?$.outerHTML:$.data),j===g.staticCount-1&&(g.anchor=$),$=r($);return S?r($):$}else w();break;case ge:S?$=v(d,g,_,P,B,A):$=w();break;default:if(L&1)(Y!==1||g.type.toLowerCase()!==d.tagName.toLowerCase())&&!x(d)?$=w():$=p(d,g,_,P,B,A);else if(L&6){g.slotScopeIds=B;const X=o(d);if(S?$=I(d):kt(d)&&d.data==="teleport start"?$=I(d,d.data,"teleport end"):$=r(d),t(g,X,null,_,P,Gn(X),A),gt(g)&&!g.type.__asyncResolved){let j;S?(j=le(ge),j.anchor=$?$.previousSibling:X.lastChild):j=d.nodeType===3?nr(""):le("div"),j.el=d,g.component.subTree=j}}else L&64?Y!==8?$=w():$=g.type.hydrate(d,g,_,P,B,A,e,h):L&128&&($=g.type.hydrate(d,g,_,P,Gn(o(d)),B,A,e,u))}return C!=null&&En(C,null,P,g),$},p=(d,g,_,P,B,A)=>{A=A||!!g.dynamicChildren;const{type:S,props:w,patchFlag:M,shapeFlag:C,dirs:L,transition:U}=g,Y=S==="input"||S==="option";if(Y||M!==-1){L&&qe(g,null,_,"created");let $=!1;if(x(d)){$=bl(null,U)&&_&&_.vnode.props&&_.vnode.props.appear;const j=d.content.firstChild;$&&U.beforeEnter(j),T(j,d,_),g.el=d=j}if(C&16&&!(w&&(w.innerHTML||w.textContent))){let j=h(d.firstChild,g,d,_,P,B,A);for(;j;){Un(d,1)||Ft();const ae=j;j=j.nextSibling,a(ae)}}else if(C&8){let j=g.children;j[0]===`
`&&(d.tagName==="PRE"||d.tagName==="TEXTAREA")&&(j=j.slice(1)),d.textContent!==j&&(Un(d,0)||Ft(),d.textContent=g.children)}if(w){if(Y||!A||M&48){const j=d.tagName.includes("-");for(const ae in w)(Y&&(ae.endsWith("value")||ae==="indeterminate")||An(ae)&&!jt(ae)||ae[0]==="."||j)&&s(d,ae,null,w[ae],void 0,_)}else if(w.onClick)s(d,"onClick",null,w.onClick,void 0,_);else if(M&4&&ht(w.style))for(const j in w.style)w.style[j]}let X;(X=w&&w.onVnodeBeforeMount)&&Ee(X,_,g),L&&qe(g,null,_,"beforeMount"),((X=w&&w.onVnodeMounted)||L||$)&&Ml(()=>{X&&Ee(X,_,g),$&&U.enter(d),L&&qe(g,null,_,"mounted")},P)}return d.nextSibling},h=(d,g,_,P,B,A,S)=>{S=S||!!g.dynamicChildren;const w=g.children,M=w.length;for(let C=0;C<M;C++){const L=S?w[C]:w[C]=Ce(w[C]),U=L.type===mt;d?(U&&!S&&C+1<M&&Ce(w[C+1]).type===mt&&(l(i(d.data.slice(L.children.length)),_,r(d)),d.data=L.children),d=u(d,L,P,B,A,S)):U&&!L.children?l(L.el=i(""),_):(Un(_,1)||Ft(),n(null,L,_,null,P,B,Gn(_),A))}return d},v=(d,g,_,P,B,A)=>{const{slotScopeIds:S}=g;S&&(B=B?B.concat(S):S);const w=o(d),M=h(r(d),g,w,_,P,B,A);return M&&kt(M)&&M.data==="]"?r(g.anchor=M):(Ft(),l(g.anchor=f("]"),w,M),M)},b=(d,g,_,P,B,A)=>{if(Un(d.parentElement,1)||Ft(),g.el=null,A){const M=I(d);for(;;){const C=r(d);if(C&&C!==M)a(C);else break}}const S=r(d),w=o(d);return a(d),n(null,g,w,S,_,P,Gn(w),B),_&&(_.vnode.el=g.el,Vs(_,g.el)),S},I=(d,g="[",_="]")=>{let P=0;for(;d;)if(d=r(d),d&&kt(d)&&(d.data===g&&P++,d.data===_)){if(P===0)return r(d);P--}return d},T=(d,g,_)=>{const P=g.parentNode;P&&P.replaceChild(d,g);let B=_;for(;B;)B.vnode.el===g&&(B.vnode.el=B.subTree.el=d),B=B.parent},x=d=>d.nodeType===1&&d.tagName==="TEMPLATE";return[c,u]}const Tr="data-allow-mismatch",zc={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function Un(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(Tr);)e=e.parentElement;const n=e&&e.getAttribute(Tr);if(n==null)return!1;if(n==="")return!0;{const s=n.split(",");return t===0&&s.includes("children")?!0:n.split(",").includes(zc[t])}}const jc=_s().requestIdleCallback||(e=>setTimeout(e,1)),Gc=_s().cancelIdleCallback||(e=>clearTimeout(e)),Uc=(e=1e4)=>t=>{const n=jc(t,{timeout:e});return()=>Gc(n)};function Wc(e){const{top:t,left:n,bottom:s,right:i}=e.getBoundingClientRect(),{innerHeight:r,innerWidth:o}=window;return(t>0&&t<r||s>0&&s<r)&&(n>0&&n<o||i>0&&i<o)}const Kc=e=>(t,n)=>{const s=new IntersectionObserver(i=>{for(const r of i)if(r.isIntersecting){s.disconnect(),t();break}},e);return n(i=>{if(i instanceof Element){if(Wc(i))return t(),s.disconnect(),!1;s.observe(i)}}),()=>s.disconnect()},qc=e=>t=>{if(e){const n=matchMedia(e);if(n.matches)t();else return n.addEventListener("change",t,{once:!0}),()=>n.removeEventListener("change",t)}},Yc=(e=[])=>(t,n)=>{oe(e)&&(e=[e]);let s=!1;const i=o=>{s||(s=!0,r(),t(),o.target.dispatchEvent(new o.constructor(o.type,o)))},r=()=>{n(o=>{for(const a of e)o.removeEventListener(a,i)})};return n(o=>{for(const a of e)o.addEventListener(a,i,{once:!0})}),r};function Xc(e,t){if(kt(e)&&e.data==="["){let n=1,s=e.nextSibling;for(;s;){if(s.nodeType===1){if(t(s)===!1)break}else if(kt(s))if(s.data==="]"){if(--n===0)break}else s.data==="["&&n++;s=s.nextSibling}}else t(e)}const gt=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function Jc(e){W(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:s,delay:i=200,hydrate:r,timeout:o,suspensible:a=!0,onError:l}=e;let f=null,c,u=0;const p=()=>(u++,f=null,h()),h=()=>{let v;return f||(v=f=t().catch(b=>{if(b=b instanceof Error?b:new Error(String(b)),l)return new Promise((I,T)=>{l(b,()=>I(p()),()=>T(b),u+1)});throw b}).then(b=>v!==f&&f?f:(b&&(b.__esModule||b[Symbol.toStringTag]==="Module")&&(b=b.default),c=b,b)))};return Ki({name:"AsyncComponentWrapper",__asyncLoader:h,__asyncHydrate(v,b,I){const T=r?()=>{const x=r(I,d=>Xc(v,d));x&&(b.bum||(b.bum=[])).push(x)}:I;c?T():h().then(()=>!b.isUnmounted&&T())},get __asyncResolved(){return c},setup(){const v=de;if(qi(v),c)return()=>qs(c,v);const b=d=>{f=null,Rt(d,v,13,!s)};if(a&&v.suspense||Yt)return h().then(d=>()=>qs(d,v)).catch(d=>(b(d),()=>s?le(s,{error:d}):null));const I=fe(!1),T=fe(),x=fe(!!i);return i&&setTimeout(()=>{x.value=!1},i),o!=null&&setTimeout(()=>{if(!I.value&&!T.value){const d=new Error(`Async component timed out after ${o}ms.`);b(d),T.value=d}},o),h().then(()=>{I.value=!0,v.parent&&Nn(v.parent.vnode)&&v.parent.update()}).catch(d=>{b(d),T.value=d}),()=>{if(I.value&&c)return qs(c,v);if(T.value&&s)return le(s,{error:T.value});if(n&&!x.value)return le(n)}}})}function qs(e,t){const{ref:n,props:s,children:i,ce:r}=t.vnode,o=le(e,s,i);return o.ref=n,o.ce=r,delete t.vnode.ce,o}const Nn=e=>e.type.__isKeepAlive,Zc={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=ke(),s=n.ctx;if(!s.renderer)return()=>{const x=t.default&&t.default();return x&&x.length===1?x[0]:x};const i=new Map,r=new Set;let o=null;const a=n.suspense,{renderer:{p:l,m:f,um:c,o:{createElement:u}}}=s,p=u("div");s.activate=(x,d,g,_,P)=>{const B=x.component;f(x,d,g,0,a),l(B.vnode,x,d,g,B,a,_,x.slotScopeIds,P),ue(()=>{B.isDeactivated=!1,B.a&&Gt(B.a);const A=x.props&&x.props.onVnodeMounted;A&&Ee(A,B.parent,x)},a)},s.deactivate=x=>{const d=x.component;as(d.m),as(d.a),f(x,p,null,1,a),ue(()=>{d.da&&Gt(d.da);const g=x.props&&x.props.onVnodeUnmounted;g&&Ee(g,d.parent,x),d.isDeactivated=!0},a)};function h(x){Ys(x),c(x,n,a,!0)}function v(x){i.forEach((d,g)=>{const _=xi(d.type);_&&!x(_)&&b(g)})}function b(x){const d=i.get(x);d&&(!o||!ze(d,o))?h(d):o&&Ys(o),i.delete(x),r.delete(x)}Pt(()=>[e.include,e.exclude],([x,d])=>{x&&v(g=>cn(x,g)),d&&v(g=>!cn(d,g))},{flush:"post",deep:!0});let I=null;const T=()=>{I!=null&&(cs(n.subTree.type)?ue(()=>{i.set(I,Wn(n.subTree))},n.subTree.suspense):i.set(I,Wn(n.subTree)))};return Nt(T),en(T),tn(()=>{i.forEach(x=>{const{subTree:d,suspense:g}=n,_=Wn(d);if(x.type===_.type&&x.key===_.key){Ys(_);const P=_.component.da;P&&ue(P,g);return}h(x)})}),()=>{if(I=null,!t.default)return o=null;const x=t.default(),d=x[0];if(x.length>1)return o=null,x;if(!rt(d)||!(d.shapeFlag&4)&&!(d.shapeFlag&128))return o=null,d;let g=Wn(d);if(g.type===ce)return o=null,g;const _=g.type,P=xi(gt(g)?g.type.__asyncResolved||{}:_),{include:B,exclude:A,max:S}=e;if(B&&(!P||!cn(B,P))||A&&P&&cn(A,P))return g.shapeFlag&=-257,o=g,d;const w=g.key==null?_:g.key,M=i.get(w);return g.el&&(g=Ye(g),d.shapeFlag&128&&(d.ssContent=g)),I=w,M?(g.el=M.el,g.component=M.component,g.transition&&it(g,g.transition),g.shapeFlag|=512,r.delete(w),r.add(w)):(r.add(w),S&&r.size>parseInt(S,10)&&b(r.values().next().value)),g.shapeFlag|=256,o=g,cs(d.type)?d:g}}},Qc=Zc;function cn(e,t){return z(e)?e.some(n=>cn(n,t)):oe(e)?e.split(",").includes(t):xa(e)?(e.lastIndex=0,e.test(t)):!1}function Yo(e,t){Jo(e,"a",t)}function Xo(e,t){Jo(e,"da",t)}function Jo(e,t,n=de){const s=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Rs(t,s,n),n){let i=n.parent;for(;i&&i.parent;)Nn(i.parent.vnode)&&ef(s,t,n,i),i=i.parent}}function ef(e,t,n,s){const i=Rs(t,e,s,!0);Ds(()=>{Ni(s[t],i)},n)}function Ys(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Wn(e){return e.shapeFlag&128?e.ssContent:e}function Rs(e,t,n=de,s=!1){if(n){const i=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...o)=>{wt();const a=Ot(n),l=He(t,n,e,o);return a(),St(),l});return s?i.unshift(r):i.push(r),r}}const ot=e=>(t,n=de)=>{(!Yt||e==="sp")&&Rs(e,(...s)=>t(...s),n)},Zo=ot("bm"),Nt=ot("m"),Ns=ot("bu"),en=ot("u"),tn=ot("bum"),Ds=ot("um"),Qo=ot("sp"),el=ot("rtg"),tl=ot("rtc");function nl(e,t=de){Rs("ec",e,t)}const Yi="components",tf="directives";function nf(e,t){return Xi(Yi,e,!0,t)||e}const sl=Symbol.for("v-ndc");function sf(e){return oe(e)?Xi(Yi,e,!1)||e:e||sl}function rf(e){return Xi(tf,e)}function Xi(e,t,n=!0,s=!1){const i=pe||de;if(i){const r=i.type;if(e===Yi){const a=xi(r,!1);if(a&&(a===t||a===me(t)||a===On(me(t))))return r}const o=xr(i[e]||r[e],t)||xr(i.appContext[e],t);return!o&&s?r:o}}function xr(e,t){return e&&(e[t]||e[me(t)]||e[On(me(t))])}function of(e,t,n,s){let i;const r=n&&n[s],o=z(e);if(o||oe(e)){const a=o&&ht(e);let l=!1;a&&(l=!Re(e),e=Es(e)),i=new Array(e.length);for(let f=0,c=e.length;f<c;f++)i[f]=t(l?be(e[f]):e[f],f,void 0,r&&r[f])}else if(typeof e=="number"){i=new Array(e);for(let a=0;a<e;a++)i[a]=t(a+1,a,void 0,r&&r[a])}else if(ne(e))if(e[Symbol.iterator])i=Array.from(e,(a,l)=>t(a,l,void 0,r&&r[l]));else{const a=Object.keys(e);i=new Array(a.length);for(let l=0,f=a.length;l<f;l++){const c=a[l];i[l]=t(e[c],c,l,r&&r[l])}}else i=[];return n&&(n[s]=i),i}function lf(e,t){for(let n=0;n<t.length;n++){const s=t[n];if(z(s))for(let i=0;i<s.length;i++)e[s[i].name]=s[i].fn;else s&&(e[s.name]=s.key?(...i)=>{const r=s.fn(...i);return r&&(r.key=s.key),r}:s.fn)}return e}function af(e,t,n={},s,i){if(pe.ce||pe.parent&&gt(pe.parent)&&pe.parent.ce)return t!=="default"&&(n.name=t),Mn(),fs(ge,null,[le("slot",n,s&&s())],64);let r=e[t];r&&r._c&&(r._d=!1),Mn();const o=r&&Ji(r(n)),a=n.key||o&&o.key,l=fs(ge,{key:(a&&!Ge(a)?a:`_${t}`)+(!o&&s?"_fb":"")},o||(s?s():[]),o&&e._===1?64:-2);return!i&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),r&&r._c&&(r._d=!0),l}function Ji(e){return e.some(t=>rt(t)?!(t.type===ce||t.type===ge&&!Ji(t.children)):!0)?e:null}function cf(e,t){const n={};for(const s in e)n[t&&/[A-Z]/.test(s)?`on:${s}`:un(s)]=e[s];return n}const pi=e=>e?Dl(e)?Bn(e):pi(e.parent):null,gn=se(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>pi(e.parent),$root:e=>pi(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Zi(e),$forceUpdate:e=>e.f||(e.f=()=>{ji(e.update)}),$nextTick:e=>e.n||(e.n=Rn.bind(e.proxy)),$watch:e=>$f.bind(e)}),Xs=(e,t)=>e!==J&&!e.__isScriptSetup&&ee(e,t),hi={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:i,props:r,accessCache:o,type:a,appContext:l}=e;let f;if(t[0]!=="$"){const h=o[t];if(h!==void 0)switch(h){case 1:return s[t];case 2:return i[t];case 4:return n[t];case 3:return r[t]}else{if(Xs(s,t))return o[t]=1,s[t];if(i!==J&&ee(i,t))return o[t]=2,i[t];if((f=e.propsOptions[0])&&ee(f,t))return o[t]=3,r[t];if(n!==J&&ee(n,t))return o[t]=4,n[t];gi&&(o[t]=0)}}const c=gn[t];let u,p;if(c)return t==="$attrs"&&ye(e.attrs,"get",""),c(e);if((u=a.__cssModules)&&(u=u[t]))return u;if(n!==J&&ee(n,t))return o[t]=4,n[t];if(p=l.config.globalProperties,ee(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:i,ctx:r}=e;return Xs(i,t)?(i[t]=n,!0):s!==J&&ee(s,t)?(s[t]=n,!0):ee(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:i,propsOptions:r}},o){let a;return!!n[o]||e!==J&&ee(e,o)||Xs(t,o)||(a=r[0])&&ee(a,o)||ee(s,o)||ee(gn,o)||ee(i.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ee(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}},ff=se({},hi,{get(e,t){if(t!==Symbol.unscopables)return hi.get(e,t,e)},has(e,t){return t[0]!=="_"&&!Aa(t)}});function uf(){return null}function df(){return null}function pf(e){}function hf(e){}function gf(){return null}function mf(){}function vf(e,t){return null}function yf(){return il().slots}function bf(){return il().attrs}function il(){const e=ke();return e.setupContext||(e.setupContext=Vl(e))}function Cn(e){return z(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function wf(e,t){const n=Cn(e);for(const s in t){if(s.startsWith("__skip"))continue;let i=n[s];i?z(i)||W(i)?i=n[s]={type:i,default:t[s]}:i.default=t[s]:i===null&&(i=n[s]={default:t[s]}),i&&t[`__skip_${s}`]&&(i.skipFactory=!0)}return n}function Sf(e,t){return!e||!t?e||t:z(e)&&z(t)?e.concat(t):se({},Cn(e),Cn(t))}function _f(e,t){const n={};for(const s in e)t.includes(s)||Object.defineProperty(n,s,{enumerable:!0,get:()=>e[s]});return n}function Tf(e){const t=ke();let n=e();return Si(),Di(n)&&(n=n.catch(s=>{throw Ot(t),s})),[n,()=>Ot(t)]}let gi=!0;function xf(e){const t=Zi(e),n=e.proxy,s=e.ctx;gi=!1,t.beforeCreate&&Er(t.beforeCreate,e,"bc");const{data:i,computed:r,methods:o,watch:a,provide:l,inject:f,created:c,beforeMount:u,mounted:p,beforeUpdate:h,updated:v,activated:b,deactivated:I,beforeDestroy:T,beforeUnmount:x,destroyed:d,unmounted:g,render:_,renderTracked:P,renderTriggered:B,errorCaptured:A,serverPrefetch:S,expose:w,inheritAttrs:M,components:C,directives:L,filters:U}=t;if(f&&Ef(f,s,null),o)for(const X in o){const j=o[X];W(j)&&(s[X]=j.bind(n))}if(i){const X=i.call(n,n);ne(X)&&(e.data=Ps(X))}if(gi=!0,r)for(const X in r){const j=r[X],ae=W(j)?j.bind(n,n):W(j.get)?j.get.bind(n,n):Fe,_t=!W(j)&&W(j.set)?j.set.bind(n):Fe,Xe=ir({get:ae,set:_t});Object.defineProperty(s,X,{enumerable:!0,configurable:!0,get:()=>Xe.value,set:De=>Xe.value=De})}if(a)for(const X in a)rl(a[X],s,n,X);if(l){const X=W(l)?l.call(n):l;Reflect.ownKeys(X).forEach(j=>{Bs(j,X[j])})}c&&Er(c,e,"c");function $(X,j){z(j)?j.forEach(ae=>X(ae.bind(n))):j&&X(j.bind(n))}if($(Zo,u),$(Nt,p),$(Ns,h),$(en,v),$(Yo,b),$(Xo,I),$(nl,A),$(tl,P),$(el,B),$(tn,x),$(Ds,g),$(Qo,S),z(w))if(w.length){const X=e.exposed||(e.exposed={});w.forEach(j=>{Object.defineProperty(X,j,{get:()=>n[j],set:ae=>n[j]=ae})})}else e.exposed||(e.exposed={});_&&e.render===Fe&&(e.render=_),M!=null&&(e.inheritAttrs=M),C&&(e.components=C),L&&(e.directives=L),S&&qi(e)}function Ef(e,t,n=Fe){z(e)&&(e=mi(e));for(const s in e){const i=e[s];let r;ne(i)?"default"in i?r=mn(i.from||s,i.default,!0):r=mn(i.from||s):r=mn(i),he(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:o=>r.value=o}):t[s]=r}}function Er(e,t,n){He(z(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function rl(e,t,n,s){let i=s.includes(".")?xl(n,s):()=>n[s];if(oe(e)){const r=t[e];W(r)&&Pt(i,r)}else if(W(e))Pt(i,e.bind(n));else if(ne(e))if(z(e))e.forEach(r=>rl(r,t,n,s));else{const r=W(e.handler)?e.handler.bind(n):t[e.handler];W(r)&&Pt(i,r,e)}}function Zi(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:i,optionsCache:r,config:{optionMergeStrategies:o}}=e.appContext,a=r.get(t);let l;return a?l=a:!i.length&&!n&&!s?l=t:(l={},i.length&&i.forEach(f=>ls(l,f,o,!0)),ls(l,t,o)),ne(t)&&r.set(t,l),l}function ls(e,t,n,s=!1){const{mixins:i,extends:r}=t;r&&ls(e,r,n,!0),i&&i.forEach(o=>ls(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const a=Cf[o]||n&&n[o];e[o]=a?a(e[o],t[o]):t[o]}return e}const Cf={data:Cr,props:Pr,emits:Pr,methods:fn,computed:fn,beforeCreate:Se,created:Se,beforeMount:Se,mounted:Se,beforeUpdate:Se,updated:Se,beforeDestroy:Se,beforeUnmount:Se,destroyed:Se,unmounted:Se,activated:Se,deactivated:Se,errorCaptured:Se,serverPrefetch:Se,components:fn,directives:fn,watch:Mf,provide:Cr,inject:Pf};function Cr(e,t){return t?e?function(){return se(W(e)?e.call(this,this):e,W(t)?t.call(this,this):t)}:t:e}function Pf(e,t){return fn(mi(e),mi(t))}function mi(e){if(z(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Se(e,t){return e?[...new Set([].concat(e,t))]:t}function fn(e,t){return e?se(Object.create(null),e,t):t}function Pr(e,t){return e?z(e)&&z(t)?[...new Set([...e,...t])]:se(Object.create(null),Cn(e),Cn(t??{})):t}function Mf(e,t){if(!e)return t;if(!t)return e;const n=se(Object.create(null),e);for(const s in t)n[s]=Se(e[s],t[s]);return n}function ol(){return{app:null,config:{isNativeTag:_a,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Af=0;function Of(e,t){return function(s,i=null){W(s)||(s=se({},s)),i!=null&&!ne(i)&&(i=null);const r=ol(),o=new WeakSet,a=[];let l=!1;const f=r.app={_uid:Af++,_component:s,_props:i,_container:null,_context:r,_instance:null,version:kl,get config(){return r.config},set config(c){},use(c,...u){return o.has(c)||(c&&W(c.install)?(o.add(c),c.install(f,...u)):W(c)&&(o.add(c),c(f,...u))),f},mixin(c){return r.mixins.includes(c)||r.mixins.push(c),f},component(c,u){return u?(r.components[c]=u,f):r.components[c]},directive(c,u){return u?(r.directives[c]=u,f):r.directives[c]},mount(c,u,p){if(!l){const h=f._ceVNode||le(s,i);return h.appContext=r,p===!0?p="svg":p===!1&&(p=void 0),u&&t?t(h,c):e(h,c,p),l=!0,f._container=c,c.__vue_app__=f,Bn(h.component)}},onUnmount(c){a.push(c)},unmount(){l&&(He(a,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(c,u){return r.provides[c]=u,f},runWithContext(c){const u=Ct;Ct=f;try{return c()}finally{Ct=u}}};return f}}let Ct=null;function Bs(e,t){if(de){let n=de.provides;const s=de.parent&&de.parent.provides;s===n&&(n=de.provides=Object.create(s)),n[e]=t}}function mn(e,t,n=!1){const s=de||pe;if(s||Ct){const i=Ct?Ct._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&W(t)?t.call(s&&s.proxy):t}}function If(){return!!(de||pe||Ct)}const ll={},al=()=>Object.create(ll),cl=e=>Object.getPrototypeOf(e)===ll;function Lf(e,t,n,s=!1){const i={},r=al();e.propsDefaults=Object.create(null),fl(e,t,i,r);for(const o in e.propsOptions[0])o in i||(i[o]=void 0);n?e.props=s?i:Ao(i):e.type.props?e.props=i:e.props=r,e.attrs=r}function Rf(e,t,n,s){const{props:i,attrs:r,vnode:{patchFlag:o}}=e,a=Q(i),[l]=e.propsOptions;let f=!1;if((s||o>0)&&!(o&16)){if(o&8){const c=e.vnode.dynamicProps;for(let u=0;u<c.length;u++){let p=c[u];if(Fs(e.emitsOptions,p))continue;const h=t[p];if(l)if(ee(r,p))h!==r[p]&&(r[p]=h,f=!0);else{const v=me(p);i[v]=vi(l,a,v,h,e,!1)}else h!==r[p]&&(r[p]=h,f=!0)}}}else{fl(e,t,i,r)&&(f=!0);let c;for(const u in a)(!t||!ee(t,u)&&((c=Pe(u))===u||!ee(t,c)))&&(l?n&&(n[u]!==void 0||n[c]!==void 0)&&(i[u]=vi(l,a,u,void 0,e,!0)):delete i[u]);if(r!==a)for(const u in r)(!t||!ee(t,u))&&(delete r[u],f=!0)}f&&et(e.attrs,"set","")}function fl(e,t,n,s){const[i,r]=e.propsOptions;let o=!1,a;if(t)for(let l in t){if(jt(l))continue;const f=t[l];let c;i&&ee(i,c=me(l))?!r||!r.includes(c)?n[c]=f:(a||(a={}))[c]=f:Fs(e.emitsOptions,l)||(!(l in s)||f!==s[l])&&(s[l]=f,o=!0)}if(r){const l=Q(n),f=a||J;for(let c=0;c<r.length;c++){const u=r[c];n[u]=vi(i,l,u,f[u],e,!ee(f,u))}}return o}function vi(e,t,n,s,i,r){const o=e[n];if(o!=null){const a=ee(o,"default");if(a&&s===void 0){const l=o.default;if(o.type!==Function&&!o.skipFactory&&W(l)){const{propsDefaults:f}=i;if(n in f)s=f[n];else{const c=Ot(i);s=f[n]=l.call(null,t),c()}}else s=l;i.ce&&i.ce._setProp(n,s)}o[0]&&(r&&!a?s=!1:o[1]&&(s===""||s===Pe(n))&&(s=!0))}return s}const Nf=new WeakMap;function ul(e,t,n=!1){const s=n?Nf:t.propsCache,i=s.get(e);if(i)return i;const r=e.props,o={},a=[];let l=!1;if(!W(e)){const c=u=>{l=!0;const[p,h]=ul(u,t,!0);se(o,p),h&&a.push(...h)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!l)return ne(e)&&s.set(e,$t),$t;if(z(r))for(let c=0;c<r.length;c++){const u=me(r[c]);Mr(u)&&(o[u]=J)}else if(r)for(const c in r){const u=me(c);if(Mr(u)){const p=r[c],h=o[u]=z(p)||W(p)?{type:p}:se({},p),v=h.type;let b=!1,I=!0;if(z(v))for(let T=0;T<v.length;++T){const x=v[T],d=W(x)&&x.name;if(d==="Boolean"){b=!0;break}else d==="String"&&(I=!1)}else b=W(v)&&v.name==="Boolean";h[0]=b,h[1]=I,(b||ee(h,"default"))&&a.push(u)}}const f=[o,a];return ne(e)&&s.set(e,f),f}function Mr(e){return e[0]!=="$"&&!jt(e)}const dl=e=>e[0]==="_"||e==="$stable",Qi=e=>z(e)?e.map(Ce):[Ce(e)],Df=(e,t,n)=>{if(t._n)return t;const s=Gi((...i)=>Qi(t(...i)),n);return s._c=!1,s},pl=(e,t,n)=>{const s=e._ctx;for(const i in e){if(dl(i))continue;const r=e[i];if(W(r))t[i]=Df(i,r,s);else if(r!=null){const o=Qi(r);t[i]=()=>o}}},hl=(e,t)=>{const n=Qi(t);e.slots.default=()=>n},gl=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},Bf=(e,t,n)=>{const s=e.slots=al();if(e.vnode.shapeFlag&32){const i=t._;i?(gl(s,t,n),n&&lo(s,"_",i,!0)):pl(t,s)}else t&&hl(e,t)},Ff=(e,t,n)=>{const{vnode:s,slots:i}=e;let r=!0,o=J;if(s.shapeFlag&32){const a=t._;a?n&&a===1?r=!1:gl(i,t,n):(r=!t.$stable,pl(t,i)),o=t}else t&&(hl(e,t),o={default:1});if(r)for(const a in i)!dl(a)&&o[a]==null&&delete i[a]},ue=Ml;function ml(e){return yl(e)}function vl(e){return yl(e,$c)}function yl(e,t){const n=_s();n.__VUE__=!0;const{insert:s,remove:i,patchProp:r,createElement:o,createText:a,createComment:l,setText:f,setElementText:c,parentNode:u,nextSibling:p,setScopeId:h=Fe,insertStaticContent:v}=e,b=(m,y,E,N=null,O=null,R=null,H=void 0,V=null,F=!!y.dynamicChildren)=>{if(m===y)return;m&&!ze(m,y)&&(N=Fn(m),De(m,O,R,!0),m=null),y.patchFlag===-2&&(F=!1,y.dynamicChildren=null);const{type:D,ref:K,shapeFlag:k}=y;switch(D){case mt:I(m,y,E,N);break;case ce:T(m,y,E,N);break;case Mt:m==null&&x(y,E,N,H);break;case ge:C(m,y,E,N,O,R,H,V,F);break;default:k&1?_(m,y,E,N,O,R,H,V,F):k&6?L(m,y,E,N,O,R,H,V,F):(k&64||k&128)&&D.process(m,y,E,N,O,R,H,V,F,Dt)}K!=null&&O&&En(K,m&&m.ref,R,y||m,!y)},I=(m,y,E,N)=>{if(m==null)s(y.el=a(y.children),E,N);else{const O=y.el=m.el;y.children!==m.children&&f(O,y.children)}},T=(m,y,E,N)=>{m==null?s(y.el=l(y.children||""),E,N):y.el=m.el},x=(m,y,E,N)=>{[m.el,m.anchor]=v(m.children,y,E,N,m.el,m.anchor)},d=({el:m,anchor:y},E,N)=>{let O;for(;m&&m!==y;)O=p(m),s(m,E,N),m=O;s(y,E,N)},g=({el:m,anchor:y})=>{let E;for(;m&&m!==y;)E=p(m),i(m),m=E;i(y)},_=(m,y,E,N,O,R,H,V,F)=>{y.type==="svg"?H="svg":y.type==="math"&&(H="mathml"),m==null?P(y,E,N,O,R,H,V,F):S(m,y,O,R,H,V,F)},P=(m,y,E,N,O,R,H,V)=>{let F,D;const{props:K,shapeFlag:k,transition:G,dirs:q}=m;if(F=m.el=o(m.type,R,K&&K.is,K),k&8?c(F,m.children):k&16&&A(m.children,F,null,N,O,Js(m,R),H,V),q&&qe(m,null,N,"created"),B(F,m,m.scopeId,H,N),K){for(const ie in K)ie!=="value"&&!jt(ie)&&r(F,ie,null,K[ie],R,N);"value"in K&&r(F,"value",null,K.value,R),(D=K.onVnodeBeforeMount)&&Ee(D,N,m)}q&&qe(m,null,N,"beforeMount");const Z=bl(O,G);Z&&G.beforeEnter(F),s(F,y,E),((D=K&&K.onVnodeMounted)||Z||q)&&ue(()=>{D&&Ee(D,N,m),Z&&G.enter(F),q&&qe(m,null,N,"mounted")},O)},B=(m,y,E,N,O)=>{if(E&&h(m,E),N)for(let R=0;R<N.length;R++)h(m,N[R]);if(O){let R=O.subTree;if(y===R||cs(R.type)&&(R.ssContent===y||R.ssFallback===y)){const H=O.vnode;B(m,H,H.scopeId,H.slotScopeIds,O.parent)}}},A=(m,y,E,N,O,R,H,V,F=0)=>{for(let D=F;D<m.length;D++){const K=m[D]=V?dt(m[D]):Ce(m[D]);b(null,K,y,E,N,O,R,H,V)}},S=(m,y,E,N,O,R,H)=>{const V=y.el=m.el;let{patchFlag:F,dynamicChildren:D,dirs:K}=y;F|=m.patchFlag&16;const k=m.props||J,G=y.props||J;let q;if(E&&Tt(E,!1),(q=G.onVnodeBeforeUpdate)&&Ee(q,E,y,m),K&&qe(y,m,E,"beforeUpdate"),E&&Tt(E,!0),(k.innerHTML&&G.innerHTML==null||k.textContent&&G.textContent==null)&&c(V,""),D?w(m.dynamicChildren,D,V,E,N,Js(y,O),R):H||j(m,y,V,null,E,N,Js(y,O),R,!1),F>0){if(F&16)M(V,k,G,E,O);else if(F&2&&k.class!==G.class&&r(V,"class",null,G.class,O),F&4&&r(V,"style",k.style,G.style,O),F&8){const Z=y.dynamicProps;for(let ie=0;ie<Z.length;ie++){const te=Z[ie],Me=k[te],ve=G[te];(ve!==Me||te==="value")&&r(V,te,Me,ve,O,E)}}F&1&&m.children!==y.children&&c(V,y.children)}else!H&&D==null&&M(V,k,G,E,O);((q=G.onVnodeUpdated)||K)&&ue(()=>{q&&Ee(q,E,y,m),K&&qe(y,m,E,"updated")},N)},w=(m,y,E,N,O,R,H)=>{for(let V=0;V<y.length;V++){const F=m[V],D=y[V],K=F.el&&(F.type===ge||!ze(F,D)||F.shapeFlag&70)?u(F.el):E;b(F,D,K,null,N,O,R,H,!0)}},M=(m,y,E,N,O)=>{if(y!==E){if(y!==J)for(const R in y)!jt(R)&&!(R in E)&&r(m,R,y[R],null,O,N);for(const R in E){if(jt(R))continue;const H=E[R],V=y[R];H!==V&&R!=="value"&&r(m,R,V,H,O,N)}"value"in E&&r(m,"value",y.value,E.value,O)}},C=(m,y,E,N,O,R,H,V,F)=>{const D=y.el=m?m.el:a(""),K=y.anchor=m?m.anchor:a("");let{patchFlag:k,dynamicChildren:G,slotScopeIds:q}=y;q&&(V=V?V.concat(q):q),m==null?(s(D,E,N),s(K,E,N),A(y.children||[],E,K,O,R,H,V,F)):k>0&&k&64&&G&&m.dynamicChildren?(w(m.dynamicChildren,G,E,O,R,H,V),(y.key!=null||O&&y===O.subTree)&&er(m,y,!0)):j(m,y,E,K,O,R,H,V,F)},L=(m,y,E,N,O,R,H,V,F)=>{y.slotScopeIds=V,m==null?y.shapeFlag&512?O.ctx.activate(y,E,N,H,F):U(y,E,N,O,R,H,F):Y(m,y,F)},U=(m,y,E,N,O,R,H)=>{const V=m.component=Nl(m,N,O);if(Nn(m)&&(V.ctx.renderer=Dt),Bl(V,!1,H),V.asyncDep){if(O&&O.registerDep(V,$,H),!m.el){const F=V.subTree=le(ce);T(null,F,y,E)}}else $(V,m,y,E,O,R,H)},Y=(m,y,E)=>{const N=y.component=m.component;if(Kf(m,y,E))if(N.asyncDep&&!N.asyncResolved){X(N,y,E);return}else N.next=y,N.update();else y.el=m.el,N.vnode=y},$=(m,y,E,N,O,R,H)=>{const V=()=>{if(m.isMounted){let{next:k,bu:G,u:q,parent:Z,vnode:ie}=m;{const Ae=wl(m);if(Ae){k&&(k.el=ie.el,X(m,k,H)),Ae.asyncDep.then(()=>{m.isUnmounted||V()});return}}let te=k,Me;Tt(m,!1),k?(k.el=ie.el,X(m,k,H)):k=ie,G&&Gt(G),(Me=k.props&&k.props.onVnodeBeforeUpdate)&&Ee(Me,Z,k,ie),Tt(m,!0);const ve=Jn(m),$e=m.subTree;m.subTree=ve,b($e,ve,u($e.el),Fn($e),m,O,R),k.el=ve.el,te===null&&Vs(m,ve.el),q&&ue(q,O),(Me=k.props&&k.props.onVnodeUpdated)&&ue(()=>Ee(Me,Z,k,ie),O)}else{let k;const{el:G,props:q}=y,{bm:Z,m:ie,parent:te,root:Me,type:ve}=m,$e=gt(y);if(Tt(m,!1),Z&&Gt(Z),!$e&&(k=q&&q.onVnodeBeforeMount)&&Ee(k,te,y),Tt(m,!0),G&&zs){const Ae=()=>{m.subTree=Jn(m),zs(G,m.subTree,m,O,null)};$e&&ve.__asyncHydrate?ve.__asyncHydrate(G,m,Ae):Ae()}else{Me.ce&&Me.ce._injectChildStyle(ve);const Ae=m.subTree=Jn(m);b(null,Ae,E,N,m,O,R),y.el=Ae.el}if(ie&&ue(ie,O),!$e&&(k=q&&q.onVnodeMounted)){const Ae=y;ue(()=>Ee(k,te,Ae),O)}(y.shapeFlag&256||te&&gt(te.vnode)&&te.vnode.shapeFlag&256)&&m.a&&ue(m.a,O),m.isMounted=!0,y=E=N=null}};m.scope.on();const F=m.effect=new bn(V);m.scope.off();const D=m.update=F.run.bind(F),K=m.job=F.runIfDirty.bind(F);K.i=m,K.id=m.uid,F.scheduler=()=>ji(K),Tt(m,!0),D()},X=(m,y,E)=>{y.component=m;const N=m.vnode.props;m.vnode=y,m.next=null,Rf(m,y.props,N,E),Ff(m,y.children,E),wt(),vr(m),St()},j=(m,y,E,N,O,R,H,V,F=!1)=>{const D=m&&m.children,K=m?m.shapeFlag:0,k=y.children,{patchFlag:G,shapeFlag:q}=y;if(G>0){if(G&128){_t(D,k,E,N,O,R,H,V,F);return}else if(G&256){ae(D,k,E,N,O,R,H,V,F);return}}q&8?(K&16&&sn(D,O,R),k!==D&&c(E,k)):K&16?q&16?_t(D,k,E,N,O,R,H,V,F):sn(D,O,R,!0):(K&8&&c(E,""),q&16&&A(k,E,N,O,R,H,V,F))},ae=(m,y,E,N,O,R,H,V,F)=>{m=m||$t,y=y||$t;const D=m.length,K=y.length,k=Math.min(D,K);let G;for(G=0;G<k;G++){const q=y[G]=F?dt(y[G]):Ce(y[G]);b(m[G],q,E,null,O,R,H,V,F)}D>K?sn(m,O,R,!0,!1,k):A(y,E,N,O,R,H,V,F,k)},_t=(m,y,E,N,O,R,H,V,F)=>{let D=0;const K=y.length;let k=m.length-1,G=K-1;for(;D<=k&&D<=G;){const q=m[D],Z=y[D]=F?dt(y[D]):Ce(y[D]);if(ze(q,Z))b(q,Z,E,null,O,R,H,V,F);else break;D++}for(;D<=k&&D<=G;){const q=m[k],Z=y[G]=F?dt(y[G]):Ce(y[G]);if(ze(q,Z))b(q,Z,E,null,O,R,H,V,F);else break;k--,G--}if(D>k){if(D<=G){const q=G+1,Z=q<K?y[q].el:N;for(;D<=G;)b(null,y[D]=F?dt(y[D]):Ce(y[D]),E,Z,O,R,H,V,F),D++}}else if(D>G)for(;D<=k;)De(m[D],O,R,!0),D++;else{const q=D,Z=D,ie=new Map;for(D=Z;D<=G;D++){const Oe=y[D]=F?dt(y[D]):Ce(y[D]);Oe.key!=null&&ie.set(Oe.key,D)}let te,Me=0;const ve=G-Z+1;let $e=!1,Ae=0;const rn=new Array(ve);for(D=0;D<ve;D++)rn[D]=0;for(D=q;D<=k;D++){const Oe=m[D];if(Me>=ve){De(Oe,O,R,!0);continue}let Ue;if(Oe.key!=null)Ue=ie.get(Oe.key);else for(te=Z;te<=G;te++)if(rn[te-Z]===0&&ze(Oe,y[te])){Ue=te;break}Ue===void 0?De(Oe,O,R,!0):(rn[Ue-Z]=D+1,Ue>=Ae?Ae=Ue:$e=!0,b(Oe,y[Ue],E,null,O,R,H,V,F),Me++)}const ur=$e?Vf(rn):$t;for(te=ur.length-1,D=ve-1;D>=0;D--){const Oe=Z+D,Ue=y[Oe],dr=Oe+1<K?y[Oe+1].el:N;rn[D]===0?b(null,Ue,E,dr,O,R,H,V,F):$e&&(te<0||D!==ur[te]?Xe(Ue,E,dr,2):te--)}}},Xe=(m,y,E,N,O=null)=>{const{el:R,type:H,transition:V,children:F,shapeFlag:D}=m;if(D&6){Xe(m.component.subTree,y,E,N);return}if(D&128){m.suspense.move(y,E,N);return}if(D&64){H.move(m,y,E,Dt);return}if(H===ge){s(R,y,E);for(let k=0;k<F.length;k++)Xe(F[k],y,E,N);s(m.anchor,y,E);return}if(H===Mt){d(m,y,E);return}if(N!==2&&D&1&&V)if(N===0)V.beforeEnter(R),s(R,y,E),ue(()=>V.enter(R),O);else{const{leave:k,delayLeave:G,afterLeave:q}=V,Z=()=>s(R,y,E),ie=()=>{k(R,()=>{Z(),q&&q()})};G?G(R,Z,ie):ie()}else s(R,y,E)},De=(m,y,E,N=!1,O=!1)=>{const{type:R,props:H,ref:V,children:F,dynamicChildren:D,shapeFlag:K,patchFlag:k,dirs:G,cacheIndex:q}=m;if(k===-2&&(O=!1),V!=null&&En(V,null,E,m,!0),q!=null&&(y.renderCache[q]=void 0),K&256){y.ctx.deactivate(m);return}const Z=K&1&&G,ie=!gt(m);let te;if(ie&&(te=H&&H.onVnodeBeforeUnmount)&&Ee(te,y,m),K&6)Sa(m.component,E,N);else{if(K&128){m.suspense.unmount(E,N);return}Z&&qe(m,null,y,"beforeUnmount"),K&64?m.type.remove(m,y,E,Dt,N):D&&!D.hasOnce&&(R!==ge||k>0&&k&64)?sn(D,y,E,!1,!0):(R===ge&&k&384||!O&&K&16)&&sn(F,y,E),N&&cr(m)}(ie&&(te=H&&H.onVnodeUnmounted)||Z)&&ue(()=>{te&&Ee(te,y,m),Z&&qe(m,null,y,"unmounted")},E)},cr=m=>{const{type:y,el:E,anchor:N,transition:O}=m;if(y===ge){wa(E,N);return}if(y===Mt){g(m);return}const R=()=>{i(E),O&&!O.persisted&&O.afterLeave&&O.afterLeave()};if(m.shapeFlag&1&&O&&!O.persisted){const{leave:H,delayLeave:V}=O,F=()=>H(E,R);V?V(m.el,R,F):F()}else R()},wa=(m,y)=>{let E;for(;m!==y;)E=p(m),i(m),m=E;i(y)},Sa=(m,y,E)=>{const{bum:N,scope:O,job:R,subTree:H,um:V,m:F,a:D}=m;as(F),as(D),N&&Gt(N),O.stop(),R&&(R.flags|=8,De(H,m,y,E)),V&&ue(V,y),ue(()=>{m.isUnmounted=!0},y),y&&y.pendingBranch&&!y.isUnmounted&&m.asyncDep&&!m.asyncResolved&&m.suspenseId===y.pendingId&&(y.deps--,y.deps===0&&y.resolve())},sn=(m,y,E,N=!1,O=!1,R=0)=>{for(let H=R;H<m.length;H++)De(m[H],y,E,N,O)},Fn=m=>{if(m.shapeFlag&6)return Fn(m.component.subTree);if(m.shapeFlag&128)return m.suspense.next();const y=p(m.anchor||m.el),E=y&&y[$o];return E?p(E):y};let ks=!1;const fr=(m,y,E)=>{m==null?y._vnode&&De(y._vnode,null,null,!0):b(y._vnode||null,m,y,null,null,null,E),y._vnode=m,ks||(ks=!0,vr(),os(),ks=!1)},Dt={p:b,um:De,m:Xe,r:cr,mt:U,mc:A,pc:j,pbc:w,n:Fn,o:e};let $s,zs;return t&&([$s,zs]=t(Dt)),{render:fr,hydrate:$s,createApp:Of(fr,$s)}}function Js({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Tt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function bl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function er(e,t,n=!1){const s=e.children,i=t.children;if(z(s)&&z(i))for(let r=0;r<s.length;r++){const o=s[r];let a=i[r];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=i[r]=dt(i[r]),a.el=o.el),!n&&a.patchFlag!==-2&&er(o,a)),a.type===mt&&(a.el=o.el)}}function Vf(e){const t=e.slice(),n=[0];let s,i,r,o,a;const l=e.length;for(s=0;s<l;s++){const f=e[s];if(f!==0){if(i=n[n.length-1],e[i]<f){t[s]=i,n.push(s);continue}for(r=0,o=n.length-1;r<o;)a=r+o>>1,e[n[a]]<f?r=a+1:o=a;f<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,o=n[r-1];r-- >0;)n[r]=o,o=t[o];return n}function wl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:wl(t)}function as(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Sl=Symbol.for("v-scx"),_l=()=>mn(Sl);function Hf(e,t){return Dn(e,null,t)}function kf(e,t){return Dn(e,null,{flush:"post"})}function Tl(e,t){return Dn(e,null,{flush:"sync"})}function Pt(e,t,n){return Dn(e,t,n)}function Dn(e,t,n=J){const{immediate:s,deep:i,flush:r,once:o}=n,a=se({},n),l=t&&s||!t&&r!=="post";let f;if(Yt){if(r==="sync"){const h=_l();f=h.__watcherHandles||(h.__watcherHandles=[])}else if(!l){const h=()=>{};return h.stop=Fe,h.resume=Fe,h.pause=Fe,h}}const c=de;a.call=(h,v,b)=>He(h,c,v,b);let u=!1;r==="post"?a.scheduler=h=>{ue(h,c&&c.suspense)}:r!=="sync"&&(u=!0,a.scheduler=(h,v)=>{v?h():ji(h)}),a.augmentJob=h=>{t&&(h.flags|=4),u&&(h.flags|=2,c&&(h.id=c.uid,h.i=c))};const p=_c(e,t,a);return Yt&&(f?f.push(p):l&&p()),p}function $f(e,t,n){const s=this.proxy,i=oe(e)?e.includes(".")?xl(s,e):()=>s[e]:e.bind(s,s);let r;W(t)?r=t:(r=t.handler,n=t);const o=Ot(this),a=Dn(i,r.bind(s),n);return o(),a}function xl(e,t){const n=t.split(".");return()=>{let s=e;for(let i=0;i<n.length&&s;i++)s=s[n[i]];return s}}function zf(e,t,n=J){const s=ke(),i=me(t),r=Pe(t),o=El(e,i),a=Ro((l,f)=>{let c,u=J,p;return Tl(()=>{const h=e[i];Te(c,h)&&(c=h,f())}),{get(){return l(),n.get?n.get(c):c},set(h){const v=n.set?n.set(h):h;if(!Te(v,c)&&!(u!==J&&Te(h,u)))return;const b=s.vnode.props;b&&(t in b||i in b||r in b)&&(`onUpdate:${t}`in b||`onUpdate:${i}`in b||`onUpdate:${r}`in b)||(c=h,f()),s.emit(`update:${t}`,v),Te(h,v)&&Te(h,u)&&!Te(v,p)&&f(),u=h,p=v}}});return a[Symbol.iterator]=()=>{let l=0;return{next(){return l<2?{value:l++?o||J:a,done:!1}:{done:!0}}}},a}const El=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${me(t)}Modifiers`]||e[`${Pe(t)}Modifiers`];function jf(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||J;let i=n;const r=t.startsWith("update:"),o=r&&El(s,t.slice(7));o&&(o.trim&&(i=n.map(c=>oe(c)?c.trim():c)),o.number&&(i=n.map(ts)));let a,l=s[a=un(t)]||s[a=un(me(t))];!l&&r&&(l=s[a=un(Pe(t))]),l&&He(l,e,6,i);const f=s[a+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,He(f,e,6,i)}}function Cl(e,t,n=!1){const s=t.emitsCache,i=s.get(e);if(i!==void 0)return i;const r=e.emits;let o={},a=!1;if(!W(e)){const l=f=>{const c=Cl(f,t,!0);c&&(a=!0,se(o,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!r&&!a?(ne(e)&&s.set(e,null),null):(z(r)?r.forEach(l=>o[l]=null):se(o,r),ne(e)&&s.set(e,o),o)}function Fs(e,t){return!e||!An(t)?!1:(t=t.slice(2).replace(/Once$/,""),ee(e,t[0].toLowerCase()+t.slice(1))||ee(e,Pe(t))||ee(e,t))}function Jn(e){const{type:t,vnode:n,proxy:s,withProxy:i,propsOptions:[r],slots:o,attrs:a,emit:l,render:f,renderCache:c,props:u,data:p,setupState:h,ctx:v,inheritAttrs:b}=e,I=xn(e);let T,x;try{if(n.shapeFlag&4){const g=i||s,_=g;T=Ce(f.call(_,g,c,u,h,p,v)),x=a}else{const g=t;T=Ce(g.length>1?g(u,{attrs:a,slots:o,emit:l}):g(u,null)),x=t.props?a:Uf(a)}}catch(g){vn.length=0,Rt(g,e,1),T=le(ce)}let d=T;if(x&&b!==!1){const g=Object.keys(x),{shapeFlag:_}=d;g.length&&_&7&&(r&&g.some(Ri)&&(x=Wf(x,r)),d=Ye(d,x,!1,!0))}return n.dirs&&(d=Ye(d,null,!1,!0),d.dirs=d.dirs?d.dirs.concat(n.dirs):n.dirs),n.transition&&it(d,n.transition),T=d,xn(I),T}function Gf(e,t=!0){let n;for(let s=0;s<e.length;s++){const i=e[s];if(rt(i)){if(i.type!==ce||i.children==="v-if"){if(n)return;n=i}}else return}return n}const Uf=e=>{let t;for(const n in e)(n==="class"||n==="style"||An(n))&&((t||(t={}))[n]=e[n]);return t},Wf=(e,t)=>{const n={};for(const s in e)(!Ri(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Kf(e,t,n){const{props:s,children:i,component:r}=e,{props:o,children:a,patchFlag:l}=t,f=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?Ar(s,o,f):!!o;if(l&8){const c=t.dynamicProps;for(let u=0;u<c.length;u++){const p=c[u];if(o[p]!==s[p]&&!Fs(f,p))return!0}}}else return(i||a)&&(!a||!a.$stable)?!0:s===o?!1:s?o?Ar(s,o,f):!0:!!o;return!1}function Ar(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let i=0;i<s.length;i++){const r=s[i];if(t[r]!==e[r]&&!Fs(n,r))return!0}return!1}function Vs({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const cs=e=>e.__isSuspense;let yi=0;const qf={name:"Suspense",__isSuspense:!0,process(e,t,n,s,i,r,o,a,l,f){if(e==null)Xf(t,n,s,i,r,o,a,l,f);else{if(r&&r.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}Jf(e,t,n,s,i,o,a,l,f)}},hydrate:Zf,normalize:Qf},Yf=qf;function Pn(e,t){const n=e.props&&e.props[t];W(n)&&n()}function Xf(e,t,n,s,i,r,o,a,l){const{p:f,o:{createElement:c}}=l,u=c("div"),p=e.suspense=Pl(e,i,s,t,u,n,r,o,a,l);f(null,p.pendingBranch=e.ssContent,u,null,s,p,r,o),p.deps>0?(Pn(e,"onPending"),Pn(e,"onFallback"),f(null,e.ssFallback,t,n,s,null,r,o),Wt(p,e.ssFallback)):p.resolve(!1,!0)}function Jf(e,t,n,s,i,r,o,a,{p:l,um:f,o:{createElement:c}}){const u=t.suspense=e.suspense;u.vnode=t,t.el=e.el;const p=t.ssContent,h=t.ssFallback,{activeBranch:v,pendingBranch:b,isInFallback:I,isHydrating:T}=u;if(b)u.pendingBranch=p,ze(p,b)?(l(b,p,u.hiddenContainer,null,i,u,r,o,a),u.deps<=0?u.resolve():I&&(T||(l(v,h,n,s,i,null,r,o,a),Wt(u,h)))):(u.pendingId=yi++,T?(u.isHydrating=!1,u.activeBranch=b):f(b,i,u),u.deps=0,u.effects.length=0,u.hiddenContainer=c("div"),I?(l(null,p,u.hiddenContainer,null,i,u,r,o,a),u.deps<=0?u.resolve():(l(v,h,n,s,i,null,r,o,a),Wt(u,h))):v&&ze(p,v)?(l(v,p,n,s,i,u,r,o,a),u.resolve(!0)):(l(null,p,u.hiddenContainer,null,i,u,r,o,a),u.deps<=0&&u.resolve()));else if(v&&ze(p,v))l(v,p,n,s,i,u,r,o,a),Wt(u,p);else if(Pn(t,"onPending"),u.pendingBranch=p,p.shapeFlag&512?u.pendingId=p.component.suspenseId:u.pendingId=yi++,l(null,p,u.hiddenContainer,null,i,u,r,o,a),u.deps<=0)u.resolve();else{const{timeout:x,pendingId:d}=u;x>0?setTimeout(()=>{u.pendingId===d&&u.fallback(h)},x):x===0&&u.fallback(h)}}function Pl(e,t,n,s,i,r,o,a,l,f,c=!1){const{p:u,m:p,um:h,n:v,o:{parentNode:b,remove:I}}=f;let T;const x=eu(e);x&&t&&t.pendingBranch&&(T=t.pendingId,t.deps++);const d=e.props?ns(e.props.timeout):void 0,g=r,_={vnode:e,parent:t,parentComponent:n,namespace:o,container:s,hiddenContainer:i,deps:0,pendingId:yi++,timeout:typeof d=="number"?d:-1,activeBranch:null,pendingBranch:null,isInFallback:!c,isHydrating:c,isUnmounted:!1,effects:[],resolve(P=!1,B=!1){const{vnode:A,activeBranch:S,pendingBranch:w,pendingId:M,effects:C,parentComponent:L,container:U}=_;let Y=!1;_.isHydrating?_.isHydrating=!1:P||(Y=S&&w.transition&&w.transition.mode==="out-in",Y&&(S.transition.afterLeave=()=>{M===_.pendingId&&(p(w,U,r===g?v(S):r,0),_n(C))}),S&&(b(S.el)===U&&(r=v(S)),h(S,L,_,!0)),Y||p(w,U,r,0)),Wt(_,w),_.pendingBranch=null,_.isInFallback=!1;let $=_.parent,X=!1;for(;$;){if($.pendingBranch){$.effects.push(...C),X=!0;break}$=$.parent}!X&&!Y&&_n(C),_.effects=[],x&&t&&t.pendingBranch&&T===t.pendingId&&(t.deps--,t.deps===0&&!B&&t.resolve()),Pn(A,"onResolve")},fallback(P){if(!_.pendingBranch)return;const{vnode:B,activeBranch:A,parentComponent:S,container:w,namespace:M}=_;Pn(B,"onFallback");const C=v(A),L=()=>{_.isInFallback&&(u(null,P,w,C,S,null,M,a,l),Wt(_,P))},U=P.transition&&P.transition.mode==="out-in";U&&(A.transition.afterLeave=L),_.isInFallback=!0,h(A,S,null,!0),U||L()},move(P,B,A){_.activeBranch&&p(_.activeBranch,P,B,A),_.container=P},next(){return _.activeBranch&&v(_.activeBranch)},registerDep(P,B,A){const S=!!_.pendingBranch;S&&_.deps++;const w=P.vnode.el;P.asyncDep.catch(M=>{Rt(M,P,0)}).then(M=>{if(P.isUnmounted||_.isUnmounted||_.pendingId!==P.suspenseId)return;P.asyncResolved=!0;const{vnode:C}=P;_i(P,M,!1),w&&(C.el=w);const L=!w&&P.subTree.el;B(P,C,b(w||P.subTree.el),w?null:v(P.subTree),_,o,A),L&&I(L),Vs(P,C.el),S&&--_.deps===0&&_.resolve()})},unmount(P,B){_.isUnmounted=!0,_.activeBranch&&h(_.activeBranch,n,P,B),_.pendingBranch&&h(_.pendingBranch,n,P,B)}};return _}function Zf(e,t,n,s,i,r,o,a,l){const f=t.suspense=Pl(t,s,n,e.parentNode,document.createElement("div"),null,i,r,o,a,!0),c=l(e,f.pendingBranch=t.ssContent,n,f,r,o);return f.deps===0&&f.resolve(!1,!0),c}function Qf(e){const{shapeFlag:t,children:n}=e,s=t&32;e.ssContent=Or(s?n.default:n),e.ssFallback=s?Or(n.fallback):le(ce)}function Or(e){let t;if(W(e)){const n=At&&e._c;n&&(e._d=!1,Mn()),e=e(),n&&(e._d=!0,t=we,Al())}return z(e)&&(e=Gf(e)),e=Ce(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function Ml(e,t){t&&t.pendingBranch?z(e)?t.effects.push(...e):t.effects.push(e):_n(e)}function Wt(e,t){e.activeBranch=t;const{vnode:n,parentComponent:s}=e;let i=t.el;for(;!i&&t.component;)t=t.component.subTree,i=t.el;n.el=i,s&&s.subTree===n&&(s.vnode.el=i,Vs(s,i))}function eu(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const ge=Symbol.for("v-fgt"),mt=Symbol.for("v-txt"),ce=Symbol.for("v-cmt"),Mt=Symbol.for("v-stc"),vn=[];let we=null;function Mn(e=!1){vn.push(we=e?null:[])}function Al(){vn.pop(),we=vn[vn.length-1]||null}let At=1;function bi(e,t=!1){At+=e,e<0&&we&&t&&(we.hasOnce=!0)}function Ol(e){return e.dynamicChildren=At>0?we||$t:null,Al(),At>0&&we&&we.push(e),e}function tu(e,t,n,s,i,r){return Ol(tr(e,t,n,s,i,r,!0))}function fs(e,t,n,s,i){return Ol(le(e,t,n,s,i,!0))}function rt(e){return e?e.__v_isVNode===!0:!1}function ze(e,t){return e.type===t.type&&e.key===t.key}function nu(e){}const Il=({key:e})=>e??null,Zn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?oe(e)||he(e)||W(e)?{i:pe,r:e,k:t,f:!!n}:e:null);function tr(e,t=null,n=null,s=0,i=null,r=e===ge?0:1,o=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Il(t),ref:t&&Zn(t),scopeId:Is,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:pe};return a?(sr(l,n),r&128&&e.normalize(l)):n&&(l.shapeFlag|=oe(n)?8:16),At>0&&!o&&we&&(l.patchFlag>0||r&6)&&l.patchFlag!==32&&we.push(l),l}const le=su;function su(e,t=null,n=null,s=0,i=null,r=!1){if((!e||e===sl)&&(e=ce),rt(e)){const a=Ye(e,t,!0);return n&&sr(a,n),At>0&&!r&&we&&(a.shapeFlag&6?we[we.indexOf(e)]=a:we.push(a)),a.patchFlag=-2,a}if(du(e)&&(e=e.__vccOpts),t){t=Ll(t);let{class:a,style:l}=t;a&&!oe(a)&&(t.class=Ln(a)),ne(l)&&(As(l)&&!z(l)&&(l=se({},l)),t.style=In(l))}const o=oe(e)?1:cs(e)?128:zo(e)?64:ne(e)?4:W(e)?2:0;return tr(e,t,n,s,i,o,r,!0)}function Ll(e){return e?As(e)||cl(e)?se({},e):e:null}function Ye(e,t,n=!1,s=!1){const{props:i,ref:r,patchFlag:o,children:a,transition:l}=e,f=t?Rl(i||{},t):i,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&Il(f),ref:t&&t.ref?n&&r?z(r)?r.concat(Zn(t)):[r,Zn(t)]:Zn(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ge?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ye(e.ssContent),ssFallback:e.ssFallback&&Ye(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&it(c,l.clone(c)),c}function nr(e=" ",t=0){return le(mt,null,e,t)}function iu(e,t){const n=le(Mt,null,e);return n.staticCount=t,n}function ru(e="",t=!1){return t?(Mn(),fs(ce,null,e)):le(ce,null,e)}function Ce(e){return e==null||typeof e=="boolean"?le(ce):z(e)?le(ge,null,e.slice()):rt(e)?dt(e):le(mt,null,String(e))}function dt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ye(e)}function sr(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(z(t))n=16;else if(typeof t=="object")if(s&65){const i=t.default;i&&(i._c&&(i._d=!1),sr(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!cl(t)?t._ctx=pe:i===3&&pe&&(pe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else W(t)?(t={default:t,_ctx:pe},n=32):(t=String(t),s&64?(n=16,t=[nr(t)]):n=8);e.children=t,e.shapeFlag|=n}function Rl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const i in s)if(i==="class")t.class!==s.class&&(t.class=Ln([t.class,s.class]));else if(i==="style")t.style=In([t.style,s.style]);else if(An(i)){const r=t[i],o=s[i];o&&r!==o&&!(z(r)&&r.includes(o))&&(t[i]=r?[].concat(r,o):o)}else i!==""&&(t[i]=s[i])}return t}function Ee(e,t,n,s=null){He(e,t,7,[n,s])}const ou=ol();let lu=0;function Nl(e,t,n){const s=e.type,i=(t?t.appContext:e.appContext)||ou,r={uid:lu++,vnode:e,type:s,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Fi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ul(s,i),emitsOptions:Cl(s,i),emit:null,emitted:null,propsDefaults:J,inheritAttrs:s.inheritAttrs,ctx:J,data:J,props:J,attrs:J,slots:J,refs:J,setupState:J,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=jf.bind(null,r),e.ce&&e.ce(r),r}let de=null;const ke=()=>de||pe;let us,wi;{const e=_s(),t=(n,s)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(s),r=>{i.length>1?i.forEach(o=>o(r)):i[0](r)}};us=t("__VUE_INSTANCE_SETTERS__",n=>de=n),wi=t("__VUE_SSR_SETTERS__",n=>Yt=n)}const Ot=e=>{const t=de;return us(e),e.scope.on(),()=>{e.scope.off(),us(t)}},Si=()=>{de&&de.scope.off(),us(null)};function Dl(e){return e.vnode.shapeFlag&4}let Yt=!1;function Bl(e,t=!1,n=!1){t&&wi(t);const{props:s,children:i}=e.vnode,r=Dl(e);Lf(e,s,r,t),Bf(e,i,n);const o=r?au(e,t):void 0;return t&&wi(!1),o}function au(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,hi);const{setup:s}=n;if(s){wt();const i=e.setupContext=s.length>1?Vl(e):null,r=Ot(e),o=Qt(s,e,0,[e.props,i]),a=Di(o);if(St(),r(),(a||e.sp)&&!gt(e)&&qi(e),a){if(o.then(Si,Si),t)return o.then(l=>{_i(e,l,t)}).catch(l=>{Rt(l,e,0)});e.asyncDep=o}else _i(e,o,t)}else Fl(e,t)}function _i(e,t,n){W(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ne(t)&&(e.setupState=zi(t)),Fl(e,n)}let ds,Ti;function cu(e){ds=e,Ti=t=>{t.render._rc&&(t.withProxy=new Proxy(t.ctx,ff))}}const fu=()=>!ds;function Fl(e,t,n){const s=e.type;if(!e.render){if(!t&&ds&&!s.render){const i=s.template||Zi(e).template;if(i){const{isCustomElement:r,compilerOptions:o}=e.appContext.config,{delimiters:a,compilerOptions:l}=s,f=se(se({isCustomElement:r,delimiters:a},o),l);s.render=ds(i,f)}}e.render=s.render||Fe,Ti&&Ti(e)}{const i=Ot(e);wt();try{xf(e)}finally{St(),i()}}}const uu={get(e,t){return ye(e,"get",""),e[t]}};function Vl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,uu),slots:e.slots,emit:e.emit,expose:t}}function Bn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(zi(Oo(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in gn)return gn[n](e)},has(t,n){return n in t||n in gn}})):e.proxy}function xi(e,t=!0){return W(e)?e.displayName||e.name:e.name||t&&e.__name}function du(e){return W(e)&&"__vccOpts"in e}const ir=(e,t)=>yc(e,t,Yt);function Le(e,t,n){const s=arguments.length;return s===2?ne(t)&&!z(t)?rt(t)?le(e,null,[t]):le(e,t):le(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&rt(n)&&(n=[n]),le(e,t,n))}function pu(){}function hu(e,t,n,s){const i=n[s];if(i&&Hl(i,e))return i;const r=t();return r.memo=e.slice(),r.cacheIndex=s,n[s]=r}function Hl(e,t){const n=e.memo;if(n.length!=t.length)return!1;for(let s=0;s<n.length;s++)if(Te(n[s],t[s]))return!1;return At>0&&we&&we.push(e),!0}const kl="3.5.13",gu=Fe,mu=Pc,vu=Ht,yu=ko,bu={createComponentInstance:Nl,setupComponent:Bl,renderComponentRoot:Jn,setCurrentRenderingInstance:xn,isVNode:rt,normalizeVNode:Ce,getComponentPublicInstance:Bn,ensureValidVNode:Ji,pushWarningContext:Tc,popWarningContext:xc},wu=bu,Su=null,_u=null,Tu=null;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ei;const Ir=typeof window<"u"&&window.trustedTypes;if(Ir)try{Ei=Ir.createPolicy("vue",{createHTML:e=>e})}catch{}const $l=Ei?e=>Ei.createHTML(e):e=>e,xu="http://www.w3.org/2000/svg",Eu="http://www.w3.org/1998/Math/MathML",Qe=typeof document<"u"?document:null,Lr=Qe&&Qe.createElement("template"),Cu={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const i=t==="svg"?Qe.createElementNS(xu,e):t==="mathml"?Qe.createElementNS(Eu,e):n?Qe.createElement(e,{is:n}):Qe.createElement(e);return e==="select"&&s&&s.multiple!=null&&i.setAttribute("multiple",s.multiple),i},createText:e=>Qe.createTextNode(e),createComment:e=>Qe.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Qe.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,i,r){const o=n?n.previousSibling:t.lastChild;if(i&&(i===r||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===r||!(i=i.nextSibling)););else{Lr.innerHTML=$l(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=Lr.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},lt="transition",ln="animation",Xt=Symbol("_vtc"),zl={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},jl=se({},Wi,zl),Pu=e=>(e.displayName="Transition",e.props=jl,e),Mu=Pu((e,{slots:t})=>Le(Ko,Gl(e),t)),xt=(e,t=[])=>{z(e)?e.forEach(n=>n(...t)):e&&e(...t)},Rr=e=>e?z(e)?e.some(t=>t.length>1):e.length>1:!1;function Gl(e){const t={};for(const C in e)C in zl||(t[C]=e[C]);if(e.css===!1)return t;const{name:n="v",type:s,duration:i,enterFromClass:r=`${n}-enter-from`,enterActiveClass:o=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=r,appearActiveClass:f=o,appearToClass:c=a,leaveFromClass:u=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,v=Au(i),b=v&&v[0],I=v&&v[1],{onBeforeEnter:T,onEnter:x,onEnterCancelled:d,onLeave:g,onLeaveCancelled:_,onBeforeAppear:P=T,onAppear:B=x,onAppearCancelled:A=d}=t,S=(C,L,U,Y)=>{C._enterCancelled=Y,at(C,L?c:a),at(C,L?f:o),U&&U()},w=(C,L)=>{C._isLeaving=!1,at(C,u),at(C,h),at(C,p),L&&L()},M=C=>(L,U)=>{const Y=C?B:x,$=()=>S(L,C,U);xt(Y,[L,$]),Nr(()=>{at(L,C?l:r),We(L,C?c:a),Rr(Y)||Dr(L,s,b,$)})};return se(t,{onBeforeEnter(C){xt(T,[C]),We(C,r),We(C,o)},onBeforeAppear(C){xt(P,[C]),We(C,l),We(C,f)},onEnter:M(!1),onAppear:M(!0),onLeave(C,L){C._isLeaving=!0;const U=()=>w(C,L);We(C,u),C._enterCancelled?(We(C,p),Ci()):(Ci(),We(C,p)),Nr(()=>{C._isLeaving&&(at(C,u),We(C,h),Rr(g)||Dr(C,s,I,U))}),xt(g,[C,U])},onEnterCancelled(C){S(C,!1,void 0,!0),xt(d,[C])},onAppearCancelled(C){S(C,!0,void 0,!0),xt(A,[C])},onLeaveCancelled(C){w(C),xt(_,[C])}})}function Au(e){if(e==null)return null;if(ne(e))return[Zs(e.enter),Zs(e.leave)];{const t=Zs(e);return[t,t]}}function Zs(e){return ns(e)}function We(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Xt]||(e[Xt]=new Set)).add(t)}function at(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Xt];n&&(n.delete(t),n.size||(e[Xt]=void 0))}function Nr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Ou=0;function Dr(e,t,n,s){const i=e._endId=++Ou,r=()=>{i===e._endId&&s()};if(n!=null)return setTimeout(r,n);const{type:o,timeout:a,propCount:l}=Ul(e,t);if(!o)return s();const f=o+"end";let c=0;const u=()=>{e.removeEventListener(f,p),r()},p=h=>{h.target===e&&++c>=l&&u()};setTimeout(()=>{c<l&&u()},a+1),e.addEventListener(f,p)}function Ul(e,t){const n=window.getComputedStyle(e),s=v=>(n[v]||"").split(", "),i=s(`${lt}Delay`),r=s(`${lt}Duration`),o=Br(i,r),a=s(`${ln}Delay`),l=s(`${ln}Duration`),f=Br(a,l);let c=null,u=0,p=0;t===lt?o>0&&(c=lt,u=o,p=r.length):t===ln?f>0&&(c=ln,u=f,p=l.length):(u=Math.max(o,f),c=u>0?o>f?lt:ln:null,p=c?c===lt?r.length:l.length:0);const h=c===lt&&/\b(transform|all)(,|$)/.test(s(`${lt}Property`).toString());return{type:c,timeout:u,propCount:p,hasTransform:h}}function Br(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Fr(n)+Fr(e[s])))}function Fr(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Ci(){return document.body.offsetHeight}function Iu(e,t,n){const s=e[Xt];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ps=Symbol("_vod"),Wl=Symbol("_vsh"),Kl={beforeMount(e,{value:t},{transition:n}){e[ps]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):an(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),an(e,!0),s.enter(e)):s.leave(e,()=>{an(e,!1)}):an(e,t))},beforeUnmount(e,{value:t}){an(e,t)}};function an(e,t){e.style.display=t?e[ps]:"none",e[Wl]=!t}function Lu(){Kl.getSSRProps=({value:e})=>{if(!e)return{style:{display:"none"}}}}const ql=Symbol("");function Ru(e){const t=ke();if(!t)return;const n=t.ut=(i=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(r=>hs(r,i))},s=()=>{const i=e(t.proxy);t.ce?hs(t.ce,i):Pi(t.subTree,i),n(i)};Ns(()=>{_n(s)}),Nt(()=>{Pt(s,Fe,{flush:"post"});const i=new MutationObserver(s);i.observe(t.subTree.el.parentNode,{childList:!0}),Ds(()=>i.disconnect())})}function Pi(e,t){if(e.shapeFlag&128){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{Pi(n.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)hs(e.el,t);else if(e.type===ge)e.children.forEach(n=>Pi(n,t));else if(e.type===Mt){let{el:n,anchor:s}=e;for(;n&&(hs(n,t),n!==s);)n=n.nextSibling}}function hs(e,t){if(e.nodeType===1){const n=e.style;let s="";for(const i in t)n.setProperty(`--${i}`,t[i]),s+=`--${i}: ${t[i]};`;n[ql]=s}}const Nu=/(^|;)\s*display\s*:/;function Du(e,t,n){const s=e.style,i=oe(n);let r=!1;if(n&&!i){if(t)if(oe(t))for(const o of t.split(";")){const a=o.slice(0,o.indexOf(":")).trim();n[a]==null&&Qn(s,a,"")}else for(const o in t)n[o]==null&&Qn(s,o,"");for(const o in n)o==="display"&&(r=!0),Qn(s,o,n[o])}else if(i){if(t!==n){const o=s[ql];o&&(n+=";"+o),s.cssText=n,r=Nu.test(n)}}else t&&e.removeAttribute("style");ps in e&&(e[ps]=r?s.display:"",e[Wl]&&(s.display="none"))}const Vr=/\s*!important$/;function Qn(e,t,n){if(z(n))n.forEach(s=>Qn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Bu(e,t);Vr.test(n)?e.setProperty(Pe(s),n.replace(Vr,""),"important"):e[s]=n}}const Hr=["Webkit","Moz","ms"],Qs={};function Bu(e,t){const n=Qs[t];if(n)return n;let s=me(t);if(s!=="filter"&&s in e)return Qs[t]=s;s=On(s);for(let i=0;i<Hr.length;i++){const r=Hr[i]+s;if(r in e)return Qs[t]=r}return t}const kr="http://www.w3.org/1999/xlink";function $r(e,t,n,s,i,r=Ba(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(kr,t.slice(6,t.length)):e.setAttributeNS(kr,t,n):n==null||r&&!ao(n)?e.removeAttribute(t):e.setAttribute(t,r?"":Ge(n)?String(n):n)}function zr(e,t,n,s,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?$l(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const a=r==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=ao(n):n==null&&a==="string"?(n="",o=!0):a==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(i||t)}function nt(e,t,n,s){e.addEventListener(t,n,s)}function Fu(e,t,n,s){e.removeEventListener(t,n,s)}const jr=Symbol("_vei");function Vu(e,t,n,s,i=null){const r=e[jr]||(e[jr]={}),o=r[t];if(s&&o)o.value=s;else{const[a,l]=Hu(t);if(s){const f=r[t]=zu(s,i);nt(e,a,f,l)}else o&&(Fu(e,a,o,l),r[t]=void 0)}}const Gr=/(?:Once|Passive|Capture)$/;function Hu(e){let t;if(Gr.test(e)){t={};let s;for(;s=e.match(Gr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Pe(e.slice(2)),t]}let ei=0;const ku=Promise.resolve(),$u=()=>ei||(ku.then(()=>ei=0),ei=Date.now());function zu(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;He(ju(s,n.value),t,5,[s])};return n.value=e,n.attached=$u(),n}function ju(e,t){if(z(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>i=>!i._stopped&&s&&s(i))}else return t}const Ur=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Gu=(e,t,n,s,i,r)=>{const o=i==="svg";t==="class"?Iu(e,s,o):t==="style"?Du(e,n,s):An(t)?Ri(t)||Vu(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Uu(e,t,s,o))?(zr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&$r(e,t,s,o,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!oe(s))?zr(e,me(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),$r(e,t,s,o))};function Uu(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ur(t)&&W(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Ur(t)&&oe(n)?!1:t in e}const Wr={};/*! #__NO_SIDE_EFFECTS__ */function Yl(e,t,n){const s=Ki(e,t);ws(s)&&se(s,t);class i extends Hs{constructor(o){super(s,o,n)}}return i.def=s,i}/*! #__NO_SIDE_EFFECTS__ */const Wu=(e,t)=>Yl(e,t,la),Ku=typeof HTMLElement<"u"?HTMLElement:class{};class Hs extends Ku{constructor(t,n={},s=Mi){super(),this._def=t,this._props=n,this._createApp=s,this._isVueCE=!0,this._instance=null,this._app=null,this._nonce=this._def.nonce,this._connected=!1,this._resolved=!1,this._numberProps=null,this._styleChildren=new WeakSet,this._ob=null,this.shadowRoot&&s!==Mi?this._root=this.shadowRoot:t.shadowRoot!==!1?(this.attachShadow({mode:"open"}),this._root=this.shadowRoot):this._root=this,this._def.__asyncLoader||this._resolveProps(this._def)}connectedCallback(){if(!this.isConnected)return;this.shadowRoot||this._parseSlots(),this._connected=!0;let t=this;for(;t=t&&(t.parentNode||t.host);)if(t instanceof Hs){this._parent=t;break}this._instance||(this._resolved?(this._setParent(),this._update()):t&&t._pendingResolve?this._pendingResolve=t._pendingResolve.then(()=>{this._pendingResolve=void 0,this._resolveDef()}):this._resolveDef())}_setParent(t=this._parent){t&&(this._instance.parent=t._instance,this._instance.provides=t._instance.provides)}disconnectedCallback(){this._connected=!1,Rn(()=>{this._connected||(this._ob&&(this._ob.disconnect(),this._ob=null),this._app&&this._app.unmount(),this._instance&&(this._instance.ce=void 0),this._app=this._instance=null)})}_resolveDef(){if(this._pendingResolve)return;for(let s=0;s<this.attributes.length;s++)this._setAttr(this.attributes[s].name);this._ob=new MutationObserver(s=>{for(const i of s)this._setAttr(i.attributeName)}),this._ob.observe(this,{attributes:!0});const t=(s,i=!1)=>{this._resolved=!0,this._pendingResolve=void 0;const{props:r,styles:o}=s;let a;if(r&&!z(r))for(const l in r){const f=r[l];(f===Number||f&&f.type===Number)&&(l in this._props&&(this._props[l]=ns(this._props[l])),(a||(a=Object.create(null)))[me(l)]=!0)}this._numberProps=a,i&&this._resolveProps(s),this.shadowRoot&&this._applyStyles(o),this._mount(s)},n=this._def.__asyncLoader;n?this._pendingResolve=n().then(s=>t(this._def=s,!0)):t(this._def)}_mount(t){this._app=this._createApp(t),t.configureApp&&t.configureApp(this._app),this._app._ceVNode=this._createVNode(),this._app.mount(this._root);const n=this._instance&&this._instance.exposed;if(n)for(const s in n)ee(this,s)||Object.defineProperty(this,s,{get:()=>Os(n[s])})}_resolveProps(t){const{props:n}=t,s=z(n)?n:Object.keys(n||{});for(const i of Object.keys(this))i[0]!=="_"&&s.includes(i)&&this._setProp(i,this[i]);for(const i of s.map(me))Object.defineProperty(this,i,{get(){return this._getProp(i)},set(r){this._setProp(i,r,!0,!0)}})}_setAttr(t){if(t.startsWith("data-v-"))return;const n=this.hasAttribute(t);let s=n?this.getAttribute(t):Wr;const i=me(t);n&&this._numberProps&&this._numberProps[i]&&(s=ns(s)),this._setProp(i,s,!1,!0)}_getProp(t){return this._props[t]}_setProp(t,n,s=!0,i=!1){if(n!==this._props[t]&&(n===Wr?delete this._props[t]:(this._props[t]=n,t==="key"&&this._app&&(this._app._ceVNode.key=n)),i&&this._instance&&this._update(),s)){const r=this._ob;r&&r.disconnect(),n===!0?this.setAttribute(Pe(t),""):typeof n=="string"||typeof n=="number"?this.setAttribute(Pe(t),n+""):n||this.removeAttribute(Pe(t)),r&&r.observe(this,{attributes:!0})}}_update(){oa(this._createVNode(),this._root)}_createVNode(){const t={};this.shadowRoot||(t.onVnodeMounted=t.onVnodeUpdated=this._renderSlots.bind(this));const n=le(this._def,se(t,this._props));return this._instance||(n.ce=s=>{this._instance=s,s.ce=this,s.isCE=!0;const i=(r,o)=>{this.dispatchEvent(new CustomEvent(r,ws(o[0])?se({detail:o},o[0]):{detail:o}))};s.emit=(r,...o)=>{i(r,o),Pe(r)!==r&&i(Pe(r),o)},this._setParent()}),n}_applyStyles(t,n){if(!t)return;if(n){if(n===this._def||this._styleChildren.has(n))return;this._styleChildren.add(n)}const s=this._nonce;for(let i=t.length-1;i>=0;i--){const r=document.createElement("style");s&&r.setAttribute("nonce",s),r.textContent=t[i],this.shadowRoot.prepend(r)}}_parseSlots(){const t=this._slots={};let n;for(;n=this.firstChild;){const s=n.nodeType===1&&n.getAttribute("slot")||"default";(t[s]||(t[s]=[])).push(n),this.removeChild(n)}}_renderSlots(){const t=(this._teleportTarget||this).querySelectorAll("slot"),n=this._instance.type.__scopeId;for(let s=0;s<t.length;s++){const i=t[s],r=i.getAttribute("name")||"default",o=this._slots[r],a=i.parentNode;if(o)for(const l of o){if(n&&l.nodeType===1){const f=n+"-s",c=document.createTreeWalker(l,1);l.setAttribute(f,"");let u;for(;u=c.nextNode();)u.setAttribute(f,"")}a.insertBefore(l,i)}else for(;i.firstChild;)a.insertBefore(i.firstChild,i);a.removeChild(i)}}_injectChildStyle(t){this._applyStyles(t.styles,t)}_removeChildStyle(t){}}function Xl(e){const t=ke(),n=t&&t.ce;return n||null}function qu(){const e=Xl();return e&&e.shadowRoot}function Yu(e="$style"){{const t=ke();if(!t)return J;const n=t.type.__cssModules;if(!n)return J;const s=n[e];return s||J}}const Jl=new WeakMap,Zl=new WeakMap,gs=Symbol("_moveCb"),Kr=Symbol("_enterCb"),Xu=e=>(delete e.props.mode,e),Ju=Xu({name:"TransitionGroup",props:se({},jl,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=ke(),s=Ui();let i,r;return en(()=>{if(!i.length)return;const o=e.moveClass||`${e.name||"v"}-move`;if(!nd(i[0].el,n.vnode.el,o))return;i.forEach(Qu),i.forEach(ed);const a=i.filter(td);Ci(),a.forEach(l=>{const f=l.el,c=f.style;We(f,o),c.transform=c.webkitTransform=c.transitionDuration="";const u=f[gs]=p=>{p&&p.target!==f||(!p||/transform$/.test(p.propertyName))&&(f.removeEventListener("transitionend",u),f[gs]=null,at(f,o))};f.addEventListener("transitionend",u)})}),()=>{const o=Q(e),a=Gl(o);let l=o.tag||ge;if(i=[],r)for(let f=0;f<r.length;f++){const c=r[f];c.el&&c.el instanceof Element&&(i.push(c),it(c,qt(c,a,s,n)),Jl.set(c,c.el.getBoundingClientRect()))}r=t.default?Ls(t.default()):[];for(let f=0;f<r.length;f++){const c=r[f];c.key!=null&&it(c,qt(c,a,s,n))}return le(l,null,r)}}}),Zu=Ju;function Qu(e){const t=e.el;t[gs]&&t[gs](),t[Kr]&&t[Kr]()}function ed(e){Zl.set(e,e.el.getBoundingClientRect())}function td(e){const t=Jl.get(e),n=Zl.get(e),s=t.left-n.left,i=t.top-n.top;if(s||i){const r=e.el.style;return r.transform=r.webkitTransform=`translate(${s}px,${i}px)`,r.transitionDuration="0s",e}}function nd(e,t,n){const s=e.cloneNode(),i=e[Xt];i&&i.forEach(a=>{a.split(/\s+/).forEach(l=>l&&s.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const r=t.nodeType===1?t:t.parentNode;r.appendChild(s);const{hasTransform:o}=Ul(s);return r.removeChild(s),o}const bt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return z(t)?n=>Gt(t,n):t};function sd(e){e.target.composing=!0}function qr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ve=Symbol("_assign"),ms={created(e,{modifiers:{lazy:t,trim:n,number:s}},i){e[Ve]=bt(i);const r=s||i.props&&i.props.type==="number";nt(e,t?"change":"input",o=>{if(o.target.composing)return;let a=e.value;n&&(a=a.trim()),r&&(a=ts(a)),e[Ve](a)}),n&&nt(e,"change",()=>{e.value=e.value.trim()}),t||(nt(e,"compositionstart",sd),nt(e,"compositionend",qr),nt(e,"change",qr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:i,number:r}},o){if(e[Ve]=bt(o),e.composing)return;const a=(r||e.type==="number")&&!/^0\d/.test(e.value)?ts(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||i&&e.value.trim()===l)||(e.value=l))}},rr={deep:!0,created(e,t,n){e[Ve]=bt(n),nt(e,"change",()=>{const s=e._modelValue,i=Jt(e),r=e.checked,o=e[Ve];if(z(s)){const a=Ts(s,i),l=a!==-1;if(r&&!l)o(s.concat(i));else if(!r&&l){const f=[...s];f.splice(a,1),o(f)}}else if(Lt(s)){const a=new Set(s);r?a.add(i):a.delete(i),o(a)}else o(ea(e,r))})},mounted:Yr,beforeUpdate(e,t,n){e[Ve]=bt(n),Yr(e,t,n)}};function Yr(e,{value:t,oldValue:n},s){e._modelValue=t;let i;if(z(t))i=Ts(t,s.props.value)>-1;else if(Lt(t))i=t.has(s.props.value);else{if(t===n)return;i=vt(t,ea(e,!0))}e.checked!==i&&(e.checked=i)}const or={created(e,{value:t},n){e.checked=vt(t,n.props.value),e[Ve]=bt(n),nt(e,"change",()=>{e[Ve](Jt(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[Ve]=bt(s),t!==n&&(e.checked=vt(t,s.props.value))}},Ql={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const i=Lt(t);nt(e,"change",()=>{const r=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>n?ts(Jt(o)):Jt(o));e[Ve](e.multiple?i?new Set(r):r:r[0]),e._assigning=!0,Rn(()=>{e._assigning=!1})}),e[Ve]=bt(s)},mounted(e,{value:t}){Xr(e,t)},beforeUpdate(e,t,n){e[Ve]=bt(n)},updated(e,{value:t}){e._assigning||Xr(e,t)}};function Xr(e,t){const n=e.multiple,s=z(t);if(!(n&&!s&&!Lt(t))){for(let i=0,r=e.options.length;i<r;i++){const o=e.options[i],a=Jt(o);if(n)if(s){const l=typeof a;l==="string"||l==="number"?o.selected=t.some(f=>String(f)===String(a)):o.selected=Ts(t,a)>-1}else o.selected=t.has(a);else if(vt(Jt(o),t)){e.selectedIndex!==i&&(e.selectedIndex=i);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Jt(e){return"_value"in e?e._value:e.value}function ea(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const ta={created(e,t,n){Kn(e,t,n,null,"created")},mounted(e,t,n){Kn(e,t,n,null,"mounted")},beforeUpdate(e,t,n,s){Kn(e,t,n,s,"beforeUpdate")},updated(e,t,n,s){Kn(e,t,n,s,"updated")}};function na(e,t){switch(e){case"SELECT":return Ql;case"TEXTAREA":return ms;default:switch(t){case"checkbox":return rr;case"radio":return or;default:return ms}}}function Kn(e,t,n,s,i){const o=na(e.tagName,n.props&&n.props.type)[i];o&&o(e,t,n,s)}function id(){ms.getSSRProps=({value:e})=>({value:e}),or.getSSRProps=({value:e},t)=>{if(t.props&&vt(t.props.value,e))return{checked:!0}},rr.getSSRProps=({value:e},t)=>{if(z(e)){if(t.props&&Ts(e,t.props.value)>-1)return{checked:!0}}else if(Lt(e)){if(t.props&&e.has(t.props.value))return{checked:!0}}else if(e)return{checked:!0}},ta.getSSRProps=(e,t)=>{if(typeof t.type!="string")return;const n=na(t.type.toUpperCase(),t.props&&t.props.type);if(n.getSSRProps)return n.getSSRProps(e,t)}}const rd=["ctrl","shift","alt","meta"],od={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>rd.some(n=>e[`${n}Key`]&&!t.includes(n))},ld=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(i,...r)=>{for(let o=0;o<t.length;o++){const a=od[t[o]];if(a&&a(i,t))return}return e(i,...r)})},ad={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},cd=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=i=>{if(!("key"in i))return;const r=Pe(i.key);if(t.some(o=>o===r||ad[o]===r))return e(i)})},sa=se({patchProp:Gu},Cu);let yn,Jr=!1;function ia(){return yn||(yn=ml(sa))}function ra(){return yn=Jr?yn:vl(sa),Jr=!0,yn}const oa=(...e)=>{ia().render(...e)},fd=(...e)=>{ra().hydrate(...e)},Mi=(...e)=>{const t=ia().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=ca(s);if(!i)return;const r=t._component;!W(r)&&!r.render&&!r.template&&(r.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const o=n(i,!1,aa(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),o},t},la=(...e)=>{const t=ra().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=ca(s);if(i)return n(i,!0,aa(i))},t};function aa(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ca(e){return oe(e)?document.querySelector(e):e}let Zr=!1;const ud=()=>{Zr||(Zr=!0,id(),Lu())};/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const dd=()=>{},Hp=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:Ko,BaseTransitionPropsValidators:Wi,Comment:ce,DeprecationTypes:Tu,EffectScope:Fi,ErrorCodes:Cc,ErrorTypeStrings:mu,Fragment:ge,KeepAlive:Qc,ReactiveEffect:bn,Static:Mt,Suspense:Yf,Teleport:Dc,Text:mt,TrackOpTypes:bc,Transition:Mu,TransitionGroup:Zu,TriggerOpTypes:wc,VueElement:Hs,assertNumber:Ec,callWithAsyncErrorHandling:He,callWithErrorHandling:Qt,camelize:me,capitalize:On,cloneVNode:Ye,compatUtils:_u,compile:dd,computed:ir,createApp:Mi,createBlock:fs,createCommentVNode:ru,createElementBlock:tu,createElementVNode:tr,createHydrationRenderer:vl,createPropsRestProxy:_f,createRenderer:ml,createSSRApp:la,createSlots:lf,createStaticVNode:iu,createTextVNode:nr,createVNode:le,customRef:Ro,defineAsyncComponent:Jc,defineComponent:Ki,defineCustomElement:Yl,defineEmits:df,defineExpose:pf,defineModel:mf,defineOptions:hf,defineProps:uf,defineSSRCustomElement:Wu,defineSlots:gf,devtools:vu,effect:$a,effectScope:Va,getCurrentInstance:ke,getCurrentScope:po,getCurrentWatcher:Sc,getTransitionRawChildren:Ls,guardReactiveProps:Ll,h:Le,handleError:Rt,hasInjectionContext:If,hydrate:fd,hydrateOnIdle:Uc,hydrateOnInteraction:Yc,hydrateOnMediaQuery:qc,hydrateOnVisible:Kc,initCustomFormatter:pu,initDirectivesForSSR:ud,inject:mn,isMemoSame:Hl,isProxy:As,isReactive:ht,isReadonly:yt,isRef:he,isRuntimeOnly:fu,isShallow:Re,isVNode:rt,markRaw:Oo,mergeDefaults:wf,mergeModels:Sf,mergeProps:Rl,nextTick:Rn,normalizeClass:Ln,normalizeProps:Na,normalizeStyle:In,onActivated:Yo,onBeforeMount:Zo,onBeforeUnmount:tn,onBeforeUpdate:Ns,onDeactivated:Xo,onErrorCaptured:nl,onMounted:Nt,onRenderTracked:tl,onRenderTriggered:el,onScopeDispose:Ha,onServerPrefetch:Qo,onUnmounted:Ds,onUpdated:en,onWatcherCleanup:Do,openBlock:Mn,popScopeId:Ic,provide:Bs,proxyRefs:zi,pushScopeId:Oc,queuePostFlushCb:_n,reactive:Ps,readonly:$i,ref:fe,registerRuntimeCompiler:cu,render:oa,renderList:of,renderSlot:af,resolveComponent:nf,resolveDirective:rf,resolveDynamicComponent:sf,resolveFilter:Su,resolveTransitionHooks:qt,setBlockTracking:bi,setDevtoolsHook:yu,setTransitionHooks:it,shallowReactive:Ao,shallowReadonly:lc,shallowRef:Io,ssrContextKey:Sl,ssrUtils:wu,stop:za,toDisplayString:fo,toHandlerKey:un,toHandlers:cf,toRaw:Q,toRef:mc,toRefs:pc,toValue:fc,transformVNodeArgs:nu,triggerRef:cc,unref:Os,useAttrs:bf,useCssModule:Yu,useCssVars:Ru,useHost:Xl,useId:Fc,useModel:zf,useSSRContext:_l,useShadowRoot:qu,useSlots:yf,useTemplateRef:Vc,useTransitionState:Ui,vModelCheckbox:rr,vModelDynamic:ta,vModelRadio:or,vModelSelect:Ql,vModelText:ms,vShow:Kl,version:kl,warn:gu,watch:Pt,watchEffect:Hf,watchPostEffect:kf,watchSyncEffect:Tl,withAsyncContext:Tf,withCtx:Gi,withDefaults:vf,withDirectives:Rc,withKeys:cd,withMemo:hu,withModifiers:ld,withScopeId:Lc},Symbol.toStringTag,{value:"Module"}));function Qr(e){return e!==null&&typeof e=="object"&&"constructor"in e&&e.constructor===Object}function lr(e,t){e===void 0&&(e={}),t===void 0&&(t={}),Object.keys(t).forEach(n=>{typeof e[n]>"u"?e[n]=t[n]:Qr(t[n])&&Qr(e[n])&&Object.keys(t[n]).length>0&&lr(e[n],t[n])})}const fa={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function nn(){const e=typeof document<"u"?document:{};return lr(e,fa),e}const pd={document:fa,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(e){return typeof setTimeout>"u"?(e(),null):setTimeout(e,0)},cancelAnimationFrame(e){typeof setTimeout>"u"||clearTimeout(e)}};function Ne(){const e=typeof window<"u"?window:{};return lr(e,pd),e}function hd(e){return e===void 0&&(e=""),e.trim().split(" ").filter(t=>!!t.trim())}function gd(e){const t=e;Object.keys(t).forEach(n=>{try{t[n]=null}catch{}try{delete t[n]}catch{}})}function Ai(e,t){return t===void 0&&(t=0),setTimeout(e,t)}function vs(){return Date.now()}function md(e){const t=Ne();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function vd(e,t){t===void 0&&(t="x");const n=Ne();let s,i,r;const o=md(e);return n.WebKitCSSMatrix?(i=o.transform||o.webkitTransform,i.split(",").length>6&&(i=i.split(", ").map(a=>a.replace(",",".")).join(", ")),r=new n.WebKitCSSMatrix(i==="none"?"":i)):(r=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=r.toString().split(",")),t==="x"&&(n.WebKitCSSMatrix?i=r.m41:s.length===16?i=parseFloat(s[12]):i=parseFloat(s[4])),t==="y"&&(n.WebKitCSSMatrix?i=r.m42:s.length===16?i=parseFloat(s[13]):i=parseFloat(s[5])),i||0}function qn(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"}function yd(e){return typeof window<"u"&&typeof window.HTMLElement<"u"?e instanceof HTMLElement:e&&(e.nodeType===1||e.nodeType===11)}function Ie(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const s=n<0||arguments.length<=n?void 0:arguments[n];if(s!=null&&!yd(s)){const i=Object.keys(Object(s)).filter(r=>t.indexOf(r)<0);for(let r=0,o=i.length;r<o;r+=1){const a=i[r],l=Object.getOwnPropertyDescriptor(s,a);l!==void 0&&l.enumerable&&(qn(e[a])&&qn(s[a])?s[a].__swiper__?e[a]=s[a]:Ie(e[a],s[a]):!qn(e[a])&&qn(s[a])?(e[a]={},s[a].__swiper__?e[a]=s[a]:Ie(e[a],s[a])):e[a]=s[a])}}}return e}function Yn(e,t,n){e.style.setProperty(t,n)}function ua(e){let{swiper:t,targetPosition:n,side:s}=e;const i=Ne(),r=-t.translate;let o=null,a;const l=t.params.speed;t.wrapperEl.style.scrollSnapType="none",i.cancelAnimationFrame(t.cssModeFrameID);const f=n>r?"next":"prev",c=(p,h)=>f==="next"&&p>=h||f==="prev"&&p<=h,u=()=>{a=new Date().getTime(),o===null&&(o=a);const p=Math.max(Math.min((a-o)/l,1),0),h=.5-Math.cos(p*Math.PI)/2;let v=r+h*(n-r);if(c(v,n)&&(v=n),t.wrapperEl.scrollTo({[s]:v}),c(v,n)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[s]:v})}),i.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=i.requestAnimationFrame(u)};u()}function st(e,t){return t===void 0&&(t=""),[...e.children].filter(n=>n.matches(t))}function ys(e){try{console.warn(e);return}catch{}}function Oi(e,t){t===void 0&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:hd(t)),n}function bd(e,t){const n=[];for(;e.previousElementSibling;){const s=e.previousElementSibling;t?s.matches(t)&&n.push(s):n.push(s),e=s}return n}function wd(e,t){const n=[];for(;e.nextElementSibling;){const s=e.nextElementSibling;t?s.matches(t)&&n.push(s):n.push(s),e=s}return n}function pt(e,t){return Ne().getComputedStyle(e,null).getPropertyValue(t)}function eo(e){let t=e,n;if(t){for(n=0;(t=t.previousSibling)!==null;)t.nodeType===1&&(n+=1);return n}}function Sd(e,t){const n=[];let s=e.parentElement;for(;s;)n.push(s),s=s.parentElement;return n}function to(e,t,n){const s=Ne();return e[t==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-left":"margin-bottom"))}let ti;function _d(){const e=Ne(),t=nn();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}function da(){return ti||(ti=_d()),ti}let ni;function Td(e){let{userAgent:t}=e===void 0?{}:e;const n=da(),s=Ne(),i=s.navigator.platform,r=t||s.navigator.userAgent,o={ios:!1,android:!1},a=s.screen.width,l=s.screen.height,f=r.match(/(Android);?[\s\/]+([\d.]+)?/);let c=r.match(/(iPad).*OS\s([\d_]+)/);const u=r.match(/(iPod)(.*OS\s([\d_]+))?/),p=!c&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h=i==="Win32";let v=i==="MacIntel";const b=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!c&&v&&n.touch&&b.indexOf(`${a}x${l}`)>=0&&(c=r.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),v=!1),f&&!h&&(o.os="android",o.android=!0),(c||p||u)&&(o.os="ios",o.ios=!0),o}function xd(e){return e===void 0&&(e={}),ni||(ni=Td(e)),ni}let si;function Ed(){const e=Ne();let t=!1;function n(){const s=e.navigator.userAgent.toLowerCase();return s.indexOf("safari")>=0&&s.indexOf("chrome")<0&&s.indexOf("android")<0}if(n()){const s=String(e.navigator.userAgent);if(s.includes("Version/")){const[i,r]=s.split("Version/")[1].split(" ")[0].split(".").map(o=>Number(o));t=i<16||i===16&&r<2}}return{isSafari:t||n(),needPerspectiveFix:t,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}function Cd(){return si||(si=Ed()),si}function Pd(e){let{swiper:t,on:n,emit:s}=e;const i=Ne();let r=null,o=null;const a=()=>{!t||t.destroyed||!t.initialized||(s("beforeResize"),s("resize"))},l=()=>{!t||t.destroyed||!t.initialized||(r=new ResizeObserver(u=>{o=i.requestAnimationFrame(()=>{const{width:p,height:h}=t;let v=p,b=h;u.forEach(I=>{let{contentBoxSize:T,contentRect:x,target:d}=I;d&&d!==t.el||(v=x?x.width:(T[0]||T).inlineSize,b=x?x.height:(T[0]||T).blockSize)}),(v!==p||b!==h)&&a()})}),r.observe(t.el))},f=()=>{o&&i.cancelAnimationFrame(o),r&&r.unobserve&&t.el&&(r.unobserve(t.el),r=null)},c=()=>{!t||t.destroyed||!t.initialized||s("orientationchange")};n("init",()=>{if(t.params.resizeObserver&&typeof i.ResizeObserver<"u"){l();return}i.addEventListener("resize",a),i.addEventListener("orientationchange",c)}),n("destroy",()=>{f(),i.removeEventListener("resize",a),i.removeEventListener("orientationchange",c)})}function Md(e){let{swiper:t,extendParams:n,on:s,emit:i}=e;const r=[],o=Ne(),a=function(c,u){u===void 0&&(u={});const p=o.MutationObserver||o.WebkitMutationObserver,h=new p(v=>{if(t.__preventObserver__)return;if(v.length===1){i("observerUpdate",v[0]);return}const b=function(){i("observerUpdate",v[0])};o.requestAnimationFrame?o.requestAnimationFrame(b):o.setTimeout(b,0)});h.observe(c,{attributes:typeof u.attributes>"u"?!0:u.attributes,childList:typeof u.childList>"u"?!0:u.childList,characterData:typeof u.characterData>"u"?!0:u.characterData}),r.push(h)},l=()=>{if(t.params.observer){if(t.params.observeParents){const c=Sd(t.hostEl);for(let u=0;u<c.length;u+=1)a(c[u])}a(t.hostEl,{childList:t.params.observeSlideChildren}),a(t.wrapperEl,{attributes:!1})}},f=()=>{r.forEach(c=>{c.disconnect()}),r.splice(0,r.length)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",l),s("destroy",f)}var Ad={on(e,t,n){const s=this;if(!s.eventsListeners||s.destroyed||typeof t!="function")return s;const i=n?"unshift":"push";return e.split(" ").forEach(r=>{s.eventsListeners[r]||(s.eventsListeners[r]=[]),s.eventsListeners[r][i](t)}),s},once(e,t,n){const s=this;if(!s.eventsListeners||s.destroyed||typeof t!="function")return s;function i(){s.off(e,i),i.__emitterProxy&&delete i.__emitterProxy;for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];t.apply(s,o)}return i.__emitterProxy=t,s.on(e,i,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;const s=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[s](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed||!n.eventsListeners||e.split(" ").forEach(s=>{typeof t>"u"?n.eventsListeners[s]=[]:n.eventsListeners[s]&&n.eventsListeners[s].forEach((i,r)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&n.eventsListeners[s].splice(r,1)})}),n},emit(){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let t,n,s;for(var i=arguments.length,r=new Array(i),o=0;o<i;o++)r[o]=arguments[o];return typeof r[0]=="string"||Array.isArray(r[0])?(t=r[0],n=r.slice(1,r.length),s=e):(t=r[0].events,n=r[0].data,s=r[0].context||e),n.unshift(s),(Array.isArray(t)?t:t.split(" ")).forEach(l=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(f=>{f.apply(s,[l,...n])}),e.eventsListeners&&e.eventsListeners[l]&&e.eventsListeners[l].forEach(f=>{f.apply(s,n)})}),e}};function Od(){const e=this;let t,n;const s=e.el;typeof e.params.width<"u"&&e.params.width!==null?t=e.params.width:t=s.clientWidth,typeof e.params.height<"u"&&e.params.height!==null?n=e.params.height:n=s.clientHeight,!(t===0&&e.isHorizontal()||n===0&&e.isVertical())&&(t=t-parseInt(pt(s,"padding-left")||0,10)-parseInt(pt(s,"padding-right")||0,10),n=n-parseInt(pt(s,"padding-top")||0,10)-parseInt(pt(s,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))}function Id(){const e=this;function t(w,M){return parseFloat(w.getPropertyValue(e.getDirectionLabel(M))||0)}const n=e.params,{wrapperEl:s,slidesEl:i,size:r,rtlTranslate:o,wrongRTL:a}=e,l=e.virtual&&n.virtual.enabled,f=l?e.virtual.slides.length:e.slides.length,c=st(i,`.${e.params.slideClass}, swiper-slide`),u=l?e.virtual.slides.length:c.length;let p=[];const h=[],v=[];let b=n.slidesOffsetBefore;typeof b=="function"&&(b=n.slidesOffsetBefore.call(e));let I=n.slidesOffsetAfter;typeof I=="function"&&(I=n.slidesOffsetAfter.call(e));const T=e.snapGrid.length,x=e.slidesGrid.length;let d=n.spaceBetween,g=-b,_=0,P=0;if(typeof r>"u")return;typeof d=="string"&&d.indexOf("%")>=0?d=parseFloat(d.replace("%",""))/100*r:typeof d=="string"&&(d=parseFloat(d)),e.virtualSize=-d,c.forEach(w=>{o?w.style.marginLeft="":w.style.marginRight="",w.style.marginBottom="",w.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(Yn(s,"--swiper-centered-offset-before",""),Yn(s,"--swiper-centered-offset-after",""));const B=n.grid&&n.grid.rows>1&&e.grid;B?e.grid.initSlides(c):e.grid&&e.grid.unsetSlides();let A;const S=n.slidesPerView==="auto"&&n.breakpoints&&Object.keys(n.breakpoints).filter(w=>typeof n.breakpoints[w].slidesPerView<"u").length>0;for(let w=0;w<u;w+=1){A=0;let M;if(c[w]&&(M=c[w]),B&&e.grid.updateSlide(w,M,c),!(c[w]&&pt(M,"display")==="none")){if(n.slidesPerView==="auto"){S&&(c[w].style[e.getDirectionLabel("width")]="");const C=getComputedStyle(M),L=M.style.transform,U=M.style.webkitTransform;if(L&&(M.style.transform="none"),U&&(M.style.webkitTransform="none"),n.roundLengths)A=e.isHorizontal()?to(M,"width"):to(M,"height");else{const Y=t(C,"width"),$=t(C,"padding-left"),X=t(C,"padding-right"),j=t(C,"margin-left"),ae=t(C,"margin-right"),_t=C.getPropertyValue("box-sizing");if(_t&&_t==="border-box")A=Y+j+ae;else{const{clientWidth:Xe,offsetWidth:De}=M;A=Y+$+X+j+ae+(De-Xe)}}L&&(M.style.transform=L),U&&(M.style.webkitTransform=U),n.roundLengths&&(A=Math.floor(A))}else A=(r-(n.slidesPerView-1)*d)/n.slidesPerView,n.roundLengths&&(A=Math.floor(A)),c[w]&&(c[w].style[e.getDirectionLabel("width")]=`${A}px`);c[w]&&(c[w].swiperSlideSize=A),v.push(A),n.centeredSlides?(g=g+A/2+_/2+d,_===0&&w!==0&&(g=g-r/2-d),w===0&&(g=g-r/2-d),Math.abs(g)<1/1e3&&(g=0),n.roundLengths&&(g=Math.floor(g)),P%n.slidesPerGroup===0&&p.push(g),h.push(g)):(n.roundLengths&&(g=Math.floor(g)),(P-Math.min(e.params.slidesPerGroupSkip,P))%e.params.slidesPerGroup===0&&p.push(g),h.push(g),g=g+A+d),e.virtualSize+=A+d,_=A,P+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+I,o&&a&&(n.effect==="slide"||n.effect==="coverflow")&&(s.style.width=`${e.virtualSize+d}px`),n.setWrapperSize&&(s.style[e.getDirectionLabel("width")]=`${e.virtualSize+d}px`),B&&e.grid.updateWrapperSize(A,p),!n.centeredSlides){const w=[];for(let M=0;M<p.length;M+=1){let C=p[M];n.roundLengths&&(C=Math.floor(C)),p[M]<=e.virtualSize-r&&w.push(C)}p=w,Math.floor(e.virtualSize-r)-Math.floor(p[p.length-1])>1&&p.push(e.virtualSize-r)}if(l&&n.loop){const w=v[0]+d;if(n.slidesPerGroup>1){const M=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),C=w*n.slidesPerGroup;for(let L=0;L<M;L+=1)p.push(p[p.length-1]+C)}for(let M=0;M<e.virtual.slidesBefore+e.virtual.slidesAfter;M+=1)n.slidesPerGroup===1&&p.push(p[p.length-1]+w),h.push(h[h.length-1]+w),e.virtualSize+=w}if(p.length===0&&(p=[0]),d!==0){const w=e.isHorizontal()&&o?"marginLeft":e.getDirectionLabel("marginRight");c.filter((M,C)=>!n.cssMode||n.loop?!0:C!==c.length-1).forEach(M=>{M.style[w]=`${d}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let w=0;v.forEach(C=>{w+=C+(d||0)}),w-=d;const M=w-r;p=p.map(C=>C<=0?-b:C>M?M+I:C)}if(n.centerInsufficientSlides){let w=0;if(v.forEach(M=>{w+=M+(d||0)}),w-=d,w<r){const M=(r-w)/2;p.forEach((C,L)=>{p[L]=C-M}),h.forEach((C,L)=>{h[L]=C+M})}}if(Object.assign(e,{slides:c,snapGrid:p,slidesGrid:h,slidesSizesGrid:v}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){Yn(s,"--swiper-centered-offset-before",`${-p[0]}px`),Yn(s,"--swiper-centered-offset-after",`${e.size/2-v[v.length-1]/2}px`);const w=-e.snapGrid[0],M=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(C=>C+w),e.slidesGrid=e.slidesGrid.map(C=>C+M)}if(u!==f&&e.emit("slidesLengthChange"),p.length!==T&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),h.length!==x&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),!l&&!n.cssMode&&(n.effect==="slide"||n.effect==="fade")){const w=`${n.containerModifierClass}backface-hidden`,M=e.el.classList.contains(w);u<=n.maxBackfaceHiddenSlides?M||e.el.classList.add(w):M&&e.el.classList.remove(w)}}function Ld(e){const t=this,n=[],s=t.virtual&&t.params.virtual.enabled;let i=0,r;typeof e=="number"?t.setTransition(e):e===!0&&t.setTransition(t.params.speed);const o=a=>s?t.slides[t.getSlideIndexByData(a)]:t.slides[a];if(t.params.slidesPerView!=="auto"&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(a=>{n.push(a)});else for(r=0;r<Math.ceil(t.params.slidesPerView);r+=1){const a=t.activeIndex+r;if(a>t.slides.length&&!s)break;n.push(o(a))}else n.push(o(t.activeIndex));for(r=0;r<n.length;r+=1)if(typeof n[r]<"u"){const a=n[r].offsetHeight;i=a>i?a:i}(i||i===0)&&(t.wrapperEl.style.height=`${i}px`)}function Rd(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let s=0;s<t.length;s+=1)t[s].swiperSlideOffset=(e.isHorizontal()?t[s].offsetLeft:t[s].offsetTop)-n-e.cssOverflowAdjustment()}function Nd(e){e===void 0&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:s,rtlTranslate:i,snapGrid:r}=t;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&t.updateSlidesOffset();let o=-e;i&&(o=e),s.forEach(l=>{l.classList.remove(n.slideVisibleClass,n.slideFullyVisibleClass)}),t.visibleSlidesIndexes=[],t.visibleSlides=[];let a=n.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*t.size:typeof a=="string"&&(a=parseFloat(a));for(let l=0;l<s.length;l+=1){const f=s[l];let c=f.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(c-=s[0].swiperSlideOffset);const u=(o+(n.centeredSlides?t.minTranslate():0)-c)/(f.swiperSlideSize+a),p=(o-r[0]+(n.centeredSlides?t.minTranslate():0)-c)/(f.swiperSlideSize+a),h=-(o-c),v=h+t.slidesSizesGrid[l],b=h>=0&&h<=t.size-t.slidesSizesGrid[l];(h>=0&&h<t.size-1||v>1&&v<=t.size||h<=0&&v>=t.size)&&(t.visibleSlides.push(f),t.visibleSlidesIndexes.push(l),s[l].classList.add(n.slideVisibleClass)),b&&s[l].classList.add(n.slideFullyVisibleClass),f.progress=i?-u:u,f.originalProgress=i?-p:p}}function Dd(e){const t=this;if(typeof e>"u"){const c=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*c||0}const n=t.params,s=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:r,isEnd:o,progressLoop:a}=t;const l=r,f=o;if(s===0)i=0,r=!0,o=!0;else{i=(e-t.minTranslate())/s;const c=Math.abs(e-t.minTranslate())<1,u=Math.abs(e-t.maxTranslate())<1;r=c||i<=0,o=u||i>=1,c&&(i=0),u&&(i=1)}if(n.loop){const c=t.getSlideIndexByData(0),u=t.getSlideIndexByData(t.slides.length-1),p=t.slidesGrid[c],h=t.slidesGrid[u],v=t.slidesGrid[t.slidesGrid.length-1],b=Math.abs(e);b>=p?a=(b-p)/v:a=(b+v-h)/v,a>1&&(a-=1)}Object.assign(t,{progress:i,progressLoop:a,isBeginning:r,isEnd:o}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),r&&!l&&t.emit("reachBeginning toEdge"),o&&!f&&t.emit("reachEnd toEdge"),(l&&!r||f&&!o)&&t.emit("fromEdge"),t.emit("progress",i)}function Bd(){const e=this,{slides:t,params:n,slidesEl:s,activeIndex:i}=e,r=e.virtual&&n.virtual.enabled,o=e.grid&&n.grid&&n.grid.rows>1,a=u=>st(s,`.${n.slideClass}${u}, swiper-slide${u}`)[0];t.forEach(u=>{u.classList.remove(n.slideActiveClass,n.slideNextClass,n.slidePrevClass)});let l,f,c;if(r)if(n.loop){let u=i-e.virtual.slidesBefore;u<0&&(u=e.virtual.slides.length+u),u>=e.virtual.slides.length&&(u-=e.virtual.slides.length),l=a(`[data-swiper-slide-index="${u}"]`)}else l=a(`[data-swiper-slide-index="${i}"]`);else o?(l=t.filter(u=>u.column===i)[0],c=t.filter(u=>u.column===i+1)[0],f=t.filter(u=>u.column===i-1)[0]):l=t[i];l&&(l.classList.add(n.slideActiveClass),o?(c&&c.classList.add(n.slideNextClass),f&&f.classList.add(n.slidePrevClass)):(c=wd(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!c&&(c=t[0]),c&&c.classList.add(n.slideNextClass),f=bd(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!f===0&&(f=t[t.length-1]),f&&f.classList.add(n.slidePrevClass))),e.emitSlidesClasses()}const es=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=()=>e.isElement?"swiper-slide":`.${e.params.slideClass}`,s=t.closest(n());if(s){let i=s.querySelector(`.${e.params.lazyPreloaderClass}`);!i&&e.isElement&&(s.shadowRoot?i=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(i=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),i&&i.remove())})),i&&i.remove()}},ii=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},Ii=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const s=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),i=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const o=i,a=[o-t];a.push(...Array.from({length:t}).map((l,f)=>o+s+f)),e.slides.forEach((l,f)=>{a.includes(l.column)&&ii(e,f)});return}const r=i+s-1;if(e.params.rewind||e.params.loop)for(let o=i-t;o<=r+t;o+=1){const a=(o%n+n)%n;(a<i||a>r)&&ii(e,a)}else for(let o=Math.max(i-t,0);o<=Math.min(r+t,n-1);o+=1)o!==i&&(o>r||o<i)&&ii(e,o)};function Fd(e){const{slidesGrid:t,params:n}=e,s=e.rtlTranslate?e.translate:-e.translate;let i;for(let r=0;r<t.length;r+=1)typeof t[r+1]<"u"?s>=t[r]&&s<t[r+1]-(t[r+1]-t[r])/2?i=r:s>=t[r]&&s<t[r+1]&&(i=r+1):s>=t[r]&&(i=r);return n.normalizeSlideIndex&&(i<0||typeof i>"u")&&(i=0),i}function Vd(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:s,params:i,activeIndex:r,realIndex:o,snapIndex:a}=t;let l=e,f;const c=h=>{let v=h-t.virtual.slidesBefore;return v<0&&(v=t.virtual.slides.length+v),v>=t.virtual.slides.length&&(v-=t.virtual.slides.length),v};if(typeof l>"u"&&(l=Fd(t)),s.indexOf(n)>=0)f=s.indexOf(n);else{const h=Math.min(i.slidesPerGroupSkip,l);f=h+Math.floor((l-h)/i.slidesPerGroup)}if(f>=s.length&&(f=s.length-1),l===r&&!t.params.loop){f!==a&&(t.snapIndex=f,t.emit("snapIndexChange"));return}if(l===r&&t.params.loop&&t.virtual&&t.params.virtual.enabled){t.realIndex=c(l);return}const u=t.grid&&i.grid&&i.grid.rows>1;let p;if(t.virtual&&i.virtual.enabled&&i.loop)p=c(l);else if(u){const h=t.slides.filter(b=>b.column===l)[0];let v=parseInt(h.getAttribute("data-swiper-slide-index"),10);Number.isNaN(v)&&(v=Math.max(t.slides.indexOf(h),0)),p=Math.floor(v/i.grid.rows)}else if(t.slides[l]){const h=t.slides[l].getAttribute("data-swiper-slide-index");h?p=parseInt(h,10):p=l}else p=l;Object.assign(t,{previousSnapIndex:a,snapIndex:f,previousRealIndex:o,realIndex:p,previousIndex:r,activeIndex:l}),t.initialized&&Ii(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(o!==p&&t.emit("realIndexChange"),t.emit("slideChange"))}function Hd(e,t){const n=this,s=n.params;let i=e.closest(`.${s.slideClass}, swiper-slide`);!i&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(a=>{!i&&a.matches&&a.matches(`.${s.slideClass}, swiper-slide`)&&(i=a)});let r=!1,o;if(i){for(let a=0;a<n.slides.length;a+=1)if(n.slides[a]===i){r=!0,o=a;break}}if(i&&r)n.clickedSlide=i,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(i.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=o;else{n.clickedSlide=void 0,n.clickedIndex=void 0;return}s.slideToClickedSlide&&n.clickedIndex!==void 0&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}var kd={updateSize:Od,updateSlides:Id,updateAutoHeight:Ld,updateSlidesOffset:Rd,updateSlidesProgress:Nd,updateProgress:Dd,updateSlidesClasses:Bd,updateActiveIndex:Vd,updateClickedSlide:Hd};function $d(e){e===void 0&&(e=this.isHorizontal()?"x":"y");const t=this,{params:n,rtlTranslate:s,translate:i,wrapperEl:r}=t;if(n.virtualTranslate)return s?-i:i;if(n.cssMode)return i;let o=vd(r,e);return o+=t.cssOverflowAdjustment(),s&&(o=-o),o||0}function zd(e,t){const n=this,{rtlTranslate:s,params:i,wrapperEl:r,progress:o}=n;let a=0,l=0;const f=0;n.isHorizontal()?a=s?-e:e:l=e,i.roundLengths&&(a=Math.floor(a),l=Math.floor(l)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?a:l,i.cssMode?r[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-a:-l:i.virtualTranslate||(n.isHorizontal()?a-=n.cssOverflowAdjustment():l-=n.cssOverflowAdjustment(),r.style.transform=`translate3d(${a}px, ${l}px, ${f}px)`);let c;const u=n.maxTranslate()-n.minTranslate();u===0?c=0:c=(e-n.minTranslate())/u,c!==o&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)}function jd(){return-this.snapGrid[0]}function Gd(){return-this.snapGrid[this.snapGrid.length-1]}function Ud(e,t,n,s,i){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),s===void 0&&(s=!0);const r=this,{params:o,wrapperEl:a}=r;if(r.animating&&o.preventInteractionOnTransition)return!1;const l=r.minTranslate(),f=r.maxTranslate();let c;if(s&&e>l?c=l:s&&e<f?c=f:c=e,r.updateProgress(c),o.cssMode){const u=r.isHorizontal();if(t===0)a[u?"scrollLeft":"scrollTop"]=-c;else{if(!r.support.smoothScroll)return ua({swiper:r,targetPosition:-c,side:u?"left":"top"}),!0;a.scrollTo({[u?"left":"top"]:-c,behavior:"smooth"})}return!0}return t===0?(r.setTransition(0),r.setTranslate(c),n&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(c),n&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(p){!r||r.destroyed||p.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,n&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}var Wd={getTranslate:$d,setTranslate:zd,minTranslate:jd,maxTranslate:Gd,translateTo:Ud};function Kd(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${e}ms`,n.wrapperEl.style.transitionDelay=e===0?"0ms":""),n.emit("setTransition",e,t)}function pa(e){let{swiper:t,runCallbacks:n,direction:s,step:i}=e;const{activeIndex:r,previousIndex:o}=t;let a=s;if(a||(r>o?a="next":r<o?a="prev":a="reset"),t.emit(`transition${i}`),n&&r!==o){if(a==="reset"){t.emit(`slideResetTransition${i}`);return}t.emit(`slideChangeTransition${i}`),a==="next"?t.emit(`slideNextTransition${i}`):t.emit(`slidePrevTransition${i}`)}}function qd(e,t){e===void 0&&(e=!0);const n=this,{params:s}=n;s.cssMode||(s.autoHeight&&n.updateAutoHeight(),pa({swiper:n,runCallbacks:e,direction:t,step:"Start"}))}function Yd(e,t){e===void 0&&(e=!0);const n=this,{params:s}=n;n.animating=!1,!s.cssMode&&(n.setTransition(0),pa({swiper:n,runCallbacks:e,direction:t,step:"End"}))}var Xd={setTransition:Kd,transitionStart:qd,transitionEnd:Yd};function Jd(e,t,n,s,i){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const r=this;let o=e;o<0&&(o=0);const{params:a,snapGrid:l,slidesGrid:f,previousIndex:c,activeIndex:u,rtlTranslate:p,wrapperEl:h,enabled:v}=r;if(r.animating&&a.preventInteractionOnTransition||!v&&!s&&!i)return!1;const b=Math.min(r.params.slidesPerGroupSkip,o);let I=b+Math.floor((o-b)/r.params.slidesPerGroup);I>=l.length&&(I=l.length-1);const T=-l[I];if(a.normalizeSlideIndex)for(let d=0;d<f.length;d+=1){const g=-Math.floor(T*100),_=Math.floor(f[d]*100),P=Math.floor(f[d+1]*100);typeof f[d+1]<"u"?g>=_&&g<P-(P-_)/2?o=d:g>=_&&g<P&&(o=d+1):g>=_&&(o=d)}if(r.initialized&&o!==u&&(!r.allowSlideNext&&(p?T>r.translate&&T>r.minTranslate():T<r.translate&&T<r.minTranslate())||!r.allowSlidePrev&&T>r.translate&&T>r.maxTranslate()&&(u||0)!==o))return!1;o!==(c||0)&&n&&r.emit("beforeSlideChangeStart"),r.updateProgress(T);let x;if(o>u?x="next":o<u?x="prev":x="reset",p&&-T===r.translate||!p&&T===r.translate)return r.updateActiveIndex(o),a.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),a.effect!=="slide"&&r.setTranslate(T),x!=="reset"&&(r.transitionStart(n,x),r.transitionEnd(n,x)),!1;if(a.cssMode){const d=r.isHorizontal(),g=p?T:-T;if(t===0){const _=r.virtual&&r.params.virtual.enabled;_&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),_&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{h[d?"scrollLeft":"scrollTop"]=g})):h[d?"scrollLeft":"scrollTop"]=g,_&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1})}else{if(!r.support.smoothScroll)return ua({swiper:r,targetPosition:g,side:d?"left":"top"}),!0;h.scrollTo({[d?"left":"top"]:g,behavior:"smooth"})}return!0}return r.setTransition(t),r.setTranslate(T),r.updateActiveIndex(o),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,s),r.transitionStart(n,x),t===0?r.transitionEnd(n,x):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(g){!r||r.destroyed||g.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(n,x))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0}function Zd(e,t,n,s){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const i=this,r=i.grid&&i.params.grid&&i.params.grid.rows>1;let o=e;if(i.params.loop)if(i.virtual&&i.params.virtual.enabled)o=o+i.virtual.slidesBefore;else{let a;if(r){const p=o*i.params.grid.rows;a=i.slides.filter(h=>h.getAttribute("data-swiper-slide-index")*1===p)[0].column}else a=i.getSlideIndexByData(o);const l=r?Math.ceil(i.slides.length/i.params.grid.rows):i.slides.length,{centeredSlides:f}=i.params;let c=i.params.slidesPerView;c==="auto"?c=i.slidesPerViewDynamic():(c=Math.ceil(parseFloat(i.params.slidesPerView,10)),f&&c%2===0&&(c=c+1));let u=l-a<c;if(f&&(u=u||a<Math.ceil(c/2)),u){const p=f?a<i.activeIndex?"prev":"next":a-i.activeIndex-1<i.params.slidesPerView?"next":"prev";i.loopFix({direction:p,slideTo:!0,activeSlideIndex:p==="next"?a+1:a-l+1,slideRealIndex:p==="next"?i.realIndex:void 0})}if(r){const p=o*i.params.grid.rows;o=i.slides.filter(h=>h.getAttribute("data-swiper-slide-index")*1===p)[0].column}else o=i.getSlideIndexByData(o)}return requestAnimationFrame(()=>{i.slideTo(o,t,n,s)}),i}function Qd(e,t,n){e===void 0&&(e=this.params.speed),t===void 0&&(t=!0);const s=this,{enabled:i,params:r,animating:o}=s;if(!i)return s;let a=r.slidesPerGroup;r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(a=Math.max(s.slidesPerViewDynamic("current",!0),1));const l=s.activeIndex<r.slidesPerGroupSkip?1:a,f=s.virtual&&r.virtual.enabled;if(r.loop){if(o&&!f&&r.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+l,e,t,n)}),!0}return r.rewind&&s.isEnd?s.slideTo(0,e,t,n):s.slideTo(s.activeIndex+l,e,t,n)}function ep(e,t,n){e===void 0&&(e=this.params.speed),t===void 0&&(t=!0);const s=this,{params:i,snapGrid:r,slidesGrid:o,rtlTranslate:a,enabled:l,animating:f}=s;if(!l)return s;const c=s.virtual&&i.virtual.enabled;if(i.loop){if(f&&!c&&i.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const u=a?s.translate:-s.translate;function p(T){return T<0?-Math.floor(Math.abs(T)):Math.floor(T)}const h=p(u),v=r.map(T=>p(T));let b=r[v.indexOf(h)-1];if(typeof b>"u"&&i.cssMode){let T;r.forEach((x,d)=>{h>=x&&(T=d)}),typeof T<"u"&&(b=r[T>0?T-1:T])}let I=0;if(typeof b<"u"&&(I=o.indexOf(b),I<0&&(I=s.activeIndex-1),i.slidesPerView==="auto"&&i.slidesPerGroup===1&&i.slidesPerGroupAuto&&(I=I-s.slidesPerViewDynamic("previous",!0)+1,I=Math.max(I,0))),i.rewind&&s.isBeginning){const T=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(T,e,t,n)}else if(i.loop&&s.activeIndex===0&&i.cssMode)return requestAnimationFrame(()=>{s.slideTo(I,e,t,n)}),!0;return s.slideTo(I,e,t,n)}function tp(e,t,n){e===void 0&&(e=this.params.speed),t===void 0&&(t=!0);const s=this;return s.slideTo(s.activeIndex,e,t,n)}function np(e,t,n,s){e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),s===void 0&&(s=.5);const i=this;let r=i.activeIndex;const o=Math.min(i.params.slidesPerGroupSkip,r),a=o+Math.floor((r-o)/i.params.slidesPerGroup),l=i.rtlTranslate?i.translate:-i.translate;if(l>=i.snapGrid[a]){const f=i.snapGrid[a],c=i.snapGrid[a+1];l-f>(c-f)*s&&(r+=i.params.slidesPerGroup)}else{const f=i.snapGrid[a-1],c=i.snapGrid[a];l-f<=(c-f)*s&&(r-=i.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,i.slidesGrid.length-1),i.slideTo(r,e,t,n)}function sp(){const e=this,{params:t,slidesEl:n}=e,s=t.slidesPerView==="auto"?e.slidesPerViewDynamic():t.slidesPerView;let i=e.clickedIndex,r;const o=e.isElement?"swiper-slide":`.${t.slideClass}`;if(t.loop){if(e.animating)return;r=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?i<e.loopedSlides-s/2||i>e.slides.length-e.loopedSlides+s/2?(e.loopFix(),i=e.getSlideIndex(st(n,`${o}[data-swiper-slide-index="${r}"]`)[0]),Ai(()=>{e.slideTo(i)})):e.slideTo(i):i>e.slides.length-s?(e.loopFix(),i=e.getSlideIndex(st(n,`${o}[data-swiper-slide-index="${r}"]`)[0]),Ai(()=>{e.slideTo(i)})):e.slideTo(i)}else e.slideTo(i)}var ip={slideTo:Jd,slideToLoop:Zd,slideNext:Qd,slidePrev:ep,slideReset:tp,slideToClosest:np,slideToClickedSlide:sp};function rp(e){const t=this,{params:n,slidesEl:s}=t;if(!n.loop||t.virtual&&t.params.virtual.enabled)return;const i=()=>{st(s,`.${n.slideClass}, swiper-slide`).forEach((u,p)=>{u.setAttribute("data-swiper-slide-index",p)})},r=t.grid&&n.grid&&n.grid.rows>1,o=n.slidesPerGroup*(r?n.grid.rows:1),a=t.slides.length%o!==0,l=r&&t.slides.length%n.grid.rows!==0,f=c=>{for(let u=0;u<c;u+=1){const p=t.isElement?Oi("swiper-slide",[n.slideBlankClass]):Oi("div",[n.slideClass,n.slideBlankClass]);t.slidesEl.append(p)}};if(a){if(n.loopAddBlankSlides){const c=o-t.slides.length%o;f(c),t.recalcSlides(),t.updateSlides()}else ys("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");i()}else if(l){if(n.loopAddBlankSlides){const c=n.grid.rows-t.slides.length%n.grid.rows;f(c),t.recalcSlides(),t.updateSlides()}else ys("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");i()}else i();t.loopFix({slideRealIndex:e,direction:n.centeredSlides?void 0:"next"})}function op(e){let{slideRealIndex:t,slideTo:n=!0,direction:s,setTranslate:i,activeSlideIndex:r,byController:o,byMousewheel:a}=e===void 0?{}:e;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:f,allowSlidePrev:c,allowSlideNext:u,slidesEl:p,params:h}=l,{centeredSlides:v}=h;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&h.virtual.enabled){n&&(!h.centeredSlides&&l.snapIndex===0?l.slideTo(l.virtual.slides.length,0,!1,!0):h.centeredSlides&&l.snapIndex<h.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0)),l.allowSlidePrev=c,l.allowSlideNext=u,l.emit("loopFix");return}let b=h.slidesPerView;b==="auto"?b=l.slidesPerViewDynamic():(b=Math.ceil(parseFloat(h.slidesPerView,10)),v&&b%2===0&&(b=b+1));const I=h.slidesPerGroupAuto?b:h.slidesPerGroup;let T=I;T%I!==0&&(T+=I-T%I),T+=h.loopAdditionalSlides,l.loopedSlides=T;const x=l.grid&&h.grid&&h.grid.rows>1;f.length<b+T?ys("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):x&&h.grid.fill==="row"&&ys("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const d=[],g=[];let _=l.activeIndex;typeof r>"u"?r=l.getSlideIndex(f.filter(L=>L.classList.contains(h.slideActiveClass))[0]):_=r;const P=s==="next"||!s,B=s==="prev"||!s;let A=0,S=0;const w=x?Math.ceil(f.length/h.grid.rows):f.length,C=(x?f[r].column:r)+(v&&typeof i>"u"?-b/2+.5:0);if(C<T){A=Math.max(T-C,I);for(let L=0;L<T-C;L+=1){const U=L-Math.floor(L/w)*w;if(x){const Y=w-U-1;for(let $=f.length-1;$>=0;$-=1)f[$].column===Y&&d.push($)}else d.push(w-U-1)}}else if(C+b>w-T){S=Math.max(C-(w-T*2),I);for(let L=0;L<S;L+=1){const U=L-Math.floor(L/w)*w;x?f.forEach((Y,$)=>{Y.column===U&&g.push($)}):g.push(U)}}if(l.__preventObserver__=!0,requestAnimationFrame(()=>{l.__preventObserver__=!1}),B&&d.forEach(L=>{f[L].swiperLoopMoveDOM=!0,p.prepend(f[L]),f[L].swiperLoopMoveDOM=!1}),P&&g.forEach(L=>{f[L].swiperLoopMoveDOM=!0,p.append(f[L]),f[L].swiperLoopMoveDOM=!1}),l.recalcSlides(),h.slidesPerView==="auto"?l.updateSlides():x&&(d.length>0&&B||g.length>0&&P)&&l.slides.forEach((L,U)=>{l.grid.updateSlide(U,L,l.slides)}),h.watchSlidesProgress&&l.updateSlidesOffset(),n){if(d.length>0&&B){if(typeof t>"u"){const L=l.slidesGrid[_],Y=l.slidesGrid[_+A]-L;a?l.setTranslate(l.translate-Y):(l.slideTo(_+A,0,!1,!0),i&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-Y,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-Y))}else if(i){const L=x?d.length/h.grid.rows:d.length;l.slideTo(l.activeIndex+L,0,!1,!0),l.touchEventsData.currentTranslate=l.translate}}else if(g.length>0&&P)if(typeof t>"u"){const L=l.slidesGrid[_],Y=l.slidesGrid[_-S]-L;a?l.setTranslate(l.translate-Y):(l.slideTo(_-S,0,!1,!0),i&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-Y,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-Y))}else{const L=x?g.length/h.grid.rows:g.length;l.slideTo(l.activeIndex-L,0,!1,!0)}}if(l.allowSlidePrev=c,l.allowSlideNext=u,l.controller&&l.controller.control&&!o){const L={slideRealIndex:t,direction:s,setTranslate:i,activeSlideIndex:r,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach(U=>{!U.destroyed&&U.params.loop&&U.loopFix({...L,slideTo:U.params.slidesPerView===h.slidesPerView?n:!1})}):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...L,slideTo:l.controller.control.params.slidesPerView===h.slidesPerView?n:!1})}l.emit("loopFix")}function lp(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const s=[];e.slides.forEach(i=>{const r=typeof i.swiperSlideIndex>"u"?i.getAttribute("data-swiper-slide-index")*1:i.swiperSlideIndex;s[r]=i}),e.slides.forEach(i=>{i.removeAttribute("data-swiper-slide-index")}),s.forEach(i=>{n.append(i)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}var ap={loopCreate:rp,loopFix:op,loopDestroy:lp};function cp(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n=t.params.touchEventsTarget==="container"?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})}function fp(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e[e.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}var up={setGrabCursor:cp,unsetGrabCursor:fp};function dp(e,t){t===void 0&&(t=this);function n(s){if(!s||s===nn()||s===Ne())return null;s.assignedSlot&&(s=s.assignedSlot);const i=s.closest(e);return!i&&!s.getRootNode?null:i||n(s.getRootNode().host)}return n(t)}function no(e,t,n){const s=Ne(),{params:i}=e,r=i.edgeSwipeDetection,o=i.edgeSwipeThreshold;return r&&(n<=o||n>=s.innerWidth-o)?r==="prevent"?(t.preventDefault(),!0):!1:!0}function pp(e){const t=this,n=nn();let s=e;s.originalEvent&&(s=s.originalEvent);const i=t.touchEventsData;if(s.type==="pointerdown"){if(i.pointerId!==null&&i.pointerId!==s.pointerId)return;i.pointerId=s.pointerId}else s.type==="touchstart"&&s.targetTouches.length===1&&(i.touchId=s.targetTouches[0].identifier);if(s.type==="touchstart"){no(t,s,s.targetTouches[0].pageX);return}const{params:r,touches:o,enabled:a}=t;if(!a||!r.simulateTouch&&s.pointerType==="mouse"||t.animating&&r.preventInteractionOnTransition)return;!t.animating&&r.cssMode&&r.loop&&t.loopFix();let l=s.target;if(r.touchEventsTarget==="wrapper"&&!t.wrapperEl.contains(l)||"which"in s&&s.which===3||"button"in s&&s.button>0||i.isTouched&&i.isMoved)return;const f=!!r.noSwipingClass&&r.noSwipingClass!=="",c=s.composedPath?s.composedPath():s.path;f&&s.target&&s.target.shadowRoot&&c&&(l=c[0]);const u=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,p=!!(s.target&&s.target.shadowRoot);if(r.noSwiping&&(p?dp(u,l):l.closest(u))){t.allowClick=!0;return}if(r.swipeHandler&&!l.closest(r.swipeHandler))return;o.currentX=s.pageX,o.currentY=s.pageY;const h=o.currentX,v=o.currentY;if(!no(t,s,h))return;Object.assign(i,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=h,o.startY=v,i.touchStartTime=vs(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(i.allowThresholdMove=!1);let b=!0;l.matches(i.focusableElements)&&(b=!1,l.nodeName==="SELECT"&&(i.isTouched=!1)),n.activeElement&&n.activeElement.matches(i.focusableElements)&&n.activeElement!==l&&n.activeElement.blur();const I=b&&t.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||I)&&!l.isContentEditable&&s.preventDefault(),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.animating&&!r.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",s)}function hp(e){const t=nn(),n=this,s=n.touchEventsData,{params:i,touches:r,rtlTranslate:o,enabled:a}=n;if(!a||!i.simulateTouch&&e.pointerType==="mouse")return;let l=e;if(l.originalEvent&&(l=l.originalEvent),l.type==="pointermove"&&(s.touchId!==null||l.pointerId!==s.pointerId))return;let f;if(l.type==="touchmove"){if(f=[...l.changedTouches].filter(P=>P.identifier===s.touchId)[0],!f||f.identifier!==s.touchId)return}else f=l;if(!s.isTouched){s.startMoving&&s.isScrolling&&n.emit("touchMoveOpposite",l);return}const c=f.pageX,u=f.pageY;if(l.preventedByNestedSwiper){r.startX=c,r.startY=u;return}if(!n.allowTouchMove){l.target.matches(s.focusableElements)||(n.allowClick=!1),s.isTouched&&(Object.assign(r,{startX:c,startY:u,currentX:c,currentY:u}),s.touchStartTime=vs());return}if(i.touchReleaseOnEdges&&!i.loop){if(n.isVertical()){if(u<r.startY&&n.translate<=n.maxTranslate()||u>r.startY&&n.translate>=n.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else if(c<r.startX&&n.translate<=n.maxTranslate()||c>r.startX&&n.translate>=n.minTranslate())return}if(t.activeElement&&l.target===t.activeElement&&l.target.matches(s.focusableElements)){s.isMoved=!0,n.allowClick=!1;return}s.allowTouchCallbacks&&n.emit("touchMove",l),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=c,r.currentY=u;const p=r.currentX-r.startX,h=r.currentY-r.startY;if(n.params.threshold&&Math.sqrt(p**2+h**2)<n.params.threshold)return;if(typeof s.isScrolling>"u"){let P;n.isHorizontal()&&r.currentY===r.startY||n.isVertical()&&r.currentX===r.startX?s.isScrolling=!1:p*p+h*h>=25&&(P=Math.atan2(Math.abs(h),Math.abs(p))*180/Math.PI,s.isScrolling=n.isHorizontal()?P>i.touchAngle:90-P>i.touchAngle)}if(s.isScrolling&&n.emit("touchMoveOpposite",l),typeof s.startMoving>"u"&&(r.currentX!==r.startX||r.currentY!==r.startY)&&(s.startMoving=!0),s.isScrolling){s.isTouched=!1;return}if(!s.startMoving)return;n.allowClick=!1,!i.cssMode&&l.cancelable&&l.preventDefault(),i.touchMoveStopPropagation&&!i.nested&&l.stopPropagation();let v=n.isHorizontal()?p:h,b=n.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;i.oneWayMovement&&(v=Math.abs(v)*(o?1:-1),b=Math.abs(b)*(o?1:-1)),r.diff=v,v*=i.touchRatio,o&&(v=-v,b=-b);const I=n.touchesDirection;n.swipeDirection=v>0?"prev":"next",n.touchesDirection=b>0?"prev":"next";const T=n.params.loop&&!i.cssMode,x=n.touchesDirection==="next"&&n.allowSlideNext||n.touchesDirection==="prev"&&n.allowSlidePrev;if(!s.isMoved){if(T&&x&&n.loopFix({direction:n.swipeDirection}),s.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const P=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});n.wrapperEl.dispatchEvent(P)}s.allowMomentumBounce=!1,i.grabCursor&&(n.allowSlideNext===!0||n.allowSlidePrev===!0)&&n.setGrabCursor(!0),n.emit("sliderFirstMove",l)}let d;if(new Date().getTime(),s.isMoved&&s.allowThresholdMove&&I!==n.touchesDirection&&T&&x&&Math.abs(v)>=1){Object.assign(r,{startX:c,startY:u,currentX:c,currentY:u,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}n.emit("sliderMove",l),s.isMoved=!0,s.currentTranslate=v+s.startTranslate;let g=!0,_=i.resistanceRatio;if(i.touchReleaseOnEdges&&(_=0),v>0?(T&&x&&!d&&s.allowThresholdMove&&s.currentTranslate>(i.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>n.minTranslate()&&(g=!1,i.resistance&&(s.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+s.startTranslate+v)**_))):v<0&&(T&&x&&!d&&s.allowThresholdMove&&s.currentTranslate<(i.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]:n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-(i.slidesPerView==="auto"?n.slidesPerViewDynamic():Math.ceil(parseFloat(i.slidesPerView,10)))}),s.currentTranslate<n.maxTranslate()&&(g=!1,i.resistance&&(s.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-s.startTranslate-v)**_))),g&&(l.preventedByNestedSwiper=!0),!n.allowSlideNext&&n.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!n.allowSlidePrev&&n.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!n.allowSlidePrev&&!n.allowSlideNext&&(s.currentTranslate=s.startTranslate),i.threshold>0)if(Math.abs(v)>i.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,s.currentTranslate=s.startTranslate,r.diff=n.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY;return}}else{s.currentTranslate=s.startTranslate;return}!i.followFinger||i.cssMode||((i.freeMode&&i.freeMode.enabled&&n.freeMode||i.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),i.freeMode&&i.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(s.currentTranslate),n.setTranslate(s.currentTranslate))}function gp(e){const t=this,n=t.touchEventsData;let s=e;s.originalEvent&&(s=s.originalEvent);let i;if(s.type==="touchend"||s.type==="touchcancel"){if(i=[...s.changedTouches].filter(g=>g.identifier===n.touchId)[0],!i||i.identifier!==n.touchId)return}else{if(n.touchId!==null||s.pointerId!==n.pointerId)return;i=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(t.browser.isSafari||t.browser.isWebView)))return;n.pointerId=null,n.touchId=null;const{params:o,touches:a,rtlTranslate:l,slidesGrid:f,enabled:c}=t;if(!c||!o.simulateTouch&&s.pointerType==="mouse")return;if(n.allowTouchCallbacks&&t.emit("touchEnd",s),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&o.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}o.grabCursor&&n.isMoved&&n.isTouched&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!1);const u=vs(),p=u-n.touchStartTime;if(t.allowClick){const g=s.path||s.composedPath&&s.composedPath();t.updateClickedSlide(g&&g[0]||s.target,g),t.emit("tap click",s),p<300&&u-n.lastClickTime<300&&t.emit("doubleTap doubleClick",s)}if(n.lastClickTime=vs(),Ai(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||a.diff===0&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;let h;if(o.followFinger?h=l?t.translate:-t.translate:h=-n.currentTranslate,o.cssMode)return;if(o.freeMode&&o.freeMode.enabled){t.freeMode.onTouchEnd({currentPos:h});return}let v=0,b=t.slidesSizesGrid[0];for(let g=0;g<f.length;g+=g<o.slidesPerGroupSkip?1:o.slidesPerGroup){const _=g<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;typeof f[g+_]<"u"?h>=f[g]&&h<f[g+_]&&(v=g,b=f[g+_]-f[g]):h>=f[g]&&(v=g,b=f[f.length-1]-f[f.length-2])}let I=null,T=null;o.rewind&&(t.isBeginning?T=o.virtual&&o.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(I=0));const x=(h-f[v])/b,d=v<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;if(p>o.longSwipesMs){if(!o.longSwipes){t.slideTo(t.activeIndex);return}t.swipeDirection==="next"&&(x>=o.longSwipesRatio?t.slideTo(o.rewind&&t.isEnd?I:v+d):t.slideTo(v)),t.swipeDirection==="prev"&&(x>1-o.longSwipesRatio?t.slideTo(v+d):T!==null&&x<0&&Math.abs(x)>o.longSwipesRatio?t.slideTo(T):t.slideTo(v))}else{if(!o.shortSwipes){t.slideTo(t.activeIndex);return}t.navigation&&(s.target===t.navigation.nextEl||s.target===t.navigation.prevEl)?s.target===t.navigation.nextEl?t.slideTo(v+d):t.slideTo(v):(t.swipeDirection==="next"&&t.slideTo(I!==null?I:v+d),t.swipeDirection==="prev"&&t.slideTo(T!==null?T:v))}}function so(){const e=this,{params:t,el:n}=e;if(n&&n.offsetWidth===0)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:i,snapGrid:r}=e,o=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const a=o&&t.loop;(t.slidesPerView==="auto"||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides&&!a?e.slideTo(e.slides.length-1,0,!1,!0):e.params.loop&&!o?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=i,e.allowSlideNext=s,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}function mp(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function vp(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:s}=e;if(!s)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,e.translate===0&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let i;const r=e.maxTranslate()-e.minTranslate();r===0?i=0:i=(e.translate-e.minTranslate())/r,i!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function yp(e){const t=this;es(t,e.target),!(t.params.cssMode||t.params.slidesPerView!=="auto"&&!t.params.autoHeight)&&t.update()}function bp(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const ha=(e,t)=>{const n=nn(),{params:s,el:i,wrapperEl:r,device:o}=e,a=!!s.nested,l=t==="on"?"addEventListener":"removeEventListener",f=t;n[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:a}),i[l]("touchstart",e.onTouchStart,{passive:!1}),i[l]("pointerdown",e.onTouchStart,{passive:!1}),n[l]("touchmove",e.onTouchMove,{passive:!1,capture:a}),n[l]("pointermove",e.onTouchMove,{passive:!1,capture:a}),n[l]("touchend",e.onTouchEnd,{passive:!0}),n[l]("pointerup",e.onTouchEnd,{passive:!0}),n[l]("pointercancel",e.onTouchEnd,{passive:!0}),n[l]("touchcancel",e.onTouchEnd,{passive:!0}),n[l]("pointerout",e.onTouchEnd,{passive:!0}),n[l]("pointerleave",e.onTouchEnd,{passive:!0}),n[l]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&i[l]("click",e.onClick,!0),s.cssMode&&r[l]("scroll",e.onScroll),s.updateOnWindowResize?e[f](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",so,!0):e[f]("observerUpdate",so,!0),i[l]("load",e.onLoad,{capture:!0})};function wp(){const e=this,{params:t}=e;e.onTouchStart=pp.bind(e),e.onTouchMove=hp.bind(e),e.onTouchEnd=gp.bind(e),e.onDocumentTouchStart=bp.bind(e),t.cssMode&&(e.onScroll=vp.bind(e)),e.onClick=mp.bind(e),e.onLoad=yp.bind(e),ha(e,"on")}function Sp(){ha(this,"off")}var _p={attachEvents:wp,detachEvents:Sp};const io=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function Tp(){const e=this,{realIndex:t,initialized:n,params:s,el:i}=e,r=s.breakpoints;if(!r||r&&Object.keys(r).length===0)return;const o=e.getBreakpoint(r,e.params.breakpointsBase,e.el);if(!o||e.currentBreakpoint===o)return;const l=(o in r?r[o]:void 0)||e.originalParams,f=io(e,s),c=io(e,l),u=s.enabled;f&&!c?(i.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!f&&c&&(i.classList.add(`${s.containerModifierClass}grid`),(l.grid.fill&&l.grid.fill==="column"||!l.grid.fill&&s.grid.fill==="column")&&i.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(T=>{if(typeof l[T]>"u")return;const x=s[T]&&s[T].enabled,d=l[T]&&l[T].enabled;x&&!d&&e[T].disable(),!x&&d&&e[T].enable()});const p=l.direction&&l.direction!==s.direction,h=s.loop&&(l.slidesPerView!==s.slidesPerView||p),v=s.loop;p&&n&&e.changeDirection(),Ie(e.params,l);const b=e.params.enabled,I=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),u&&!b?e.disable():!u&&b&&e.enable(),e.currentBreakpoint=o,e.emit("_beforeBreakpoint",l),n&&(h?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!v&&I?(e.loopCreate(t),e.updateSlides()):v&&!I&&e.loopDestroy()),e.emit("breakpoint",l)}function xp(e,t,n){if(t===void 0&&(t="window"),!e||t==="container"&&!n)return;let s=!1;const i=Ne(),r=t==="window"?i.innerHeight:n.clientHeight,o=Object.keys(e).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const l=parseFloat(a.substr(1));return{value:r*l,point:a}}return{value:a,point:a}});o.sort((a,l)=>parseInt(a.value,10)-parseInt(l.value,10));for(let a=0;a<o.length;a+=1){const{point:l,value:f}=o[a];t==="window"?i.matchMedia(`(min-width: ${f}px)`).matches&&(s=l):f<=n.clientWidth&&(s=l)}return s||"max"}var Ep={setBreakpoint:Tp,getBreakpoint:xp};function Cp(e,t){const n=[];return e.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(i=>{s[i]&&n.push(t+i)}):typeof s=="string"&&n.push(t+s)}),n}function Pp(){const e=this,{classNames:t,params:n,rtl:s,el:i,device:r}=e,o=Cp(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:s},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&n.grid.fill==="column"},{android:r.android},{ios:r.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...o),i.classList.add(...t),e.emitContainerClasses()}function Mp(){const e=this,{el:t,classNames:n}=e;t.classList.remove(...n),e.emitContainerClasses()}var Ap={addClasses:Pp,removeClasses:Mp};function Op(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:s}=n;if(s){const i=e.slides.length-1,r=e.slidesGrid[i]+e.slidesSizesGrid[i]+s*2;e.isLocked=e.size>r}else e.isLocked=e.snapGrid.length===1;n.allowSlideNext===!0&&(e.allowSlideNext=!e.isLocked),n.allowSlidePrev===!0&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var Ip={checkOverflow:Op},Li={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Lp(e,t){return function(s){s===void 0&&(s={});const i=Object.keys(s)[0],r=s[i];if(typeof r!="object"||r===null){Ie(t,s);return}if(e[i]===!0&&(e[i]={enabled:!0}),i==="navigation"&&e[i]&&e[i].enabled&&!e[i].prevEl&&!e[i].nextEl&&(e[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&e[i]&&e[i].enabled&&!e[i].el&&(e[i].auto=!0),!(i in e&&"enabled"in r)){Ie(t,s);return}typeof e[i]=="object"&&!("enabled"in e[i])&&(e[i].enabled=!0),e[i]||(e[i]={enabled:!1}),Ie(t,s)}}const ri={eventsEmitter:Ad,update:kd,translate:Wd,transition:Xd,slide:ip,loop:ap,grabCursor:up,events:_p,breakpoints:Ep,checkOverflow:Ip,classes:Ap},oi={};let ar=class Ze{constructor(){let t,n;for(var s=arguments.length,i=new Array(s),r=0;r<s;r++)i[r]=arguments[r];i.length===1&&i[0].constructor&&Object.prototype.toString.call(i[0]).slice(8,-1)==="Object"?n=i[0]:[t,n]=i,n||(n={}),n=Ie({},n),t&&!n.el&&(n.el=t);const o=nn();if(n.el&&typeof n.el=="string"&&o.querySelectorAll(n.el).length>1){const c=[];return o.querySelectorAll(n.el).forEach(u=>{const p=Ie({},n,{el:u});c.push(new Ze(p))}),c}const a=this;a.__swiper__=!0,a.support=da(),a.device=xd({userAgent:n.userAgent}),a.browser=Cd(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],n.modules&&Array.isArray(n.modules)&&a.modules.push(...n.modules);const l={};a.modules.forEach(c=>{c({params:n,swiper:a,extendParams:Lp(n,l),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const f=Ie({},Li,l);return a.params=Ie({},f,oi,n),a.originalParams=Ie({},a.params),a.passedParams=Ie({},n),a.params&&a.params.on&&Object.keys(a.params.on).forEach(c=>{a.on(c,a.params.on[c])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(t){return this.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}getSlideIndex(t){const{slidesEl:n,params:s}=this,i=st(n,`.${s.slideClass}, swiper-slide`),r=eo(i[0]);return eo(t)-r}getSlideIndexByData(t){return this.getSlideIndex(this.slides.filter(n=>n.getAttribute("data-swiper-slide-index")*1===t)[0])}recalcSlides(){const t=this,{slidesEl:n,params:s}=t;t.slides=st(n,`.${s.slideClass}, swiper-slide`)}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,n){const s=this;t=Math.min(Math.max(t,0),1);const i=s.minTranslate(),o=(s.maxTranslate()-i)*t+i;s.translateTo(o,typeof n>"u"?0:n),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=t.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(t.params.containerModifierClass)===0);t.emit("_containerClasses",n.join(" "))}getSlideClasses(t){const n=this;return n.destroyed?"":t.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(n.params.slideClass)===0).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=[];t.slides.forEach(s=>{const i=t.getSlideClasses(s);n.push({slideEl:s,classNames:i}),t.emit("_slideClass",s,i)}),t.emit("_slideClasses",n)}slidesPerViewDynamic(t,n){t===void 0&&(t="current"),n===void 0&&(n=!1);const s=this,{params:i,slides:r,slidesGrid:o,slidesSizesGrid:a,size:l,activeIndex:f}=s;let c=1;if(typeof i.slidesPerView=="number")return i.slidesPerView;if(i.centeredSlides){let u=r[f]?r[f].swiperSlideSize:0,p;for(let h=f+1;h<r.length;h+=1)r[h]&&!p&&(u+=r[h].swiperSlideSize,c+=1,u>l&&(p=!0));for(let h=f-1;h>=0;h-=1)r[h]&&!p&&(u+=r[h].swiperSlideSize,c+=1,u>l&&(p=!0))}else if(t==="current")for(let u=f+1;u<r.length;u+=1)(n?o[u]+a[u]-o[f]<l:o[u]-o[f]<l)&&(c+=1);else for(let u=f-1;u>=0;u-=1)o[f]-o[u]<l&&(c+=1);return c}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:n,params:s}=t;s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(o=>{o.complete&&es(t,o)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses();function i(){const o=t.rtlTranslate?t.translate*-1:t.translate,a=Math.min(Math.max(o,t.maxTranslate()),t.minTranslate());t.setTranslate(a),t.updateActiveIndex(),t.updateSlidesClasses()}let r;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)i(),s.autoHeight&&t.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){const o=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;r=t.slideTo(o.length-1,0,!1,!0)}else r=t.slideTo(t.activeIndex,0,!1,!0);r||i()}s.watchOverflow&&n!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,n){n===void 0&&(n=!0);const s=this,i=s.params.direction;return t||(t=i==="horizontal"?"vertical":"horizontal"),t===i||t!=="horizontal"&&t!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${i}`),s.el.classList.add(`${s.params.containerModifierClass}${t}`),s.emitContainerClasses(),s.params.direction=t,s.slides.forEach(r=>{t==="vertical"?r.style.width="":r.style.height=""}),s.emit("changeDirection"),n&&s.update()),s}changeLanguageDirection(t){const n=this;n.rtl&&t==="rtl"||!n.rtl&&t==="ltr"||(n.rtl=t==="rtl",n.rtlTranslate=n.params.direction==="horizontal"&&n.rtl,n.rtl?(n.el.classList.add(`${n.params.containerModifierClass}rtl`),n.el.dir="rtl"):(n.el.classList.remove(`${n.params.containerModifierClass}rtl`),n.el.dir="ltr"),n.update())}mount(t){const n=this;if(n.mounted)return!0;let s=t||n.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=n,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName==="SWIPER-CONTAINER"&&(n.isElement=!0);const i=()=>`.${(n.params.wrapperClass||"").trim().split(" ").join(".")}`;let o=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(i()):st(s,i())[0];return!o&&n.params.createElements&&(o=Oi("div",n.params.wrapperClass),s.append(o),st(s,`.${n.params.slideClass}`).forEach(a=>{o.append(a)})),Object.assign(n,{el:s,wrapperEl:o,slidesEl:n.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:o,hostEl:n.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||pt(s,"direction")==="rtl",rtlTranslate:n.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||pt(s,"direction")==="rtl"),wrongRTL:pt(o,"display")==="-webkit-box"}),!0}init(t){const n=this;if(n.initialized||n.mount(t)===!1)return n;n.emit("beforeInit"),n.params.breakpoints&&n.setBreakpoint(),n.addClasses(),n.updateSize(),n.updateSlides(),n.params.watchOverflow&&n.checkOverflow(),n.params.grabCursor&&n.enabled&&n.setGrabCursor(),n.params.loop&&n.virtual&&n.params.virtual.enabled?n.slideTo(n.params.initialSlide+n.virtual.slidesBefore,0,n.params.runCallbacksOnInit,!1,!0):n.slideTo(n.params.initialSlide,0,n.params.runCallbacksOnInit,!1,!0),n.params.loop&&n.loopCreate(),n.attachEvents();const i=[...n.el.querySelectorAll('[loading="lazy"]')];return n.isElement&&i.push(...n.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(r=>{r.complete?es(n,r):r.addEventListener("load",o=>{es(n,o.target)})}),Ii(n),n.initialized=!0,Ii(n),n.emit("init"),n.emit("afterInit"),n}destroy(t,n){t===void 0&&(t=!0),n===void 0&&(n=!0);const s=this,{params:i,el:r,wrapperEl:o,slides:a}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),i.loop&&s.loopDestroy(),n&&(s.removeClasses(),r.removeAttribute("style"),o.removeAttribute("style"),a&&a.length&&a.forEach(l=>{l.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),l.removeAttribute("style"),l.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(l=>{s.off(l)}),t!==!1&&(s.el.swiper=null,gd(s)),s.destroyed=!0),null}static extendDefaults(t){Ie(oi,t)}static get extendedDefaults(){return oi}static get defaults(){return Li}static installModule(t){Ze.prototype.__modules__||(Ze.prototype.__modules__=[]);const n=Ze.prototype.__modules__;typeof t=="function"&&n.indexOf(t)<0&&n.push(t)}static use(t){return Array.isArray(t)?(t.forEach(n=>Ze.installModule(n)),Ze):(Ze.installModule(t),Ze)}};Object.keys(ri).forEach(e=>{Object.keys(ri[e]).forEach(t=>{ar.prototype[t]=ri[e][t]})});ar.use([Pd,Md]);const ga=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function It(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"&&!e.__swiper__}function Kt(e,t){const n=["__proto__","constructor","prototype"];Object.keys(t).filter(s=>n.indexOf(s)<0).forEach(s=>{typeof e[s]>"u"?e[s]=t[s]:It(t[s])&&It(e[s])&&Object.keys(t[s]).length>0?t[s].__swiper__?e[s]=t[s]:Kt(e[s],t[s]):e[s]=t[s]})}function ma(e){return e===void 0&&(e={}),e.navigation&&typeof e.navigation.nextEl>"u"&&typeof e.navigation.prevEl>"u"}function va(e){return e===void 0&&(e={}),e.pagination&&typeof e.pagination.el>"u"}function ya(e){return e===void 0&&(e={}),e.scrollbar&&typeof e.scrollbar.el>"u"}function ba(e){e===void 0&&(e="");const t=e.split(" ").map(s=>s.trim()).filter(s=>!!s),n=[];return t.forEach(s=>{n.indexOf(s)<0&&n.push(s)}),n.join(" ")}function Rp(e){return e===void 0&&(e=""),e?e.includes("swiper-wrapper")?e:`swiper-wrapper ${e}`:"swiper-wrapper"}function Np(e){let{swiper:t,slides:n,passedParams:s,changedParams:i,nextEl:r,prevEl:o,scrollbarEl:a,paginationEl:l}=e;const f=i.filter(S=>S!=="children"&&S!=="direction"&&S!=="wrapperClass"),{params:c,pagination:u,navigation:p,scrollbar:h,virtual:v,thumbs:b}=t;let I,T,x,d,g,_,P,B;i.includes("thumbs")&&s.thumbs&&s.thumbs.swiper&&c.thumbs&&!c.thumbs.swiper&&(I=!0),i.includes("controller")&&s.controller&&s.controller.control&&c.controller&&!c.controller.control&&(T=!0),i.includes("pagination")&&s.pagination&&(s.pagination.el||l)&&(c.pagination||c.pagination===!1)&&u&&!u.el&&(x=!0),i.includes("scrollbar")&&s.scrollbar&&(s.scrollbar.el||a)&&(c.scrollbar||c.scrollbar===!1)&&h&&!h.el&&(d=!0),i.includes("navigation")&&s.navigation&&(s.navigation.prevEl||o)&&(s.navigation.nextEl||r)&&(c.navigation||c.navigation===!1)&&p&&!p.prevEl&&!p.nextEl&&(g=!0);const A=S=>{t[S]&&(t[S].destroy(),S==="navigation"?(t.isElement&&(t[S].prevEl.remove(),t[S].nextEl.remove()),c[S].prevEl=void 0,c[S].nextEl=void 0,t[S].prevEl=void 0,t[S].nextEl=void 0):(t.isElement&&t[S].el.remove(),c[S].el=void 0,t[S].el=void 0))};i.includes("loop")&&t.isElement&&(c.loop&&!s.loop?_=!0:!c.loop&&s.loop?P=!0:B=!0),f.forEach(S=>{if(It(c[S])&&It(s[S]))Object.assign(c[S],s[S]),(S==="navigation"||S==="pagination"||S==="scrollbar")&&"enabled"in s[S]&&!s[S].enabled&&A(S);else{const w=s[S];(w===!0||w===!1)&&(S==="navigation"||S==="pagination"||S==="scrollbar")?w===!1&&A(S):c[S]=s[S]}}),f.includes("controller")&&!T&&t.controller&&t.controller.control&&c.controller&&c.controller.control&&(t.controller.control=c.controller.control),i.includes("children")&&n&&v&&c.virtual.enabled?(v.slides=n,v.update(!0)):i.includes("virtual")&&v&&c.virtual.enabled&&(n&&(v.slides=n),v.update(!0)),i.includes("children")&&n&&c.loop&&(B=!0),I&&b.init()&&b.update(!0),T&&(t.controller.control=c.controller.control),x&&(t.isElement&&(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-pagination"),l.part.add("pagination"),t.el.appendChild(l)),l&&(c.pagination.el=l),u.init(),u.render(),u.update()),d&&(t.isElement&&(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-scrollbar"),a.part.add("scrollbar"),t.el.appendChild(a)),a&&(c.scrollbar.el=a),h.init(),h.updateSize(),h.setTranslate()),g&&(t.isElement&&((!r||typeof r=="string")&&(r=document.createElement("div"),r.classList.add("swiper-button-next"),r.innerHTML=t.hostEl.constructor.nextButtonSvg,r.part.add("button-next"),t.el.appendChild(r)),(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-button-prev"),o.innerHTML=t.hostEl.constructor.prevButtonSvg,o.part.add("button-prev"),t.el.appendChild(o))),r&&(c.navigation.nextEl=r),o&&(c.navigation.prevEl=o),p.init(),p.update()),i.includes("allowSlideNext")&&(t.allowSlideNext=s.allowSlideNext),i.includes("allowSlidePrev")&&(t.allowSlidePrev=s.allowSlidePrev),i.includes("direction")&&t.changeDirection(s.direction,!1),(_||B)&&t.loopDestroy(),(P||B)&&t.loopCreate(),t.update()}function ro(e,t){e===void 0&&(e={});const n={on:{}},s={},i={};Kt(n,Li),n._emitClasses=!0,n.init=!1;const r={},o=ga.map(l=>l.replace(/_/,"")),a=Object.assign({},e);return Object.keys(a).forEach(l=>{typeof e[l]>"u"||(o.indexOf(l)>=0?It(e[l])?(n[l]={},i[l]={},Kt(n[l],e[l]),Kt(i[l],e[l])):(n[l]=e[l],i[l]=e[l]):l.search(/on[A-Z]/)===0&&typeof e[l]=="function"?n.on[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:r[l]=e[l])}),["navigation","pagination","scrollbar"].forEach(l=>{n[l]===!0&&(n[l]={}),n[l]===!1&&delete n[l]}),{params:n,passedParams:i,rest:r,events:s}}function Dp(e,t){let{el:n,nextEl:s,prevEl:i,paginationEl:r,scrollbarEl:o,swiper:a}=e;ma(t)&&s&&i&&(a.params.navigation.nextEl=s,a.originalParams.navigation.nextEl=s,a.params.navigation.prevEl=i,a.originalParams.navigation.prevEl=i),va(t)&&r&&(a.params.pagination.el=r,a.originalParams.pagination.el=r),ya(t)&&o&&(a.params.scrollbar.el=o,a.originalParams.scrollbar.el=o),a.init(n)}function Bp(e,t,n,s,i){const r=[];if(!t)return r;const o=l=>{r.indexOf(l)<0&&r.push(l)};if(n&&s){const l=s.map(i),f=n.map(i);l.join("")!==f.join("")&&o("children"),s.length!==n.length&&o("children")}return ga.filter(l=>l[0]==="_").map(l=>l.replace(/_/,"")).forEach(l=>{if(l in e&&l in t)if(It(e[l])&&It(t[l])){const f=Object.keys(e[l]),c=Object.keys(t[l]);f.length!==c.length?o(l):(f.forEach(u=>{e[l][u]!==t[l][u]&&o(l)}),c.forEach(u=>{e[l][u]!==t[l][u]&&o(l)}))}else e[l]!==t[l]&&o(l)}),r}const Fp=e=>{!e||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function li(e,t,n){e===void 0&&(e={});const s=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]},r=(o,a)=>{Array.isArray(o)&&o.forEach(l=>{const f=typeof l.type=="symbol";a==="default"&&(a="container-end"),f&&l.children?r(l.children,a):l.type&&(l.type.name==="SwiperSlide"||l.type.name==="AsyncComponentWrapper")?s.push(l):i[a]&&i[a].push(l)})};return Object.keys(e).forEach(o=>{if(typeof e[o]!="function")return;const a=e[o]();r(a,o)}),n.value=t.value,t.value=s,{slides:s,slots:i}}function Vp(e,t,n){if(!n)return null;const s=c=>{let u=c;return c<0?u=t.length+c:u>=t.length&&(u=u-t.length),u},i=e.value.isHorizontal()?{[e.value.rtlTranslate?"right":"left"]:`${n.offset}px`}:{top:`${n.offset}px`},{from:r,to:o}=n,a=e.value.params.loop?-t.length:0,l=e.value.params.loop?t.length*2:t.length,f=[];for(let c=a;c<l;c+=1)c>=r&&c<=o&&f.push(t[s(c)]);return f.map(c=>(c.props||(c.props={}),c.props.style||(c.props.style={}),c.props.swiperRef=e,c.props.style=i,Le(c.type,{...c.props},c.children)))}const kp={name:"Swiper",props:{tag:{type:String,default:"div"},wrapperTag:{type:String,default:"div"},modules:{type:Array,default:void 0},init:{type:Boolean,default:void 0},direction:{type:String,default:void 0},oneWayMovement:{type:Boolean,default:void 0},touchEventsTarget:{type:String,default:void 0},initialSlide:{type:Number,default:void 0},speed:{type:Number,default:void 0},cssMode:{type:Boolean,default:void 0},updateOnWindowResize:{type:Boolean,default:void 0},resizeObserver:{type:Boolean,default:void 0},nested:{type:Boolean,default:void 0},focusableElements:{type:String,default:void 0},width:{type:Number,default:void 0},height:{type:Number,default:void 0},preventInteractionOnTransition:{type:Boolean,default:void 0},userAgent:{type:String,default:void 0},url:{type:String,default:void 0},edgeSwipeDetection:{type:[Boolean,String],default:void 0},edgeSwipeThreshold:{type:Number,default:void 0},autoHeight:{type:Boolean,default:void 0},setWrapperSize:{type:Boolean,default:void 0},virtualTranslate:{type:Boolean,default:void 0},effect:{type:String,default:void 0},breakpoints:{type:Object,default:void 0},spaceBetween:{type:[Number,String],default:void 0},slidesPerView:{type:[Number,String],default:void 0},maxBackfaceHiddenSlides:{type:Number,default:void 0},slidesPerGroup:{type:Number,default:void 0},slidesPerGroupSkip:{type:Number,default:void 0},slidesPerGroupAuto:{type:Boolean,default:void 0},centeredSlides:{type:Boolean,default:void 0},centeredSlidesBounds:{type:Boolean,default:void 0},slidesOffsetBefore:{type:Number,default:void 0},slidesOffsetAfter:{type:Number,default:void 0},normalizeSlideIndex:{type:Boolean,default:void 0},centerInsufficientSlides:{type:Boolean,default:void 0},watchOverflow:{type:Boolean,default:void 0},roundLengths:{type:Boolean,default:void 0},touchRatio:{type:Number,default:void 0},touchAngle:{type:Number,default:void 0},simulateTouch:{type:Boolean,default:void 0},shortSwipes:{type:Boolean,default:void 0},longSwipes:{type:Boolean,default:void 0},longSwipesRatio:{type:Number,default:void 0},longSwipesMs:{type:Number,default:void 0},followFinger:{type:Boolean,default:void 0},allowTouchMove:{type:Boolean,default:void 0},threshold:{type:Number,default:void 0},touchMoveStopPropagation:{type:Boolean,default:void 0},touchStartPreventDefault:{type:Boolean,default:void 0},touchStartForcePreventDefault:{type:Boolean,default:void 0},touchReleaseOnEdges:{type:Boolean,default:void 0},uniqueNavElements:{type:Boolean,default:void 0},resistance:{type:Boolean,default:void 0},resistanceRatio:{type:Number,default:void 0},watchSlidesProgress:{type:Boolean,default:void 0},grabCursor:{type:Boolean,default:void 0},preventClicks:{type:Boolean,default:void 0},preventClicksPropagation:{type:Boolean,default:void 0},slideToClickedSlide:{type:Boolean,default:void 0},loop:{type:Boolean,default:void 0},loopedSlides:{type:Number,default:void 0},loopPreventsSliding:{type:Boolean,default:void 0},rewind:{type:Boolean,default:void 0},allowSlidePrev:{type:Boolean,default:void 0},allowSlideNext:{type:Boolean,default:void 0},swipeHandler:{type:Boolean,default:void 0},noSwiping:{type:Boolean,default:void 0},noSwipingClass:{type:String,default:void 0},noSwipingSelector:{type:String,default:void 0},passiveListeners:{type:Boolean,default:void 0},containerModifierClass:{type:String,default:void 0},slideClass:{type:String,default:void 0},slideActiveClass:{type:String,default:void 0},slideVisibleClass:{type:String,default:void 0},slideFullyVisibleClass:{type:String,default:void 0},slideBlankClass:{type:String,default:void 0},slideNextClass:{type:String,default:void 0},slidePrevClass:{type:String,default:void 0},wrapperClass:{type:String,default:void 0},lazyPreloaderClass:{type:String,default:void 0},lazyPreloadPrevNext:{type:Number,default:void 0},runCallbacksOnInit:{type:Boolean,default:void 0},observer:{type:Boolean,default:void 0},observeParents:{type:Boolean,default:void 0},observeSlideChildren:{type:Boolean,default:void 0},a11y:{type:[Boolean,Object],default:void 0},autoplay:{type:[Boolean,Object],default:void 0},controller:{type:Object,default:void 0},coverflowEffect:{type:Object,default:void 0},cubeEffect:{type:Object,default:void 0},fadeEffect:{type:Object,default:void 0},flipEffect:{type:Object,default:void 0},creativeEffect:{type:Object,default:void 0},cardsEffect:{type:Object,default:void 0},hashNavigation:{type:[Boolean,Object],default:void 0},history:{type:[Boolean,Object],default:void 0},keyboard:{type:[Boolean,Object],default:void 0},mousewheel:{type:[Boolean,Object],default:void 0},navigation:{type:[Boolean,Object],default:void 0},pagination:{type:[Boolean,Object],default:void 0},parallax:{type:[Boolean,Object],default:void 0},scrollbar:{type:[Boolean,Object],default:void 0},thumbs:{type:Object,default:void 0},virtual:{type:[Boolean,Object],default:void 0},zoom:{type:[Boolean,Object],default:void 0},grid:{type:[Object],default:void 0},freeMode:{type:[Boolean,Object],default:void 0},enabled:{type:Boolean,default:void 0}},emits:["_beforeBreakpoint","_containerClasses","_slideClass","_slideClasses","_swiper","_freeModeNoMomentumRelease","activeIndexChange","afterInit","autoplay","autoplayStart","autoplayStop","autoplayPause","autoplayResume","autoplayTimeLeft","beforeDestroy","beforeInit","beforeLoopFix","beforeResize","beforeSlideChangeStart","beforeTransitionStart","breakpoint","breakpointsBase","changeDirection","click","disable","doubleTap","doubleClick","destroy","enable","fromEdge","hashChange","hashSet","init","keyPress","lock","loopFix","momentumBounce","navigationHide","navigationShow","navigationPrev","navigationNext","observerUpdate","orientationchange","paginationHide","paginationRender","paginationShow","paginationUpdate","progress","reachBeginning","reachEnd","realIndexChange","resize","scroll","scrollbarDragEnd","scrollbarDragMove","scrollbarDragStart","setTransition","setTranslate","slideChange","slideChangeTransitionEnd","slideChangeTransitionStart","slideNextTransitionEnd","slideNextTransitionStart","slidePrevTransitionEnd","slidePrevTransitionStart","slideResetTransitionStart","slideResetTransitionEnd","sliderMove","sliderFirstMove","slidesLengthChange","slidesGridLengthChange","snapGridLengthChange","snapIndexChange","swiper","tap","toEdge","touchEnd","touchMove","touchMoveOpposite","touchStart","transitionEnd","transitionStart","unlock","update","virtualUpdate","zoomChange"],setup(e,t){let{slots:n,emit:s}=t;const{tag:i,wrapperTag:r}=e,o=fe("swiper"),a=fe(null),l=fe(!1),f=fe(!1),c=fe(null),u=fe(null),p=fe(null),h={value:[]},v={value:[]},b=fe(null),I=fe(null),T=fe(null),x=fe(null),{params:d,passedParams:g}=ro(e);li(n,h,v),p.value=g,v.value=h.value;const _=()=>{li(n,h,v),l.value=!0};d.onAny=function(A){for(var S=arguments.length,w=new Array(S>1?S-1:0),M=1;M<S;M++)w[M-1]=arguments[M];s(A,...w)},Object.assign(d.on,{_beforeBreakpoint:_,_containerClasses(A,S){o.value=S}});const P={...d};if(delete P.wrapperClass,u.value=new ar(P),u.value.virtual&&u.value.params.virtual.enabled){u.value.virtual.slides=h.value;const A={cache:!1,slides:h.value,renderExternal:S=>{a.value=S},renderExternalUpdate:!1};Kt(u.value.params.virtual,A),Kt(u.value.originalParams.virtual,A)}en(()=>{!f.value&&u.value&&(u.value.emitSlidesClasses(),f.value=!0);const{passedParams:A}=ro(e),S=Bp(A,p.value,h.value,v.value,w=>w.props&&w.props.key);p.value=A,(S.length||l.value)&&u.value&&!u.value.destroyed&&Np({swiper:u.value,slides:h.value,passedParams:A,changedParams:S,nextEl:b.value,prevEl:I.value,scrollbarEl:x.value,paginationEl:T.value}),l.value=!1}),Bs("swiper",u),Pt(a,()=>{Rn(()=>{Fp(u.value)})}),Nt(()=>{c.value&&(Dp({el:c.value,nextEl:b.value,prevEl:I.value,paginationEl:T.value,scrollbarEl:x.value,swiper:u.value},d),s("swiper",u.value))}),tn(()=>{u.value&&!u.value.destroyed&&u.value.destroy(!0,!1)});function B(A){return d.virtual?Vp(u,A,a.value):(A.forEach((S,w)=>{S.props||(S.props={}),S.props.swiperRef=u,S.props.swiperSlideIndex=w}),A)}return()=>{const{slides:A,slots:S}=li(n,h,v);return Le(i,{ref:c,class:ba(o.value)},[S["container-start"],Le(r,{class:Rp(d.wrapperClass)},[S["wrapper-start"],B(A),S["wrapper-end"]]),ma(e)&&[Le("div",{ref:I,class:"swiper-button-prev"}),Le("div",{ref:b,class:"swiper-button-next"})],ya(e)&&Le("div",{ref:x,class:"swiper-scrollbar"}),va(e)&&Le("div",{ref:T,class:"swiper-pagination"}),S["container-end"]])}}},$p={name:"SwiperSlide",props:{tag:{type:String,default:"div"},swiperRef:{type:Object,required:!1},swiperSlideIndex:{type:Number,default:void 0,required:!1},zoom:{type:Boolean,default:void 0,required:!1},lazy:{type:Boolean,default:!1,required:!1},virtualIndex:{type:[String,Number],default:void 0}},setup(e,t){let{slots:n}=t,s=!1;const{swiperRef:i}=e,r=fe(null),o=fe("swiper-slide"),a=fe(!1);function l(u,p,h){p===r.value&&(o.value=h)}Nt(()=>{!i||!i.value||(i.value.on("_slideClass",l),s=!0)}),Ns(()=>{s||!i||!i.value||(i.value.on("_slideClass",l),s=!0)}),en(()=>{!r.value||!i||!i.value||(typeof e.swiperSlideIndex<"u"&&(r.value.swiperSlideIndex=e.swiperSlideIndex),i.value.destroyed&&o.value!=="swiper-slide"&&(o.value="swiper-slide"))}),tn(()=>{!i||!i.value||i.value.off("_slideClass",l)});const f=ir(()=>({isActive:o.value.indexOf("swiper-slide-active")>=0,isVisible:o.value.indexOf("swiper-slide-visible")>=0,isPrev:o.value.indexOf("swiper-slide-prev")>=0,isNext:o.value.indexOf("swiper-slide-next")>=0}));Bs("swiperSlide",f);const c=()=>{a.value=!0};return()=>Le(e.tag,{class:ba(`${o.value}`),ref:r,"data-swiper-slide-index":typeof e.virtualIndex>"u"&&i&&i.value&&i.value.params.loop?e.swiperSlideIndex:e.virtualIndex,onLoadCapture:c},e.zoom?Le("div",{class:"swiper-zoom-container","data-swiper-zoom":typeof e.zoom=="number"?e.zoom:void 0},[n.default&&n.default(f.value),e.lazy&&!a.value&&Le("div",{class:"swiper-lazy-preloader"})]):[n.default&&n.default(f.value),e.lazy&&!a.value&&Le("div",{class:"swiper-lazy-preloader"})])}};export{yf as $,Q as A,Ha as B,pc as C,Ro as D,$i as E,Nt as F,yt as G,oe as H,z as I,ne as J,Qc as K,me as L,Mn as M,tu as N,tr as O,ee as P,gu as Q,Fe as R,rt as S,yc as T,W as U,af as V,Rl as W,Ln as X,en as Y,le as Z,ge as _,ke as a,Gi as a0,Zu as a1,In as a2,fs as a3,nr as a4,fo as a5,sf as a6,ru as a7,bf as a8,ld as a9,Ru as aA,ws as aB,cf as aC,or as aD,rr as aE,Di as aF,kp as aG,$p as aH,Hp as aI,iu as aJ,pr as aK,Mu as aa,Rc as ab,Kl as ac,mt as ad,Mi as ae,Pe as af,oa as ag,nf as ah,cd as ai,Yf as aj,Tf as ak,Na as al,Ll as am,Jc as an,nl as ao,la as ap,of as aq,Zo as ar,lf as as,Dc as at,Ye as au,ce as av,Ea as aw,rf as ax,ms as ay,On as az,fe as b,Pt as c,Xo as d,Va as e,Yo as f,po as g,If as h,mn as i,Io as j,Ds as k,Ki as l,ir as m,Rn as n,tn as o,Le as p,Bs as q,Ps as r,Ao as s,mc as t,Os as u,kl as v,Hf as w,Oo as x,he as y,ht as z};
