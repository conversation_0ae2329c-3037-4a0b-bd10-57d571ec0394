import{_ as p}from"./B-qcbZcc.js";import{l as _,j as f,b as u,M as x,N as h,O as t,a7 as a,Z as l,u as k,n as v}from"./Dp9aCaJ6.js";const y={class:"flex items-center"},b={class:"bg-white px-4 rounded"},g={class:"mx-2 text-[#000] flex-1 line-clamp-1"},w=["contenteditable"],N=_({__name:"data-item",props:{index:{},name:{},data:{}},emits:["delete","update:data"],setup(C,{emit:r}){const n=r,s=f(),o=u(!1),d=async()=>{o.value=!0,await v(),s.value.focus()},m=e=>{n("update:data",e.target.innerText)};return(e,i)=>{const c=p;return x(),h("div",null,[t("div",y,[t("span",b," #"+a(e.index+1),1),t("span",g,a(e.name),1),l(c,{class:"icon-delete text-primary cursor-pointer mr-2",name:"el-icon-EditPen",onClick:d}),l(c,{class:"icon-delete text-primary cursor-pointer",name:"el-icon-Delete",onClick:i[0]||(i[0]=E=>n("delete"))})]),t("div",{ref_key:"editRef",ref:s,class:"mt-2 whitespace-pre-line",contenteditable:k(o),onInput:m},a(e.data),41,w)])}}});export{N as _};
