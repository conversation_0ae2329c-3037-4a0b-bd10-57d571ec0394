import{_ as L}from"./DUH6hp3a.js";import{b as M,E as Q}from"./DyU4wb-Q.js";import{l as V,M as n,N as r,O as s,V as T,a2 as O,b as p,m as U,F as q,Z as y,_ as S,aq as D,u as a,a0 as w,a6 as G,a4 as P,r as W,X,a7 as B,aa as Z,ab as j,a1 as H,a3 as J}from"./Dp9aCaJ6.js";import{_ as g}from"./DlAUqK2U.js";import{u as K}from"./CcPlX2kz.js";import{ImportTypeEnum as m}from"./BiHhwkbt.js";import R from"./vIGud_9K.js";import Y from"./BH8Q2cFu.js";import tt from"./Ca81GcEL.js";import{_ as et}from"./BATYM5KP.js";import{g as ot}from"./BNnETjxs.js";import"./BVx1xlji.js";import"./0qQUtt94.js";import"./C-srSIka.js";import"./D4s3zOA5.js";import"./BDGxxzzg.js";/* empty css        */import"./CoatG_CK.js";import"./BqRNHXMi.js";import"./Cpg3PDWZ.js";import"./oVcuGg1t.js";import"./DCa8BdSG.js";import"./DCTLXrZ8.js";import"./C3h7pD7s.js";import"./9MAnF5ll.js";import"./Cv6HhfEG.js";import"./CH_T-XC0.js";import"./CQqsfOf-.js";import"./D-tOg06u.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./C4qLKnCc.js";import"./CykjJ2S5.js";import"./DP2rzg_V.js";/* empty css        */const st={class:"footer-btns"},at=V({__name:"index",props:{fixed:{type:Boolean,default:!0}},setup(l){return(d,v)=>(n(),r("div",st,[s("div",{class:"footer-btns__content",style:O(l.fixed?"position: fixed":"")},[T(d.$slots,"default",{},void 0,!0)],4)]))}}),nt=g(at,[["__scopeId","data-v-4b8d4336"]]),rt={class:"h-full flex flex-col relative"},it={class:"pb-[20px] flex items-center font-bold cursor-pointer"},pt={class:"flex"},ct=["onClick"],mt={class:"text-info text-[14px]"},lt={class:"flex-1"},dt={key:0},ut=V({__name:"importData",props:{id:{type:Number,default:0},type:{type:String,default:""}},emits:["success","back"],setup(l,{emit:d}){const v=M(),E=d,x=l,c=p(0),F=p([]),N=p([]),$=p([]),A=p([]),i=U(()=>W([{name:"文档导入",type:m.DOC,describe:"选择文本文件，直接将其按分段进行处理",component:Y,show:!0,data:F},{name:"问答对导入",type:m.CVS,describe:"批量导入问答对，效果最佳",component:R,show:!0,data:N},{name:"自动拆分问答对",type:m.QASplit,describe:"选择文本文件，让大模型自动生成问答对",component:tt,show:!0,data:$},{name:"网页解析",type:m.WEB_PAGE,describe:"输入网页链接，快速导入内容",component:et,show:!0,data:A}]).filter(({show:o})=>o)),b=e=>{c.value=e},I=()=>{b(i.value[0].type)},{lockFn:z}=K(async()=>{const e=i.value.find(({type:f})=>f===c.value);console.log(e==null?void 0:e.data);const{data:o,type:u}=e,_={kb_id:x.id,method:u,documents:o};await ot({..._}),k()}),k=()=>{E("back")};return q(()=>{I()}),(e,o)=>{const u=L,_=Q,f=nt;return n(),r("div",rt,[s("div",it,[s("div",{onClick:k,class:"flex items-center"},[y(u,{name:"el-icon-Back",size:"16"}),o[0]||(o[0]=s("span",{class:"ml-2"},"文件导入",-1))])]),s("div",pt,[(n(!0),r(S,null,D(a(i),(t,h)=>(n(),r("div",{key:h,class:X(["unselect w-[290px] p-[16px] text-center rounded-md cursor-pointer mr-4",{isselect:t.type==a(c)}]),onClick:C=>b(t.type)},[s("div",null,B(t.name),1),s("div",mt,B(t.describe),1)],10,ct))),128))]),s("div",lt,[(n(!0),r(S,null,D(a(i),(t,h)=>Z((n(),H(J(t.component),{key:t.type,modelValue:t.data,"onUpdate:modelValue":C=>t.data=C,type:x.type},null,8,["modelValue","onUpdate:modelValue","type"])),[[j,t.type==a(c)]])),128))]),a(i).length>0?(n(),r("div",dt,[y(f,{fixed:!a(v).isMobile},{default:w(()=>[y(_,{type:"primary",onClick:a(z)},{default:w(()=>o[1]||(o[1]=[G(" 保存 ")])),_:1},8,["onClick"])]),_:1},8,["fixed"])])):P("",!0)])}}}),Kt=g(ut,[["__scopeId","data-v-0ebde116"]]);export{Kt as default};
