import{E as f}from"./BvJaxNwU.js";import{_ as x}from"./DH5ElBC0.js";import{a as b,b as g}from"./DjCZV6kq.js";/* empty css        */import{l as k,m as i,M as s,N as m,O as r,_ as v,aq as w,u as o,a1 as p,a0 as y,X as B,a7 as C}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./CkQdVShy.js";import"./CQg2_aaP.js";import"./CK--Z7ot.js";const P={class:"tabbar"},S={class:"tabbar__content"},E={class:"tabbar__content__item__icon"},L={class:"tabbar__content__item__text"},N=k({__name:"index",setup(U){const e=b(),c=g(),u=i(()=>{var a,_;return((_=(a=c.getHeaderConfig)==null?void 0:a.nav)==null?void 0:_.filter(n=>n.isShow))||[]}),l=i(()=>{const a=e.path==="/"?e.path:e.path.replace(/\/$/,"");return e.meta.parentPath||e.meta.activePath||a});return(a,_)=>{const n=f,d=x;return s(),m("div",P,[r("div",S,[(s(!0),m(v,null,w(o(u),(t,h)=>(s(),p(d,{key:h,to:{path:t.link.path,replace:!0},class:"flex-1"},{default:y(()=>[r("div",{class:B(["tabbar__content__item w-full",{active:o(l)===t.link.path}])},[r("div",E,[o(l)===t.link.path?(s(),p(n,{key:0,class:"w-[18px] h-[18px]",src:o(c).getImageUrl(t.selected)},null,8,["src"])):(s(),p(n,{key:1,class:"w-[18px] h-[18px]",src:o(c).getImageUrl(t.unselected)},null,8,["src"]))]),r("div",L,C(t.name),1)],2)]),_:2},1032,["to"]))),128))])])}}}),V=I(N,[["__scopeId","data-v-4afcc8ee"]]);export{V as default};
