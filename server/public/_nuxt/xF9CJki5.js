import{a as _}from"./CtBRWm-a.js";import{b as h,a as d}from"./Br7V4jS9.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import f from"./KZHpYDEN.js";import{l as v,m as r,M as s,N as c,Z as w,a0 as k,_ as N,aq as x,u as o,a1 as B}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./C3sn0ick.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./LFZ9apcG.js";import"./DVYAUH4R.js";import"./rgjptJRl.js";import"./V6VzAR3J.js";const M={class:"menu"},g=v({__name:"nav",props:{isHome:{type:Boolean}},setup(A){const n=h(),m=r(()=>{var e;return((e=n.pageAside.nav)==null?void 0:e.filter(i=>Number(i.is_show)===1))||[]}),u=r(()=>n.pageAside.showNavIcon),t=d(),p=r(()=>{const e=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.parentPath||t.meta.activePath||e});return(e,i)=>{const l=_;return s(),c("div",M,[w(l,{"default-active":o(p)},{default:k(()=>[(s(!0),c(N,null,x(o(m),a=>(s(),B(f,{key:a.id,item:a,showName:!0,"is-show-icon":o(u),path:a.link.path,"is-active":o(p)===a.link.path},null,8,["item","is-show-icon","path","is-active"]))),128))]),_:1},8,["default-active"])])}}}),z=I(g,[["__scopeId","data-v-818826ac"]]);export{z as default};
