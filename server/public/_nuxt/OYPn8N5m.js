import{E as c}from"./BOI9rKCA.js";import{E as g}from"./CHyUIwEw.js";import{E as x}from"./Du3XpPBg.js";import{b as y,v as D}from"./DNaNbs6R.js";/* empty css        *//* empty css        */import{r as V,x as E,y as w,f as e,e as v}from"./BFZxX7k8.js";import L from"./AkdvbMTe.js";import b from"./C8Ph7Nt0.js";import{_ as k}from"./DRRIOYDp.js";import S from"./DavJovO-.js";import A from"./CfjDMbBR.js";import P from"./DTmoJ9q9.js";import{D as z}from"./BfG_8n0X.js";import{DrawModeEnum as i}from"./tONJIxwY.js";import{l as U,F as B,u as o,M as p,N as a,Z as r,a0 as l,O as s,aa as M}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./DvxhPWqY.js";import"./W1Gt2nzg.js";import"./CCY2gEyH.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./BWd1nnnI.js";import"./CMsEk3FA.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./C-y-N5Ef.js";import"./dIlF1NYp.js";import"./B-XemFWQ.js";import"./9Bti1uB6.js";/* empty css        */import"./dQYUVbIc.js";import"./CQAjyP_F.js";/* empty css        */import"./BSocHp_O.js";import"./C7WF4igz.js";import"./CADq9QTD.js";import"./Dpue_PSi.js";import"./4g_h7BIh.js";import"./Cpg3PDWZ.js";import"./DaE5R0H9.js";import"./-XMHCl9l.js";import"./CUCgy8P_.js";import"./Cv6HhfEG.js";import"./RgeKLsDk.js";import"./BEIhC0Cu.js";import"./Dxs4oV12.js";/* empty css        *//* empty css        */import"./1sgdir_0.js";import"./CIVqX2e7.js";import"./lLQEl50a.js";import"./DjwCd26w.js";import"./BFjeuWRo.js";import"./CcPlX2kz.js";import"./NR1-7dsP.js";import"./BVqQ2B7o.js";import"./BWdDF8rn.js";import"./B7MVvBhq.js";const q={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},C={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},F={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},I=U({__name:"dalle",setup(O){const n=y();return B(()=>{V({draw_api:i.DALLE3,draw_model:i.DALLE3,action:"generate",prompt:"",negative_prompt:"",size:"1024x1024"}),E.model=i.DALLE3,w()}),(R,t)=>{const d=c,u=g,f=x,_=D;return o(n).config.switch.dalle3_status?(p(),a("div",q,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:l(()=>[s("div",C,[r(L,{modelValue:o(e).prompt,"onUpdate:modelValue":t[0]||(t[0]=m=>o(e).prompt=m),model:o(i).DALLE3},null,8,["modelValue","model"]),r(S,{modelValue:o(e).size,"onUpdate:modelValue":t[1]||(t[1]=m=>o(e).size=m)},null,8,["modelValue"]),r(A,{modelValue:o(e).style,"onUpdate:modelValue":t[2]||(t[2]=m=>o(e).style=m)},null,8,["modelValue"]),r(P,{modelValue:o(e).quality,"onUpdate:modelValue":t[3]||(t[3]=m=>o(e).quality=m)},null,8,["modelValue"])]),r(k)]),_:1}),M(r(b,{"element-loading-text":"正在加载数据..."},null,512),[[_,o(v)]])])):(p(),a("div",F,[r(f,null,{icon:l(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(z)},null,8,["src"])]),title:l(()=>t[4]||(t[4]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),Ho=N(I,[["__scopeId","data-v-9879be3f"]]);export{Ho as default};
