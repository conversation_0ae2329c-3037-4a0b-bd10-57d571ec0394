import{_ as M}from"./C2uyROqA.js";import{b as Q,j as A,m as F,f as H,E as J}from"./CLVDtxqA.js";import{E as T}from"./B1txbFRp.js";/* empty css        */import{u as Z}from"./VJhqcMCs.js";import{d as q}from"./B-7_EDyl.js";import{Q as G}from"./Bg2e09vA.js";import{l as K,b as d,j as W,m as f,F as X,M as m,N as _,O as s,V as Y,Z as l,a0 as r,u as o,a2 as E,a7 as ee,a4 as I,a6 as N,y as oe}from"./Dp9aCaJ6.js";import{_ as te}from"./DlAUqK2U.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";const se={class:"poster-container"},ae={style:{height:"548px"},class:"flex justify-center overflow-hidden"},le={class:"poster-bg flex flex-col"},ne=["src"],re={class:"w-full h-full poster-contain1"},ie={class:"mt-6 px-5 grid grid-cols-2 gap-x-[10px]"},de={class:"flex-1"},ce={class:"flex-1"},pe=K({__name:"poster",setup(ue){const U=Q(),{getImageUrl:c,config:fe}=U,i=d(!1),p=d(!1),a=d({}),u=d(!1),x=A(),g=W(),B=async()=>{var e;u.value=!0;try{const{data:t}=await F({id:9});a.value=((e=JSON.parse(t)[0])==null?void 0:e.content)||{}}finally{u.value=!1}},z=f(()=>`${window.origin}/mobile?user_sn=${x.userInfo.sn}`),R=f(()=>{const e=a.value;if(e.default==1&&e.poster==1)return c(e.defaultUrl1);if(e.default==1&&e.poster==2)return c(e.defaultUrl2);if(e.default==2)return c(e.posterUrl)}),{copy:j}=Z(),L=f(()=>`${window.origin}/?user_sn=${x.userInfo.sn}`),O=async()=>{try{p.value=!0,await q(g.value)}catch{H.msgError("下载失败，请重试")}finally{p.value=!1}};return X(()=>{B()}),(e,t)=>{const v=M,w=J,P=T;return m(),_("div",se,[s("div",{class:"inline-block",onClick:t[0]||(t[0]=n=>i.value=!0)},[Y(e.$slots,"trigger",{},void 0,!0)]),l(P,{modelValue:o(i),"onUpdate:modelValue":t[2]||(t[2]=n=>oe(i)?i.value=n:null),title:"分销海报","show-close":"",class:"!rounded-[15px]",width:"390px"},{default:r(()=>{var n,y,h,k,$,b,V,C,D,S;return[s("div",ae,[o(u)?I("",!0):(m(),_("div",{key:0,ref_key:"posterRef",ref:g,class:"poster h-[548px] overflow-hidden"},[s("div",le,[s("img",{class:"w-full min-h-[548px] rounded-[10px]",src:o(R),alt:""},null,8,ne)]),s("div",re,[s("div",{class:"absolute z-10 bg-white rounded-[10px] p-[5px]",style:E({top:`${((y=(n=o(a))==null?void 0:n.code)==null?void 0:y.y)*1.218}px`,left:`${((k=(h=o(a))==null?void 0:h.code)==null?void 0:k.x)*1.218}px`})},[l(G,{value:o(z),size:110,margin:1},null,8,["value"])],4),o(a).showData?(m(),_("span",{key:0,class:"text-white text-xl absolute z-10",style:E({top:`${((b=($=o(a))==null?void 0:$.data)==null?void 0:b.y)*1.218}px`,left:`${((C=(V=o(a))==null?void 0:V.data)==null?void 0:C.x)*1.218}px`})},ee((S=(D=o(a))==null?void 0:D.data)==null?void 0:S.content),5)):I("",!0)])],512))]),s("div",ie,[s("div",de,[l(w,{class:"w-full",onClick:t[1]||(t[1]=me=>o(j)(o(L)))},{icon:r(()=>[l(v,{name:"el-icon-DocumentCopy"})]),default:r(()=>[t[3]||(t[3]=N(" 复制链接 "))]),_:1})]),s("div",ce,[l(w,{type:"primary",class:"w-full",loading:o(p),onClick:O},{icon:r(()=>[l(v,{name:"el-icon-Download"})]),default:r(()=>[t[4]||(t[4]=N(" 下载 "))]),_:1},8,["loading"])])])]}),_:1},8,["modelValue"])])}}}),Ce=te(pe,[["__scopeId","data-v-c30aafc1"]]);export{Ce as default};
