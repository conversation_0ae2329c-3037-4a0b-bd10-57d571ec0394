import{_ as Ke}from"./BWd1nnnI.js";import{P as ne,r as we,J as Se,l as _,i as Y,p as le,V as fe,q as W,b as K,c as w,n as ce,ah as O,aa as oe,ab as de,M as D,N as F,O as $,a1 as B,a0 as P,a3 as xe,X as S,a9 as T,a4 as R,Z as J,a2 as Ae,_ as pe,aq as ge,a as ye,U as Te,H as Ie,j as he,F as Oe,Y as Be,m as Ne,a7 as ke}from"./Dp9aCaJ6.js";import{ch as $e,O as ae,M as Q,bQ as te,bP as Le,J as Me,cg as qe,dc as ze,V as Fe,af as I,al as Pe,a5 as je,aJ as He,a1 as Re}from"./DNaNbs6R.js";import{_ as Ue}from"./vCtA0sHn.js";import{E as Je}from"./BtxdHD3i.js";/* empty css        */import Ye from"./--J6__kh.js";import{_ as We}from"./DlAUqK2U.js";import"./RgeKLsDk.js";import"./BwPetIct.js";const j="$treeNodeId",ue=function(t,e){!e||e[j]||Object.defineProperty(e,j,{value:t.id,enumerable:!1,configurable:!1,writable:!1})},ie=function(t,e){return t?e[t]:e[j]},re=(t,e,o)=>{const d=t.value.currentNode;o();const n=t.value.currentNode;d!==n&&e("current-change",n?n.data:null,n)},se=t=>{let e=!0,o=!0,d=!0;for(let n=0,r=t.length;n<r;n++){const s=t[n];(s.checked!==!0||s.indeterminate)&&(e=!1,s.disabled||(d=!1)),(s.checked!==!1||s.indeterminate)&&(o=!1)}return{all:e,none:o,allWithoutDisable:d,half:!e&&!o}},U=function(t){if(t.childNodes.length===0||t.loading)return;const{all:e,none:o,half:d}=se(t.childNodes);e?(t.checked=!0,t.indeterminate=!1):d?(t.checked=!1,t.indeterminate=!0):o&&(t.checked=!1,t.indeterminate=!1);const n=t.parent;!n||n.level===0||t.store.checkStrictly||U(n)},Z=function(t,e){const o=t.store.props,d=t.data||{},n=o[e];if(typeof n=="function")return n(d,t);if(typeof n=="string")return d[n];if(typeof n>"u"){const r=d[e];return r===void 0?"":r}};let Qe=0;class L{constructor(e){this.id=Qe++,this.text=null,this.checked=!1,this.indeterminate=!1,this.data=null,this.expanded=!1,this.parent=null,this.visible=!0,this.isCurrent=!1,this.canFocus=!1;for(const o in e)ne(e,o)&&(this[o]=e[o]);this.level=0,this.loaded=!1,this.childNodes=[],this.loading=!1,this.parent&&(this.level=this.parent.level+1)}initialize(){const e=this.store;if(!e)throw new Error("[Node]store is required!");e.registerNode(this);const o=e.props;if(o&&typeof o.isLeaf<"u"){const r=Z(this,"isLeaf");typeof r=="boolean"&&(this.isLeafByUser=r)}if(e.lazy!==!0&&this.data?(this.setData(this.data),e.defaultExpandAll&&(this.expanded=!0,this.canFocus=!0)):this.level>0&&e.lazy&&e.defaultExpandAll&&this.expand(),Array.isArray(this.data)||ue(this,this.data),!this.data)return;const d=e.defaultExpandedKeys,n=e.key;n&&d&&d.includes(this.key)&&this.expand(null,e.autoExpandParent),n&&e.currentNodeKey!==void 0&&this.key===e.currentNodeKey&&(e.currentNode=this,e.currentNode.isCurrent=!0),e.lazy&&e._initDefaultCheckedNode(this),this.updateLeafState(),this.parent&&(this.level===1||this.parent.expanded===!0)&&(this.canFocus=!0)}setData(e){Array.isArray(e)||ue(this,e),this.data=e,this.childNodes=[];let o;this.level===0&&Array.isArray(this.data)?o=this.data:o=Z(this,"children")||[];for(let d=0,n=o.length;d<n;d++)this.insertChild({data:o[d]})}get label(){return Z(this,"label")}get key(){const e=this.store.key;return this.data?this.data[e]:null}get disabled(){return Z(this,"disabled")}get nextSibling(){const e=this.parent;if(e){const o=e.childNodes.indexOf(this);if(o>-1)return e.childNodes[o+1]}return null}get previousSibling(){const e=this.parent;if(e){const o=e.childNodes.indexOf(this);if(o>-1)return o>0?e.childNodes[o-1]:null}return null}contains(e,o=!0){return(this.childNodes||[]).some(d=>d===e||o&&d.contains(e))}remove(){const e=this.parent;e&&e.removeChild(this)}insertChild(e,o,d){if(!e)throw new Error("InsertChild error: child is required.");if(!(e instanceof L)){if(!d){const n=this.getChildren(!0);n.includes(e.data)||(typeof o>"u"||o<0?n.push(e.data):n.splice(o,0,e.data))}Object.assign(e,{parent:this,store:this.store}),e=we(new L(e)),e instanceof L&&e.initialize()}e.level=this.level+1,typeof o>"u"||o<0?this.childNodes.push(e):this.childNodes.splice(o,0,e),this.updateLeafState()}insertBefore(e,o){let d;o&&(d=this.childNodes.indexOf(o)),this.insertChild(e,d)}insertAfter(e,o){let d;o&&(d=this.childNodes.indexOf(o),d!==-1&&(d+=1)),this.insertChild(e,d)}removeChild(e){const o=this.getChildren()||[],d=o.indexOf(e.data);d>-1&&o.splice(d,1);const n=this.childNodes.indexOf(e);n>-1&&(this.store&&this.store.deregisterNode(e),e.parent=null,this.childNodes.splice(n,1)),this.updateLeafState()}removeChildByData(e){let o=null;for(let d=0;d<this.childNodes.length;d++)if(this.childNodes[d].data===e){o=this.childNodes[d];break}o&&this.removeChild(o)}expand(e,o){const d=()=>{if(o){let n=this.parent;for(;n.level>0;)n.expanded=!0,n=n.parent}this.expanded=!0,e&&e(),this.childNodes.forEach(n=>{n.canFocus=!0})};this.shouldLoadData()?this.loadData(n=>{Array.isArray(n)&&(this.checked?this.setChecked(!0,!0):this.store.checkStrictly||U(this),d())}):d()}doCreateChildren(e,o={}){e.forEach(d=>{this.insertChild(Object.assign({data:d},o),void 0,!0)})}collapse(){this.expanded=!1,this.childNodes.forEach(e=>{e.canFocus=!1})}shouldLoadData(){return this.store.lazy===!0&&this.store.load&&!this.loaded}updateLeafState(){if(this.store.lazy===!0&&this.loaded!==!0&&typeof this.isLeafByUser<"u"){this.isLeaf=this.isLeafByUser;return}const e=this.childNodes;if(!this.store.lazy||this.store.lazy===!0&&this.loaded===!0){this.isLeaf=!e||e.length===0;return}this.isLeaf=!1}setChecked(e,o,d,n){if(this.indeterminate=e==="half",this.checked=e===!0,this.store.checkStrictly)return;if(!(this.shouldLoadData()&&!this.store.checkDescendants)){const{all:s,allWithoutDisable:a}=se(this.childNodes);!this.isLeaf&&!s&&a&&(this.checked=!1,e=!1);const u=()=>{if(o){const g=this.childNodes;for(let i=0,p=g.length;i<p;i++){const N=g[i];n=n||e!==!1;const k=N.disabled?N.checked:n;N.setChecked(k,o,!0,n)}const{half:h,all:c}=se(g);c||(this.checked=c,this.indeterminate=h)}};if(this.shouldLoadData()){this.loadData(()=>{u(),U(this)},{checked:e!==!1});return}else u()}const r=this.parent;!r||r.level===0||d||U(r)}getChildren(e=!1){if(this.level===0)return this.data;const o=this.data;if(!o)return null;const d=this.store.props;let n="children";return d&&(n=d.children||"children"),o[n]===void 0&&(o[n]=null),e&&!o[n]&&(o[n]=[]),o[n]}updateChildren(){const e=this.getChildren()||[],o=this.childNodes.map(r=>r.data),d={},n=[];e.forEach((r,s)=>{const a=r[j];!!a&&o.findIndex(g=>g[j]===a)>=0?d[a]={index:s,data:r}:n.push({index:s,data:r})}),this.store.lazy||o.forEach(r=>{d[r[j]]||this.removeChildByData(r)}),n.forEach(({index:r,data:s})=>{this.insertChild({data:s},r)}),this.updateLeafState()}loadData(e,o={}){if(this.store.lazy===!0&&this.store.load&&!this.loaded&&(!this.loading||Object.keys(o).length)){this.loading=!0;const d=r=>{this.childNodes=[],this.doCreateChildren(r,o),this.loaded=!0,this.loading=!1,this.updateLeafState(),e&&e.call(this,r)},n=()=>{this.loading=!1};this.store.load(this,d,n)}else e&&e.call(this)}eachNode(e){const o=[this];for(;o.length;){const d=o.shift();o.unshift(...d.childNodes),e(d)}}reInitChecked(){this.store.checkStrictly||U(this)}}class Ve{constructor(e){this.currentNode=null,this.currentNodeKey=null;for(const o in e)ne(e,o)&&(this[o]=e[o]);this.nodesMap={}}initialize(){if(this.root=new L({data:this.data,store:this}),this.root.initialize(),this.lazy&&this.load){const e=this.load;e(this.root,o=>{this.root.doCreateChildren(o),this._initDefaultCheckedNodes()})}else this._initDefaultCheckedNodes()}filter(e){const o=this.filterNodeMethod,d=this.lazy,n=function(r){const s=r.root?r.root.childNodes:r.childNodes;if(s.forEach(a=>{a.visible=o.call(a,e,a.data,a),n(a)}),!r.visible&&s.length){let a=!0;a=!s.some(u=>u.visible),r.root?r.root.visible=a===!1:r.visible=a===!1}e&&r.visible&&!r.isLeaf&&(!d||r.loaded)&&r.expand()};n(this)}setData(e){e!==this.root.data?(this.root.setData(e),this._initDefaultCheckedNodes()):this.root.updateChildren()}getNode(e){if(e instanceof L)return e;const o=Se(e)?ie(this.key,e):e;return this.nodesMap[o]||null}insertBefore(e,o){const d=this.getNode(o);d.parent.insertBefore({data:e},d)}insertAfter(e,o){const d=this.getNode(o);d.parent.insertAfter({data:e},d)}remove(e){const o=this.getNode(e);o&&o.parent&&(o===this.currentNode&&(this.currentNode=null),o.parent.removeChild(o))}append(e,o){const d=$e(o)?this.root:this.getNode(o);d&&d.insertChild({data:e})}_initDefaultCheckedNodes(){const e=this.defaultCheckedKeys||[],o=this.nodesMap;e.forEach(d=>{const n=o[d];n&&n.setChecked(!0,!this.checkStrictly)})}_initDefaultCheckedNode(e){(this.defaultCheckedKeys||[]).includes(e.key)&&e.setChecked(!0,!this.checkStrictly)}setDefaultCheckedKey(e){e!==this.defaultCheckedKeys&&(this.defaultCheckedKeys=e,this._initDefaultCheckedNodes())}registerNode(e){const o=this.key;!e||!e.data||(o?e.key!==void 0&&(this.nodesMap[e.key]=e):this.nodesMap[e.id]=e)}deregisterNode(e){!this.key||!e||!e.data||(e.childNodes.forEach(d=>{this.deregisterNode(d)}),delete this.nodesMap[e.key])}getCheckedNodes(e=!1,o=!1){const d=[],n=function(r){(r.root?r.root.childNodes:r.childNodes).forEach(a=>{(a.checked||o&&a.indeterminate)&&(!e||e&&a.isLeaf)&&d.push(a.data),n(a)})};return n(this),d}getCheckedKeys(e=!1){return this.getCheckedNodes(e).map(o=>(o||{})[this.key])}getHalfCheckedNodes(){const e=[],o=function(d){(d.root?d.root.childNodes:d.childNodes).forEach(r=>{r.indeterminate&&e.push(r.data),o(r)})};return o(this),e}getHalfCheckedKeys(){return this.getHalfCheckedNodes().map(e=>(e||{})[this.key])}_getAllNodes(){const e=[],o=this.nodesMap;for(const d in o)ne(o,d)&&e.push(o[d]);return e}updateChildren(e,o){const d=this.nodesMap[e];if(!d)return;const n=d.childNodes;for(let r=n.length-1;r>=0;r--){const s=n[r];this.remove(s.data)}for(let r=0,s=o.length;r<s;r++){const a=o[r];this.append(a,d.data)}}_setCheckedKeys(e,o=!1,d){const n=this._getAllNodes().sort((u,g)=>u.level-g.level),r=Object.create(null),s=Object.keys(d);n.forEach(u=>u.setChecked(!1,!1));const a=u=>{u.childNodes.forEach(g=>{var h;r[g.data[e]]=!0,(h=g.childNodes)!=null&&h.length&&a(g)})};for(let u=0,g=n.length;u<g;u++){const h=n[u],c=h.data[e].toString();if(!s.includes(c)){h.checked&&!r[c]&&h.setChecked(!1,!1);continue}if(h.childNodes.length&&a(h),h.isLeaf||this.checkStrictly){h.setChecked(!0,!1);continue}if(h.setChecked(!0,!0),o){h.setChecked(!1,!1);const p=function(N){N.childNodes.forEach(C=>{C.isLeaf||C.setChecked(!1,!1),p(C)})};p(h)}}}setCheckedNodes(e,o=!1){const d=this.key,n={};e.forEach(r=>{n[(r||{})[d]]=!0}),this._setCheckedKeys(d,o,n)}setCheckedKeys(e,o=!1){this.defaultCheckedKeys=e;const d=this.key,n={};e.forEach(r=>{n[r]=!0}),this._setCheckedKeys(d,o,n)}setDefaultExpandedKeys(e){e=e||[],this.defaultExpandedKeys=e,e.forEach(o=>{const d=this.getNode(o);d&&d.expand(null,this.autoExpandParent)})}setChecked(e,o,d){const n=this.getNode(e);n&&n.setChecked(!!o,d)}getCurrentNode(){return this.currentNode}setCurrentNode(e){const o=this.currentNode;o&&(o.isCurrent=!1),this.currentNode=e,this.currentNode.isCurrent=!0}setUserCurrentNode(e,o=!0){const d=e[this.key],n=this.nodesMap[d];this.setCurrentNode(n),o&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0)}setCurrentNodeKey(e,o=!0){if(e==null){this.currentNode&&(this.currentNode.isCurrent=!1),this.currentNode=null;return}const d=this.getNode(e);d&&(this.setCurrentNode(d),o&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0))}}const Xe=_({name:"ElTreeNodeContent",props:{node:{type:Object,required:!0},renderContent:Function},setup(t){const e=Q("tree"),o=Y("NodeInstance"),d=Y("RootTree");return()=>{const n=t.node,{data:r,store:s}=n;return t.renderContent?t.renderContent(le,{_self:o,node:n,data:r,store:s}):fe(d.ctx.slots,"default",{node:n,data:r},()=>[le("span",{class:e.be("node","label")},[n.label])])}}});var Ze=ae(Xe,[["__file","tree-node-content.vue"]]);function Ce(t){const e=Y("TreeNodeMap",null),o={treeNodeExpand:d=>{t.node!==d&&t.node.collapse()},children:[]};return e&&e.children.push(o),W("TreeNodeMap",o),{broadcastExpanded:d=>{if(t.accordion)for(const n of o.children)n.treeNodeExpand(d)}}}const ve=Symbol("dragEvents");function Ge({props:t,ctx:e,el$:o,dropIndicator$:d,store:n}){const r=Q("tree"),s=K({showDropIndicator:!1,draggingNode:null,dropNode:null,allowDrop:!0,dropType:null});return W(ve,{treeNodeDragStart:({event:h,treeNode:c})=>{if(typeof t.allowDrag=="function"&&!t.allowDrag(c.node))return h.preventDefault(),!1;h.dataTransfer.effectAllowed="move";try{h.dataTransfer.setData("text/plain","")}catch{}s.value.draggingNode=c,e.emit("node-drag-start",c.node,h)},treeNodeDragOver:({event:h,treeNode:c})=>{const i=c,p=s.value.dropNode;p&&p.node.id!==i.node.id&&te(p.$el,r.is("drop-inner"));const N=s.value.draggingNode;if(!N||!i)return;let k=!0,C=!0,b=!0,A=!0;typeof t.allowDrop=="function"&&(k=t.allowDrop(N.node,i.node,"prev"),A=C=t.allowDrop(N.node,i.node,"inner"),b=t.allowDrop(N.node,i.node,"next")),h.dataTransfer.dropEffect=C||k||b?"move":"none",(k||C||b)&&(p==null?void 0:p.node.id)!==i.node.id&&(p&&e.emit("node-drag-leave",N.node,p.node,h),e.emit("node-drag-enter",N.node,i.node,h)),k||C||b?s.value.dropNode=i:s.value.dropNode=null,i.node.nextSibling===N.node&&(b=!1),i.node.previousSibling===N.node&&(k=!1),i.node.contains(N.node,!1)&&(C=!1),(N.node===i.node||N.node.contains(i.node))&&(k=!1,C=!1,b=!1);const x=i.$el.querySelector(`.${r.be("node","content")}`).getBoundingClientRect(),M=o.value.getBoundingClientRect();let E;const V=k?C?.25:b?.45:1:-1,X=b?C?.75:k?.55:0:1;let q=-9999;const f=h.clientY-x.top;f<x.height*V?E="before":f>x.height*X?E="after":C?E="inner":E="none";const v=i.$el.querySelector(`.${r.be("node","expand-icon")}`).getBoundingClientRect(),m=d.value;E==="before"?q=v.top-M.top:E==="after"&&(q=v.bottom-M.top),m.style.top=`${q}px`,m.style.left=`${v.right-M.left}px`,E==="inner"?Le(i.$el,r.is("drop-inner")):te(i.$el,r.is("drop-inner")),s.value.showDropIndicator=E==="before"||E==="after",s.value.allowDrop=s.value.showDropIndicator||A,s.value.dropType=E,e.emit("node-drag-over",N.node,i.node,h)},treeNodeDragEnd:h=>{const{draggingNode:c,dropType:i,dropNode:p}=s.value;if(h.preventDefault(),h.dataTransfer.dropEffect="move",c&&p){const N={data:c.node.data};i!=="none"&&c.node.remove(),i==="before"?p.node.parent.insertBefore(N,p.node):i==="after"?p.node.parent.insertAfter(N,p.node):i==="inner"&&p.node.insertChild(N),i!=="none"&&(n.value.registerNode(N),n.value.key&&c.node.eachNode(k=>{var C;(C=n.value.nodesMap[k.data[n.value.key]])==null||C.setChecked(k.checked,!n.value.checkStrictly)})),te(p.$el,r.is("drop-inner")),e.emit("node-drag-end",c.node,p.node,i,h),i!=="none"&&e.emit("node-drop",c.node,p.node,i,h)}c&&!p&&e.emit("node-drag-end",c.node,null,i,h),s.value.showDropIndicator=!1,s.value.draggingNode=null,s.value.dropNode=null,s.value.allowDrop=!0}}),{dragState:s}}const _e=_({name:"ElTreeNode",components:{ElCollapseTransition:Ue,ElCheckbox:Je,NodeContent:Ze,ElIcon:Me,Loading:qe},props:{node:{type:L,default:()=>({})},props:{type:Object,default:()=>({})},accordion:Boolean,renderContent:Function,renderAfterExpand:Boolean,showCheckbox:{type:Boolean,default:!1}},emits:["node-expand"],setup(t,e){const o=Q("tree"),{broadcastExpanded:d}=Ce(t),n=Y("RootTree"),r=K(!1),s=K(!1),a=K(null),u=K(null),g=K(null),h=Y(ve),c=ye();W("NodeInstance",c),t.node.expanded&&(r.value=!0,s.value=!0);const i=n.props.props.children||"children";w(()=>{const f=t.node.data[i];return f&&[...f]},()=>{t.node.updateChildren()}),w(()=>t.node.indeterminate,f=>{k(t.node.checked,f)}),w(()=>t.node.checked,f=>{k(f,t.node.indeterminate)}),w(()=>t.node.childNodes.length,()=>t.node.reInitChecked()),w(()=>t.node.expanded,f=>{ce(()=>r.value=f),f&&(s.value=!0)});const p=f=>ie(n.props.nodeKey,f.data),N=f=>{const v=t.props.class;if(!v)return{};let m;if(Te(v)){const{data:ee}=f;m=v(ee,f)}else m=v;return Ie(m)?{[m]:!0}:m},k=(f,v)=>{(a.value!==f||u.value!==v)&&n.ctx.emit("check-change",t.node.data,f,v),a.value=f,u.value=v},C=f=>{re(n.store,n.ctx.emit,()=>n.store.value.setCurrentNode(t.node)),n.currentNode.value=t.node,n.props.expandOnClickNode&&A(),n.props.checkOnClickNode&&!t.node.disabled&&x(null,{target:{checked:!t.node.checked}}),n.ctx.emit("node-click",t.node.data,t.node,c,f)},b=f=>{n.instance.vnode.props.onNodeContextmenu&&(f.stopPropagation(),f.preventDefault()),n.ctx.emit("node-contextmenu",f,t.node.data,t.node,c)},A=()=>{t.node.isLeaf||(r.value?(n.ctx.emit("node-collapse",t.node.data,t.node,c),t.node.collapse()):(t.node.expand(),e.emit("node-expand",t.node.data,t.node,c)))},x=(f,v)=>{t.node.setChecked(v.target.checked,!n.props.checkStrictly),ce(()=>{const m=n.store.value;n.ctx.emit("check",t.node.data,{checkedNodes:m.getCheckedNodes(),checkedKeys:m.getCheckedKeys(),halfCheckedNodes:m.getHalfCheckedNodes(),halfCheckedKeys:m.getHalfCheckedKeys()})})};return{ns:o,node$:g,tree:n,expanded:r,childNodeRendered:s,oldChecked:a,oldIndeterminate:u,getNodeKey:p,getNodeClass:N,handleSelectChange:k,handleClick:C,handleContextMenu:b,handleExpandIconClick:A,handleCheckChange:x,handleChildNodeExpand:(f,v,m)=>{d(v),n.ctx.emit("node-expand",f,v,m)},handleDragStart:f=>{n.props.draggable&&h.treeNodeDragStart({event:f,treeNode:t})},handleDragOver:f=>{f.preventDefault(),n.props.draggable&&h.treeNodeDragOver({event:f,treeNode:{$el:g.value,node:t.node}})},handleDrop:f=>{f.preventDefault()},handleDragEnd:f=>{n.props.draggable&&h.treeNodeDragEnd(f)},CaretRight:ze}}}),et=["aria-expanded","aria-disabled","aria-checked","draggable","data-key"],tt=["aria-expanded"];function nt(t,e,o,d,n,r){const s=O("el-icon"),a=O("el-checkbox"),u=O("loading"),g=O("node-content"),h=O("el-tree-node"),c=O("el-collapse-transition");return oe((D(),F("div",{ref:"node$",class:S([t.ns.b("node"),t.ns.is("expanded",t.expanded),t.ns.is("current",t.node.isCurrent),t.ns.is("hidden",!t.node.visible),t.ns.is("focusable",!t.node.disabled),t.ns.is("checked",!t.node.disabled&&t.node.checked),t.getNodeClass(t.node)]),role:"treeitem",tabindex:"-1","aria-expanded":t.expanded,"aria-disabled":t.node.disabled,"aria-checked":t.node.checked,draggable:t.tree.props.draggable,"data-key":t.getNodeKey(t.node),onClick:e[1]||(e[1]=T((...i)=>t.handleClick&&t.handleClick(...i),["stop"])),onContextmenu:e[2]||(e[2]=(...i)=>t.handleContextMenu&&t.handleContextMenu(...i)),onDragstart:e[3]||(e[3]=T((...i)=>t.handleDragStart&&t.handleDragStart(...i),["stop"])),onDragover:e[4]||(e[4]=T((...i)=>t.handleDragOver&&t.handleDragOver(...i),["stop"])),onDragend:e[5]||(e[5]=T((...i)=>t.handleDragEnd&&t.handleDragEnd(...i),["stop"])),onDrop:e[6]||(e[6]=T((...i)=>t.handleDrop&&t.handleDrop(...i),["stop"]))},[$("div",{class:S(t.ns.be("node","content")),style:Ae({paddingLeft:(t.node.level-1)*t.tree.props.indent+"px"})},[t.tree.props.icon||t.CaretRight?(D(),B(s,{key:0,class:S([t.ns.be("node","expand-icon"),t.ns.is("leaf",t.node.isLeaf),{expanded:!t.node.isLeaf&&t.expanded}]),onClick:T(t.handleExpandIconClick,["stop"])},{default:P(()=>[(D(),B(xe(t.tree.props.icon||t.CaretRight)))]),_:1},8,["class","onClick"])):R("v-if",!0),t.showCheckbox?(D(),B(a,{key:1,"model-value":t.node.checked,indeterminate:t.node.indeterminate,disabled:!!t.node.disabled,onClick:e[0]||(e[0]=T(()=>{},["stop"])),onChange:t.handleCheckChange},null,8,["model-value","indeterminate","disabled","onChange"])):R("v-if",!0),t.node.loading?(D(),B(s,{key:2,class:S([t.ns.be("node","loading-icon"),t.ns.is("loading")])},{default:P(()=>[J(u)]),_:1},8,["class"])):R("v-if",!0),J(g,{node:t.node,"render-content":t.renderContent},null,8,["node","render-content"])],6),J(c,null,{default:P(()=>[!t.renderAfterExpand||t.childNodeRendered?oe((D(),F("div",{key:0,class:S(t.ns.be("node","children")),role:"group","aria-expanded":t.expanded},[(D(!0),F(pe,null,ge(t.node.childNodes,i=>(D(),B(h,{key:t.getNodeKey(i),"render-content":t.renderContent,"render-after-expand":t.renderAfterExpand,"show-checkbox":t.showCheckbox,node:i,accordion:t.accordion,props:t.props,onNodeExpand:t.handleChildNodeExpand},null,8,["render-content","render-after-expand","show-checkbox","node","accordion","props","onNodeExpand"]))),128))],10,tt)),[[de,t.expanded]]):R("v-if",!0)]),_:1})],42,et)),[[de,t.node.visible]])}var ot=ae(_e,[["render",nt],["__file","tree-node.vue"]]);function dt({el$:t},e){const o=Q("tree"),d=he([]),n=he([]);Oe(()=>{s()}),Be(()=>{d.value=Array.from(t.value.querySelectorAll("[role=treeitem]")),n.value=Array.from(t.value.querySelectorAll("input[type=checkbox]"))}),w(n,a=>{a.forEach(u=>{u.setAttribute("tabindex","-1")})}),Fe(t,"keydown",a=>{const u=a.target;if(!u.className.includes(o.b("node")))return;const g=a.code;d.value=Array.from(t.value.querySelectorAll(`.${o.is("focusable")}[role=treeitem]`));const h=d.value.indexOf(u);let c;if([I.up,I.down].includes(g)){if(a.preventDefault(),g===I.up){c=h===-1?0:h!==0?h-1:d.value.length-1;const p=c;for(;!e.value.getNode(d.value[c].dataset.key).canFocus;){if(c--,c===p){c=-1;break}c<0&&(c=d.value.length-1)}}else{c=h===-1?0:h<d.value.length-1?h+1:0;const p=c;for(;!e.value.getNode(d.value[c].dataset.key).canFocus;){if(c++,c===p){c=-1;break}c>=d.value.length&&(c=0)}}c!==-1&&d.value[c].focus()}[I.left,I.right].includes(g)&&(a.preventDefault(),u.click());const i=u.querySelector('[type="checkbox"]');[I.enter,I.space].includes(g)&&i&&(a.preventDefault(),i.click())});const s=()=>{var a;d.value=Array.from(t.value.querySelectorAll(`.${o.is("focusable")}[role=treeitem]`)),n.value=Array.from(t.value.querySelectorAll("input[type=checkbox]"));const u=t.value.querySelectorAll(`.${o.is("checked")}[role=treeitem]`);if(u.length){u[0].setAttribute("tabindex","0");return}(a=d.value[0])==null||a.setAttribute("tabindex","0")}}const rt=_({name:"ElTree",components:{ElTreeNode:ot},props:{data:{type:Array,default:()=>[]},emptyText:{type:String},renderAfterExpand:{type:Boolean,default:!0},nodeKey:String,checkStrictly:Boolean,defaultExpandAll:Boolean,expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:Boolean,checkDescendants:{type:Boolean,default:!1},autoExpandParent:{type:Boolean,default:!0},defaultCheckedKeys:Array,defaultExpandedKeys:Array,currentNodeKey:[String,Number],renderContent:Function,showCheckbox:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1},allowDrag:Function,allowDrop:Function,props:{type:Object,default:()=>({children:"children",label:"label",disabled:"disabled"})},lazy:{type:Boolean,default:!1},highlightCurrent:Boolean,load:Function,filterNodeMethod:Function,accordion:Boolean,indent:{type:Number,default:18},icon:{type:Pe}},emits:["check-change","current-change","node-click","node-contextmenu","node-collapse","node-expand","check","node-drag-start","node-drag-end","node-drop","node-drag-leave","node-drag-enter","node-drag-over"],setup(t,e){const{t:o}=je(),d=Q("tree"),n=K(new Ve({key:t.nodeKey,data:t.data,lazy:t.lazy,props:t.props,load:t.load,currentNodeKey:t.currentNodeKey,checkStrictly:t.checkStrictly,checkDescendants:t.checkDescendants,defaultCheckedKeys:t.defaultCheckedKeys,defaultExpandedKeys:t.defaultExpandedKeys,autoExpandParent:t.autoExpandParent,defaultExpandAll:t.defaultExpandAll,filterNodeMethod:t.filterNodeMethod}));n.value.initialize();const r=K(n.value.root),s=K(null),a=K(null),u=K(null),{broadcastExpanded:g}=Ce(t),{dragState:h}=Ge({props:t,ctx:e,el$:a,dropIndicator$:u,store:n});dt({el$:a},n);const c=Ne(()=>{const{childNodes:l}=r.value;return!l||l.length===0||l.every(({visible:y})=>!y)});w(()=>t.currentNodeKey,l=>{n.value.setCurrentNodeKey(l)}),w(()=>t.defaultCheckedKeys,l=>{n.value.setDefaultCheckedKey(l)}),w(()=>t.defaultExpandedKeys,l=>{n.value.setDefaultExpandedKeys(l)}),w(()=>t.data,l=>{n.value.setData(l)},{deep:!0}),w(()=>t.checkStrictly,l=>{n.value.checkStrictly=l});const i=l=>{if(!t.filterNodeMethod)throw new Error("[Tree] filterNodeMethod is required when filter");n.value.filter(l)},p=l=>ie(t.nodeKey,l.data),N=l=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in getNodePath");const y=n.value.getNode(l);if(!y)return[];const z=[y.data];let H=y.parent;for(;H&&H!==r.value;)z.push(H.data),H=H.parent;return z.reverse()},k=(l,y)=>n.value.getCheckedNodes(l,y),C=l=>n.value.getCheckedKeys(l),b=()=>{const l=n.value.getCurrentNode();return l?l.data:null},A=()=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in getCurrentKey");const l=b();return l?l[t.nodeKey]:null},x=(l,y)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedNodes");n.value.setCheckedNodes(l,y)},M=(l,y)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedKeys");n.value.setCheckedKeys(l,y)},E=(l,y,z)=>{n.value.setChecked(l,y,z)},V=()=>n.value.getHalfCheckedNodes(),X=()=>n.value.getHalfCheckedKeys(),q=(l,y=!0)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentNode");re(n,e.emit,()=>n.value.setUserCurrentNode(l,y))},f=(l,y=!0)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentKey");re(n,e.emit,()=>n.value.setCurrentNodeKey(l,y))},v=l=>n.value.getNode(l),m=l=>{n.value.remove(l)},ee=(l,y)=>{n.value.append(l,y)},me=(l,y)=>{n.value.insertBefore(l,y)},be=(l,y)=>{n.value.insertAfter(l,y)},Ee=(l,y,z)=>{g(y),e.emit("node-expand",l,y,z)},De=(l,y)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in updateKeyChild");n.value.updateChildren(l,y)};return W("RootTree",{ctx:e,props:t,store:n,root:r,currentNode:s,instance:ye()}),W(He,void 0),{ns:d,store:n,root:r,currentNode:s,dragState:h,el$:a,dropIndicator$:u,isEmpty:c,filter:i,getNodeKey:p,getNodePath:N,getCheckedNodes:k,getCheckedKeys:C,getCurrentNode:b,getCurrentKey:A,setCheckedNodes:x,setCheckedKeys:M,setChecked:E,getHalfCheckedNodes:V,getHalfCheckedKeys:X,setCurrentNode:q,setCurrentKey:f,t:o,getNode:v,remove:m,append:ee,insertBefore:me,insertAfter:be,handleNodeExpand:Ee,updateKeyChildren:De}}});function st(t,e,o,d,n,r){const s=O("el-tree-node");return D(),F("div",{ref:"el$",class:S([t.ns.b(),t.ns.is("dragging",!!t.dragState.draggingNode),t.ns.is("drop-not-allow",!t.dragState.allowDrop),t.ns.is("drop-inner",t.dragState.dropType==="inner"),{[t.ns.m("highlight-current")]:t.highlightCurrent}]),role:"tree"},[(D(!0),F(pe,null,ge(t.root.childNodes,a=>(D(),B(s,{key:t.getNodeKey(a),node:a,props:t.props,accordion:t.accordion,"render-after-expand":t.renderAfterExpand,"show-checkbox":t.showCheckbox,"render-content":t.renderContent,onNodeExpand:t.handleNodeExpand},null,8,["node","props","accordion","render-after-expand","show-checkbox","render-content","onNodeExpand"]))),128)),t.isEmpty?(D(),F("div",{key:0,class:S(t.ns.e("empty-block"))},[fe(t.$slots,"empty",{},()=>{var a;return[$("span",{class:S(t.ns.e("empty-text"))},ke((a=t.emptyText)!=null?a:t.t("el.tree.emptyText")),3)]})],2)):R("v-if",!0),oe($("div",{ref:"dropIndicator$",class:S(t.ns.e("drop-indicator"))},null,2),[[de,t.dragState.showDropIndicator]])],2)}var G=ae(rt,[["render",st],["__file","tree.vue"]]);G.install=t=>{t.component(G.name,G)};const at=G,it=at,lt={class:"flex item-center"},ct=_({__name:"outline",props:{content:{default:""},quote:{default:()=>[]}},setup(t){const e=t,o=r=>{const s=/(`{3}[\s\S]*?`{3}(?:(?!.)))|(`{3}[\s\S]*)|(`[\s\S]*?`{1}?)|(`[\s\S]*)|(?:\[(?:(?:number )|\^)?([\d]{1,2})\])/g;return r.replaceAll(s,"")},d=(r,s=[])=>(Object.keys(r).forEach(a=>{const u={label:o(a),children:[]};s.push(u),Re(r[a])?u.children=r[a].map(g=>({label:o(g)})):d(r[a],u.children)}),s),n=Ne(()=>{try{let r=JSON.parse(e.content);return r=d(r),r}catch{return[]}});return(r,s)=>{const a=Ke,u=it;return D(),B(Ye,null,{title:P(()=>[J(a,{name:"local-icon-list-2",size:16}),s[0]||(s[0]=$("span",{class:"text-2xl ml-1"}," 大纲 ",-1))]),default:P(()=>[J(u,{style:{"max-width":"600px"},data:n.value,"node-key":"label","default-expand-all":"","expand-on-click-node":!1},{default:P(({node:g,data:h})=>[$("div",lt,[s[1]||(s[1]=$("span",{class:"mr-1"},"•",-1)),$("span",null,ke(g.label),1)])]),_:1},8,["data"])]),_:1})}}}),mt=We(ct,[["__scopeId","data-v-80d5b3d4"]]);export{mt as default};
