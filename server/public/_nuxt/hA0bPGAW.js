import{E as V}from"./dYUcznge.js";import{_ as B}from"./BNxdmYOC.js";import{_ as D}from"./a_MrTnou.js";import{E as L}from"./CDBp2FXR.js";import{_ as T}from"./Begv_G9N.js";import{_ as N}from"./CN8DIg3d.js";import{a as M,l as O,b as P}from"./CylNgAGi.js";/* empty css        *//* empty css        *//* empty css        */import{u as U}from"./CSOjM5CZ.js";import z from"./DTsnWiEs.js";import{_ as F}from"./BPArIywZ.js";import{_ as J}from"./BdexgSiT.js";import{g as Z}from"./Bo3PTL3c.js";import{u as j}from"./BR1hkMb9.js";import{l as G,b as d,ak as H,c as K,M as a,N as f,Z as m,a0 as s,O as e,u as o,y as w,_ as Q,aq as W,a1 as h,a4 as y,a7 as X}from"./Dp9aCaJ6.js";import{_ as Y}from"./DlAUqK2U.js";import"./DJ1U04Bw.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";import"./Bdk6a5L7.js";import"./9Bti1uB6.js";import"./UGYfuE8C.js";import"./CCrQ70Bv.js";import"./Dke71UIn.js";/* empty css        */import"./DmsLvQBk.js";import"./Cv6HhfEG.js";import"./BRjkVUgK.js";/* empty css        */import"./CdxGqnFp.js";import"./DRScr1jn.js";import"./BiUm2mPE.js";import"./Dq3-3yo4.js";import"./C6z7M7jp.js";import"./BjDNN4zb.js";import"./Db_grgJe.js";import"./CBuTaJhf.js";import"./qb8OCGLI.js";import"./D8pK6L_e.js";import"./CLzOu_sh.js";import"./Om49Ukpp.js";import"./C6mdKn2J.js";import"./DlKZEFPo.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./BNnETjxs.js";import"./DTRTE1rZ.js";import"./DMWQqzlS.js";import"./DuZCf-OL.js";import"./D-bImviZ.js";import"./Za7Ic34B.js";/* empty css        */import"./CHoMW6S7.js";import"./Du5U7mLs.js";import"./D4p_nyQ5.js";import"./MlMCfT1S.js";import"./CGsEtxyu.js";import"./C1u9TGpz.js";/* empty css        *//* empty css        */import"./BcMOgD9v.js";import"./BnqojDrv.js";import"./S96BBjVJ.js";import"./HT4ANvwR.js";import"./4QYjDMhx.js";import"./fZOypmWP.js";import"./BGZ_z1Ox.js";import"./Cg11N2Sp.js";/* empty css        */import"./C6_W4ts7.js";import"./Bnk6gZHw.js";import"./DOf6_-ry.js";import"./D2o9q88N.js";import"./oNDQuFBQ.js";import"./DPTIapjF.js";import"./Dz_NV032.js";/* empty css        *//* empty css        */import"./Bi0_2T0f.js";import"./67xbGseh.js";import"./-8u5pS_P.js";import"./BGQTDGoa.js";import"./B_YHg0zt.js";import"./iRXjSIie.js";import"./CcPlX2kz.js";import"./Dp1MVQvw.js";import"./5s13DQWQ.js";import"./BvtBsGiF.js";import"./Cv-CslZ-.js";import"./8bkz5fAn.js";import"./n0f8TtPz.js";import"./BohYg9ra.js";import"./CAg73TsC.js";import"./BgSv0nTC.js";import"./DHvRs-FZ.js";import"./CP-u1HLg.js";import"./B60jNIJQ.js";import"./CBsO62Hv.js";import"./6vuzXKEq.js";import"./qUJznlAp.js";import"./97HcMQu2.js";import"./B6pB34Di.js";import"./CyS7htb1.js";import"./Cpg3PDWZ.js";const tt={class:"h-full flex"},ot=["onClick"],rt=["src"],et={class:"ml-[8px] line-clamp-1"},it={class:"flex items-center cursor-pointer"},pt={class:"text-xl flex-1 min-w-0"},mt={class:"sm:h-full py-[16px] pr-[16px] flex flex-col sm:flex-row flex-1 min-w-0"},nt={class:"sm:h-full flex-1 min-w-0 min-h-0 bg-body rounded-[12px]"},at=G({__name:"setting",async setup(st){let c,x;const i=M(),u=O();P();const v=j();v.getRobot();const _=d(!1),l=d(i.query.id),{data:b,refresh:k}=([c,x]=H(()=>U(()=>Z({id:l.value}),{transform(t){return(t==null?void 0:t.category_id)===0&&(t.category_id=""),t},default(){return{}},lazy:!0},"$3nIwi7TB6J")),c=await c,x(),c),p=d("edit"),q=[{name:"智能体设置",icon:"el-icon-Setting",key:"edit"},{name:"发布智能体",key:"release",icon:"el-icon-Position"},{name:"对话数据",key:"dialogue",icon:"el-icon-ChatDotRound"},{name:"立即对话",key:"chat",icon:"el-icon-ChatLineRound"}],S=t=>{switch(t){case"chat":u.push({path:"/application/chat",query:{id:l.value}});break;default:u.replace({path:i.path,query:{...i.query,currentTab:t}})}},C=async t=>{_.value=!1,t!=i.query.id&&(l.value=t,await k(),u.replace({path:i.path,query:{...i.query,id:t}}))};return K(()=>i.query,t=>{p.value=t.currentTab||"edit"},{immediate:!0}),(t,n)=>{const g=V,R=B,A=D,E=L,$=T,I=N;return a(),f("div",tt,[m($,{modelValue:o(p),"onUpdate:modelValue":[n[1]||(n[1]=r=>w(p)?p.value=r:null),S],"menu-list":q,"back-path":"/application/layout/robot"},{title:s(()=>[e("div",null,[m(E,{placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:o(_),"onUpdate:visible":n[0]||(n[0]=r=>w(_)?_.value=r:null)},{reference:s(()=>[e("div",it,[e("div",pt,[m(R,{content:o(b).name,teleported:!0,effect:"light"},null,8,["content"])]),m(A,{name:"el-icon-ArrowDown"})])]),default:s(()=>[e("div",null,[m(g,{style:{height:"250px"}},{default:s(()=>[(a(!0),f(Q,null,W(o(v).robotLists,r=>(a(),f("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-primary-light-9 px-[10px] my-[5px] rounded-[12px] hover:text-primary",key:r.id,onClick:lt=>C(r.id)},[e("img",{class:"rounded-[50%] w-[28px] h-[28px] flex-none",src:r.image,alt:""},null,8,rt),e("div",et,X(r.name),1)],8,ot))),128))]),_:1})])]),_:1},8,["visible"])])]),_:1},8,["modelValue"]),e("div",mt,[e("div",nt,[o(p)==="edit"?(a(),h(z,{key:0,"model-value":o(b),onSuccess:n[2]||(n[2]=r=>o(u).push("/application/layout/robot"))},null,8,["model-value"])):y("",!0),m(I,null,{default:s(()=>[o(p)==="release"?(a(),h(g,{key:0},{default:s(()=>[m(F,{"app-id":o(l)},null,8,["app-id"])]),_:1})):y("",!0)]),_:1}),o(p)==="dialogue"?(a(),h(J,{key:1,"app-id":o(l)},null,8,["app-id"])):y("",!0)])])])}}}),ur=Y(at,[["__scopeId","data-v-21d9b61a"]]);export{ur as default};
