import{_ as h}from"./C_7xENts.js";import{e as U,o as z,p as M,h as N,b as O,f as j,E as L}from"./CzTOiozM.js";import"./DP2rzg_V.js";/* empty css        */import{l as y,m as E,M as u,a1 as i,W as g,u as _,y as k,ar as D,a0 as V,N as x,_ as B,aq as C,j as F,b as P,c as T,a3 as q,a9 as G,O as b,a7 as A,a4 as I,Z as S,a6 as w,V as K}from"./Dp9aCaJ6.js";import{E as Z,a as H}from"./CLdudPaH.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{_ as $}from"./DlAUqK2U.js";import{b as J,a as Q}from"./BL75K97j.js";/* empty css        */import{a as X,b as Y}from"./Bvuso-iR.js";import{E as ee}from"./CH0sQbfA.js";import"./CaD5i18d.js";import"./DCTLXrZ8.js";import"./4kyntJ1V.js";import"./Cv6HhfEG.js";import"./CmG-gwTN.js";import"./v-ANcAMI.js";import"./D13MzIoa.js";import"./CQleycmY.js";const te=y({__name:"input",emits:["update:modelValue"],setup(d,{emit:p}){const l=d,r=p,o=E({get(){return l.modelValue},set(e){r("update:modelValue",e)}});return(e,a)=>{const m=U;return u(),i(m,g(l,{modelValue:_(o),"onUpdate:modelValue":a[0]||(a[0]=s=>k(o)?o.value=s:null)}),null,16,["modelValue"])}}}),oe=y({__name:"textarea",props:{autosize:{type:[Boolean,Object]}},emits:["update:modelValue"],setup(d,{emit:p}){const l=d,r=p,o=E({get(){return l.modelValue},set(e){r("update:modelValue",e)}});return(e,a)=>{const m=U;return u(),i(m,g(l,{modelValue:_(o),"onUpdate:modelValue":a[0]||(a[0]=s=>k(o)?o.value=s:null),type:"textarea",autosize:e.autosize?{minRows:2}:!1}),null,16,["modelValue","autosize"])}}}),le=y({__name:"select",props:{defaultValue:{},modelValue:{},options:{default:()=>[]}},emits:["update:modelValue"],setup(d,{emit:p}){const l=d,r=p,o=E({get(){return l.modelValue},set(e){r("update:modelValue",e)}});return D(()=>{l.defaultValue&&(o.value=l.defaultValue)}),(e,a)=>{const m=Z,s=H;return u(),i(s,g(e.$attrs,{modelValue:_(o),"onUpdate:modelValue":a[0]||(a[0]=t=>k(o)?o.value=t:null)}),{default:V(()=>[(u(!0),x(B,null,C(e.options,(t,n)=>(u(),i(m,{key:n,label:t,value:t},null,8,["label","value"]))),128))]),_:1},16,["modelValue"])}}}),ae=$(le,[["__scopeId","data-v-bf7a521e"]]),se=y({__name:"radio",props:{modelValue:{},options:{},defaultValue:{}},emits:["update:modelValue"],setup(d,{emit:p}){const l=d,r=p,o=E({get(){return l.modelValue},set(e){r("update:modelValue",e)}});return D(()=>{l.defaultValue&&(o.value=l.defaultValue)}),(e,a)=>{const m=J,s=Q;return u(),i(s,g(e.$attrs,{modelValue:_(o),"onUpdate:modelValue":a[0]||(a[0]=t=>k(o)?o.value=t:null)}),{default:V(()=>[(u(!0),x(B,null,C(e.options,(t,n)=>(u(),i(m,{key:n,label:t,name:t},null,8,["label","name"]))),128))]),_:1},16,["modelValue"])}}}),ne=$(se,[["__scopeId","data-v-231e96fe"]]),re=y({__name:"checkbox",props:{modelValue:{},options:{},defaultValue:{}},emits:["update:modelValue"],setup(d,{emit:p}){const l=d,r=p,o=E({get(){return l.modelValue},set(e){r("update:modelValue",e)}});return D(()=>{l.defaultValue&&(o.value=l.defaultValue)}),(e,a)=>{const m=X,s=Y;return u(),i(s,g(e.$attrs,{modelValue:_(o),"onUpdate:modelValue":a[0]||(a[0]=t=>k(o)?o.value=t:null)}),{default:V(()=>[(u(!0),x(B,null,C(e.options,(t,n)=>(u(),i(m,{key:n,label:t,name:t},null,8,["label","name"]))),128))]),_:1},16,["modelValue"])}}}),ue=$(re,[["__scopeId","data-v-71662013"]]),me=Object.freeze(Object.defineProperty({__proto__:null,WidgetCheckbox:ue,WidgetInput:te,WidgetRadio:ne,WidgetSelect:ae,WidgetTextarea:oe},Symbol.toStringTag,{value:"Module"})),de=y({__name:"index",props:{formLists:{default:()=>[]},modelValue:{}},emits:["update:modelValue"],setup(d,{expose:p,emit:l}){const r=d,o=l,e=F(),a=E({get(){return r.modelValue},set(n){o("update:modelValue",n)}}),m=P({}),s=n=>me[n],t=async()=>{var n;await((n=e.value)==null?void 0:n.validate())};return T(()=>r.formLists,async n=>{m.value=n==null?void 0:n.reduce((c,v)=>(a.value[v.props.field]=void 0,v.props.isRequired&&(c[v.props.field]=[{required:!0,message:"必填项不能为空",trigger:"blur"}]),c),{}),setTimeout(()=>{var c;(c=e.value)==null||c.resetFields()})}),p({validate:t}),(n,c)=>{const v=z,R=M;return u(),i(R,g({ref_key:"formRef",ref:e},r,{rules:_(m),model:_(a),labelPosition:"top",onSubmit:c[0]||(c[0]=G(()=>{},["prevent"]))}),{default:V(()=>[(u(!0),x(B,null,C(n.formLists,f=>(u(),i(v,{key:f.id,prop:f.props.field,label:f.props.title},{default:V(()=>[(u(),i(q(s(f.name)),g({ref_for:!0},f.props,{modelValue:_(a)[f.props.field],"onUpdate:modelValue":W=>_(a)[f.props.field]=W}),null,16,["modelValue","onUpdate:modelValue"]))]),_:2},1032,["prop","label"]))),128))]),_:1},16,["rules","model"])}}}),pe={class:"flex flex-col p-[16px] flex-1"},ie={class:"flex pb-[20px]"},ce={class:"line-clamp-1"},fe={class:"flex-1 min-h-0"},_e={class:""},Ve={class:"flex flex-col justify-center items-center"},ve=y({__name:"create-panel",props:{modelData:{},modelValue:{},loading:{type:Boolean}},emits:["update:modelValue","insert","create","select","update:modelKey"],setup(d,{emit:p}){const l=d,r=p,o=F(),e=N(l,"modelValue",r),a=O(),m=async()=>{var s;try{await((s=o.value)==null?void 0:s.validate())}catch{j.msgError("必填项不能为空");return}r("create")};return(s,t)=>{const n=h,c=L,v=de,R=ee;return u(),x("div",pe,[b("div",ie,[b("div",{class:"text-lg font-medium flex flex-1 min-w-0 items-center mr-auto",onClick:t[0]||(t[0]=f=>r("select"))},[b("span",ce,A(s.modelData.name),1),_(a).isMobile?(u(),i(n,{key:0,name:"el-icon-CaretBottom"})):I("",!0)]),S(c,{link:"",type:"primary",onClick:t[1]||(t[1]=f=>r("insert"))},{default:V(()=>t[3]||(t[3]=[w(" 插入示例 ")])),_:1})]),b("div",fe,[S(R,null,{default:V(()=>[b("div",_e,[S(v,{ref_key:"formDesignerRef",ref:o,modelValue:_(e),"onUpdate:modelValue":t[2]||(t[2]=f=>k(e)?e.value=f:null),"form-lists":s.modelData.form,size:"large"},null,8,["modelValue","form-lists"])])]),_:1})]),b("div",null,[b("div",Ve,[K(s.$slots,"actions",{},void 0,!0),s.modelData.id?(u(),i(c,{key:0,class:"create-btn",type:"primary",loading:s.loading,onClick:m},{default:V(()=>t[4]||(t[4]=[w(" 智能创作 ")])),_:1},8,["loading"])):I("",!0)])])])}}}),Le=$(ve,[["__scopeId","data-v-eddebddc"]]);export{Le as default};
