import{af as T,bO as Q,O as me,M as w,bP as J,bQ as ae,bR as Pe,bS as _e,K as pe,al as V,U as K,aU as Ae,a8 as Be,J as Ce,av as Ie,X as ie,a4 as ze,bT as Le,bU as We,aI as ye,a0 as Re,P as De,Q as ve}from"./CylNgAGi.js";import{l as q,M as R,a1 as Se,a0 as re,V as A,W as je,ac as Fe,m as f,i as Z,b as x,r as fe,c as X,q as ce,F as he,o as xe,p as g,H as D,aa as ke,ab as Ve,_ as be,a as Me,w as qe,J as Ue,n as Ge,t as He,ah as Je,N as Y,O as de,X as ee,a6 as Qe,a7 as Xe}from"./Dp9aCaJ6.js";import{_ as Ke}from"./CCrQ70Bv.js";import{E as Ee}from"./DJ1U04Bw.js";import{C as Ze}from"./Dke71UIn.js";let Ye=class{constructor(n,t){this.parent=n,this.domNode=t,this.subIndex=0,this.subIndex=0,this.init()}init(){this.subMenuItems=this.domNode.querySelectorAll("li"),this.addListeners()}gotoSubIndex(n){n===this.subMenuItems.length?n=0:n<0&&(n=this.subMenuItems.length-1),this.subMenuItems[n].focus(),this.subIndex=n}addListeners(){const n=this.parent.domNode;Array.prototype.forEach.call(this.subMenuItems,t=>{t.addEventListener("keydown",r=>{let i=!1;switch(r.code){case T.down:{this.gotoSubIndex(this.subIndex+1),i=!0;break}case T.up:{this.gotoSubIndex(this.subIndex-1),i=!0;break}case T.tab:{Q(n,"mouseleave");break}case T.enter:case T.space:{i=!0,r.currentTarget.click();break}}return i&&(r.preventDefault(),r.stopPropagation()),!1})})}},et=class{constructor(n,t){this.domNode=n,this.submenu=null,this.submenu=null,this.init(t)}init(n){this.domNode.setAttribute("tabindex","0");const t=this.domNode.querySelector(`.${n}-menu`);t&&(this.submenu=new Ye(this,t)),this.addListeners()}addListeners(){this.domNode.addEventListener("keydown",n=>{let t=!1;switch(n.code){case T.down:{Q(n.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(0),t=!0;break}case T.up:{Q(n.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(this.submenu.subMenuItems.length-1),t=!0;break}case T.tab:{Q(n.currentTarget,"mouseleave");break}case T.enter:case T.space:{t=!0,n.currentTarget.click();break}}t&&n.preventDefault()})}},tt=class{constructor(n,t){this.domNode=n,this.init(t)}init(n){const t=this.domNode.childNodes;Array.from(t).forEach(r=>{r.nodeType===1&&new et(r,n)})}};const nt=q({name:"ElMenuCollapseTransition",setup(){const e=w("menu");return{listeners:{onBeforeEnter:t=>t.style.opacity="0.2",onEnter(t,r){J(t,`${e.namespace.value}-opacity-transition`),t.style.opacity="1",r()},onAfterEnter(t){ae(t,`${e.namespace.value}-opacity-transition`),t.style.opacity=""},onBeforeLeave(t){t.dataset||(t.dataset={}),Pe(t,e.m("collapse"))?(ae(t,e.m("collapse")),t.dataset.oldOverflow=t.style.overflow,t.dataset.scrollWidth=t.clientWidth.toString(),J(t,e.m("collapse"))):(J(t,e.m("collapse")),t.dataset.oldOverflow=t.style.overflow,t.dataset.scrollWidth=t.clientWidth.toString(),ae(t,e.m("collapse"))),t.style.width=`${t.scrollWidth}px`,t.style.overflow="hidden"},onLeave(t){J(t,"horizontal-collapse-transition"),t.style.width=`${t.dataset.scrollWidth}px`}}}}});function ot(e,n,t,r,i,C){return R(),Se(Fe,je({mode:"out-in"},e.listeners),{default:re(()=>[A(e.$slots,"default")]),_:3},16)}var at=me(nt,[["render",ot],["__file","menu-collapse-transition.vue"]]);function Te(e,n){const t=f(()=>{let i=e.parent;const C=[n.value];for(;i.type.name!=="ElMenu";)i.props.index&&C.unshift(i.props.index),i=i.parent;return C});return{parentMenu:f(()=>{let i=e.parent;for(;i&&!["ElMenu","ElSubMenu"].includes(i.type.name);)i=i.parent;return i}),indexPath:t}}function st(e){return f(()=>{const t=e.backgroundColor;return t?new _e(t).shade(20).toString():""})}const Oe=(e,n)=>{const t=w("menu");return f(()=>t.cssVarBlock({"text-color":e.textColor||"","hover-text-color":e.textColor||"","bg-color":e.backgroundColor||"","hover-bg-color":st(e).value||"","active-color":e.activeTextColor||"",level:`${n}`}))},lt=pe({index:{type:String,required:!0},showTimeout:Number,hideTimeout:Number,popperClass:String,disabled:Boolean,teleported:{type:Boolean,default:void 0},popperOffset:Number,expandCloseIcon:{type:V},expandOpenIcon:{type:V},collapseCloseIcon:{type:V},collapseOpenIcon:{type:V}}),se="ElSubMenu";var ge=q({name:se,props:lt,setup(e,{slots:n,expose:t}){const r=Me(),{indexPath:i,parentMenu:C}=Te(r,f(()=>e.index)),m=w("menu"),b=w("sub-menu"),l=Z("rootMenu");l||K(se,"can not inject root menu");const d=Z(`subMenu:${C.value.uid}`);d||K(se,"can not inject sub menu");const p=x({}),I=x({});let y;const O=x(!1),te=x(),U=x(null),N=f(()=>_.value==="horizontal"&&P.value?"bottom-start":"right-start"),B=f(()=>_.value==="horizontal"&&P.value||_.value==="vertical"&&!l.props.collapse?e.expandCloseIcon&&e.expandOpenIcon?k.value?e.expandOpenIcon:e.expandCloseIcon:Ae:e.collapseCloseIcon&&e.collapseOpenIcon?k.value?e.collapseOpenIcon:e.collapseCloseIcon:Be),P=f(()=>d.level===0),j=f(()=>{const s=e.teleported;return s===void 0?P.value:s}),ne=f(()=>l.props.collapse?`${m.namespace.value}-zoom-in-left`:`${m.namespace.value}-zoom-in-top`),oe=f(()=>_.value==="horizontal"&&P.value?["bottom-start","bottom-end","top-start","top-end","right-start","left-start"]:["right-start","right","right-end","left-start","bottom-start","bottom-end","top-start","top-end"]),k=f(()=>l.openedMenus.includes(e.index)),z=f(()=>{let s=!1;return Object.values(p.value).forEach(c=>{c.active&&(s=!0)}),Object.values(I.value).forEach(c=>{c.active&&(s=!0)}),s}),_=f(()=>l.props.mode),L=fe({index:e.index,indexPath:i,active:z}),F=Oe(l.props,d.level+1),G=f(()=>{var s;return(s=e.popperOffset)!=null?s:l.props.popperOffset}),W=f(()=>{var s;return(s=e.popperClass)!=null?s:l.props.popperClass}),H=f(()=>{var s;return(s=e.showTimeout)!=null?s:l.props.showTimeout}),o=f(()=>{var s;return(s=e.hideTimeout)!=null?s:l.props.hideTimeout}),a=()=>{var s,c,M;return(M=(c=(s=U.value)==null?void 0:s.popperRef)==null?void 0:c.popperInstanceRef)==null?void 0:M.destroy()},u=s=>{s||a()},h=()=>{l.props.menuTrigger==="hover"&&l.props.mode==="horizontal"||l.props.collapse&&l.props.mode==="vertical"||e.disabled||l.handleSubMenuClick({index:e.index,indexPath:i.value,active:z.value})},v=(s,c=H.value)=>{var M;if(s.type!=="focus"){if(l.props.menuTrigger==="click"&&l.props.mode==="horizontal"||!l.props.collapse&&l.props.mode==="vertical"||e.disabled){d.mouseInChild.value=!0;return}d.mouseInChild.value=!0,y==null||y(),{stop:y}=Ie(()=>{l.openMenu(e.index,i.value)},c),j.value&&((M=C.value.vnode.el)==null||M.dispatchEvent(new MouseEvent("mouseenter")))}},E=(s=!1)=>{var c;if(l.props.menuTrigger==="click"&&l.props.mode==="horizontal"||!l.props.collapse&&l.props.mode==="vertical"){d.mouseInChild.value=!1;return}y==null||y(),d.mouseInChild.value=!1,{stop:y}=Ie(()=>!O.value&&l.closeMenu(e.index,i.value),o.value),j.value&&s&&((c=d.handleMouseleave)==null||c.call(d,!0))};X(()=>l.props.collapse,s=>u(!!s));{const s=M=>{I.value[M.index]=M},c=M=>{delete I.value[M.index]};ce(`subMenu:${r.uid}`,{addSubMenu:s,removeSubMenu:c,handleMouseleave:E,mouseInChild:O,level:d.level+1})}return t({opened:k}),he(()=>{l.addSubMenu(L),d.addSubMenu(L)}),xe(()=>{d.removeSubMenu(L),l.removeSubMenu(L)}),()=>{var s;const c=[(s=n.title)==null?void 0:s.call(n),g(Ce,{class:b.e("icon-arrow"),style:{transform:k.value?e.expandCloseIcon&&e.expandOpenIcon||e.collapseCloseIcon&&e.collapseOpenIcon&&l.props.collapse?"none":"rotateZ(180deg)":"none"}},{default:()=>D(B.value)?g(r.appContext.components[B.value]):g(B.value)})],M=l.isMenuPopup?g(Ee,{ref:U,visible:k.value,effect:"light",pure:!0,offset:G.value,showArrow:!1,persistent:!0,popperClass:W.value,placement:N.value,teleported:j.value,fallbackPlacements:oe.value,transition:ne.value,gpuAcceleration:!1},{content:()=>{var S;return g("div",{class:[m.m(_.value),m.m("popup-container"),W.value],onMouseenter:$=>v($,100),onMouseleave:()=>E(!0),onFocus:$=>v($,100)},[g("ul",{class:[m.b(),m.m("popup"),m.m(`popup-${N.value}`)],style:F.value},[(S=n.default)==null?void 0:S.call(n)])])},default:()=>g("div",{class:b.e("title"),onClick:h},c)}):g(be,{},[g("div",{class:b.e("title"),ref:te,onClick:h},c),g(Ke,{},{default:()=>{var S;return ke(g("ul",{role:"menu",class:[m.b(),m.m("inline")],style:F.value},[(S=n.default)==null?void 0:S.call(n)]),[[Ve,k.value]])}})]);return g("li",{class:[b.b(),b.is("active",z.value),b.is("opened",k.value),b.is("disabled",e.disabled)],role:"menuitem",ariaHaspopup:!0,ariaExpanded:k.value,onMouseenter:v,onMouseleave:()=>E(),onFocus:v},[M])}}});const ut=pe({mode:{type:String,values:["horizontal","vertical"],default:"vertical"},defaultActive:{type:String,default:""},defaultOpeneds:{type:ie(Array),default:()=>ze([])},uniqueOpened:Boolean,router:Boolean,menuTrigger:{type:String,values:["hover","click"],default:"hover"},collapse:Boolean,backgroundColor:String,textColor:String,activeTextColor:String,closeOnClickOutside:Boolean,collapseTransition:{type:Boolean,default:!0},ellipsis:{type:Boolean,default:!0},popperOffset:{type:Number,default:6},ellipsisIcon:{type:V,default:()=>Le},popperEffect:{type:String,values:["dark","light"],default:"dark"},popperClass:String,showTimeout:{type:Number,default:300},hideTimeout:{type:Number,default:300}}),le=e=>Array.isArray(e)&&e.every(n=>D(n)),it={close:(e,n)=>D(e)&&le(n),open:(e,n)=>D(e)&&le(n),select:(e,n,t,r)=>D(e)&&le(n)&&Ue(t)&&(r===void 0||r instanceof Promise)};var rt=q({name:"ElMenu",props:ut,emits:it,setup(e,{emit:n,slots:t,expose:r}){const i=Me(),C=i.appContext.config.globalProperties.$router,m=x(),b=w("menu"),l=w("sub-menu"),d=x(-1),p=x(e.defaultOpeneds&&!e.collapse?e.defaultOpeneds.slice(0):[]),I=x(e.defaultActive),y=x({}),O=x({}),te=f(()=>e.mode==="horizontal"||e.mode==="vertical"&&e.collapse),U=()=>{const o=I.value&&y.value[I.value];if(!o||e.mode==="horizontal"||e.collapse)return;o.indexPath.forEach(u=>{const h=O.value[u];h&&N(u,h.indexPath)})},N=(o,a)=>{p.value.includes(o)||(e.uniqueOpened&&(p.value=p.value.filter(u=>a.includes(u))),p.value.push(o),n("open",o,a))},B=o=>{const a=p.value.indexOf(o);a!==-1&&p.value.splice(a,1)},P=(o,a)=>{B(o),n("close",o,a)},j=({index:o,indexPath:a})=>{p.value.includes(o)?P(o,a):N(o,a)},ne=o=>{(e.mode==="horizontal"||e.collapse)&&(p.value=[]);const{index:a,indexPath:u}=o;if(!(ye(a)||ye(u)))if(e.router&&C){const h=o.route||a,v=C.push(h).then(E=>(E||(I.value=a),E));n("select",a,u,{index:a,indexPath:u,route:h},v)}else I.value=a,n("select",a,u,{index:a,indexPath:u})},oe=o=>{const a=y.value,u=a[o]||I.value&&a[I.value]||a[e.defaultActive];u?I.value=u.index:I.value=o},k=o=>{const a=getComputedStyle(o),u=Number.parseInt(a.marginLeft,10),h=Number.parseInt(a.marginRight,10);return o.offsetWidth+u+h||0},z=()=>{var o,a;if(!m.value)return-1;const u=Array.from((a=(o=m.value)==null?void 0:o.childNodes)!=null?a:[]).filter($=>$.nodeName!=="#comment"&&($.nodeName!=="#text"||$.nodeValue)),h=64,v=getComputedStyle(m.value),E=Number.parseInt(v.paddingLeft,10),s=Number.parseInt(v.paddingRight,10),c=m.value.clientWidth-E-s;let M=0,S=0;return u.forEach(($,Ne)=>{M+=k($),M<=c-h&&(S=Ne+1)}),S===u.length?-1:S},_=o=>O.value[o].indexPath,L=(o,a=33.34)=>{let u;return()=>{u&&clearTimeout(u),u=setTimeout(()=>{o()},a)}};let F=!0;const G=()=>{if(d.value===z())return;const o=()=>{d.value=-1,Ge(()=>{d.value=z()})};F?o():L(o)(),F=!1};X(()=>e.defaultActive,o=>{y.value[o]||(I.value=""),oe(o)}),X(()=>e.collapse,o=>{o&&(p.value=[])}),X(y.value,U);let W;qe(()=>{e.mode==="horizontal"&&e.ellipsis?W=Re(m,G).stop:W==null||W()});const H=x(!1);{const o=v=>{O.value[v.index]=v},a=v=>{delete O.value[v.index]};ce("rootMenu",fe({props:e,openedMenus:p,items:y,subMenus:O,activeIndex:I,isMenuPopup:te,addMenuItem:v=>{y.value[v.index]=v},removeMenuItem:v=>{delete y.value[v.index]},addSubMenu:o,removeSubMenu:a,openMenu:N,closeMenu:P,handleMenuItemClick:ne,handleSubMenuClick:j})),ce(`subMenu:${i.uid}`,{addSubMenu:o,removeSubMenu:a,mouseInChild:H,level:0})}return he(()=>{e.mode==="horizontal"&&new tt(i.vnode.el,b.namespace.value)}),r({open:a=>{const{indexPath:u}=O.value[a];u.forEach(h=>N(h,u))},close:B,handleResize:G}),()=>{var o,a;let u=(a=(o=t.default)==null?void 0:o.call(t))!=null?a:[];const h=[];if(e.mode==="horizontal"&&m.value){const c=We(u),M=d.value===-1?c:c.slice(0,d.value),S=d.value===-1?[]:c.slice(d.value);S!=null&&S.length&&e.ellipsis&&(u=M,h.push(g(ge,{index:"sub-menu-more",class:l.e("hide-arrow"),popperOffset:e.popperOffset},{title:()=>g(Ce,{class:l.e("icon-more")},{default:()=>g(e.ellipsisIcon)}),default:()=>S})))}const v=Oe(e,0),E=e.closeOnClickOutside?[[Ze,()=>{p.value.length&&(H.value||(p.value.forEach(c=>n("close",c,_(c))),p.value=[]))}]]:[],s=ke(g("ul",{key:String(e.collapse),role:"menubar",ref:m,style:v.value,class:{[b.b()]:!0,[b.m(e.mode)]:!0,[b.m("collapse")]:e.collapse}},[...u,...h]),E);return e.collapseTransition&&e.mode==="vertical"?g(at,()=>s):s}}});const ct=pe({index:{type:ie([String,null]),default:null},route:{type:ie([String,Object])},disabled:Boolean}),dt={click:e=>D(e.index)&&Array.isArray(e.indexPath)},ue="ElMenuItem",mt=q({name:ue,components:{ElTooltip:Ee},props:ct,emits:dt,setup(e,{emit:n}){const t=Me(),r=Z("rootMenu"),i=w("menu"),C=w("menu-item");r||K(ue,"can not inject root menu");const{parentMenu:m,indexPath:b}=Te(t,He(e,"index")),l=Z(`subMenu:${m.value.uid}`);l||K(ue,"can not inject sub menu");const d=f(()=>e.index===r.activeIndex),p=fe({index:e.index,indexPath:b,active:d}),I=()=>{e.disabled||(r.handleMenuItemClick({index:e.index,indexPath:b.value,route:e.route}),n("click",p))};return he(()=>{l.addSubMenu(p),r.addMenuItem(p)}),xe(()=>{l.removeSubMenu(p),r.removeMenuItem(p)}),{parentMenu:m,rootMenu:r,active:d,nsMenu:i,nsMenuItem:C,handleClick:I}}});function pt(e,n,t,r,i,C){const m=Je("el-tooltip");return R(),Y("li",{class:ee([e.nsMenuItem.b(),e.nsMenuItem.is("active",e.active),e.nsMenuItem.is("disabled",e.disabled)]),role:"menuitem",tabindex:"-1",onClick:n[0]||(n[0]=(...b)=>e.handleClick&&e.handleClick(...b))},[e.parentMenu.type.name==="ElMenu"&&e.rootMenu.props.collapse&&e.$slots.title?(R(),Se(m,{key:0,effect:e.rootMenu.props.popperEffect,placement:"right","fallback-placements":["left"],persistent:""},{content:re(()=>[A(e.$slots,"title")]),default:re(()=>[de("div",{class:ee(e.nsMenu.be("tooltip","trigger"))},[A(e.$slots,"default")],2)]),_:3},8,["effect"])):(R(),Y(be,{key:1},[A(e.$slots,"default"),A(e.$slots,"title")],64))],2)}var $e=me(mt,[["render",pt],["__file","menu-item.vue"]]);const vt={title:String},ft="ElMenuItemGroup",ht=q({name:ft,props:vt,setup(){return{ns:w("menu-item-group")}}});function bt(e,n,t,r,i,C){return R(),Y("li",{class:ee(e.ns.b())},[de("div",{class:ee(e.ns.e("title"))},[e.$slots.title?A(e.$slots,"title",{key:1}):(R(),Y(be,{key:0},[Qe(Xe(e.title),1)],64))],2),de("ul",null,[A(e.$slots,"default")])],2)}var we=me(ht,[["render",bt],["__file","menu-item-group.vue"]]);const Et=De(rt,{MenuItem:$e,MenuItemGroup:we,SubMenu:ge}),Tt=ve($e);ve(we);ve(ge);export{Tt as E,Et as a};
