import{l as Q,b as oe,r as ce,m as v,c as de,F as me,Y as pe,M as b,N as D,aa as G,u as a,X as x,ai as E,V as H,a4 as X,Z as K,a9 as O,a0 as Z,a1 as S,H as be}from"./Dp9aCaJ6.js";import{K as fe,aL as ve,Z as p,Y as Ne,aM as ee,aS as P,aI as y,ap as h,a5 as Ve,M as he,aO as ye,S as Ie,aN as ge,aw as we,e as Ee,O as Se,aT as _,aR as J,aU as _e,aV as Pe,J as j,aW as Fe,aX as Ae,U as Ce,P as ke}from"./DjCZV6kq.js";import{v as q}from"./DlKZEFPo.js";const Me=fe({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:Number,readonly:<PERSON><PERSON><PERSON>,disabled:<PERSON>olean,size:ve,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:l=>l===null||p(l)||["min","max"].includes(l),default:null},name:String,label:String,placeholder:String,precision:{type:Number,validator:l=>l>=0&&l===Number.parseInt(`${l}`,10)},validateEvent:{type:Boolean,default:!0},...Ne(["ariaLabel"])}),Te={[ee]:(l,F)=>F!==l,blur:l=>l instanceof FocusEvent,focus:l=>l instanceof FocusEvent,[P]:l=>p(l)||y(l),[h]:l=>p(l)||y(l)},Be=["aria-label","onKeydown"],De=["aria-label","onKeydown"],xe=Q({name:"ElInputNumber"}),Ke=Q({...xe,props:Me,emits:Te,setup(l,{expose:F,emit:c}){const r=l,{t:z}=Ve(),d=he("input-number"),N=oe(),u=ce({currentValue:r.modelValue,userInput:null}),{formItem:f}=ye(),U=v(()=>p(r.modelValue)&&r.modelValue<=r.min),L=v(()=>p(r.modelValue)&&r.modelValue>=r.max),ne=v(()=>{const e=Y(r.step);return _(r.precision)?Math.max(Y(r.modelValue),e):(e>r.precision,r.precision)}),A=v(()=>r.controls&&r.controlsPosition==="right"),$=Ie(),V=ge(),C=v(()=>{if(u.userInput!==null)return u.userInput;let e=u.currentValue;if(y(e))return"";if(p(e)){if(Number.isNaN(e))return"";_(r.precision)||(e=e.toFixed(r.precision))}return e}),k=(e,n)=>{if(_(n)&&(n=ne.value),n===0)return Math.round(e);let t=String(e);const i=t.indexOf(".");if(i===-1||!t.replace(".","").split("")[i+n])return e;const g=t.length;return t.charAt(g-1)==="5"&&(t=`${t.slice(0,Math.max(0,g-1))}6`),Number.parseFloat(Number(t).toFixed(n))},Y=e=>{if(y(e))return 0;const n=e.toString(),t=n.indexOf(".");let i=0;return t!==-1&&(i=n.length-t-1),i},R=(e,n=1)=>p(e)?k(e+r.step*n):u.currentValue,M=()=>{if(r.readonly||V.value||L.value)return;const e=Number(C.value)||0,n=R(e);I(n),c(P,u.currentValue),B()},T=()=>{if(r.readonly||V.value||U.value)return;const e=Number(C.value)||0,n=R(e,-1);I(n),c(P,u.currentValue),B()},W=(e,n)=>{const{max:t,min:i,step:s,precision:m,stepStrictly:g,valueOnClear:w}=r;t<i&&Ce("InputNumber","min should not be greater than max.");let o=Number(e);if(y(e)||Number.isNaN(o))return null;if(e===""){if(w===null)return null;o=be(w)?{min:i,max:t}[w]:w}return g&&(o=k(Math.round(o/s)*s,m)),_(m)||(o=k(o,m)),(o>t||o<i)&&(o=o>t?t:i,n&&c(h,o)),o},I=(e,n=!0)=>{var t;const i=u.currentValue,s=W(e);if(!n){c(h,s);return}i===s&&e||(u.userInput=null,c(h,s),i!==s&&c(ee,s,i),r.validateEvent&&((t=f==null?void 0:f.validate)==null||t.call(f,"change").catch(m=>J())),u.currentValue=s)},ae=e=>{u.userInput=e;const n=e===""?null:Number(e);c(P,n),I(n,!1)},te=e=>{const n=e!==""?Number(e):"";(p(n)&&!Number.isNaN(n)||e==="")&&I(n),B(),u.userInput=null},re=()=>{var e,n;(n=(e=N.value)==null?void 0:e.focus)==null||n.call(e)},le=()=>{var e,n;(n=(e=N.value)==null?void 0:e.blur)==null||n.call(e)},ue=e=>{c("focus",e)},se=e=>{var n;u.userInput=null,c("blur",e),r.validateEvent&&((n=f==null?void 0:f.validate)==null||n.call(f,"blur").catch(t=>J()))},B=()=>{u.currentValue!==r.modelValue&&(u.currentValue=r.modelValue)},ie=e=>{document.activeElement===e.target&&e.preventDefault()};return de(()=>r.modelValue,(e,n)=>{const t=W(e,!0);u.userInput===null&&t!==n&&(u.currentValue=t)},{immediate:!0}),me(()=>{var e;const{min:n,max:t,modelValue:i}=r,s=(e=N.value)==null?void 0:e.input;if(s.setAttribute("role","spinbutton"),Number.isFinite(t)?s.setAttribute("aria-valuemax",String(t)):s.removeAttribute("aria-valuemax"),Number.isFinite(n)?s.setAttribute("aria-valuemin",String(n)):s.removeAttribute("aria-valuemin"),s.setAttribute("aria-valuenow",u.currentValue||u.currentValue===0?String(u.currentValue):""),s.setAttribute("aria-disabled",String(V.value)),!p(i)&&i!=null){let m=Number(i);Number.isNaN(m)&&(m=null),c(h,m)}s.addEventListener("wheel",ie,{passive:!1})}),pe(()=>{var e,n;const t=(e=N.value)==null?void 0:e.input;t==null||t.setAttribute("aria-valuenow",`${(n=u.currentValue)!=null?n:""}`)}),we({from:"label",replacement:"aria-label",version:"2.8.0",scope:"el-input-number",ref:"https://element-plus.org/en-US/component/input-number.html"},v(()=>!!r.label)),F({focus:re,blur:le}),(e,n)=>(b(),D("div",{class:x([a(d).b(),a(d).m(a($)),a(d).is("disabled",a(V)),a(d).is("without-controls",!e.controls),a(d).is("controls-right",a(A))]),onDragstart:n[0]||(n[0]=O(()=>{},["prevent"]))},[e.controls?G((b(),D("span",{key:0,role:"button","aria-label":a(z)("el.inputNumber.decrease"),class:x([a(d).e("decrease"),a(d).is("disabled",a(U))]),onKeydown:E(T,["enter"])},[H(e.$slots,"decrease-icon",{},()=>[K(a(j),null,{default:Z(()=>[a(A)?(b(),S(a(_e),{key:0})):(b(),S(a(Pe),{key:1}))]),_:1})])],42,Be)),[[a(q),T]]):X("v-if",!0),e.controls?G((b(),D("span",{key:1,role:"button","aria-label":a(z)("el.inputNumber.increase"),class:x([a(d).e("increase"),a(d).is("disabled",a(L))]),onKeydown:E(M,["enter"])},[H(e.$slots,"increase-icon",{},()=>[K(a(j),null,{default:Z(()=>[a(A)?(b(),S(a(Fe),{key:0})):(b(),S(a(Ae),{key:1}))]),_:1})])],42,De)),[[a(q),M]]):X("v-if",!0),K(a(Ee),{id:e.id,ref_key:"input",ref:N,type:"number",step:e.step,"model-value":a(C),placeholder:e.placeholder,readonly:e.readonly,disabled:a(V),size:a($),max:e.max,min:e.min,name:e.name,"aria-label":e.label||e.ariaLabel,"validate-event":!1,onKeydown:[E(O(M,["prevent"]),["up"]),E(O(T,["prevent"]),["down"])],onBlur:se,onFocus:ue,onInput:ae,onChange:te},null,8,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","aria-label","onKeydown"])],34))}});var Oe=Se(Ke,[["__file","input-number.vue"]]);const $e=ke(Oe);export{$e as E};
