import{_ as b}from"./DBRSLWAE.js";import{E as N}from"./DhvjYsFQ.js";import{_ as U}from"./BUuWSjTP.js";import{E as $}from"./CHWQlbQz.js";import{b as I,bF as M,v as j}from"./BsMqt_su.js";import"./BEVfyfzl.js";/* empty css        */import{l as A,aA as L,j as C,b as x,c as O,n as k,m as P,F as T,u as a,M as r,N as p,O as c,aa as Z,a1 as q,a0 as w,V as G,Z as d,a4 as f,a2 as H,a9 as V,y as J}from"./Dp9aCaJ6.js";import{_ as K}from"./DlAUqK2U.js";const Q={key:0},W={class:"flex"},X={key:0,class:"el-upload flex-col bg-fill-lighter"},Y={key:1,class:"imgContiner relative"},ee=["src"],te=A({__name:"index",props:{modelValue:{type:String,default:""},excludeDomain:{type:Boolean,default:!1},canClose:{type:Boolean,default:!0},size:{type:String,default:"100px"}},emits:["change","update:modelValue"],setup(s,{emit:z}){L(t=>({"4c5b0256":s.size}));const h=z,{getImageUrl:R}=I(),u=s,v=C(),n=x(!1),y=x(!1);O(n,t=>{t?k(()=>{var e;(e=v.value)==null||e.play()}):k(()=>{var e;(e=v.value)==null||e.pause()})});const m=x(!1),l=P({get(){return u.excludeDomain?R(u.modelValue):u.modelValue},set(t){h("update:modelValue",t)}}),_=C(),B=async({raw:t})=>{var e,i;try{m.value=!0;const o=await M("video",{file:t});m.value=!1,l.value=u.excludeDomain?o.url:o.uri,h("change",o.uri),(e=_.value)==null||e.clearFiles()}catch{m.value=!1,(i=_.value)==null||i.clearFiles()}};return T(()=>{y.value=!0}),(t,e)=>{const i=b,o=b,E=N,F=U,D=$,S=j;return a(y)?(r(),p("div",Q,[c("div",W,[Z((r(),q(E,{"element-loading-text":"上传中...",ref_key:"uploadRef",ref:_,class:"avatar-uploader flex","show-file-list":!1,limit:1,"on-change":B,"auto-upload":!1,accept:".wmv,.avi,.mpg,.mpeg,.3gp,.mov,.mp4,.flv,.rmvb,.mkv"},{default:w(()=>[G(t.$slots,"default",{},()=>[a(l)?f("",!0):(r(),p("div",X,[d(i,{name:"el-icon-Plus",size:20}),e[3]||(e[3]=c("div",{class:"text-tx-secondary mt-[2px]"},"添加视频",-1))])),a(l)?(r(),p("div",Y,[c("div",{class:"border border-solid border-br-light rounded-[6px] relative cursor-pointer",style:H({width:s.size,height:s.size})},[c("video",{class:"rounded-lg w-full h-full",src:a(l)},null,8,ee),c("div",{class:"z-[10px] absolute left-1/2 top-1/2 translate-x-[-50%] translate-y-[-50%] rounded-full w-5 h-5 flex justify-center items-center bg-[rgba(0,0,0,0.3)]",onClick:e[0]||(e[0]=V(g=>n.value=!0,["stop"]))},[d(o,{name:"el-icon-CaretRight",size:18,color:"#fff"})])],4),s.canClose?(r(),p("div",{key:0,class:"icon absolute top-[-10px] right-[-10px] text-tx-secondary",onClick:e[1]||(e[1]=V(g=>l.value="",["stop"]))},[d(i,{size:"20",name:"el-icon-CircleCloseFilled"})])):f("",!0)])):f("",!0)],!0)]),_:3})),[[S,a(m)]])]),d(D,{modelValue:a(n),"onUpdate:modelValue":e[2]||(e[2]=g=>J(n)?n.value=g:null),width:"740px",title:"视频预览"},{default:w(()=>[d(F,{ref_key:"playerRef",ref:v,src:a(l),width:"100%",height:"450px"},null,8,["src"])]),_:1},8,["modelValue"])])):f("",!0)}}}),ue=K(te,[["__scopeId","data-v-6025f73c"]]);export{ue as _};
