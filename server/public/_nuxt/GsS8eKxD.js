import{E as g}from"./Dkrb28GR.js";import{E as w}from"./DfSuJ7Ej.js";import{a as v,_ as B}from"./B1MekKW7.js";/* empty css        */import{u as E}from"./QLtE1ufq.js";import N from"./BJkDGruV.js";import{_ as C}from"./kWUG8Hyb.js";import{useSearch as S}from"./BRkGZCjn.js";import{e as A}from"./CyaqLv7U.js";import{l as I,ak as R,c as j,M as t,N as r,Z as m,a0 as i,u as e,_ as V,a1 as c,O as Z}from"./Dp9aCaJ6.js";import"./CPkX1WPy.js";import"./wW1KxDBP.js";import"./t2WiU-SJ.js";import"./DeQZaZZG.js";/* empty css        */import"./B4gxkFPb.js";import"./uE1Ww79i.js";import"./DlAUqK2U.js";import"./CfxOQUme.js";import"./Df0xfART.js";import"./DCTLXrZ8.js";import"./DwRIabhv.js";import"./D2wP0d1N.js";import"./C1GDMjBs.js";import"./9Bti1uB6.js";import"./GQTyu-ru.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./DJRXUk4u.js";import"./CytsermG.js";import"./BLV0QRdm.js";import"./B2jDEpOp.js";import"./CqHBsm8z.js";import"./Cq2NhlyP.js";import"./D2beQuxj.js";import"./BfwFqcrE.js";import"./BxTfM8xu.js";import"./DtTKtiSt.js";import"./CcPlX2kz.js";import"./AkJeabtx.js";import"./B2PttNjr.js";import"./CgVO1mSI.js";import"./BAxWXQAH.js";import"./DxZY3BF8.js";import"./iVGeVDsx.js";import"./C5X3Iz5j.js";import"./Bwx2-5pK.js";import"./Cv6HhfEG.js";import"./DmvaW7ga.js";import"./BMW57zJa.js";import"./AOe4nXt1.js";/* empty css        */import"./BW7sRm8E.js";import"./Cml2jZ-P.js";import"./siQy5HJc.js";import"./1nb9e5kE.js";import"./yUK7FRoX.js";import"./GiYHvNx2.js";import"./BijL3eFF.js";import"./j1t_hxOX.js";import"./C5AZPSfW.js";/* empty css        */const $={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Kt=I({__name:"index",async setup(q){let p,s;const l=v(),{showSearchResult:a,config:u,getConfig:_,getSearchInfo:f,options:d,result:y}=S();return[p,s]=R(()=>E(()=>_(),"$mZBhG8hzNj")),await p,s(),j(()=>l.query.id,o=>{o?y.value.id<0&&f(o):(d.value.ask="",a.value=!1)},{immediate:!0}),(o,n)=>{const h=g,k=w,x=B;return t(),r("div",null,[m(x,{name:"default"},{default:i(()=>[e(u).status>0?(t(),r(V,{key:0},[e(a)?(t(),c(C,{key:0})):(t(),c(N,{key:1}))],64)):(t(),r("div",$,[m(k,null,{icon:i(()=>[m(h,{class:"w-[150px] dark:opacity-60",src:e(A)},null,8,["src"])]),title:i(()=>n[0]||(n[0]=[Z("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Kt as default};
