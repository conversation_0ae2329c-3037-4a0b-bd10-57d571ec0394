import{l as O,j as b,r as k,b as H,ak as E,M as i,N as c,Z as l,a0 as d,_ as M,aq as Q,u as n,O as s,aa as S,a1 as C,a7 as p,a4 as x,a6 as U,ab as X,aG as Z,X as K,aH as Y}from"./Dp9aCaJ6.js";import{E as ee}from"./BsP4I97R.js";import{W as te}from"./BrHfWJzi.js";import{l as oe,j as se,cy as ae,cg as ne,J as ie,E as re}from"./DAgm18qP.js";import{E as le}from"./hOM-i1i4.js";import{E as ce}from"./DuLE2t5J.js";/* empty css        *//* empty css        */import{u as V}from"./DLAzaSQT.js";import{t as de,E as me,F as pe}from"./Bo3PTL3c.js";import{e as ue}from"./BOfxv3LJ.js";import{_ as _e}from"./DlAUqK2U.js";import"./D3kCNEsc.js";import"./D6pAoVka.js";import"./BkCbCnZg.js";const fe={class:"flex-1 min-h-0 mx-[16px] relative"},ge=["onClick"],we={class:"flex-1 min-h-[70vh] overflow-hidden mx-auto"},xe=["onClick"],ye={class:"flex items-center"},he={class:"flex-1 min-w-0 ml-[15px]"},ve={class:"line-clamp-1 text-xl font-medium"},be={class:"line-clamp-1 text-tx-secondary text-xs mt-[5px]"},ke={class:"mt-[13px] text-tx-secondary line-clamp-2 h-[40px] text-sm"},Ee={key:1,class:"flex justify-center items-center mt-[50px]"},Se={class:"flex flex-col justify-center items-center w-full h-[60vh]"},Ce=O({__name:"robot",props:{keyword:{}},async setup(N){let r,m;const P=N,R=oe(),u=se(),z=b(),_=k({keyword:"",cid:0}),I={4e3:{rowPerView:7},2e3:{rowPerView:6},1800:{rowPerView:5},1600:{rowPerView:5},1440:{rowPerView:4},1360:{rowPerView:4},1280:{rowPerView:4},1024:{rowPerView:3}},e=k({pageNo:1,count:0,loading:!0,pageSize:15,lists:[]}),y=H(0),{data:h}=([r,m]=E(()=>V(()=>de(),{default(){return[]},transform(t){return[{id:0,name:"全部"}].concat(t)},lazy:!0},"$LRegJQe7Nd")),r=await r,m(),r);[r,m]=E(()=>V(()=>f(),{lazy:!0},"$elq9G93T6A")),await r,m();const f=async()=>{e.loading=!0;try{const t=await me({..._,page_no:e.pageNo,page_size:e.pageSize});e.pageNo===1&&(e.lists=[]),e.count=t.count,e.lists.push(...t.lists)}finally{setTimeout(()=>e.loading=!1,200)}},B=()=>{u.isLogin&&e.count>=e.pageNo*e.pageSize&&(e.pageNo++,f())},g=()=>{e.pageNo=1,f()},L=b(),q=t=>{L.value=t,console.log(t)},v=t=>{var o;y.value=t,_.cid=(o=h.value[t])==null?void 0:o.id,g()};v(0);const D=async t=>{if(!u.isLogin){u.toggleShowLogin();return}const{id:o}=await pe({id:t.id});R.push({path:"/robot_square/chat",query:{id:o}})};return ae(()=>P.keyword,t=>{_.keyword=t,g()},{debounce:500}),(t,o)=>{const j=Y,A=Z,$=ee,F=te,T=ie,W=le,G=re,J=ce;return i(),c("div",fe,[l(A,{slidesPerView:"auto",spaceBetween:16,class:"category-lists",onSwiper:q,style:{padding:"10px 0"}},{default:d(()=>[(i(!0),c(M,null,Q(n(h),(a,w)=>(i(),C(j,{key:a.id,style:{width:"auto","margin-right":"12px"}},{default:d(()=>[Object.keys(a).includes("name")?(i(),c("div",{key:0,class:K(["category-item bg-white",{"is-active":n(y)===w}]),onClick:Ve=>v(w)},p(a.name),11,ge)):x("",!0)]),_:2},1024))),128))]),_:1}),s("div",we,[S((i(),c("div",{class:"model-lists mb-[10px] mx-[0px]",ref_key:"robotRef",ref:z,"infinite-scroll-distance":"50"},[n(e).lists.length?(i(),C(F,{key:0,ref:"waterFull",delay:100,list:n(e).lists,width:315,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:I},{item:d(({item:a})=>[s("div",{class:"card-item cursor-pointer bg-white dark:bg-[#1d2025]",onClick:w=>D(a)},[s("div",ye,[l($,{class:"flex-none",src:a.image,size:64},null,8,["src"]),s("div",he,[s("div",ve,p(a.name),1),s("div",be,p(a.author),1)])]),s("div",ke,p(a.intro),1),o[0]||(o[0]=s("div",{class:"mt-[30px] entry-btn"},"开始对话",-1))],8,xe)]),_:1},8,["list"])):x("",!0),n(e).loading?(i(),c("div",Ee,[l(T,{size:"25",class:"is-loading"},{default:d(()=>[l(n(ne))]),_:1}),o[1]||(o[1]=s("span",{class:"mt-[4px] ml-[10px] text-[#999999]"},"加载中...",-1))])):x("",!0),S(s("div",Se,[l(W,{class:"w-[200px] h-[200px]",src:n(ue)},null,8,["src"]),o[3]||(o[3]=s("div",{class:"text-tx-regular mb-4"},"暂无智能体",-1)),l(G,{type:"primary",onClick:g},{default:d(()=>o[2]||(o[2]=[U(" 点击刷新")])),_:1})],512),[[X,!n(e).lists.length&&!n(e).loading]])])),[[J,B]])])])}}}),Ge=_e(Ce,[["__scopeId","data-v-67ca5aa0"]]);export{Ge as default};
