import{_ as m}from"./CiUA4g-j.js";import{cn as _}from"./BBthjZaB.js";import{l as h,r as V,M as t,N as l,Z as v,O as s,_ as f,aq as b,u as c,X as o,a7 as r}from"./Dp9aCaJ6.js";import{_ as w}from"./DlAUqK2U.js";import"./Bj9H9xtu.js";import"./Dxaq3UbQ.js";import"./vwcCUF2-.js";import"./DCTLXrZ8.js";import"./C3aKVaZD.js";import"./Bwa6YuoW.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */const z={class:"mt-[15px]"},L={class:"grid grid-cols-4 gap-x-4"},k=["onClick"],g={class:"flex justify-center items-center mt-[10px] h-[20px]"},y={class:"text-base text-[#101010] dark:text-white mt-[4px] size-scale"},C={class:"text-xs text-[#798696] dark:text-white mt-[4px] size-name"},B=h({__name:"doubao-picture-size",props:{modelValue:{default:"1:1"}},emits:["update:modelValue"],setup(i,{emit:p}){const n=p,u=i,{modelValue:a}=_(u,n),x=V({lists:[{name:"头像图",scaleLabel:"1:1",scaleValue:"512x512",class:"w-[20px] h-[20px]"},{name:"媒体配图",scaleLabel:"3:4",scaleValue:"384x512",class:"w-[15px] h-[20px]"},{name:"文章配图",scaleLabel:"4:3",scaleValue:"512x384",class:"w-[20px] h-[15px]"},{name:"宣传海报",scaleLabel:"9:16",scaleValue:"288x512",class:"w-[13px] h-[20px]"},{name:"电脑壁纸",scaleLabel:"16:9",scaleValue:"512x288",class:"w-[20px] h-[12px]"},{name:"横版名片",scaleLabel:"3:2",scaleValue:"512x341",class:"w-[20px] h-[14px]"},{name:"小红书图",scaleLabel:"2:3",scaleValue:"341x512",class:"w-[13px] h-[20px]"}]});return a.value="1:1",(N,S)=>(t(),l("div",z,[v(m,{title:"图片尺寸",tips:"指定生成图像的宽高比",required:""}),s("div",L,[(t(!0),l(f,null,b(c(x).lists,(e,d)=>(t(),l("div",{key:d,class:o(["picture-size cursor-pointer text-center hover:text-primary",{"picture-size-active":c(a)==(e==null?void 0:e.scaleValue),"picture-size-disable":!(e!=null&&e.scaleValue)}]),onClick:q=>a.value=e.scaleValue},[s("div",g,[s("div",{class:o(["rect",e.class])},null,2)]),s("div",y,r(e.scaleLabel),1),s("div",C,r(e.name),1)],10,k))),128))])]))}}),H=w(B,[["__scopeId","data-v-72a4a7e0"]]);export{H as default};
