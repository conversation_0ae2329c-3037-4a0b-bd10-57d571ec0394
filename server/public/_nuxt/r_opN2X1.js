import{E as d,a as h,b as g,c as w}from"./BeH2HjUA.js";import{j as v,cw as C,a as b,V as y,cQ as D,f as E}from"./BQ-RMI0l.js";import{u as x,a as S}from"./D1YPE9q5.js";import{_ as L}from"./DqdDDOmB.js";import k from"./4X4kTXMf.js";import{_ as B}from"./BeV2iLwv.js";import R from"./BlTiifkq.js";import{l as V,c as r,M as $,N as z,Z as t,a0 as i,a2 as H,u as M}from"./Dp9aCaJ6.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./Bb7qOt1h.js";import"./CBhydjq2.js";import"./B_BP6MI7.js";import"./DlAUqK2U.js";import"./zAf_4_O9.js";import"./t-5G02zu.js";import"./DdkLcgv7.js";import"./Ci86EFhe.js";import"./BBQxRZuk.js";import"./BOZQs96k.js";import"./Da1DFZhM.js";import"./De57gGYj.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        *//* empty css        *//* empty css        */import"./CcPlX2kz.js";import"./DzVRtw2w.js";import"./Cv6HhfEG.js";import"./GPdvJwRg.js";import"./IJR_P5Ix.js";import"./D_paGRSe.js";import"./L7ewHh_h.js";/* empty css        */import"./DkjF24mE.js";import"./Dj0_HBiv.js";import"./CEzjtqAq.js";import"./Dpae-tzl.js";import"./BlP3Ke9w.js";/* empty css        */import"./DEQgTlxz.js";import"./CD_6C6YJ.js";import"./ByS3-7D7.js";import"./ZTotkVGG.js";/* empty css        */import"./mWrySVHP.js";import"./djnMiA6p.js";import"./DhTLUUeS.js";import"./C1aUxTFE.js";import"./DpOQaFAg.js";import"./IZ5UIo3-.js";import"./DzZ0m6Bt.js";import"./8yhmlZGP.js";import"./DlKZEFPo.js";import"./D66_3-dK.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./BVR8g5h9.js";import"./jPLx-_JQ.js";import"./Cwr-KwFA.js";import"./CzTHal_J.js";import"./CfylPmvH.js";import"./DqL3SXf4.js";import"./d0pkqrxb.js";import"./CWR0P6nN.js";import"./DLjq-dzX.js";import"./BbE61BO3.js";import"./i6D0BXVr.js";import"./l0sNRNKZ.js";import"./DkAUFH8T.js";import"./CWwM_yVY.js";import"./C2S7-d6r.js";import"./B36mVPSB.js";/* empty css        */import"./DOqsK0fR.js";const ao=V({__name:"design",setup(N){const n=v(),o=x(),{height:c}=C(),u=b(),{initTabs:l}=S();y(window,"beforeunload",m=>{o.isChangeData&&(m.preventDefault(),m.returnValue="内容已修改，确认离开页面吗？")}),D(async(m,f)=>{try{o.isChangeData&&n.isLogin&&await E.confirm("内容已修改，确认离开页面吗？")}catch{return!1}});const e=()=>{o.isChangeData=!0};return r(()=>o.canvasJson,e),r(()=>o.music,e),r(()=>o.dub,e),r(()=>o.voiceContent,e,{deep:!0}),r(()=>u.query,()=>{l()}),(m,f)=>{const p=d,a=h,_=g,s=w;return $(),z("div",{class:"overflow-x-auto",style:H({height:`${M(c)}px`})},[t(s,{class:"bg-page !min-w-[1200px] h-full"},{default:i(()=>[t(p,{height:"auto",style:{padding:"0"}},{default:i(()=>[t(L)]),_:1}),t(s,{class:"min-h-0"},{default:i(()=>[t(a,{width:"auto",style:{overflow:"visible"}},{default:i(()=>[t(k)]),_:1}),t(_,{style:{padding:"0"}},{default:i(()=>[t(B)]),_:1}),t(a,{width:"auto"},{default:i(()=>[t(R)]),_:1})]),_:1})]),_:1})],4)}}});export{ao as default};
