import{E as k,a as w}from"./pw6-iEEW.js";import{E as C}from"./CBkeCdOF.js";import{E as I}from"./CYgEYzPG.js";import{E as M}from"./DTIryUWu.js";import{cJ as S}from"./Br7V4jS9.js";/* empty css        *//* empty css        */import{u as V}from"./BQs8y7a2.js";import{u as B,I as L}from"./CDG5bHsH.js";import{e as P}from"./BFDvpBeT.js";import{l as T,r as N,ak as z,M as s,N as a,O as t,Z as n,a0 as _,_ as d,aq as u,u as o,a1 as A}from"./Dp9aCaJ6.js";const O={class:"h-full flex flex-col"},R={class:"mt-[5px] px-main"},U={class:"flex-1 min-h-0"},$={class:"p-main pt-0"},q={key:0,class:"flex flex-wrap mx-[-7px]"},D=["onClick"],F={class:"border border-solid border-br-light rounded-md cursor-pointer p-[10px]"},J={class:"pic-wrap h-0 pt-[100%] relative"},Z={class:"absolute inset-0"},le=T({__name:"prospect",async setup(j){let l,c;const f=B(),y=[{type:1,label:"系统前景"}],i=N({type:1}),{data:p}=([l,c]=z(()=>V(()=>S(),{lazy:!0},"$MMRLoo0MmU")),l=await l,c(),l),b=r=>{f.addImage(r.url,L.PROSPECT,r)};return(r,m)=>{const x=w,h=k,v=C,E=I,g=M;return s(),a("div",O,[t("div",R,[n(h,{modelValue:o(i).type,"onUpdate:modelValue":m[0]||(m[0]=e=>o(i).type=e)},{default:_(()=>[(s(),a(d,null,u(y,e=>n(x,{key:e.type,label:e.label,name:e.type},null,8,["label","name"])),64))]),_:1},8,["modelValue"])]),t("div",U,[n(g,null,{default:_(()=>[t("div",$,[o(p).length?(s(),a("div",q,[(s(!0),a(d,null,u(o(p),e=>(s(),a("div",{key:e.id,class:"w-[50%]"},[t("div",{class:"px-[7px] mb-[14px]",onClick:G=>b(e)},[t("div",F,[t("div",null,[t("div",J,[t("div",Z,[n(v,{src:e.url,class:"w-full h-full",fit:"contain",lazy:""},null,8,["src"])])])])])],8,D)]))),128))])):(s(),A(E,{key:1,image:o(P),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}});export{le as _};
