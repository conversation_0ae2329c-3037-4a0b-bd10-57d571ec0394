import{E as V}from"./JBMcerbz.js";import{_ as B}from"./BAr3c1ZT.js";import{_ as D}from"./C2uyROqA.js";import{E as L}from"./CYs1NzuK.js";import{_ as T}from"./DKQXk0aC.js";import{_ as N}from"./CN8DIg3d.js";import{a as M,l as O,b as P}from"./CLVDtxqA.js";/* empty css        *//* empty css        *//* empty css        */import{u as U}from"./CPeHeFy-.js";import z from"./DAaH2xNP.js";import{_ as F}from"./Bp4qLYMG.js";import{_ as J}from"./DnXqT0A7.js";import{g as Z}from"./Bo3PTL3c.js";import{u as j}from"./CuLC4HB6.js";import{l as G,b as d,ak as H,c as K,M as a,N as f,Z as m,a0 as s,O as e,u as o,y as w,_ as Q,aq as W,a1 as h,a4 as y,a7 as X}from"./Dp9aCaJ6.js";import{_ as Y}from"./DlAUqK2U.js";import"./DTPcp5_i.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";import"./DssWEouO.js";import"./9Bti1uB6.js";import"./DEG58TaP.js";import"./DI1qIu00.js";import"./87qJxv_v.js";/* empty css        */import"./DqV7Sug9.js";import"./Cv6HhfEG.js";import"./BZXjPGYa.js";/* empty css        */import"./BsfZLwFN.js";import"./CD28ZGm9.js";import"./DY73EaVE.js";import"./D5Ye8pBY.js";import"./CRS4Ri59.js";import"./1ThCdcE_.js";import"./DW2yWX9x.js";import"./C76tEu0I.js";import"./Bkygmrc1.js";import"./BjrRGs-I.js";import"./BaEiXiMm.js";import"./DmkDxza3.js";import"./DOeZo8uY.js";import"./DlKZEFPo.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./BNnETjxs.js";import"./CuRoC2NG.js";import"./BICGiF8V.js";import"./uz2DoZ_U.js";import"./Hd0kM9t1.js";import"./B1txbFRp.js";/* empty css        */import"./g6LfHct_.js";import"./Du5U7mLs.js";import"./CSSDQfJm.js";import"./BYShjMEC.js";import"./BQcHrgkb.js";import"./CkNWwKqv.js";/* empty css        *//* empty css        */import"./CR1z0UPw.js";import"./D3Sqf4PW.js";import"./BpyZaG8A.js";import"./B_I59N0D.js";import"./BBX5iu7R.js";import"./D92sLaNt.js";import"./BBFeqFQi.js";import"./DLMIaim0.js";/* empty css        */import"./C6_W4ts7.js";import"./O7rjrS3B.js";import"./DXwSsE2S.js";import"./BIcDjjDP.js";import"./D_SQCo6f.js";import"./DpOMRpsE.js";import"./CjON5iPp.js";/* empty css        *//* empty css        */import"./VJhqcMCs.js";import"./67xbGseh.js";import"./C_ElNdC8.js";import"./BrXWurWJ.js";import"./DguLjiNL.js";import"./B-7_EDyl.js";import"./CcPlX2kz.js";import"./Dp1MVQvw.js";import"./CqQVx8tK.js";import"./DYFh1E4A.js";import"./CJXSvk3L.js";import"./DcNltpix.js";import"./D-q0QyCV.js";import"./Cvz9QQGz.js";import"./BKJ74Wxu.js";import"./rfO90tn0.js";import"./DILeaQhY.js";import"./BZCTB_w4.js";import"./Ba8QNUKy.js";import"./DhyA-4N3.js";import"./-3N1FKKQ.js";import"./DKaVji1j.js";import"./Dt8dy3ia.js";import"./BtQB-JFB.js";import"./DQo9WDrm.js";import"./Cpg3PDWZ.js";const tt={class:"h-full flex"},ot=["onClick"],rt=["src"],et={class:"ml-[8px] line-clamp-1"},it={class:"flex items-center cursor-pointer"},pt={class:"text-xl flex-1 min-w-0"},mt={class:"sm:h-full py-[16px] pr-[16px] flex flex-col sm:flex-row flex-1 min-w-0"},nt={class:"sm:h-full flex-1 min-w-0 min-h-0 bg-body rounded-[12px]"},at=G({__name:"setting",async setup(st){let c,x;const i=M(),u=O();P();const v=j();v.getRobot();const _=d(!1),l=d(i.query.id),{data:b,refresh:k}=([c,x]=H(()=>U(()=>Z({id:l.value}),{transform(t){return(t==null?void 0:t.category_id)===0&&(t.category_id=""),t},default(){return{}},lazy:!0},"$3nIwi7TB6J")),c=await c,x(),c),p=d("edit"),q=[{name:"智能体设置",icon:"el-icon-Setting",key:"edit"},{name:"发布智能体",key:"release",icon:"el-icon-Position"},{name:"对话数据",key:"dialogue",icon:"el-icon-ChatDotRound"},{name:"立即对话",key:"chat",icon:"el-icon-ChatLineRound"}],S=t=>{switch(t){case"chat":u.push({path:"/application/chat",query:{id:l.value}});break;default:u.replace({path:i.path,query:{...i.query,currentTab:t}})}},C=async t=>{_.value=!1,t!=i.query.id&&(l.value=t,await k(),u.replace({path:i.path,query:{...i.query,id:t}}))};return K(()=>i.query,t=>{p.value=t.currentTab||"edit"},{immediate:!0}),(t,n)=>{const g=V,R=B,A=D,E=L,$=T,I=N;return a(),f("div",tt,[m($,{modelValue:o(p),"onUpdate:modelValue":[n[1]||(n[1]=r=>w(p)?p.value=r:null),S],"menu-list":q,"back-path":"/application/layout/robot"},{title:s(()=>[e("div",null,[m(E,{placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:o(_),"onUpdate:visible":n[0]||(n[0]=r=>w(_)?_.value=r:null)},{reference:s(()=>[e("div",it,[e("div",pt,[m(R,{content:o(b).name,teleported:!0,effect:"light"},null,8,["content"])]),m(A,{name:"el-icon-ArrowDown"})])]),default:s(()=>[e("div",null,[m(g,{style:{height:"250px"}},{default:s(()=>[(a(!0),f(Q,null,W(o(v).robotLists,r=>(a(),f("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-primary-light-9 px-[10px] my-[5px] rounded-[12px] hover:text-primary",key:r.id,onClick:lt=>C(r.id)},[e("img",{class:"rounded-[50%] w-[28px] h-[28px] flex-none",src:r.image,alt:""},null,8,rt),e("div",et,X(r.name),1)],8,ot))),128))]),_:1})])]),_:1},8,["visible"])])]),_:1},8,["modelValue"]),e("div",mt,[e("div",nt,[o(p)==="edit"?(a(),h(z,{key:0,"model-value":o(b),onSuccess:n[2]||(n[2]=r=>o(u).push("/application/layout/robot"))},null,8,["model-value"])):y("",!0),m(I,null,{default:s(()=>[o(p)==="release"?(a(),h(g,{key:0},{default:s(()=>[m(F,{"app-id":o(l)},null,8,["app-id"])]),_:1})):y("",!0)]),_:1}),o(p)==="dialogue"?(a(),h(J,{key:1,"app-id":o(l)},null,8,["app-id"])):y("",!0)])])])}}}),ur=Y(at,[["__scopeId","data-v-21d9b61a"]]);export{ur as default};
