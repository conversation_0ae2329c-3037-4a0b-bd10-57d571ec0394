import{_ as g}from"./CQG-tpkc.js";import{E as V}from"./dsHDlwYU.js";import{E}from"./CfXpSR1a.js";import{cn as b,e as w}from"./Br7V4jS9.js";import{E as h,a as y}from"./DzsXYoW8.js";/* empty css        *//* empty css        */import"./DDDB64Nb.js";import"./l0sNRNKZ.js";import{b as C}from"./C9pl8Zxy.js";import{l as I,b as N,M as U,N as k,Z as t,a0 as i,O as o,u as s,a7 as B,y as F}from"./Dp9aCaJ6.js";import{_ as O}from"./DlAUqK2U.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./CXUUfEOz.js";import"./DTIryUWu.js";import"./9Bti1uB6.js";import"./B9mz6c9C.js";import"./C3sn0ick.js";import"./DlKZEFPo.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";const z={class:"flex flex-col gap-2"},D={class:"flex items-center gap-2"},M={class:"flex items-center cursor-pointer text-[#999999]"},Q={class:"flex gap-4 items-center pl-3"},S={class:"flex items-center gap-2 mb-2"},$={class:"flex items-center cursor-pointer text-[#999999]"},j={class:"flex gap-4 items-center"},L=I({__name:"doubao-options",props:{modelValue:{type:Object,default:{seed:"",ddim_steps:20}}},emits:["update:modelValue"],setup(p,{emit:d}){const c=d,u=p,{modelValue:n}=b(u,c),m=N("1");return(P,e)=>{const r=g,a=V,_=E,f=w,v=h,x=y;return U(),k("div",null,[t(x,{modelValue:s(m),"onUpdate:modelValue":e[3]||(e[3]=l=>F(m)?m.value=l:null),class:"complex_params"},{default:i(()=>[t(v,{title:"高级参数",name:"1"},{title:i(()=>e[4]||(e[4]=[o("div",{class:"flex items-center gap-2"},[o("span",null,"高级参数")],-1)])),default:i(()=>[o("div",z,[o("div",null,[o("div",D,[e[5]||(e[5]=o("span",null,"绘画质量",-1)),t(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"越低：细节简练；越高：细节丰富, 默认值20"},{reference:i(()=>[o("div",M,[t(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),o("div",Q,[t(_,{modelValue:s(n).ddim_steps,"onUpdate:modelValue":e[0]||(e[0]=l=>s(n).ddim_steps=l),step:1,max:50,min:1},null,8,["modelValue"]),o("span",null,B(s(n).ddim_steps),1)])]),o("div",null,[o("div",S,[e[6]||(e[6]=o("span",null,"随机种子",-1)),t(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"种子用于指定生成效果，可以用于生成套图，保障生成的一系列图片保持同一种风格"},{reference:i(()=>[o("div",$,[t(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),o("div",j,[t(f,{modelValue:s(n).seed,"onUpdate:modelValue":e[1]||(e[1]=l=>s(n).seed=l),type:"number",min:-1,maxlength:18,onFocus:e[2]||(e[2]=l=>s(C)()),placeholder:"请输入seed种子编号"},null,8,["modelValue"])])])])]),_:1})]),_:1},8,["modelValue"])])}}}),de=O(L,[["__scopeId","data-v-ca887ef4"]]);export{de as default};
