import{h as R,E as S,e as j,n as D,f as F}from"./DjCZV6kq.js";import{_ as M}from"./DL-oL0AQ.js";import{E as $}from"./DOuG0VWa.js";/* empty css        */import{d as z}from"./BMzinL7R.js";import{u as L}from"./CcPlX2kz.js";import{B as P}from"./Bo3PTL3c.js";import H from"./Dp1MVQvw.js";import{l as O,b as f,j as Q,m as T,c as Z,M as k,a1 as q,a0 as n,O as t,u as o,N as A,a4 as G,Z as s,a7 as w,a6 as _,y as x}from"./Dp9aCaJ6.js";import{_ as J}from"./DlAUqK2U.js";import"./PR4uin41.js";import"./B1pEJPcQ.js";import"./JyVczL8-.js";import"./CDkfsEbM.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";const K=["src"],W={class:"absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center"},X={class:"text-center text-white mt-[15px] text-[18px] font-bold"},Y={class:"flex py-3 ml-[-5px] items-center"},ee={class:"flex-1 ml-3"},te={class:""},oe={class:"flex items-center"},le={class:"flex items-center"},se=O({__name:"poster",props:{show:{type:Boolean},url:{},apikey:{},shareId:{}},emits:["update:show","update"],setup(V,{emit:h}){const i=V,v=h,m=R(i,"show",v),p=`${D}/resource/image/other/ai_share_bg.png`,l=f(p),d=f("快来扫码"),u=f("和我的智能体对话吧"),B=a=>{l.value=a.uri},g=Q(),b=T(()=>`${location.origin}/chat/${i.apikey}`),{lockFn:E,isLock:C}=L(async()=>{try{await P({id:i.shareId,url:l.value}),v("update"),await z(g.value)}catch{return F.msgError("下载失败，请重试"),Promise.reject()}}),U=()=>{l.value=p};return Z(()=>i.url,a=>{a?l.value=a:l.value=p}),(a,e)=>{const c=S,I=M,y=j,N=$;return k(),q(N,{modelValue:o(m),"onUpdate:modelValue":e[2]||(e[2]=r=>x(m)?m.value=r:null),title:"生成海报",width:"400"},{default:n(()=>[t("div",{class:"poster relative",ref_key:"posterRef",ref:g},[o(l)?(k(),A("img",{key:0,class:"object-cover w-full h-full",src:o(l)},null,8,K)):G("",!0),t("div",W,[s(H,{text:o(b),size:"200",class:"rounded-[10px]",dotScale:"1",margin:"12"},null,8,["text"]),t("div",X,[t("div",null,w(o(d)),1),t("div",null,w(o(u)),1)])])],512),t("div",Y,[s(I,{limit:1,onSuccess:B},{default:n(()=>[s(c,{type:"primary",link:""},{default:n(()=>e[3]||(e[3]=[_("自定义背景图")])),_:1})]),_:1}),t("div",ee,[s(c,{type:"primary",link:"",onClick:U},{default:n(()=>e[4]||(e[4]=[_("使用默认图")])),_:1})]),e[5]||(e[5]=t("div",{class:"text-tx-regular"},"背景图尺寸：430*670",-1))]),t("div",te,[t("div",oe,[e[6]||(e[6]=t("div",{class:"text-tx-regular flex-none mr-2"},"标题",-1)),s(y,{modelValue:o(d),"onUpdate:modelValue":e[0]||(e[0]=r=>x(d)?d.value=r:null),placeholder:"请输入背景图地址"},null,8,["modelValue"])]),t("div",le,[e[7]||(e[7]=t("div",{class:"text-tx-regular flex-none mr-2"},"描述",-1)),s(y,{modelValue:o(u),"onUpdate:modelValue":e[1]||(e[1]=r=>x(u)?u.value=r:null),placeholder:"请输入背景图地址",class:"py-3"},null,8,["modelValue"])])]),t("div",null,[s(c,{type:"primary",size:"large",class:"w-full",loading:o(C),onClick:o(E)},{default:n(()=>e[8]||(e[8]=[_(" 保存 ")])),_:1},8,["loading","onClick"])])]),_:1},8,["modelValue"])}}}),we=J(se,[["__scopeId","data-v-4f9d7aed"]]);export{we as default};
