import{E as j}from"./BGU3hFiH.js";import{f as z,E as h,v as q}from"./BsMqt_su.js";import{E as H,a as M}from"./f1oA63J4.js";import{_ as O}from"./mtkgvlCx.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{u as Z}from"./DdgDjo0O.js";import{u as G}from"./67xbGseh.js";import{_ as J}from"./CHhZ8-NJ.js";import{_ as K}from"./DepWE5NH.js";import{_ as C}from"./MK231Q-r.js";import{w as Q,x as W,y as X,z as Y}from"./Bo3PTL3c.js";import{l as ee,j as x,C as te,r as ae,m as oe,b as se,c as ne,M as r,N as ie,Z as a,u as o,O as n,a0 as l,a6 as m,a1 as g,a4 as b,a7 as le,aa as pe,y as re,_ as me}from"./Dp9aCaJ6.js";const ce={class:"flex items-center mt-4"},de={class:"flex"},fe={class:"ml-4 flex items-center"},ue={class:"mt-4"},_e={class:"flex items-center"},ye={class:"flex justify-end mt-4"},De=ee({__name:"api",props:{appId:{},type:{}},emits:["back"],setup(R,{emit:$}){const c=R,E=$,{copy:B}=Z(),v=x(),d=x(),{appId:V}=te(c),f=ae({robot_id:V,type:c.type}),{pager:p,getLists:i}=G({fetchFun:Q,params:f});i();const A=async t=>{await z.confirm("确定删除？"),await W({id:t,type:f.type}),i()},I=oe(()=>{switch(c.type){case 5:return"发布企业微信";default:return"发布API"}}),P=async t=>{var e;await X({...t,...f}),(e=v.value)==null||e.close(),i()},N=async t=>{var e;await Y({...t,...f}),(e=d.value)==null||e.close(),i()},k=se("");k.value=`${window.location.origin}/api/v1/chat/completions`;const S=async t=>{await B(t)},D=t=>{var e,u;(e=d.value)==null||e.open(),(u=d.value)==null||u.setFormData(t)};return ne(()=>c.appId,()=>{i()}),(t,e)=>{const u=j,w=h,F=h,_=H,L=M,T=O,U=q;return r(),ie(me,null,[a(u,{content:o(I),onBack:e[0]||(e[0]=s=>E("back"))},null,8,["content"]),n("div",ce,[n("div",de,[a(w,{type:"primary",onClick:e[1]||(e[1]=s=>{var y;return(y=o(v))==null?void 0:y.open()})},{default:l(()=>e[4]||(e[4]=[m(" 创建API ")])),_:1}),t.type==4?(r(),g(C,{key:0,type:"app",class:"ml-[10px]"})):b("",!0),t.type==5?(r(),g(C,{key:1,type:"wx",class:"ml-[10px]"})):b("",!0),t.type==7?(r(),g(C,{key:2,type:"yd",class:"ml-[10px]"})):b("",!0)]),n("div",fe,[e[6]||(e[6]=m(" API根地址： ")),n("span",null,le(o(k)),1),a(F,{onClick:e[2]||(e[2]=s=>S(o(k))),type:"primary",link:""},{default:l(()=>e[5]||(e[5]=[m("复制")])),_:1})])]),n("div",ue,[pe((r(),g(L,{data:o(p).lists,size:"large"},{default:l(()=>[a(_,{label:"apikey",prop:"apikey","min-width":"200"}),a(_,{label:"名称",prop:"name","min-width":"180","show-tooltip-when-overflow":""}),a(_,{label:"最后使用时间",prop:"use_time","min-width":"180"}),a(_,{label:"操作",width:"150",fixed:"right"},{default:l(({row:s})=>[n("div",_e,[a(w,{type:"primary",link:"",onClick:y=>D(s)},{default:l(()=>e[7]||(e[7]=[m(" 用量设置 ")])),_:2},1032,["onClick"]),a(w,{type:"danger",link:"",onClick:y=>A(s.id)},{default:l(()=>e[8]||(e[8]=[m(" 删除 ")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[U,o(p).loading]]),n("div",ye,[a(T,{modelValue:o(p),"onUpdate:modelValue":e[3]||(e[3]=s=>re(p)?p.value=s:null),onChange:o(i)},null,8,["modelValue","onChange"])])]),a(J,{ref_key:"createApiRef",ref:v,onConfirm:P},null,512),a(K,{ref_key:"usageSettingsRef",ref:d,onConfirm:N},null,512)],64)}}});export{De as _};
