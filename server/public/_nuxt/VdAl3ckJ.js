import{_ as h}from"./C-hk8zs7.js";import{a as f,cm as v,_ as y}from"./BBthjZaB.js";import{l as i,m as g,M as s,N as c,O as r,_ as k,aq as b,a1 as N,a0 as u,X as C,a2 as L,a6 as $,a7 as w,a4 as B,Z as _}from"./Dp9aCaJ6.js";import{_ as d}from"./DlAUqK2U.js";const V={class:"flex"},z={class:"flex bg-body p-[8px] rounded-[10px]"},F=["onClick"],A={key:0,style:{"font-size":"10px",color:"red"}},I=i({__name:"index",props:{navList:{}},setup(x){const a=f(),l=g(()=>{const t=a.path==="/"?a.path:a.path.replace(/\/$/,"");return a.meta.activePath||t}),e=t=>{const n=l.value;return n===t?(console.log("✅ 激活 (精确匹配):",t),!0):n.startsWith(t+"/")?(console.log("✅ 激活 (前缀匹配):",t),!0):!1},p=t=>{console.log("点击菜单:",t.name,"- 激活状态:",e(t.path))};return(t,n)=>{const m=h;return s(),c("div",V,[r("div",z,[(s(!0),c(k,null,b(t.navList,o=>(s(),N(m,{key:o.path,to:o.path},{default:u(()=>[r("div",{class:C(["text-xl px-[17.5px] py-[5px] rounded-[7px] min-w-[85px] text-center font-bold",{"text-white bg-primary nav-active":e(o.path)}]),style:L(e(o.path)?"background-color: #4A92FF !important; color: white !important;":""),onClick:M=>p(o)},[$(w(o.name)+" ",1),e(o.path)?(s(),c("span",A,"✓")):B("",!0)],14,F)]),_:2},1032,["to"]))),128))])])}}}),S=d(I,[["__scopeId","data-v-cf7829d4"]]),q={class:"h-full flex flex-col"},D={class:"flex-1 min-h-0"},E=i({__name:"layout",setup(x){const a=[{name:"智能体",path:"/application/layout/robot"},{name:"智能体形象",path:"/application/layout/digital"},{name:"知识库",path:"/application/layout/kb"}];return(l,e)=>{const p=S,t=v,n=y;return s(),c("div",null,[_(n,{name:"default"},{default:u(()=>[r("div",q,[_(p,{class:"px-[20px] pt-[16px]","nav-list":a}),r("div",D,[_(t)])])]),_:1})])}}}),W=d(E,[["__scopeId","data-v-8125b6d3"]]);export{W as default};
