import{E as R}from"./C76tEu0I.js";import{_ as D}from"./C2uyROqA.js";import{E as B}from"./DTPcp5_i.js";import{_ as L}from"./BV-HPVlr.js";import{E as T}from"./CYs1NzuK.js";import{E as V}from"./D92sLaNt.js";import{E as $}from"./CBgDGK3D.js";import{E as j}from"./DXM1QjAY.js";import{v as I,bA as P,f as v,d as U}from"./CLVDtxqA.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{b as q}from"./B-7_EDyl.js";import{D as A}from"./B5ITMacb.js";import{d as F,g as M}from"./B5TkE_dZ.js";import{E as O}from"./JBMcerbz.js";import{l as H,r as Z,j as G,aa as b,u as n,M as c,N as p,a1 as J,a0 as r,O as o,_ as K,aq as Q,Z as s,a4 as W,a6 as X,a7 as g}from"./Dp9aCaJ6.js";const Y={class:"h-full bg-body rounded-[15px] p-[16px] flex flex-col"},ee={"infinite-scroll-distance":"50"},oe={class:"grid grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4"},te={class:"flex justify-between"},se={class:"flex items-center"},ae=["onClick"],re=["onClick"],ne=["onClick"],le={class:"relative rounded-[12px] overflow-hidden flex-1"},ce={class:"bg-[var(--el-bg-color-page)]"},ie={class:"w-full box-border"},de={class:"line-clamp-1"},pe={class:"flex justify-between items-center"},_e={class:"text-[#8794A3]"},fe={key:1,class:"h-full flex items-center justify-center"},je=H({__name:"video",setup(me){const e=Z({pageNo:1,count:0,pageSize:15,loading:!0,lists:[]}),h=async t=>{await v.confirm("确定删除？"),await F({id:t}),U.success("删除成功"),w()},x=async(t,_)=>{try{const l=await $request.get({url:t,responseType:"blob",baseURL:""},{isReturnDefaultResponse:!0,apiPrefix:""});console.log(l);const i=new Blob([l._data],{type:l.headers.get("Content-Type")}),d=window.URL.createObjectURL(i);q(d,_)}catch{v.msgError("文件下载失败")}},f=async()=>{try{const t=await M({status:2,page_no:e.pageNo,page_size:e.pageSize});e.count=t.count,e.pageNo===1&&(e.lists=[]),e.lists.push(...t.lists)}catch{}finally{e.loading=!1}},y=()=>{e.count>=e.pageNo*e.pageSize&&(e.pageNo++,f())},w=async()=>{m.value.setScrollTop(0),e.pageSize=e.pageNo*e.pageSize,e.pageNo=1,await f()},m=G();return(async()=>{var t;e.loading=!0,e.pageSize=15,e.pageNo=1,await f(),(t=m.value)==null||t.setScrollTop(0)})(),(t,_)=>{const l=R,i=D,d=B,k=L,E=T,N=V,C=$,S=j,z=I;return b((c(),p("div",Y,[n(e).lists.length>0?(c(),J(n(O),{key:0,class:"video-result flex-1",ref_key:"scrollBarRef",ref:m},{default:r(()=>[b((c(),p("div",ee,[o("div",oe,[(c(!0),p(K,null,Q(n(e).lists,(a,ue)=>(c(),p("div",{key:a.id,class:"rounded-xl p-4 flex flex-col gap-2 border border-[#eff0f2] dark:border-[#333333]"},[o("div",te,[s(l,null,{default:r(()=>[X(g(a.type_desc),1)]),_:2},1024),o("div",se,[s(d,{effect:"dark",content:"复制提示词",placement:"bottom"},{default:r(()=>[o("div",{onClick:u=>n(P)(a.prompt)},[s(i,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-CopyDocument",size:"18",color:"#556477"})],8,ae)]),_:2},1024),s(d,{effect:"dark",content:"下载视频",placement:"bottom"},{default:r(()=>[o("div",{onClick:u=>x(a.video_url,"视频")},[s(i,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Download",size:"18",color:"#556477"})],8,re)]),_:2},1024),s(d,{effect:"dark",content:"删除",placement:"bottom"},{default:r(()=>[o("div",{onClick:u=>h(a.id)},[s(i,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Delete",size:"18",color:"#556477"})],8,ne)]),_:2},1024)])]),o("div",le,[o("div",ce,[s(k,{src:a.video_url,type:"video",ratio:[4,3]},null,8,["src"])])]),s(E,{placement:"bottom",title:"提示词",width:"300px","show-arrow":!1,transition:"custom-popover",trigger:"hover",content:a.prompt},{reference:r(()=>[o("div",ie,[o("div",de,g(a.prompt),1)])]),_:2},1032,["content"]),o("div",pe,[o("span",_e,g(a.create_time),1)])]))),128))])])),[[S,y]])]),_:1},512)):n(e).loading?W("",!0):(c(),p("div",fe,[s(C,null,{icon:r(()=>[s(N,{class:"w-[150px] dark:opacity-60",src:n(A)},null,8,["src"])]),"sub-title":r(()=>_[0]||(_[0]=[o("div",{class:"text-info"},"暂时没有视频哦，快去生成试试吧",-1)])),_:1})]))])),[[z,n(e).loading]])}}});export{je as _};
