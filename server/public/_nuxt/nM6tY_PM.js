import{E as h}from"./JOeTFF2G.js";import{_ as w}from"./Bj9H9xtu.js";import{E as B}from"./D0csLVr8.js";import{cn as M,d as C,bF as N,o as F}from"./BBthjZaB.js";/* empty css        */import"./DNtYbVbo.js";import"./DP2rzg_V.js";import{l as I,b as P,m as U,M as r,a1 as $,a0 as n,O as o,Z as p,u as l,N as d,a9 as j,a6 as q,a7 as z,y as G}from"./Dp9aCaJ6.js";import{_ as D}from"./xixvWuCN.js";import{_ as J}from"./DlAUqK2U.js";import"./DBBnJi1E.js";import"./DiPtOrlW.js";import"./B4Y9LdPA.js";import"./hxgYBnNa.js";import"./DO7Ufrqu.js";const L={class:"flex-1 leading-snug"},R={key:0,class:"flex justify-center items-center h-[150px] relative"},S={key:1,class:"uploader-container"},A={class:"el-upload__tip text-[#798696]"},m=10,O=I({__name:"uploader-picture",props:{modelValue:{default:""},type:{default:"image"},files:{}},emits:["update:modelValue"],setup(f,{emit:_}){const u=_,i=f,{modelValue:s}=M(i,u),a=P([]),v=t=>t.size>m*1024*1024?(C.error(`文件大小不能超过${m}MB`),!1):!0,g=t=>{u("update:modelValue",t.uri),s.value=t.uri,a.value=[{name:t.name,url:t.uri}]},x=t=>N(i.type,{file:t.file,name:"file",header:{}}),b=U(()=>{switch(i.type){case"image":return".jpg,.png,.jpeg";case"video":return".wmv,.avi,.mpg,.mpeg,.3gp,.mov,.mp4,.flv,.rmvb,.mkv";case"audio":return;default:return"*"}});return(t,e)=>{const y=h,k=w,E=B,V=F;return r(),$(V,{prop:"image",required:""},{label:n(()=>e[2]||(e[2]=[o("span",{class:"font-bold text-tx-primary"}," 上传参考图 ",-1)])),default:n(()=>[o("div",L,[p(E,{"file-list":l(a),"onUpdate:fileList":e[1]||(e[1]=c=>G(a)?a.value=c:null),class:"uploader",drag:"",multiple:!1,"show-file-list":!1,"on-success":g,"http-request":x,"before-upload":v,accept:l(b)},{default:n(()=>[o("div",null,[l(s)?(r(),d("div",R,[p(y,{class:"!block h-[100%]",src:l(s),fit:"contain"},null,8,["src"]),p(k,{class:"!absolute right-0 top-0 z-10 drop-shadow",name:"el-icon-CircleCloseFilled",color:"#ffffff",onClick:e[0]||(e[0]=j(c=>s.value="",["stop"]))})])):(r(),d("div",S,[e[3]||(e[3]=o("img",{src:D,alt:"文件上传",class:"w-8 mx-auto mb-2"},null,-1)),e[4]||(e[4]=o("div",{class:"el-upload__text text-[#798696] !text-[13px]"},[q(" 拖拽文件到此处或者"),o("em",null,"点击上传")],-1)),o("div",A,z(`支持图片格式：JPG/JPEG/PNG，低于${m}MB`),1)]))])]),_:1},8,["file-list","accept"])])]),_:1})}}}),re=J(O,[["__scopeId","data-v-cd547139"]]);export{re as default};
