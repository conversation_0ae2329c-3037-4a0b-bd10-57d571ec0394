import{_ as K}from"./CW-7MqHx.js";import{h as X,e as B,E as z,l as Z,a as G,n as H,o as J,p as Q}from"./Br7V4jS9.js";import{_ as W}from"./DUhzbwl1.js";import{_ as Y}from"./9W9ohL4i.js";import{_ as A}from"./CQG-tpkc.js";import{u as N}from"./Bb2-23m7.js";import{l as I,M as m,N as g,O as t,a7 as h,_ as S,u as l,a1 as q,a9 as U,a4 as ee,X as te,b as D,m as L,Z as i,as as le,a0 as s,a6 as c,aq as oe,ak as ie,j as ae,s as se,y as ne}from"./Dp9aCaJ6.js";import{_ as re}from"./DlAUqK2U.js";import{P as de}from"./DDCcsBP0.js";import{g as ue,p as me,a as pe,b as _e}from"./C6_W4ts7.js";import{E as ve}from"./DTIryUWu.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */import{u as ce}from"./BQs8y7a2.js";import"./DmQlxOml.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CtBRWm-a.js";import"./C3sn0ick.js";import"./LFZ9apcG.js";/* empty css        */import"./CuIdQWNz.js";import"./BT1PeYhT.js";import"./ynsGo-Dq.js";import"./C5S6gXlf.js";import"./1QakEyj_.js";import"./Dg3tPGYu.js";import"./Cpg3PDWZ.js";import"./Cs-EFu39.js";import"./9Bti1uB6.js";/* empty css        */const fe={class:"flex-1 min-w-0 line-clamp-1"},ye=I({__name:"item",props:{isActive:{type:Boolean},value:{},url:{}},setup(M){const{play:p,pause:f,audioPlaying:b}=N();return(d,u)=>{const v=A;return m(),g("div",{class:te(["dub-item",{"dub-item--is-active":d.isActive}])},[t("div",fe,h(d.value),1),d.url?(m(),g(S,{key:0},[l(b)?(m(),q(v,{key:0,name:"el-icon-VideoPause",size:18,onClick:U(l(f),["stop"])},null,8,["onClick"])):(m(),q(v,{key:1,name:"el-icon-VideoPlay",size:18,onClick:u[0]||(u[0]=U($=>l(p)(d.url),["stop"]))}))],64)):ee("",!0)],2)}}}),xe=re(ye,[["__scopeId","data-v-1593ed02"]]),ge={class:"inline-flex items-center"},be={class:"text-tx-primary flex cursor-pointer"},Ve={class:"ml-[20px]"},ke={class:"flex flex-wrap mx-[-10px]"},we={class:"w-full"},Ce=I({__name:"picker",props:{modelValue:{}},emits:["update:modelValue"],setup(M,{emit:p}){const f=M,b=p,d=D(""),u=X(f,"modelValue",b),{play:v,pause:$,audioPlaying:V}=N(),k=D({}),o=async()=>{k.value=await ue()},y=L(()=>k.value[u.value]||{});return o(),(R,r)=>{const P=A,_=B,e=z,E=xe,w=de;return m(),g("div",ge,[i(_,{"model-value":l(y).name||l(u),readonly:"",placeholder:"请选择配音",clearable:""},le({_:2},[l(y).example?{name:"suffix",fn:s(()=>[t("div",be,[l(V)?(m(),q(P,{key:0,name:"el-icon-VideoPause",size:18,onClick:U(l($),["stop"])},null,8,["onClick"])):(m(),q(P,{key:1,name:"el-icon-VideoPlay",size:18,onClick:r[0]||(r[0]=U(n=>l(v)(l(y).example),["stop"]))}))])]),key:"0"}:void 0]),1032,["model-value"]),i(w,{title:"选择配音",width:"500px",onOpen:r[1]||(r[1]=n=>d.value=l(u)),onConfirm:r[2]||(r[2]=n=>u.value=l(d))},{trigger:s(()=>[t("div",Ve,[i(e,{type:"primary",plain:""},{default:s(()=>r[3]||(r[3]=[c(" 选择配音 ")])),_:1})])]),default:s(()=>[t("div",ke,[(m(!0),g(S,null,oe(l(k),(n,x)=>(m(),g("div",{class:"w-[50%] mb-[15px] px-[10px]",key:x},[t("div",we,[i(E,{"is-active":x==l(d),value:n.name,url:n.example,onClick:C=>d.value=x},null,8,["is-active","value","url","onClick"])])]))),128))])]),_:1})])}}}),$e={class:"h-full flex"},Pe={class:"h-full flex-1 min-w-0 py-[16px] pr-[16px]"},qe={class:"h-full flex flex-col bg-body rounded-2xl"},Ue={class:"flex-1 min-h-0"},De={class:"lg:flex"},Me={class:"flex-1 min-w-0"},Ee={class:"w-[420px]"},Ie={class:"form-tips"},Re={class:"form-tips"},Be={class:"form-tips"},ze={class:"form-tips"},Ae={class:"flex flex-1"},Ne={class:"w-[420px]"},Se={class:"w-[420px]"},Le={class:"flex p-4 items-center justify-center"},bt=I({__name:"edit",async setup(M){let p,f;const b=Z(),d=G(),u=L(()=>d.query.id),v=D(""),$=async()=>{if(u.value){const _=await _e({id:u.value});return v.value=_.name,_}else return Promise.reject()},V=D("edit"),k=[{name:"形象设置",icon:"el-icon-Setting",key:"edit"}],{data:o}=([p,f]=ie(()=>ce(()=>$(),{default(){return{name:"",avatar:"",image:"",wide_stay_video:"",wide_talk_video:"",vertical_stay_video:"",vertical_talk_video:"",channel:"",dubbing:"",idle_time:10,idle_reply:""}},lazy:!0},"$bL2ljimD45")),p=await p,f(),p),y=ae(),R=se({name:[{required:!0,message:"请输入形象名称"}],avatar:[{required:!0,type:"string",message:"请选择形象头像"}],image:[{required:!0,type:"string",message:"请选择形象封面"}],wide_stay_video:[{required:!0,type:"string",message:"请选择宽屏人物待机视频"}],wide_talk_video:[{required:!0,type:"string",message:"请选择宽屏人物说话视频"}],vertical_stay_video:[{required:!0,type:"string",message:"请选择竖屏人物待机视频"}],vertical_talk_video:[{required:!0,type:"string",message:"请选择竖屏人物说话视频"}],dubbing:[{required:!0,message:"请选择配音角色"}],idle_time:[{required:!0,message:"请输入自定义闲时时间"}],idle_reply:[{required:!0,type:"string",message:"请输入闲时回复内容"}]}),r=(_,e)=>{o.value[_]=H+"/resource/digital/"+e},P=async()=>{var e;await((e=y.value)==null?void 0:e.validate()),o.value.idle_time=Number(o.value.idle_time),await(u.value?me(o.value):pe(o.value)),setTimeout(()=>{b.replace({path:"/application/layout/digital"})},1e3)};return(_,e)=>{const E=K,w=B,n=J,x=W,C=Y,j=Ce,F=Q,T=ve,O=z;return m(),g("div",$e,[i(E,{modelValue:l(V),"onUpdate:modelValue":e[0]||(e[0]=a=>ne(V)?V.value=a:null),title:l(v),"back-path":"/application/layout/digital","menu-list":k},null,8,["modelValue","title"]),t("div",Pe,[t("div",qe,[t("div",Ue,[i(T,null,{default:s(()=>[t("div",De,[t("div",Me,[i(F,{class:"p-4",ref_key:"formRef",ref:y,model:l(o),"label-width":"140px",rules:l(R)},{default:s(()=>[i(n,{label:"形象名称",prop:"name"},{default:s(()=>[t("div",Ee,[i(w,{modelValue:l(o).name,"onUpdate:modelValue":e[1]||(e[1]=a=>l(o).name=a),placeholder:"请输入形象名称",clearable:""},null,8,["modelValue"])])]),_:1}),i(n,{label:"形象头像",prop:"avatar"},{default:s(()=>[t("div",null,[t("div",null,[i(x,{modelValue:l(o).avatar,"onUpdate:modelValue":e[2]||(e[2]=a=>l(o).avatar=a)},null,8,["modelValue"])]),e[15]||(e[15]=t("div",{class:"form-tips"},"建议尺寸：50*50px",-1))])]),_:1}),i(n,{label:"形象封面",prop:"image"},{default:s(()=>[t("div",null,[t("div",null,[i(x,{modelValue:l(o).image,"onUpdate:modelValue":e[3]||(e[3]=a=>l(o).image=a)},null,8,["modelValue"])]),e[16]||(e[16]=t("div",{class:"form-tips"},"建议尺寸：280px×187px",-1))])]),_:1}),i(n,{label:"宽屏人物待机视频",prop:"wide_stay_video"},{default:s(()=>[t("div",null,[t("div",null,[i(C,{modelValue:l(o).wide_stay_video,"onUpdate:modelValue":e[4]||(e[4]=a=>l(o).wide_stay_video=a)},null,8,["modelValue"])]),t("div",Ie,[e[17]||(e[17]=c(" 格式为MP4，大小不能超过20M ")),t("span",{class:"text-primary cursor-pointer",onClick:e[5]||(e[5]=a=>r("wide_stay_video","wide_stay_video.mp4"))}," 使用默认视频 ")])])]),_:1}),i(n,{label:"宽屏人物说话视频",prop:"wide_talk_video"},{default:s(()=>[t("div",null,[t("div",null,[i(C,{modelValue:l(o).wide_talk_video,"onUpdate:modelValue":e[6]||(e[6]=a=>l(o).wide_talk_video=a)},null,8,["modelValue"])]),t("div",Re,[e[18]||(e[18]=c(" 格式为MP4，大小不能超过20M ")),t("span",{class:"text-primary cursor-pointer",onClick:e[7]||(e[7]=a=>r("wide_talk_video","wide_talk_video.mp4"))}," 使用默认视频 ")])])]),_:1}),i(n,{label:"竖屏人物待机视频",prop:"vertical_stay_video"},{default:s(()=>[t("div",null,[t("div",null,[i(C,{modelValue:l(o).vertical_stay_video,"onUpdate:modelValue":e[8]||(e[8]=a=>l(o).vertical_stay_video=a)},null,8,["modelValue"])]),t("div",Be,[e[19]||(e[19]=c(" 格式为MP4，大小不能超过20M ")),t("span",{class:"text-primary cursor-pointer",onClick:e[9]||(e[9]=a=>r("vertical_stay_video","vertical_stay_video.mp4"))}," 使用默认视频 ")])])]),_:1}),i(n,{label:"竖屏人物说话视频",prop:"vertical_talk_video"},{default:s(()=>[t("div",null,[t("div",null,[i(C,{modelValue:l(o).vertical_talk_video,"onUpdate:modelValue":e[10]||(e[10]=a=>l(o).vertical_talk_video=a)},null,8,["modelValue"])]),t("div",ze,[e[20]||(e[20]=c(" 格式为MP4，大小不能超过20M ")),t("span",{class:"text-primary cursor-pointer",onClick:e[11]||(e[11]=a=>r("vertical_talk_video","vertical_talk_video.mp4"))}," 使用默认视频 ")])])]),_:1}),i(n,{label:"配音角色",prop:"dubbing"},{default:s(()=>[t("div",Ae,[i(j,{modelValue:l(o).dubbing,"onUpdate:modelValue":e[12]||(e[12]=a=>l(o).dubbing=a)},null,8,["modelValue"])])]),_:1}),i(n,{label:"自定义闲时时间",prop:"idle_time"},{default:s(()=>[t("div",Ne,[i(w,{modelValue:l(o).idle_time,"onUpdate:modelValue":e[13]||(e[13]=a=>l(o).idle_time=a),placeholder:"请输入自定义闲时时间",type:"number"},{append:s(()=>e[21]||(e[21]=[c("秒")])),_:1},8,["modelValue"]),e[22]||(e[22]=t("div",{class:"form-tips"}," 例如：选择5s，每隔5秒就会有一个回复内容，内容是在闲时回复内容的文案 ",-1))])]),_:1}),i(n,{label:"闲时回复内容",prop:"idle_reply"},{default:s(()=>[t("div",Se,[i(w,{modelValue:l(o).idle_reply,"onUpdate:modelValue":e[14]||(e[14]=a=>l(o).idle_reply=a),placeholder:"请输入闲时回复内容",type:"textarea",rows:4,clearable:""},null,8,["modelValue"]),e[23]||(e[23]=t("div",{class:"form-tips"}," 根据自定义闲时时间段设置后形象回复的内容 ",-1))])]),_:1})]),_:1},8,["model","rules"])])])]),_:1})]),t("div",Le,[i(O,{type:"primary",onClick:P},{default:s(()=>e[24]||(e[24]=[c("保存")])),_:1})])])])])}}});export{bt as default};
