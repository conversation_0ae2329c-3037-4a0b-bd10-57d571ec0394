import{j as H,l as J,cO as K,f as C,cP as Q,cC as W,bv as X,E as R,e as Y,v as ee}from"./BBthjZaB.js";import{_ as te}from"./C-hk8zs7.js";import{_ as oe}from"./Bj9H9xtu.js";import{E as se,a as ne}from"./BikmaNnr.js";import{_ as ae,a as ie}from"./Bciq9nIH.js";import{E as le}from"./DNtYbVbo.js";import{_ as re}from"./BlCn8106.js";import{E as de}from"./Bwa6YuoW.js";/* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{u as me}from"./67xbGseh.js";import{a as ce}from"./CiYvFM4x.js";import{l as pe,r as T,b as _e,ak as ue,c as fe,k as he,M as l,N as _,Z as o,a0 as s,O as n,a7 as u,u as a,a6 as m,aa as ge,a1 as f,a4 as v,_ as ke,y as ve,n as ye}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./vwcCUF2-.js";import"./DCTLXrZ8.js";import"./Bw4533_S.js";import"./DO7Ufrqu.js";import"./DiPtOrlW.js";import"./EdzuQeTd.js";import"./BeV_2vTx.js";import"./JOeTFF2G.js";import"./DBBnJi1E.js";import"./B4Y9LdPA.js";/* empty css        */import"./BKq__9dH.js";import"./BuBpo4Nc.js";import"./Cpg3PDWZ.js";import"./ND-j-PcX.js";import"./9Bti1uB6.js";/* empty css        */import"./C98sO_nA.js";import"./DJi2sD98.js";import"./6TYmzuF5.js";import"./Cv6HhfEG.js";/* empty css        */const xe={class:"h-full"},be={class:"p-main"},Ce={class:"bg-white rounded-lg p-[20px]"},Ve={class:"border-b border-solid border-br-light pb-[10px]"},we={class:"text-tx-secondary ml-[10px]"},Ee={class:"py-main"},$e={class:"flex items-center flex-wrap"},Se={class:"flex-1"},Be=["href"],Re={class:"flex-none flex"},Te={class:"flex-1 min-h-0"},De={class:"flex items-center"},Fe={class:"ml-2"},Ne={key:0},Ue={key:1,class:"text-warning"},Ie={key:2,class:"text-success"},Pe=["onClick"],Le=["href"],je={class:"flex justify-end mt-4"},$t=pe({__name:"video_compositing",async setup(ze){let V,w;const E=H();J();const h=T({show:!1,url:""}),y=T({keyword:""}),{pager:r,getLists:c,resetPage:D}=me({fetchFun:K,params:y}),x=_e([]),F=i=>{x.value=i.map(t=>t.id)},$=async i=>{await C.confirm("确定要删除？"),await Q({ids:i}),c()},N=async(i,t)=>{const{value:g}=await C.prompt("修改视频名称","",{inputValue:t});await W({name:g,id:i}),c()},U=X(()=>{D()},1e3),I=i=>{C.confirm(`错误信息：${i}`,"失败原因",{showConfirmButton:!1,type:"error",cancelButtonText:"关闭"})},P=async i=>{h.show=!0,await ye(),h.url=i},L=(i,t)=>{ce(i,t)};[V,w]=ue(()=>c()),await V,w();let b=null;return fe(()=>r.extend,i=>{i.unfinished_num>0?b=setTimeout(()=>{c()},30*1e3):E.getUser()},{immediate:!0}),he(()=>{b&&clearTimeout(b)}),(i,t)=>{const g=R,S=te,B=oe,k=R,j=Y,d=se,z=ae,M=le,O=ne,q=re,A=de,Z=ie,G=ee;return l(),_("div",xe,[o(A,null,{default:s(()=>[n("div",be,[n("div",Ce,[n("div",Ve,[t[4]||(t[4]=n("span",{class:"text-2xl font-medium"},"视频合成",-1)),n("span",we," 剩余合成时长："+u(a(E).userInfo.video_num)+"分钟 ",1)]),n("div",Ee,[n("div",$e,[n("div",Se,[o(S,{custom:"",to:"/digital_human/design"},{default:s(({href:e})=>[n("a",{href:e,class:"mr-[10px]",target:"_blank"},[o(g,{type:"primary"},{default:s(()=>t[5]||(t[5]=[m("新建视频")])),_:1})],8,Be)]),_:1}),o(g,{disabled:!a(x).length,onClick:t[0]||(t[0]=e=>$(a(x)))},{default:s(()=>t[6]||(t[6]=[m("批量删除")])),_:1},8,["disabled"])]),n("div",Re,[o(j,{modelValue:a(y).keyword,"onUpdate:modelValue":t[1]||(t[1]=e=>a(y).keyword=e),placeholder:"搜索",onInput:a(U)},{prepend:s(()=>[o(k,{loading:a(r).loading},{icon:s(()=>[o(B,{name:"el-icon-Search"})]),_:1},8,["loading"])]),_:1},8,["modelValue","onInput"]),o(g,{circle:"",plain:"",class:"ml-[10px]",onClick:a(c)},{icon:s(()=>[o(B,{name:"el-icon-Refresh"})]),_:1},8,["onClick"])])])]),n("div",Te,[ge((l(),f(O,{height:"100%",size:"large",data:a(r).lists,onSelectionChange:F},{default:s(()=>[o(d,{type:"selection",width:"55"}),o(d,{label:"文件名称","min-width":"200"},{default:s(({row:e})=>[n("div",De,[e.video_url?(l(),f(z,{key:0,onClick:p=>P(e.video_url),class:"cursor-pointer",type:"video","file-size":"70px",uri:e.video_url},null,8,["onClick","uri"])):v("",!0),n("div",Fe,u(e.name),1)])]),_:1}),o(d,{label:"生成进度","min-width":"200"},{default:s(({row:e})=>[e.status!==1?(l(),f(M,{key:0,percentage:e.synthetic_schedule},null,8,["percentage"])):(l(),_(ke,{key:1},[m("-")],64))]),_:1}),o(d,{label:"任务状态","min-width":"120"},{default:s(({row:e})=>[e.status==1?(l(),_("span",Ne,u(e.status_desc),1)):e.status==2?(l(),_("span",Ue,u(e.status_desc),1)):e.status==3?(l(),_("span",Ie,u(e.status_desc),1)):(l(),_("span",{key:3,class:"text-error cursor-pointer",onClick:p=>I(e.fail_reason)},u(e.status_desc),9,Pe))]),_:1}),o(d,{label:"消耗时长",prop:"consume_time","min-width":"120"}),o(d,{label:"最后更新时间",prop:"update_time","min-width":"180","show-tooltip-when-overflow":""}),o(d,{label:"操作",width:"180",fixed:"right"},{default:s(({row:e})=>[e.status==3&&e.video_url?(l(),f(k,{key:0,type:"primary",link:"",onClick:p=>L(e.video_url,e.name)},{default:s(()=>t[7]||(t[7]=[m(" 下载 ")])),_:2},1032,["onClick"])):v("",!0),e.status==1||e.status==4?(l(),f(S,{key:1,custom:"",to:`/digital_human/design?id=${e.id}`},{default:s(({href:p})=>[n("a",{href:p,class:"mr-[10px]",target:"_blank"},[o(k,{type:"primary",link:""},{default:s(()=>t[8]||(t[8]=[m(" 编辑 ")])),_:1})],8,Le)]),_:2},1032,["to"])):v("",!0),o(k,{type:"primary",link:"",onClick:p=>N(e.id,e.name)},{default:s(()=>t[9]||(t[9]=[m(" 重命名 ")])),_:2},1032,["onClick"]),e.delete_btn?(l(),f(k,{key:2,type:"danger",link:"",onClick:p=>$([e.id])},{default:s(()=>t[10]||(t[10]=[m(" 删除 ")])),_:2},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"])),[[G,a(r).loading]])]),n("div",je,[o(q,{modelValue:a(r),"onUpdate:modelValue":t[2]||(t[2]=e=>ve(r)?r.value=e:null),onChange:a(c)},null,8,["modelValue","onChange"])])])])]),_:1}),o(Z,{modelValue:a(h).show,"onUpdate:modelValue":t[3]||(t[3]=e=>a(h).show=e),url:a(h).url,type:"video"},null,8,["modelValue","url"])])}}});export{$t as default};
