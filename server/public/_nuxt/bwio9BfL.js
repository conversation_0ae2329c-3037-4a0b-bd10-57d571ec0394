import{_ as h}from"./DMHEbzLi.js";import{a as S,b,E as g}from"./DFChVafq.js";import{f as z}from"./DAgm18qP.js";/* empty css        *//* empty css        *//* empty css        */import{u as v,c as D}from"./DioAmjF5.js";import{l as k,b as C,M as a,N as p,O as r,Z as s,a0 as n,_ as y,aq as E,u as c,a7 as l,a1 as B,a6 as N}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./CfiDmG6E.js";import"./DCTLXrZ8.js";import"./Uw_fYagh.js";import"./9Bti1uB6.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./Dh2YTmLQ.js";import"./yC1JkMCN.js";const V={class:"flex items-center justify-center"},j={class:"bg-white min-w-[90px] flex item-center py-[6px] px-[10px] text-tx-primary rounded-md shadow-[0_2px_6px_#ebefff]"},I={class:"flex-1 mr-[4px]"},Y=k({__name:"select-size",setup(M){const m=C(D),e=v(),_=async t=>{var o;if(t.id!==e.defaultSize.resolution){if(!((o=e.getCanvasJson())!=null&&o.objects.length)){e.changeSize(t);return}await z.confirm("是否确认更改画布尺寸？当前画面所有设置将被重置且无法恢复"),e.changeSize(t)}};return(t,o)=>{const d=h,f=g,u=S,x=b;return a(),p("div",V,[o[0]||(o[0]=r("span",{class:"mr-[10px]"},"画布尺寸",-1)),s(x,null,{dropdown:n(()=>[s(u,null,{default:n(()=>[(a(!0),p(y,null,E(c(m),(i,w)=>(a(),B(f,{key:w,onClick:$=>_(i)},{default:n(()=>[N(l(i.label),1)]),_:2},1032,["onClick"]))),128))]),_:1})]),default:n(()=>[r("div",j,[r("span",I,l(c(e).defaultSize.label),1),s(d,{name:"el-icon-ArrowDown"})])]),_:1})])}}});export{Y as default};
