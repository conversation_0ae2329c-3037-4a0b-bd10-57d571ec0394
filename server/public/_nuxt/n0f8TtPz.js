import{E as A}from"./D2o9q88N.js";import{E as M,v as q,f as H}from"./CylNgAGi.js";import{E as J,a as O}from"./CGsEtxyu.js";import{_ as Z}from"./a_MrTnou.js";import{E as G,a as K,b as Q}from"./Bdk6a5L7.js";import{_ as W}from"./DPTIapjF.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import{u as X}from"./67xbGseh.js";import{_ as Y}from"./-8u5pS_P.js";import{_ as ee}from"./BohYg9ra.js";import{_ as te}from"./BGQTDGoa.js";import{w as ae,y as oe,A as ne,z as se,x as le}from"./Bo3PTL3c.js";import{l as ie,j as y,b as re,C as me,r as de,c as pe,M as h,N as ce,Z as a,O as f,a0 as o,a6 as r,aa as _e,u as p,a1 as fe,y as ue,_ as ge}from"./Dp9aCaJ6.js";const ye={class:"mt-4"},we={class:"mt-4"},ke={class:"flex items-center"},be={class:"flex justify-end mt-4"},Ae=ie({__name:"js",props:{appId:{}},emits:["back"],setup(E,{emit:R}){const w=E,x=R,u=y(),k=y(),c=y(),b=re({}),{appId:S}=me(w),_=de({robot_id:S,type:2}),{pager:m,getLists:s}=X({fetchFun:ae,params:_});s();const $=async t=>{await H.confirm("确定删除？"),await le({id:t,type:_.type}),s()},j=(t,e)=>{switch(t){case"delete":$(e.id);break;case"edit":v(e);break;case"usage":V(e)}},v=t=>{var n;let e=null;t&&(e={id:t.id,name:t.name,password:t.secret}),(n=u.value)==null||n.open(e)},B=async(t,e)=>{var n;await(e=="add"?oe({...t,..._}):ne({id:t.id,name:t.name,password:t.password})),(n=u.value)==null||n.close(),s()},D=t=>{b.value=t},I=t=>{var e;D(t),(e=k.value)==null||e.open()},V=t=>{var e,n;(e=c.value)==null||e.open(),(n=c.value)==null||n.setFormData(t)},N=async t=>{var e;await se({...t,..._}),(e=c.value)==null||e.close(),s()};return pe(()=>w.appId,()=>{s()}),(t,e)=>{const n=A,l=M,d=J,P=Z,g=G,T=K,F=Q,L=O,U=W,z=q;return h(),ce(ge,null,[a(n,{content:"发布JS嵌入",onBack:e[0]||(e[0]=i=>x("back"))}),f("div",ye,[a(l,{type:"primary",onClick:e[1]||(e[1]=i=>v())},{default:o(()=>e[3]||(e[3]=[r(" 创建链接 ")])),_:1})]),f("div",we,[_e((h(),fe(L,{data:p(m).lists,size:"large"},{default:o(()=>[a(d,{label:"apikey",prop:"apikey","min-width":"200"}),a(d,{label:"分享名称",prop:"name","min-width":"180","show-tooltip-when-overflow":""}),a(d,{label:"访问密码",prop:"secret","min-width":"120"}),a(d,{label:"最后使用时间",prop:"use_time","min-width":"180"}),a(d,{label:"操作","min-width":"150",fixed:"right"},{default:o(({row:i})=>[f("div",ke,[a(l,{type:"primary",link:"",onClick:C=>I(i)},{default:o(()=>e[4]||(e[4]=[r(" 查看代码 ")])),_:2},1032,["onClick"]),a(F,{class:"ml-[10px]",onCommand:C=>j(C,i)},{dropdown:o(()=>[a(T,null,{default:o(()=>[a(g,{command:"edit"},{default:o(()=>[a(l,{type:"primary",link:""},{default:o(()=>e[6]||(e[6]=[r(" 编辑 ")])),_:1})]),_:1}),a(g,{command:"usage"},{default:o(()=>[a(l,{type:"primary",link:""},{default:o(()=>e[7]||(e[7]=[r(" 用量设置 ")])),_:1})]),_:1}),a(g,{command:"delete"},{default:o(()=>[a(l,{type:"danger",link:""},{default:o(()=>e[8]||(e[8]=[r(" 删除 ")])),_:1})]),_:1})]),_:1})]),default:o(()=>[a(l,{type:"primary",link:""},{default:o(()=>[e[5]||(e[5]=r(" 更多 ")),a(P,{name:"el-icon-ArrowDown"})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[z,p(m).loading]]),f("div",be,[a(U,{modelValue:p(m),"onUpdate:modelValue":e[2]||(e[2]=i=>ue(m)?m.value=i:null),onChange:p(s)},null,8,["modelValue","onChange"])])]),a(Y,{ref_key:"createShareRef",ref:u,isShowChatType:!1,onConfirm:B},null,512),a(ee,{ref_key:"jsEmbeddingRef",ref:k,"channel-id":p(b).apikey},null,8,["channel-id"]),a(te,{ref_key:"usageSettingsRef",ref:c,onConfirm:N},null,512)],64)}}});export{Ae as _};
