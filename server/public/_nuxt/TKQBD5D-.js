import{b as c}from"./CzTOiozM.js";import{l,M as t,N as s,O as r,_ as n,aq as i,u as _,a4 as o,a7 as p}from"./Dp9aCaJ6.js";const u={class:"layout-footer flex justify-center text-center text-xs py-[8px] bg-transparent"},d={class:"ml-2 text-tx-secondary"},f=["href"],x=["src"],y={class:"ml-1"},b=l({__name:"index",setup(h){const a=c();return(m,g)=>(t(),s("footer",u,[r("div",d,[(t(!0),s(n,null,i(_(a).getCopyrightConfig,e=>(t(),s(n,{key:e.key},[e.key?(t(),s("a",{key:0,class:"inline-flex items-center justify-center mx-2 hover:underline",href:e.value,target:"_blank"},[e.icon?(t(),s("img",{key:0,src:e.icon,alt:"备案号",style:{width:"20px",height:"20px"}},null,8,x)):o("",!0),r("span",y,p(e.key),1)],8,f)):o("",!0)],64))),128))])]))}});export{b as _};
