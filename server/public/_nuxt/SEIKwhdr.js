import{_ as ue}from"./BWd1nnnI.js";import{f as N,E as Q,o as pe,e as X,p as de,a as me,b as _e,j as fe,v as ce}from"./DNaNbs6R.js";import{E as ge,a as ye}from"./ClWRsIx9.js";import{P as be}from"./BFjeuWRo.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */import{l as Y,j as O,r as Z,M as f,N as x,Z as e,a0 as o,V as ke,a6 as g,O as c,u as a,a7 as V,a1 as S,a4 as k,b as I,i as ve,F as we,ai as xe,aa as Ve,y as Ee,n as H}from"./Dp9aCaJ6.js";import{E as Re,a as Ce}from"./CUCgy8P_.js";import{a as Se,E as he}from"./CSbJ_DKr.js";import{_ as ze}from"./CJcXdMmM.js";import{_ as De}from"./DaE5R0H9.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import{u as $e}from"./67xbGseh.js";import{_ as Fe}from"./h8DA_YXX.js";import{i as J,h as qe,j as Ue,l as Ie}from"./BNnETjxs.js";import{u as Ne}from"./BE7GAo-z.js";const Pe={class:"export-data"},je={class:"flex"},Be=Y({__name:"index",props:{params:{type:Object,default:()=>({})},pageSize:{type:Number,default:25},fetchFun:{type:Function,required:!0},exportFun:{type:Function}},setup(h){const P=O(),p=h,z=O(),i=Z({page_type:0,page_start:1,page_end:200,file_name:""}),D={page_start:[{required:!0,message:"请输入起始页码"},{type:"number",message:"页码必须是整数"},{validator:(r,s,n)=>{if(s<=0)return n(new Error("页码必须大于0"));n()}}],page_end:[{required:!0,message:"请输入结束页码"},{type:"number",message:"页码必须是整数"},{validator:(r,s,n)=>{if(s<=0)return n(new Error("页码必须大于0"));n()}}]},_=Z({count:0,sum_page:0,page_size:0,max_page:0,all_max_size:0}),E=async()=>{const r=await p.fetchFun({...p.params,page_size:p.pageSize,export:1});Object.assign(_,r),i.file_name=r.file_name,i.page_end=r.page_end,i.page_start=r.page_start},$=async()=>{var r,s;await((r=P.value)==null?void 0:r.validate()),N.loading("正在导出中...");try{const n=p.exportFun?await p.exportFun({...p.params,...i,page_size:p.pageSize,export:2}):await p.fetchFun({...p.params,...i,page_size:p.pageSize,export:2});(s=z.value)==null||s.close(),N.closeLoading(),n!=null&&n.url&&window.open(n.url,"blank")}catch{N.closeLoading()}};return E(),(r,s)=>{const n=Q,u=pe,v=ge,F=ye,q=X,j=de,B=be;return f(),x("div",Pe,[e(B,{ref_key:"popupRef",ref:z,title:"导出设置",width:"500px","confirm-button-text":"确认导出",async:!0,onOpen:E,onConfirm:$},{trigger:o(()=>[ke(r.$slots,"trigger",{},()=>[e(n,null,{default:o(()=>s[4]||(s[4]=[g("导出")])),_:1})])]),default:o(()=>[c("div",null,[e(j,{ref_key:"formRef",ref:P,model:a(i),"label-width":"120px",rules:D},{default:o(()=>[e(u,{label:"数据量："},{default:o(()=>[g(" 预计导出"+V(a(_).count)+"条数据， 共"+V(a(_).sum_page)+"页，每页"+V(a(_).page_size)+"条数据 ",1)]),_:1}),e(u,{label:"导出限制："},{default:o(()=>[g(" 每次导出最大允许"+V(a(_).max_page)+"页，共"+V(a(_).all_max_size)+"条数据 ",1)]),_:1}),e(u,{prop:"page_type",label:"导出范围：",required:""},{default:o(()=>[e(F,{modelValue:a(i).page_type,"onUpdate:modelValue":s[0]||(s[0]=y=>a(i).page_type=y)},{default:o(()=>[e(v,{label:0},{default:o(()=>s[5]||(s[5]=[g("全部导出")])),_:1}),e(v,{label:1},{default:o(()=>s[6]||(s[6]=[g("分页导出")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(i).page_type==1?(f(),S(u,{key:0,label:"分页范围："},{default:o(()=>[c("div",je,[e(u,{prop:"page_start"},{default:o(()=>[e(q,{style:{width:"140px"},modelValue:a(i).page_start,"onUpdate:modelValue":s[1]||(s[1]=y=>a(i).page_start=y),modelModifiers:{number:!0},placeholder:""},null,8,["modelValue"])]),_:1}),s[7]||(s[7]=c("span",{class:"flex-none ml-2 mr-2"},"页，至",-1)),e(u,{prop:"page_end"},{default:o(()=>[e(q,{style:{width:"140px"},modelValue:a(i).page_end,"onUpdate:modelValue":s[2]||(s[2]=y=>a(i).page_end=y),modelModifiers:{number:!0},placeholder:""},null,8,["modelValue"])]),_:1})])]),_:1})):k("",!0),e(u,{label:"导出文件名称：",prop:"file_name"},{default:o(()=>[e(q,{modelValue:a(i).file_name,"onUpdate:modelValue":s[3]||(s[3]=y=>a(i).file_name=y),placeholder:"请输入导出文件名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:3},512)])}}}),Le={class:"pb-[20px] flex items-center font-bold cursor-pointer"},Oe={class:"ml-2"},Te={class:"flex"},Me={class:"ml-auto flex"},Ke={class:"min-w-[240px]"},Ae={class:"min-w-[180px] ml-2"},Ge={key:0},We={key:1,class:"text-warning"},Ze={key:2,class:"text-success"},He={key:3,class:"text-danger"},Je={key:4,class:"text-danger"},Qe={class:"flex justify-end mt-4"},kt=Y({__name:"itemList",props:{itemId:{type:Number,default:0},itemName:{type:String,default:""}},emits:["back"],setup(h,{emit:P}){const p=me(),z=_e(),i=fe(),D=p.query.id,_=I(),E=h,$=ve("knowDetail"),r=I(!1),s=O(),n=I({keyword:"",status:"",fd_id:E.itemId,kb_id:p.query.id}),{pager:u,getLists:v,resetPage:F,resetParams:q}=$e({fetchFun:J,params:n.value}),j=I(0),B=async()=>{const d=u.lists.map(m=>m.uuid),{tasks:t,lists:b}=await Ie({...n.value,uuids:d});u.lists.map(m=>{const U=b.findIndex(L=>m.uuid==L.uuid);m.status=b[U].status,m.tokens=b[U].tokens}),j.value=t.length,t.length==0&&(ee(),i.getUser())},y=async()=>{B()},{start:R,end:ee}=Ne(y,{time:3e3,key:"kb_id"}),te=async()=>{r.value=!0,await H(),s.value.open({kb_id:p.query.id,fd_id:E.itemId})},ae=async d=>{r.value=!0,await H(),s.value.open({kb_id:p.query.id,fd_id:E.itemId,uuid:d})},T=async d=>{await qe({kb_id:D,uuids:d}),v(),R()},M=async d=>{await N.confirm("请确认是否删除！"),await Ue({kb_id:D,uuids:d}),v(),R()},oe=async()=>{var t;const d=(t=_.value)==null?void 0:t.getSelectionRows().map(b=>b.uuid);await M(d)},se=async()=>{var t;const d=(t=_.value)==null?void 0:t.getSelectionRows().map(b=>b.uuid);await T(d)},le=()=>{F()};return we(async()=>{await v(),R()}),(d,t)=>{var A,G;const b=ue,m=Q,U=Be,L=X,C=Re,ne=Ce,w=he,K=ze,ie=De,re=ce;return f(),x("div",null,[c("div",Le,[c("div",{onClick:t[0]||(t[0]=l=>d.$emit("back")),class:"flex items-center"},[e(b,{name:"el-icon-Back",size:"16"}),c("span",Oe,V(h.itemName),1)])]),c("div",Te,[c("div",null,[e(m,{type:"primary",disabled:a($).power===3,onClick:te},{default:o(()=>t[7]||(t[7]=[g(" 录入数据 ")])),_:1},8,["disabled"]),e(U,{class:"mx-2.5 inline-block","fetch-fun":a(J),params:a(n),"page-size":a(u).size},{trigger:o(()=>[e(m,{disabled:a($).power===3},{default:o(()=>t[8]||(t[8]=[g(" 一键导出 ")])),_:1},8,["disabled"])]),_:1},8,["fetch-fun","params","page-size"]),e(m,{disabled:!((A=a(_))!=null&&A.getSelectionRows().length),onClick:se},{default:o(()=>t[9]||(t[9]=[g(" 批量重试 ")])),_:1},8,["disabled"]),e(m,{disabled:!((G=a(_))!=null&&G.getSelectionRows().length),onClick:oe},{default:o(()=>t[10]||(t[10]=[g(" 批量删除 ")])),_:1},8,["disabled"])]),c("div",Me,[c("div",Ke,[e(L,{modelValue:a(n).keyword,"onUpdate:modelValue":t[1]||(t[1]=l=>a(n).keyword=l),placeholder:"请输入问题/回答内容关键词进行搜索",clearable:"",onKeyup:xe(a(F),["enter"])},null,8,["modelValue","onKeyup"])]),c("div",Ae,[e(ne,{modelValue:a(n).status,"onUpdate:modelValue":t[2]||(t[2]=l=>a(n).status=l),onChange:le},{default:o(()=>[e(C,{label:"全部",value:""}),e(C,{label:"等待学习",value:0}),e(C,{label:"学习中",value:1}),e(C,{label:"学习失败",value:3}),e(C,{label:"学习成功",value:2})]),_:1},8,["modelValue"])])])]),Ve((f(),S(a(Se),{ref_key:"tableRef",ref:_,class:"mt-4",data:a(u).lists,size:"large","row-class-name":"h-[70px]"},{default:o(()=>[e(w,{type:"selection",width:"55"}),e(w,{label:"文档内容",prop:"question","min-width":"200"},{default:o(({row:l})=>[e(K,{content:l.question,line:2,teleported:!0,effect:"light",placement:"right"},null,8,["content"])]),_:1}),e(w,{label:"补充内容",prop:"answer","min-width":"200"},{default:o(({row:l})=>[e(K,{content:l.answer||"-",line:2,teleported:!0,effect:"light",placement:"right"},null,8,["content"])]),_:1}),e(w,{label:"学习状态",prop:"source","min-width":"150"},{default:o(({row:l})=>[l.status==0?(f(),x("div",Ge,"等待学习")):k("",!0),l.status==1?(f(),x("div",We," 学习中 ")):k("",!0),l.status==2?(f(),x("div",Ze," 学习成功 ")):k("",!0),l.status==3?(f(),x("div",He," 学习失败 ")):k("",!0),l.status==3?(f(),x("div",Je," 原因："+V(l.error),1)):k("",!0)]),_:1}),e(w,{label:`消耗${a(z).getTokenUnit}`,prop:"tokens","min-width":"150"},null,8,["label"]),e(w,{label:"最后更新时间",prop:"update_time","min-width":"150"}),e(w,{label:"操作",prop:"source","min-width":"180"},{default:o(({row:l})=>[l.status!=1?(f(),S(m,{key:0,type:"primary",onClick:W=>ae(l.uuid),link:""},{default:o(()=>t[11]||(t[11]=[g("修正")])),_:2},1032,["onClick"])):k("",!0),l.status==3?(f(),S(m,{key:1,type:"primary",onClick:W=>T([l.uuid]),link:""},{default:o(()=>t[12]||(t[12]=[g("重试")])),_:2},1032,["onClick"])):k("",!0),e(m,{type:"danger",onClick:W=>M([l.uuid]),link:""},{default:o(()=>t[13]||(t[13]=[g("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[re,a(u).loading]]),c("div",Qe,[e(ie,{modelValue:a(u),"onUpdate:modelValue":t[3]||(t[3]=l=>Ee(u)?u.value=l:null),onChange:t[4]||(t[4]=()=>{a(R)(),a(v)()})},null,8,["modelValue"])]),a(r)?(f(),S(Fe,{key:0,ref_key:"popEntryRef",ref:s,onSuccess:t[5]||(t[5]=()=>{r.value=!1,a(v)(),a(R)()}),onClose:t[6]||(t[6]=l=>r.value=!1)},null,512)):k("",!0)])}}});export{kt as _};
