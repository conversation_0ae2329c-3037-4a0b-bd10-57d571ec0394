import{_ as l}from"./CQG-tpkc.js";import{E as c}from"./dsHDlwYU.js";import"./Br7V4jS9.js";/* empty css        *//* empty css        */import{l as m,M as o,N as s,O as t,a7 as p,a4 as n,a1 as d,a0 as f,Z as u,V as _}from"./Dp9aCaJ6.js";const h={class:"flex justify-between select-none"},g={class:"flex items-center gap-2 font-bold mb-2"},v={key:0,class:"text-error ml-1"},x={class:"flex items-center cursor-pointer text-[#999999]"},V=m({__name:"sidbar-item-title",props:{title:{type:String,default:""},tips:{type:String,default:""},required:{type:Boolean,default:!1}},setup(e){return(r,y)=>{const a=l,i=c;return o(),s("div",h,[t("h3",g,[t("p",null,[t("span",null,p(e.title),1),e.required?(o(),s("span",v,"*")):n("",!0)]),e.tips!=="none"&&e.tips!==""?(o(),d(i,{key:0,placement:"right",width:200,trigger:"hover","show-arrow":!1,transition:"custom-popover",content:e.tips},{reference:f(()=>[t("div",x,[u(a,{name:"el-icon-QuestionFilled",size:14})])]),_:1},8,["content"])):n("",!0)]),t("div",null,[_(r.$slots,"default")])])}}});export{V as _};
