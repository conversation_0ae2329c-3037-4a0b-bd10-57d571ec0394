import{l as y,$ as B,b as T,m as b,M as n,a1 as g,a0 as i,aa as $,ab as x,O as p,u as e,X as u,a3 as z,a4 as m,N as d,V as C,a7 as k,Z as a,_ as S,ac as N,a6 as D}from"./Dp9aCaJ6.js";import{K as M,ag as U,ct as h,M as A,J as E,O as F,cu as O,P,h as J,o as K,e as X}from"./CzTOiozM.js";import{E as Z}from"./Hgq3jOTy.js";import"./DP2rzg_V.js";import{_ as j}from"./DlAUqK2U.js";const q=["light","dark"],G=M({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:U(h),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:<PERSON><PERSON>an,center:<PERSON>olean,effect:{type:String,values:q,default:"light"}}),H={close:f=>f instanceof MouseEvent},L=y({name:"ElAlert"}),Q=y({...L,props:G,emits:H,setup(f,{emit:v}){const _=f,{Close:I}=O,t=B(),s=A("alert"),l=T(!0),w=b(()=>h[_.type]),V=b(()=>[s.e("icon"),{[s.is("big")]:!!_.description||!!t.default}]),c=b(()=>({"with-description":_.description||t.default})),r=o=>{l.value=!1,v("close",o)};return(o,oe)=>(n(),g(N,{name:e(s).b("fade"),persisted:""},{default:i(()=>[$(p("div",{class:u([e(s).b(),e(s).m(o.type),e(s).is("center",o.center),e(s).is(o.effect)]),role:"alert"},[o.showIcon&&e(w)?(n(),g(e(E),{key:0,class:u(e(V))},{default:i(()=>[(n(),g(z(e(w))))]),_:1},8,["class"])):m("v-if",!0),p("div",{class:u(e(s).e("content"))},[o.title||o.$slots.title?(n(),d("span",{key:0,class:u([e(s).e("title"),e(c)])},[C(o.$slots,"title",{},()=>[D(k(o.title),1)])],2)):m("v-if",!0),o.$slots.default||o.description?(n(),d("p",{key:1,class:u(e(s).e("description"))},[C(o.$slots,"default",{},()=>[D(k(o.description),1)])],2)):m("v-if",!0),o.closable?(n(),d(S,{key:2},[o.closeText?(n(),d("div",{key:0,class:u([e(s).e("close-btn"),e(s).is("customed")]),onClick:r},k(o.closeText),3)):(n(),g(e(E),{key:1,class:u(e(s).e("close-btn")),onClick:r},{default:i(()=>[a(e(I))]),_:1},8,["class"]))],64)):m("v-if",!0)],2)],2),[[x,l.value]])]),_:3},8,["name"]))}});var R=F(Q,[["__file","alert.vue"]]);const W=P(R),Y={class:"flow-config pt-[10px]"},ee=y({__name:"flow-config",props:{modelValue:{}},emits:["update:modelValue"],setup(f,{emit:v}){const t=J(f,"modelValue",v);return(s,l)=>{const w=W,V=Z,c=K,r=X;return n(),d("div",Y,[a(w,{title:"注意事项",type:"warning",closable:!1,class:"mb-[20px]"},{default:i(()=>l[5]||(l[5]=[p("ol",{class:"list-decimal pl-[20px]"},[p("li",null,"coze工作流配置地址：https://www.coze.cn （工作空间->资源库）"),p("li",null,"启用后对话时将使用coze工作流作为响应结果，相似问题输出仍使用AI模型输出"),p("li",null,"coze工作流设置时输入变量名需为“input”,输出变量名需为“output_text”或“output_image”"),p("li",null,"输出变量“output_text”对应为输出文本内容，输出变量“output_image”对应为输出图片资源，目前仅支持单图")],-1)])),_:1}),a(c,{label:"启用 coze 工作流",prop:"flow_status"},{default:i(()=>[p("div",null,[a(V,{modelValue:e(t).flow_status,"onUpdate:modelValue":l[0]||(l[0]=o=>e(t).flow_status=o),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])]),_:1}),e(t).flow_status===1?(n(),d(S,{key:0},[a(c,{label:"应用 ID",prop:"flow_config.app_id"},{default:i(()=>[a(r,{class:"!w-[320px]",modelValue:e(t).flow_config.app_id,"onUpdate:modelValue":l[1]||(l[1]=o=>e(t).flow_config.app_id=o),placeholder:"请输入应用 ID"},null,8,["modelValue"])]),_:1}),a(c,{label:"工作流 ID",prop:"flow_config.workflow_id"},{default:i(()=>[a(r,{class:"!w-[320px]",modelValue:e(t).flow_config.workflow_id,"onUpdate:modelValue":l[2]||(l[2]=o=>e(t).flow_config.workflow_id=o),placeholder:"请输入工作流 ID"},null,8,["modelValue"])]),_:1}),a(c,{label:"智能体 ID",prop:"flow_config.bot_id"},{default:i(()=>[a(r,{class:"!w-[320px]",modelValue:e(t).flow_config.bot_id,"onUpdate:modelValue":l[3]||(l[3]=o=>e(t).flow_config.bot_id=o),placeholder:"请输入智能体 ID"},null,8,["modelValue"])]),_:1}),a(c,{label:"token",prop:"flow_config.api_token"},{default:i(()=>[a(r,{class:"!w-[320px]",modelValue:e(t).flow_config.api_token,"onUpdate:modelValue":l[4]||(l[4]=o=>e(t).flow_config.api_token=o),placeholder:"token"},null,8,["modelValue"])]),_:1})],64)):m("",!0)])}}}),ie=j(ee,[["__scopeId","data-v-bdeafcb5"]]);export{ie as default};
