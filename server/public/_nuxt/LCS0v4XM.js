import{_ as $}from"./DDaEm-_F.js";import{l as A,b as O,j,by as q,c$ as H,f as p,E}from"./B5S_Er7H.js";import{E as J}from"./DySxZBuW.js";/* empty css        */import{l as P,b as m,j as Z,c as T,M as f,N as g,O as o,Z as h,a0 as y,u as l,y as S,a1 as F,a6 as G,a4 as C,aa as K,ay as Q,a7 as B}from"./Dp9aCaJ6.js";import{u as W}from"./pttW598Y.js";import{c as X}from"./B_1915px.js";import{_ as Y}from"./DlAUqK2U.js";const ee={class:"bg-body rounded-[12px] w-[330px] h-full flex flex-col"},te={class:"flex-1 min-h-0"},oe={class:"px-[15px] py-[15px]"},se={class:"mt-[10px] flex"},ae={class:"mt-[15px]"},ne={class:"flex"},le={class:"px-[15px] pb-[15px]"},re={key:0,class:"text-sm ml-[10px]"},ie={key:1,class:"text-sm ml-[10px]"},ce=P({__name:"control-panel",emits:["update","history","refresh"],setup(ue,{expose:I,emit:L}){const b=L,U=A(),d=O(),i=j(),c=m(""),s=m(""),x=Z(null),r=m(!1),{data:n,refresh:V}=W(()=>H(),{lazy:!0,default(){return{}}},"$UJscNzO9rO"),_=m(-1),k=()=>{var e;const t=(e=n.value.example_content)==null?void 0:e.length;if(t){let a=Math.round(Math.random()*(t-1));_.value===a&&(a<t-1?a++:a--),_.value=a}};T(n,()=>{!s.value&&k()}),T(_,t=>{if(!n.value.example_content)return;const e=n.value.example_content[t];e&&(s.value=e)});const R=()=>{i.isLogin||i.toggleShowLogin()};let u=null;const M=async()=>{if(!i.isLogin)return i.toggleShowLogin();if(!c.value)return p.msgError("请输入内容");r.value||(r.value=!0,u=X({type:4,question:c.value}),s.value="",u.addEventListener("chat",({data:t})=>{const{data:e,index:a}=t;s.value+=e}),u.addEventListener("finish",({data:t})=>{const{data:e,index:a}=t;e&&(s.value+=e)}),u.addEventListener("close",async()=>{V(),b("refresh"),await i.getUser(),setTimeout(async()=>{r.value=!1,w()},600)}),u.addEventListener("error",async t=>{var e;if(((e=t.data)==null?void 0:e.code)===1100){d.getIsShowRecharge?(await p.confirm(`${d.getTokenUnit}数量已用完，请前往充值`),U.push("/user/recharge")):p.msgError(`${d.getTokenUnit}数量已用完。请联系客服增加`);return}t.errorType==="connectError"&&p.msgError("请求失败，请重试"),setTimeout(()=>{r.value=!1},200)}))},w=()=>{var t,e;(e=x.value)==null||e.scrollTo({top:(t=x.value)==null?void 0:t.scrollHeight})};return q(s,async t=>{setTimeout(()=>{b("update",t)},500),r.value&&w()},{throttle:500}),I({changDescInput(t){s.value=t}}),(t,e)=>{const a=$,N=E,z=J,D=E;return f(),g("div",ee,[o("div",te,[h(z,null,{default:y(()=>[o("div",oe,[o("div",null,[o("div",null,[e[2]||(e[2]=o("h3",{class:"font-bold"},[o("span",null,"帮我生成"),o("span",{class:"text-error"},"*")],-1)),o("div",se,[h(a,{modelValue:l(c),"onUpdate:modelValue":e[0]||(e[0]=v=>S(c)?c.value=v:null),maxlength:"99999",rows:7,clearable:!0,"show-word-limit":!1,customStyle:{paddingBottom:"24px"},placeholder:"请输入简单描述，AI将智能输出markdown内容",onClick:R},null,8,["modelValue"])])]),o("div",ae,[o("div",ne,[e[4]||(e[4]=o("h3",{class:"font-bold mr-auto"},[o("span",null,"需求描述")],-1)),l(n).is_example?(f(),F(N,{key:0,link:"",type:"primary",onClick:k},{default:y(()=>e[3]||(e[3]=[G(" 试试示例 ")])),_:1})):C("",!0)]),K(o("textarea",{ref_key:"textareaRef",ref:x,"onUpdate:modelValue":e[1]||(e[1]=v=>S(s)?s.value=v:null),class:"bg-page h-[400px] w-full mt-[10px] rounded-[12px] p-[10px] resize-none border-[1px] border-solid border-[transparent] focus:border-primary"},null,512),[[Q,l(s)]])])])])]),_:1})]),o("div",le,[h(D,{size:"large",type:"primary",class:"w-full !border-none",onClick:M,loading:l(r)},{default:y(()=>[e[5]||(e[5]=o("span",{class:"text-base font-bold"},"生成思维导图",-1)),l(n).member_free?(f(),g("span",re," 会员免费 ")):l(n).balance>0?(f(),g("span",ie," 消耗 "+B(l(n).balance)+" "+B(l(d).getTokenUnit),1)):C("",!0)]),_:1},8,["loading"])])])}}}),he=Y(ce,[["__scopeId","data-v-fcf65801"]]);export{he as default};
