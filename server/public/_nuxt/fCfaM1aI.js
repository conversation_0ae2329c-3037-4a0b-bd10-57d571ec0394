import{_ as ge}from"./9wPy3dd6.js";import{_ as he}from"./CSeJad-s.js";import{c as ye,a as ve,_ as xe,b as we}from"./38vf73Sp.js";import{_ as Se}from"./C_7xENts.js";import{i as Ce,b as be,j as ke,l as Le,bw as Ee,bx as Ie,by as Re,f as D,_ as Te,E as Ae}from"./CzTOiozM.js";import{E as $e,a as De}from"./CnH843Nq.js";import{_ as Fe}from"./CN8DIg3d.js";import{E as Ue}from"./CH0sQbfA.js";import{_ as Ve}from"./DAy4RZPQ.js";import{E as Ne}from"./DPZJZrNl.js";/* empty css        */import{l as Me,j as X,b as F,ak as Z,r as Be,c as ze,F as Oe,n as G,k as je,M as p,N as h,Z as d,a0 as c,O as f,u as e,_ as B,aq as z,as as Pe,a1 as L,a4 as U,a6 as qe,a9 as He,a7 as K,X as We}from"./Dp9aCaJ6.js";import{u as Je}from"./7WLOh-Vd.js";import{u as Q}from"./YCXpYPC4.js";import{u as Xe}from"./CcPlX2kz.js";import Ze from"./Wt7w0Hcr.js";import{_ as Ge}from"./BWdLBA_9.js";import{h as Ke,i as Qe,j as Ye,k as et,l as tt,e as ot,b as Y,c as st}from"./B_1915px.js";import{useSessionFiles as nt}from"./e4nr0tgR.js";import{_ as at}from"./DlAUqK2U.js";import"./4kyntJ1V.js";/* empty css        */import"./BBz865WS.js";import"./CSeNE23V.js";import"./n7JpCWQv.js";import"./v-ANcAMI.js";import"./Cfoh3Adv.js";import"./CLI3JalT.js";/* empty css        */import"./Doy9WyQ7.js";import"./Cj46UrnO.js";import"./Cpg3PDWZ.js";import"./Pei5_z3G.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./Bnncr18K.js";import"./Bb2-23m7.js";import"./DwFObZc_.js";import"./CWd238n9.js";import"./Bg2e09vA.js";import"./6MP-Yojp.js";import"./C5ASc731.js";import"./D7EFJCC3.js";import"./KKGJdshx.js";import"./CmG-gwTN.js";import"./DhT69RLv.js";import"./CLdudPaH.js";import"./CaD5i18d.js";import"./Cv6HhfEG.js";import"./D13MzIoa.js";import"./CQleycmY.js";/* empty css        *//* empty css        */import"./BGN3kN5U.js";import"./B2t9mnt6.js";import"./CJBWowqe.js";import"./CPMpBhdt.js";import"./CMdqNgMe.js";const it=Ce({id:"dialogueSate",state:()=>({sessionId:"",sessionLists:[]}),getters:{getCurrentSession:r=>r.sessionLists.find(x=>String(x.id)===String(r.sessionId))||{}},actions:{setSessionId(r=""){this.sessionId=String(r)},setSessionSelect(r){r||([r]=this.sessionLists),this.setSessionId((r==null?void 0:r.id)||"")},async getSessionLists(){const r=await Ke({page_type:0});return this.sessionLists=r.lists||[],this.setSessionSelect(),this.sessionLists},async sessionAdd(){await Qe({}),await this.getSessionLists(),this.setSessionSelect()},async sessionEdit(r){await Ye({...r}),await this.getSessionLists(),this.setSessionSelect(r)},async sessionClear(){await et(),await this.getSessionLists()},async sessionDelete(r){await tt({id:r}),await this.getSessionLists()}}}),rt={class:"h-full flex"},lt={class:"p-[16px]"},ct={class:"flex items-center justify-around text-xl font-medium px-[16px] pt-[16px] cursor-pointer"},pt={class:"flex-1 min-w-0 pr-4 py-4"},dt={class:"h-full flex flex-col bg-body rounded-[12px]"},ut={class:"flex-1 min-h-0"},mt={class:"my-[5px]"},ft={key:0,class:"flex flex-col",style:{"margin-left":"52px"}},_t=["onClick"],gt={class:"mr-2 text-tx-primary"},ht={key:1,class:"max-w-[1200px] mx-auto"},yt={class:"mb-[10px] px-[30px]"},vt={class:"flex gap-3 mr-3"},xt={class:"flex items-center h-12 px-3 bg-page rounded-lg max-w-xs line-clamp-1 overflow-hidden"},wt=Me({__name:"chat",async setup(r){var J;let x,V;const y=be(),w=ke(),N=X(),n=it(),ee=Le(),O=F(""),{copy:te}=Je(),C=nt();[x,V]=Z(()=>Q(()=>n.getSessionLists(),{lazy:!0},"$SUo5FcMjp8")),await x,V();const oe=Ee(),{data:l,refresh:j}=([x,V]=Z(()=>Q(()=>ot({type:1,category_id:n.sessionId,page_type:0}),{transform(a){return a.lists||[]},default(){return[]},lazy:!0},"$5oItIRu1UV")),x=await x,V(),x);(J=y.getChatConfig)!=null&&J.is_reopen&&(n.sessionAdd(),y.getChatConfig.is_reopen=0);const se=async()=>{if(!w.isLogin)return w.toggleShowLogin();n.sessionId&&(await D.confirm("确定清空记录？"),await Y({category_id:n.sessionId,type:1}),j())},M=F(-1),{lockFn:ne}=Xe(async()=>{const a=l.value[l.value.length-1],t=l.value.find(({id:v})=>v===a.id);t&&(M.value=a.id,l.value.splice(l.value.length-2,2),I(t.content))}),m=Be({show:!1,data:{url:"",name:"",type:"10"}}),ae=a=>{m.show=!!a.support_image,m.show||(m.data.url="")},ie=()=>{var a;if(!w.isLogin)return(a=N.value)==null||a.blur(),w.toggleShowLogin();R()};let s=null;const S=F(!1);let P=!1;const E=F([]),I=async(a,t="input")=>{var T;if(!w.isLogin)return w.toggleShowLogin();if(!a)return D.msgError("请输入问题");if(S.value)return;const v=Date.now();E.value=[],S.value=!0;const b=C.files.value.map(i=>({name:i.name,type:"30",url:i.url}));m.data.url&&b.push({...m.data}),l.value.push({type:1,content:a,files_plugin:b}),l.value.push({type:2,typing:!0,content:[""],reasoning:"",key:v}),(T=N.value)==null||T.setInputValue();const u=l.value.find(i=>i.key===v);n.sessionId||(P=!0,await n.sessionAdd(),P=!1),s=st({type:1,other_id:n.sessionId,question:a,model:O.value,annex:m.data.url?[{type:m.data.type,name:m.data.name,url:m.data.url}]:[],files:C.files.value.map(i=>({...i,type:"30"}))}),s.addEventListener("reasoning",({data:i})=>{const{data:_,index:g}=i;u.reasoning||(u.reasoning=""),u.reasoning+=_}),s.addEventListener("chat",({data:i})=>{const{data:_,index:g}=i;u.content[g]||(u.content[g]=""),u.content[g]+=_}),s.addEventListener("finish",({data:i})=>{const{data:_,index:g}=i;_&&(u.content[g]+=_),m.data.url=""}),s.addEventListener("question",({data:i})=>{E.value=JSON.parse(i.data)}),s.addEventListener("close",async()=>{M.value!==-1&&u.content[0].length&&(await Y({type:1,id:M.value}),M.value=-1),await w.getUser(),n.getCurrentSession.name==="新的会话"&&await n.sessionEdit({id:n.sessionId,name:a}),S.value=!1,u.typing=!1,setTimeout(async()=>{await j(),await G(),R()},1e3)}),s.addEventListener("error",async i=>{var _,g;if(t==="input"&&((_=N.value)==null||_.setInputValue(a)),((g=i.data)==null?void 0:g.code)===1100){y.getIsShowRecharge?(await D.confirm(`${y.getTokenUnit}数量已用完，请前往充值`),ee.push("/user/recharge")):D.msgError(`${y.getTokenUnit}数量已用完。请联系客服增加`);return}i.errorType==="connectError"&&D.msgError("请求失败，请重试"),["connectError","responseError"].includes(i.errorType)&&l.value.splice(l.value.length-2,2),u.typing=!1,setTimeout(()=>{S.value=!1},200)})},q=X(),H=F(),R=async()=>{var t,v,b;const a=(v=(t=q.value)==null?void 0:t.wrapRef)==null?void 0:v.scrollHeight;(b=q.value)==null||b.setScrollTop(a)},{height:re}=Ie(H);Re(re,()=>{S.value&&R()},{immediate:!0});const W=()=>{s==null||s.removeEventListener("chat"),s==null||s.removeEventListener("close"),s==null||s.removeEventListener("error"),s==null||s.removeEventListener("finish"),s==null||s.abort(),S.value=!1,E.value=[]};return ze(()=>n.sessionId,async(a,t)=>{!P&&a!=t&&(W(),await j(),R())}),Oe(async()=>{await G(),l.value.length&&R()}),je(()=>{W()}),(a,t)=>{const v=ge,b=he,u=ve,T=Se,i=Ae,_=xe,g=$e,le=De,ce=Fe,pe=Ue,de=Ve,ue=we,me=ye,fe=Ne,_e=Te;return p(),h("div",null,[d(_e,{name:"default"},{default:c(()=>[f("div",rt,[f("div",lt,[d(b,{modelValue:e(n).sessionId,"onUpdate:modelValue":t[0]||(t[0]=o=>e(n).sessionId=o),data:e(n).sessionLists,onAdd:e(n).sessionAdd,onEdit:e(n).sessionEdit,onDelete:e(n).sessionDelete,onClear:e(n).sessionClear,onClickItem:e(n).setSessionSelect},{top:c(()=>[f("div",ct,[t[7]||(t[7]=f("div",{class:"pb-[6px] text-primary border-solid border-b-[2px] border-primary"}," 问答助手 ",-1)),d(v,{to:"/dialogue/role"},{default:c(()=>t[6]||(t[6]=[f("div",{class:"pb-[8px]"},"角色助手",-1)])),_:1})])]),_:1},8,["modelValue","data","onAdd","onEdit","onDelete","onClear","onClickItem"])]),f("div",pt,[d(fe,{class:"h-full",content:e(y).getChatConfig.watermark,font:{color:e(oe)?"rgba(256,256,256,0.08)":"rgba(0,0,0,0.06)",fontSize:12}},{default:c(()=>[f("div",dt,[f("div",ut,[d(pe,{ref_key:"scrollbarRef",ref:q},{default:c(()=>[d(ce,null,{default:c(()=>[e(l).length?(p(),h("div",{key:0,ref_key:"innerRef",ref:H,class:"px-8"},[(p(!0),h(B,null,z(e(l),(o,k)=>(p(),h("div",{key:o.id+""+k,class:"mt-4 sm:pb-[20px]"},[o.type==1?(p(),L(_,{key:0,type:"right",avatar:e(w).userInfo.avatar,color:"white"},{actions:c(()=>[f("div",mt,[d(i,{link:"",type:"info",onClick:A=>e(te)(o.content)},{icon:c(()=>[d(T,{name:"el-icon-CopyDocument"})]),default:c(()=>[t[8]||(t[8]=qe(" 复制 "))]),_:2},1032,["onClick"])])]),default:c(()=>[d(u,{content:o.content,"files-plugin":o.files_plugin},null,8,["content","files-plugin"])]),_:2},1032,["avatar"])):U("",!0),o.type==2?(p(),L(_,{key:1,type:"left",avatar:e(y).getChatConfig.chat_logo,time:o.create_time,bg:"var(--el-bg-color-page)",modelName:o.model},{outer_actions:c(()=>[k===e(l).length-1&&!S.value?(p(),h("div",ft,[(p(!0),h(B,null,z(E.value.length?E.value:o.correlation,(A,$)=>(p(),h("div",{key:$,class:"inline-flex items-center rounded-[12px] bg-page cursor-pointer mt-[10px] hover:bg-primary-light-9",style:{padding:"8px 12px",width:"fit-content"},onClick:He(St=>I(A,"input"),["stop"])},[f("span",gt,K(A),1),d(T,{name:"el-icon-Right",color:"#999",size:"20"})],8,_t))),128))])):U("",!0)]),default:c(()=>[o.reasoning?(p(),L(le,{key:0,"model-value":"the-chat-msg-collapse",class:"mb-2 the-chat-msg-collapse"},{default:c(()=>[d(g,{title:"深度思考",name:"the-chat-msg-collapse"},{default:c(()=>[d(u,{content:o.reasoning,class:"text-tx-secondary px-3 border-l-[3px] border-br-light"},null,8,["content"])]),_:2},1024)]),_:2},1024)):U("",!0),(p(!0),h(B,null,z(o.content,(A,$)=>(p(),L(u,{key:$,content:A,type:"html",typing:o.typing,"line-numbers":!e(y).isMobile,"show-rewrite":k===e(l).length-1,"show-copy":"","show-voice":e(y).getIsVoiceOpen,class:We(["mb-[15px] last-of-type:mb-0",{"pt-[15px] border-t border-solid border-br-light":$>0}]),"show-poster":"","record-list":e(l),index:$,"record-id":o.id,onRewrite:e(ne)},null,8,["content","typing","line-numbers","show-rewrite","show-voice","class","record-list","index","record-id","onRewrite"]))),128))]),_:2},1032,["avatar","time","modelName"])):U("",!0)]))),128))],512)):(p(),h("div",ht,[d(Ze,{onClickItem:t[1]||(t[1]=o=>I(o,"sample"))})]))]),_:1})]),_:1},512)]),f("div",yt,[d(me,{ref_key:"chatActionRef",ref:N,loading:S.value,"file-plugin":e(m).data,"onUpdate:filePlugin":t[3]||(t[3]=o=>e(m).data=o),onEnter:I,onClear:se,onPause:t[4]||(t[4]=o=>{var k;return(k=e(s))==null?void 0:k.abort()}),onFocus:ie,"show-continue":e(l).length,"show-file-upload":e(m).show,onContinue:t[5]||(t[5]=o=>I("继续","btn"))},Pe({btn:c(()=>[f("div",vt,[d(de,{class:"min-w-[280px] select-class",sub_id:O.value,"onUpdate:sub_id":t[2]||(t[2]=o=>O.value=o),"onUpdate:modelConfig":ae},null,8,["sub_id"]),e(C).isSupportFile?(p(),L(Ge,{key:0,type:"file","is-parse-content":!0,onOnSuccess:e(C).addFile},null,8,["onOnSuccess"])):U("",!0)])]),_:2},[e(C).files.value.length?{name:"file-list",fn:c(()=>[(p(!0),h(B,null,z(e(C).files.value,o=>(p(),L(ue,{key:o.id,onClose:k=>e(C).removeFile(o)},{default:c(()=>[f("div",xt,K(o.name),1)]),_:2},1032,["onClose"]))),128))]),key:"0"}:void 0]),1032,["loading","file-plugin","show-continue","show-file-upload"])])])]),_:1},8,["content","font"])])])]),_:1})])}}}),Ro=at(wt,[["__scopeId","data-v-44a43b33"]]);export{Ro as default};
