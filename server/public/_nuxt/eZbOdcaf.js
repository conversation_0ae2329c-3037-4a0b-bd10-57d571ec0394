import{E as B,a as D}from"./DLovRgFC.js";import{j as E,cS as d,da as N,db as V,dd as G,e as M,o as U,E as W,p as h,de as j}from"./DjCZV6kq.js";import{_ as q}from"./D3UglwDI.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{u as A}from"./CcPlX2kz.js";import{l as z,j as O,r as X,M as f,N as Z,O as c,Z as a,a0 as t,u as o,a1 as _,a4 as w,a6 as v}from"./Dp9aCaJ6.js";const $={class:"flex-1 flex flex-col"},H={class:"flex flex-1 flex-col pt-[30px] px-[30px] min-h-0"},J={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},K={class:"flex justify-center flex-1"},me=z({__name:"forgot-pwd",setup(Q){const i=E(),{setLoginPopupType:g}=E(),u=O(),x=O(),b={mobile:[{required:!0,message:"请输入手机号码"}],email:[{required:!0,message:"请输入邮箱账号"},{type:"email",message:"请输入正确的邮箱账号"}],code:[{required:!0,message:"请输入验证码",trigger:["change","blur"]}],password:[{required:!0,message:"请输入6-20位数字+字母或符号组合",trigger:["change","blur"]},{min:6,max:20,message:"密码长度应为6-20",trigger:["change","blur"]}],password_confirm:[{validator(s,e,m){e===""?m(new Error("请再次输入密码")):e!==l.password?m(new Error("两次输入的密码不一致")):m()},trigger:["change","blur"]}]},l=X({email:"",mobile:"",password:"",code:"",password_confirm:""}),k=async()=>{var s;i.loginPopupType===d.FORGOT_PWD_MOBILE?await C():await F(),(s=x.value)==null||s.start()},C=async()=>{var s;await((s=u.value)==null?void 0:s.validateField(["mobile"])),await N({scene:V.FIND_PASSWORD,mobile:l.mobile})},F=async()=>{var s;await((s=u.value)==null?void 0:s.validateField(["email"])),await G({scene:V.FIND_PASSWORD,email:l.email})},P=async()=>{var s;await((s=u.value)==null?void 0:s.validate()),await j({...l,scene:i.loginPopupType===d.FORGOT_PWD_MOBILE?1:2}),i.logout(),g(d.LOGIN)},{lockFn:S,isLock:I}=A(P);return(s,e)=>{const m=B,L=D,p=M,n=U,R=q,y=W,T=h;return f(),Z("div",$,[c("div",H,[e[8]||(e[8]=c("span",{class:"text-2xl font-medium text-tx-primary"},"更换密码",-1)),a(T,{ref_key:"formRef",ref:u,class:"mt-[35px]",size:"large",model:o(l),rules:b},{default:t(()=>[o(i).loginPopupType===o(d).FORGOT_PWD_MOBILE?(f(),_(n,{key:0,prop:"mobile"},{default:t(()=>[a(p,{modelValue:o(l).mobile,"onUpdate:modelValue":e[0]||(e[0]=r=>o(l).mobile=r),placeholder:"请输入手机号"},{prepend:t(()=>[a(L,{"model-value":"+86",style:{width:"80px"}},{default:t(()=>[a(m,{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1})):w("",!0),o(i).loginPopupType===o(d).FORGOT_PWD_MAILBOX?(f(),_(n,{key:1,prop:"email"},{default:t(()=>[a(p,{modelValue:o(l).email,"onUpdate:modelValue":e[1]||(e[1]=r=>o(l).email=r),placeholder:"请输入邮箱账号"},null,8,["modelValue"])]),_:1})):w("",!0),a(n,{prop:"code"},{default:t(()=>[a(p,{modelValue:o(l).code,"onUpdate:modelValue":e[2]||(e[2]=r=>o(l).code=r),placeholder:"请输入验证码"},{suffix:t(()=>[c("div",J,[a(R,{ref_key:"verificationCodeRef",ref:x,onClickGet:k},null,512)])]),_:1},8,["modelValue"])]),_:1}),a(n,{prop:"password"},{default:t(()=>[a(p,{modelValue:o(l).password,"onUpdate:modelValue":e[3]||(e[3]=r=>o(l).password=r),placeholder:"请输入6-20位数字+字母或符号组合",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),a(n,{prop:"password_confirm"},{default:t(()=>[a(p,{modelValue:o(l).password_confirm,"onUpdate:modelValue":e[4]||(e[4]=r=>o(l).password_confirm=r),placeholder:"请再次输入密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),a(n,{class:"mt-[30px]"},{default:t(()=>[a(y,{class:"w-full",type:"primary",loading:o(I),onClick:o(S)},{default:t(()=>e[6]||(e[6]=[v(" 确认 ")])),_:1},8,["loading","onClick"])]),_:1}),a(n,{class:"mt-[20px]"},{default:t(()=>[c("div",K,[o(i).isLogin?w("",!0):(f(),_(y,{key:0,type:"primary",link:"",onClick:e[5]||(e[5]=r=>o(g)(o(d).LOGIN))},{default:t(()=>e[7]||(e[7]=[v(" 返回登录 ")])),_:1}))])]),_:1})]),_:1},8,["model"])])])}}});export{me as _};
