import{E as d}from"./CBkeCdOF.js";import{_ as x}from"./DmQlxOml.js";import{_ as v}from"./CQG-tpkc.js";import{E as h}from"./Br7V4jS9.js";/* empty css        */import{u as g,a as k}from"./CDG5bHsH.js";import{u as w}from"./Bb2-23m7.js";import{l as C,M as t,N as c,u as s,_ as E,Z as e,X as y,O as B,a0 as b}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./B4zJW-6l.js";import"./B9mz6c9C.js";import"./CCBJtVSv.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CiYvFM4x.js";import"./Dg3tPGYu.js";import"./mww26Q3C.js";const M={key:0,class:"px-[10px]"},N={class:"flex-1 min-w-0 mx-[5px]"},S=C({__name:"select-music",setup(T){const o=g(),{changeTabs:n}=k(),{play:i,audioPlaying:a,pause:m}=w(),r=()=>{o.music.url?a.value?m():i(o.music.url):n("music")},l=()=>{o.music={id:0,url:"",cover:"",name:""}};return(z,O)=>{const p=d,u=x,_=v,f=h;return t(),c("div",{class:"h-[40px] px-[10px] max-w-[220px] flex items-center shadow-[0_2px_6px_#ebefff] rounded-full bg-white cursor-pointer",onClick:r},[s(o).music.id?(t(),c(E,{key:1},[e(p,{class:y([{playing:s(a)},"w-[30px] h-[30px] rounded-full"]),src:s(o).music.cover},null,8,["class","src"]),B("div",N,[e(u,{content:s(o).music.name},null,8,["content"])]),e(f,{link:"",onClick:l},{default:b(()=>[e(_,{name:"el-icon-Close",size:"20"})]),_:1})],64)):(t(),c("div",M,"选择音乐"))])}}}),Y=I(S,[["__scopeId","data-v-38a0cc13"]]);export{Y as default};
