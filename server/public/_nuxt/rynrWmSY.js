import{E as j}from"./DQS3pGu2.js";import{_ as L}from"./C_7xENts.js";import{j as M,b as U,m as q,f as H,cX as J,E as S}from"./CzTOiozM.js";import{_ as O}from"./9wPy3dd6.js";import{E as T}from"./CLJ-nna1.js";/* empty css        *//* empty css        *//* empty css        */import{u as F}from"./7WLOh-Vd.js";import{u as P}from"./YCXpYPC4.js";import{l as X,b as Z,ak as G,M as i,N as _,Z as r,a0 as c,V as K,O as t,u as e,a7 as o,a6 as u,a1 as N,a4 as Q,_ as R,aq as W}from"./Dp9aCaJ6.js";import{_ as Y}from"./DlAUqK2U.js";import"./CaD5i18d.js";import"./DCTLXrZ8.js";import"./DflfJMcq.js";import"./CH0sQbfA.js";import"./9Bti1uB6.js";const tt={class:"user-info"},et={class:"p-[8px]"},st={class:"flex items-center"},ot={class:"flex-1 flex items-center"},nt={class:"ml-[10px]"},at={class:"text-lg line-clamp-1"},rt={class:"bg-[#eff2fe] mt-[20px] py-[20px] px-[5px] rounded-[10px] dark:bg-[#333]"},ct={class:"flex justify-between px-[20px]"},it={class:"text-xl font-medium text-tx-primary"},lt={class:"text-tx-secondary text-sm mt-[10px]"},dt={class:"flex items-center"},_t={key:0},pt={key:1},mt={class:"flex mt-[20px]"},ut={class:"flex-1 flex flex-col items-center px-[5px]"},ft={class:"text-md text-primary text-center font-bold"},xt={class:"text-xs mt-[5px]"},ht={class:"flex-1 flex flex-col items-center px-[5px]"},vt={class:"text-md text-primary text-center font-bold"},gt={class:"py-[20px] flex"},bt={class:"flex flex-col items-center w-full"},kt={class:"text-tx-regular"},yt={class:"mt-2"},wt={class:"border-t border-solid border-br-light pt-[20px]"},It={class:"flex justify-end"},Et=X({__name:"user-info",async setup(Ct){let l,f;const s=M(),{copy:B}=F(),x=U(),z=Z([{icon:"local-icon-user_works",name:"我的作品",path:"/user/works"},{icon:"local-icon-head_goumai",name:"购买记录",path:"/user/record"},{icon:"local-icon-head_shiyong",name:"余额明细",path:"/user/balance"}]),{data:d}=([l,f]=G(()=>P(()=>q({id:2}),{default(){return[]},transform(p){return JSON.parse(p.data)[1]},lazy:!0},"$EqHsJzg0HM")),l=await l,f(),l),A=async()=>{await H.confirm("确定退出登录吗？"),await J(),s.logout(),window.location.reload()};return(p,n)=>{const D=j,h=L,v=S,m=O,V=S,$=T;return i(),_("div",tt,[r($,{placement:"bottom",trigger:"hover",teleported:!1,"show-arrow":!1,transition:"custom-popover",width:390},{reference:c(()=>[K(p.$slots,"default",{},void 0,!0)]),default:c(()=>[t("div",et,[t("div",st,[t("div",ot,[r(D,{class:"flex-none",size:50,src:e(s).userInfo.avatar},null,8,["src"]),t("div",nt,[t("div",at,o(e(s).userInfo.nickname),1),t("div",{class:"text-xs text-tx-secondary mt-1 cursor-pointer flex items-center",onClick:n[0]||(n[0]=a=>e(B)(e(s).userInfo.sn))},[u(" ID："+o(e(s).userInfo.sn)+" ",1),r(h,{class:"ml-1",size:"12",name:"el-icon-CopyDocument"})])])]),t("div",null,[r(m,{to:"/user/center"},{default:c(()=>[r(v,{text:"",bg:"",round:""},{default:c(()=>n[1]||(n[1]=[u("个人中心")])),_:1})]),_:1})])]),t("div",rt,[e(x).getIsShowMember?(i(),N(m,{key:0,to:"/user/member"},{default:c(()=>{var a,g,b,k;return[t("div",ct,[t("div",null,[t("div",it,o(e(s).userInfo.package_name||((g=(a=e(d))==null?void 0:a.content)==null?void 0:g.title)),1),t("div",lt,o(e(s).userInfo.package_time?`有效期至：${e(s).userInfo.package_time}`:(k=(b=e(d))==null?void 0:b.content)==null?void 0:k.sub_title),1)]),t("div",dt,[r(v,{text:"",bg:"",round:"",class:"dark:!bg-[#1b1c1d] !bg-white"},{default:c(()=>{var y,w,I,E,C;return[(y=e(s).userInfo)!=null&&y.package_is_overdue?(i(),_("span",_t,o(e(s).userInfo.package_name?"立即开通":(I=(w=e(d))==null?void 0:w.content)==null?void 0:I.btn),1)):(i(),_("span",pt,o(e(s).userInfo.package_name?"立即续费":(C=(E=e(d))==null?void 0:E.content)==null?void 0:C.btn),1))]}),_:1})])])]}),_:1})):Q("",!0),t("div",mt,[t("div",ut,[t("span",ft,o(e(s).userInfo.balance),1),t("span",xt,o(e(x).getTokenUnit)+"数量",1)]),t("div",ht,[t("span",vt,o(e(s).userInfo.robot_num),1),n[2]||(n[2]=t("span",{class:"text-xs mt-[5px]"},"智能体",-1))])])]),t("div",gt,[(i(!0),_(R,null,W(e(z),a=>(i(),N(m,{class:"w-[33.3%] flex",key:a.path,to:a.path},{default:c(()=>[t("div",bt,[t("div",kt,[r(h,{name:a.icon,size:20},null,8,["name"])]),t("div",yt,o(a.name),1)])]),_:2},1032,["to"]))),128))]),t("div",wt,[t("div",It,[r(V,{link:"",onClick:A},{default:c(()=>n[3]||(n[3]=[u(" 退出登录 ")])),_:1})])])])]),_:3})])}}}),Ft=Y(Et,[["__scopeId","data-v-17233f26"]]);export{Ft as default};
