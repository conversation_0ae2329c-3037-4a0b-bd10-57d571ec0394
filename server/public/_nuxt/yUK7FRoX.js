import{_ as m}from"./uE1Ww79i.js";import{bA as p,E as u}from"./B1MekKW7.js";import{useSearch as f}from"./BRkGZCjn.js";import{l as _,M as d,N as x,Z as n,a0 as o,a6 as r,u as k}from"./Dp9aCaJ6.js";const y={class:"flex flex-wrap items-center"},v=_({__name:"action-btns",setup(C){const{launchSearch:c,result:i}=f(),l=()=>{const s=i.value.data.reduce((e,t)=>(["markdown","expand_query"].includes(t.type)&&(e+=t.content+`
`),e),"");p(s)};return(s,e)=>{const t=m,a=u;return d(),x("div",y,[n(a,{link:"",onClick:e[0]||(e[0]=B=>k(c)())},{icon:o(()=>[n(t,{name:"el-icon-RefreshLeft"})]),default:o(()=>[e[1]||(e[1]=r(" 重写 "))]),_:1}),n(a,{link:"",type:"primary",onClick:l},{icon:o(()=>[n(t,{name:"el-icon-DocumentCopy"})]),default:o(()=>[e[2]||(e[2]=r(" 复制 "))]),_:1})])}}});export{v as _};
