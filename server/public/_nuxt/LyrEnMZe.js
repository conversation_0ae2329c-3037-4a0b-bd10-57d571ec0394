import{E as j}from"./CDipRpC2.js";import{_ as $}from"./a_MrTnou.js";import{b as B,j as L,cg as N,J as q}from"./CylNgAGi.js";import{E as O}from"./dYUcznge.js";import{E as R}from"./DBoLoxaV.js";/* empty css        */import{W as A}from"./DxX9qVdQ.js";import{h as J,z as M}from"./B8XxWWwZ.js";import{I as U}from"./NobvHViT.js";import{_ as W}from"./D-qclbEV.js";import{D as F}from"./WKs0Or0Z.js";import{l as H,j as v,r as g,b as x,M as a,N as r,O as i,_ as k,aq as T,u as s,Z as l,a1 as X,a0 as w,aa as Z,a4 as b,X as G,a7 as K}from"./Dp9aCaJ6.js";import{_ as Q}from"./DlAUqK2U.js";import"./Cg11N2Sp.js";import"./BGZ_z1Ox.js";import"./qb8OCGLI.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";import"./fZOypmWP.js";import"./Za7Ic34B.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        *//* empty css        */import"./Bi0_2T0f.js";import"./iRXjSIie.js";const Y={class:"square h-full flex flex-col"},ee={class:"nav flex px-4 pt-4"},oe={class:"category-list flex-1"},te=["onClick"],se={class:"flex-1 min-w-0"},ae={key:0,class:"h-full flex flex-col justify-center"},ie={class:"main"},ne=["infinite-scroll-disabled"],re={class:"image-payload h-full w-full relative text-sm"},le={class:"image-bg"},ce=["onClick"],me={key:0,class:"flex justify-center items-center mt-[50px]"},de=H({__name:"draw",setup(pe){B(),L(),v();const d=v(null),c=g({status:3,page_no:0,page_size:20,model:"sd"}),e=g({more:!0,loading:!1,lists:[]}),C=x([{name:"SD绘画",type:"sd"},{name:"DALLE绘画",type:"dalle3"},{name:"MJ绘画",type:"mj"},{name:"豆包绘画",type:"doubao"}]);x([]);const m=g({show:!1,data:{}}),D=t=>{m.show=!0,m.data=t},E={4e3:{rowPerView:8},2e3:{rowPerView:6},1800:{rowPerView:6},1600:{rowPerView:5},1440:{rowPerView:5},1360:{rowPerView:5},1280:{rowPerView:4},1024:{rowPerView:4}},P=()=>{var t;(t=d==null?void 0:d.value)==null||t.renderer()},h=()=>{e.more=!0,c.page_no=0},V=async t=>{await J(t),h()},S=t=>{c.model!==t&&(c.model=t,h(),_())},_=async()=>{if(!e.loading){if(e.more)c.page_no+=1;else return;e.loading=!0;try{const t=await M(c),{lists:n,page_no:p,page_size:f,count:u}=t;p*f>u&&(e.more=!1),p==1?e.lists=n:e.lists=[...e.lists,...n],setTimeout(()=>e.loading=!1,500)}catch(t){e.loading=!1,console.log("获取绘画广场列表错误=>",t)}}};return _(),(t,n)=>{const p=j,f=$,u=q,z=O,I=R;return a(),r("div",Y,[i("div",ee,[i("div",oe,[(a(!0),r(k,null,T(s(C),o=>(a(),r("div",{key:o.type,class:G(["category-item",{"category-item--active":o.type===s(c).model}]),onClick:y=>S(o.type)},K(o.name),11,te))),128))])]),i("div",se,[!s(e).lists.length&&!s(e).loading?(a(),r("div",ae,[l(p,{"image-size":150,image:s(F),description:"暂时没有绘画哦，快去生成试试吧"},null,8,["image"])])):(a(),X(z,{key:1},{default:w(()=>[i("div",ie,[Z((a(),r("div",{"infinite-scroll-delay":200,"infinite-scroll-distance":400,"infinite-scroll-disabled":!s(e).more},[s(e).lists.length?(a(),r(k,{key:0},[l(A,{ref_key:"waterFull",ref:d,delay:100,list:s(e).lists,width:326,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:E},{item:w(({item:o})=>[i("div",re,[i("div",le,[l(U,{thumbnail:o.thumbnail,image:(o==null?void 0:o.image)||(o==null?void 0:o.image_url),onRefresh:P,onOnClick:y=>D(o)},null,8,["thumbnail","image","onOnClick"])]),i("div",{class:"image-del",onClick:y=>V(o.id)},[l(f,{class:"cursor-pointer rounded-md p-1 box-content",name:"el-icon-Delete",size:"18",color:"#fff"})],8,ce)])]),_:1},8,["list"]),s(e).loading?(a(),r("div",me,[l(u,{size:"25",class:"is-loading"},{default:w(()=>[l(s(N))]),_:1}),n[1]||(n[1]=i("span",{class:"mt-[4px] ml-[10px] text-tx-secondary"},"加载中...",-1))])):b("",!0)],64)):b("",!0)],8,ne)),[[I,_]])])]),_:1}))]),l(W,{show:s(m).show,"onUpdate:show":n[0]||(n[0]=o=>s(m).show=o),data:s(m).data},null,8,["show","data"])])}}}),Ae=Q(de,[["__scopeId","data-v-a48eb256"]]);export{Ae as default};
