import{b as y,l as v,a as b,_ as k}from"./CylNgAGi.js";import w from"./BQKFdVGK.js";import{l as g,m,b as C,c as u,M as s,N as n,Z as N,a0 as q,O as a,u as r,_ as B,aq as R,a1 as L,a3 as V,a4 as D,x as I,X as S,a7 as j}from"./Dp9aCaJ6.js";import{_ as z}from"./DlAUqK2U.js";import"./hiU_ALBd.js";import"./fZOypmWP.js";import"./BGZ_z1Ox.js";import"./qb8OCGLI.js";import"./Cg11N2Sp.js";import"./a_MrTnou.js";/* empty css        */import"./BE7GAo-z.js";import"./CcPlX2kz.js";import"./5SHXd8at.js";const A={class:"p-main"},E={key:0,class:"flex h-full flex-col pt-[10px] max-w-[1200px] mx-auto"},F={class:"tab-lists"},M={class:"flex mx-[-10px]"},O=["onClick"],T={class:"w-full"},X={class:"flex-1 min-h-0"},Z={key:1,class:"w-full h-full bg-white rounded-[12px] flex items-center justify-center"},$=g({__name:"index",setup(G){y();const _=v(),p=b(),c=m(()=>[{name:"充值中心",type:"recharge",show:!0,component:I(w)}].filter(t=>!!t.show)),d=p.query.type,o=C(d),f=e=>{o.value=e,_.replace({path:"",query:{type:e}})},i=m(()=>c.value.find(e=>e.type===o.value));return u(c,e=>{if(!i.value&&e.length){const[t]=e;o.value=t.type}},{immediate:!0}),u(()=>p.query.type,e=>{o.value=e}),(e,t)=>{const x=k;return s(),n("div",null,[N(x,{name:"default"},{default:q(()=>[a("div",A,[r(c).length?(s(),n("div",E,[a("div",F,[a("div",M,[(s(!0),n(B,null,R(r(c),(l,h)=>(s(),n("div",{class:S(["tab-item",{"is-active":r(o)==l.type}]),key:h,onClick:H=>f(l.type)},[a("span",T,j(l.name),1)],10,O))),128))])]),a("div",X,[r(i)?(s(),L(V(r(i).component),{key:0})):D("",!0)])])):(s(),n("div",Z,t[0]||(t[0]=[a("div",{class:"text-xl"},"功能未开启!",-1)])))])]),_:1})])}}}),ce=z($,[["__scopeId","data-v-db46a5c1"]]);export{ce as default};
