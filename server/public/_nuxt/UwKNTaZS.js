import{K as z,R as k,Z as v,al as _,X as b,M as C,J as P,O as B,$ as N,P as w}from"./CLVDtxqA.js";import{l as d,b as A,m as i,c as I,M as o,N as u,a2 as f,u as n,a1 as m,a0 as L,a3 as M,V as T,X as V,H as X}from"./Dp9aCaJ6.js";const $=z({size:{type:[Number,String],values:k,default:"",validator:a=>v(a)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:_},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:b(String),default:"cover"}}),j={error:a=>a instanceof Event},q=["src","alt","srcset"],D=d({name:"ElAvatar"}),F=d({...D,props:$,emits:j,setup(a,{emit:y}){const t=a,e=C("avatar"),c=A(!1),S=i(()=>{const{size:s,icon:l,shape:p}=t,r=[e.b()];return X(s)&&r.push(e.m(s)),l&&r.push(e.m("icon")),p&&r.push(e.m(p)),r}),h=i(()=>{const{size:s}=t;return v(s)?e.cssVarBlock({size:N(s)||""}):void 0}),E=i(()=>({objectFit:t.fit}));I(()=>t.src,()=>c.value=!1);function g(s){c.value=!0,y("error",s)}return(s,l)=>(o(),u("span",{class:V(n(S)),style:f(n(h))},[(s.src||s.srcSet)&&!c.value?(o(),u("img",{key:0,src:s.src,alt:s.alt,srcset:s.srcSet,style:f(n(E)),onError:g},null,44,q)):s.icon?(o(),m(n(P),{key:1},{default:L(()=>[(o(),m(M(s.icon)))]),_:1})):T(s.$slots,"default",{key:2})],6))}});var H=B(F,[["__file","avatar.vue"]]);const O=w(H);export{O as E};
