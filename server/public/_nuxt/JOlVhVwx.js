import{cn as i,e as m}from"./DAgm18qP.js";import{_ as c}from"./D_gfJTDn.js";import{b as d}from"./DLSFF3if.js";import{l as _,M as f,N as V,Z as t,O as g,u as a,y as v}from"./Dp9aCaJ6.js";const b={class:"bg-[var(--el-bg-color-page)] rounded-[12px]"},B=_({__name:"negative-prompt",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(s,{emit:n}){const l=n,r=s,{modelValue:e}=i(r,l);return(h,o)=>{const u=m;return f(),V("div",null,[t(c,{title:"反向词",tips:"输入你希望AI绘制的内容，例如：white hair,sit，这样AI尽可能避免绘制白色的毛发和坐着的姿势"}),g("div",b,[t(u,{modelValue:a(e),"onUpdate:modelValue":o[0]||(o[0]=p=>v(e)?e.value=p:null),rows:4,"input-style":{boxShadow:"unset",backgroundColor:"transparent"},resize:"none",type:"textarea",placeholder:"请输入反向提示词",onFocus:a(d)},null,8,["modelValue","onFocus"])])])}}});export{B as _};
