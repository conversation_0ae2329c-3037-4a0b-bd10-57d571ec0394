import{E as H}from"./CVeuodRR.js";import{E as O,v as W,f as Z}from"./DAgm18qP.js";import{E as G,a as J}from"./BKSqj-iK.js";import{_ as K}from"./DMHEbzLi.js";import{E as Q,a as X,b as Y}from"./DFChVafq.js";import{_ as ee}from"./Dbgaq24H.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import{u as te}from"./xYW1pseX.js";import{u as ae}from"./67xbGseh.js";import{_ as oe}from"./CDs6l-nv.js";import{_ as ne}from"./B2cILlfz.js";import se from"./DvjVWoYe.js";import{w as ie,y as le,A as re,z as pe,x as me}from"./Bo3PTL3c.js";import{l as de,j as C,r as h,C as ue,c as ce,M as $,N as fe,Z as a,O as u,a0 as o,a6 as l,aa as _e,u as r,a1 as ye,a7 as ke,y as we,W as ge,_ as ve}from"./Dp9aCaJ6.js";const be={class:"mt-4"},Ce={class:"mt-4"},he={class:"flex items-center"},$e={class:"flex justify-end mt-4"},We=de({__name:"web",props:{appId:{}},emits:["back"],setup(x,{emit:E}){const g=x,R=E,{copy:S}=te(),k=C(),f=C(),p=h({show:!1,url:"",apikey:"",shareId:""}),{appId:I}=ue(g),_=h({robot_id:I,type:1}),{pager:c,getLists:m}=ae({fetchFun:ie,params:_});m();const P=async t=>{await Z.confirm("确定删除？"),await me({id:t,type:_.type}),m()},B=(t,e)=>{switch(t){case"edit":b(e);break;case"delete":P(e.id);break;case"usage":U(e);break;case"preview":V(e);break}},v=t=>`${location.origin}/chat/${t.apikey} #${t.name} ${t.secret?`密码: ${t.secret}`:""}`,V=async t=>{const e=v(t);window.open(e,"_blank")},b=t=>{var s;let e=null;t&&(e={id:t.id,name:t.name,password:t.secret}),(s=k.value)==null||s.open(e)},D=async(t,e)=>{var s;await(e=="add"?le({...t,..._}):re({id:t.id,name:t.name,password:t.password})),(s=k.value)==null||s.close(),m()},L=async t=>{const e=v(t);await S(e)},U=t=>{var e,s;(e=f.value)==null||e.open(),(s=f.value)==null||s.setFormData(t)},N=async t=>{var e;await pe({...t,..._}),(e=f.value)==null||e.close(),m()},T=t=>{p.show=!0,p.url=t.share_bg,p.apikey=t.apikey,p.shareId=t.id};return ce(()=>g.appId,()=>{m()}),(t,e)=>{const s=H,i=O,d=G,F=K,y=Q,j=X,z=Y,A=J,M=ee,q=W;return $(),fe(ve,null,[a(s,{content:"发布网页/朋友圈海报",onBack:e[0]||(e[0]=n=>R("back"))}),u("div",be,[a(i,{class:"mb-4",type:"primary",onClick:e[1]||(e[1]=n=>b())},{default:o(()=>e[5]||(e[5]=[l(" 创建链接 ")])),_:1}),e[6]||(e[6]=u("div",{class:"text-xs text-tx-secondary"}," 可以直接分享该模型给其他用户去进行对话，对方无需登录即可直接进行对话。注意，这个功能会消耗你账号的问答条数。请保管好链接和密码。 ",-1))]),u("div",Ce,[_e(($(),ye(A,{data:r(c).lists,size:"large"},{default:o(()=>[a(d,{label:"apikey",prop:"apikey","min-width":"200"}),a(d,{label:"分享名称",prop:"name","min-width":"180","show-tooltip-when-overflow":""}),a(d,{label:"访问密码",prop:"secret","min-width":"120"}),a(d,{label:"对话模式","min-width":"120"},{default:o(({row:n})=>[u("div",null,ke(n.chat_type==1?"文本对话":"形象对话"),1)]),_:1}),a(d,{label:"最后使用时间",prop:"use_time","min-width":"180"}),a(d,{label:"操作",width:"220",fixed:"right"},{default:o(({row:n})=>[u("div",he,[a(i,{type:"primary",link:"",onClick:w=>T(n)},{default:o(()=>e[7]||(e[7]=[l(" 生成海报 ")])),_:2},1032,["onClick"]),a(i,{type:"primary",link:"",onClick:w=>L(n)},{default:o(()=>e[8]||(e[8]=[l(" 复制链接 ")])),_:2},1032,["onClick"]),a(z,{class:"ml-[10px]",onCommand:w=>B(w,n)},{dropdown:o(()=>[a(j,null,{default:o(()=>[a(y,{command:"edit"},{default:o(()=>[a(i,{type:"primary",link:""},{default:o(()=>e[10]||(e[10]=[l(" 编辑 ")])),_:1})]),_:1}),a(y,{command:"preview"},{default:o(()=>[a(i,{type:"primary",link:""},{default:o(()=>e[11]||(e[11]=[l(" 预览 ")])),_:1})]),_:1}),a(y,{command:"usage"},{default:o(()=>[a(i,{type:"primary",link:""},{default:o(()=>e[12]||(e[12]=[l(" 用量设置 ")])),_:1})]),_:1}),a(y,{command:"delete"},{default:o(()=>[a(i,{type:"danger",link:""},{default:o(()=>e[13]||(e[13]=[l(" 删除 ")])),_:1})]),_:1})]),_:1})]),default:o(()=>[a(i,{type:"primary",link:""},{default:o(()=>[e[9]||(e[9]=l(" 更多 ")),a(F,{name:"el-icon-ArrowDown"})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[q,r(c).loading]]),u("div",$e,[a(M,{modelValue:r(c),"onUpdate:modelValue":e[2]||(e[2]=n=>we(c)?c.value=n:null),onChange:r(m)},null,8,["modelValue","onChange"])])]),a(oe,{ref_key:"createShareRef",ref:k,isShowChatType:!0,onConfirm:D},null,512),a(ne,{ref_key:"usageSettingsRef",ref:f,onConfirm:N},null,512),a(se,ge(r(p),{show:r(p).show,"onUpdate:show":e[3]||(e[3]=n=>r(p).show=n),onUpdate:e[4]||(e[4]=n=>r(m)())}),null,16,["show"])],64)}}});export{We as _};
