import{_ as H}from"./C8ZjkaO0.js";import{l as b,b as i,j as v,m as $,c as z,n as E,w as I,M as h,N as w,O as u,V as R,u as a,a2 as d,Z as V,a4 as B,a9 as M}from"./Dp9aCaJ6.js";import{bx as N,cD as S,V as D}from"./B5S_Er7H.js";import{_ as X}from"./DlAUqK2U.js";const Y=b({__name:"index",props:{defaultHeight:{default:32},bg:{default:"white"},iconColor:{default:"inherit"},zIndex:{default:999}},setup(g,{expose:_}){const n=g,o=i(!1),s=i(!1),c=v(),l=i("auto"),f=v(),y=$(()=>{const e=o.value?l.value:n.defaultHeight;return{height:typeof e=="number"?`${e}px`:e,backgroundColor:n.bg}});z(o,async e=>{e?(await E(),l.value=r.value?r.value:"auto"):l.value=n.defaultHeight});const{height:r}=N(c),{x:p,y:m,height:k,width:x}=S(f);return I(()=>{r.value>n.defaultHeight?s.value=!0:s.value=!1}),D(window,"click",e=>{e.clientX>p.value&&e.clientX<p.value+x.value&&e.clientY>m.value&&e.clientY<m.value+k.value||(o.value=!1)},{capture:!0}),_({hidden(){o.value=!1},show(){o.value=!0}}),(e,t)=>{const C=H;return h(),w("div",{class:"dropdown-more",style:d({"--dropdown-more-default-height":`${e.defaultHeight}px`,"--dropdown-more-z-index":e.zIndex}),onClick:t[1]||(t[1]=M(()=>{},["stop"]))},[t[2]||(t[2]=u("div",{class:"dropdown-placeholder"},null,-1)),u("div",{ref_key:"contentRef",ref:f,class:"dropdown-content",style:d(a(y))},[u("div",{ref_key:"slotRef",ref:c,class:"dropdown-slot"},[R(e.$slots,"default",{},void 0,!0)],512),a(s)?(h(),w("div",{key:0,style:d({transform:`rotateZ(${a(o)?"180deg":"0"})`}),class:"dropdown-icon cursor-pointer",onClick:t[0]||(t[0]=Z=>o.value=!a(o))},[V(C,{color:e.iconColor,name:"el-icon-ArrowDown"},null,8,["color"])],4)):B("",!0)],4)],4)}}}),T=X(Y,[["__scopeId","data-v-a93115e1"]]);export{T as _};
