import{E as d}from"./CgJ0pGO6.js";import{_}from"./_WXat9Ag.js";import{b as f,c_ as h}from"./DAgm18qP.js";import{l as y,m as I,M as s,N as n,Z as i,a0 as r,O as S,a7 as k,u as t,a4 as N}from"./Dp9aCaJ6.js";import{_ as g}from"./DlAUqK2U.js";import"./ktB38vtC.js";import"./CfiDmG6E.js";import"./DCTLXrZ8.js";import"./D5aRiEq0.js";import"./Cnczlvk4.js";import"./DbtR4j96.js";const B=["src"],b=["src"],q=y({__name:"menu-item",props:{item:{},path:{},isShowIcon:{type:[Number,Boolean]},isActive:{type:<PERSON>olean}},setup(c){const p=c,{getImageUrl:a}=f(),m=I(()=>{const e=p.item.link.query;try{const o=JSON.parse(e);return h(o)}catch{return e}});return(e,o)=>{const u=d,l=_;return s(),n("div",null,[i(l,{to:`${e.path}${t(m)?`?${t(m)}`:""}`},{default:r(()=>[i(u,{index:e.path},{title:r(()=>[S("span",null,k(e.item.name),1)]),default:r(()=>[e.isActive&&e.item.selected&&e.isShowIcon?(s(),n("img",{key:0,class:"menu-item-icon",src:t(a)(e.item.selected)},null,8,B)):e.item.unselected&&e.isShowIcon?(s(),n("img",{key:1,class:"menu-item-icon",src:t(a)(e.item.unselected)},null,8,b)):N("",!0)]),_:1},8,["index"])]),_:1},8,["to"])])}}}),J=g(q,[["__scopeId","data-v-eaa55001"]]);export{J as default};
