import{K as g,al as $,cv as h,a5 as y,M as E,O as C,J as H,P}from"./CzTOiozM.js";import{E as N}from"./DdwyL6lX.js";import{l as f,$ as S,m as V,M as o,N as r,X as t,u as a,V as l,a4 as i,O as n,Z as B,a1 as u,a0 as D,a3 as w,a6 as m,a7 as p}from"./Dp9aCaJ6.js";const I=g({icon:{type:$,default:()=>h},title:String,content:{type:String,default:""}}),M={back:()=>!0},O=["aria-label"],T=f({name:"ElPageHeader"}),_=f({...T,props:I,emits:M,setup(J,{emit:v}){const c=S(),{t:d}=y(),s=E("page-header"),b=V(()=>[s.b(),{[s.m("has-breadcrumb")]:!!c.breadcrumb,[s.m("has-extra")]:!!c.extra,[s.is("contentful")]:!!c.default}]);function k(){v("back")}return(e,K)=>(o(),r("div",{class:t(a(b))},[e.$slots.breadcrumb?(o(),r("div",{key:0,class:t(a(s).e("breadcrumb"))},[l(e.$slots,"breadcrumb")],2)):i("v-if",!0),n("div",{class:t(a(s).e("header"))},[n("div",{class:t(a(s).e("left"))},[n("div",{class:t(a(s).e("back")),role:"button",tabindex:"0",onClick:k},[e.icon||e.$slots.icon?(o(),r("div",{key:0,"aria-label":e.title||a(d)("el.pageHeader.title"),class:t(a(s).e("icon"))},[l(e.$slots,"icon",{},()=>[e.icon?(o(),u(a(H),{key:0},{default:D(()=>[(o(),u(w(e.icon)))]),_:1})):i("v-if",!0)])],10,O)):i("v-if",!0),n("div",{class:t(a(s).e("title"))},[l(e.$slots,"title",{},()=>[m(p(e.title||a(d)("el.pageHeader.title")),1)])],2)],2),B(a(N),{direction:"vertical"}),n("div",{class:t(a(s).e("content"))},[l(e.$slots,"content",{},()=>[m(p(e.content),1)])],2)],2),e.$slots.extra?(o(),r("div",{key:0,class:t(a(s).e("extra"))},[l(e.$slots,"extra")],2)):i("v-if",!0)],2),e.$slots.default?(o(),r("div",{key:1,class:t(a(s).e("main"))},[l(e.$slots,"default")],2)):i("v-if",!0)],2))}});var z=C(_,[["__file","page-header.vue"]]);const j=P(z);export{j as E};
