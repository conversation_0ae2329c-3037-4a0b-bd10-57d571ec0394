import{e as V,o as q,E as C,p as D}from"./B5S_Er7H.js";import{E as L}from"./Cx3Arr0P.js";import{E as N}from"./Q8b-geZB.js";import{E as B}from"./DySxZBuW.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import{u as I}from"./CcPlX2kz.js";import{r as M}from"./C4bZp7qs.js";import{l as S,b as u,M as n,a1 as T,a0 as s,O as e,Z as o,u as t,a6 as U,N as d,a4 as _,_ as j,aq as O,a7 as p}from"./Dp9aCaJ6.js";import{_ as P}from"./DlAUqK2U.js";import"./CacOB3dg.js";import"./GM0TYtnE.js";import"./Tox-HrLw.js";const R=""+new URL("empty.C6MrDaor.png",import.meta.url).href,Z={class:"p-main"},z={class:"grid lg:grid-cols-2 gap-4 grid-cols-1 h-full"},A={class:"px-[10px] py-[5px] h-full lg:borderLeft"},G={key:0,class:"flex flex-col items-center justify-center h-full"},H={key:0},J={class:"text-sm text-tx-secondary mt-[5px] whitespace-pre-line"},K={class:"text-sm text-tx-secondary whitespace-pre-line"},Q=S({__name:"testData",props:{id:{type:Number,default:0},type:{type:String,default:""}},setup(f){const x=f,i=u([]),r=u({kb_id:x.id,question:""}),b=async()=>{i.value=await M({...r.value})},{lockFn:g,isLock:y}=I(b);return(W,a)=>{const h=V,c=q,v=C,k=D,E=L,w=N,m=B;return n(),T(m,null,{default:s(()=>[e("div",Z,[e("div",z,[e("div",null,[o(k,{"label-width":"90px"},{default:s(()=>[o(c,{label:"测试文本"},{default:s(()=>[o(h,{modelValue:t(r).question,"onUpdate:modelValue":a[0]||(a[0]=l=>t(r).question=l),type:"textarea",rows:"20"},null,8,["modelValue"])]),_:1}),o(c,null,{default:s(()=>[e("div",null,[o(v,{loading:t(y),disabled:t(r).question==""||t(r).kb_id=="",type:"primary",onClick:t(g)},{default:s(()=>a[1]||(a[1]=[U(" 测试 ")])),_:1},8,["loading","disabled","onClick"])])]),_:1})]),_:1})]),e("div",A,[t(i).length==0?(n(),d("div",G,[o(E,{src:t(R)},null,8,["src"]),a[2]||(a[2]=e("div",{class:"mt-[10px] text-[#5a646e]"}," 测试结果将在这里展示 ",-1))])):_("",!0),o(m,null,{default:s(()=>[t(i).length!=0?(n(),d("div",H,[(n(!0),d(j,null,O(t(i),(l,F)=>(n(),d("div",{key:F,class:"p-[10px] border border-solid border-br-light mb-[10px] rounded"},[o(w,{percentage:Math.abs(l.score/1)*100,color:"var(--el-text-color-disabled)"},{default:s(()=>[e("span",null,p(Math.abs(l.score).toFixed(5)),1)]),_:2},1032,["percentage"]),e("div",J,p(l.question),1),e("div",K,p(l.answer),1)]))),128))])):_("",!0)]),_:1})])])])]),_:1})}}}),mt=P(Q,[["__scopeId","data-v-efecb610"]]);export{mt as default};
