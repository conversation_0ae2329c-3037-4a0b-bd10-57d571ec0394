import{a as d}from"./Ber4dqCN.js";import{b as f,a as v}from"./DyU4wb-Q.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import k from"./BI8FMm2D.js";import{l as w,m as r,M as n,N as c,Z as g,a0 as M,_ as S,aq as x,u as a,a1 as B}from"./Dp9aCaJ6.js";import{_ as C}from"./DlAUqK2U.js";import"./Dt_kzei6.js";import"./DCa8BdSG.js";import"./DCTLXrZ8.js";import"./D-tOg06u.js";import"./BrOxmQbV.js";import"./DH3UTaL6.js";import"./CZg8VLWm.js";const H={class:"menu"},I=w({__name:"menu",props:{isHome:{type:Boolean}},setup(m){const u=m,s=f(),l=r(()=>{var e;return((e=s.getHeaderConfig.nav)==null?void 0:e.filter(p=>p.isShow))||[]}),h=r(()=>s.getHeaderConfig.isShowIcon&&!u.isHome),t=v(),i=r(()=>{const e=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.parentPath||t.meta.activePath||e});return(e,p)=>{const _=d;return n(),c("div",H,[g(_,{"default-active":a(i),mode:"horizontal"},{default:M(()=>[(n(!0),c(S,null,x(a(l),o=>(n(),B(k,{key:o.id,item:o,"is-show-icon":a(h),path:o.link.path,"is-active":a(i)===o.link.path},null,8,["item","is-show-icon","path","is-active"]))),128))]),_:1},8,["default-active"])])}}}),D=C(I,[["__scopeId","data-v-10dc3ac7"]]);export{D as default};
