import{E as A}from"./DTtBEUup.js";import{E as D}from"./CIrs9wkW.js";import{E as M}from"./IgeL0vc_.js";import{f as V,bF as W}from"./B1MekKW7.js";/* empty css        */import{l as Z,b as u,j as z,m as G,c as H,M as p,N as v,Z as h,as as J,aq as k,a0 as _,V as K,W as F,u as n,y as w,a1 as Q,O as f,_ as T,a7 as X,a4 as Y}from"./Dp9aCaJ6.js";import{_ as ee}from"./DlAUqK2U.js";const te={class:"upload"},le={class:"file-list p-4"},ae={class:"flex-1"},se=["src"],oe=Z({__name:"index",props:{files:{type:Array,default:()=>[]},type:{type:String,default:"image"},multiple:{type:Boolean,default:!0},limit:{type:Number,default:10},data:{type:Object,default:()=>({})},name:{type:String,default:"file"},header:{type:Object,default:()=>({})},showFileList:{type:Boolean,default:!1}},emits:["end","start","change","error","success","update:files"],setup(d,{emit:B}){const a=d,y=u(""),c=u(!1),C=e=>{y.value=e.url,c.value=!0},g=u(!1),r=B,b=z(),i=u(!1),l=u([]),L=()=>{i.value=!0},x=e=>{r("change",e),l.value.length==0&&(g.value=!1,r("end"))},S=(e,t)=>{r("success",e),r("update:files",[...a.files,{url:e.uri,name:e.name}]);const s=l.value.indexOf(t);!a.showFileList&&l.value.splice(s,1),x(t)},I=e=>{const t=l.value.indexOf(e),s=a.files;s.splice(t,1),r("update:files",[...s])},P=(e,t)=>{var s;V.msgError(`${t.name}文件上传失败`),(s=b.value)==null||s.abort(t),i.value=!1,r("error",t),x(t)},R=()=>{V.msgError(`超出上传上限${a.limit}，请重新上传`)},U=()=>{l.value=[],i.value=!1},j=G(()=>{switch(a.type){case"image":return".jpg,.png,.gif,.jpeg";case"video":return".wmv,.avi,.mpg,.mpeg,.3gp,.mov,.mp4,.flv,.rmvb,.mkv";case"audio":return;default:return"*"}}),N=e=>W(a.type,{file:e.file,name:a.name,header:a.header,data:a.data}),O=e=>{g.value=!0,r("start",e)};return H(()=>a.files,e=>{!l.value.length&&(e!=null&&e.length)&&(l.value=[...e])},{immediate:!0}),(e,t)=>{const s=A,$=D,E=M;return p(),v("div",te,[h(s,F({ref_key:"uploadRefs",ref:b,"file-list":n(l),"onUpdate:fileList":t[0]||(t[0]=o=>w(l)?l.value=o:null)},e.$attrs,{multiple:d.multiple,limit:d.limit,"show-file-list":d.showFileList,"on-progress":L,"on-success":S,"on-exceed":R,"on-error":P,accept:n(j),"on-remove":I,"http-request":N,"before-upload":O,"on-preview":C}),J({_:2},[k(e.$slots,(o,m)=>({name:m,fn:_(q=>[K(e.$slots,m,F(q,{loading:n(g)}),void 0,!0)])}))]),1040,["file-list","multiple","limit","show-file-list","accept"]),!d.showFileList&&n(l).length?(p(),Q(E,{key:0,modelValue:n(i),"onUpdate:modelValue":t[1]||(t[1]=o=>w(i)?i.value=o:null),title:"上传进度","close-on-click-modal":!1,width:"500px",modal:!1,onClose:U},{default:_(()=>[f("div",le,[(p(!0),v(T,null,k(n(l),(o,m)=>(p(),v("div",{key:m,class:"mb-5"},[f("div",null,X(o.name),1),f("div",ae,[h($,{percentage:parseInt(String(o.percentage))},null,8,["percentage"])])]))),128))])]),_:1},8,["modelValue"])):Y("",!0),h(E,{modelValue:n(c),"onUpdate:modelValue":t[2]||(t[2]=o=>w(c)?c.value=o:null),width:"50vw"},{default:_(()=>[f("img",{"w-full":"",src:n(y),alt:"Preview Image"},null,8,se)]),_:1},8,["modelValue"])])}}}),pe=ee(oe,[["__scopeId","data-v-1e31db6a"]]);export{pe as _};
