import{l as N,i as j,m as E,M as T,N as K,O as R,V as k,X as m,u as e,a7 as le,Z as M,a0 as w,a1 as U,a3 as se,a4 as _,a2 as G,b as D,c as Z,n as te,F as ne,a as re,$ as ie,q as de,aa as ue,ab as ce,W as fe,as as ve,ac as ye,at as pe}from"./Dp9aCaJ6.js";import{K as Y,al as me,a5 as ge,am as Ce,an as be,J as he,O as H,ao as we,X as J,ap as Q,aq as ke,a6 as De,ar as W,as as Ee,$ as Ie,at as Be,au as Te,av as X,W as Fe,aw as $e,M as Ae,ax as Se,ay as Pe,az as Le,P as Oe}from"./CzTOiozM.js";import{c as Re}from"./9Bti1uB6.js";import{i as Me}from"./DCTLXrZ8.js";const x=Symbol("dialogInjectionKey"),ee=Y({center:Boolean,alignCenter:Boolean,closeIcon:{type:me},draggable:Boolean,overflow:<PERSON>olean,fullscreen:Boolean,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),Ne={close:()=>!0},ze=["aria-level"],Ve=["aria-label"],qe=["id"],Ke=N({name:"ElDialogContent"}),Ue=N({...Ke,props:ee,emits:Ne,setup(o){const t=o,{t:u}=ge(),{Close:A}=we,{dialogRef:n,headerRef:c,bodyId:I,ns:s,style:g}=j(x),{focusTrapRef:r}=j(Ce),f=E(()=>[s.b(),s.is("fullscreen",t.fullscreen),s.is("draggable",t.draggable),s.is("align-center",t.alignCenter),{[s.m("center")]:t.center}]),v=Re(r,n),C=E(()=>t.draggable),d=E(()=>t.overflow);return be(n,c,C,d),(l,B)=>(T(),K("div",{ref:e(v),class:m(e(f)),style:G(e(g)),tabindex:"-1"},[R("header",{ref_key:"headerRef",ref:c,class:m([e(s).e("header"),{"show-close":l.showClose}])},[k(l.$slots,"header",{},()=>[R("span",{role:"heading","aria-level":l.ariaLevel,class:m(e(s).e("title"))},le(l.title),11,ze)]),l.showClose?(T(),K("button",{key:0,"aria-label":e(u)("el.dialog.close"),class:m(e(s).e("headerbtn")),type:"button",onClick:B[0]||(B[0]=S=>l.$emit("close"))},[M(e(he),{class:m(e(s).e("close"))},{default:w(()=>[(T(),U(se(l.closeIcon||e(A))))]),_:1},8,["class"])],10,Ve)):_("v-if",!0)],2),R("div",{id:e(I),class:m(e(s).e("body"))},[k(l.$slots,"default")],10,qe),l.$slots.footer?(T(),K("footer",{key:0,class:m(e(s).e("footer"))},[k(l.$slots,"footer")],2)):_("v-if",!0)],6))}});var _e=H(Ue,[["__file","dialog-content.vue"]]);const je=Y({...ee,appendToBody:Boolean,appendTo:{type:J(String),default:"body"},beforeClose:{type:J(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:{type:Boolean,default:!1},headerAriaLevel:{type:String,default:"2"}}),Ze={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[Q]:o=>ke(o),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},Je=(o,t)=>{var u;const n=re().emit,{nextZIndex:c}=De();let I="";const s=W(),g=W(),r=D(!1),f=D(!1),v=D(!1),C=D((u=o.zIndex)!=null?u:c());let d,l;const B=Ee("namespace",Te),S=E(()=>{const i={},h=`--${B.value}-dialog`;return o.fullscreen||(o.top&&(i[`${h}-margin-top`]=o.top),o.width&&(i[`${h}-width`]=Ie(o.width))),i}),z=E(()=>o.alignCenter?{display:"flex"}:{});function P(){n("opened")}function V(){n("closed"),n(Q,!1),o.destroyOnClose&&(v.value=!1)}function q(){n("close")}function L(){l==null||l(),d==null||d(),o.openDelay&&o.openDelay>0?{stop:d}=X(()=>O(),o.openDelay):O()}function F(){d==null||d(),l==null||l(),o.closeDelay&&o.closeDelay>0?{stop:l}=X(()=>a(),o.closeDelay):a()}function $(){function i(h){h||(f.value=!0,r.value=!1)}o.beforeClose?o.beforeClose(i):F()}function y(){o.closeOnClickModal&&$()}function O(){Fe&&(r.value=!0)}function a(){r.value=!1}function p(){n("openAutoFocus")}function b(){n("closeAutoFocus")}function oe(i){var h;((h=i.detail)==null?void 0:h.focusReason)==="pointer"&&i.preventDefault()}o.lockScroll&&Be(r);function ae(){o.closeOnPressEscape&&$()}return Z(()=>o.modelValue,i=>{i?(f.value=!1,L(),v.value=!0,C.value=Me(o.zIndex)?c():C.value++,te(()=>{n("open"),t.value&&(t.value.scrollTop=0)})):r.value&&F()}),Z(()=>o.fullscreen,i=>{t.value&&(i?(I=t.value.style.transform,t.value.style.transform=""):t.value.style.transform=I)}),ne(()=>{o.modelValue&&(r.value=!0,v.value=!0,L())}),{afterEnter:P,afterLeave:V,beforeLeave:q,handleClose:$,onModalClick:y,close:F,doClose:a,onOpenAutoFocus:p,onCloseAutoFocus:b,onCloseRequested:ae,onFocusoutPrevented:oe,titleId:s,bodyId:g,closed:f,style:S,overlayDialogStyle:z,rendered:v,visible:r,zIndex:C}},We=["aria-label","aria-labelledby","aria-describedby"],Xe=N({name:"ElDialog",inheritAttrs:!1}),Ge=N({...Xe,props:je,emits:Ze,setup(o,{expose:t}){const u=o,A=ie();$e({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},E(()=>!!A.title));const n=Ae("dialog"),c=D(),I=D(),s=D(),{visible:g,titleId:r,bodyId:f,style:v,overlayDialogStyle:C,rendered:d,zIndex:l,afterEnter:B,afterLeave:S,beforeLeave:z,handleClose:P,onModalClick:V,onOpenAutoFocus:q,onCloseAutoFocus:L,onCloseRequested:F,onFocusoutPrevented:$}=Je(u,c);de(x,{dialogRef:c,headerRef:I,bodyId:f,ns:n,rendered:d,style:v});const y=Le(V),O=E(()=>u.draggable&&!u.fullscreen);return t({visible:g,dialogContentRef:s}),(a,p)=>(T(),U(pe,{to:a.appendTo,disabled:a.appendTo!=="body"?!1:!a.appendToBody},[M(ye,{name:"dialog-fade",onAfterEnter:e(B),onAfterLeave:e(S),onBeforeLeave:e(z),persisted:""},{default:w(()=>[ue(M(e(Se),{"custom-mask-event":"",mask:a.modal,"overlay-class":a.modalClass,"z-index":e(l)},{default:w(()=>[R("div",{role:"dialog","aria-modal":"true","aria-label":a.title||void 0,"aria-labelledby":a.title?void 0:e(r),"aria-describedby":e(f),class:m(`${e(n).namespace.value}-overlay-dialog`),style:G(e(C)),onClick:p[0]||(p[0]=(...b)=>e(y).onClick&&e(y).onClick(...b)),onMousedown:p[1]||(p[1]=(...b)=>e(y).onMousedown&&e(y).onMousedown(...b)),onMouseup:p[2]||(p[2]=(...b)=>e(y).onMouseup&&e(y).onMouseup(...b))},[M(e(Pe),{loop:"",trapped:e(g),"focus-start-el":"container",onFocusAfterTrapped:e(q),onFocusAfterReleased:e(L),onFocusoutPrevented:e($),onReleaseRequested:e(F)},{default:w(()=>[e(d)?(T(),U(_e,fe({key:0,ref_key:"dialogContentRef",ref:s},a.$attrs,{center:a.center,"align-center":a.alignCenter,"close-icon":a.closeIcon,draggable:e(O),overflow:a.overflow,fullscreen:a.fullscreen,"show-close":a.showClose,title:a.title,"aria-level":a.headerAriaLevel,onClose:e(P)}),ve({header:w(()=>[a.$slots.title?k(a.$slots,"title",{key:1}):k(a.$slots,"header",{key:0,close:e(P),titleId:e(r),titleClass:e(n).e("title")})]),default:w(()=>[k(a.$slots,"default")]),_:2},[a.$slots.footer?{name:"footer",fn:w(()=>[k(a.$slots,"footer")])}:void 0]),1040,["center","align-center","close-icon","draggable","overflow","fullscreen","show-close","title","aria-level","onClose"])):_("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,We)]),_:3},8,["mask","overlay-class","z-index"]),[[ce,e(g)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["to","disabled"]))}});var Ye=H(Ge,[["__file","dialog.vue"]]);const oo=Oe(Ye);export{oo as E,Ze as a,je as d,Je as u};
