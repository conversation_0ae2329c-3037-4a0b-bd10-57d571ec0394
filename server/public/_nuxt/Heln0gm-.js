import{bx as h,by as n,v as m}from"./Br7V4jS9.js";import{l as _,j as w,b as d,F as g,n as S,k as y,aa as f,u as c,M as z,N as k,O as u,ab as x,a2 as b}from"./Dp9aCaJ6.js";import{u as C}from"./CDG5bHsH.js";import{_ as B}from"./DlAUqK2U.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./Dg3tPGYu.js";import"./mww26Q3C.js";const R={class:"canvas-bg"},v="design-canvas",T=_({__name:"canvas-display",setup(D){const a=C(),t=w(null),{width:o}=h(t),s=d(a.defaultSize.height),i=d(!1);n(()=>a.defaultSize,()=>{l()});const l=()=>{o.value>=a.defaultSize.width?s.value=a.defaultSize.height:s.value=o.value*a.defaultSize.height/a.defaultSize.width};return n(o,e=>{l()}),n(s,e=>{if(e){const r=e/a.defaultSize.height;a.setZoom(r)}}),g(async()=>{i.value=!0,await S();try{await a.initCanvas(v,t.value)}finally{i.value=!1}}),y(()=>{var e;(e=a.canvas)==null||e.dispose(),a.$dispose()}),(e,r)=>{const p=m;return f((z(),k("div",{ref_key:"workspaceRef",ref:t,class:"canvas-display overflow-hidden",style:b({height:`${c(s)}px`})},[f(u("div",R,[u("canvas",{id:v})],512),[[x,c(a).canvas]])],4)),[[p,c(i)]])}}}),L=B(T,[["__scopeId","data-v-089e911c"]]);export{L as default};
