import{_ as w}from"./C7oRqtae.js";import{_ as x}from"./Bj9H9xtu.js";import{cn as b}from"./BBthjZaB.js";import{_ as C}from"./CiUA4g-j.js";import{a as _}from"./COx8WVYb.js";import{l as k,b as V,c as $,M as l,N as c,Z as i,O as n,_ as B,aq as M,u,X as N,a7 as j}from"./Dp9aCaJ6.js";const q={class:"grid grid-cols-2 gap-4"},z=["onClick"],F={class:"relative rounded-[12px] overflow-hidden"},I={class:"text-hidden-2 text-center"},A=k({__name:"doubao-model",props:{modelValue:{default:""},draw_type:{default:""}},emits:["update:modelValue"],setup(m,{emit:f}){const v=f,t=m,{modelValue:r}=b(t,v),d=V("general"),g=(e,o)=>{r.value=e[t.draw_type],d.value=o};return $(()=>t.draw_type,()=>{var e,o,s;r.value=(s=(o=(e=_.value)==null?void 0:e.engine)==null?void 0:o[d.value])==null?void 0:s[t.draw_type]},{immediate:!1}),(e,o)=>{var p;const s=w,h=x;return l(),c("div",null,[i(C,{title:"模型选择",required:"",tips:"指定midjourney的渲染模型"}),n("div",q,[(l(!0),c(B,null,M(((p=u(_))==null?void 0:p.engine)||[],(a,y)=>(l(),c("div",{key:a.icon,class:"flex flex-col gap-2",onClick:D=>g(a,y)},[n("div",F,[i(s,{class:"rounded-[12px] overflow-hidden bg-[var(--el-bg-color-page)]",src:a.icon,fit:"cover",ratio:[144,100]},null,8,["src"]),n("div",{class:N(["absolute top-0 left-0 bg-[rgba(0,0,0,0.4)] w-full h-full flex justify-center items-center transition-opacity opacity-0",{"opacity-100":a[e.draw_type]===u(r)}])},[i(h,{name:"el-icon-CircleCheckFilled",size:20,color:"#fff"})],2)]),n("div",I,j(a.title),1)],8,z))),128))])])}}});export{A as _};
