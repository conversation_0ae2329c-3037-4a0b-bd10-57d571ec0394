import{i as a,j as i,k as o,f as r,l}from"./BQ-RMI0l.js";import{e as u,f as c,h as g,i as h}from"./R2n930gq.js";import{i as p}from"./B7SD2TCw.js";import{b as f,r as m}from"./Dp9aCaJ6.js";const w=a({id:"aiPPT",state(){return{config:{status:0,price:0,isVipFree:!0},options:{type:1,prompt:"",cover_id:"",title:"",catalogs:[]},isGenning:!1,isGenningOutline:!1,showTemplate:!1,showOutline:!1,outlineLists:[]}},actions:{async getPPTConfig(){this.config=await u()},async genPPT(s){const e=i();if(!e.isLogin)return e.toggleShowLogin();let t={...this.options};if(o(s)?t=s:p(s)&&(t.prompt=s),!t.prompt)return r.msgError("请输入标题");t.type===1?await this.genPPTSubmit(t):t.type===2?this.showTemplate=!0:(this.showOutline=!0,this.outlineLists=[],this.genOutline(t.prompt))},async genPPTSubmit(s){if(this.isGenning)return;this.isGenning=!0;const e=l(),t=i();try{await c(s),await e.push({path:"/ai_ppt/history"}),t.getUser(),this.options.catalogs=[],this.options.cover_id="",this.options.title="",this.options.prompt=""}catch(n){console.error(n)}finally{this.isGenning=!1}},async genOutline(s=""){if(this.isGenningOutline)return;this.isGenningOutline=!0;const e=m({prompt:s||this.options.prompt,title:"",catalogs:[],status:0});this.outlineLists.push(e);try{const t=await g({prompt:e.prompt});e.status=1,e.title=t.title,e.catalogs=t.catalogs}catch{e.status=2}finally{this.isGenningOutline=!1}}}}),G=()=>{const s=f([]);return{searchEx:s,getSearchEx:async()=>{s.value=await h()}}};export{w as useAiPPTStore,G as useSearchEx};
