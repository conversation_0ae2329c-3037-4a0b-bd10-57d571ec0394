import{a as N,E as I}from"./Db_grgJe.js";import{o as M,e as O,E as S,p as L}from"./CylNgAGi.js";import{_ as $}from"./a_MrTnou.js";import{_ as j}from"./BnqojDrv.js";import{_ as K}from"./97HcMQu2.js";import{E as T}from"./dYUcznge.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{u as Z}from"./Du5U7mLs.js";import{u as A}from"./BNnETjxs.js";import{P as G}from"./D-bImviZ.js";import{l as H,j as c,b as v,c as J,M as u,a1 as k,a0 as a,Z as l,u as n,N as Q,aq as W,_ as X,O as r,y as Y,a6 as h}from"./Dp9aCaJ6.js";const ee={class:"flex-1"},le={class:"flex-1"},oe={class:"flex-1"},te={class:"max-w-[600px]"},we=H({__name:"correct-popup",emits:["confirm"],setup(se,{expose:x,emit:b}){const y=b,d=c(),m=c(),p=v(""),o=v({name:"",kb_id:"",ask:"",reply:"",images:[],video:[],files:[]});J(p,t=>{o.value.video=[{url:t,name:""}]});const w={kb_id:[{required:!0,message:"选择知识库"}],ask:[{required:!0,message:"请输入问题"}],reply:[{required:!0,message:"请输入答案"}]},V=t=>{var e;o.value={...o.value,ask:t.ask,reply:t.reply,name:t.name},(e=m.value)==null||e.open()},g=()=>{var t;(t=m.value)==null||t.close()},E=async()=>{var t;await((t=d.value)==null?void 0:t.validate()),y("confirm",o.value)},{optionsData:U}=Z({knowledge:{api:A,params:{page_type:0},transformData(t){return t.lists||[]}}});return x({open:V,close:g}),(t,e)=>{const R=I,B=N,i=M,f=O,D=$,_=j,P=K,q=S,z=L,C=T;return u(),k(G,{ref_key:"popupRef",ref:m,center:"",title:"修正问答",async:"",width:"900px",onConfirm:E},{default:a(()=>[l(C,null,{default:a(()=>[l(z,{ref_key:"formRef",ref:d,model:n(o),rules:w,"label-width":"120px"},{default:a(()=>[l(i,{label:"选择知识库",prop:"kb_id"},{default:a(()=>[l(B,{modelValue:n(o).kb_id,"onUpdate:modelValue":e[0]||(e[0]=s=>n(o).kb_id=s),class:"w-[240px]"},{default:a(()=>[(u(!0),Q(X,null,W(n(U).knowledge,(s,F)=>(u(),k(R,{key:F,label:`${s.name}`,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"提问问题",prop:"ask"},{default:a(()=>[l(f,{modelValue:n(o).ask,"onUpdate:modelValue":e[1]||(e[1]=s=>n(o).ask=s),placeholder:"请输入问题",type:"textarea",resize:"none",rows:4,maxlength:"600","show-word-limit":"",clearable:""},null,8,["modelValue"])]),_:1}),l(i,{label:"问题答案",prop:"reply"},{default:a(()=>[l(f,{modelValue:n(o).reply,"onUpdate:modelValue":e[2]||(e[2]=s=>n(o).reply=s),placeholder:"请输入答案",type:"textarea",resize:"none",rows:15,clearable:""},null,8,["modelValue"])]),_:1}),l(i,{label:"上传图片"},{default:a(()=>[r("div",ee,[r("div",null,[l(_,{files:n(o).images,"onUpdate:files":e[3]||(e[3]=s=>n(o).images=s),type:"image","list-type":"picture-card",limit:9,multiple:"","show-file-list":""},{default:a(()=>[l(D,{name:"el-icon-Plus",size:20})]),_:1},8,["files"])]),e[6]||(e[6]=r("div",{class:"form-tips"},"最多支持上传 9 张图",-1))])]),_:1}),l(i,{label:"上传视频"},{default:a(()=>[r("div",le,[r("div",null,[l(P,{modelValue:n(p),"onUpdate:modelValue":e[4]||(e[4]=s=>Y(p)?p.value=s:null),size:"80px"},null,8,["modelValue"])]),e[7]||(e[7]=r("div",{class:"form-tips"},"格式为MP4，大小不能超过20M",-1))])]),_:1}),l(i,{label:"上传附件"},{default:a(()=>[r("div",oe,[r("div",te,[l(_,{files:n(o).files,"onUpdate:files":e[5]||(e[5]=s=>n(o).files=s),type:"file","show-file-list":""},{tip:a(()=>e[9]||(e[9]=[r("div",{class:"el-upload__tip"}," 支持上传PDF、docx、excel、等文件格式 ",-1)])),default:a(()=>[l(q,null,{default:a(()=>e[8]||(e[8]=[h("上传附件")])),_:1})]),_:1},8,["files"])])])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},512)}}});export{we as _};
