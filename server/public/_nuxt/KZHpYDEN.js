import{E as h}from"./CtBRWm-a.js";import{_ as y}from"./DVYAUH4R.js";import{b as f,cX as k}from"./Br7V4jS9.js";import{l as N,m as b,M as t,N as o,Z as p,a0 as m,a7 as w,a4 as r,u as s}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./C3sn0ick.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./LFZ9apcG.js";import"./rgjptJRl.js";import"./V6VzAR3J.js";const S={key:0,class:"mb-[6px]"},g=["src"],B=["src"],q={key:0,class:"text-sm"},v=N({__name:"menu-item",props:{item:{},path:{},showName:{type:Boolean},isShowIcon:{type:[Number,Boolean]},isActive:{type:Boolean}},setup(u){const l=u,{getImageUrl:a}=f(),i=b(()=>{const e=l.item.link.query;try{const n=JSON.parse(e);return k(n)}catch{return e}});return(e,n)=>{const d=h,_=y;return t(),o("div",null,[p(_,{to:`${e.path}${s(i)?`?${s(i)}`:""}`},{default:m(()=>[p(d,{index:e.path},{title:m(()=>{var c;return[(c=e.item)!=null&&c.showName||e.showName?(t(),o("span",q,w(e.item.name),1)):r("",!0)]}),default:m(()=>[e.isShowIcon?(t(),o("span",S,[e.isActive&&e.item.selected?(t(),o("img",{key:0,class:"menu-item-icon",src:s(a)(e.item.selected)},null,8,g)):e.item.unselected?(t(),o("img",{key:1,class:"menu-item-icon",src:s(a)(e.item.unselected)},null,8,B)):r("",!0)])):r("",!0)]),_:1},8,["index"])]),_:1},8,["to"])])}}}),T=I(v,[["__scopeId","data-v-236bb885"]]);export{T as default};
