import{_ as x}from"./DBRSLWAE.js";import{E as h,a as y}from"./DwdGJTJg.js";import"./BsMqt_su.js";import{u as $,a as w}from"./BW58jJKx.js";import{l as T,m as k,b as B,c as D,M as r,N as p,O as m,Z as l,a0 as c,_ as M,aq as S,u as e,a4 as E,a1 as f,a7 as N,aa as z,a3 as L,ab as V}from"./Dp9aCaJ6.js";import{_ as j}from"./DoJLXnlN.js";import{_ as A}from"./BZennykg.js";import{_ as I}from"./CV93ngzB.js";import O from"./Yui5n8B9.js";import P from"./B-YWUiWY.js";import{_ as q}from"./CcPSaOiP.js";import F from"./BSn0a35C.js";import{_ as R}from"./DZHirkPG.js";import{_ as Z}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./Ck3hJ2J3.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./DiWHXvJr.js";import"./Ov5eMejF.js";import"./DlvGt6PY.js";import"./Bhzc5F_3.js";import"./DugIyZO8.js";import"./D05sCSvx.js";import"./D714yjg9.js";import"./Cpwj6wt2.js";/* empty css        *//* empty css        */import"./CFea67Vu.js";import"./B7E4a2p6.js";import"./9uRGTfH7.js";import"./CRvGIoCT.js";/* empty css        */import"./CDtlekFs.js";/* empty css        */import"./lCnkreI3.js";import"./Bz4jch3v.js";import"./D1YDx4Yr.js";import"./DCTLXrZ8.js";import"./Dbma4Whm.js";import"./BATbmIFb.js";import"./BcmWjB0N.js";import"./BPgEaOor.js";import"./C5OYjNdj.js";import"./DlKZEFPo.js";import"./BnJ9chus.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./NHaiYAPx.js";import"./DOF_zc-p.js";import"./Boo8ogMh.js";import"./C9-ctX7D.js";const G={class:"h-full relative z-20 bg-white"},H={class:"flex flex-col items-center justify-center"},J={class:"text-lg mt-[6px]"},K={class:"w-[360px] h-full"},Q=T({__name:"index",setup(U){const u={Avatar:j,Dub:A,Music:I,Background:O,Text:P,Captions:q,Maps:F,Prospect:R},d=$(),{tabsState:a,initTabs:v,changeTabs:b}=w(),s=k({get(){return a.value.isCollapsed},set(i){a.value.isCollapsed=i}}),n=B(!1);return v(),D(()=>a.value.current,async i=>{d.setActiveObjectByType(i)}),(i,o)=>{const _=x,C=y,g=h;return r(),p("div",{class:"control-panel h-full",onMouseenter:o[2]||(o[2]=t=>n.value=!0),onMouseleave:o[3]||(o[3]=t=>n.value=!1)},[m("div",G,[l(g,{"tab-position":"left",class:"h-full",type:"card","model-value":e(a).current,onTabChange:o[0]||(o[0]=t=>e(b)(t))},{default:c(()=>[(r(!0),p(M,null,S(e(a).tabs,t=>(r(),f(C,{key:t.id,name:t.id,lazy:""},{label:c(()=>[m("div",H,[l(_,{name:t.icon,size:22},null,8,["name"]),m("span",J,N(t.label),1)])]),default:c(()=>[z(m("div",K,[(r(),f(L(u[t.component])))],512),[[V,!e(s)]])]),_:2},1032,["name"]))),128))]),_:1},8,["model-value"])]),e(s)||e(n)?(r(),p("div",{key:0,class:"panel-left-arrow",onClick:o[1]||(o[1]=t=>s.value=!e(s))},[l(_,{class:"mr-1",name:`el-icon-${e(s)?"CaretRight":"CaretLeft"}`},null,8,["name"])])):E("",!0)],32)}}}),Wt=Z(Q,[["__scopeId","data-v-8d50fa1f"]]);export{Wt as default};
