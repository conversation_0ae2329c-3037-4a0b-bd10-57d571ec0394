import{_}from"./CQG-tpkc.js";import{cv as D,dr as y,ds as g,v as I}from"./Br7V4jS9.js";import{u as R}from"./BE7GAo-z.js";import{E as O}from"./CBkeCdOF.js";import{l as b,C as A,F as x,k as L,M as o,N as c,O as r,aa as w,u as e,a1 as V,a4 as u,Z as F,_ as v}from"./Dp9aCaJ6.js";const M=()=>{const t=D(()=>({pending:!0,data:{}}),"$SqqwjC5BAv");return{state:t,getData:async()=>{try{t.value.pending=!0;const i=await y({channel:"bind"});t.value.data=i,t.value.pending=!1}catch(i){return t.value.pending=!1,Promise.reject(i)}}}};var l=(t=>(t[t.CODE_ERROR=-1]="CODE_ERROR",t[t.INVALID=0]="INVALID",t[t.NORMAL=1]="NORMAL",t[t.SCANNED_CODE=2]="SCANNED_CODE",t[t.BIND_FAIL=3]="BIND_FAIL",t[t.BIND_SUCCESS=4]="BIND_SUCCESS",t))(l||{});const S=t=>{const n=D(()=>1,"$5Bc8VhT1Td"),i=async()=>{try{const a=await g({key:t.value.key,channel:"bind"});return n.value=a.status,n.value==4&&(h(a.user),m()),a}catch{n.value=3,m()}},f=()=>{n.value=0},{start:d,end:m,result:p}=R(i,{key:"bind_wx",totalTime:120*1e3,callback:f});return{status:n,start:d,end:m,result:p}},h=async t=>{location.reload()},T={class:"pb-[10px]"},j={class:"flex flex-col items-center mt-[20px] pt-3 pb-[30px]"},U={class:"relative w-[180px] h-[180px]"},$={key:1,class:"absolute left-0 top-0 w-full h-full bg-mask",style:{background:"rgba(0, 0, 0, 0.5)"}},q={class:"h-full text-primary flex justify-center items-center"},H=b({__name:"bind-weixin",setup(t){const{state:n,getData:i}=M(),{pending:f,data:d}=A(n.value),{start:m,end:p,status:a}=S(d),C=async()=>{a.value=l.NORMAL;try{await i(),m()}catch{a.value=l.CODE_ERROR}};return x(()=>{C()}),L(()=>{p()}),(N,s)=>{const k=_,E=I;return o(),c("div",T,[s[9]||(s[9]=r("div",{class:"text-xl font-medium pt-4 pl-4"},"绑定微信",-1)),r("div",j,[w((o(),c("div",U,[e(d).url?(o(),V(e(O),{key:0,src:e(d).url,class:"w-full h-full"},null,8,["src"])):u("",!0),e(a)==e(l).SCANNED_CODE?(o(),c("div",$,[r("div",q,[F(k,{name:"el-icon-SuccessFilled",size:30})])])):u("",!0),e(a)==e(l).INVALID||e(a)==e(l).BIND_FAIL||e(a)==e(l).CODE_ERROR?(o(),c("div",{key:2,class:"absolute left-0 top-0 w-full h-full bg-overlay cursor-pointer",style:{background:"rgba(0, 0, 0, 0.5)"},onClick:C},s[0]||(s[0]=[r("div",{class:"h-full flex flex-col justify-center items-center text-white"},[r("div",null,"点击刷新")],-1)]))):u("",!0)])),[[E,e(f)]]),e(a)==e(l).SCANNED_CODE?(o(),c(v,{key:0},[s[1]||(s[1]=r("div",{class:"mt-3"},"扫码成功",-1)),s[2]||(s[2]=r("div",{class:"mt-5 text-error text-sm"}," 请在微信公众号中确认绑定 ",-1))],64)):u("",!0),e(a)==e(l).INVALID?(o(),c(v,{key:1},[s[3]||(s[3]=r("div",{class:"mt-3 text-error"},"二维码失效",-1)),s[4]||(s[4]=r("div",{class:"mt-5 text-sm"},"请在点击二维码刷新",-1))],64)):u("",!0),e(a)==e(l).BIND_FAIL?(o(),c(v,{key:2},[s[5]||(s[5]=r("div",{class:"mt-3 text-error"},"绑定失败，请重新绑定",-1)),s[6]||(s[6]=r("div",{class:"mt-5 text-sm"},"请在点击二维码刷新",-1))],64)):u("",!0),e(a)==e(l).NORMAL||e(a)==e(l).BIND_SUCCESS||e(a)==e(l).CODE_ERROR?(o(),c(v,{key:3},[s[7]||(s[7]=r("div",{class:"mt-3"},"使用手机微信扫码绑定",-1)),s[8]||(s[8]=r("div",{class:"mt-2 text-tx-placeholder"},"绑定后可使用微信扫码登录",-1))],64)):u("",!0)])])}}});export{H as _};
