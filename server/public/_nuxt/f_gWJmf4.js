import{E as d,a as h,b as g,c as w}from"./lLcPORTI.js";import{j as v,cw as C,a as b,V as y,cQ as D,f as E}from"./BBthjZaB.js";import{u as x,a as S}from"./D4MHNy-o.js";import{_ as L}from"./DrIsSpk2.js";import k from"./DYJcLJ-g.js";import{_ as B}from"./Co0XKKlW.js";import R from"./CrI-o4Or.js";import{l as V,c as r,M as $,N as z,Z as t,a0 as i,a2 as H,u as M}from"./Dp9aCaJ6.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./BuBpo4Nc.js";import"./DMyJLFuc.js";import"./Bj9H9xtu.js";import"./DlAUqK2U.js";import"./HjBqiulG.js";import"./CRHv1E5O.js";import"./JOeTFF2G.js";import"./DBBnJi1E.js";import"./DiPtOrlW.js";import"./B4Y9LdPA.js";import"./C-hk8zs7.js";import"./ND-j-PcX.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        *//* empty css        *//* empty css        */import"./CcPlX2kz.js";import"./Dmdd9l2n.js";import"./Cv6HhfEG.js";import"./BZZdiSEe.js";import"./DmOnwihU.js";import"./CEYTy2ef.js";import"./Bwa6YuoW.js";/* empty css        */import"./BzHpYkC6.js";import"./DXfM9A1O.js";import"./DQciAm-M.js";import"./DSvHt5sr.js";import"./Cz8pxCeT.js";/* empty css        */import"./TKbFOiod.js";import"./ufQA1gs1.js";import"./n0lswI5r.js";import"./DPRLemnx.js";/* empty css        */import"./CwFz8ID8.js";import"./DJi2sD98.js";import"./vwcCUF2-.js";import"./6TYmzuF5.js";import"./DO7Ufrqu.js";import"./Bw4533_S.js";import"./BeV_2vTx.js";import"./DQFSO0Sy.js";import"./DlKZEFPo.js";import"./zGBzNvge.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./D1wvxfUy.js";import"./CTRAaZiO.js";import"./hxgYBnNa.js";import"./ZIA996xd.js";import"./D7EZVpG5.js";import"./BiOLToJe.js";import"./Cty1Qmbh.js";import"./Z4WVNJrb.js";import"./de4nkRf9.js";import"./CTfTbMiI.js";import"./BWJbVfKg.js";import"./l0sNRNKZ.js";import"./BKQy72-l.js";import"./D_A4TIs9.js";import"./D0csLVr8.js";import"./DNtYbVbo.js";/* empty css        */import"./UiD2yG31.js";const ao=V({__name:"design",setup(N){const n=v(),o=x(),{height:c}=C(),u=b(),{initTabs:l}=S();y(window,"beforeunload",m=>{o.isChangeData&&(m.preventDefault(),m.returnValue="内容已修改，确认离开页面吗？")}),D(async(m,f)=>{try{o.isChangeData&&n.isLogin&&await E.confirm("内容已修改，确认离开页面吗？")}catch{return!1}});const e=()=>{o.isChangeData=!0};return r(()=>o.canvasJson,e),r(()=>o.music,e),r(()=>o.dub,e),r(()=>o.voiceContent,e,{deep:!0}),r(()=>u.query,()=>{l()}),(m,f)=>{const p=d,a=h,_=g,s=w;return $(),z("div",{class:"overflow-x-auto",style:H({height:`${M(c)}px`})},[t(s,{class:"bg-page !min-w-[1200px] h-full"},{default:i(()=>[t(p,{height:"auto",style:{padding:"0"}},{default:i(()=>[t(L)]),_:1}),t(s,{class:"min-h-0"},{default:i(()=>[t(a,{width:"auto",style:{overflow:"visible"}},{default:i(()=>[t(k)]),_:1}),t(_,{style:{padding:"0"}},{default:i(()=>[t(B)]),_:1}),t(a,{width:"auto"},{default:i(()=>[t(R)]),_:1})]),_:1})]),_:1})],4)}}});export{ao as default};
