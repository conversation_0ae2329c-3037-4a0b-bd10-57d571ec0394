import{E}from"./Ct8b-CPS.js";import{E as M}from"./L7ewHh_h.js";import{_ as w}from"./WSQgGJ_U.js";import{_ as B}from"./B_BP6MI7.js";import{E as L}from"./D_paGRSe.js";import{cn as S}from"./BQ-RMI0l.js";/* empty css        */import{n as $,o as l,q,u as r,v as z,m as _}from"./Cz8cblQP.js";import{_ as F}from"./CGQC2hmt.js";import{l as I,F as N,M as a,N as i,Z as t,a0 as u,O as n,u as s,y as j,_ as A,aq as D,a1 as O,X as R,a7 as U}from"./Dp9aCaJ6.js";import{_ as X}from"./DlAUqK2U.js";import"./DdkLcgv7.js";import"./Ci86EFhe.js";import"./BBQxRZuk.js";import"./BOZQs96k.js";import"./B5ZCswNG.js";import"./Bb7qOt1h.js";import"./Cpg3PDWZ.js";/* empty css        */import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";import"./BXLvtum9.js";import"./DhTLUUeS.js";import"./DCTLXrZ8.js";import"./BHIHQvwt.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */const Z={class:"mb-2",style:{"--el-border-radius-base":"12px"}},G={key:0,class:"grid grid-cols-2 gap-4"},H=["onClick"],J={class:"relative rounded-[12px] overflow-hidden"},K={class:"text-hidden-2 text-center"},P=I({__name:"sd-model",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(f,{emit:g}){const v=g,y=f,{modelValue:m}=S(y,v),c=o=>{o==="clear"?(m.value="",_.value=[]):(m.value=r.value[o].model_name,_.value=r.value[o].loras)},h=o=>{c("clear"),l.value=o,z()};return N(async()=>{$()}),(o,d)=>{const b=E,p=M,x=w,C=B,k=L;return a(),i("div",null,[t(F,{title:"主要模型",required:"",tips:"让AI根据此模型的风格绘制图片，修改合适的描述词和参数可以让生成效果更加精美"}),t(p,null,{default:u(()=>[n("div",Z,[t(b,{block:!1,class:"h-[38px] !bg-[transparent]",id:"ddddd",modelValue:s(l),"onUpdate:modelValue":d[0]||(d[0]=e=>j(l)?l.value=e:null),options:s(q),onChange:h},null,8,["modelValue","options"])])]),_:1}),t(p,{"max-height":"360px"},{default:u(()=>[s(r).length>0?(a(),i("div",G,[(a(!0),i(A,null,D(s(r),(e,V)=>(a(),i("div",{key:e.id,class:"flex flex-col gap-2",onClick:Q=>c(V)},[n("div",J,[t(x,{class:"rounded-[12px] overflow-hidden bg-[var(--el-bg-color-page)]",src:e.cover,fit:"cover",ratio:[144,100]},null,8,["src"]),n("div",{class:R(["absolute top-0 left-0 bg-[rgba(0,0,0,0.4)] w-full h-full flex justify-center items-center transition-opacity opacity-0",{"opacity-100":e.model_name===s(m)}])},[t(C,{name:"el-icon-CircleCheckFilled",size:20,color:"#fff"})],2)]),n("div",K,U(e.title||e.model_name),1)],8,H))),128))])):(a(),O(k,{key:1,description:"暂无模型数据","image-size":50}))]),_:1})])}}}),Me=X(P,[["__scopeId","data-v-25bc3d68"]]);export{Me as default};
