import{_ as x}from"./C8ZjkaO0.js";import{E as h,a as y}from"./emCILI1b.js";import"./B5S_Er7H.js";import{u as $,a as w}from"./DsvdwZsr.js";import{l as T,m as k,b as B,c as D,M as r,N as p,O as m,Z as l,a0 as c,_ as M,aq as S,u as e,a4 as E,a1 as f,a7 as N,aa as z,a3 as L,ab as V}from"./Dp9aCaJ6.js";import{_ as j}from"./DA_wDe0g.js";import{_ as A}from"./_cvRaOCd.js";import{_ as I}from"./DsLrYHAn.js";import O from"./CxS1xOro.js";import P from"./BWdjqLFG.js";import{_ as q}from"./DxZnwchX.js";import F from"./q29Y2Wdl.js";import{_ as R}from"./CQPR_q9V.js";import{_ as Z}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./BuewU-zc.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./CmwH0tdT.js";import"./DTpVU3st.js";import"./Cx3Arr0P.js";import"./CacOB3dg.js";import"./GM0TYtnE.js";import"./Tox-HrLw.js";import"./ChwnGhEb.js";import"./DySxZBuW.js";/* empty css        *//* empty css        */import"./pttW598Y.js";import"./DTE0c8O2.js";import"./D6nEL_TX.js";import"./YnZswHSX.js";/* empty css        */import"./B4AH7dv-.js";/* empty css        */import"./Baby5rv5.js";import"./Db173UeR.js";import"./DbRYflOL.js";import"./DCTLXrZ8.js";import"./Di6b97gu.js";import"./BzS1o01N.js";import"./BmXQ27ak.js";import"./IItDHZjE.js";import"./Ba21RIuI.js";import"./DlKZEFPo.js";import"./D_IjOTI0.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./Bese9Adk.js";import"./DuG-fuuD.js";import"./CCyf6mXH.js";import"./BWzes9rz.js";const G={class:"h-full relative z-20 bg-white"},H={class:"flex flex-col items-center justify-center"},J={class:"text-lg mt-[6px]"},K={class:"w-[360px] h-full"},Q=T({__name:"index",setup(U){const u={Avatar:j,Dub:A,Music:I,Background:O,Text:P,Captions:q,Maps:F,Prospect:R},d=$(),{tabsState:a,initTabs:v,changeTabs:b}=w(),s=k({get(){return a.value.isCollapsed},set(i){a.value.isCollapsed=i}}),n=B(!1);return v(),D(()=>a.value.current,async i=>{d.setActiveObjectByType(i)}),(i,o)=>{const _=x,C=y,g=h;return r(),p("div",{class:"control-panel h-full",onMouseenter:o[2]||(o[2]=t=>n.value=!0),onMouseleave:o[3]||(o[3]=t=>n.value=!1)},[m("div",G,[l(g,{"tab-position":"left",class:"h-full",type:"card","model-value":e(a).current,onTabChange:o[0]||(o[0]=t=>e(b)(t))},{default:c(()=>[(r(!0),p(M,null,S(e(a).tabs,t=>(r(),f(C,{key:t.id,name:t.id,lazy:""},{label:c(()=>[m("div",H,[l(_,{name:t.icon,size:22},null,8,["name"]),m("span",J,N(t.label),1)])]),default:c(()=>[z(m("div",K,[(r(),f(L(u[t.component])))],512),[[V,!e(s)]])]),_:2},1032,["name"]))),128))]),_:1},8,["model-value"])]),e(s)||e(n)?(r(),p("div",{key:0,class:"panel-left-arrow",onClick:o[1]||(o[1]=t=>s.value=!e(s))},[l(_,{class:"mr-1",name:`el-icon-${e(s)?"CaretRight":"CaretLeft"}`},null,8,["name"])])):E("",!0)],32)}}}),Wt=Z(Q,[["__scopeId","data-v-8d50fa1f"]]);export{Wt as default};
