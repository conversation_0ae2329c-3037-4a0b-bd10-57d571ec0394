import{b as _,j as u,E as f}from"./CylNgAGi.js";import{c as n,a,b,d as g,f as k}from"./B8XxWWwZ.js";import{l as y,M as t,N as s,Z as h,a0 as x,u as e,O as w,a4 as r,a7 as i}from"./Dp9aCaJ6.js";const B={class:"absolute bottom-0 left-0 bg-body p-4 w-full z-10"},S={key:0},v={key:1},C={key:0,class:"text-sm ml-2"},N={key:1,class:"text-sm ml-2"},D=y({__name:"create-button",props:{disabled:{type:Boolean,default:!1}},emits:["create"],setup(l,{emit:E}){const c=_(),d=u(),m=()=>{b()||g(k.value)};return(L,o)=>{const p=f;return t(),s("div",B,[h(p,{size:"large",type:"primary",class:"w-full",disabled:l.disabled,onClick:m,loading:e(n)},{default:x(()=>[e(n)?(t(),s("div",S,"正在请求中")):(t(),s("div",v,[o[0]||(o[0]=w("span",{class:"text-base font-bold"},"立即生成",-1)),e(a).is_member?(t(),s("span",C,"会员免费")):r("",!0),e(a).power!=0&&!e(a).is_member&&e(d).isLogin?(t(),s("span",N,"消耗"+i(e(a).power||"--")+i(e(c).getTokenUnit),1)):r("",!0)]))]),_:1},8,["disabled","loading"])])}}});export{D as _};
