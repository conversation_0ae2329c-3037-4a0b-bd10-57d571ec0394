import{_ as b}from"./B-qcbZcc.js";import{E as V}from"./PR4uin41.js";import{b as k,bN as w,v as z}from"./DjCZV6kq.js";import"./B1pEJPcQ.js";import{l as B,b as v,m as N,j as S,F as D,u as s,M as n,N as i,aa as E,Z as p,a0 as F,V as I,O as f,a4 as u,a2 as j,a9 as M}from"./Dp9aCaJ6.js";import{_ as R}from"./DlAUqK2U.js";const U={key:0},$={"element-loading-text":"上传中..."},A={key:0,class:"el-upload flex-col bg-fill-lighter"},L={key:1,class:"imgContiner relative"},O=["src"],P=B({__name:"index",props:{modelValue:{type:String,default:""},excludeDomain:{type:Boolean,default:!1},canClose:{type:Boolean,default:!0},size:{type:String,default:"100px"}},emits:["change","update:modelValue"],setup(r,{emit:h}){const g=h,{getImageUrl:x}=k(),d=r,c=v(!1),_=v(!1),t=N({get(){return d.excludeDomain?x(d.modelValue):d.modelValue},set(o){g("update:modelValue",o)}}),m=S(),y=async({raw:o})=>{var e,l;try{c.value=!0;const a=await w({file:o});c.value=!1,t.value=d.excludeDomain?a.url:a.uri,g("change",a.uri),(e=m.value)==null||e.clearFiles()}catch{c.value=!1,(l=m.value)==null||l.clearFiles()}};return D(()=>{_.value=!0}),(o,e)=>{const l=b,a=V,C=z;return s(_)?(n(),i("div",U,[E((n(),i("div",$,[p(a,{ref_key:"uploadRef",ref:m,class:"avatar-uploader","show-file-list":!1,limit:1,"on-change":y,"auto-upload":!1,accept:".jpg,.png,.gif,.jpeg"},{default:F(()=>[I(o.$slots,"default",{},()=>[s(t)?u("",!0):(n(),i("div",A,[p(l,{name:"el-icon-Plus",size:20}),e[1]||(e[1]=f("div",{class:"text-tx-secondary mt-[2px]"},"添加图片",-1))])),s(t)?(n(),i("div",L,[f("div",{class:"border border-solid border-br-light rounded-[6px] relative cursor-pointer",style:j({width:r.size,height:r.size})},[f("img",{class:"rounded-lg w-full h-full",src:s(t)},null,8,O)],4),r.canClose?(n(),i("div",{key:0,class:"icon absolute top-[-10px] right-[-10px] text-tx-secondary",onClick:e[0]||(e[0]=M(Z=>t.value="",["stop"]))},[p(l,{size:"20",name:"el-icon-CircleCloseFilled"})])):u("",!0)])):u("",!0)],!0)]),_:3},512)])),[[C,s(c)]])])):u("",!0)}}}),T=R(P,[["__scopeId","data-v-ba6ae763"]]);export{T as _};
