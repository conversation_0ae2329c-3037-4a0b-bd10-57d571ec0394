import{_ as R,a as b}from"./C91bcVt2.js";import{E as z}from"./CH0sQbfA.js";import{u as B,_ as M}from"./CH2LqYJ4.js";import{_ as E}from"./C_7xENts.js";import{E as k}from"./CiiNlfb8.js";import{v as C}from"./CzTOiozM.js";/* empty css        */import{b as I}from"./Bu_nKEGp.js";import{l as L,j as P,r as T,F as $,c as V,M as c,N as l,O as o,aa as d,u as f,Z as r,a0 as j,a4 as D}from"./Dp9aCaJ6.js";const F={class:"h-full bg-body rounded-[15px] p-[16px] flex flex-col"},O={class:"flex-1 min-h-0 flex flex-col"},U={class:"flex-1 min-h-0"},Z={key:0,class:"h-full flex flex-col"},q={class:"flex-1 min-h-0 flex"},A={class:"flex-1 min-w-0 h-full"},G={"infinite-scroll-distance":"50"},H={class:"mt-[16px]"},J={key:1,class:"h-full flex flex-col items-center justify-center"},K={class:"text-tx-secondary"},cs=L({__name:"music",setup(Q){const i=P(),{getMusic:u,currentId:p,pause:W}=B(),s=T({pageNo:1,count:0,pageSize:15,loading:!1,lists:[]}),_=async()=>{try{const t=await I({status:2,page_no:s.pageNo,page_size:s.pageSize});s.count=t.count,s.pageNo===1&&(s.lists=[]),s.lists.push(...t.lists)}catch{}finally{s.loading=!1}};u();const m=()=>{s.count>=s.pageNo*s.pageSize&&(s.pageNo++,_())},g=async()=>{s.pageSize=s.pageNo*s.pageSize,s.pageNo=1,await _()},x=async()=>{s.loading=!0,s.pageSize=15,s.pageNo=1,await _()},h=()=>{const t=s.lists.find(e=>e.status===2);t&&(p.value=t.id)};return $(async()=>{await x(),h()}),V(p,t=>{if(i.value){const e=document.getElementById(`music-item-${t}`);if(!e)return;const a=e==null?void 0:e.getBoundingClientRect(),n=i.value.wrapRef.getBoundingClientRect();a.top<n.top&&i.value.setScrollTop(e==null?void 0:e.offsetTop),a.bottom>n.bottom&&i.value.setScrollTop((e==null?void 0:e.offsetTop)-n.height+a.height)}}),(t,e)=>{const a=R,n=z,v=b,y=M,N=E,S=k,w=C;return c(),l("div",F,[o("div",O,[d((c(),l("div",U,[f(s).lists.length?(c(),l("div",Z,[o("div",q,[o("div",A,[r(n,{ref_key:"scrollBarRef",ref:i},{default:j(()=>[d((c(),l("div",G,[r(a,{"music-list":f(s).lists,onUpdate:g},null,8,["music-list"])])),[[S,m]])]),_:1},512)]),r(v)]),o("div",H,[r(y,{ref:"musicPlayerRef",class:"bg-page"},null,512)])])):f(s).loading?D("",!0):(c(),l("div",J,[o("div",K,[r(N,{size:45,name:"local-icon-music1"})]),e[0]||(e[0]=o("div",{class:"my-[10px]"},"当前还没有音乐哦",-1)),e[1]||(e[1]=o("div",{class:"text-tx-secondary text-sm flex item-center"}," 快去创建你的作品吧！ ",-1))]))])),[[w,f(s).loading]])])])}}});export{cs as _};
