import{a as s,E as n}from"./ajKoJK3T.js";import"./BQ-RMI0l.js";import{_ as r}from"./DlAUqK2U.js";import{a1 as c,a0 as e,M as _,Z as m,V as o}from"./Dp9aCaJ6.js";import"./C2SkKptv.js";const p={};function d(t,f){const a=n,l=s;return _(),c(l,{"model-value":"name"},{default:e(()=>[m(a,{name:"name"},{title:e(()=>[o(t.$slots,"title",{},void 0,!0)]),default:e(()=>[o(t.$slots,"default",{},void 0,!0)]),_:3})]),_:3})}const k=r(p,[["render",d],["__scopeId","data-v-7291479b"]]);export{k as default};
