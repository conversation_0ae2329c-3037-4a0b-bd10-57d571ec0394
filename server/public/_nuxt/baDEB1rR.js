import{_ as st}from"./C9-ovotH.js";import{_ as nt,c as rt,a as it,b as lt}from"./Vstdrfbz.js";import{_ as ct}from"./B_BP6MI7.js";import{b as ut,a as dt,j as pt,cw as je,bx as ft,by as vt,u as mt,bB as X,bE as ue,f as Y,E as _t}from"./BQ-RMI0l.js";import{E as ht,a as yt}from"./ajKoJK3T.js";import{E as gt}from"./KlqAu7Os.js";import{_ as xt}from"./DDaEm-_F.js";import{u as wt}from"./CxQjGp2g.js";import{l as bt,j as I,b as x,ak as kt,r as de,m as Ae,c as Rt,F as Ct,k as Et,M as c,N as w,O as s,u as e,X as qe,a4 as _,a7 as z,Z as v,a0 as f,a1 as b,_ as pe,aq as fe,as as Tt,a6 as U,aa as Ve,ab as Fe,a2 as ve,n as St}from"./Dp9aCaJ6.js";import{u as Lt}from"./DkjF24mE.js";import{u as It,a as zt}from"./2wJCR1yG.js";import{u as Pt}from"./Bb2-23m7.js";import{_ as $t}from"./CCkJG4UG.js";import{_ as jt}from"./Cj8yBlmW.js";import{C as At,D as qt,c as Vt,b as Ft,r as Ot,a as Bt,v as Dt,d as Ht}from"./Bo3PTL3c.js";import{P as Nt}from"./B3UVgHox.js";import{u as Mt}from"./D02l-YB7.js";import{_ as Ut}from"./B0_-IDWi.js";import{useSessionFiles as Wt}from"./p3HD8iMK.js";import{E as Oe}from"./L7ewHh_h.js";import{_ as Jt}from"./DlAUqK2U.js";import"./C1aUxTFE.js";/* empty css        */import"./DdkLcgv7.js";import"./Ci86EFhe.js";import"./BBQxRZuk.js";import"./BOZQs96k.js";import"./DfVmRZTU.js";/* empty css        */import"./B5ZCswNG.js";import"./Bb7qOt1h.js";import"./Cpg3PDWZ.js";import"./De57gGYj.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./C-ql7yOt.js";/* empty css        */import"./DwFObZc_.js";import"./B_1915px.js";import"./D49putpZ.js";import"./Bg2e09vA.js";import"./B7SD2TCw.js";import"./C2S7-d6r.js";import"./B36mVPSB.js";import"./Cwr-KwFA.js";import"./DpOQaFAg.js";import"./C2SkKptv.js";import"./DP2rzg_V.js";/* empty css        */import"./BkCXSo5y.js";const Be=""+new URL("user_avatar.B42E77Pp.png",import.meta.url).href,Zt={class:"h-full flex flex-col max-w-[720px] mx-auto bg-page rounded-[10px] overflow-hidden",style:{"box-shadow":"0px 5px 40px 0px rgba(0, 0, 0, 0.05)"}},Gt={class:"flex p-main items-center bg-body"},Kt=["src"],Qt={class:"text-2xl line-clamp-1"},Xt={class:"text-tx-secondary line-clamp-1"},Yt={class:"flex-1 min-h-0"},eo={ref:"containerRef",class:"h-full flex flex-col rounded relative"},to={class:"absolute top-0 left-0 w-full h-full flex flex-col z-10"},oo={class:"flex-1 min-h-0"},ao={class:"p-main"},so={class:"my-[5px]"},no={class:"bg-body"},ro={class:"flex items-center h-12 px-3 bg-page rounded-lg max-w-xs line-clamp-1 overflow-hidden"},io={key:0,class:"pb-[10px] text-center text-tx-regular"},lo={key:1,class:"h-full relative"},co=["width","height"],uo={class:"h-full flex justify-center items-center"},po={class:"p-[20px] h-full flex relative z-10"},fo={class:"flex-1 h-full flex flex-col"},vo={class:"flex-1 min-h-0"},mo={class:"flex items-center cursor-pointer"},_o={class:"text-xl flex-1 min-w-0 text-white"},ho={class:"h-full flex"},yo={class:"h-full flex flex-col items-center w-[160px] justify-end"},go=["width","height","id"],xo={class:"text-xs text-white bg-[rgba(51,51,51,0.3)] py-[5px] px-[10px] rounded my-[10px]"},wo={class:"w-[400px] h-full flex flex-col mr-[20px] pt-[100px]"},bo={class:"flex-1 min-h-0 bg-[rgba(0,0,0,0.5)] rounded-[20px] overflow-hidden flex flex-col"},ko={class:"flex-1 min-h-0"},Ro={class:"py-4 px-[20px]"},Co={key:1,class:"h-full flex justify-center text-tx-secondary items-center"},Eo={key:0,class:"flex justify-center items-center py-[10px]"},To={class:"flex flex-col justify-center items-center"},So={class:"flex justify-center mt-4"},Lo=bt({__name:"[key]",async setup(Io){let W,me;const J=ut();Mt();const F=Wt(),De=dt(),E=pt(),{copy:He}=wt(),ee=I(),{key:k=""}=De.params,T=x(""),{height:_e,width:zo}=je(),{data:a}=([W,me]=kt(async()=>Lt(async()=>{const o=await At({apikey:k}),t=X("SHARE_CHAT_UNIQUE_ID","");if(!t.value){const[d]=await qt({robot_id:o.robot.id},{authorization:k,password:T.value,identity:E.visitorId});t.value=d}return{...o,uniqueId:t.value}},{default(){return{robot:{}}}},"$89nQiJhyNZ")),W=await W,me(),W),te=I(),P=de({_index:-1,robot_id:a.value.robot.id,record_id:-1,content:""}),Ne=(o,t)=>{var d;P.record_id=o.id,P._index=t,(d=te.value)==null||d.open()},Me=async()=>{var o;try{await Vt(P),(o=te.value)==null||o.close(),P.content="",h.value[P._index].is_feedback=1}catch(t){console.log("反馈提交失败-->",t)}},O=Ae(()=>a.value.chat_type===2&&a.value.robot.is_digital&&a.value.digital.id&&!a.value.digital.is_disable?2:1);J.isMobile&&O.value===2&&window.location.replace(`/mobile/packages/pages/digital_chat/share_chat?key=${k}`);const he=Ae(()=>{var o;return((o=a.value.menus)==null?void 0:o.map(t=>({keyword:t})))||[]}),H=async()=>{var t;const o=X(ue,{});if(T.value=o.value[k]||"",a.value.pwd&&!T.value)return(t=ee.value)==null||t.open(),Promise.reject()},Ue=async o=>{var d;const t=X(ue,{});T.value=o.password,t.value=Object.assign(t.value,{[k]:o.password}),(d=ee.value)==null||d.close(),$e()},ye=()=>{const o=X(ue,{});o.value=Object.assign(o.value,{[k]:""})},h=x([]);let oe=0;const ae=async()=>{try{const o=await Bt({share_apikey:k,identity:E.visitorId,page_size:25e3},{password:T.value,authorization:k,identity:E.visitorId});if(h.value=o.lists||[],setTimeout(()=>{N()}),O.value===2&&y.value==3){const t=h.value[h.value.length-1];t&&t.id!==oe&&(oe=t.id,Qe(oe))}}catch(o){return o=="访问密码错误!"&&(ye(),await H()),Promise.reject()}},ge=async()=>{await H(),await Y.confirm("确定清空记录？"),await Ft({},{password:T.value,authorization:k,identity:E.visitorId}),ae()};let u=null;const $=x(!1),Z=I(),G=async(o,t="input")=>{var g;if(await E.getFingerprint(),await H(),!o)return Y.msgError("请输入问题");if($.value)return;$.value=!0,R(3);const d=Date.now(),n=F.files.value.map(r=>({name:r.name,type:"30",url:r.url}));h.value.push({type:1,content:o,files_plugin:n}),h.value.push({type:2,typing:!0,content:"",reasoning:"",key:d}),(g=Z.value)==null||g.setInputValue();const l=h.value.find(r=>r.key===d);u=Ot({question:o,unique_id:a.value.uniqueId,stream:!0,files:F.files.value.map(r=>({...r,type:"30"}))},{password:T.value,authorization:k,identity:E.visitorId}),u.addEventListener("reasoning",({data:r})=>{const{data:p,index:C}=r;l.reasoning||(l.reasoning=""),l.reasoning+=p}),u.addEventListener("chat",({data:r})=>{const{data:p,index:C}=r;l.content||(l.content=""),l.content+=p}),u.addEventListener("file",({data:r})=>{try{const p=JSON.parse(r.data);l.files=p}catch(p){console.error(p)}}),u.addEventListener("image",({data:r})=>{try{const p=JSON.parse(r.data);l.images=p}catch(p){console.error(p)}}),u.addEventListener("video",({data:r})=>{try{const p=JSON.parse(r.data);l.videos=p}catch(p){console.error(p)}}),u.addEventListener("close",()=>{l.typing=!1,$.value=!1,setTimeout(async()=>{await ae(),N()},1e3)}),u.addEventListener("error",r=>{var p,C,q;R(1),r.errorType==="connectError"&&Y.msgError("请求失败，请重试"),((p=r.data)==null?void 0:p.code)===1200&&(Y.msgError((C=r.data)==null?void 0:C.message),ye(),setTimeout(()=>{H()},10)),["connectError","responseError"].includes(r.errorType)&&(h.value.splice(h.value.length-2,2),t==="input"&&((q=Z.value)==null||q.setInputValue(o))),l.typing=!1,setTimeout(()=>{$.value=!1},200)})},K=I(),se=x(),N=async()=>{var t,d,n;const o=(d=(t=K.value)==null?void 0:t.wrapRef)==null?void 0:d.scrollHeight;(n=K.value)==null||n.setScrollTop(o)},{height:We}=ft(se);vt(We,()=>{$.value&&N()},{throttle:500,immediate:!0});const y=x(0),Je=de({0:"正在初始化对话...",1:"点击开始说话",2:"我在听，您请说...",3:"稍等，让我想一想",4:"正在回复中..."}),R=o=>{O.value===2&&(y.value=o)},xe=x(),B=x(!0),ne=x(!1),re=x(0),j=x(!1),we=x(0),S=de({id:"audio-canvas",width:80,height:40,minHeight:5,scale:2}),{render:Ze,stopRender:Ge,draw:Po}=It(S),{start:Ke,stop:ie,isRecording:M,authorize:be,close:$o,isOpen:jo}=zt({onstart(){R(2),clearTimeout(xe.value),j.value=!1,re.value=Date.now()},async onstop(o){if(Ge(),j.value=!1,!ne.value){R(1);return}ne.value=!1,R(3);try{const t=await Dt({file:o.blob});if(!t.text){B.value&&A();return}G(t.text,"voice")}catch{B.value&&A()}},async ondata(o){var d;const t=Date.now();j.value&&Ze(o),o.powerLevel>=10&&(clearTimeout(we.value),y.value=2,j.value=!0,re.value=t,we.value=setTimeout(()=>{ne.value=!0,clearTimeout(xe.value),Re(),ie()},2e3)),t-re.value>=((d=a.value.digital)==null?void 0:d.idle_time)*1e3&&(j.value||(Ye(),ie()))}}),{play:ke,pause:Re,audioPlaying:Ce}=Pt({onstart(){y.value=4,le.value&&(le.value=!1)},onstop(){R(2),B.value?A():R(1)},onerror(){R(1)}}),Qe=async o=>{ke(async()=>await Ee({type:2,record_id:o}),!1)},A=async()=>{M.value||(await be(),Ke())},Xe=async()=>{if(y.value==4){Re(),A();return}y.value!=3&&(M.value?(B.value=!1,ie(),R(1)):(B.value=!0,A()))},Ee=async o=>{try{const{url:t}=await Ht(o,{password:T.value,authorization:k,identity:E.visitorId});return t}catch{return R(1),Promise.reject()}},Q=x(""),le=x(!1),Ye=async()=>{if(!a.value.robot.is_digital||!a.value.digital.id||(Q.value||(Q.value=await Ee({type:3,record_id:a.value.robot.id})),!Q.value))return Promise.reject();le.value=!0;const o=Date.now();h.value.push({type:2,typing:!1,content:a.value.digital.idle_reply,key:o}),await St(),N(),ke(Q.value,!1)};Rt(y,o=>{switch(o){case 2:A()}}),I();const ce=I(),{width:Te,height:Se}=je(),Le=I(),Ie=I(),ze=async o=>new Promise((t,d)=>{const n=document.createElement("video");n.src=o,n.preload="auto",n.loop=!0,n.muted=!0,n.autoplay=!1,n.playsInline=!0,n.play(),n.addEventListener("loadedmetadata",l=>{n.width=n.videoWidth,n.height=n.videoHeight,t(n)}),n.addEventListener("error",l=>{d(l)}),n.addEventListener("play",l=>{Pe()})}),Pe=()=>{if(!ce.value)return;const o=Te.value*2,t=Se.value*2,d=ce.value.getContext("2d");if(!d)return;const n=y.value===4?Ie.value:Le.value;if(!n)return;d.clearRect(0,0,o,t);const{videoHeight:l,videoWidth:g}=n;let r=0,p=0,C=g,q=l;if(g/l>=o/t){const L=o*l/t;r=(g-L)/2,C=L}else{const L=t*g/o;p=(l-L)/2,q=L}d.drawImage(n,r,p,C,q,0,0,o,t),requestAnimationFrame(Pe)},$e=async()=>{if(await ae(),O.value==2){Le.value=await ze(a.value.digital.wide_stay_video),Ie.value=await ze(a.value.digital.wide_talk_video),B.value=!0;try{await be(),A()}catch{R(1)}setTimeout(()=>{N()},100)}};Ct(async()=>{await E.getFingerprint(),await H(),$e()});const et=()=>{u==null||u.removeEventListener("reasoning"),u==null||u.removeEventListener("chat"),u==null||u.removeEventListener("close"),u==null||u.removeEventListener("error"),u==null||u.abort()};return Et(()=>{et()}),mt({title:a.value.name}),(o,t)=>{const d=st,n=nt,l=it,g=ct,r=_t,p=ht,C=yt,q=lt,L=rt,tt=gt,ot=xt;return c(),w("div",null,[s("div",{class:"layout-bg",style:ve({height:`${e(_e)=="Infinity"?"100vh":e(_e)+"px"}`})},[e(O)===1?(c(),w("div",{key:0,class:qe(["h-full",{"p-main":!e(J).isMobile}])},[s("div",Zt,[s("div",Gt,[e(a).robot.image?(c(),w("img",{key:0,src:e(a).robot.image,class:"w-[40px] h-[40px] mr-[10px] flex-none rounded-full",alt:""},null,8,Kt)):_("",!0),s("div",null,[s("div",Qt,z(e(a).robot.name),1),s("div",Xt,z(e(a).robot.intro),1)])]),s("div",Yt,[v(tt,{class:"h-full",content:e(J).getChatConfig.watermark,font:{color:"rgba(0,0,0,0.06)",fontSize:12}},{default:f(()=>{var m,V;return[s("div",eo,[s("div",to,[s("div",oo,[v(e(Oe),{ref_key:"scrollbarRef",ref:K},{default:f(()=>[s("div",ao,[s("div",{ref_key:"innerRef",ref:se},[e(a).robot.welcome_introducer?(c(),b(n,{key:0,class:"mb-[20px]",type:"left",avatar:`${e(a).robot.icons?e(a).robot.icons:e(a).robot.image}`,bg:"var(--el-bg-color)"},{default:f(()=>[v(d,{content:e(a).robot.welcome_introducer,onClickCustomLink:t[0]||(t[0]=i=>G(i,"link"))},null,8,["content"])]),_:1},8,["avatar"])):_("",!0),(c(!0),w(pe,null,fe(e(h),(i,D)=>(c(),w("div",{key:i.id+""+D,class:"mt-4"},[i.type==1?(c(),b(n,{key:0,type:"right",bg:"var(--el-color-primary)",color:"white",avatar:e(Be)},{actions:f(()=>[s("div",so,[v(r,{link:"",type:"info",onClick:at=>e(He)(i.content)},{icon:f(()=>[v(g,{name:"el-icon-CopyDocument"})]),default:f(()=>[t[4]||(t[4]=U(" 复制 "))]),_:2},1032,["onClick"])])]),default:f(()=>[v(l,{content:i.content,"files-plugin":i.files_plugin},null,8,["content","files-plugin"])]),_:2},1032,["avatar"])):_("",!0),i.type==2?(c(),b(n,{key:1,type:"left",time:i.create_time,avatar:`${e(a).robot.icons?e(a).robot.icons:e(a).robot.image}`,bg:"var(--el-bg-color)"},{outer_actions:f(()=>[i.create_time&&e(a).robot.is_show_feedback?(c(),b(r,{key:0,class:"ml-[52px] mt-2",style:{"--el-button-border-color":"transparent","--el-color-info-light-8":"transparent"},type:i.is_feedback?"info":"primary",plain:!0,bg:"",size:"small",disabled:i.is_feedback,onClick:at=>Ne(i,D)},{default:f(()=>[U(z(i.is_feedback?"已反馈":"反馈"),1)]),_:2},1032,["type","disabled","onClick"])):_("",!0)]),default:f(()=>[i.reasoning?(c(),b(C,{key:0,"model-value":"the-chat-msg-collapse",class:"mb-2 the-chat-msg-collapse"},{default:f(()=>[v(p,{title:"深度思考",name:"the-chat-msg-collapse"},{default:f(()=>[v(l,{content:i.reasoning,class:"text-tx-secondary px-3 border-l-[3px] border-br-light"},null,8,["content"])]),_:2},1024)]),_:2},1024)):_("",!0),v(l,{content:String(i.content),type:"html",typing:i.typing,"show-copy":"","show-context":!!e(a).robot.is_show_context,"show-quote":!!e(a).robot.is_show_quote,"show-voice":e(J).getIsVoiceOpen,context:i.context,quotes:i.quotes,images:i.images,files:i.files,videos:i.videos,"record-id":i.id,"record-type":2,channel:e(k),"user-id":e(E).visitorId},null,8,["content","typing","show-context","show-quote","show-voice","context","quotes","images","files","videos","record-id","channel","user-id"])]),_:2},1032,["time","avatar"])):_("",!0)]))),128))],512)])]),_:1},512)]),s("div",no,[v(L,{ref_key:"chatActionRef",ref:Z,loading:e($),menus:e(he),"btn-color":"#f6f6f6",onEnter:G,onClear:ge,onPause:t[1]||(t[1]=i=>{var D;return(D=e(u))==null?void 0:D.abort()})},Tt({_:2},[e(a).robot.support_file?{name:"btn",fn:f(()=>[v(Ut,{class:"mr-3",type:"file","is-parse-content":!0,"is-only-parse-content":!0,onOnSuccess:e(F).addFile},null,8,["onOnSuccess"])]),key:"0"}:void 0,e(F).files.value.length?{name:"file-list",fn:f(()=>[(c(!0),w(pe,null,fe(e(F).files.value,i=>(c(),b(q,{key:i.id,onClose:D=>e(F).removeFile(i)},{default:f(()=>[s("div",ro,z(i.name),1)]),_:2},1032,["onClose"]))),128))]),key:"1"}:void 0]),1032,["loading","menus"]),(m=e(a).robot)!=null&&m.copyright?(c(),w("div",io,z((V=e(a).robot)==null?void 0:V.copyright),1)):_("",!0)])])],512)]}),_:1},8,["content"])])])],2)):_("",!0),e(O)===2?(c(),w("div",lo,[s("canvas",{ref_key:"canvasRef",ref:ce,id:"digital-canvas",width:e(Te)*2,height:e(Se)*2},null,8,co),U(" "+z(e(y))+" ",1),Ve(s("div",uo,t[5]||(t[5]=[s("img",{class:"w-[400px]",src:$t,alt:""},null,-1)]),512),[[Fe,e(y)===0]]),Ve(s("div",{class:"h-full",style:ve({background:e(a).robot.digital_bg})},[s("div",po,[s("div",fo,[s("div",vo,[s("div",mo,[s("div",_o,z(e(a).name),1)])]),t[6]||(t[6]=s("div",{class:"flex justify-center"},null,-1))]),s("div",ho,[s("div",yo,[s("div",{class:qe(["recorder gradient-button",{"recorder--stop":!e(M)&&!e(Ce)}]),onClick:Xe},[e(j)?(c(),w("canvas",{key:0,style:ve({width:`${e(S).width}px`,height:`${e(S).height}px`}),width:e(S).width*e(S).scale,height:e(S).height*e(S).scale,id:e(S).id},null,12,go)):_("",!0),e(M)&&!e(j)?(c(),b(g,{key:1,name:"el-icon-Microphone",size:40})):e(Ce)?(c(),b(g,{key:2,name:"local-icon-pause",size:40})):e(M)?_("",!0):(c(),b(g,{key:3,name:"el-icon-Mute",size:40}))],2),s("div",xo,[s("div",null,z(e(Je)[e(y)]),1)])]),s("div",wo,[s("div",bo,[s("div",ko,[e(h).length?(c(),b(e(Oe),{key:0,ref_key:"scrollbarRef",ref:K},{default:f(()=>[s("div",Ro,[s("div",{ref_key:"innerRef",ref:se},[(c(!0),w(pe,null,fe(e(h),(m,V)=>(c(),w("div",{key:m.id+""+V,class:"mt-4 sm:pb-[20px]"},[m.type==1?(c(),b(n,{key:0,type:"right",avatar:e(Be),color:"white"},{default:f(()=>[v(l,{content:String(m.content)},null,8,["content"])]),_:2},1032,["avatar"])):_("",!0),m.type==2?(c(),b(n,{key:1,type:"left",time:m.create_time,avatar:e(a).robot.icons?e(a).robot.icons:e(a).robot.image,bg:"#fff"},{default:f(()=>[m.reasoning?(c(),b(C,{key:0,"model-value":"the-chat-msg-collapse",class:"mb-2 the-chat-msg-collapse"},{default:f(()=>[v(p,{title:"深度思考",name:"the-chat-msg-collapse"},{default:f(()=>[v(l,{content:m.reasoning,class:"text-tx-secondary px-3 border-l-[3px] border-br-light"},null,8,["content"])]),_:2},1024)]),_:2},1024)):_("",!0),v(l,{content:String(m.content),type:"html",typing:m.typing,images:m.images,files:m.files,videos:m.videos,"record-id":m.id,"record-type":2},null,8,["content","typing","images","files","videos","record-id"])]),_:2},1032,["time","avatar"])):_("",!0)]))),128))],512)])]),_:1},512)):(c(),w("div",Co," 暂无聊天记录 "))]),e($)?(c(),w("div",Eo,[v(r,{color:"#fff",round:"",onClick:t[2]||(t[2]=m=>{var V;return(V=e(u))==null?void 0:V.abort()})},{default:f(()=>t[7]||(t[7]=[U(" 停止 ")])),_:1})])):_("",!0)]),s("div",null,[v(L,{ref_key:"chatActionRef",ref:Z,loading:[3,4].includes(e(y)),menus:e(he),"show-pause":!1,"show-clear":!1,onEnter:G},null,8,["loading","menus"])])]),s("div",To,[s("div",{class:"gradient-button",onClick:ge},[v(g,{name:"local-icon-clear",size:24})])])])])],4),[[Fe,e(y)!==0]])])):_("",!0)],4),v(jt,{ref_key:"loginRef",ref:ee,onConfirm:Ue},null,512),v(Nt,{ref_key:"popupRef",ref:te,async:!0,width:"390",title:"问题反馈",appendToBody:!1,class:"feedback-pop"},{footer:f(()=>[s("div",So,[v(r,{type:"primary",onClick:Me},{default:f(()=>t[8]||(t[8]=[U(" 提交反馈 ")])),_:1})])]),default:f(()=>[v(ot,{modelValue:e(P).content,"onUpdate:modelValue":t[3]||(t[3]=m=>e(P).content=m),rows:"8",placeholder:"描述一下你遇到了什么问题"},null,8,["modelValue"])]),_:1},512)])}}}),Pa=Jt(Lo,[["__scopeId","data-v-83e794a9"]]);export{Pa as default};
