import{E as x,a as g}from"./DqV7Sug9.js";import{a as k}from"./CLVDtxqA.js";import{b as v}from"./BNnETjxs.js";import y from"./BnJB0GR-.js";import{l as w,r as B,j as U,b as V,M as o,N as i,O as D,Z as E,a0 as s,_ as S,aq as h,u as a,a1 as n,a3 as C}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./BZXjPGYa.js";import"./C2uyROqA.js";import"./CYs1NzuK.js";import"./DTPcp5_i.js";import"./DCTLXrZ8.js";import"./DssWEouO.js";import"./JBMcerbz.js";import"./9Bti1uB6.js";import"./BICGiF8V.js";import"./DW2yWX9x.js";import"./C76tEu0I.js";import"./1ThCdcE_.js";import"./Bkygmrc1.js";import"./BjrRGs-I.js";import"./87qJxv_v.js";import"./uz2DoZ_U.js";import"./DI1qIu00.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./Hd0kM9t1.js";import"./B1txbFRp.js";/* empty css        */import"./g6LfHct_.js";import"./CD28ZGm9.js";import"./DY73EaVE.js";import"./D5Ye8pBY.js";import"./CRS4Ri59.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */const q={class:"p-main flex h-full flex-col"},R=w({__name:"setUp",emits:["update"],setup(T,{emit:l}){const c=l,d=k(),r=B({current:"baseSetting",lists:[{type:"baseSetting",name:"基础信息",component:U(y)}]}),m=V({}),p=async()=>{m.value=await v({id:d.query.id})},u=()=>{p(),c("update")};return p(),(j,e)=>{const _=g,f=x;return o(),i("div",q,[e[1]||(e[1]=D("div",{class:"text-xl font-medium"},"知识库设置",-1)),E(f,{class:"flex-1 min-h-0",modelValue:a(r).current,"onUpdate:modelValue":e[0]||(e[0]=t=>a(r).current=t)},{default:s(()=>[(o(!0),i(S,null,h(a(r).lists,(t,b)=>(o(),n(_,{label:t.name,name:t.type,key:b},{default:s(()=>[(o(),n(C(t.component),{data:a(m),onUpdate:u},null,40,["data"]))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])}}}),yt=N(R,[["__scopeId","data-v-4da5484e"]]);export{yt as default};
