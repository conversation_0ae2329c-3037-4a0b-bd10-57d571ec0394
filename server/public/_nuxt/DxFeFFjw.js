import{e as i}from"./DjCZV6kq.js";import{l as s,M as l,N as a,O as d,Z as r,u as e,ai as f}from"./Dp9aCaJ6.js";import{_ as u}from"./CXFsOouL.js";import{_}from"./BpVknjtj.js";import{useSearch as c}from"./BfgGtaz4.js";import{_ as x}from"./DlAUqK2U.js";import"./B-qcbZcc.js";import"./DiWwZzDD.js";import"./DCTLXrZ8.js";import"./hikO1t1A.js";import"./B9cNZX9s.js";import"./CIYOwJ14.js";import"./DZCM63qd.js";import"./9Bti1uB6.js";import"./Bi_VJcob.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./T3mT__EX.js";import"./_YM0GcE8.js";import"./BLV0QRdm.js";import"./Cq2NhlyP.js";import"./HpKxq3-W.js";const y={class:"bg-page overflow-hidden flex items-center input-select"},v={class:"flex-none flex px-[8px]"},k=s({__name:"input-select",setup(V){const{options:p,launchSearch:m}=c();return(w,o)=>{const n=i;return l(),a("div",y,[d("div",v,[r(u,{mode:"dropdown",model:e(p).model,"onUpdate:model":o[0]||(o[0]=t=>e(p).model=t),type:e(p).type,"onUpdate:type":o[1]||(o[1]=t=>e(p).type=t)},null,8,["model","type"])]),r(n,{modelValue:e(p).ask,"onUpdate:modelValue":o[2]||(o[2]=t=>e(p).ask=t),placeholder:"输入你想搜索的问题",onKeydown:o[3]||(o[3]=f(t=>e(m)(),["enter"]))},null,8,["modelValue"]),r(_,{onClick:o[4]||(o[4]=t=>e(m)())})])}}}),P=x(k,[["__scopeId","data-v-f9d3468d"]]);export{P as default};
