import{E as g,p as F}from"./B1MekKW7.js";import{E as j}from"./DeQZaZZG.js";import{a as C,E as O}from"./E8sU8GGn.js";import{E as U}from"./BxTfM8xu.js";/* empty css        *//* empty css        */import{u as A,T as E}from"./B3ZvK_Z-.js";import{_ as B}from"./Db0KaHnm.js";import N from"./BuzGgK0i.js";import{e as w}from"./CyaqLv7U.js";import{c as u}from"./DVLEc6gw.js";import{l as I,r as x,m as P,c as k,M as y,N as T,O as p,Z as a,a0 as n,a6 as X,u as e}from"./Dp9aCaJ6.js";import{_ as D}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./DxZY3BF8.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./DjadXojw.js";import"./D74K85Xd.js";import"./Bwx2-5pK.js";import"./Df0xfART.js";import"./DCTLXrZ8.js";import"./GQTyu-ru.js";import"./DmvaW7ga.js";import"./wW1KxDBP.js";import"./BMW57zJa.js";import"./AOe4nXt1.js";import"./vclUXml2.js";import"./DlKZEFPo.js";import"./BAyGR4uh.js";import"./t2WiU-SJ.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./eoit9ZmK.js";import"./KpUACw60.js";import"./uE1Ww79i.js";import"./Dkrb28GR.js";import"./CPkX1WPy.js";/* empty css        *//* empty css        */import"./DFeHsHxy.js";const H={class:"avatar-select h-full flex flex-col"},K={class:"px-main pt-main"},L={key:0,class:"mt-[5px] flex-1 min-h-0"},M={class:"h-full"},W={class:"p-main"},Z={class:"h-full"},$={class:"p-main"},q={key:1,class:"p-main"},G=I({__name:"text",setup(J){const r=A(),_=x({currentTab:"setting"}),b={fontSize:64,fontFamily:"Alibaba PuHuiTi",fill:"#ffffff",stroke:"",effect:{name:"",server_key:"",type:"",url:""}},o=x(u(b)),v=P(()=>{var s;return((s=r.activeObject)==null?void 0:s.customType)===E.TEXT}),z=()=>{_.currentTab="setting";const s=u(b);r.addText("这里是文字",E.TEXT,s)};k(()=>r.activeObject,s=>{var t;if(v.value)for(const l in o)o[l]=(t=s==null?void 0:s.data)==null?void 0:t[l];else Object.assign(o,u(b))},{immediate:!0});const S=["fontSize","fontFamily","fill","stroke"];return k(()=>o,s=>{var l,d,f;if(!v.value)return;for(const m in o)if(S.includes(m)){let c=s[m];m==="fontSize"&&(c=r.calcFontSize(c)),(l=r.activeObject)==null||l.set(m,c)}const t=u(o);(d=r.activeObject)==null||d.set("data",t),(f=r.canvas)==null||f.renderAll()},{deep:!0}),(s,t)=>{const l=g,d=F,f=j,m=C,c=O,V=U;return y(),T("div",H,[p("div",K,[a(l,{type:"primary",class:"w-full",size:"large",onClick:z},{default:n(()=>t[6]||(t[6]=[X(" 添加文字 ")])),_:1})]),e(v)?(y(),T("div",L,[a(c,{modelValue:e(_).currentTab,"onUpdate:modelValue":t[5]||(t[5]=i=>e(_).currentTab=i),stretch:""},{default:n(()=>[a(m,{name:"setting"},{label:n(()=>t[7]||(t[7]=[p("span",{class:"el-tab__label-text"},"文字设置",-1)])),default:n(()=>[p("div",M,[a(f,null,{default:n(()=>[p("div",W,[a(d,null,{default:n(()=>[a(B,{font:e(o).fontFamily,"onUpdate:font":t[0]||(t[0]=i=>e(o).fontFamily=i),"font-size":e(o).fontSize,"onUpdate:fontSize":t[1]||(t[1]=i=>e(o).fontSize=i),"font-color":e(o).fill,"onUpdate:fontColor":t[2]||(t[2]=i=>e(o).fill=i),"stroke-color":e(o).stroke,"onUpdate:strokeColor":t[3]||(t[3]=i=>e(o).stroke=i)},null,8,["font","font-size","font-color","stroke-color"])]),_:1})])]),_:1})])]),_:1}),a(m,{name:"special"},{label:n(()=>t[8]||(t[8]=[p("span",{class:"el-tab__label-text"},"文字特效",-1)])),default:n(()=>[p("div",Z,[a(f,null,{default:n(()=>[p("div",$,[a(N,{modelValue:e(o).effect,"onUpdate:modelValue":t[4]||(t[4]=i=>e(o).effect=i)},null,8,["modelValue"])])]),_:1})])]),_:1})]),_:1},8,["modelValue"])])):(y(),T("div",q,[a(V,{image:e(w),description:"请添加文字，或在右侧选中文字～"},null,8,["image"])]))])}}}),Dt=D(G,[["__scopeId","data-v-002da2fb"]]);export{Dt as default};
