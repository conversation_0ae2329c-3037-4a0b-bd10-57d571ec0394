import{_ as b}from"./CN8DIg3d.js";import{j as H,_ as P}from"./B5S_Er7H.js";import I from"./LCS0v4XM.js";import{_ as V}from"./BBrnGDgJ.js";import B from"./CT4XURus.js";import N from"./Dpa-Vh6M.js";import S from"./BwMqSSjB.js";import{l as C,b as f,j as u,M as r,N as k,Z as i,a0 as M,O as l,u as d,a1 as _,n as h}from"./Dp9aCaJ6.js";import"./DDaEm-_F.js";import"./DlAUqK2U.js";import"./DySxZBuW.js";/* empty css        */import"./pttW598Y.js";import"./B_1915px.js";import"./C8ZjkaO0.js";import"./BgJCO7ll.js";import"./DbRYflOL.js";import"./DCTLXrZ8.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./qjB-nkmo.js";import"./mlU_0VDP.js";import"./o_xM7Vbm.js";import"./BNkFx8AD.js";import"./DmkLlkqP.js";import"./ChwnGhEb.js";/* empty css        */const L={class:"flex p-4 h-full"},U={class:"flex-1 min-w-0 h-full ml-4 bg-body rounded-[12px] relative"},j={key:0,class:"h-full flex"},E={class:"h-full flex-1 min-w-0 p-[15px]"},D={class:"border-l-[1px] border-solid border-br-light w-[300px]"},de=C({__name:"index",setup(O){const p=f(""),o=f(!1),n=u(),v=u(),y=H(),c=async()=>{var e;if(!y.isLogin)return y.toggleShowLogin();o.value=!o.value,o.value||(await h(),(e=n.value)==null||e.renderMarkMap(p.value))},m=f(-1),g=async e=>{var t;p.value=e,o.value=!1,await h(),(t=n.value)==null||t.renderMarkMap(e)},w=u(),R=()=>{var e;m.value=-1,(e=w.value)==null||e.refresh()},x=async({id:e,text:t})=>{var s,a;m.value=e,o.value=!1,(s=v.value)==null||s.changDescInput(t),await h(),(a=n.value)==null||a.renderMarkMap(t)};return(e,t)=>{const s=b,a=P;return r(),k("div",null,[i(a,{name:"default"},{default:M(()=>[l("div",L,[i(s,null,{default:M(()=>[i(I,{ref_key:"controlPanelRef",ref:v,onUpdate:g,onHistory:c,onRefresh:R},null,512)]),_:1}),l("div",U,[d(o)?(r(),_(N,{key:1,onView:x,onHistory:c})):(r(),k("div",j,[l("div",E,[d(p).length?(r(),_(V,{key:0,ref_key:"mindMapPreviewRef",ref:n},null,512)):(r(),_(B,{key:1}))]),l("div",D,[i(S,{ref_key:"historyRef",ref:w,currentId:d(m),onView:x,onHistory:c},null,8,["currentId"])])]))])])]),_:1})])}}});export{de as default};
