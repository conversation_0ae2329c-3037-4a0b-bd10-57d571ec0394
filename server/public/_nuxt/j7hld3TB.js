import{U as Q,aI as ue,K,X as m,a4 as J,a5 as ke,M as N,aN as M,J as O,bH as Ee,bI as $e,bJ as Re,T as Ce,aa as Pe,bK as we,O as A,bL as Ue,h as Fe,aR as Le,P as Se}from"./DAgm18qP.js";import{I as de,R as k,l as _,b as ce,m as W,M as h,a1 as S,a0 as C,N as L,_ as _e,aq as Te,X as y,u as t,ai as pe,V as R,a4 as b,O as q,a9 as D,Z as F,a7 as Y,a2 as Oe,a5 as De,i as Be,j as G,aB as x,U as je,c as ee,o as qe,q as Ne,t as Ke,as as te,W as se}from"./Dp9aCaJ6.js";import{E as Me}from"./BVL3rOLc.js";import{c as ae}from"./B4CR6vtE.js";import{i as Ae}from"./CXZvQ2S9.js";const fe=Symbol("uploadContextKey"),He="ElUpload";class Ie extends Error{constructor(l,e,p,u){super(l),this.name="UploadAjaxError",this.status=e,this.method=p,this.url=u}}function oe(o,l,e){let p;return e.response?p=`${e.response.error||e.response}`:e.responseText?p=`${e.responseText}`:p=`fail to ${l.method} ${o} ${e.status}`,new Ie(p,e.status,l.method,o)}function ze(o){const l=o.responseText||o.response;if(!l)return l;try{return JSON.parse(l)}catch{return l}}const Xe=o=>{typeof XMLHttpRequest>"u"&&Q(He,"XMLHttpRequest is undefined");const l=new XMLHttpRequest,e=o.action;l.upload&&l.upload.addEventListener("progress",d=>{const v=d;v.percent=d.total>0?d.loaded/d.total*100:0,o.onProgress(v)});const p=new FormData;if(o.data)for(const[d,v]of Object.entries(o.data))de(v)&&v.length?p.append(d,...v):p.append(d,v);p.append(o.filename,o.file,o.file.name),l.addEventListener("error",()=>{o.onError(oe(e,o,l))}),l.addEventListener("load",()=>{if(l.status<200||l.status>=300)return o.onError(oe(e,o,l));o.onSuccess(ze(l))}),l.open(o.method,e,!0),o.withCredentials&&"withCredentials"in l&&(l.withCredentials=!0);const u=o.headers||{};if(u instanceof Headers)u.forEach((d,v)=>l.setRequestHeader(v,d));else for(const[d,v]of Object.entries(u))ue(v)||l.setRequestHeader(d,String(v));return l.send(p),l},ve=["text","picture","picture-card"];let Ve=1;const Z=()=>Date.now()+Ve++,me=K({action:{type:String,default:"#"},headers:{type:m(Object)},method:{type:String,default:"post"},data:{type:m([Object,Function,Promise]),default:()=>J({})},multiple:{type:Boolean,default:!1},name:{type:String,default:"file"},drag:{type:Boolean,default:!1},withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:{type:String,default:""},fileList:{type:m(Array),default:()=>J([])},autoUpload:{type:Boolean,default:!0},listType:{type:String,values:ve,default:"text"},httpRequest:{type:m(Function),default:Xe},disabled:Boolean,limit:Number}),Je=K({...me,beforeUpload:{type:m(Function),default:k},beforeRemove:{type:m(Function)},onRemove:{type:m(Function),default:k},onChange:{type:m(Function),default:k},onPreview:{type:m(Function),default:k},onSuccess:{type:m(Function),default:k},onProgress:{type:m(Function),default:k},onError:{type:m(Function),default:k},onExceed:{type:m(Function),default:k},crossorigin:{type:m(String)}}),We=K({files:{type:m(Array),default:()=>J([])},disabled:{type:Boolean,default:!1},handlePreview:{type:m(Function),default:k},listType:{type:String,values:ve,default:"text"},crossorigin:{type:m(String)}}),Ge={remove:o=>!!o},Ze=["onKeydown"],Qe=["src","crossorigin"],Ye=["onClick"],xe=["title"],et=["onClick"],tt=["onClick"],st=_({name:"ElUploadList"}),at=_({...st,props:We,emits:Ge,setup(o,{emit:l}){const e=o,{t:p}=ke(),u=N("upload"),d=N("icon"),v=N("list"),E=M(),g=ce(!1),$=W(()=>[u.b("list"),u.bm("list",e.listType),u.is("disabled",e.disabled)]),w=f=>{l("remove",f)};return(f,n)=>(h(),S(De,{tag:"ul",class:y(t($)),name:t(v).b()},{default:C(()=>[(h(!0),L(_e,null,Te(f.files,s=>(h(),L("li",{key:s.uid||s.name,class:y([t(u).be("list","item"),t(u).is(s.status),{focusing:g.value}]),tabindex:"0",onKeydown:pe(i=>!t(E)&&w(s),["delete"]),onFocus:n[0]||(n[0]=i=>g.value=!0),onBlur:n[1]||(n[1]=i=>g.value=!1),onClick:n[2]||(n[2]=i=>g.value=!1)},[R(f.$slots,"default",{file:s},()=>[f.listType==="picture"||s.status!=="uploading"&&f.listType==="picture-card"?(h(),L("img",{key:0,class:y(t(u).be("list","item-thumbnail")),src:s.url,crossorigin:f.crossorigin,alt:""},null,10,Qe)):b("v-if",!0),s.status==="uploading"||f.listType!=="picture-card"?(h(),L("div",{key:1,class:y(t(u).be("list","item-info"))},[q("a",{class:y(t(u).be("list","item-name")),onClick:D(i=>f.handlePreview(s),["prevent"])},[F(t(O),{class:y(t(d).m("document"))},{default:C(()=>[F(t(Ee))]),_:1},8,["class"]),q("span",{class:y(t(u).be("list","item-file-name")),title:s.name},Y(s.name),11,xe)],10,Ye),s.status==="uploading"?(h(),S(t(Me),{key:0,type:f.listType==="picture-card"?"circle":"line","stroke-width":f.listType==="picture-card"?6:2,percentage:Number(s.percentage),style:Oe(f.listType==="picture-card"?"":"margin-top: 0.5rem")},null,8,["type","stroke-width","percentage","style"])):b("v-if",!0)],2)):b("v-if",!0),q("label",{class:y(t(u).be("list","item-status-label"))},[f.listType==="text"?(h(),S(t(O),{key:0,class:y([t(d).m("upload-success"),t(d).m("circle-check")])},{default:C(()=>[F(t($e))]),_:1},8,["class"])):["picture-card","picture"].includes(f.listType)?(h(),S(t(O),{key:1,class:y([t(d).m("upload-success"),t(d).m("check")])},{default:C(()=>[F(t(Re))]),_:1},8,["class"])):b("v-if",!0)],2),t(E)?b("v-if",!0):(h(),S(t(O),{key:2,class:y(t(d).m("close")),onClick:i=>w(s)},{default:C(()=>[F(t(Ce))]),_:2},1032,["class","onClick"])),b(" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn"),b(" This is a bug which needs to be fixed "),b(" TODO: Fix the incorrect navigation interaction "),t(E)?b("v-if",!0):(h(),L("i",{key:3,class:y(t(d).m("close-tip"))},Y(t(p)("el.upload.deleteTip")),3)),f.listType==="picture-card"?(h(),L("span",{key:4,class:y(t(u).be("list","item-actions"))},[q("span",{class:y(t(u).be("list","item-preview")),onClick:i=>f.handlePreview(s)},[F(t(O),{class:y(t(d).m("zoom-in"))},{default:C(()=>[F(t(Pe))]),_:1},8,["class"])],10,et),t(E)?b("v-if",!0):(h(),L("span",{key:0,class:y(t(u).be("list","item-delete")),onClick:i=>w(s)},[F(t(O),{class:y(t(d).m("delete"))},{default:C(()=>[F(t(we))]),_:1},8,["class"])],10,tt))],2)):b("v-if",!0)])],42,Ze))),128)),R(f.$slots,"append")]),_:3},8,["class","name"]))}});var ne=A(at,[["__file","upload-list.vue"]]);const ot=K({disabled:{type:Boolean,default:!1}}),nt={file:o=>de(o)},lt=["onDrop","onDragover"],ye="ElUploadDrag",rt=_({name:ye}),it=_({...rt,props:ot,emits:nt,setup(o,{emit:l}){Be(fe)||Q(ye,"usage: <el-upload><el-upload-dragger /></el-upload>");const p=N("upload"),u=ce(!1),d=M(),v=g=>{if(d.value)return;u.value=!1,g.stopPropagation();const $=Array.from(g.dataTransfer.files);l("file",$)},E=()=>{d.value||(u.value=!0)};return(g,$)=>(h(),L("div",{class:y([t(p).b("dragger"),t(p).is("dragover",u.value)]),onDrop:D(v,["prevent"]),onDragover:D(E,["prevent"]),onDragleave:$[0]||($[0]=D(w=>u.value=!1,["prevent"]))},[R(g.$slots,"default")],42,lt))}});var ut=A(it,[["__file","upload-dragger.vue"]]);const dt=K({...me,beforeUpload:{type:m(Function),default:k},onRemove:{type:m(Function),default:k},onStart:{type:m(Function),default:k},onSuccess:{type:m(Function),default:k},onProgress:{type:m(Function),default:k},onError:{type:m(Function),default:k},onExceed:{type:m(Function),default:k}}),ct=["onKeydown"],pt=["name","multiple","accept"],ft=_({name:"ElUploadContent",inheritAttrs:!1}),vt=_({...ft,props:dt,setup(o,{expose:l}){const e=o,p=N("upload"),u=M(),d=G({}),v=G(),E=a=>{if(a.length===0)return;const{autoUpload:c,limit:r,fileList:P,multiple:U,onStart:B,onExceed:H}=e;if(r&&P.length+a.length>r){H(a,P);return}U||(a=a.slice(0,1));for(const I of a){const j=I;j.uid=Z(),B(j),c&&g(j)}},g=async a=>{if(v.value.value="",!e.beforeUpload)return w(a);let c,r={};try{const U=e.data,B=e.beforeUpload(a);r=x(e.data)?ae(e.data):e.data,c=await B,x(e.data)&&Ae(U,r)&&(r=ae(e.data))}catch{c=!1}if(c===!1){e.onRemove(a);return}let P=a;c instanceof Blob&&(c instanceof File?P=c:P=new File([c],a.name,{type:a.type})),w(Object.assign(P,{uid:a.uid}),r)},$=async(a,c)=>je(a)?a(c):a,w=async(a,c)=>{const{headers:r,data:P,method:U,withCredentials:B,name:H,action:I,onProgress:j,onSuccess:ge,onError:he,httpRequest:be}=e;try{c=await $(c??P,a)}catch{e.onRemove(a);return}const{uid:z}=a,X={headers:r||{},withCredentials:B,file:a,data:c,method:U,filename:H,action:I,onProgress:T=>{j(T,a)},onSuccess:T=>{ge(T,a),delete d.value[z]},onError:T=>{he(T,a),delete d.value[z]}},V=be(X);d.value[z]=V,V instanceof Promise&&V.then(X.onSuccess,X.onError)},f=a=>{const c=a.target.files;c&&E(Array.from(c))},n=()=>{u.value||(v.value.value="",v.value.click())},s=()=>{n()};return l({abort:a=>{Ue(d.value).filter(a?([r])=>String(a.uid)===r:()=>!0).forEach(([r,P])=>{P instanceof XMLHttpRequest&&P.abort(),delete d.value[r]})},upload:g}),(a,c)=>(h(),L("div",{class:y([t(p).b(),t(p).m(a.listType),t(p).is("drag",a.drag)]),tabindex:"0",onClick:n,onKeydown:pe(D(s,["self"]),["enter","space"])},[a.drag?(h(),S(ut,{key:0,disabled:t(u),onFile:E},{default:C(()=>[R(a.$slots,"default")]),_:3},8,["disabled"])):R(a.$slots,"default",{key:1}),q("input",{ref_key:"inputRef",ref:v,class:y(t(p).e("input")),name:a.name,multiple:a.multiple,accept:a.accept,type:"file",onChange:f,onClick:c[0]||(c[0]=D(()=>{},["stop"]))},null,42,pt)],42,ct))}});var le=A(vt,[["__file","upload-content.vue"]]);const re="ElUpload",ie=o=>{var l;(l=o.url)!=null&&l.startsWith("blob:")&&URL.revokeObjectURL(o.url)},mt=(o,l)=>{const e=Fe(o,"fileList",void 0,{passive:!0}),p=n=>e.value.find(s=>s.uid===n.uid);function u(n){var s;(s=l.value)==null||s.abort(n)}function d(n=["ready","uploading","success","fail"]){e.value=e.value.filter(s=>!n.includes(s.status))}const v=(n,s)=>{const i=p(s);i&&(console.error(n),i.status="fail",e.value.splice(e.value.indexOf(i),1),o.onError(n,i,e.value),o.onChange(i,e.value))},E=(n,s)=>{const i=p(s);i&&(o.onProgress(n,i,e.value),i.status="uploading",i.percentage=Math.round(n.percent))},g=(n,s)=>{const i=p(s);i&&(i.status="success",i.response=n,o.onSuccess(n,i,e.value),o.onChange(i,e.value))},$=n=>{ue(n.uid)&&(n.uid=Z());const s={name:n.name,percentage:0,status:"ready",size:n.size,raw:n,uid:n.uid};if(o.listType==="picture-card"||o.listType==="picture")try{s.url=URL.createObjectURL(n)}catch(i){Le(re,i.message),o.onError(i,s,e.value)}e.value=[...e.value,s],o.onChange(s,e.value)},w=async n=>{const s=n instanceof File?p(n):n;s||Q(re,"file to be removed not found");const i=a=>{u(a);const c=e.value;c.splice(c.indexOf(a),1),o.onRemove(a,c),ie(a)};o.beforeRemove?await o.beforeRemove(s,e.value)!==!1&&i(s):i(s)};function f(){e.value.filter(({status:n})=>n==="ready").forEach(({raw:n})=>{var s;return n&&((s=l.value)==null?void 0:s.upload(n))})}return ee(()=>o.listType,n=>{n!=="picture-card"&&n!=="picture"||(e.value=e.value.map(s=>{const{raw:i,url:a}=s;if(!a&&i)try{s.url=URL.createObjectURL(i)}catch(c){o.onError(c,s,e.value)}return s}))}),ee(e,n=>{for(const s of n)s.uid||(s.uid=Z()),s.status||(s.status="success")},{immediate:!0,deep:!0}),{uploadFiles:e,abort:u,clearFiles:d,handleError:v,handleProgress:E,handleStart:$,handleSuccess:g,handleRemove:w,submit:f,revokeFileObjectURL:ie}},yt=_({name:"ElUpload"}),gt=_({...yt,props:Je,setup(o,{expose:l}){const e=o,p=M(),u=G(),{abort:d,submit:v,clearFiles:E,uploadFiles:g,handleStart:$,handleError:w,handleRemove:f,handleSuccess:n,handleProgress:s,revokeFileObjectURL:i}=mt(e,u),a=W(()=>e.listType==="picture-card"),c=W(()=>({...e,fileList:g.value,onStart:$,onProgress:s,onSuccess:n,onError:w,onRemove:f}));return qe(()=>{g.value.forEach(i)}),Ne(fe,{accept:Ke(e,"accept")}),l({abort:d,submit:v,clearFiles:E,handleStart:$,handleRemove:f}),(r,P)=>(h(),L("div",null,[t(a)&&r.showFileList?(h(),S(ne,{key:0,disabled:t(p),"list-type":r.listType,files:t(g),crossorigin:r.crossorigin,"handle-preview":r.onPreview,onRemove:t(f)},te({append:C(()=>[F(le,se({ref_key:"uploadRef",ref:u},t(c)),{default:C(()=>[r.$slots.trigger?R(r.$slots,"trigger",{key:0}):b("v-if",!0),!r.$slots.trigger&&r.$slots.default?R(r.$slots,"default",{key:1}):b("v-if",!0)]),_:3},16)]),_:2},[r.$slots.file?{name:"default",fn:C(({file:U})=>[R(r.$slots,"file",{file:U})])}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):b("v-if",!0),!t(a)||t(a)&&!r.showFileList?(h(),S(le,se({key:1,ref_key:"uploadRef",ref:u},t(c)),{default:C(()=>[r.$slots.trigger?R(r.$slots,"trigger",{key:0}):b("v-if",!0),!r.$slots.trigger&&r.$slots.default?R(r.$slots,"default",{key:1}):b("v-if",!0)]),_:3},16)):b("v-if",!0),r.$slots.trigger?R(r.$slots,"default",{key:2}):b("v-if",!0),R(r.$slots,"tip"),!t(a)&&r.showFileList?(h(),S(ne,{key:3,disabled:t(p),"list-type":r.listType,files:t(g),crossorigin:r.crossorigin,"handle-preview":r.onPreview,onRemove:t(f)},te({_:2},[r.$slots.file?{name:"default",fn:C(({file:U})=>[R(r.$slots,"file",{file:U})])}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):b("v-if",!0)]))}});var ht=A(gt,[["__file","upload.vue"]]);const Ct=Se(ht);export{Ct as E};
