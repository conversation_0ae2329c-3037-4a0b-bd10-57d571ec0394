import{_ as g}from"./uE1Ww79i.js";import{b as M,bw as R,E as T}from"./B1MekKW7.js";import{E as q}from"./DeQZaZZG.js";import{E as F,a as O}from"./E8sU8GGn.js";import{E as P}from"./IgeL0vc_.js";/* empty css        *//* empty css        */import{u as Q}from"./QLtE1ufq.js";import{f as U}from"./B_1915px.js";import{u as X}from"./DFeHsHxy.js";import{l as Z,ak as G,m as C,b as H,M as s,N as o,O as n,_ as i,aq as x,u as c,Z as a,a0 as m,a6 as J,y as K,a4 as w,X as W,a7 as f,a1 as Y}from"./Dp9aCaJ6.js";import{_ as ee}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./DxZY3BF8.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";const te={class:"sample-lists-container sm:px-[10px]"},se={class:"flex sample-lists"},oe={class:"flex justify-center items-center mb-[20px]"},ne=["src"],ae={class:"text-2xl font-medium ml-3 text-tx-primary"},le=["onClick"],re={class:"flex-1 text-center line-clamp-1 text-sm sm:text-base"},ie={class:"flex-none flex items-center"},ce={class:"h-[50vh]"},me=["onClick"],pe={class:"line-clamp-2"},v=3,E=3,ue=Z({__name:"sample-lists",emits:["click-item"],async setup(de,{emit:D}){let y,k;const b=D;M();const B=R(),{data:p,suspense:S}=X(["samplesList"],{queryFn:U,placeholderData:[]});[y,k]=G(()=>Q(()=>S(),{lazy:!0},"$BCxmeyzygt")),await y,k();const h=(l,e)=>l.slice(0,e),V=C(()=>h(p.value,v)),L=C(()=>p.value.reduce((l,e)=>{var d;return l+=((d=e.sample)==null?void 0:d.length)||0,l},0)>v*E||p.value.length>v),u=H(!1);return(l,e)=>{const d=g,N=g,$=T,j=q,z=O,A=F,I=P;return s(),o("div",te,[e[3]||(e[3]=n("div",{class:"sm:my-[60px] my-[30px] text-center text-[30px] font-medium"},null,-1)),n("div",se,[(s(!0),o(i,null,x(c(V),t=>(s(),o("div",{key:t.id,class:W(["flex-1 sm:mx-[10px] mx-[5px] p-[20px] sample-lists-item",{"is-dark":c(B)}])},[n("div",oe,[t.image?(s(),o("img",{key:0,class:"w-[35px] h-[35px]",src:t.image,alt:""},null,8,ne)):w("",!0),n("div",ae,f(t.name),1)]),n("div",null,[(s(!0),o(i,null,x(h(t.sample,E),r=>(s(),o("div",{key:r.id,class:"bg-body sm:mb-[15px] mb-[10px] p-[10px] flex justify-center rounded-[12px] cursor-pointer",onClick:_=>b("click-item",r.content)},[n("div",re,f(r.content),1),n("div",ie,[a(d,{name:"el-icon-Right",color:"inherit",size:"16"})])],8,le))),128))])],2))),128))]),c(L)?(s(),o(i,{key:0},[n("div",{class:"flex justify-center mt-10",onClick:e[0]||(e[0]=t=>u.value=!0)},[a($,{link:""},{default:m(()=>[e[2]||(e[2]=J(" 查看更多 ")),a(N,{name:"el-icon-ArrowRight"})]),_:1})]),a(I,{modelValue:c(u),"onUpdate:modelValue":e[1]||(e[1]=t=>K(u)?u.value=t:null),width:"600px",title:"问题示例",class:"sample-popup"},{default:m(()=>[a(A,{"model-value":0},{default:m(()=>[(s(!0),o(i,null,x(c(p),(t,r)=>(s(),Y(z,{key:t.id,label:t.name,name:r},{default:m(()=>[n("div",ce,[a(j,null,{default:m(()=>[(s(!0),o(i,null,x(t.sample,_=>(s(),o("div",{key:_.id,class:"bg-page mb-[10px] p-[10px] flex justify-center rounded-[2px] cursor-pointer",onClick:_e=>b("click-item",_.content)},[n("div",pe,f(_.content),1)],8,me))),128))]),_:2},1024)])]),_:2},1032,["label","name"]))),128))]),_:1})]),_:1},8,["modelValue"])],64)):w("",!0)])}}}),Ne=ee(ue,[["__scopeId","data-v-aff0f892"]]);export{Ne as default};
