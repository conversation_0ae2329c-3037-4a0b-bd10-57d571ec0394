import{E as g}from"./CBkeCdOF.js";import{E as w}from"./D9I8-w1V.js";import{a as v,_ as B}from"./Br7V4jS9.js";/* empty css        */import{u as E}from"./BQs8y7a2.js";import N from"./CfyIZFVg.js";import{_ as C}from"./Yer8kdKm.js";import{useSearch as S}from"./CAmkCvVs.js";import{e as A}from"./BFDvpBeT.js";import{l as I,ak as R,c as j,M as t,N as r,Z as m,a0 as i,u as e,_ as V,a1 as c,O as Z}from"./Dp9aCaJ6.js";import"./B4zJW-6l.js";import"./B9mz6c9C.js";import"./CCBJtVSv.js";import"./DTIryUWu.js";/* empty css        */import"./CSxLM40j.js";import"./CQG-tpkc.js";import"./DlAUqK2U.js";import"./BtJkLTbD.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./BqjG40R6.js";import"./dsHDlwYU.js";import"./CXUUfEOz.js";import"./9Bti1uB6.js";import"./DgYHmeWQ.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./CTqHNoPX.js";import"./BnEBgps2.js";import"./BLV0QRdm.js";import"./CS0kanFB.js";import"./Bxajobz5.js";import"./Cq2NhlyP.js";import"./V6VzAR3J.js";import"./DBaOsZb5.js";import"./CYgEYzPG.js";import"./CDczoLtO.js";import"./CcPlX2kz.js";import"./Dk_NFeHv.js";import"./PP0YUb69.js";import"./5Hj0hBnW.js";import"./CVBoTnr5.js";import"./DQ7Jjyt0.js";import"./HSqVM7L2.js";import"./BKFgJjkk.js";import"./Cu5sLRaN.js";import"./Cv6HhfEG.js";import"./C5S6gXlf.js";import"./BXXFZfct.js";import"./LFZ9apcG.js";/* empty css        */import"./tqrl0DWq.js";import"./8_8pgzek.js";import"./DzsXYoW8.js";import"./C3sn0ick.js";import"./D9cXQobF.js";import"./aOBsthdE.js";import"./6TEPa7eZ.js";import"./DVAaxSjy.js";import"./Dz5YFaKT.js";/* empty css        */const $={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Kt=I({__name:"index",async setup(q){let p,s;const l=v(),{showSearchResult:a,config:u,getConfig:_,getSearchInfo:f,options:d,result:y}=S();return[p,s]=R(()=>E(()=>_(),"$mZBhG8hzNj")),await p,s(),j(()=>l.query.id,o=>{o?y.value.id<0&&f(o):(d.value.ask="",a.value=!1)},{immediate:!0}),(o,n)=>{const h=g,k=w,x=B;return t(),r("div",null,[m(x,{name:"default"},{default:i(()=>[e(u).status>0?(t(),r(V,{key:0},[e(a)?(t(),c(C,{key:0})):(t(),c(N,{key:1}))],64)):(t(),r("div",$,[m(k,null,{icon:i(()=>[m(h,{class:"w-[150px] dark:opacity-60",src:e(A)},null,8,["src"])]),title:i(()=>n[0]||(n[0]=[Z("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Kt as default};
