import{M as c,O as i,P as b,Q as p}from"./CzTOiozM.js";import{l as n,$ as w,m as h,M as u,N as _,V as d,X as f,u as s,a2 as m}from"./Dp9aCaJ6.js";const k=n({name:"ElContainer"}),B=n({...k,props:{direction:{type:String}},setup(o){const t=o,e=w(),a=c("container"),r=h(()=>t.direction==="vertical"?!0:t.direction==="horizontal"?!1:e&&e.default?e.default().some(v=>{const E=v.type.name;return E==="ElHeader"||E==="ElFooter"}):!1);return(l,v)=>(u(),_("section",{class:f([s(a).b(),s(a).is("vertical",s(r))])},[d(l.$slots,"default")],2))}});var C=i(B,[["__file","container.vue"]]);const M=n({name:"ElAside"}),N=n({...M,props:{width:{type:String,default:null}},setup(o){const t=o,e=c("aside"),a=h(()=>t.width?e.cssVarBlock({width:t.width}):{});return(r,l)=>(u(),_("aside",{class:f(s(e).b()),style:m(s(a))},[d(r.$slots,"default")],6))}});var $=i(N,[["__file","aside.vue"]]);const V=n({name:"ElFooter"}),F=n({...V,props:{height:{type:String,default:null}},setup(o){const t=o,e=c("footer"),a=h(()=>t.height?e.cssVarBlock({height:t.height}):{});return(r,l)=>(u(),_("footer",{class:f(s(e).b()),style:m(s(a))},[d(r.$slots,"default")],6))}});var g=i(F,[["__file","footer.vue"]]);const H=n({name:"ElHeader"}),z=n({...H,props:{height:{type:String,default:null}},setup(o){const t=o,e=c("header"),a=h(()=>t.height?e.cssVarBlock({height:t.height}):{});return(r,l)=>(u(),_("header",{class:f(s(e).b()),style:m(s(a))},[d(r.$slots,"default")],6))}});var y=i(z,[["__file","header.vue"]]);const A=n({name:"ElMain"}),x=n({...A,setup(o){const t=c("main");return(e,a)=>(u(),_("main",{class:f(s(t).b())},[d(e.$slots,"default")],2))}});var S=i(x,[["__file","main.vue"]]);const P=b(C,{Aside:$,Footer:g,Header:y,Main:S}),Q=p($),X=p(g),j=p(y),q=p(S);export{j as E,Q as a,q as b,P as c,X as d};
