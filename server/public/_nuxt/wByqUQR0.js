import{E as d,a as h,b as g,c as w}from"./DXN2M8mh.js";import{j as v,cs as C,a as b,V as y,cM as D,f as E}from"./B1MekKW7.js";import{u as x,a as S}from"./B3ZvK_Z-.js";import{_ as L}from"./2WVsUMjB.js";import k from"./B1wU0-Ya.js";import{_ as B}from"./D3M4eAhL.js";import M from"./DIk3FDVY.js";import{l as R,c as r,M as V,N as $,Z as t,a0 as i,a2 as z,u as H}from"./Dp9aCaJ6.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./DjadXojw.js";import"./D74K85Xd.js";import"./uE1Ww79i.js";import"./DlAUqK2U.js";import"./CQAw0cuz.js";import"./D3aLd1tg.js";import"./Dkrb28GR.js";import"./CPkX1WPy.js";import"./wW1KxDBP.js";import"./t2WiU-SJ.js";import"./DplCVaqu.js";import"./IgeL0vc_.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        *//* empty css        *//* empty css        */import"./CcPlX2kz.js";import"./E8sU8GGn.js";import"./Cv6HhfEG.js";import"./DxZY3BF8.js";import"./LRiAza88.js";import"./BxTfM8xu.js";import"./DeQZaZZG.js";/* empty css        */import"./QLtE1ufq.js";import"./CyaqLv7U.js";import"./CSGlwKPU.js";import"./eoit9ZmK.js";import"./KpUACw60.js";/* empty css        */import"./Lk_w6vIw.js";import"./DqJ8zU9f.js";import"./DO7Qf5R-.js";import"./xK8mIbdw.js";/* empty css        */import"./Db0KaHnm.js";import"./Bwx2-5pK.js";import"./Df0xfART.js";import"./GQTyu-ru.js";import"./DmvaW7ga.js";import"./BMW57zJa.js";import"./AOe4nXt1.js";import"./vclUXml2.js";import"./DlKZEFPo.js";import"./BAyGR4uh.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./BuzGgK0i.js";import"./DFeHsHxy.js";import"./DVLEc6gw.js";import"./BuCnF03T.js";import"./CG7WIFG0.js";import"./C30IsppY.js";import"./ByrMMEHQ.js";import"./Ck8UAkQ_.js";import"./nMGYJSn9.js";import"./CAGX2SUz.js";import"./D8VuEnxr.js";import"./l0sNRNKZ.js";import"./ClCAAZSV.js";import"./XBgbYT1J.js";import"./DTtBEUup.js";import"./CIrs9wkW.js";/* empty css        */import"./CvzS_dvH.js";const ao=R({__name:"design",setup(N){const n=v(),o=x(),{height:c}=C(),u=b(),{initTabs:l}=S();y(window,"beforeunload",m=>{o.isChangeData&&(m.preventDefault(),m.returnValue="内容已修改，确认离开页面吗？")}),D(async(m,f)=>{try{o.isChangeData&&n.isLogin&&await E.confirm("内容已修改，确认离开页面吗？")}catch{return!1}});const e=()=>{o.isChangeData=!0};return r(()=>o.canvasJson,e),r(()=>o.music,e),r(()=>o.dub,e),r(()=>o.voiceContent,e,{deep:!0}),r(()=>u.query,()=>{l()}),(m,f)=>{const p=d,a=h,_=g,s=w;return V(),$("div",{class:"overflow-x-auto",style:z({height:`${H(c)}px`})},[t(s,{class:"bg-page !min-w-[1200px] h-full"},{default:i(()=>[t(p,{height:"auto",style:{padding:"0"}},{default:i(()=>[t(L)]),_:1}),t(s,{class:"min-h-0"},{default:i(()=>[t(a,{width:"auto",style:{overflow:"visible"}},{default:i(()=>[t(k)]),_:1}),t(_,{style:{padding:"0"}},{default:i(()=>[t(B)]),_:1}),t(a,{width:"auto"},{default:i(()=>[t(M)]),_:1})]),_:1})]),_:1})],4)}}});export{ao as default};
