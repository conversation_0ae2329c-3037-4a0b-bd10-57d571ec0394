import{E as x}from"./DlvGt6PY.js";import{b as g}from"./BsMqt_su.js";/* empty css        */import{u as h}from"./Dwj2_tQp.js";import{_ as y}from"./CLSG_Eto.js";import k from"./D9gB6tbt.js";import{l as b,m as u,M as s,N as v,Z as o,a0 as a,O as t,a1 as n,a4 as c,a7 as w,u as e,_ as B}from"./Dp9aCaJ6.js";const E={class:"w-[54px] py-[8px] my-[10px] bg-body shadow-light rounded-lg cursor-pointer"},N={class:"flex flex-col items-center justify-center"},O={class:"text-xs"},C={class:"absolute right-[10px] top-[60%] z-[9999]"},F=b({__name:"index",setup(M){const r=g(),[_,l]=h(),i=u(()=>r.getOnlineKf),m=u(()=>r.getManualKf);return(S,T)=>{const d=x;return s(),v(B,null,[o(e(_),null,{default:a(({icon:p,text:f})=>[t("div",E,[t("div",N,[p?(s(),n(d,{key:0,class:"w-[30px] h-[30px] mb-[4px]",src:p},null,8,["src"])):c("",!0),t("div",O,w(f),1)])])]),_:1}),t("div",null,[t("div",C,[e(m).status==1?(s(),n(y,{key:0},{default:a(()=>[o(e(l),{icon:e(m).icons,text:"客服"},null,8,["icon"])]),_:1})):c("",!0),e(i).status==1?(s(),n(k,{key:1},{default:a(()=>[o(e(l),{icon:e(i).icons,text:"在线客服"},null,8,["icon"])]),_:1})):c("",!0)])])],64)}}});export{F as _};
