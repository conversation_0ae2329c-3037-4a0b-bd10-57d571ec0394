import{bw as ft,l as _t,f as st,bA as gt,E as bt,v as vt}from"./BsMqt_su.js";import{a as yt,E as wt}from"./DwdGJTJg.js";import{E as kt}from"./DlvGt6PY.js";import{E as Et,a as ht}from"./f1oA63J4.js";import{_ as Pt}from"./mtkgvlCx.js";import{E as Ct}from"./Cpwj6wt2.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{u as Ft}from"./67xbGseh.js";import{g as Rt,d as At}from"./CH-eeB8d.js";import Bt from"./BVGr7pZY.js";import{_ as Dt}from"./GFMnBAKT.js";import{_ as Vt}from"./DvvnuMMs.js";import{_ as $t}from"./sdkJGT_c.js";import jt from"./D4hlNMXW.js";import{l as Tt,b as _,j as g,r as Lt,F as zt,M as n,N as d,Z as s,a0 as i,u as o,O as t,a4 as m,a7 as a,a2 as b,a6 as x,aa as Nt,a1 as v,y as Ut,n as F}from"./Dp9aCaJ6.js";import"./Cv6HhfEG.js";import"./Ck3hJ2J3.js";import"./Bhzc5F_3.js";import"./DugIyZO8.js";import"./D05sCSvx.js";import"./D1YDx4Yr.js";import"./DCTLXrZ8.js";import"./BcmWjB0N.js";import"./BATbmIFb.js";import"./CpvOqAuN.js";import"./BPgEaOor.js";import"./CTbdiA5k.js";import"./Bz4jch3v.js";import"./Dbma4Whm.js";/* empty css        */import"./DBRSLWAE.js";import"./DlAUqK2U.js";import"./CHWQlbQz.js";import"./9Bti1uB6.js";/* empty css        */import"./DdgDjo0O.js";import"./Cw-YHvx4.js";import"./Bg2e09vA.js";import"./CcJ-n_gx.js";import"./DlKZEFPo.js";import"./DP2rzg_V.js";/* empty css        */import"./DBaujZmN.js";import"./DIsaPqhV.js";import"./DhvjYsFQ.js";import"./BEVfyfzl.js";import"./Boo8ogMh.js";const Wt=""+new URL("distribution_apply_bg.CkYfuHoF.png",import.meta.url).href,It=""+new URL("distribution_url_bg.BVazyjv5.png",import.meta.url).href,St=""+new URL("distribution_poster_bg.OxyEI-6k.png",import.meta.url).href,Mt={class:"flex flex-col min-h-0 h-full"},Ot={key:0,class:"w-full flex-1"},Ht={key:1,class:"h-full w-full"},Yt={key:0,class:"grid grid-cols-2 gap-x-[20px]"},Zt={class:"bg-page rounded-[12px] px-[30px] py-[25px]"},qt={class:"mt-[23px] flex"},Gt={key:0,class:"flex-1 bg-[#EDF6FE] p-[25px] rounded-[10px]"},Jt={class:"text-xl text-primary"},Kt={class:"text-base mt-[12px] text-[#9E9E9E]"},Qt={class:"flex flex-col min-h-0 bg-page rounded-[12px] px-[30px] py-[25px]"},Xt={key:1},te={class:"grid grid-cols-2 xl:grid-cols-4 gap-x-[20px] gap-y-[20px]"},ee={class:"bg-page rounded-[12px] px-[30px] py-[25px]"},oe={class:"flex justify-between items-center"},se={class:"text-primary text-[30px] font-medium"},le={class:"bg-page rounded-[12px] px-[30px] py-[25px]"},ie={class:"flex justify-between items-center"},ne={class:"text-primary text-[30px] font-medium"},ae={class:"bg-page rounded-[12px] px-[30px] py-[25px]"},pe={class:"flex justify-between items-center"},re={class:"text-primary text-[30px] font-medium"},de={class:"bg-page rounded-[12px] px-[30px] py-[25px]"},me={class:"flex justify-between items-center"},xe={class:"text-primary text-[30px] font-medium"},ue={class:"grid grid-cols-2 gap-x-[20px] mt-4"},ce={class:"w-full bg-page rounded-[12px] px-[30px] py-[25px]"},fe={class:"mt-[23px] flex"},_e={key:0,class:"flex-1 bg-[#EDF6FE] p-[25px] rounded-[10px]"},ge={class:"text-xl text-primary"},be={class:"text-base mt-[12px] text-[#9E9E9E]"},ve={class:"flex flex-col min-h-0 w-full bg-page rounded-[12px] px-[30px] py-[25px]"},ye={class:"grid grid-cols-2 gap-x-[20px] h-full"},we={class:"line-clamp-2 text-[#9E9E9E] mt-[12px]",style:{"word-break":"break-all"}},ke={class:"absolute top-[15px] right-[15px]"},Ee={class:"absolute top-[15px] right-[15px]"},he={class:"bg-page rounded-[12px] px-[30px] py-[25px] mt-4"},Pe={class:"w-full mt-1 bg-white dark:bg-page pb-[20px]"},Ce={class:"flex items-center"},Fe={class:"ml-2"},Re={class:"flex justify-end mt-5 mr-4"},Vo=Tt({__name:"distribution",setup(Ae){const lt=ft();_t();const y=_(!1),w=_(!1),k=_(!1),R=g(),A=g(),B=g(),D=g(),E=Lt({level:""}),{pager:r,getLists:f,resetPage:Be,resetParams:De}=Ft({fetchFun:Rt,params:E}),l=_(),V=async()=>{l.value=await At()},it=async()=>{w.value=!0,await F(),A.value.open()},nt=async()=>{var P;if(((P=l.value.apply_detail)==null?void 0:P.status)==1){st.msgWarning("正在审核中！");return}y.value=!0,await F(),R.value.open()},at=async()=>{if(l.value.withdraw_config.type.length==0){st.alertWarning("后台未设置提现方式！");return}k.value=!0,await F(),D.value.open()},pt=()=>{gt(l.value.config.pc_promotion_url)},h=()=>{f(),V(),y.value=!1,w.value=!1,k.value=!1};return zt(()=>{f(),V()}),(P,e)=>{const u=bt,C=yt,rt=wt,dt=kt,c=Et,mt=ht,xt=Pt,ut=Ct,ct=vt;return n(),d("div",Mt,[s(ut,{class:"bg-white dark:bg-body rounded-[12px] p-[20px]"},{default:i(()=>{var $,j,T,L,z,N,U,W,I,S,M,O,H,Y,Z,q,G,J,K,Q,X,tt,et,ot;return[(($=o(l))==null?void 0:$.is_open)!=1?(n(),d("div",Ot,e[3]||(e[3]=[t("div",{class:"text-xl"},"推广功能未开启!",-1)]))):m("",!0),((j=o(l))==null?void 0:j.is_open)==1?(n(),d("div",Ht,[(L=(T=o(l))==null?void 0:T.user)!=null&&L.is_distribution?(n(),d("div",Xt,[t("div",te,[t("div",ee,[e[9]||(e[9]=t("div",{class:"font-medium text-xl pt-[20px] pb-[15px]"},"可提现余额（元）",-1)),t("div",oe,[t("div",se,a((M=(S=o(l))==null?void 0:S.user)==null?void 0:M.user_money),1),s(u,{type:"primary",class:"!border-none",style:{"--el-button-bg-color":"#4A92FF"},onClick:at},{default:i(()=>e[8]||(e[8]=[x(" 去提现 ")])),_:1})])]),t("div",le,[e[10]||(e[10]=t("div",{class:"font-medium text-xl pt-[20px] pb-[15px]"},"今日收益（元）",-1)),t("div",ie,[t("div",ne,a((H=(O=o(l))==null?void 0:O.user)==null?void 0:H.today_money),1)])]),t("div",ae,[e[12]||(e[12]=t("div",{class:"font-medium text-xl pt-[20px] pb-[15px]"},"已提现（元）",-1)),t("div",pe,[t("div",re,a((Z=(Y=o(l))==null?void 0:Y.user)==null?void 0:Z.withdrawn_money),1),s(u,{type:"warning",class:"!border-none",style:{"--el-button-bg-color":"#FFA64C"},onClick:e[0]||(e[0]=p=>o(B).open())},{default:i(()=>e[11]||(e[11]=[x(" 提现记录 ")])),_:1})])]),t("div",de,[e[14]||(e[14]=t("div",{class:"font-medium text-xl pt-[20px] pb-[15px]"},"累计收益（元）",-1)),t("div",me,[t("div",xe,a((G=(q=o(l))==null?void 0:q.user)==null?void 0:G.total_user_money),1),s(u,{type:"warning",class:"!border-none",style:{"--el-button-bg-color":"#F5BE0A"},onClick:it},{default:i(()=>e[13]||(e[13]=[x(" 收益明细 ")])),_:1})])])]),t("div",ue,[t("div",ce,[e[15]||(e[15]=t("div",{class:"font-medium text-xl"},"推广返现规则",-1)),t("div",fe,[((J=o(l))==null?void 0:J.config.level)>=1?(n(),d("div",_e,[t("div",ge," 高级合伙人（返利"+a((Q=(K=o(l))==null?void 0:K.config)==null?void 0:Q.first_ratio)+"%） ",1),t("div",be," 成为收益合伙人，受邀注册的用户消费您获得 "+a((tt=(X=o(l))==null?void 0:X.config)==null?void 0:tt.first_ratio)+"%奖励 ",1)])):m("",!0)])]),t("div",ve,[e[21]||(e[21]=t("div",{class:"font-medium text-xl"},"推广方式",-1)),t("div",ye,[t("div",{class:"flex flex-col mt-[23px] p-[25px] rounded-[10px] bg-cover relative",style:b({"background-image":`url(${o(It)})`})},[e[17]||(e[17]=t("div",{class:"text-xl text-[#333333]"},"邀请链接",-1)),t("div",we,a((ot=(et=o(l))==null?void 0:et.config)==null?void 0:ot.pc_promotion_url),1),t("div",ke,[s(u,{type:"primary",size:"small",class:"!border-none",style:{"--el-button-bg-color":"#4A92FF"},onClick:pt},{default:i(()=>e[16]||(e[16]=[x(" 复制 ")])),_:1})])],4),t("div",{class:"flex flex-col mt-[23px] p-[25px] rounded-[10px] bg-cover relative",style:b({"background-image":`url(${o(St)})`})},[e[19]||(e[19]=t("div",{class:"text-xl text-[#333333]"},"分享海报",-1)),e[20]||(e[20]=t("div",{class:"text-[#9E9E9E] mt-[12px]"}," 扫描二维码，一起加入吧！ ",-1)),t("div",Ee,[s(Bt,{class:"inline-block"},{trigger:i(()=>[s(u,{type:"primary",size:"small",class:"!border-none",style:{"--el-button-bg-color":"#4A92FF"}},{default:i(()=>e[18]||(e[18]=[x(" 分享 ")])),_:1})]),_:1})])],4)])])]),t("div",he,[e[22]||(e[22]=t("div",{class:"font-medium text-xl"}," 邀请列表 ",-1)),s(rt,{modelValue:o(E).level,"onUpdate:modelValue":e[1]||(e[1]=p=>o(E).level=p),class:"mt-2",onTabChange:o(f)},{default:i(()=>[s(C,{label:`全部(${o(r).extend.all})`,name:""},null,8,["label"]),s(C,{label:`直接用户(${o(r).extend.first})`,name:1},null,8,["label"]),s(C,{label:`间接用户(${o(r).extend.second})`,name:2},null,8,["label"])]),_:1},8,["modelValue","onTabChange"]),t("div",Pe,[Nt((n(),v(mt,{size:"large",data:o(r).lists,style:b({"--el-table-header-bg-color":o(lt)?"#000":"#EFEFEF"})},{default:i(()=>[s(c,{label:"用户昵称"},{default:i(({row:p})=>[t("div",Ce,[s(dt,{class:"w-[48px] h-[48px]",src:p.avatar},null,8,["src"]),t("div",Fe,a(p.nickname),1)])]),_:1}),s(c,{label:"订单量",prop:"order_num"},{default:i(({row:p})=>[x(a(p.order_num?p.order_num:"0"),1)]),_:1}),s(c,{label:"累计消费",prop:"total_amount"}),s(c,{label:"邀请人数",prop:"invite_num"}),s(c,{label:"推广返利资格",prop:"is_distribution_desc"}),s(c,{label:"注册时间",prop:"create_time"})]),_:1},8,["data","style"])),[[ct,o(r).loading]]),t("div",Re,[s(xt,{modelValue:o(r),"onUpdate:modelValue":e[2]||(e[2]=p=>Ut(r)?r.value=p:null),onChange:o(f)},null,8,["modelValue","onChange"])])])])])):(n(),d("div",Yt,[t("div",Zt,[e[4]||(e[4]=t("div",{class:"font-medium text-xl"},"推广返现规则",-1)),t("div",qt,[((z=o(l))==null?void 0:z.config.level)>=1?(n(),d("div",Gt,[t("div",Jt," 高级合伙人（返利"+a((U=(N=o(l))==null?void 0:N.config)==null?void 0:U.first_ratio)+"%） ",1),t("div",Kt," 成为收益合伙人，受邀注册的用户消费您获得 "+a((I=(W=o(l))==null?void 0:W.config)==null?void 0:I.first_ratio)+"%奖励 ",1)])):m("",!0)])]),t("div",Qt,[e[7]||(e[7]=t("div",{class:"font-medium text-xl"},"申请推广返现",-1)),t("div",{class:"flex justify-between items-center h-full mt-[23px] p-[25px] rounded-[10px] bg-cover",style:b({"background-image":`url(${o(Wt)})`})},[e[6]||(e[6]=t("div",{class:"text-black"},"您当前还不是收益合伙人，请申请成为收益合伙人",-1)),s(u,{type:"primary",onClick:nt},{default:i(()=>e[5]||(e[5]=[x("申请成为收益合伙人")])),_:1})],4)])])),o(y)?(n(),v(Dt,{key:2,ref_key:"applyPopRef",ref:R,onClosePop:h},null,512)):m("",!0),o(k)?(n(),v(jt,{key:3,ref_key:"withdrawApplyPopRef",ref:D,onClosePop:h},null,512)):m("",!0),o(w)?(n(),v(Vt,{key:4,ref_key:"incomeDetailPopRef",ref:A,onClosePop:h},null,512)):m("",!0),s($t,{ref_key:"withdrawRecordPopRef",ref:B},null,512)])):m("",!0)]}),_:1})])}}});export{Vo as default};
