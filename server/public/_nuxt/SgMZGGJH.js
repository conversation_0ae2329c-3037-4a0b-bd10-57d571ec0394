import{cn as R,d as F,e as P,v as W}from"./B5S_Er7H.js";import{_ as Y}from"./C8ZjkaO0.js";import{E as D}from"./DbRYflOL.js";import"./l0sNRNKZ.js";/* empty css        */import{j as K,b as c,a as C,l as G}from"./Dlvoorzj.js";import{_ as U}from"./DhEhpSjL.js";import S from"./B9V0JL63.js";import{l as X,b as i,F as L,n as O,M as A,N as u,Z as s,aJ as j,aa as M,u as o,y as I,O as t,a0 as B,a4 as Z,a7 as _}from"./Dp9aCaJ6.js";import{_ as J}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";import"./Dv--ZGyq.js";import"./BgJCO7ll.js";import"./DySxZBuW.js";import"./9Bti1uB6.js";/* empty css        */import"./Cx3Arr0P.js";import"./CacOB3dg.js";import"./GM0TYtnE.js";import"./Tox-HrLw.js";import"./qICSvFaq.js";import"./CqoCrSl4.js";/* empty css        *//* empty css        *//* empty css        */const q="data:image/png;base64,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",T={class:"prompt_container"},H={"element-loading-text":"正在翻译",class:"bg-[var(--el-bg-color-page)] rounded-[12px]"},z={class:"flex items-center p-3 pb-2"},$={class:"flex items-center text-sm text-[#798696] mt-2"},ee=X({__name:"prompt",props:{modelValue:{default:""},model:{default:""}},emits:["update:modelValue"],setup(w,{emit:b}){const E=b,d=w,{modelValue:a}=R(d,E),m=i(!1),v=i(""),r=i(0),l=i([]),Q=()=>{c()||(a.value="")},V=()=>{l.value.length>0&&(r.value<l.value.length-1?r.value++:r.value=0)},k=()=>{if(!c()&&C.value.translate_switch==1){if(!a.value)return F.warning("请输入描述词");m.value=!0,G({prompt:a.value,model:d.model}).then(x=>{a.value=x.result}).finally(()=>{m.value=!1})}};return L(async()=>{await O(),l.value=await K({model:d.model})}),(x,e)=>{var g;const N=P,p=Y,y=D,h=W;return A(),u("div",T,[s(U,{title:"描述词",required:"",tips:"输入你希望AI绘制的内容，无需完整的句子，只需要关键的提示词即可，但必须为英文。例如：1dog,black hair"}),e[8]||(e[8]=j('<div data-v-d1c71eed><div class="text-tx-secondary text-xs mb-2" data-v-d1c71eed> 参考下方示例输入描述 </div><div class="tag-container" data-v-d1c71eed><div class="tag" data-v-d1c71eed><span class="mr-1" data-v-d1c71eed>主体物</span><span class="text-tx-primary" data-v-d1c71eed>上学的女孩</span></div><span class="text-primary-light-7" data-v-d1c71eed>+</span><div class="tag" data-v-d1c71eed><span class="mr-1" data-v-d1c71eed>细节</span><span class="text-tx-primary" data-v-d1c71eed>开心</span></div><span class="text-primary-light-7" data-v-d1c71eed>+</span><div class="tag" data-v-d1c71eed><span class="mr-1" data-v-d1c71eed>特征词</span><span class="text-tx-primary" data-v-d1c71eed>精致五官</span></div></div></div>',1)),M((A(),u("div",H,[s(N,{modelValue:o(a),"onUpdate:modelValue":e[0]||(e[0]=n=>I(a)?a.value=n:null),rows:4,"input-style":{boxShadow:"unset",backgroundColor:"transparent"},resize:"none",type:"textarea",placeholder:"请输入正向提示词",onFocus:o(c)},null,8,["modelValue","onFocus"]),t("div",z,[s(S,{modelValue:o(a),"onUpdate:modelValue":e[1]||(e[1]=n=>I(a)?a.value=n:null)},{trigger:B(()=>e[4]||(e[4]=[t("div",{class:"flex items-center cursor-pointer text-[#6F7E8E] text-sm hover:text-primary"},[t("img",{class:"w-[13px] h-[13px] align-middle",src:q}),t("span",{class:"ml-[4px]"},"描述词推荐")],-1)])),_:1},8,["modelValue"]),o(C).translate_switch==1?(A(),u("div",{key:0,class:"flex items-center cursor-pointer text-[#6F7E8E] text-sm ml-2 hover:text-primary",onClick:k},[s(p,{name:"el-icon-Refresh"}),e[5]||(e[5]=t("span",{class:"ml-[4px]"},"翻译成英文",-1))])):Z("",!0),t("div",{class:"flex items-center cursor-pointer text-primary text-sm ml-auto",onClick:Q},[s(p,{name:"el-icon-Delete"}),e[6]||(e[6]=t("span",{class:"ml-[4px]"},"清空",-1))])])])),[[h,o(m)]]),t("div",$,[e[7]||(e[7]=t("div",{class:"flex items-center gap-1 flex-nowrap"},[t("span",{class:"whitespace-nowrap"},"随便试试：")],-1)),t("div",{class:"flex-[1_0_0] truncate text-[#333333] dark:text-white cursor-pointer hover:text-primary",onClick:e[2]||(e[2]=n=>{var f;return a.value=((f=o(l)[o(r)])==null?void 0:f.prompt_en)||o(v)})},_(((g=o(l)[o(r)])==null?void 0:g.prompt)||o(v)),1),s(y,{effect:"dark",content:"刷新",placement:"bottom"},{default:B(()=>[t("div",{onClick:e[3]||(e[3]=n=>V())},[s(p,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] p-1 box-content",name:"el-icon-RefreshRight",size:"18",color:"#556477"})])]),_:1})])])}}}),ye=J(ee,[["__scopeId","data-v-d1c71eed"]]);export{ye as default};
