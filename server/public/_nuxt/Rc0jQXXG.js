import{_ as m}from"./CNKTogib.js";import{_ as p}from"./C9gRmyZn.js";import{b as s}from"./BsMqt_su.js";import{l as a,M as l,N as n,Z as t,X as c,u as r,O as i,V as _}from"./Dp9aCaJ6.js";import{_ as d}from"./DlAUqK2U.js";import"./CcJ-n_gx.js";import"./CN8DIg3d.js";import"./DBRSLWAE.js";import"./CJVFds9B.js";/* empty css        */import"./Dw9lwl3q.js";import"./7IPfpED4.js";import"./BjwsZjjh.js";import"./D1YDx4Yr.js";import"./DCTLXrZ8.js";import"./CieRD4VA.js";import"./Cpwj6wt2.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./DdgDjo0O.js";import"./CFea67Vu.js";import"./Krgy79Z_.js";import"./DVrqmBF9.js";import"./DlvGt6PY.js";import"./Bhzc5F_3.js";import"./DugIyZO8.js";import"./D05sCSvx.js";/* empty css        */import"./D714yjg9.js";import"./BE7GAo-z.js";import"./BVa8SGAE.js";import"./Ccj3Gk7F.js";import"./CHWQlbQz.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";const f={class:"layout-header h-full flex items-center"},u={class:"flex-1 min-w-0"},g={class:""},h=a({__name:"index",setup(x){const o=s();return(e,v)=>(l(),n("div",f,[t(m,{class:c("mr-[50px]"),logo:r(o).getWebsiteConfig.pc_logo,title:r(o).getWebsiteConfig.pc_name},null,8,["logo","title"]),i("div",u,[i("div",g,[_(e.$slots,"default",{},void 0,!0)])]),t(p,{class:"ml-auto"})]))}}),io=d(h,[["__scopeId","data-v-a4fd1a58"]]);export{io as default};
