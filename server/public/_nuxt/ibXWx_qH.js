import{E as l}from"./DlvGt6PY.js";import{j as p,E as i}from"./BsMqt_su.js";/* empty css        */import{l as c,M as m,N as u,Z as e,u as _,O as f,a0 as g,a6 as x}from"./Dp9aCaJ6.js";const d=""+new URL("noAuth.iRqApgVd.png",import.meta.url).href,h={class:"flex flex-col justify-center items-center h-[60vh]"},V=c({__name:"tologin",setup(E){const{toggleShowLogin:o,setLoginPopupType:s}=p(),n=()=>{s(),o()};return(B,t)=>{const r=l,a=i;return m(),u("div",h,[e(r,{class:"w-[200px] h-[200px]",src:_(d)},null,8,["src"]),t[1]||(t[1]=f("div",{class:"text-tx-regular mb-4"},"暂无查看权限，请登录账号后查看",-1)),e(a,{type:"primary",onClick:n},{default:g(()=>t[0]||(t[0]=[x(" 点击登录 ")])),_:1})])}}});export{V as _};
