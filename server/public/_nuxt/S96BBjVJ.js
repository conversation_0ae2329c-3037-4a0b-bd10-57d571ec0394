import{E as y}from"./HT4ANvwR.js";import{h as E,o as w,E as C}from"./CylNgAGi.js";import{_ as D}from"./a_MrTnou.js";import{_ as N}from"./4QYjDMhx.js";import{E as B}from"./fZOypmWP.js";import"./DP2rzg_V.js";/* empty css        */import{u as L}from"./Du5U7mLs.js";import{c as I}from"./C6_W4ts7.js";import{l as S,M as s,N as l,Z as o,a0 as n,O as d,u as a,_ as m,a6 as c,aq as F,a4 as M,X as O,a7 as $}from"./Dp9aCaJ6.js";const j={class:"pt-[10px]"},q={class:"px-[16px]"},z={class:"flex flex-wrap"},P=["onClick"],T={class:"line-clamp-2 ml-[15px]"},et=S({__name:"digital-config",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:u}){const t=E(_,"modelValue",u),{optionsData:x,refresh:f}=L({digitalLists:{api:I,params:{page_type:0},transformData(r){return r.lists||[]}}}),g=r=>{if(t.value.digital_id==r){t.value.digital_id="";return}t.value.digital_id=r};return(r,i)=>{const v=y,p=w,b=C,h=D,k=N,V=B;return s(),l("div",j,[o(p,{label:"启用形象",prop:"is_digital"},{default:n(()=>[d("div",null,[o(v,{modelValue:a(t).is_digital,"onUpdate:modelValue":i[0]||(i[0]=e=>a(t).is_digital=e),"inline-prompt":"","active-value":1,"inactive-value":0,"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])])]),_:1}),a(t).is_digital?(s(),l(m,{key:0},[o(p,{label:"选择形象",prop:"digital_id"},{default:n(()=>[o(b,{type:"primary",link:"",onClick:a(f)},{default:n(()=>i[1]||(i[1]=[c("刷新")])),_:1},8,["onClick"])]),_:1}),d("div",q,[d("div",z,[o(k,{to:"/application/digital/edit",target:"_blank",class:"flex items-center justify-center p-[15px] border border-br-light border-solid w-[260px] rounded-[10px] h-[80px] mx-[7.5px] mb-[10px]"},{default:n(()=>[o(h,{name:"el-icon-Plus"}),i[2]||(i[2]=c(" 新增形象 "))]),_:1}),(s(!0),l(m,null,F(a(x).digitalLists,e=>(s(),l("div",{class:O(["flex items-center p-[15px] border border-br-light border-solid w-[260px] rounded-[10px] mx-[7.5px] cursor-pointer mb-[15px] h-[80px]",{"!text-primary border-primary bg-primary-light-9":a(t).digital_id==e.id}]),key:e.id,onClick:Z=>g(e.id)},[o(V,{class:"w-[50px] h-[50px] rounded-[50%] overflow-hidden border border-solid border-white flex-none",fit:"cover",src:e.avatar},null,8,["src"]),d("div",T,$(e.name),1)],10,P))),128))])])],64)):M("",!0)])}}});export{et as _};
