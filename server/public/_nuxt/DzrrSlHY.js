import{_ as d}from"./4uO2RExZ.js";import{_}from"./4QAgeNil.js";import{a as c,_ as u}from"./B1MekKW7.js";import{u as f}from"./BVYqt77i.js";import{l as x,m as h,M as C,N as b,Z as i,a0 as v,O as e,u as o}from"./Dp9aCaJ6.js";import"./DeQZaZZG.js";import"./uE1Ww79i.js";import"./DlAUqK2U.js";/* empty css        */import"./DplCVaqu.js";import"./B2PttNjr.js";import"./DnwDmPQp.js";import"./GQTyu-ru.js";/* empty css        */import"./Dkrb28GR.js";import"./CPkX1WPy.js";import"./wW1KxDBP.js";import"./t2WiU-SJ.js";import"./DM16LU_i.js";/* empty css        */import"./F_wAMeMO.js";import"./DjadXojw.js";import"./Cpg3PDWZ.js";import"./IgeL0vc_.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./QnuaST3W.js";import"./Bb2-23m7.js";import"./BjPc4uj5.js";import"./DwFObZc_.js";import"./B_1915px.js";import"./BB8haoms.js";import"./Bg2e09vA.js";import"./D2beQuxj.js";import"./DTtBEUup.js";import"./CIrs9wkW.js";import"./DVLEc6gw.js";import"./DmvaW7ga.js";import"./siQy5HJc.js";import"./1nb9e5kE.js";import"./DT_rtNMg.js";import"./DDaEm-_F.js";import"./QLtE1ufq.js";import"./BxRaFxlt.js";import"./Bo3PTL3c.js";import"./97m9rtmw.js";import"./DFZPzZRS.js";import"./Bz0w0rwk.js";const I={class:"h-full flex"},S={class:"flex h-full p-[16px]"},V={class:"h-full pr-[16px] py-[16px] flex-1 min-w-0"},k={class:"h-full flex flex-col bg-body rounded-lg"},Vo=x({__name:"chat",setup(y){const t=f(),m=c(),s=h(()=>m.query.id);return(E,r)=>{const p=d,n=_,l=u;return C(),b("div",null,[i(l,{name:"default"},{default:v(()=>[e("div",I,[e("div",S,[i(p,{modelValue:o(t).sessionId,"onUpdate:modelValue":r[0]||(r[0]=a=>o(t).sessionId=a),data:o(t).sessionLists,onAdd:o(t).sessionAdd,onEdit:o(t).sessionEdit,onDelete:o(t).sessionDelete,onClear:o(t).sessionClear,onClickItem:o(t).setSessionSelect},null,8,["modelValue","data","onAdd","onEdit","onDelete","onClear","onClickItem"])]),e("div",V,[e("div",k,[i(n,{"robot-id":o(s)},null,8,["robot-id"])])])])]),_:1})])}}});export{Vo as default};
