import{e as i}from"./CylNgAGi.js";import{l as s,M as l,N as a,O as d,Z as r,u as e,ai as f}from"./Dp9aCaJ6.js";import{_ as u}from"./O4OBitKz.js";import{_}from"./DZCjMkJY.js";import{useSearch as c}from"./DLNXsxqB.js";import{_ as x}from"./DlAUqK2U.js";import"./a_MrTnou.js";import"./DJ1U04Bw.js";import"./DCTLXrZ8.js";import"./dafiBzu8.js";import"./CDBp2FXR.js";import"./Bdk6a5L7.js";import"./dYUcznge.js";import"./9Bti1uB6.js";import"./CBuTaJhf.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./Bsadk78l.js";import"./CIBn2O6T.js";import"./BLV0QRdm.js";import"./Cq2NhlyP.js";import"./B0brwBYu.js";const y={class:"bg-page overflow-hidden flex items-center input-select"},v={class:"flex-none flex px-[8px]"},k=s({__name:"input-select",setup(V){const{options:p,launchSearch:m}=c();return(w,o)=>{const n=i;return l(),a("div",y,[d("div",v,[r(u,{mode:"dropdown",model:e(p).model,"onUpdate:model":o[0]||(o[0]=t=>e(p).model=t),type:e(p).type,"onUpdate:type":o[1]||(o[1]=t=>e(p).type=t)},null,8,["model","type"])]),r(n,{modelValue:e(p).ask,"onUpdate:modelValue":o[2]||(o[2]=t=>e(p).ask=t),placeholder:"输入你想搜索的问题",onKeydown:o[3]||(o[3]=f(t=>e(m)(),["enter"]))},null,8,["modelValue"]),r(_,{onClick:o[4]||(o[4]=t=>e(m)())})])}}}),P=x(k,[["__scopeId","data-v-f9d3468d"]]);export{P as default};
