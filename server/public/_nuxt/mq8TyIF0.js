import{_ as M}from"./C2uyROqA.js";import{h as R,a1 as j,f as z,E as A,v as O}from"./CLVDtxqA.js";import{E as T}from"./DY73EaVE.js";import{E as X}from"./JBMcerbz.js";import"./D5Ye8pBY.js";/* empty css        */import{l as Z,b as h,j as G,M as r,N as c,O as s,aa as H,u as a,Z as d,a0 as x,a6 as J,a7 as k,a9 as K,_ as E,aq as F,a4 as P,X as Q}from"./Dp9aCaJ6.js";import{c as W,d as Y}from"./Ce8FGLE8.js";import{isSameFile as ee}from"./BiHhwkbt.js";import{_ as te}from"./lCX_iigg.js";import{_ as se}from"./DlAUqK2U.js";import"./CRS4Ri59.js";import"./1ThCdcE_.js";import"./DQo9WDrm.js";import"./Cpg3PDWZ.js";const ae={class:"h-full flex flex-col"},oe={class:"py-[16px]"},le={class:"el-upload__text"},ne={class:"el-upload__text"},ie={class:"el-upload__text"},re={key:0,class:"grid grid-cols-2 gap-4 flex-1 min-h-[500px]"},ce={style:{"border-right":"1px solid #eeeeee"}},de={class:"mt-4 max-w-[500px]"},pe=["onClick"],me={class:"ml-2"},ue={class:"closeIcon ml-auto opacity-0 transition duration-300 flex items-center"},_e={class:"flex flex-col"},fe={class:"text-lg"},ve={class:"flex-auto mt-2 h-[100px]"},he=Z({__name:"cvs",props:{modelValue:{}},emits:["update:modelValue"],setup(V,{expose:I,emit:D}){const n=R(V,"modelValue",D),y=[".csv",".xlsx"],g=y.join(", "),p=h([]),C=G(),w=h(!1),m=h(-1);h([{question:"什么是知识库",answer:"知识库是指一个集中存储和管理知识的系统或平..."},{question:"如何创建知识库",answer:"1.确定目标和范围：明确知识库的目标和用途，确定..."}]);const U=async({raw:e})=>{var t,o;try{if(e){const u="."+((t=e.name.split(".").pop())==null?void 0:t.toLowerCase());if(!y.includes(u))throw`不支持的文件类型，请上传 ${g} 格式的文件`;w.value=!0,await ee(e,p.value);const i=await N(e);if(!i||!i.length)throw"解析结果为空，已自动忽略";if(!j(i))throw"解析失败";e.data=i,n.value.push({name:e.name,path:"",data:i}),p.value.push(e),console.log(p.value[0].name),b(p.value.length-1)}}catch(u){z.msgError(u)}finally{w.value=!1,(o=C.value)==null||o.clearFiles()}},$=async e=>{n.value[m.value].data.splice(e,1)},N=async e=>{const t=e.name.substring(e.name.lastIndexOf(".")+1);let o="";switch(t){case"csv":o=await Y(e);break;case"xlsx":o=await W(e);break}return o},b=e=>{m.value=e},B=e=>{n.value.splice(e,1),p.value.splice(e,1)};return I({clearFiles:()=>{p.value=[]}}),(e,t)=>{var q;const o=M,u=A,i=T,L=X,S=O;return r(),c("div",null,[s("div",ae,[H((r(),c("div",oe,[d(i,{ref_key:"uploadRef",ref:C,drag:"","on-change":U,"auto-upload":!1,"show-file-list":!1,accept:a(g),multiple:!0,limit:50},{tip:x(()=>t[4]||(t[4]=[s("div",{class:"el-upload__tip"}," 先完成填写后再上传，问题总数建议不要超过1000条，否则上传会卡顿 ",-1),s("div",{class:"el-upload__tip"}," 导入前会进行去重，如果问题和答案完全相同，则不会被导入，所以最终导入的内容可能会比文件的内容少。但是，对于带有换行的内容，目前无法去重。 ",-1)])),default:x(()=>[s("div",le,[d(o,{name:"el-icon-Upload"}),t[1]||(t[1]=J(" 拖拽文件至此，或点击")),t[2]||(t[2]=s("em",null," 选择文件 ",-1))]),s("div",ne,"支持 "+k(a(g))+" 文件",1),s("div",ie,[d(u,{type:"primary",class:"ml-2",link:"",onClick:t[0]||(t[0]=K(()=>{},["stop"]))},{default:x(()=>t[3]||(t[3]=[s("a",{href:"/static/xlsxTemplate.xlsx",target:"_blank"},"点击下载模版",-1)])),_:1})])]),_:1},8,["accept"])])),[[S,a(w)]]),a(n).length>0?(r(),c("div",re,[s("div",ce,[s("div",de,[(r(!0),c(E,null,F(a(n),(f,l)=>(r(),c("div",{key:l,class:Q(["fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer hover:bg-page transition duration-300",{"bg-page":a(m)==l}]),onClick:_=>b(l)},[d(o,{name:"el-icon-Folder",size:16,color:"#ffc94d"}),s("div",me,k(f.name),1),s("div",ue,[d(o,{name:"el-icon-DeleteFilled",onClick:_=>B(l)},null,8,["onClick"])])],10,pe))),128))])]),s("div",_e,[s("div",fe," 分段预览（"+k((q=a(n)[a(m)])==null?void 0:q.data.length)+"组） ",1),s("div",ve,[d(L,{height:"100%"},{default:x(()=>{var f;return[(r(!0),c(E,null,F((f=a(n)[a(m)])==null?void 0:f.data,(l,_)=>(r(),c("div",{class:"bg-page rounded p-[10px] mt-2",key:_},[d(te,{index:_,name:a(n)[a(m)].name,q:l.q,"onUpdate:q":v=>l.q=v,a:l.a,"onUpdate:a":v=>l.a=v,onDelete:v=>$(_)},null,8,["index","name","q","onUpdate:q","a","onUpdate:a","onDelete"])]))),128))]}),_:1})])])])):P("",!0)])])}}}),Le=se(he,[["__scopeId","data-v-f22869d6"]]);export{Le as default};
