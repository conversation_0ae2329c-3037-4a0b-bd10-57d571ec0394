import{a as A,E as k}from"./pw6-iEEW.js";import{E as T}from"./CBkeCdOF.js";import{E as w}from"./CYgEYzPG.js";import{E as C}from"./DTIryUWu.js";import{cA as V}from"./Br7V4jS9.js";/* empty css        *//* empty css        */import{u as I}from"./BQs8y7a2.js";import{u as B,I as f}from"./CDG5bHsH.js";import{e as S}from"./BFDvpBeT.js";import{l as z,m as D,r as L,ak as N,M as r,N as l,O as a,Z as c,a0 as v,u as e,_ as F,aq as R,a1 as $,X as q,a7 as J}from"./Dp9aCaJ6.js";const M={class:"avatar-select h-full flex flex-col"},O={class:"px-main"},P={class:"mt-[5px]"},U={class:"flex-1 min-h-0"},X={class:"px-main pb-main"},Z={key:0,class:"flex flex-wrap mx-[-7px]"},j=["onClick"],G={class:"px-[7px] mb-[14px]"},H={class:"pic-wrap h-0 pt-[110%] relative"},K={class:"absolute inset-0 bg-[#f4f6ff] rounded-lg"},Q={class:"mt-[10px] line-clamp-1 text-center"},ma=z({__name:"avatar",async setup(W){let n,d;const m=B(),x=t=>{m.addImage(t.cover_url,f.AVATAR,t)},b=D(()=>{var o;const t=(o=m.canvasJson.objects)==null?void 0:o.find(i=>i.customType===f.AVATAR);if(t!=null&&t.data)return t.data}),_=L({keyword:"",currentTab:"2d"}),{data:p}=([n,d]=N(()=>I(()=>V(),{lazy:!0},"$ax15cTFL79")),n=await n,d(),n);return(t,o)=>{const i=A,h=k,y=T,g=w,E=C;return r(),l("div",M,[a("div",O,[a("div",P,[c(h,{modelValue:e(_).currentTab,"onUpdate:modelValue":o[0]||(o[0]=s=>e(_).currentTab=s)},{default:v(()=>[c(i,{label:"2D形象",name:"2d"})]),_:1},8,["modelValue"])])]),a("div",U,[c(E,null,{default:v(()=>[a("div",X,[e(p).length?(r(),l("div",Z,[(r(!0),l(F,null,R(e(p),s=>{var u;return r(),l("div",{key:s.type,class:"w-[50%]",onClick:Y=>x(s)},[a("div",G,[a("div",{class:q(["border border-solid border-br-light rounded-md p-[10px] cursor-pointer",{"!border-primary":((u=e(b))==null?void 0:u.avatar_id)==s.avatar_id}])},[a("div",null,[a("div",H,[a("div",K,[c(y,{src:s.cover_url,class:"w-full h-full",fit:"contain",lazy:""},null,8,["src"])])]),a("div",Q,J(s.name),1)])],2)])],8,j)}),128))])):(r(),$(g,{key:1,image:e(S),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}});export{ma as _};
