import{_ as R}from"./a_MrTnou.js";import{E as T}from"./BiUm2mPE.js";import{E as z}from"./dYUcznge.js";import{h as M,f as O,v as Q}from"./CylNgAGi.js";import"./Dq3-3yo4.js";/* empty css        */import{r as E,a as P,b as X}from"./Cc-gfOyt.js";import{isSameFile as Z}from"./BiHhwkbt.js";import{s as G}from"./C4qLKnCc.js";import{_ as H}from"./CEpGSTz9.js";import{l as J,b as g,j as K,r as W,i as Y,M as i,N as r,aa as ee,u as o,Z as p,a0 as V,O as s,a6 as te,a7 as w,_ as F,aq as I,a4 as ae,X as oe}from"./Dp9aCaJ6.js";import{_ as se}from"./DlAUqK2U.js";import"./C6z7M7jp.js";import"./BjDNN4zb.js";import"./CyS7htb1.js";import"./Cpg3PDWZ.js";const ne={class:"h-full flex flex-col"},le={class:"py-[16px]"},ce={class:"el-upload__text"},ie={class:"el-upload__text"},re={key:0,class:"grid grid-cols-2 gap-4 flex-1 min-h-[500px]"},de={style:{"border-right":"1px solid #eeeeee"}},pe={class:"mt-4 max-w-[500px]"},me=["onClick"],ue={class:"ml-2"},_e={class:"closeIcon ml-auto opacity-0 transition duration-300 flex items-center"},fe={class:"flex flex-col"},he={class:"text-lg"},ve={class:"flex-auto mt-2 h-[100px]"},xe=J({__name:"QASplit",props:{modelValue:{}},emits:["update:modelValue"],setup(L,{emit:S}){const n=M(L,"modelValue",S),k=[".txt",".docx",".pdf",".md"],h=k.join(", "),u=g([]),C=K(),v=g(!1),d=g(-1),$=W([]),q=Y("knowDetail"),N=async({raw:e})=>{var a,t;try{if(e){const l="."+((a=e.name.split(".").pop())==null?void 0:a.toLowerCase());if(!k.includes(l))throw`不支持的文件类型，请上传 ${h} 格式的文件`;v.value=!0,await Z(e,u.value);const _=await A(e);if(!_)throw"解析结果为空，已自动忽略";const x=U(_);n.value.push({name:e.name,path:"",data:x}),u.value.push(e),b(u.value.length-1)}}catch(l){O.msgError(l)}finally{v.value=!1,(t=C.value)==null||t.clearFiles()}},A=async e=>{const a=e.name.substring(e.name.lastIndexOf(".")+1);let t="";switch(a){case"md":case"txt":t=await E(e);break;case"pdf":t=await X(e);break;case"doc":case"docx":t=await P(e);break;default:t=await E(e);break}return t},b=e=>{d.value=e,$.length=0},U=e=>{const a=[];return G({text:e,chunkLen:q.qa_length}).forEach(l=>{a.push({q:l,a:""})}),a},j=async e=>{n.value[d.value].data.splice(e,1)},B=e=>{n.value.splice(e,1),u.value.splice(e,1)};return(e,a)=>{var y;const t=R,l=T,_=z,x=Q;return i(),r("div",ne,[ee((i(),r("div",le,[p(l,{ref_key:"uploadRef",ref:C,drag:"","on-change":N,"auto-upload":!1,"show-file-list":!1,accept:o(h),multiple:!0,limit:50},{default:V(()=>[s("div",ce,[p(t,{name:"el-icon-Upload"}),a[0]||(a[0]=te(" 拖拽文件至此，或点击")),a[1]||(a[1]=s("em",null," 选择文件 ",-1))]),s("div",ie,"支持 "+w(o(h))+" 文件",1)]),_:1},8,["accept"])])),[[x,o(v)]]),o(n).length>0?(i(),r("div",re,[s("div",de,[s("div",pe,[(i(!0),r(F,null,I(o(n),(f,c)=>(i(),r("div",{key:c,class:oe(["fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer hover:bg-page transition duration-300",{"bg-page":o(d)==c}]),onClick:m=>b(c)},[p(t,{name:"el-icon-Folder",size:16,color:"#ffc94d"}),s("div",ue,w(f.name),1),s("div",_e,[p(t,{name:"el-icon-DeleteFilled",onClick:m=>B(c)},null,8,["onClick"])])],10,me))),128))])]),s("div",fe,[s("div",he," 分段预览（"+w((y=o(n)[o(d)])==null?void 0:y.data.length)+"组） ",1),s("div",ve,[p(_,{height:"100%"},{default:V(()=>{var f;return[(i(!0),r(F,null,I((f=o(n)[o(d)])==null?void 0:f.data,(c,m)=>(i(),r("div",{class:"bg-page rounded p-[10px] mt-2",key:m},[p(H,{index:m,name:o(n)[o(d)].name,data:c.q,"onUpdate:data":D=>c.q=D,onDelete:D=>j(m)},null,8,["index","name","data","onUpdate:data","onDelete"])]))),128))]}),_:1})])])])):ae("",!0)])}}}),je=se(xe,[["__scopeId","data-v-fdad676b"]]);export{je as default};
