import{E as g,a as x}from"./Dg0Y74mO.js";import{_ as v}from"./CcbLKbjB.js";import{b as y,v as k}from"./B5S_Er7H.js";/* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{a as V}from"./5SHXd8at.js";import{u as C}from"./67xbGseh.js";import{l as N,F as B,M as o,N as c,O as t,aa as E,u as _,a1 as T,a0 as l,Z as n,a7 as a,a6 as m,a4 as d,y as R}from"./Dp9aCaJ6.js";const S={class:"mt-4 flex-1 flex flex-col min-h-0"},D={class:"flex-1 min-h-0"},F={class:"flex items-center"},L={key:0},M={key:0},U={class:"flex items-center"},j={class:"flex items-center"},z={class:"flex flex-col"},A={key:0,class:"text-warning"},O={class:"flex justify-end mt-4"},ee=N({__name:"recharge",setup(P){const u=y(),{pager:i,getLists:p}=C({fetchFun:V});return B(()=>{p()}),(Z,r)=>{const s=g,h=x,f=v,b=k;return o(),c("div",S,[t("div",D,[E((o(),T(h,{height:"100%",size:"large",data:_(i).lists},{default:l(()=>[n(s,{label:"订单编号",prop:"order_sn","min-width":"130"},{default:l(({row:e})=>[t("div",F,[t("span",null,a(e.order_sn||"-"),1)])]),_:1}),n(s,{label:"套餐名称",prop:"name","min-width":"130"}),n(s,{label:"套餐内容","min-width":"200"},{default:l(({row:e})=>[t("div",null,[m(a(_(u).getTokenUnit)+"数量："+a(e.chat_balance)+" ",1),e.give_chat_balance>0?(o(),c("span",L," (赠送"+a(e.give_chat_balance)+") ",1)):d("",!0)]),t("div",null,[m(" 智能体数量："+a(e.robot_number)+" ",1),e.give_robot_number>0?(o(),c("span",M," (赠送"+a(e.give_robot_number)+"个) ",1)):d("",!0)])]),_:1}),n(s,{label:"购买方式","min-width":"100"},{default:l(({row:e})=>[t("div",U,[t("span",null,a(e.channel_text),1)])]),_:1}),n(s,{label:"支付方式",prop:"pay_way_text","min-width":"100"}),n(s,{label:"实付金额","min-width":"100"},{default:l(({row:e})=>[t("div",j,[t("span",null,a(e.channel==2?"-":"¥"+e.order_amount),1)])]),_:1}),n(s,{label:"支付状态",prop:"pay_status_text","min-width":"100"},{default:l(({row:e})=>[t("div",z,[t("span",null,a(e.pay_status_text),1),e.refund_status==1?(o(),c("span",A,"已退款")):d("",!0)])]),_:1}),n(s,{label:"支付/操作时间",prop:"pay_time","min-width":"160"},{default:l(({row:e})=>[m(a((e==null?void 0:e.pay_time)=="-"?e==null?void 0:e.create_time:e==null?void 0:e.pay_time),1)]),_:1})]),_:1},8,["data"])),[[b,_(i).loading]])]),t("div",O,[n(f,{modelValue:_(i),"onUpdate:modelValue":r[0]||(r[0]=e=>R(i)?i.value=e:null),onChange:_(p)},null,8,["modelValue","onChange"])])])}}});export{ee as _};
