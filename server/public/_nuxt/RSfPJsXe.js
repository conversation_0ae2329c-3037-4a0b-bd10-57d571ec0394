import{l as L,p as I,b as B,Y as U,F as K}from"./uahP8ofS.js";/*!
 * qrcode.vue v3.4.0
 * A Vue.js component to generate QRCode.
 * © 2017-2023 @scopewu(https://github.com/scopewu)
 * MIT License.
 */var O=function(){return O=Object.assign||function(h){for(var f,s=1,c=arguments.length;s<c;s++){f=arguments[s];for(var v in f)Object.prototype.hasOwnProperty.call(f,v)&&(h[v]=f[v])}return h},O.apply(this,arguments)},A;(function(u){var h=function(){function n(e,t,r,o){if(this.version=e,this.errorCorrectionLevel=t,this.modules=[],this.isFunction=[],e<n.MIN_VERSION||e>n.MAX_VERSION)throw new RangeError("Version value out of range");if(o<-1||o>7)throw new RangeError("Mask value out of range");this.size=e*4+17;for(var a=[],i=0;i<this.size;i++)a.push(!1);for(var i=0;i<this.size;i++)this.modules.push(a.slice()),this.isFunction.push(a.slice());this.drawFunctionPatterns();var l=this.addEccAndInterleave(r);if(this.drawCodewords(l),o==-1)for(var d=1e9,i=0;i<8;i++){this.applyMask(i),this.drawFormatBits(i);var C=this.getPenaltyScore();C<d&&(o=i,d=C),this.applyMask(i)}c(0<=o&&o<=7),this.mask=o,this.applyMask(o),this.drawFormatBits(o),this.isFunction=[]}return n.encodeText=function(e,t){var r=u.QrSegment.makeSegments(e);return n.encodeSegments(r,t)},n.encodeBinary=function(e,t){var r=u.QrSegment.makeBytes(e);return n.encodeSegments([r],t)},n.encodeSegments=function(e,t,r,o,a,i){if(r===void 0&&(r=1),o===void 0&&(o=40),a===void 0&&(a=-1),i===void 0&&(i=!0),!(n.MIN_VERSION<=r&&r<=o&&o<=n.MAX_VERSION)||a<-1||a>7)throw new RangeError("Invalid value");var l,d;for(l=r;;l++){var C=n.getNumDataCodewords(l,t)*8,m=v.getTotalBits(e,l);if(m<=C){d=m;break}if(l>=o)throw new RangeError("Data too long")}for(var E=0,M=[n.Ecc.MEDIUM,n.Ecc.QUARTILE,n.Ecc.HIGH];E<M.length;E++){var w=M[E];i&&d<=n.getNumDataCodewords(l,w)*8&&(t=w)}for(var g=[],p=0,N=e;p<N.length;p++){var R=N[p];f(R.mode.modeBits,4,g),f(R.numChars,R.mode.numCharCountBits(l),g);for(var S=0,z=R.getData();S<z.length;S++){var Y=z[S];g.push(Y)}}c(g.length==d);var _=n.getNumDataCodewords(l,t)*8;c(g.length<=_),f(0,Math.min(4,_-g.length),g),f(0,(8-g.length%8)%8,g),c(g.length%8==0);for(var F=236;g.length<_;F^=253)f(F,8,g);for(var b=[];b.length*8<g.length;)b.push(0);return g.forEach(function(G,Q){return b[Q>>>3]|=G<<7-(Q&7)}),new n(l,t,b,a)},n.prototype.getModule=function(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]},n.prototype.getModules=function(){return this.modules},n.prototype.drawFunctionPatterns=function(){for(var e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);for(var t=this.getAlignmentPatternPositions(),r=t.length,e=0;e<r;e++)for(var o=0;o<r;o++)e==0&&o==0||e==0&&o==r-1||e==r-1&&o==0||this.drawAlignmentPattern(t[e],t[o]);this.drawFormatBits(0),this.drawVersion()},n.prototype.drawFormatBits=function(e){for(var t=this.errorCorrectionLevel.formatBits<<3|e,r=t,o=0;o<10;o++)r=r<<1^(r>>>9)*1335;var a=(t<<10|r)^21522;c(a>>>15==0);for(var o=0;o<=5;o++)this.setFunctionModule(8,o,s(a,o));this.setFunctionModule(8,7,s(a,6)),this.setFunctionModule(8,8,s(a,7)),this.setFunctionModule(7,8,s(a,8));for(var o=9;o<15;o++)this.setFunctionModule(14-o,8,s(a,o));for(var o=0;o<8;o++)this.setFunctionModule(this.size-1-o,8,s(a,o));for(var o=8;o<15;o++)this.setFunctionModule(8,this.size-15+o,s(a,o));this.setFunctionModule(8,this.size-8,!0)},n.prototype.drawVersion=function(){if(!(this.version<7)){for(var e=this.version,t=0;t<12;t++)e=e<<1^(e>>>11)*7973;var r=this.version<<12|e;c(r>>>18==0);for(var t=0;t<18;t++){var o=s(r,t),a=this.size-11+t%3,i=Math.floor(t/3);this.setFunctionModule(a,i,o),this.setFunctionModule(i,a,o)}}},n.prototype.drawFinderPattern=function(e,t){for(var r=-4;r<=4;r++)for(var o=-4;o<=4;o++){var a=Math.max(Math.abs(o),Math.abs(r)),i=e+o,l=t+r;0<=i&&i<this.size&&0<=l&&l<this.size&&this.setFunctionModule(i,l,a!=2&&a!=4)}},n.prototype.drawAlignmentPattern=function(e,t){for(var r=-2;r<=2;r++)for(var o=-2;o<=2;o++)this.setFunctionModule(e+o,t+r,Math.max(Math.abs(o),Math.abs(r))!=1)},n.prototype.setFunctionModule=function(e,t,r){this.modules[t][e]=r,this.isFunction[t][e]=!0},n.prototype.addEccAndInterleave=function(e){var t=this.version,r=this.errorCorrectionLevel;if(e.length!=n.getNumDataCodewords(t,r))throw new RangeError("Invalid argument");for(var o=n.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][t],a=n.ECC_CODEWORDS_PER_BLOCK[r.ordinal][t],i=Math.floor(n.getNumRawDataModules(t)/8),l=o-i%o,d=Math.floor(i/o),C=[],m=n.reedSolomonComputeDivisor(a),E=0,M=0;E<o;E++){var w=e.slice(M,M+d-a+(E<l?0:1));M+=w.length;var g=n.reedSolomonComputeRemainder(w,m);E<l&&w.push(0),C.push(w.concat(g))}for(var p=[],N=function(R){C.forEach(function(S,z){(R!=d-a||z>=l)&&p.push(S[R])})},E=0;E<C[0].length;E++)N(E);return c(p.length==i),p},n.prototype.drawCodewords=function(e){if(e.length!=Math.floor(n.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var t=0,r=this.size-1;r>=1;r-=2){r==6&&(r=5);for(var o=0;o<this.size;o++)for(var a=0;a<2;a++){var i=r-a,l=(r+1&2)==0,d=l?this.size-1-o:o;!this.isFunction[d][i]&&t<e.length*8&&(this.modules[d][i]=s(e[t>>>3],7-(t&7)),t++)}}c(t==e.length*8)},n.prototype.applyMask=function(e){if(e<0||e>7)throw new RangeError("Mask value out of range");for(var t=0;t<this.size;t++)for(var r=0;r<this.size;r++){var o=void 0;switch(e){case 0:o=(r+t)%2==0;break;case 1:o=t%2==0;break;case 2:o=r%3==0;break;case 3:o=(r+t)%3==0;break;case 4:o=(Math.floor(r/3)+Math.floor(t/2))%2==0;break;case 5:o=r*t%2+r*t%3==0;break;case 6:o=(r*t%2+r*t%3)%2==0;break;case 7:o=((r+t)%2+r*t%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[t][r]&&o&&(this.modules[t][r]=!this.modules[t][r])}},n.prototype.getPenaltyScore=function(){for(var e=0,t=0;t<this.size;t++){for(var r=!1,o=0,a=[0,0,0,0,0,0,0],i=0;i<this.size;i++)this.modules[t][i]==r?(o++,o==5?e+=n.PENALTY_N1:o>5&&e++):(this.finderPenaltyAddHistory(o,a),r||(e+=this.finderPenaltyCountPatterns(a)*n.PENALTY_N3),r=this.modules[t][i],o=1);e+=this.finderPenaltyTerminateAndCount(r,o,a)*n.PENALTY_N3}for(var i=0;i<this.size;i++){for(var r=!1,l=0,a=[0,0,0,0,0,0,0],t=0;t<this.size;t++)this.modules[t][i]==r?(l++,l==5?e+=n.PENALTY_N1:l>5&&e++):(this.finderPenaltyAddHistory(l,a),r||(e+=this.finderPenaltyCountPatterns(a)*n.PENALTY_N3),r=this.modules[t][i],l=1);e+=this.finderPenaltyTerminateAndCount(r,l,a)*n.PENALTY_N3}for(var t=0;t<this.size-1;t++)for(var i=0;i<this.size-1;i++){var d=this.modules[t][i];d==this.modules[t][i+1]&&d==this.modules[t+1][i]&&d==this.modules[t+1][i+1]&&(e+=n.PENALTY_N2)}for(var C=0,m=0,E=this.modules;m<E.length;m++){var M=E[m];C=M.reduce(function(p,N){return p+(N?1:0)},C)}var w=this.size*this.size,g=Math.ceil(Math.abs(C*20-w*10)/w)-1;return c(0<=g&&g<=9),e+=g*n.PENALTY_N4,c(0<=e&&e<=2568888),e},n.prototype.getAlignmentPatternPositions=function(){if(this.version==1)return[];for(var e=Math.floor(this.version/7)+2,t=this.version==32?26:Math.ceil((this.version*4+4)/(e*2-2))*2,r=[6],o=this.size-7;r.length<e;o-=t)r.splice(1,0,o);return r},n.getNumRawDataModules=function(e){if(e<n.MIN_VERSION||e>n.MAX_VERSION)throw new RangeError("Version number out of range");var t=(16*e+128)*e+64;if(e>=2){var r=Math.floor(e/7)+2;t-=(25*r-10)*r-55,e>=7&&(t-=36)}return c(208<=t&&t<=29648),t},n.getNumDataCodewords=function(e,t){return Math.floor(n.getNumRawDataModules(e)/8)-n.ECC_CODEWORDS_PER_BLOCK[t.ordinal][e]*n.NUM_ERROR_CORRECTION_BLOCKS[t.ordinal][e]},n.reedSolomonComputeDivisor=function(e){if(e<1||e>255)throw new RangeError("Degree out of range");for(var t=[],r=0;r<e-1;r++)t.push(0);t.push(1);for(var o=1,r=0;r<e;r++){for(var a=0;a<t.length;a++)t[a]=n.reedSolomonMultiply(t[a],o),a+1<t.length&&(t[a]^=t[a+1]);o=n.reedSolomonMultiply(o,2)}return t},n.reedSolomonComputeRemainder=function(e,t){for(var r=t.map(function(d){return 0}),o=function(d){var C=d^r.shift();r.push(0),t.forEach(function(m,E){return r[E]^=n.reedSolomonMultiply(m,C)})},a=0,i=e;a<i.length;a++){var l=i[a];o(l)}return r},n.reedSolomonMultiply=function(e,t){if(e>>>8||t>>>8)throw new RangeError("Byte out of range");for(var r=0,o=7;o>=0;o--)r=r<<1^(r>>>7)*285,r^=(t>>>o&1)*e;return c(r>>>8==0),r},n.prototype.finderPenaltyCountPatterns=function(e){var t=e[1];c(t<=this.size*3);var r=t>0&&e[2]==t&&e[3]==t*3&&e[4]==t&&e[5]==t;return(r&&e[0]>=t*4&&e[6]>=t?1:0)+(r&&e[6]>=t*4&&e[0]>=t?1:0)},n.prototype.finderPenaltyTerminateAndCount=function(e,t,r){return e&&(this.finderPenaltyAddHistory(t,r),t=0),t+=this.size,this.finderPenaltyAddHistory(t,r),this.finderPenaltyCountPatterns(r)},n.prototype.finderPenaltyAddHistory=function(e,t){t[0]==0&&(e+=this.size),t.pop(),t.unshift(e)},n.MIN_VERSION=1,n.MAX_VERSION=40,n.PENALTY_N1=3,n.PENALTY_N2=3,n.PENALTY_N3=40,n.PENALTY_N4=10,n.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],n.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],n}();u.QrCode=h;function f(n,e,t){if(e<0||e>31||n>>>e)throw new RangeError("Value out of range");for(var r=e-1;r>=0;r--)t.push(n>>>r&1)}function s(n,e){return(n>>>e&1)!=0}function c(n){if(!n)throw new Error("Assertion error")}var v=function(){function n(e,t,r){if(this.mode=e,this.numChars=t,this.bitData=r,t<0)throw new RangeError("Invalid argument");this.bitData=r.slice()}return n.makeBytes=function(e){for(var t=[],r=0,o=e;r<o.length;r++){var a=o[r];f(a,8,t)}return new n(n.Mode.BYTE,e.length,t)},n.makeNumeric=function(e){if(!n.isNumeric(e))throw new RangeError("String contains non-numeric characters");for(var t=[],r=0;r<e.length;){var o=Math.min(e.length-r,3);f(parseInt(e.substring(r,r+o),10),o*3+1,t),r+=o}return new n(n.Mode.NUMERIC,e.length,t)},n.makeAlphanumeric=function(e){if(!n.isAlphanumeric(e))throw new RangeError("String contains unencodable characters in alphanumeric mode");var t=[],r;for(r=0;r+2<=e.length;r+=2){var o=n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r))*45;o+=n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r+1)),f(o,11,t)}return r<e.length&&f(n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r)),6,t),new n(n.Mode.ALPHANUMERIC,e.length,t)},n.makeSegments=function(e){return e==""?[]:n.isNumeric(e)?[n.makeNumeric(e)]:n.isAlphanumeric(e)?[n.makeAlphanumeric(e)]:[n.makeBytes(n.toUtf8ByteArray(e))]},n.makeEci=function(e){var t=[];if(e<0)throw new RangeError("ECI assignment value out of range");if(e<128)f(e,8,t);else if(e<16384)f(2,2,t),f(e,14,t);else if(e<1e6)f(6,3,t),f(e,21,t);else throw new RangeError("ECI assignment value out of range");return new n(n.Mode.ECI,0,t)},n.isNumeric=function(e){return n.NUMERIC_REGEX.test(e)},n.isAlphanumeric=function(e){return n.ALPHANUMERIC_REGEX.test(e)},n.prototype.getData=function(){return this.bitData.slice()},n.getTotalBits=function(e,t){for(var r=0,o=0,a=e;o<a.length;o++){var i=a[o],l=i.mode.numCharCountBits(t);if(i.numChars>=1<<l)return 1/0;r+=4+l+i.bitData.length}return r},n.toUtf8ByteArray=function(e){e=encodeURI(e);for(var t=[],r=0;r<e.length;r++)e.charAt(r)!="%"?t.push(e.charCodeAt(r)):(t.push(parseInt(e.substring(r+1,r+3),16)),r+=2);return t},n.NUMERIC_REGEX=/^[0-9]*$/,n.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,n.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",n}();u.QrSegment=v})(A||(A={}));(function(u){(function(h){var f=function(){function s(c,v){this.ordinal=c,this.formatBits=v}return s.LOW=new s(0,1),s.MEDIUM=new s(1,0),s.QUARTILE=new s(2,3),s.HIGH=new s(3,2),s}();h.Ecc=f})(u.QrCode||(u.QrCode={}))})(A||(A={}));(function(u){(function(h){var f=function(){function s(c,v){this.modeBits=c,this.numBitsCharCount=v}return s.prototype.numCharCountBits=function(c){return this.numBitsCharCount[Math.floor((c+7)/17)]},s.NUMERIC=new s(1,[10,12,14]),s.ALPHANUMERIC=new s(2,[9,11,13]),s.BYTE=new s(4,[8,16,16]),s.KANJI=new s(8,[8,10,12]),s.ECI=new s(7,[0,0,0]),s}();h.Mode=f})(u.QrSegment||(u.QrSegment={}))})(A||(A={}));var P=A,k="H",T={L:P.QrCode.Ecc.LOW,M:P.QrCode.Ecc.MEDIUM,Q:P.QrCode.Ecc.QUARTILE,H:P.QrCode.Ecc.HIGH},W=function(){try{new Path2D().addPath(new Path2D)}catch{return!1}return!0}();function y(u){return u in T}function H(u,h){h===void 0&&(h=0);var f=[];return u.forEach(function(s,c){var v=null;s.forEach(function(n,e){if(!n&&v!==null){f.push("M".concat(v+h," ").concat(c+h,"h").concat(e-v,"v1H").concat(v+h,"z")),v=null;return}if(e===s.length-1){if(!n)return;v===null?f.push("M".concat(e+h,",").concat(c+h," h1v1H").concat(e+h,"z")):f.push("M".concat(v+h,",").concat(c+h," h").concat(e+1-v,"v1H").concat(v+h,"z"));return}n&&v===null&&(v=e)})}),f.join("")}var D={value:{type:String,required:!0,default:""},size:{type:Number,default:100},level:{type:String,default:k,validator:function(u){return y(u)}},background:{type:String,default:"#fff"},foreground:{type:String,default:"#000"},margin:{type:Number,required:!1,default:0}},$=O(O({},D),{renderAs:{type:String,required:!1,default:"canvas",validator:function(u){return["canvas","svg"].indexOf(u)>-1}}}),X=L({name:"QRCodeSvg",props:D,setup:function(u){var h=B(0),f=B(""),s=function(){var c=u.value,v=u.level,n=u.margin,e=P.QrCode.encodeText(c,T[v]).getModules();h.value=e.length+n*2,f.value=H(e,n)};return s(),U(s),function(){return I("svg",{width:u.size,height:u.size,"shape-rendering":"crispEdges",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(h.value," ").concat(h.value)},[I("path",{fill:u.background,d:"M0,0 h".concat(h.value,"v").concat(h.value,"H0z")}),I("path",{fill:u.foreground,d:f.value})])}}}),J=L({name:"QRCodeCanvas",props:D,setup:function(u){var h=B(null),f=function(){var s=u.value,c=u.level,v=u.size,n=u.margin,e=u.background,t=u.foreground,r=h.value;if(r){var o=r.getContext("2d");if(o){var a=P.QrCode.encodeText(s,T[c]).getModules(),i=a.length+n*2,l=window.devicePixelRatio||1,d=v/i*l;r.height=r.width=v*l,o.scale(d,d),o.fillStyle=e,o.fillRect(0,0,i,i),o.fillStyle=t,W?o.fill(new Path2D(H(a,n))):a.forEach(function(C,m){C.forEach(function(E,M){E&&o.fillRect(M+n,m+n,1,1)})})}}};return K(f),U(f),function(){return I("canvas",{ref:h,style:{width:"".concat(u.size,"px"),height:"".concat(u.size,"px")}})}}}),Z=L({name:"Qrcode",render:function(){var u=this.$props,h=u.renderAs,f=u.value,s=u.size,c=u.margin,v=u.level,n=u.background,e=u.foreground,t=s>>>0,r=c>>>0,o=y(v)?v:k;return I(h==="svg"?X:J,{value:f,size:t,margin:r,level:o,background:n,foreground:e})},props:$});export{Z as Q};
