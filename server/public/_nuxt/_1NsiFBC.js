import{E as d}from"./Cx3Arr0P.js";import{_ as x}from"./BISO3PD6.js";import{_ as v}from"./C8ZjkaO0.js";import{E as h}from"./B5S_Er7H.js";/* empty css        */import{u as g,a as k}from"./DsvdwZsr.js";import{u as w}from"./Bb2-23m7.js";import{l as C,M as t,N as c,u as s,_ as E,Z as e,X as y,O as B,a0 as b}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./CacOB3dg.js";import"./GM0TYtnE.js";import"./Tox-HrLw.js";import"./DbRYflOL.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CiYvFM4x.js";import"./CmwH0tdT.js";import"./DTpVU3st.js";const M={key:0,class:"px-[10px]"},N={class:"flex-1 min-w-0 mx-[5px]"},S=C({__name:"select-music",setup(T){const o=g(),{changeTabs:n}=k(),{play:i,audioPlaying:a,pause:m}=w(),r=()=>{o.music.url?a.value?m():i(o.music.url):n("music")},l=()=>{o.music={id:0,url:"",cover:"",name:""}};return(z,O)=>{const p=d,u=x,_=v,f=h;return t(),c("div",{class:"h-[40px] px-[10px] max-w-[220px] flex items-center shadow-[0_2px_6px_#ebefff] rounded-full bg-white cursor-pointer",onClick:r},[s(o).music.id?(t(),c(E,{key:1},[e(p,{class:y([{playing:s(a)},"w-[30px] h-[30px] rounded-full"]),src:s(o).music.cover},null,8,["class","src"]),B("div",N,[e(u,{content:s(o).music.name},null,8,["content"])]),e(f,{link:"",onClick:l},{default:b(()=>[e(_,{name:"el-icon-Close",size:"20"})]),_:1})],64)):(t(),c("div",M,"选择音乐"))])}}}),Y=I(S,[["__scopeId","data-v-38a0cc13"]]);export{Y as default};
