import{_ as ie}from"./CQG-tpkc.js";import{E as de}from"./Bl6ZnX2R.js";import{a as re,E as ce}from"./Cu5sLRaN.js";import{h as me,f as F,E as ue,e as pe,v as _e}from"./Br7V4jS9.js";import{E as fe}from"./CuIdQWNz.js";import{E as ve}from"./DTIryUWu.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./BT1PeYhT.js";import{isSameFile as he}from"./BiHhwkbt.js";import{r as M,a as ge,b as xe}from"./x4QhavUR.js";import{s as ye}from"./C4qLKnCc.js";import{_ as be}from"./BfQ9g1yp.js";import{r as we,s as ke}from"./BNnETjxs.js";import{l as Ce,b as r,j as Ee,m as O,F as Ve,M as i,N as c,O as a,Z as n,a0 as u,_ as k,aq as C,u as o,y as I,a7 as f,a6 as D,a4 as Q,aa as Te,a1 as P,X as Fe}from"./Dp9aCaJ6.js";import{_ as Ie}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./DgYHmeWQ.js";import"./Cv6HhfEG.js";import"./C5S6gXlf.js";import"./B9mz6c9C.js";import"./BXXFZfct.js";import"./LFZ9apcG.js";import"./ynsGo-Dq.js";import"./Dg3tPGYu.js";import"./Cpg3PDWZ.js";const De={class:"h-full flex flex-col min-h-0"},Le={class:"mb-4 p-4 bg-gray-50 rounded-lg"},Se={class:"flex items-center mb-3"},Ue={class:"grid grid-cols-2 gap-4"},$e={class:"flex justify-between items-center"},ze={key:0,class:"mt-3 p-3 bg-white rounded border"},Be={class:"flex justify-between items-start"},Ne={class:"flex-1"},je={class:"font-medium text-sm"},qe={class:"text-xs text-gray-500 mt-1"},Re={class:"text-xs text-gray-400 mt-1"},Ae={class:"py-[16px]"},Me={class:"el-upload__text"},Oe={class:"el-upload__text"},Qe={key:0,class:"grid grid-cols-2 gap-4 flex-1 min-h-[500px]"},Pe={style:{"border-right":"1px solid #eeeeee"}},Xe={class:"mt-4 max-w-[500px]"},Ze=["onClick"],Ge={class:"ml-2"},He={class:"closeIcon ml-auto opacity-0 transition duration-300 flex items-center"},Je={class:"mt-4"},Ke={class:"flex"},We={class:"mt-2"},Ye={class:"flex flex-col"},et={class:"text-lg"},tt={class:"flex-auto mt-2 h-[100px]"},at=Ce({__name:"doc",props:{modelValue:{}},emits:["update:modelValue"],setup(X,{expose:Z,emit:G}){const m=me(X,"modelValue",G),L=[".txt",".docx",".pdf",".md"],E=L.join(", "),p=r([]),S=Ee(),V=r(!1),v=r(-1);r("");const w=r(512),h=r(""),g=r(""),U=r([]),$=r([]),T=r(!1),z=O(()=>{if(!h.value)return[];const e=$.value.find(t=>t.id===h.value);return e?e.templates:[]}),x=O(()=>g.value?z.value.find(e=>e.id===g.value):null),H=()=>{g.value=""},J=()=>{},K=async()=>{if(x.value)try{T.value=!0;const e=await we({id:x.value.id}),t=document.createElement("a");t.href=e.download_url,t.download=e.name,t.target="_blank",document.body.appendChild(t),t.click(),document.body.removeChild(t),F.msgSuccess("模板下载成功")}catch(e){F.msgError("模板下载失败: "+(e.message||"未知错误"))}finally{T.value=!1}},W=async()=>{try{const e=await ke();$.value=e,U.value=e.map(t=>({id:t.id,name:t.name}))}catch(e){console.error("获取模板库数据失败:",e)}};Ve(()=>{W()});const Y=async({raw:e})=>{var t,s;try{if(e){const d="."+((t=e.name.split(".").pop())==null?void 0:t.toLowerCase());if(!L.includes(d))throw`不支持的文件类型，请上传 ${E} 格式的文件`;V.value=!0,await he(e,p.value);const y=await ee(e);if(!y)throw"解析结果为空，已自动忽略";m.value.push({name:e.name,path:"",data:[]}),e.data=y,p.value.push(e),N(p.value.length-1),B()}}catch(d){F.msgError(d)}finally{V.value=!1,(s=S.value)==null||s.clearFiles()}},B=()=>{m.value.forEach(e=>{e.data=[];const t=p.value.findIndex(d=>d.name==e.name);ye({text:p.value[t].data,chunkLen:w.value}).forEach(d=>{e.data.push({q:d,a:""})})})},ee=async e=>{const t=e.name.substring(e.name.lastIndexOf(".")+1);let s="";switch(t){case"md":case"txt":s=await M(e);break;case"pdf":s=await xe(e);break;case"doc":case"docx":s=await ge(e);break;default:s=await M(e);break}return s},te=async e=>{m.value[v.value].data.splice(e,1)},ae=e=>{m.value.splice(e,1),p.value.splice(e,1)},N=e=>{v.value=e};return Z({clearFiles:()=>{p.value=[]}}),(e,t)=>{var R;const s=ie,d=de,y=ce,j=re,q=ue,oe=fe,le=pe,se=ve,ne=_e;return i(),c("div",De,[a("div",Le,[a("div",Se,[n(s,{name:"el-icon-Document",size:"16",class:"mr-2"}),t[3]||(t[3]=a("span",{class:"font-medium"},"模板库",-1)),n(d,{content:"选择模板快速下载导入文档",placement:"top"},{default:u(()=>[n(s,{name:"el-icon-QuestionFilled",size:"14",class:"ml-2 text-gray-400"})]),_:1})]),a("div",Ue,[a("div",null,[n(j,{modelValue:o(h),"onUpdate:modelValue":t[0]||(t[0]=l=>I(h)?h.value=l:null),placeholder:"请选择模板类别",clearable:"",class:"w-full",onChange:H},{default:u(()=>[(i(!0),c(k,null,C(o(U),l=>(i(),P(y,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),a("div",null,[n(j,{modelValue:o(g),"onUpdate:modelValue":t[1]||(t[1]=l=>I(g)?g.value=l:null),placeholder:"请选择具体模板",clearable:"",class:"w-full",disabled:!o(h),onChange:J,filterable:""},{default:u(()=>[(i(!0),c(k,null,C(o(z),l=>(i(),P(y,{key:l.id,label:l.name,value:l.id},{default:u(()=>[a("div",$e,[a("span",null,f(l.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])])]),o(x)?(i(),c("div",ze,[a("div",Be,[a("div",Ne,[a("div",je,f(o(x).name),1),a("div",qe,f(o(x).description),1),a("div",Re," 文件类型："+f(o(x).file_type),1)]),n(q,{type:"primary",size:"small",onClick:K,loading:o(T)},{default:u(()=>t[4]||(t[4]=[D(" 下载模板 ")])),_:1},8,["loading"])])])):Q("",!0)]),Te((i(),c("div",Ae,[n(oe,{ref_key:"uploadRef",ref:S,drag:"","on-change":Y,"auto-upload":!1,"show-file-list":!1,accept:o(E),multiple:!0,limit:50},{default:u(()=>[a("div",Me,[n(s,{name:"el-icon-Upload"}),t[5]||(t[5]=D(" 拖拽文件至此，或点击")),t[6]||(t[6]=a("em",null," 选择文件 ",-1))]),a("div",Oe,"支持 "+f(o(E))+" 文件",1)]),_:1},8,["accept"])])),[[ne,o(V)]]),o(m).length>0?(i(),c("div",Qe,[a("div",Pe,[a("div",Xe,[(i(!0),c(k,null,C(o(m),(l,_)=>(i(),c("div",{key:_,class:Fe(["fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer hover:bg-page transition duration-300",{"bg-page":o(v)==_}]),onClick:b=>N(_)},[n(s,{name:"el-icon-Folder",size:16,color:"#ffc94d"}),a("div",Ge,f(l.name),1),a("div",He,[n(s,{name:"el-icon-DeleteFilled",onClick:b=>ae(_)},null,8,["onClick"])])],10,Ze))),128))]),a("div",Je,[a("div",Ke,[t[7]||(t[7]=a("div",null,"分段长度",-1)),n(d,{content:"按结束符号进行分段。我们建议您的文档应合理的使用标点符号，以确保每个完整的句子长度不要超过该值中文文档建议400~1000英文文档建议600~1200",placement:"top"},{default:u(()=>[a("span",null,[n(s,{name:"el-icon-QuestionFilled"})])]),_:1})]),n(le,{class:"mt-2 !w-[300px]",modelValue:o(w),"onUpdate:modelValue":t[2]||(t[2]=l=>I(w)?w.value=l:null)},null,8,["modelValue"]),a("div",We,[n(q,{type:"primary",onClick:B},{default:u(()=>t[8]||(t[8]=[D("重新预览")])),_:1})])])]),a("div",Ye,[a("div",et," 分段预览（"+f((R=o(m)[o(v)])==null?void 0:R.data.length)+"组） ",1),a("div",tt,[n(se,{height:"100%"},{default:u(()=>{var l;return[(i(!0),c(k,null,C((l=o(m)[o(v)])==null?void 0:l.data,(_,b)=>(i(),c("div",{class:"bg-page rounded p-[10px] mt-2",key:b},[n(be,{index:b,name:o(m)[o(v)].name,data:_.q,"onUpdate:data":A=>_.q=A,onDelete:A=>te(b)},null,8,["index","name","data","onUpdate:data","onDelete"])]))),128))]}),_:1})])])])):Q("",!0)])}}}),$t=Ie(at,[["__scopeId","data-v-85ed1cb8"]]);export{$t as default};
