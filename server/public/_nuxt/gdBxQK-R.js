import{a as B,l as I,f as C,e as F,o as N,E as z,p as D}from"./DNaNbs6R.js";import{_ as M}from"./BWd1nnnI.js";import{E as Q}from"./dIlF1NYp.js";import{_ as S}from"./Dvr6zBhi.js";import{_ as q}from"./WtvpZyqr.js";import{E as A}from"./BOI9rKCA.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{k as K,c as O}from"./BNnETjxs.js";import{l as P,r as R,c as j,M as T,N as Z,Z as o,a0 as l,u as s,O as r,a6 as f}from"./Dp9aCaJ6.js";import{_ as $}from"./DlAUqK2U.js";import"./CMsEk3FA.js";import"./DCTLXrZ8.js";import"./B-XemFWQ.js";import"./9Bti1uB6.js";import"./CUCgy8P_.js";import"./C7WF4igz.js";import"./Cv6HhfEG.js";import"./RgeKLsDk.js";import"./W1Gt2nzg.js";import"./BEIhC0Cu.js";import"./Dxs4oV12.js";import"./BwPetIct.js";import"./vCtA0sHn.js";/* empty css        *//* empty css        */import"./BFjeuWRo.js";import"./CQAjyP_F.js";/* empty css        */import"./tBQNkaKJ.js";import"./CBBj1tAb.js";import"./D94ozPrj.js";import"./DTXB5BGp.js";const G={class:"h-full"},H={class:"flex items-center cursor-pointer text-[#666]"},J={class:"flex items-center cursor-pointer text-[#666]"},L=P({__name:"base-setting",props:{data:{default:()=>({})}},emits:["update"],setup(b,{emit:w}){const g=b,v=w,m=B(),x=I(),t=R({name:"",image:"",intro:"",documents_model_id:"",documents_model_sub_id:"",embedding_model_id:"",is_owner:1});j(()=>g.data,n=>{Object.assign(t,n)});const E=async()=>{await K({...t,id:m.query.id}),v("update")},V=async()=>{await C.confirm("确认删除吗？"),await O({id:m.query.id}),x.push("/application/layout/kb")};return(n,e)=>{const d=F,i=N,p=M,u=Q,_=S,k=q,c=z,y=D,U=A;return T(),Z("div",G,[o(U,null,{default:l(()=>[o(y,{"label-position":"top",class:"setup-form"},{default:l(()=>[o(i,{label:"知识库名称"},{default:l(()=>[o(d,{placeholder:"知识库名称",modelValue:s(t).name,"onUpdate:modelValue":e[0]||(e[0]=a=>s(t).name=a)},null,8,["modelValue"])]),_:1}),o(i,{label:"简介"},{default:l(()=>[o(d,{type:"textarea",placeholder:"知识库名称",rows:5,resize:"none",modelValue:s(t).intro,"onUpdate:modelValue":e[1]||(e[1]=a=>s(t).intro=a)},null,8,["modelValue"])]),_:1}),o(i,{label:"向量模型"},{label:l(()=>[o(u,{placement:"right",width:300,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"向量模型可以将自然语言转成向量(即数据训练), 用于进行语义检索, 注意: 不同向量模型无法一起使用, 选择完后将无法修改。"},{reference:l(()=>[r("div",H,[e[6]||(e[6]=r("span",{class:"mr-1"},"向量模型",-1)),o(p,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),default:l(()=>[o(_,{class:"flex-1",id:s(t).embedding_model_id,"onUpdate:id":e[2]||(e[2]=a=>s(t).embedding_model_id=a),"set-default":!1,type:"vectorModels",disabled:!0},null,8,["id"])]),_:1}),o(i,{label:"文件处理模型"},{label:l(()=>[o(u,{placement:"right",width:300,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"文件模型用于QA拆分功能(导入数据->自动拆分问答对), 利用该AI模型对导入的文本进行处理，最终拆分成一问一答的数据形式。"},{reference:l(()=>[r("div",J,[e[7]||(e[7]=r("span",{class:"mr-1"},"文件处理模型",-1)),o(p,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),default:l(()=>[o(_,{class:"flex-1",id:s(t).documents_model_id,"onUpdate:id":e[3]||(e[3]=a=>s(t).documents_model_id=a),sub_id:s(t).documents_model_sub_id,"onUpdate:sub_id":e[4]||(e[4]=a=>s(t).documents_model_sub_id=a),"set-default":!1},null,8,["id","sub_id"])]),_:1}),o(i,{label:"设置封面"},{default:l(()=>[o(k,{modelValue:s(t).image,"onUpdate:modelValue":e[5]||(e[5]=a=>s(t).image=a)},null,8,["modelValue"])]),_:1}),o(i,null,{default:l(()=>[o(c,{type:"primary",disabled:n.data.power>=2,onClick:E},{default:l(()=>e[8]||(e[8]=[f(" 保存设置 ")])),_:1},8,["disabled"]),o(c,{onClick:V,disabled:n.data.power!==1},{default:l(()=>e[9]||(e[9]=[f(" 删除 ")])),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1})])}}}),De=$(L,[["__scopeId","data-v-50067880"]]);export{De as default};
