import{e as i}from"./DAgm18qP.js";import{l as s,M as l,N as a,O as d,Z as r,u as e,ai as f}from"./Dp9aCaJ6.js";import{_ as u}from"./C-NSIm4h.js";import{_}from"./BhDelzvi.js";import{useSearch as c}from"./L_xqytYh.js";import{_ as x}from"./DlAUqK2U.js";import"./DMHEbzLi.js";import"./CfiDmG6E.js";import"./DCTLXrZ8.js";import"./BZieL5UA.js";import"./CFxUWFIJ.js";import"./DFChVafq.js";import"./Uw_fYagh.js";import"./9Bti1uB6.js";import"./B5giXwus.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./Bm06Fksf.js";import"./CKPPULrB.js";import"./BLV0QRdm.js";import"./Cq2NhlyP.js";import"./DbtR4j96.js";const y={class:"bg-page overflow-hidden flex items-center input-select"},v={class:"flex-none flex px-[8px]"},k=s({__name:"input-select",setup(V){const{options:p,launchSearch:m}=c();return(w,o)=>{const n=i;return l(),a("div",y,[d("div",v,[r(u,{mode:"dropdown",model:e(p).model,"onUpdate:model":o[0]||(o[0]=t=>e(p).model=t),type:e(p).type,"onUpdate:type":o[1]||(o[1]=t=>e(p).type=t)},null,8,["model","type"])]),r(n,{modelValue:e(p).ask,"onUpdate:modelValue":o[2]||(o[2]=t=>e(p).ask=t),placeholder:"输入你想搜索的问题",onKeydown:o[3]||(o[3]=f(t=>e(m)(),["enter"]))},null,8,["modelValue"]),r(_,{onClick:o[4]||(o[4]=t=>e(m)())})])}}}),P=x(k,[["__scopeId","data-v-f9d3468d"]]);export{P as default};
