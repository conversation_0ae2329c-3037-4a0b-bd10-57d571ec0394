import{a as w,b as C}from"./CxOVJjFn.js";import{_ as N}from"./C5vUgO5w.js";import{E as D}from"./BztQVLRH.js";import{E as B}from"./DZCM63qd.js";import{cE as L}from"./DjCZV6kq.js";/* empty css        *//* empty css        */import{u as S}from"./BGGhXV_w.js";import{u as $}from"./DjoWgbDN.js";import I from"./byfpyCE4.js";import{e as R}from"./CVf9TiPx.js";import{l as U,r as A,ak as G,m as M,M as o,N as r,O as l,Z as d,a0 as i,u as t,_ as x,aq as y,a1 as p,a6 as O,a7 as T}from"./Dp9aCaJ6.js";const Z=""+new URL("dub.hl03NZow.png",import.meta.url).href,q={class:"h-full flex flex-col"},z={class:"pt-[15px] px-main"},F={class:"flex-1 min-h-0"},P={class:"p-main"},j={key:0},ie=U({__name:"dub",async setup(H){let s,u;const c=$(),n=A({keyword:"",index:0}),{data:_,refresh:J,pending:K}=([s,u]=G(()=>S(()=>L(n),{lazy:!0},"$O0PtTSN0G7")),s=await s,u(),s),f=M(()=>{var a;return((a=_.value[n.index])==null?void 0:a.list)||[]}),h=a=>{c.dub=a};return(a,b)=>{const v=C,g=w,k=N,E=D,V=B;return o(),r("div",q,[l("div",null,[l("div",z,[d(k,{class:"my-[-5px]","default-height":42},{default:i(()=>[d(g,{modelValue:t(n).index,"onUpdate:modelValue":b[0]||(b[0]=e=>t(n).index=e),class:"el-radio-group-margin"},{default:i(()=>[(o(!0),r(x,null,y(t(_),(e,m)=>(o(),p(v,{key:m,label:m},{default:i(()=>[O(T(e.type),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),l("div",F,[d(V,null,{default:i(()=>[l("div",P,[t(f).length?(o(),r("div",j,[(o(!0),r(x,null,y(t(f),e=>(o(),p(I,{"active-id":t(c).dub.Voice,"item-id":e.Voice,key:e.Voice,class:"mb-[15px]",name:`${e.Name}-${e.Desc}`,pic:t(Z),url:e.VoiceUrl,disabled:t(c).voiceContent.type===2,onClick:m=>h(e)},null,8,["active-id","item-id","name","pic","url","disabled","onClick"]))),128))])):(o(),p(E,{key:1,image:t(R),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}});export{ie as _};
