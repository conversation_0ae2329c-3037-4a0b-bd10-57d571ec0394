import{_ as u}from"./CN8DIg3d.js";import{E as d}from"./DdkLcgv7.js";import{E as x}from"./DqzU5vn5.js";import{b as y,_ as h}from"./BQ-RMI0l.js";/* empty css        */import k from"./C42oHLfP.js";import v from"./wZ0nl1PE.js";import{e as w}from"./Dj0_HBiv.js";import{l as E,j as R,M as m,N as p,Z as t,a0 as r,u as i,O as n}from"./Dp9aCaJ6.js";import"./Ci86EFhe.js";import"./BBQxRZuk.js";import"./BOZQs96k.js";import"./B_BP6MI7.js";import"./DlAUqK2U.js";import"./DhTLUUeS.js";import"./DCTLXrZ8.js";import"./BXLvtum9.js";import"./BHIHQvwt.js";import"./L7ewHh_h.js";import"./9Bti1uB6.js";import"./DDaEm-_F.js";import"./CfylPmvH.js";import"./l0sNRNKZ.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./DkjF24mE.js";import"./CcPlX2kz.js";import"./Bu_nKEGp.js";import"./CxfT0z4n.js";import"./D49putpZ.js";import"./gexBVUvT.js";import"./CMYCTDrJ.js";import"./8yhmlZGP.js";import"./DlKZEFPo.js";import"./djnMiA6p.js";import"./C1aUxTFE.js";import"./Cv6HhfEG.js";import"./DpOQaFAg.js";import"./IZ5UIo3-.js";import"./DzZ0m6Bt.js";/* empty css        *//* empty css        */import"./DjwCd26w.js";import"./B3UVgHox.js";import"./De57gGYj.js";/* empty css        */import"./BzMu647W.js";const g={key:0,class:"h-full p-[16px] flex"},N={class:"flex-1 min-w-0 h-full pl-[16px]"},B={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Bt=E({__name:"index",setup(j){const l=y(),e=R();return(C,o)=>{const a=u,_=d,c=x,f=h;return m(),p("div",null,[t(f,{name:"default"},{default:r(()=>[i(l).config.switch.music_status?(m(),p("div",g,[t(k,{onUpdate:o[0]||(o[0]=I=>{var s;return(s=i(e))==null?void 0:s.refresh()})}),n("div",N,[t(a,null,{default:r(()=>[t(v,{ref_key:"recordRef",ref:e},null,512)]),_:1})])])):(m(),p("div",B,[t(c,null,{icon:r(()=>[t(_,{class:"w-[150px] dark:opacity-60",src:i(w)},null,8,["src"])]),title:r(()=>o[1]||(o[1]=[n("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Bt as default};
