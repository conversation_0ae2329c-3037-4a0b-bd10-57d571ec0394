import{_ as q}from"./CQG-tpkc.js";import{_ as C}from"./rgjptJRl.js";import{E}from"./CBkeCdOF.js";import{E as B}from"./DTIryUWu.js";import{_ as R}from"./D1e6cbtq.js";import{a as L,_ as z}from"./Br7V4jS9.js";/* empty css        *//* empty css        */import{u as I}from"./BQs8y7a2.js";import{G as S}from"./Bo3PTL3c.js";import{l as V,ak as A,m as u,M as i,N as x,Z as e,a0 as a,O as t,_ as D,aq as F,u as r,a1 as f,a4 as O,X as h,a7 as b}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./B4zJW-6l.js";import"./B9mz6c9C.js";import"./CCBJtVSv.js";import"./PP0YUb69.js";import"./9SnvtqGZ.js";import"./DgYHmeWQ.js";/* empty css        */import"./BXrbOnVu.js";import"./1QakEyj_.js";import"./Dg3tPGYu.js";import"./Cpg3PDWZ.js";import"./Cs-EFu39.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./CHOEeQ9C.js";import"./Bb2-23m7.js";import"./Cl_fs4jJ.js";import"./DwFObZc_.js";import"./B_1915px.js";import"./BhyLIuCu.js";import"./Bg2e09vA.js";import"./V6VzAR3J.js";import"./CuIdQWNz.js";import"./BT1PeYhT.js";import"./ynsGo-Dq.js";import"./C5S6gXlf.js";import"./DzsXYoW8.js";import"./C3sn0ick.js";import"./BNboyO0J.js";import"./DDaEm-_F.js";import"./C2gafFWG.js";import"./Bz7RyBol.js";import"./DDCcsBP0.js";import"./OxjSv_PS.js";import"./x4QhavUR.js";const X={class:"h-full flex"},Z={class:"p-4 h-full"},$={class:"w-[300px] h-full flex flex-col bg-body rounded-lg"},G={class:"p-[15px]"},M={class:"flex items-center"},T={class:"flex-1 min-h-0"},j={class:"px-[15px]"},H={class:"flex-1 min-w-0 ml-[15px]"},J={class:"line-clamp-1 text-xl font-medium"},K={class:"flex-1 min-w-0 pr-4 py-4"},P={class:"bg-body rounded-[10px] h-full"},jt=V({__name:"chat",async setup(Q){let s,p;const y=L(),{data:l}=([s,p]=A(()=>I(()=>S(),{default(){return[]},lazy:!0},"$X7Lic9ZOFl")),s=await s,p(),s),m=u(()=>y.query.id),n=u(()=>l.value.find(c=>c.id===Number(m.value))||{});return(c,d)=>{const v=q,_=C,g=E,w=B,k=R,N=z;return i(),x("div",null,[e(N,{name:"default"},{default:a(()=>[t("div",X,[t("div",Z,[t("div",$,[t("div",G,[t("div",M,[e(_,{class:"flex bg-body p-[5px] text-bold rounded-[50%] text-primary shadow-light",to:"/robot_square",replace:!0},{default:a(()=>[e(v,{name:"el-icon-Back",size:18})]),_:1}),d[0]||(d[0]=t("div",{class:"text-xl flex-1 min-w-0 ml-[10px]"}," 智能体广场 ",-1))])]),t("div",T,[e(w,null,{default:a(()=>[t("div",j,[(i(!0),x(D,null,F(r(l),o=>(i(),f(_,{key:o.id,to:{path:"",query:{id:o.id}},class:h(["flex mb-[15px] rounded-[10px] px-[15px] py-[10px] items-center border border-br-light bg-body",{"text-white !border-primary !bg-primary":r(m)==o.id}]),replace:!0},{default:a(()=>[e(g,{class:"w-[50px] h-[50px] rounded-[50%]",src:o.image,alt:""},null,8,["src"]),t("div",H,[t("div",J,b(o.name),1),t("div",{class:h(["line-clamp-1 mt-[4px] text-tx-secondary",{"!text-white":r(m)==o.id}])},b(o.intro),3)])]),_:2},1032,["to","class"]))),128))])]),_:1})])])]),t("div",K,[t("div",P,[r(n).id?(i(),f(k,{key:0,"robot-id":r(n).robot_id,"square-id":r(n).id},null,8,["robot-id","square-id"])):O("",!0)])])])]),_:1})])}}});export{jt as default};
