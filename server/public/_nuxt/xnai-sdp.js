import{E as l}from"./DeQZaZZG.js";import{b as c,_}from"./B1MekKW7.js";/* empty css        */import u from"./D-GTFJoH.js";import{l as d,M as t,N as r,Z as p,a0 as m,O as i,_ as f,aq as x,u as e,a1 as h,a3 as k,a4 as v}from"./Dp9aCaJ6.js";import"./CstON6P4.js";import"./Dkrb28GR.js";import"./CPkX1WPy.js";import"./wW1KxDBP.js";import"./t2WiU-SJ.js";import"./BC3Yhcvq.js";import"./DplCVaqu.js";import"./D2beQuxj.js";/* empty css        */import"./DlAUqK2U.js";import"./CcDY2hhD.js";import"./C73D5DMN.js";import"./DCeIRQC0.js";import"./BEWPMjYM.js";import"./Obgzek2B.js";import"./1nb9e5kE.js";import"./Df0xfART.js";import"./DCTLXrZ8.js";import"./AOe4nXt1.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./BR-n8kyL.js";import"./C-yzHPYx.js";import"./CN8DIg3d.js";import"./uE1Ww79i.js";import"./K0Vfa6Py.js";/* empty css        */import"./D467drPx.js";import"./DtpDBFjz.js";import"./D2wP0d1N.js";import"./C1GDMjBs.js";import"./9Bti1uB6.js";/* empty css        */import"./BjPc4uj5.js";import"./QLtE1ufq.js";import"./WMQbJQcL.js";import"./DPQJTQSO.js";import"./BxTfM8xu.js";import"./BE7GAo-z.js";import"./D_9hdWA4.js";import"./R3yOnkZw.js";import"./IgeL0vc_.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";import"./CwDcW2Mh.js";import"./Cv1u9LLW.js";import"./V7UEpkrK.js";const N={class:"h-full"},S={class:"index"},Eo=d({__name:"index",setup(g){const a=c();return(y,B)=>{const n=l,s=_;return t(),r("div",null,[p(s,{name:"single-row"},{default:m(()=>[i("div",N,[p(n,null,{default:m(()=>[i("div",S,[(t(!0),r(f,null,x(e(a).pageIndex,o=>(t(),r("div",{key:o.id},[o.isShow?(t(),h(k(e(u)[o.name]),{key:0,prop:o.prop},null,8,["prop"])):v("",!0)]))),128))])]),_:1})])]),_:1})])}}});export{Eo as default};
