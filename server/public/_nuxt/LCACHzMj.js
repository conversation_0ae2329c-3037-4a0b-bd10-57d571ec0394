import{_ as d}from"./DH5ElBC0.js";import{a as x,cm as h,_ as f}from"./DjCZV6kq.js";import{l,m as v,M as e,N as c,O as n,_ as y,aq as b,a1 as L,a0 as i,X as N,u as g,a7 as k,Z as _}from"./Dp9aCaJ6.js";import{_ as u}from"./DlAUqK2U.js";const $={class:"flex"},B={class:"flex bg-body p-[8px] rounded-[10px]"},P=l({__name:"index",props:{navList:{}},setup(m){const t=x(),p=v(()=>{const o=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.activePath||o});return(o,r)=>{const s=d;return e(),c("div",$,[n("div",B,[(e(!0),c(y,null,b(o.navList,a=>(e(),L(s,{key:a.path,to:a.path},{default:i(()=>[n("div",{class:N(["text-xl px-[17.5px] py-[5px] rounded-[7px] min-w-[85px] text-center font-bold",{"text-white bg-primary":a.path==g(p)}])},k(a.name),3)]),_:2},1032,["to"]))),128))])])}}}),w=u(P,[["__scopeId","data-v-585af5cb"]]),C={class:"h-full flex flex-col"},I={class:"flex-1 min-h-0"},V=l({__name:"layout",setup(m){const t=[{name:"智能体",path:"/application/layout/robot"},{name:"智能体形象",path:"/application/layout/digital"},{name:"知识库",path:"/application/layout/kb"}];return(p,o)=>{const r=w,s=h,a=f;return e(),c("div",null,[_(a,{name:"default"},{default:i(()=>[n("div",C,[_(r,{class:"px-[20px] pt-[16px]","nav-list":t}),n("div",I,[_(s)])])]),_:1})])}}}),F=u(V,[["__scopeId","data-v-8125b6d3"]]);export{F as default};
