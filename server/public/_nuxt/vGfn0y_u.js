import{_ as S}from"./DUH6hp3a.js";import{E as U}from"./BolOpO9r.js";import{E as k}from"./Bu00qTZy.js";import{a as z,E as B}from"./C3h7pD7s.js";import{cn as C,e as I}from"./DyU4wb-Q.js";import{E as L,a as M}from"./WW2eh4dw.js";/* empty css        *//* empty css        */import"./kwcMwiMO.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{w as N,b as O}from"./BX6AFV3P.js";import{l as Q,b as _,F as D,M as p,N as f,Z as s,a0 as i,O as e,u as l,a7 as v,_ as P,aq as $,y as j,a1 as q}from"./Dp9aCaJ6.js";import{_ as R}from"./DlAUqK2U.js";import"./DCa8BdSG.js";import"./DCTLXrZ8.js";import"./C27mmGOG.js";import"./BDGxxzzg.js";import"./9Bti1uB6.js";import"./CH_T-XC0.js";import"./9MAnF5ll.js";import"./Cv6HhfEG.js";import"./D4s3zOA5.js";import"./CQqsfOf-.js";import"./D-tOg06u.js";import"./Dt_kzei6.js";import"./DlKZEFPo.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";const Z={class:"flex flex-col gap-2"},A={class:"flex items-center gap-2"},G={class:"flex items-center cursor-pointer text-[#999999]"},H={class:"flex gap-4 items-center pl-3"},J={class:"flex items-center gap-2"},K={class:"flex items-center cursor-pointer text-[#999999]"},T={class:"flex gap-4 items-center pl-3"},W={class:"flex items-center gap-2 mb-2"},X={class:"flex items-center cursor-pointer text-[#999999]"},Y={class:"flex gap-4 items-center"},ee={class:"flex items-center gap-2 mb-2"},te={class:"flex items-center cursor-pointer text-[#999999]"},oe={class:"flex gap-4 items-center"},se=Q({__name:"sd-options",props:{modelValue:{type:Object,default:{step:"",sampling:"",seed:"",cfg:""}}},emits:["update:modelValue"],setup(g,{emit:x}){const V=x,h=g,{modelValue:n}=C(h,V),d=_([]),m=_("1");return D(async()=>{N().then(c=>{d.value=c})}),(c,t)=>{const r=S,a=U,u=k,w=B,E=z,b=I,F=L,y=M;return p(),f("div",null,[s(y,{modelValue:l(m),"onUpdate:modelValue":t[5]||(t[5]=o=>j(m)?m.value=o:null),class:"complex_params"},{default:i(()=>[s(F,{title:"高级参数",name:"1"},{title:i(()=>t[6]||(t[6]=[e("div",{class:"flex items-center gap-2"},[e("span",null,"高级参数")],-1)])),default:i(()=>[e("div",Z,[e("div",null,[e("div",A,[t[7]||(t[7]=e("span",null,"绘画步数",-1)),s(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"越低：细节简练，耗时更短；越高：细节丰富，耗时变长；注*步数过高可能产生细节扭曲"},{reference:i(()=>[e("div",G,[s(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",H,[s(u,{modelValue:l(n).step,"onUpdate:modelValue":t[0]||(t[0]=o=>l(n).step=o),step:1,max:150},null,8,["modelValue"]),e("span",null,v(l(n).step),1)])]),e("div",null,[e("div",J,[t[8]||(t[8]=e("span",null,"文本强度",-1)),s(a,{placement:"right","show-arrow":!1,transition:"custom-popover",width:200,trigger:"hover",content:"低：淡化输入的特征，淡化风格；高：强化输入的特征，强化风格；最佳使用区间7-12，推荐不超过15"},{reference:i(()=>[e("div",K,[s(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",T,[s(u,{modelValue:l(n).cfg_scale,"onUpdate:modelValue":t[1]||(t[1]=o=>l(n).cfg_scale=o),step:.5,max:30},null,8,["modelValue"]),e("span",null,v(l(n).cfg_scale),1)])]),e("div",null,[e("div",W,[t[9]||(t[9]=e("span",null,"采样模式",-1)),s(a,{placement:"right","show-arrow":!1,transition:"custom-popover",width:200,trigger:"hover",content:"靠前的采样（euler）：适合动漫，细节简练，快速；靠后的采样（DPM）：适合写实，细节丰富，较慢"},{reference:i(()=>[e("div",X,[s(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",Y,[s(E,{modelValue:l(n).sampler_name,"onUpdate:modelValue":t[2]||(t[2]=o=>l(n).sampler_name=o),placeholder:"请选择采样模式"},{default:i(()=>[(p(!0),f(P,null,$(l(d),o=>(p(),q(w,{key:o.name,label:o.name,value:o.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),e("div",null,[e("div",ee,[t[10]||(t[10]=e("span",null,"随机种子",-1)),s(a,{placement:"right","show-arrow":!1,transition:"custom-popover",width:200,trigger:"hover",content:"每次生成图的初始画布，种子、提示词、参数和模型相同的情况下，可复原绘画结果"},{reference:i(()=>[e("div",te,[s(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",oe,[s(b,{modelValue:l(n).seed,"onUpdate:modelValue":t[3]||(t[3]=o=>l(n).seed=o),type:"number",min:-1,maxlength:18,onFocus:t[4]||(t[4]=o=>l(O)()),placeholder:"请选择采样模式"},null,8,["modelValue"])])])])]),_:1})]),_:1},8,["modelValue"])])}}}),Oe=R(se,[["__scopeId","data-v-f7ef8053"]]);export{Oe as default};
