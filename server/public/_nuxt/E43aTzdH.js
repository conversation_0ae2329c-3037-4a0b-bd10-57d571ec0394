import{b as L,s as A,m as S,e as z,E as j,_ as O}from"./BBthjZaB.js";import{_ as T}from"./BWJbVfKg.js";import{E as W}from"./JOeTFF2G.js";import{_ as $}from"./C-hk8zs7.js";import{W as q}from"./Yu4so_0x.js";/* empty css        */import{u as U}from"./BzHpYkC6.js";import{e as X}from"./DXfM9A1O.js";import{l as F,ak as J,b as v,m as M,M as x,N as b,Z as n,a0 as d,O as t,a2 as Q,u as o,X as R,a7 as V,y as Z,a1 as G,a4 as k,a6 as H}from"./Dp9aCaJ6.js";import{_ as K}from"./DlAUqK2U.js";import"./vwcCUF2-.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./DBBnJi1E.js";import"./DiPtOrlW.js";import"./B4Y9LdPA.js";const Y={class:"app-center"},ee={class:"2xl:max-w-[880px] xl:max-w-[780px] lg:max-w-[680px] max-w-[680px] search w-full mt-4"},te={class:"card-item px-[20px] py-[24px] bg-body rounded-[12px] gap-y-[20px]"},oe={class:"flex justify-between"},ae={class:"mr-2"},se={class:"text-tx-primary text-xl"},ne={class:"text-tx-secondary text-base mt-[10px]"},re={class:"mt-[20px] show-btn"},le={class:"flex-none py-[8px]"},ie={key:0,class:"flex flex-col justify-center items-center w-full"},ce=F({__name:"index",async setup(pe){let i,u;const{getImageUrl:_}=L(),{data:r}=([i,u]=J(()=>U(()=>S({id:11}),{transform:a=>{const e=JSON.parse(a.data)[0].content;return e.data=e.data.filter(l=>l.is_show==1)||[],e},default(){return[]}},"$SwzDsnqXhy")),i=await i,u(),i),c=v(""),p=v(r.value.data||[]),C=()=>{const a=c.value.trim().toLowerCase();a===""?p.value=r.value.data:p.value=r.value.data.filter(e=>{const l=e.title.toLowerCase(),m=e.desc.toLowerCase();return l.includes(a)||m.includes(a)})},P={4e3:{rowPerView:6},2e3:{rowPerView:5},1800:{rowPerView:4},1600:{rowPerView:4},1440:{rowPerView:4},1360:{rowPerView:3},1280:{rowPerView:3},1024:{rowPerView:3}},E=M(()=>a=>{switch(a){case 1:return"text-black";case 2:return"text-white";case 3:return"text-primary"}});return(a,e)=>{const l=z,m=T,N=j,f=W,B=$,D=q,I=O;return x(),b("div",null,[n(I,{name:"default"},{default:d(()=>{var w,h,y,g;return[t("div",Y,[t("header",{class:"header flex-none flex flex-col justify-center items-center m-[16px] rounded-[12px] overflow-hidden",style:Q({"background-image":`url(${o(_)((w=o(r))==null?void 0:w.pc_background)})`})},[t("div",{class:R(["font-medium 2xl:text-[50px] xl:text-[40px] lg:text-[36px] text-[36px]",o(E)((h=o(r))==null?void 0:h.pc_text_color)])},V((y=o(r))==null?void 0:y.pc_title),3),t("div",ee,[n(l,{size:"large",class:"2xl:h-[54px] xl:h-[48px] lg:h-[44px]",style:{"--el-border-color":"transparent"},modelValue:o(c),"onUpdate:modelValue":e[0]||(e[0]=s=>Z(c)?c.value=s:null),"prefix-icon":o(A),placeholder:"输入您想搜索的应用",onInput:C},null,8,["modelValue","prefix-icon"])])],4),t("div",null,[n(D,{ref:"waterFull",delay:100,list:o(p),width:364,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:P},{item:d(({item:s})=>[t("div",te,[n(B,{to:s.pcLink.path},{default:d(()=>[t("div",oe,[t("div",ae,[t("div",se,V(s.title),1),t("div",ne,[s.desc?(x(),G(m,{key:0,content:s.desc,teleported:!0,effect:"light",placement:"right"},null,8,["content"])):k("",!0)]),t("div",re,[n(N,{class:"!border-none",type:"primary"},{default:d(()=>e[1]||(e[1]=[H(" 去试试 ")])),_:1})])]),t("div",le,[n(f,{src:o(_)(s.image),class:"w-[82px] h-[82px] rounded-[18px]"},null,8,["src"])])])]),_:2},1032,["to"])])]),_:1},8,["list"]),((g=o(p))==null?void 0:g.length)===0?(x(),b("div",ie,[n(f,{class:"w-[200px] h-[200px]",src:o(X)},null,8,["src"]),e[2]||(e[2]=t("div",{class:"text-tx-regular mb-4"}," 找不到更多应用了～ ",-1))])):k("",!0)])])]}),_:1})])}}}),Ne=K(ce,[["__scopeId","data-v-7e173420"]]);export{Ne as default};
