import{K as g,bp as y,bq as k,br as C,bs as $,M as w,O as S,P as B}from"./B5S_Er7H.js";import{l as m,m as E,M as l,N as a,O as f,V as i,X as t,u as e,a4 as r,a1 as I,a3 as M,a7 as p}from"./Dp9aCaJ6.js";const o={success:"icon-success",warning:"icon-warning",error:"icon-error",info:"icon-info"},d={[o.success]:y,[o.warning]:k,[o.error]:C,[o.info]:$},N=g({title:{type:String,default:""},subTitle:{type:String,default:""},icon:{type:String,values:["success","warning","info","error"],default:"info"}}),h=m({name:"ElResult"}),P=m({...h,props:N,setup(v){const b=v,n=w("result"),c=E(()=>{const s=b.icon,u=s&&o[s]?o[s]:"icon-info",_=d[u]||d["icon-info"];return{class:u,component:_}});return(s,u)=>(l(),a("div",{class:t(e(n).b())},[f("div",{class:t(e(n).e("icon"))},[i(s.$slots,"icon",{},()=>[e(c).component?(l(),I(M(e(c).component),{key:0,class:t(e(c).class)},null,8,["class"])):r("v-if",!0)])],2),s.title||s.$slots.title?(l(),a("div",{key:0,class:t(e(n).e("title"))},[i(s.$slots,"title",{},()=>[f("p",null,p(s.title),1)])],2)):r("v-if",!0),s.subTitle||s.$slots["sub-title"]?(l(),a("div",{key:1,class:t(e(n).e("subtitle"))},[i(s.$slots,"sub-title",{},()=>[f("p",null,p(s.subTitle),1)])],2)):r("v-if",!0),s.$slots.extra?(l(),a("div",{key:2,class:t(e(n).e("extra"))},[i(s.$slots,"extra")],2)):r("v-if",!0)],2))}});var R=S(P,[["__file","result.vue"]]);const D=B(R);export{D as E};
