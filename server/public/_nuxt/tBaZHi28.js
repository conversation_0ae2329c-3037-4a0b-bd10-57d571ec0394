import{_ as R}from"./CDqp_Mv_.js";import{h as q,b as B,o as C,e as I}from"./BsMqt_su.js";import{E as A}from"./DlEdU_jR.js";import{E as M,a as S}from"./9uRGTfH7.js";import"./DP2rzg_V.js";import"./C5OYjNdj.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{l as D,b as z,M as y,N as F,Z as s,a0 as d,O as a,u as l,y as G,a7 as g,a6 as p,a1 as O,a4 as P}from"./Dp9aCaJ6.js";const T={class:"pt-[10px]"},Z={class:"w-80"},j={class:"flex-1 min-w-0"},H={class:"w-full flex"},J={class:"flex-1 max-w-[320px]"},K={class:"flex-1 min-w-0"},L={class:"w-full flex"},Q={class:"flex-1 max-w-[320px]"},W={class:"w-80"},X={class:"form-tips"},Y={class:"w-80"},$={class:"w-80"},ue=D({__name:"search-config",props:{modelValue:{}},emits:["update:modelValue"],setup(c,{emit:U}){const t=q(c,"modelValue",U),m=z({});return B(),(le,e)=>{const E=R,i=C,n=A,r=M,u=S,k=I;return y(),F("div",T,[s(i,{label:"AI模型",prop:"model_id"},{default:d(()=>[a("div",Z,[s(E,{class:"flex-1",id:l(t).model_id,"onUpdate:id":e[0]||(e[0]=o=>l(t).model_id=o),sub_id:l(t).model_sub_id,"onUpdate:sub_id":e[1]||(e[1]=o=>l(t).model_sub_id=o),configs:l(m),"onUpdate:configs":e[2]||(e[2]=o=>G(m)?m.value=o:null),"set-default":!1,disabled:""},null,8,["id","sub_id","configs"])])]),_:1}),s(i,{label:"相似度",required:"",prop:"search_similarity"},{default:d(()=>[a("div",j,[a("div",H,[a("div",J,[s(n,{min:0,max:1,step:.001,modelValue:l(t).search_similarity,"onUpdate:modelValue":e[3]||(e[3]=o=>l(t).search_similarity=o)},null,8,["modelValue"])])]),e[10]||(e[10]=a("div",{class:"form-tips"}," 输入0-1之间的数值，支持3位小数点；高相似度推荐设置0.8以上 ",-1))])]),_:1}),s(i,{label:"单次搜索数量",required:"",prop:"search_limits"},{default:d(()=>[a("div",K,[a("div",L,[a("div",Q,[s(n,{min:0,max:20,modelValue:l(t).search_limits,"onUpdate:modelValue":e[4]||(e[4]=o=>l(t).search_limits=o)},null,8,["modelValue"])])]),e[11]||(e[11]=a("div",{class:"form-tips"},"默认设置为5，请输入0-20之间的整数数值",-1))])]),_:1}),s(i,{label:"温度属性",required:"",prop:"temperature"},{default:d(()=>{var o,_,f,x,V,b,v,w;return[a("div",W,[s(n,{modelValue:l(t).temperature,"onUpdate:modelValue":e[5]||(e[5]=N=>l(t).temperature=N),min:(_=(o=l(m))==null?void 0:o.range)==null?void 0:_[0],max:(x=(f=l(m))==null?void 0:f.range)==null?void 0:x[1],step:.1},null,8,["modelValue","min","max"]),a("div",X," 输入"+g((b=(V=l(m))==null?void 0:V.range)==null?void 0:b[0])+"-"+g((w=(v=l(m))==null?void 0:v.range)==null?void 0:w[1])+"之间的数值，支持1位小数点；",1)])]}),_:1}),s(i,{label:"空搜索回复"},{default:d(()=>[s(u,{modelValue:l(t).search_empty_type,"onUpdate:modelValue":e[6]||(e[6]=o=>l(t).search_empty_type=o)},{default:d(()=>[s(r,{label:1},{default:d(()=>e[12]||(e[12]=[p(" AI回复")])),_:1}),s(r,{label:2},{default:d(()=>e[13]||(e[13]=[p(" 自定义回复")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(t).search_empty_type===2?(y(),O(i,{key:0},{default:d(()=>[a("div",Y,[s(k,{modelValue:l(t).search_empty_text,"onUpdate:modelValue":e[7]||(e[7]=o=>l(t).search_empty_text=o),placeholder:"请输入回复内容，当搜索匹配不上内容时，直接回复填写的内容",type:"textarea",autosize:{minRows:6,maxRows:6},maxlength:1e3,"show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1})):P("",!0),s(i,{label:"上下文",required:"",prop:"context_num"},{default:d(()=>[a("div",$,[s(n,{modelValue:l(t).context_num,"onUpdate:modelValue":e[8]||(e[8]=o=>l(t).context_num=o),min:0,max:5,step:1},null,8,["modelValue"]),e[14]||(e[14]=a("div",{class:"form-tips"},"生成文本的最大长度，取值范围为0~5之间的整数",-1))])]),_:1}),s(i,{label:"文件解析",prop:"support_file"},{default:d(()=>[a("div",null,[s(u,{modelValue:l(t).support_file,"onUpdate:modelValue":e[9]||(e[9]=o=>l(t).support_file=o)},{default:d(()=>[s(r,{label:1},{default:d(()=>e[15]||(e[15]=[p(" 启用 ")])),_:1}),s(r,{label:0},{default:d(()=>e[16]||(e[16]=[p(" 关闭 ")])),_:1})]),_:1},8,["modelValue"]),e[17]||(e[17]=a("div",{class:"form-tips"},"开启后对话时支持上传文件，需消耗大量token，按需启用",-1))])]),_:1})])}}});export{ue as _};
