import{_ as n}from"./CQG-tpkc.js";import{_ as i}from"./D713wtmw.js";import{cR as m}from"./Br7V4jS9.js";import{l as s,M as p,N as c,Z as t}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./CLFpwOKH.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./CCBJtVSv.js";import"./LFZ9apcG.js";import"./B9mz6c9C.js";import"./e7d6W8dp.js";import"./Cs-EFu39.js";import"./9Bti1uB6.js";const y=s({__name:"index",setup(a){const e=m(),o=()=>{e.setSetting({key:"showDrawer",value:!0})};return(_,l)=>{const r=n;return p(),c("div",{class:"setting flex cursor-pointer h-full items-center pl-2",onClick:o},[t(r,{size:20,name:"local-icon-dianpu_fengge"}),t(i)])}}});export{y as default};
