import{_ as F}from"./DUH6hp3a.js";import{E as U}from"./BolOpO9r.js";import{E as y}from"./Bu00qTZy.js";import{a as k,E as q}from"./C3h7pD7s.js";import{cn as z,e as Q}from"./DyU4wb-Q.js";import{E as j,a as B}from"./WW2eh4dw.js";/* empty css        *//* empty css        */import"./kwcMwiMO.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{a as C,b as I}from"./BX6AFV3P.js";import{l as N,b as O,M as d,N as _,Z as o,a0 as i,O as e,u as l,a7 as c,_ as M,aq as S,y as L,a1 as $}from"./Dp9aCaJ6.js";import{_ as D}from"./DlAUqK2U.js";import"./DCa8BdSG.js";import"./DCTLXrZ8.js";import"./C27mmGOG.js";import"./BDGxxzzg.js";import"./9Bti1uB6.js";import"./CH_T-XC0.js";import"./9MAnF5ll.js";import"./Cv6HhfEG.js";import"./D4s3zOA5.js";import"./CQqsfOf-.js";import"./D-tOg06u.js";import"./Dt_kzei6.js";import"./DlKZEFPo.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";const P={class:"flex flex-col gap-2"},R={class:"flex items-center gap-2"},Z={class:"flex items-center cursor-pointer text-[#999999]"},A={class:"flex gap-4 items-center pl-3"},G={class:"flex items-center gap-2"},H={class:"flex items-center cursor-pointer text-[#999999]"},J={class:"flex gap-4 items-center mt-2"},K={class:"flex items-center gap-2 mb-2"},T={class:"flex items-center cursor-pointer text-[#999999]"},W={class:"flex gap-4 items-center pl-3"},X={class:"flex items-center gap-2 mb-2"},Y={class:"flex items-center cursor-pointer text-[#999999]"},ee={class:"flex gap-4 items-center pl-3"},te={class:"flex items-center gap-2 mb-2"},oe={class:"flex items-center cursor-pointer text-[#999999]"},le={class:"flex gap-4 items-center"},se=N({__name:"mj-options",props:{modelValue:{type:Object,default:{seed:"",iw:1,q:1,s:100,c:0}}},emits:["update:modelValue"],setup(f,{emit:v}){const g=v,x=f,{modelValue:n}=z(x,g),m=O("1");return(ne,t)=>{const r=F,a=U,p=y,V=q,w=k,h=Q,E=j,b=B;return d(),_("div",null,[o(b,{modelValue:l(m),"onUpdate:modelValue":t[6]||(t[6]=s=>L(m)?m.value=s:null),class:"complex_params"},{default:i(()=>[o(E,{title:"高级参数",name:"1"},{title:i(()=>t[7]||(t[7]=[e("div",{class:"flex items-center gap-2"},[e("span",null,"高级参数")],-1)])),default:i(()=>[e("div",P,[e("div",null,[e("div",R,[t[8]||(t[8]=e("span",null,"参考图权重",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"设置生成图片时垫图的权重，值越大越像垫图，取值范围0.5-2， 默认值1"},{reference:i(()=>[e("div",Z,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",A,[o(p,{modelValue:l(n).iw,"onUpdate:modelValue":t[0]||(t[0]=s=>l(n).iw=s),step:.1,min:.5,max:2},null,8,["modelValue"]),e("span",null,c(l(n).iw),1)])]),e("div",null,[e("div",G,[t[9]||(t[9]=e("span",null,"图片质量",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"设置图片的质量，越大质量越高，取值范围0.25 - 1，默认值1"},{reference:i(()=>[e("div",H,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",J,[o(w,{modelValue:l(n).q,"onUpdate:modelValue":t[1]||(t[1]=s=>l(n).q=s),placeholder:"请选择图片质量"},{default:i(()=>[(d(!0),_(M,null,S(l(C).mj_quality,(s,u)=>(d(),$(V,{key:u,label:s,value:u},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),e("div",null,[e("div",K,[t[10]||(t[10]=e("span",null,"风格化值",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"设置生成图片时的风格化程度，值越大，风格化的程度越高，取值范围0-1000， 默认值100"},{reference:i(()=>[e("div",T,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",W,[o(p,{modelValue:l(n).s,"onUpdate:modelValue":t[2]||(t[2]=s=>l(n).s=s),step:1,max:1e3,min:0},null,8,["modelValue"]),e("span",null,c(l(n).s),1)])]),e("div",null,[e("div",X,[t[11]||(t[11]=e("span",null,"混乱值",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"本参数会控制生成4张图的差别， 值越大，生成4张图的区别越大，值越小,生成的4张图越接近，取值范围0-100， 默认值0"},{reference:i(()=>[e("div",Y,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",ee,[o(p,{modelValue:l(n).c,"onUpdate:modelValue":t[3]||(t[3]=s=>l(n).c=s),step:1,max:100,min:0},null,8,["modelValue"]),e("span",null,c(l(n).c),1)])]),e("div",null,[e("div",te,[t[12]||(t[12]=e("span",null,"随机种子",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"种子用于指定生成效果，可以用于生成套图，保障生成的一系列图片保持同一种风格"},{reference:i(()=>[e("div",oe,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",le,[o(h,{modelValue:l(n).seed,"onUpdate:modelValue":t[4]||(t[4]=s=>l(n).seed=s),type:"number",min:-1,maxlength:18,onFocus:t[5]||(t[5]=s=>l(I)()),placeholder:"请输入seed种子编号"},null,8,["modelValue"])])])])]),_:1})]),_:1},8,["modelValue"])])}}}),Se=D(se,[["__scopeId","data-v-b052342e"]]);export{Se as default};
