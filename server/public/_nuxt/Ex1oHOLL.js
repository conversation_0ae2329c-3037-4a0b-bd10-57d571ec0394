import{_ as c}from"./4QYjDMhx.js";import{k as r,cU as l}from"./CylNgAGi.js";import{i as m}from"./B0brwBYu.js";import{l as u,m as n,M as _,a1 as f,a0 as x,V as k,W as d,u as a}from"./Dp9aCaJ6.js";const E=u({__name:"index",props:{to:{}},setup(s){const t=s,e=n(()=>{let o="";return m(t.to)?o=t.to:r(t.to)&&(o=t.to.path),l(o)}),i=n(()=>e.value?{...t,target:"blank",to:r(t.to)?t.to.path:t.to}:t);return(o,h)=>{const p=c;return _(),f(p,d(a(i),{external:a(e)}),{default:x(()=>[k(o.$slots,"default")]),_:3},16,["external"])}}});export{E as _};
