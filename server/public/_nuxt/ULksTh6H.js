import{e as h,o as k,p as y}from"./CzTOiozM.js";import"./DP2rzg_V.js";/* empty css        */import{P as E}from"./BGN3kN5U.js";import{l as V,j as f,r as b,s as F,M as I,N as P,Z as l,a0 as s,u as o}from"./Dp9aCaJ6.js";const D=V({__name:"create-api",emits:["confirm"],setup(B,{expose:i,emit:u}){const c=u,n=f(),r=f(),t=b({name:""}),_=F({name:[{required:!0,message:"请输入接口名称"}]}),d=()=>{var e;(e=r.value)==null||e.open()},v=()=>{var e;(e=r.value)==null||e.close()},R=async()=>{var e;await((e=n.value)==null?void 0:e.validate()),c("confirm",t)};return i({open:d,close:v}),(e,a)=>{const w=h,x=k,C=y;return I(),P("div",null,[l(E,{ref_key:"popupRef",ref:r,title:"创建API",async:!0,width:"550px",onConfirm:R,onClose:a[1]||(a[1]=m=>{var p;return(p=o(n))==null?void 0:p.resetFields()})},{default:s(()=>[l(C,{ref_key:"formRef",ref:n,model:o(t),rules:o(_),"label-width":"84px"},{default:s(()=>[l(x,{label:"接口名称",prop:"name"},{default:s(()=>[l(w,{modelValue:o(t).name,"onUpdate:modelValue":a[0]||(a[0]=m=>o(t).name=m),placeholder:"接口名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},512)])}}});export{D as _};
