import{E as w,a as I}from"./DLovRgFC.js";import{j as k,da as B,db as F,dc as g,e as M,o as O,p as P}from"./DjCZV6kq.js";import{_ as j}from"./D3UglwDI.js";import{P as N}from"./BEaZAbYp.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{l as U,j as m,r as q,M as D,a1 as $,a0 as l,O as u,Z as o,u as n}from"./Dp9aCaJ6.js";const h={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},ee=U({__name:"bindmobilePop",emits:["close"],setup(z,{expose:f,emit:G}){const a=m(),_=k(),t=q({mobile:"",code:""}),i=m(),p=m(),b=()=>{a.value.open()},v={mobile:[{required:!0,message:"请输入手机号"}],code:[{required:!0,message:"请输入验证码"}]},E=async()=>{var s,e;await((s=p.value)==null?void 0:s.validateField(["mobile"])),await B({scene:F.BIND_MOBILE,mobile:t.mobile}),(e=i.value)==null||e.start()},x=async()=>{await g({type:"bind",...t}),a.value.close()};return f({open:b}),(s,e)=>{const y=w,V=I,d=M,c=O,C=j,R=P,S=N;return D(),$(S,{ref_key:"popRef",ref:a,title:n(_).userInfo.mobile?"点击更改":"立即绑定",async:"","confirm-button-text":"确认更改",onConfirm:x,onClose:e[2]||(e[2]=r=>s.$emit("close"))},{default:l(()=>[u("div",null,[o(R,{ref_key:"formRef",ref:p,size:"large",model:n(t),rules:v},{default:l(()=>[o(c,{prop:"mobile"},{default:l(()=>[o(d,{modelValue:n(t).mobile,"onUpdate:modelValue":e[0]||(e[0]=r=>n(t).mobile=r),placeholder:"请输入手机号"},{prepend:l(()=>[o(V,{"model-value":"+86",style:{width:"80px"}},{default:l(()=>[o(y,{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(c,{prop:"code"},{default:l(()=>[o(d,{modelValue:n(t).code,"onUpdate:modelValue":e[1]||(e[1]=r=>n(t).code=r),placeholder:"请输入验证码"},{suffix:l(()=>[u("div",h,[o(C,{ref_key:"verificationCodeRef",ref:i,onClickGet:E},null,512)])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["title"])}}});export{ee as _};
