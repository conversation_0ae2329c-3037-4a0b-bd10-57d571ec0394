import{E as R,a as I}from"./Bwx2-5pK.js";import{j as N,dt as U,cS as P,da as T,db as j,e as q,o as G,E as M,p as $,du as D}from"./B1MekKW7.js";import{_ as z}from"./CW46YUqZ.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{u as W}from"./CcPlX2kz.js";import{l as Z,j as E,r as A,m as x,M as r,N as H,O as c,Z as l,a0 as s,u as o,a1 as i,a4 as d,a6 as f}from"./Dp9aCaJ6.js";const J={class:"pt-[10px]"},K={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},Q={class:"flex"},X={class:"flex-1 flex"},me=Z({__name:"mobile-login",setup(Y){const m=N(),_=E(),V={mobile:[{required:!0,message:"请输入手机号"}],password:[{required:!0,message:"请输入密码"}],code:[{required:!0,message:"请输入验证码"}]},t=A({code:"",mobile:"",password:"",scene:1,terminal:U}),y=x(()=>t.scene===3),k=x(()=>t.scene===1),w=a=>{t.scene=a},v=E(),b=async()=>{var a,e;await((a=_.value)==null?void 0:a.validateField(["account"])),await T({scene:j.LOGIN,mobile:t.mobile}),(e=v.value)==null||e.start()},{lockFn:C,isLock:L}=W(async()=>{var e;await((e=_.value)==null?void 0:e.validate());const a=await D(t);m.login(a.token),location.reload(),await m.getUser(),m.toggleShowLogin(!1)});return(a,e)=>{const S=R,F=I,g=q,p=G,B=z,u=M,O=$;return r(),H("div",J,[c("div",null,[l(O,{ref_key:"formRef",ref:_,size:"large",model:o(t),rules:V},{default:s(()=>[l(p,{prop:"mobile"},{default:s(()=>[l(g,{modelValue:o(t).mobile,"onUpdate:modelValue":e[0]||(e[0]=n=>o(t).mobile=n),placeholder:"请输入手机号"},{prepend:s(()=>[l(F,{"model-value":"+86",style:{width:"80px"}},{default:s(()=>[l(S,{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(k)?(r(),i(p,{key:0,prop:"password"},{default:s(()=>[l(g,{modelValue:o(t).password,"onUpdate:modelValue":e[1]||(e[1]=n=>o(t).password=n),type:"password","show-password":"",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):d("",!0),o(y)?(r(),i(p,{key:1,prop:"code"},{default:s(()=>[l(g,{modelValue:o(t).code,"onUpdate:modelValue":e[2]||(e[2]=n=>o(t).code=n),placeholder:"请输入验证码"},{suffix:s(()=>[c("div",K,[l(B,{ref_key:"verificationCodeRef",ref:v,onClickGet:b},null,512)])]),_:1},8,["modelValue"])]),_:1})):d("",!0),c("div",Q,[c("div",X,[o(k)?(r(),i(u,{key:0,type:"primary",link:"",onClick:e[3]||(e[3]=n=>w(3))},{default:s(()=>e[6]||(e[6]=[f(" 手机验证码登录 ")])),_:1})):d("",!0),o(y)?(r(),i(u,{key:1,type:"primary",link:"",onClick:e[4]||(e[4]=n=>w(1))},{default:s(()=>e[7]||(e[7]=[f(" 手机密码登录 ")])),_:1})):d("",!0)]),o(k)?(r(),i(u,{key:0,link:"",onClick:e[5]||(e[5]=n=>o(m).setLoginPopupType(o(P).FORGOT_PWD_MOBILE))},{default:s(()=>e[8]||(e[8]=[f(" 忘记密码？ ")])),_:1})):d("",!0)]),l(p,{class:"my-[30px]"},{default:s(()=>[l(u,{class:"w-full",type:"primary",loading:o(L),onClick:o(C)},{default:s(()=>e[9]||(e[9]=[f(" 登录 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])])])}}});export{me as _};
