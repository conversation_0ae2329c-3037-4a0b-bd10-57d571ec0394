import{E as Pe}from"./C6mdKn2J.js";import{K as ve,X as le,aL as ye,Y as Te,ap as te,aS as fe,aM as Ee,Z as me,V as Me,aO as ze,af as Ie,U as pe,aR as Ve,M as ge,O as Be,a5 as Xe,aP as Fe,S as Ye,aw as Ke,P as Re}from"./CylNgAGi.js";import{b as xe,E as Ae}from"./DJ1U04Bw.js";import{I as $e,b as Q,F as Ue,n as se,m as v,j as We,i as Oe,c as re,l as ae,r as Ce,C as ce,M as N,N as K,Z as Le,a0 as we,O as J,a7 as je,u as t,X as R,a2 as G,H as he,p as He,q as qe,a1 as ie,a4 as ne,_ as oe,aq as ue}from"./Dp9aCaJ6.js";import{d as Se}from"./qb8OCGLI.js";const Ne=Symbol("sliderContextKey"),Ze=ve({modelValue:{type:le([Number,Array]),default:0},id:{type:String,default:void 0},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},showInput:Boolean,showInputControls:{type:Boolean,default:!0},size:ye,inputSize:ye,showStops:Boolean,showTooltip:{type:Boolean,default:!0},formatTooltip:{type:le(Function),default:void 0},disabled:Boolean,range:Boolean,vertical:Boolean,height:String,debounce:{type:Number,default:300},label:{type:String,default:void 0},rangeStartLabel:{type:String,default:void 0},rangeEndLabel:{type:String,default:void 0},formatValueText:{type:le(Function),default:void 0},tooltipClass:{type:String,default:void 0},placement:{type:String,values:xe,default:"top"},marks:{type:le(Object)},validateEvent:{type:Boolean,default:!0},...Te(["ariaLabel"])}),de=e=>me(e)||$e(e)&&e.every(me),_e={[te]:de,[fe]:de,[Ee]:de},Ge=(e,l,u)=>{const a=Q();return Ue(async()=>{e.range?(Array.isArray(e.modelValue)?(l.firstValue=Math.max(e.min,e.modelValue[0]),l.secondValue=Math.min(e.max,e.modelValue[1])):(l.firstValue=e.min,l.secondValue=e.max),l.oldValue=[l.firstValue,l.secondValue]):(typeof e.modelValue!="number"||Number.isNaN(e.modelValue)?l.firstValue=e.min:l.firstValue=Math.min(e.max,Math.max(e.min,e.modelValue)),l.oldValue=l.firstValue),Me(window,"resize",u),await se(),u()}),{sliderWrapper:a}},Je=e=>v(()=>e.marks?Object.keys(e.marks).map(Number.parseFloat).sort((u,a)=>u-a).filter(u=>u<=e.max&&u>=e.min).map(u=>({point:u,position:(u-e.min)*100/(e.max-e.min),mark:e.marks[u]})):[]),Qe=(e,l,u)=>{const{form:a,formItem:s}=ze(),c=We(),i=Q(),V=Q(),w={firstButton:i,secondButton:V},d=v(()=>e.disabled||(a==null?void 0:a.disabled)||!1),f=v(()=>Math.min(l.firstValue,l.secondValue)),o=v(()=>Math.max(l.firstValue,l.secondValue)),x=v(()=>e.range?`${100*(o.value-f.value)/(e.max-e.min)}%`:`${100*(l.firstValue-e.min)/(e.max-e.min)}%`),C=v(()=>e.range?`${100*(f.value-e.min)/(e.max-e.min)}%`:"0%"),P=v(()=>e.vertical?{height:e.height}:{}),T=v(()=>e.vertical?{height:x.value,bottom:C.value}:{width:x.value,left:C.value}),M=()=>{c.value&&(l.sliderSize=c.value[`client${e.vertical?"Height":"Width"}`])},I=m=>{const h=e.min+m*(e.max-e.min)/100;if(!e.range)return i;let E;return Math.abs(f.value-h)<Math.abs(o.value-h)?E=l.firstValue<l.secondValue?"firstButton":"secondButton":E=l.firstValue>l.secondValue?"firstButton":"secondButton",w[E]},S=m=>{const h=I(m);return h.value.setPosition(m),h},p=m=>{l.firstValue=m,g(e.range?[f.value,o.value]:m)},k=m=>{l.secondValue=m,e.range&&g([f.value,o.value])},g=m=>{u(te,m),u(fe,m)},y=async()=>{await se(),u(Ee,e.range?[f.value,o.value]:e.modelValue)},j=m=>{var h,E,q,Z,_,O;if(d.value||l.dragging)return;M();let Y=0;if(e.vertical){const L=(q=(E=(h=m.touches)==null?void 0:h.item(0))==null?void 0:E.clientY)!=null?q:m.clientY;Y=(c.value.getBoundingClientRect().bottom-L)/l.sliderSize*100}else{const L=(O=(_=(Z=m.touches)==null?void 0:Z.item(0))==null?void 0:_.clientX)!=null?O:m.clientX,z=c.value.getBoundingClientRect().left;Y=(L-z)/l.sliderSize*100}if(!(Y<0||Y>100))return S(Y)};return{elFormItem:s,slider:c,firstButton:i,secondButton:V,sliderDisabled:d,minValue:f,maxValue:o,runwayStyle:P,barStyle:T,resetSize:M,setPosition:S,emitChange:y,onSliderWrapperPrevent:m=>{var h,E;((h=w.firstButton.value)!=null&&h.dragging||(E=w.secondButton.value)!=null&&E.dragging)&&m.preventDefault()},onSliderClick:m=>{j(m)&&y()},onSliderDown:async m=>{const h=j(m);h&&(await se(),h.value.onButtonDown(m))},setFirstValue:p,setSecondValue:k}},{left:De,down:el,right:ll,up:tl,home:al,end:nl,pageUp:ol,pageDown:rl}=Ie,sl=(e,l,u)=>{const a=Q(),s=Q(!1),c=v(()=>l.value instanceof Function),i=v(()=>c.value&&l.value(e.modelValue)||e.modelValue),V=Se(()=>{u.value&&(s.value=!0)},50),w=Se(()=>{u.value&&(s.value=!1)},50);return{tooltip:a,tooltipVisible:s,formatValue:i,displayTooltip:V,hideTooltip:w}},il=(e,l,u)=>{const{disabled:a,min:s,max:c,step:i,showTooltip:V,precision:w,sliderSize:d,formatTooltip:f,emitChange:o,resetSize:x,updateDragging:C}=Oe(Ne),{tooltip:P,tooltipVisible:T,formatValue:M,displayTooltip:I,hideTooltip:S}=sl(e,f,V),p=Q(),k=v(()=>`${(e.modelValue-s.value)/(c.value-s.value)*100}%`),g=v(()=>e.vertical?{bottom:k.value}:{left:k.value}),y=()=>{l.hovering=!0,I()},j=()=>{l.hovering=!1,l.dragging||S()},H=n=>{a.value||(n.preventDefault(),Y(n),window.addEventListener("mousemove",L),window.addEventListener("touchmove",L),window.addEventListener("mouseup",z),window.addEventListener("touchend",z),window.addEventListener("contextmenu",z),p.value.focus())},A=n=>{a.value||(l.newPosition=Number.parseFloat(k.value)+n/(c.value-s.value)*100,$(l.newPosition),o())},W=()=>{A(-i.value)},m=()=>{A(i.value)},h=()=>{A(-i.value*4)},E=()=>{A(i.value*4)},q=()=>{a.value||($(0),o())},Z=()=>{a.value||($(100),o())},_=n=>{let b=!0;[De,el].includes(n.key)?W():[ll,tl].includes(n.key)?m():n.key===al?q():n.key===nl?Z():n.key===rl?h():n.key===ol?E():b=!1,b&&n.preventDefault()},O=n=>{let b,X;return n.type.startsWith("touch")?(X=n.touches[0].clientY,b=n.touches[0].clientX):(X=n.clientY,b=n.clientX),{clientX:b,clientY:X}},Y=n=>{l.dragging=!0,l.isClick=!0;const{clientX:b,clientY:X}=O(n);e.vertical?l.startY=X:l.startX=b,l.startPosition=Number.parseFloat(k.value),l.newPosition=l.startPosition},L=n=>{if(l.dragging){l.isClick=!1,I(),x();let b;const{clientX:X,clientY:U}=O(n);e.vertical?(l.currentY=U,b=(l.startY-l.currentY)/d.value*100):(l.currentX=X,b=(l.currentX-l.startX)/d.value*100),l.newPosition=l.startPosition+b,$(l.newPosition)}},z=()=>{l.dragging&&(setTimeout(()=>{l.dragging=!1,l.hovering||S(),l.isClick||$(l.newPosition),o()},0),window.removeEventListener("mousemove",L),window.removeEventListener("touchmove",L),window.removeEventListener("mouseup",z),window.removeEventListener("touchend",z),window.removeEventListener("contextmenu",z))},$=async n=>{if(n===null||Number.isNaN(+n))return;n<0?n=0:n>100&&(n=100);const b=100/((c.value-s.value)/i.value);let U=Math.round(n/b)*b*(c.value-s.value)*.01+s.value;U=Number.parseFloat(U.toFixed(w.value)),U!==e.modelValue&&u(te,U),!l.dragging&&e.modelValue!==l.oldValue&&(l.oldValue=e.modelValue),await se(),l.dragging&&I(),P.value.updatePopper()};return re(()=>l.dragging,n=>{C(n)}),{disabled:a,button:p,tooltip:P,tooltipVisible:T,showTooltip:V,wrapperStyle:g,formatValue:M,handleMouseEnter:y,handleMouseLeave:j,onButtonDown:H,onKeyDown:_,setPosition:$}},ul=(e,l,u,a)=>({stops:v(()=>{if(!e.showStops||e.min>e.max)return[];if(e.step===0)return[];const i=(e.max-e.min)/e.step,V=100*e.step/(e.max-e.min),w=Array.from({length:i-1}).map((d,f)=>(f+1)*V);return e.range?w.filter(d=>d<100*(u.value-e.min)/(e.max-e.min)||d>100*(a.value-e.min)/(e.max-e.min)):w.filter(d=>d>100*(l.firstValue-e.min)/(e.max-e.min))}),getStopStyle:i=>e.vertical?{bottom:`${i}%`}:{left:`${i}%`}}),dl=(e,l,u,a,s,c)=>{const i=d=>{s(te,d),s(fe,d)},V=()=>e.range?![u.value,a.value].every((d,f)=>d===l.oldValue[f]):e.modelValue!==l.oldValue,w=()=>{var d,f;e.min>e.max&&pe("Slider","min should not be greater than max.");const o=e.modelValue;e.range&&Array.isArray(o)?o[1]<e.min?i([e.min,e.min]):o[0]>e.max?i([e.max,e.max]):o[0]<e.min?i([e.min,o[1]]):o[1]>e.max?i([o[0],e.max]):(l.firstValue=o[0],l.secondValue=o[1],V()&&(e.validateEvent&&((d=c==null?void 0:c.validate)==null||d.call(c,"change").catch(x=>Ve())),l.oldValue=o.slice())):!e.range&&typeof o=="number"&&!Number.isNaN(o)&&(o<e.min?i(e.min):o>e.max?i(e.max):(l.firstValue=o,V()&&(e.validateEvent&&((f=c==null?void 0:c.validate)==null||f.call(c,"change").catch(x=>Ve())),l.oldValue=o)))};w(),re(()=>l.dragging,d=>{d||w()}),re(()=>e.modelValue,(d,f)=>{l.dragging||Array.isArray(d)&&Array.isArray(f)&&d.every((o,x)=>o===f[x])&&l.firstValue===d[0]&&l.secondValue===d[1]||w()},{deep:!0}),re(()=>[e.min,e.max],()=>{w()})},ml=ve({modelValue:{type:Number,default:0},vertical:Boolean,tooltipClass:String,placement:{type:String,values:xe,default:"top"}}),cl={[te]:e=>me(e)},vl=["tabindex"],fl=ae({name:"ElSliderButton"}),gl=ae({...fl,props:ml,emits:cl,setup(e,{expose:l,emit:u}){const a=e,s=ge("slider"),c=Ce({hovering:!1,dragging:!1,isClick:!1,startX:0,currentX:0,startY:0,currentY:0,startPosition:0,newPosition:0,oldValue:a.modelValue}),{disabled:i,button:V,tooltip:w,showTooltip:d,tooltipVisible:f,wrapperStyle:o,formatValue:x,handleMouseEnter:C,handleMouseLeave:P,onButtonDown:T,onKeyDown:M,setPosition:I}=il(a,c,u),{hovering:S,dragging:p}=ce(c);return l({onButtonDown:T,onKeyDown:M,setPosition:I,hovering:S,dragging:p}),(k,g)=>(N(),K("div",{ref_key:"button",ref:V,class:R([t(s).e("button-wrapper"),{hover:t(S),dragging:t(p)}]),style:G(t(o)),tabindex:t(i)?-1:0,onMouseenter:g[0]||(g[0]=(...y)=>t(C)&&t(C)(...y)),onMouseleave:g[1]||(g[1]=(...y)=>t(P)&&t(P)(...y)),onMousedown:g[2]||(g[2]=(...y)=>t(T)&&t(T)(...y)),onTouchstart:g[3]||(g[3]=(...y)=>t(T)&&t(T)(...y)),onFocus:g[4]||(g[4]=(...y)=>t(C)&&t(C)(...y)),onBlur:g[5]||(g[5]=(...y)=>t(P)&&t(P)(...y)),onKeydown:g[6]||(g[6]=(...y)=>t(M)&&t(M)(...y))},[Le(t(Ae),{ref_key:"tooltip",ref:w,visible:t(f),placement:k.placement,"fallback-placements":["top","bottom","right","left"],"stop-popper-mouse-event":!1,"popper-class":k.tooltipClass,disabled:!t(d),persistent:""},{content:we(()=>[J("span",null,je(t(x)),1)]),default:we(()=>[J("div",{class:R([t(s).e("button"),{hover:t(S),dragging:t(p)}])},null,2)]),_:1},8,["visible","placement","popper-class","disabled"])],46,vl))}});var ke=Be(gl,[["__file","button.vue"]]);const bl=ve({mark:{type:le([String,Object]),default:void 0}});var yl=ae({name:"ElSliderMarker",props:bl,setup(e){const l=ge("slider"),u=v(()=>he(e.mark)?e.mark:e.mark.label),a=v(()=>he(e.mark)?void 0:e.mark.style);return()=>He("div",{class:l.e("marks-text"),style:a.value},u.value)}});const Vl=["id","role","aria-label","aria-labelledby"],wl={key:1},hl=ae({name:"ElSlider"}),Sl=ae({...hl,props:Ze,emits:_e,setup(e,{expose:l,emit:u}){const a=e,s=ge("slider"),{t:c}=Xe(),i=Ce({firstValue:0,secondValue:0,oldValue:0,dragging:!1,sliderSize:1}),{elFormItem:V,slider:w,firstButton:d,secondButton:f,sliderDisabled:o,minValue:x,maxValue:C,runwayStyle:P,barStyle:T,resetSize:M,emitChange:I,onSliderWrapperPrevent:S,onSliderClick:p,onSliderDown:k,setFirstValue:g,setSecondValue:y}=Qe(a,i,u),{stops:j,getStopStyle:H}=ul(a,i,x,C),{inputId:A,isLabeledByFormItem:W}=Fe(a,{formItemContext:V}),m=Ye(),h=v(()=>a.inputSize||m.value),E=v(()=>a.label||a.ariaLabel||c("el.slider.defaultLabel",{min:a.min,max:a.max})),q=v(()=>a.range?a.rangeStartLabel||c("el.slider.defaultRangeStartLabel"):E.value),Z=v(()=>a.formatValueText?a.formatValueText(n.value):`${n.value}`),_=v(()=>a.rangeEndLabel||c("el.slider.defaultRangeEndLabel")),O=v(()=>a.formatValueText?a.formatValueText(b.value):`${b.value}`),Y=v(()=>[s.b(),s.m(m.value),s.is("vertical",a.vertical),{[s.m("with-input")]:a.showInput}]),L=Je(a);dl(a,i,x,C,u,V);const z=v(()=>{const r=[a.min,a.max,a.step].map(F=>{const D=`${F}`.split(".")[1];return D?D.length:0});return Math.max.apply(null,r)}),{sliderWrapper:$}=Ge(a,i,M),{firstValue:n,secondValue:b,sliderSize:X}=ce(i),U=r=>{i.dragging=r};return qe(Ne,{...ce(a),sliderSize:X,disabled:o,precision:z,emitChange:I,resetSize:M,updateDragging:U}),Ke({from:"label",replacement:"aria-label",version:"2.8.0",scope:"el-slider",ref:"https://element-plus.org/en-US/component/slider.html"},v(()=>!!a.label)),l({onSliderClick:p}),(r,F)=>{var D,be;return N(),K("div",{id:r.range?t(A):void 0,ref_key:"sliderWrapper",ref:$,class:R(t(Y)),role:r.range?"group":void 0,"aria-label":r.range&&!t(W)?t(E):void 0,"aria-labelledby":r.range&&t(W)?(D=t(V))==null?void 0:D.labelId:void 0,onTouchstart:F[2]||(F[2]=(...B)=>t(S)&&t(S)(...B)),onTouchmove:F[3]||(F[3]=(...B)=>t(S)&&t(S)(...B))},[J("div",{ref_key:"slider",ref:w,class:R([t(s).e("runway"),{"show-input":r.showInput&&!r.range},t(s).is("disabled",t(o))]),style:G(t(P)),onMousedown:F[0]||(F[0]=(...B)=>t(k)&&t(k)(...B)),onTouchstart:F[1]||(F[1]=(...B)=>t(k)&&t(k)(...B))},[J("div",{class:R(t(s).e("bar")),style:G(t(T))},null,6),Le(ke,{id:r.range?void 0:t(A),ref_key:"firstButton",ref:d,"model-value":t(n),vertical:r.vertical,"tooltip-class":r.tooltipClass,placement:r.placement,role:"slider","aria-label":r.range||!t(W)?t(q):void 0,"aria-labelledby":!r.range&&t(W)?(be=t(V))==null?void 0:be.labelId:void 0,"aria-valuemin":r.min,"aria-valuemax":r.range?t(b):r.max,"aria-valuenow":t(n),"aria-valuetext":t(Z),"aria-orientation":r.vertical?"vertical":"horizontal","aria-disabled":t(o),"onUpdate:modelValue":t(g)},null,8,["id","model-value","vertical","tooltip-class","placement","aria-label","aria-labelledby","aria-valuemin","aria-valuemax","aria-valuenow","aria-valuetext","aria-orientation","aria-disabled","onUpdate:modelValue"]),r.range?(N(),ie(ke,{key:0,ref_key:"secondButton",ref:f,"model-value":t(b),vertical:r.vertical,"tooltip-class":r.tooltipClass,placement:r.placement,role:"slider","aria-label":t(_),"aria-valuemin":t(n),"aria-valuemax":r.max,"aria-valuenow":t(b),"aria-valuetext":t(O),"aria-orientation":r.vertical?"vertical":"horizontal","aria-disabled":t(o),"onUpdate:modelValue":t(y)},null,8,["model-value","vertical","tooltip-class","placement","aria-label","aria-valuemin","aria-valuemax","aria-valuenow","aria-valuetext","aria-orientation","aria-disabled","onUpdate:modelValue"])):ne("v-if",!0),r.showStops?(N(),K("div",wl,[(N(!0),K(oe,null,ue(t(j),(B,ee)=>(N(),K("div",{key:ee,class:R(t(s).e("stop")),style:G(t(H)(B))},null,6))),128))])):ne("v-if",!0),t(L).length>0?(N(),K(oe,{key:2},[J("div",null,[(N(!0),K(oe,null,ue(t(L),(B,ee)=>(N(),K("div",{key:ee,style:G(t(H)(B.position)),class:R([t(s).e("stop"),t(s).e("marks-stop")])},null,6))),128))]),J("div",{class:R(t(s).e("marks"))},[(N(!0),K(oe,null,ue(t(L),(B,ee)=>(N(),ie(t(yl),{key:ee,mark:B.mark,style:G(t(H)(B.position))},null,8,["mark","style"]))),128))],2)],64)):ne("v-if",!0)],38),r.showInput&&!r.range?(N(),ie(t(Pe),{key:0,ref:"input","model-value":t(n),class:R(t(s).e("input")),step:r.step,disabled:t(o),controls:r.showInputControls,min:r.min,max:r.max,debounce:r.debounce,size:t(h),"onUpdate:modelValue":t(g),onChange:t(I)},null,8,["model-value","class","step","disabled","controls","min","max","debounce","size","onUpdate:modelValue","onChange"])):ne("v-if",!0)],42,Vl)}}});var kl=Be(Sl,[["__file","slider.vue"]]);const Nl=Re(kl);export{Nl as E};
