import{K as h,M as y,N as b,O as g,P as S,Q as P}from"./BsMqt_su.js";import{b as $,F as B,c as E,l as p,M as n,N as u,a1 as w,u as s,a4 as N,X as m,t as I,_ as c,aq as k,W as v,V as _,al as T,Z as C}from"./Dp9aCaJ6.js";const M=(r,o=0)=>{if(o===0)return r;const a=$(!1);let t=0;const l=()=>{t&&clearTimeout(t),t=window.setTimeout(()=>{a.value=r.value},o)};return B(l),E(()=>r.value,e=>{e?l():a.value=e}),a},V=h({animated:{type:Boolean,default:!1},count:{type:Number,default:1},rows:{type:Number,default:3},loading:{type:Boolean,default:!0},throttle:{type:Number}}),z=h({variant:{type:String,values:["circle","rect","h1","h3","text","caption","p","image","button"],default:"text"}}),F=p({name:"ElSkeletonItem"}),L=p({...F,props:z,setup(r){const o=y("skeleton");return(a,t)=>(n(),u("div",{class:m([s(o).e("item"),s(o).e(a.variant)])},[a.variant==="image"?(n(),w(s(b),{key:0})):N("v-if",!0)],2))}});var i=g(L,[["__file","skeleton-item.vue"]]);const R=p({name:"ElSkeleton"}),q=p({...R,props:V,setup(r,{expose:o}){const a=r,t=y("skeleton"),l=M(I(a,"loading"),a.throttle);return o({uiLoading:l}),(e,K)=>s(l)?(n(),u("div",v({key:0,class:[s(t).b(),s(t).is("animated",e.animated)]},e.$attrs),[(n(!0),u(c,null,k(e.count,d=>(n(),u(c,{key:d},[e.loading?_(e.$slots,"template",{key:d},()=>[C(i,{class:m(s(t).is("first")),variant:"p"},null,8,["class"]),(n(!0),u(c,null,k(e.rows,f=>(n(),w(i,{key:f,class:m([s(t).e("paragraph"),s(t).is("last",f===e.rows&&e.rows>1)]),variant:"p"},null,8,["class"]))),128))]):N("v-if",!0)],64))),128))],16)):_(e.$slots,"default",T(v({key:1},e.$attrs)))}});var H=g(q,[["__file","skeleton.vue"]]);const W=S(H,{SkeletonItem:i});P(i);export{W as E};
