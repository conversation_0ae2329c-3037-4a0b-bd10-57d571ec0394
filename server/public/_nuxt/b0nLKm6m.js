import{_ as C}from"./4QYjDMhx.js";import{E as b,o as p,e as x,p as B,f as F}from"./CylNgAGi.js";import{a as N}from"./CH-eeB8d.js";import{E as R}from"./C1u9TGpz.js";import{E as U}from"./Za7Ic34B.js";import{l as P,b as m,r as q,M as D,a1 as I,a0 as o,O as s,Z as a,a6 as V,u as e,y as v}from"./Dp9aCaJ6.js";const S={class:"w-[280px]"},L={class:"w-[280px]"},M={class:"text-[12px]"},G=P({__name:"apply",emits:["closePop"],setup(O,{expose:_,emit:c}){const n=m(!1),g=c,f=m(),r=m({name:"",mobile:""}),u=m(0),k=q({name:[{required:!0,message:"请输入真实名称",trigger:"blur"}],mobile:[{required:!0,message:"请输入手机号码",trigger:"blur"}]}),y=()=>{n.value=!0},w=async i=>{if(!i){console.log(i);return}await i.validate(),u.value==1?(await N(r.value),d()):F.msgError("请先阅读并同意《用户分销协议》！")},d=()=>{n.value=!1,g("closePop")};return _({open:y}),(i,l)=>{const E=C;return D(),I(e(U),{modelValue:e(n),"onUpdate:modelValue":l[4]||(l[4]=t=>v(n)?n.value=t:null),width:"450px",title:"申请成为分销商","close-on-click-modal":!1,onClose:d},{footer:o(()=>[s("div",null,[a(e(b),{onClick:d},{default:o(()=>l[7]||(l[7]=[V("取消")])),_:1}),a(e(b),{type:"primary",onClick:l[3]||(l[3]=t=>w(e(f)))},{default:o(()=>l[8]||(l[8]=[V("提交申请 ")])),_:1})])]),default:o(()=>[s("div",null,[a(e(B),{ref_key:"ruleFormRef",ref:f,rules:e(k),model:e(r),"label-width":"80px"},{default:o(()=>[a(e(p),{label:"真实名称",prop:"name"},{default:o(()=>[s("div",S,[a(e(x),{modelValue:e(r).name,"onUpdate:modelValue":l[0]||(l[0]=t=>e(r).name=t),placeholder:"请输入名称"},null,8,["modelValue"])])]),_:1}),a(e(p),{label:"联系方式",prop:"mobile"},{default:o(()=>[s("div",L,[a(e(x),{modelValue:e(r).mobile,"onUpdate:modelValue":l[1]||(l[1]=t=>e(r).mobile=t),placeholder:"请输入手机号码"},null,8,["modelValue"])])]),_:1}),a(e(p),null,{default:o(()=>[a(e(R),{"true-label":1,"false-label":0,modelValue:e(u),"onUpdate:modelValue":l[2]||(l[2]=t=>v(u)?u.value=t:null)},{default:o(()=>[s("div",M,[l[6]||(l[6]=s("span",{class:"text-[#999999]"},"阅读并同意",-1)),a(E,{to:"/policy/distribution"},{default:o(()=>l[5]||(l[5]=[s("span",{class:"text-primary"}," 《用户分销协议》 ",-1)])),_:1})])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["rules","model"])])]),_:1},8,["modelValue"])}}});export{G as _};
