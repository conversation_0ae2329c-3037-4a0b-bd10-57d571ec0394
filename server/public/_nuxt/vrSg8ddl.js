import{a as M,E as U}from"./DW2yWX9x.js";import{b as w,cn as z,f as p,o as B,e as N}from"./CLVDtxqA.js";import{E as I}from"./DOeZo8uY.js";import{E as L}from"./BFvazox5.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import{F as O}from"./CsEkN0LW.js";import{l as q,M as i,N as V,Z as o,a0 as s,u as t,_ as A,aq as P,y as r,O as v,a1 as R}from"./Dp9aCaJ6.js";const Z={class:"flex flex-1"},j={class:"flex flex-1"},h=q({__name:"text-setting",props:{font:{},fontSize:{},fontColor:{},strokeColor:{}},emits:["update:font","update:fontSize","update:fontColor","update:strokeColor"],setup(x,{emit:C}){const b=w(),k=x,E=C,{font:f,fontSize:m,fontColor:n,strokeColor:a}=z(k,E),g=async d=>{p.loading("正在加载字体中，请稍等...");try{await new O(d).load(null,100*1e3),f.value=d}catch(l){console.log(l),p.msgError("字体加载失败，请重试")}finally{p.closeLoading()}};return(d,l)=>{const y=U,S=M,u=B,F=I,c=L,_=N;return i(),V("div",null,[o(u,{label:"设置字体"},{default:s(()=>[o(S,{class:"w-full","model-value":t(f),onChange:g},{default:s(()=>[(i(!0),V(A,null,P(t(b).fontList,e=>(i(),R(y,{key:e.code,label:e.name,value:e.code},null,8,["label","value"]))),128))]),_:1},8,["model-value"])]),_:1}),o(u,{label:"字体大小"},{default:s(()=>[o(F,{modelValue:t(m),"onUpdate:modelValue":l[0]||(l[0]=e=>r(m)?m.value=e:null),min:20,max:160,"controls-position":"right"},null,8,["modelValue"])]),_:1}),o(u,{label:"字体颜色"},{default:s(()=>[v("div",Z,[o(c,{modelValue:t(n),"onUpdate:modelValue":l[1]||(l[1]=e=>r(n)?n.value=e:null)},null,8,["modelValue"]),o(_,{class:"flex-1 ml-[10px]",modelValue:t(n),"onUpdate:modelValue":l[2]||(l[2]=e=>r(n)?n.value=e:null),readonly:""},null,8,["modelValue"])])]),_:1}),o(u,{label:"描边颜色"},{default:s(()=>[v("div",j,[o(c,{modelValue:t(a),"onUpdate:modelValue":l[3]||(l[3]=e=>r(a)?a.value=e:null)},null,8,["modelValue"]),o(_,{class:"flex-1 ml-[10px]",modelValue:t(a),"onUpdate:modelValue":l[4]||(l[4]=e=>r(a)?a.value=e:null),readonly:""},null,8,["modelValue"])])]),_:1})])}}});export{h as _};
