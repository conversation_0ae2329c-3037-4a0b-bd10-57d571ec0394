import{u as d,a as s,E as R}from"./vwcCUF2-.js";import{d as f}from"./C3aKVaZD.js";import{K as U,aq as $,M as T,O as V,$ as D,aY as O,P as H}from"./BBthjZaB.js";import{l as g,m as n,b as I,M as u,a1 as K,a0 as c,N as L,a7 as v,X as M,u as p,a4 as b,V as m,W as q,a6 as z}from"./Dp9aCaJ6.js";const F=U({trigger:d.trigger,placement:f.placement,disabled:d.disabled,visible:s.visible,transition:s.transition,popperOptions:f.popperOptions,tabindex:f.tabindex,content:s.content,popperStyle:s.popperStyle,popperClass:s.popperClass,enterable:{...s.enterable,default:!0},effect:{...s.effect,default:"light"},teleported:s.teleported,title:String,width:{type:[String,Number],default:150},offset:{type:Number,default:void 0},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},showArrow:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},"onUpdate:visible":{type:Function}}),W={"update:visible":t=>$(t),"before-enter":()=>!0,"before-leave":()=>!0,"after-enter":()=>!0,"after-leave":()=>!0},X="onUpdate:visible",Y=g({name:"ElPopover"}),j=g({...Y,props:F,emits:W,setup(t,{expose:r,emit:a}){const o=t,w=n(()=>o[X]),i=T("popover"),l=I(),y=n(()=>{var e;return(e=p(l))==null?void 0:e.popperRef}),P=n(()=>[{width:D(o.width)},o.popperStyle]),E=n(()=>[i.b(),o.popperClass,{[i.m("plain")]:!!o.content}]),C=n(()=>o.transition===`${i.namespace.value}-fade-in-linear`),N=()=>{var e;(e=l.value)==null||e.hide()},S=()=>{a("before-enter")},B=()=>{a("before-leave")},k=()=>{a("after-enter")},A=()=>{a("update:visible",!1),a("after-leave")};return r({popperRef:y,hide:N}),(e,_)=>(u(),K(p(R),q({ref_key:"tooltipRef",ref:l},e.$attrs,{trigger:e.trigger,placement:e.placement,disabled:e.disabled,visible:e.visible,transition:e.transition,"popper-options":e.popperOptions,tabindex:e.tabindex,content:e.content,offset:e.offset,"show-after":e.showAfter,"hide-after":e.hideAfter,"auto-close":e.autoClose,"show-arrow":e.showArrow,"aria-label":e.title,effect:e.effect,enterable:e.enterable,"popper-class":p(E),"popper-style":p(P),teleported:e.teleported,persistent:e.persistent,"gpu-acceleration":p(C),"onUpdate:visible":p(w),onBeforeShow:S,onBeforeHide:B,onShow:k,onHide:A}),{content:c(()=>[e.title?(u(),L("div",{key:0,class:M(p(i).e("title")),role:"title"},v(e.title),3)):b("v-if",!0),m(e.$slots,"default",{},()=>[z(v(e.content),1)])]),default:c(()=>[e.$slots.reference?m(e.$slots,"reference",{key:0}):b("v-if",!0)]),_:3},16,["trigger","placement","disabled","visible","transition","popper-options","tabindex","content","offset","show-after","hide-after","auto-close","show-arrow","aria-label","effect","enterable","popper-class","popper-style","teleported","persistent","gpu-acceleration","onUpdate:visible"]))}});var G=V(j,[["__file","popover.vue"]]);const h=(t,r)=>{const a=r.arg||r.value,o=a==null?void 0:a.popperRef;o&&(o.triggerRef=t)};var J={mounted(t,r){h(t,r)},updated(t,r){h(t,r)}};const Q="popover",Z=O(J,Q),re=H(G,{directive:Z});export{re as E};
