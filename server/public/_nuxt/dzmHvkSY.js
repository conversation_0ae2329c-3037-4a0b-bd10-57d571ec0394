import{E as l}from"./DTIryUWu.js";import{b as c,_}from"./Br7V4jS9.js";/* empty css        */import u from"./BtjDAWvW.js";import{l as d,M as t,N as r,Z as p,a0 as m,O as i,_ as f,aq as x,u as e,a1 as h,a3 as k,a4 as v}from"./Dp9aCaJ6.js";import"./Dw89qU0m.js";import"./CBkeCdOF.js";import"./B4zJW-6l.js";import"./B9mz6c9C.js";import"./CCBJtVSv.js";import"./DVYAUH4R.js";import"./rgjptJRl.js";import"./V6VzAR3J.js";/* empty css        */import"./DlAUqK2U.js";import"./C6GIihZw.js";import"./C05Cd-H5.js";import"./CKHVg3VA.js";import"./BYb6fCsX.js";import"./CtBRWm-a.js";import"./C3sn0ick.js";import"./Bl6ZnX2R.js";import"./DCTLXrZ8.js";import"./LFZ9apcG.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./g1_v1W9U.js";import"./DkF3Ju39.js";import"./CN8DIg3d.js";import"./CQG-tpkc.js";import"./C6OVyO68.js";/* empty css        */import"./D72pFiwL.js";import"./JPruwdx6.js";import"./dsHDlwYU.js";import"./CXUUfEOz.js";import"./9Bti1uB6.js";/* empty css        */import"./Cl_fs4jJ.js";import"./BQs8y7a2.js";import"./I4Y7aOf-.js";import"./xxuACs5W.js";import"./CYgEYzPG.js";import"./BE7GAo-z.js";import"./ojqmL5xl.js";import"./C_jh3IY4.js";import"./Cs-EFu39.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";import"./ZkgGCeqT.js";import"./Cv1u9LLW.js";import"./Ditelj6Y.js";const N={class:"h-full"},S={class:"index"},Eo=d({__name:"index",setup(g){const a=c();return(y,B)=>{const n=l,s=_;return t(),r("div",null,[p(s,{name:"single-row"},{default:m(()=>[i("div",N,[p(n,null,{default:m(()=>[i("div",S,[(t(!0),r(f,null,x(e(a).pageIndex,o=>(t(),r("div",{key:o.id},[o.isShow?(t(),h(k(e(u)[o.name]),{key:0,prop:o.prop},null,8,["prop"])):v("",!0)]))),128))])]),_:1})])]),_:1})])}}});export{Eo as default};
