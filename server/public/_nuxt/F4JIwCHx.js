import{h as d,e as i,o as _,p as f}from"./DNaNbs6R.js";import"./DP2rzg_V.js";/* empty css        */import{l as V,M as x,N as h,O as l,Z as e,a0 as s,u as a}from"./Dp9aCaJ6.js";const E={class:"manual-import"},F={class:"py-4 flex flex-col"},v={class:"flex-1 min-w-0"},C=V({__name:"manual-doc",props:{modelValue:{}},emits:["update:modelValue"],setup(n,{emit:m}){const o=d(n,"modelValue",m);return(I,t)=>{const r=i,p=_,u=f;return x(),h("div",E,[l("div",F,[e(u,null,{default:s(()=>[e(p,null,{default:s(()=>[l("div",v,[e(r,{modelValue:a(o).question,"onUpdate:modelValue":t[0]||(t[0]=c=>a(o).question=c),placeholder:"请输入文本内容，10000个字以内。",type:"textarea",resize:"none",rows:15,maxlength:"10000"},null,8,["modelValue"])])]),_:1})]),_:1})])])}}});export{C as default};
