import{_ as g}from"./DMHEbzLi.js";import{j as k,b as D,cz as N,du as I,dv as R,cW as A,v as b}from"./DAgm18qP.js";import{u as x}from"./BE7GAo-z.js";import{E as L}from"./hOM-i1i4.js";import{l as w,C as S,F as G,k as M,M as c,N as i,O as r,aa as B,u as t,a1 as F,a4 as d,Z as U,_ as v}from"./Dp9aCaJ6.js";const O=k(),V=D(),T=()=>{const e=N(()=>({pending:!0,data:{}}),"$OO04Zgg9KB");return{state:e,getData:async()=>{try{e.value.pending=!0;const n=await I();e.value.data=n,e.value.pending=!1}catch(n){return e.value.pending=!1,Promise.reject(n)}}}};var a=(e=>(e[e.CODE_ERROR=-1]="CODE_ERROR",e[e.INVALID=0]="INVALID",e[e.NORMAL=1]="NORMAL",e[e.SCANNED_CODE=2]="SCANNED_CODE",e[e.LOGIN_FAIL=3]="LOGIN_FAIL",e[e.LOGIN_SUCCESS=4]="LOGIN_SUCCESS",e))(a||{});const h=e=>{const l=N(()=>1,"$u1hDQUKMyG"),n=async()=>{try{const o=await R({key:e.value.key});return l.value=o.status,l.value==4&&(j(o.user),p()),o}catch{l.value=3}},m=()=>{l.value=0},{start:u,end:p,result:f}=x(n,{key:"wx_login",totalTime:120*1e3,callback:m});return{status:l,start:u,end:p,result:f}},j=async e=>{const{login:l,setUser:n,toggleShowLogin:m,setLoginPopupType:u}=O;!e.mobile&&V.getLoginConfig.coerce_mobile?(O.temToken=e.token,u(A.BIND_MOBILE)):(l(e.token),n(e),m(!1),location.reload())},P={class:"pb-[10px]"},$={class:"flex flex-col items-center mt-[20px]"},z={class:"relative w-[180px] h-[180px]"},K={key:1,class:"absolute left-0 top-0 w-full h-full bg-mask",style:{background:"rgba(0, 0, 0, 0.5)"}},Z={class:"h-full text-primary flex justify-center items-center"},Y=w({__name:"weixin-login",setup(e){k();const{state:l,getData:n}=T(),{pending:m,data:u}=S(l.value),{start:p,end:f,status:o}=h(u),C=async()=>{o.value=a.NORMAL;try{await n(),p()}catch{o.value=a.CODE_ERROR}};return G(()=>{console.log("123"),C()}),M(()=>{f()}),(_,s)=>{const y=g,E=b;return c(),i("div",P,[r("div",$,[B((c(),i("div",z,[t(u).url?(c(),F(t(L),{key:0,src:t(u).url,class:"w-full h-full"},null,8,["src"])):d("",!0),t(o)==t(a).SCANNED_CODE?(c(),i("div",K,[r("div",Z,[U(y,{name:"el-icon-SuccessFilled",size:30})])])):d("",!0),t(o)==t(a).INVALID||t(o)==t(a).LOGIN_FAIL||t(o)==t(a).CODE_ERROR?(c(),i("div",{key:2,class:"absolute left-0 top-0 w-full h-full bg-overlay cursor-pointer",style:{background:"rgba(0, 0, 0, 0.5)"},onClick:C},s[0]||(s[0]=[r("div",{class:"h-full flex flex-col justify-center items-center text-white"},[r("div",null,"点击刷新")],-1)]))):d("",!0)])),[[E,t(m)]]),t(o)==t(a).SCANNED_CODE?(c(),i(v,{key:0},[s[1]||(s[1]=r("div",{class:"mt-3"},"扫码成功",-1)),s[2]||(s[2]=r("div",{class:"mt-5 text-error text-sm"}," 请在微信公众号中确认登录 ",-1))],64)):d("",!0),t(o)==t(a).INVALID?(c(),i(v,{key:1},[s[3]||(s[3]=r("div",{class:"mt-3 text-error"},"二维码失效",-1)),s[4]||(s[4]=r("div",{class:"mt-5 text-sm"},"请在点击二维码刷新",-1))],64)):d("",!0),t(o)==t(a).LOGIN_FAIL?(c(),i(v,{key:2},[s[5]||(s[5]=r("div",{class:"mt-3 text-error"},"登录失败，请重新登录",-1)),s[6]||(s[6]=r("div",{class:"mt-5 text-sm"},"请在点击二维码刷新",-1))],64)):d("",!0),t(o)==t(a).NORMAL||t(o)==t(a).LOGIN_SUCCESS||t(o)==t(a).CODE_ERROR?(c(),i(v,{key:3},[s[7]||(s[7]=r("div",{class:"mt-3"},"微信扫码登录/注册",-1)),s[8]||(s[8]=r("div",{class:"mt-5 text-tx-secondary text-sm"}," 首次扫码关注公众号后将自动注册新账号 ",-1))],64)):d("",!0)])])}}});export{Y as _};
