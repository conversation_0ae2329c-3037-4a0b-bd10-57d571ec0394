import{_ as z}from"./B_BP6MI7.js";import{E as N}from"./C-3P_06P.js";import{E as j}from"./C1aUxTFE.js";import{e as $,E as A}from"./BQ-RMI0l.js";import{E as G}from"./L7ewHh_h.js";/* empty css        *//* empty css        */import{useAiPPTStore as H}from"./hmDyEHSL.js";import{_ as L}from"./uEBsjo8L.js";import{l as q,j as D,k as F,M as o,N as n,O as t,Z as a,a0 as r,_ as g,aq as y,u as p,a7 as _,a4 as c,a1 as h,a6 as x,n as M}from"./Dp9aCaJ6.js";import{_ as Z}from"./DlAUqK2U.js";import"./R2n930gq.js";import"./B7SD2TCw.js";import"./DdkLcgv7.js";import"./Ci86EFhe.js";import"./BBQxRZuk.js";import"./BOZQs96k.js";import"./De57gGYj.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        *//* empty css        */import"./CcPlX2kz.js";const J={class:"h-full flex flex-col"},K={class:"px-[15px] py-[15px]"},Q={class:"flex items-center cursor-pointer"},W={class:"flex-1 min-h-0"},X={class:"py-[20px] max-w-[650px] mx-auto"},Y={class:"flex justify-end mb-[30px]"},tt={class:"outline-text !rounded-tr-none"},et={key:0,class:"flex mb-[20px]"},st={class:"outline-text !rounded-tl-none !bg-page !text-tx-primary"},ot={key:1,class:"flex mb-[20px]"},lt={class:"outline-text !rounded-tl-none !bg-page !text-tx-primary"},nt={key:2,class:"flex mb-[20px]"},at={class:"outline-text !rounded-tl-none !bg-page !text-tx-primary"},it={class:"mb-[30px] flex flex-col bg-white p-4 shadow-lighter rounded-[15px] h-[600px]"},rt={class:"flex-1 my-4 min-h-0 border border-br-light border-solid rounded-[6px]"},dt={key:0,class:"p-4"},pt={class:"p-4"},mt={class:"outline-item flex items-center"},ct={class:"ml-[10px] flex-1 min-w-0"},ut={class:"mt-4 flex"},_t={class:"flex-1 min-w-0 ml-[10px]"},xt={class:"flex items-center mb-[5px]"},ft={class:"ml-[15px]"},vt={key:2,class:"p-4 h-full flex text-tx-secondary items-center justify-center"},bt={class:"flex items-center"},gt={class:"ml-auto w-1/2"},yt=q({__name:"gen-outline",setup(ht){const l=H(),f=D(),E=()=>{l.showOutline=!1},U=async()=>{var e,m,u;const d=(m=(e=f.value)==null?void 0:e.wrapRef)==null?void 0:m.scrollHeight;(u=f.value)==null||u.setScrollTop(d)},B=async()=>{l.genOutline(),await M(),U()};let k={};const C=d=>{k=d,l.showTemplate=!0},S=async()=>{const{title:d,catalogs:e}=k;await l.genPPTSubmit({...l.options,title:d,catalogs:e}),l.showTemplate=!1,l.showOutline=!1};return F(()=>{l.isGenningOutline=!1}),(d,e)=>{const m=z,u=N,w=j,v=$,T=G,V=A;return o(),n("div",J,[t("div",K,[t("div",Q,[t("div",{class:"flex bg-body p-[5px] text-bold rounded-[50%] text-primary shadow-light",onClick:e[0]||(e[0]=s=>E())},[a(m,{name:"el-icon-Back",size:18})]),e[3]||(e[3]=t("div",{class:"text-xl flex-1 min-w-0 ml-[10px]"},"AIPPT",-1))])]),t("div",W,[a(T,{ref_key:"scrollbarRef",ref:f},{default:r(()=>[t("div",X,[(o(!0),n(g,null,y(p(l).outlineLists,(s,O)=>(o(),n("div",{key:O},[t("div",Y,[t("div",tt,_(s.prompt),1)]),t("div",null,[s.status>=0?(o(),n("div",et,[t("div",st," 您的内容【"+_(s.prompt)+"】大纲正在生成中... ",1)])):c("",!0),s.status==1?(o(),n("div",ot,[t("div",lt," 您的内容【"+_(s.prompt)+"】大纲已生成，来看看吧 ",1)])):c("",!0),s.status==2?(o(),n("div",nt,[t("div",at," 您的内容【"+_(s.prompt)+"】大纲生成失败了，请重试 ",1)])):c("",!0)]),t("div",it,[e[10]||(e[10]=t("div",{class:"flex items-center justify-between"},[t("div",{class:"font-bold"},"PPT大纲"),t("div",{class:"text-xs text-tx-secondary"}," 由AI生成，仅供参考 ")],-1)),t("div",rt,[s.status==0?(o(),n("div",dt,[a(u,{animated:"",rows:12})])):s.status==1?(o(),h(T,{key:1},{default:r(()=>[t("div",pt,[t("div",mt,[a(w,{size:"small"},{default:r(()=>e[4]||(e[4]=[x("主题")])),_:1}),t("div",ct,[a(v,{modelValue:s.title,"onUpdate:modelValue":i=>s.title=i},null,8,["modelValue","onUpdate:modelValue"])])]),t("div",ut,[a(w,{size:"small",class:"mt-[6px]"},{default:r(()=>e[5]||(e[5]=[x("大纲")])),_:1}),t("div",_t,[(o(!0),n(g,null,y(s.catalogs,(i,I)=>(o(),n("div",{key:I,class:"outline-item mb-[5px]"},[t("div",xt,[e[6]||(e[6]=t("span",{class:"mr-1"},"•",-1)),a(v,{modelValue:i.catalog,"onUpdate:modelValue":P=>i.catalog=P},null,8,["modelValue","onUpdate:modelValue"])]),t("div",ft,[(o(!0),n(g,null,y(i.sub_catalog,(P,b)=>(o(),n("div",{key:b,class:"flex items-center mb-[5px]"},[e[7]||(e[7]=t("span",{class:"mr-1"},"•",-1)),a(v,{modelValue:i.sub_catalog[b],"onUpdate:modelValue":R=>i.sub_catalog[b]=R},null,8,["modelValue","onUpdate:modelValue"])]))),128))])]))),128))])])])]),_:2},1024)):(o(),n("div",vt," 生成失败，请点击下方按钮重新生成 "))]),t("div",bt,[s.status>=1?(o(),h(V,{key:0,link:"",onClick:B},{icon:r(()=>[a(m,{name:"el-icon-RefreshRight",size:16})]),default:r(()=>[e[8]||(e[8]=x(" 重新生成 "))]),_:1})):c("",!0),t("div",gt,[s.status==1?(o(),h(V,{key:0,size:"large",type:"primary",class:"w-full",onClick:i=>C(s)},{default:r(()=>e[9]||(e[9]=[x(" 选择PPT模板 ")])),_:2},1032,["onClick"])):c("",!0)])])])]))),128))])]),_:1},512)]),a(L,{visible:p(l).showTemplate,"onUpdate:visible":e[1]||(e[1]=s=>p(l).showTemplate=s),"cover-id":p(l).options.cover_id,"onUpdate:coverId":e[2]||(e[2]=s=>p(l).options.cover_id=s),prompt:p(l).options.prompt,onConfirm:S},null,8,["visible","cover-id","prompt"])])}}}),Ft=Z(yt,[["__scopeId","data-v-aadca646"]]);export{Ft as default};
