import{E as V}from"./DZCM63qd.js";import{_ as B}from"./DoX3YPdQ.js";import{_ as D}from"./B-qcbZcc.js";import{E as L}from"./B9cNZX9s.js";import{_ as T}from"./Beil-tKA.js";import{_ as N}from"./CN8DIg3d.js";import{a as M,l as O,b as P}from"./DjCZV6kq.js";/* empty css        *//* empty css        *//* empty css        */import{u as U}from"./BGGhXV_w.js";import z from"./DMyaKwjH.js";import{_ as F}from"./3hw4EGDR.js";import{_ as J}from"./CvWbd5nZ.js";import{g as Z}from"./Bo3PTL3c.js";import{u as j}from"./BR1oB9zx.js";import{l as G,b as d,ak as H,c as K,M as a,N as f,Z as m,a0 as s,O as e,u as o,y as w,_ as Q,aq as W,a1 as h,a4 as y,a7 as X}from"./Dp9aCaJ6.js";import{_ as Y}from"./DlAUqK2U.js";import"./DiWwZzDD.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";import"./CIYOwJ14.js";import"./9Bti1uB6.js";import"./BVQPS-VG.js";import"./wTC9-DVv.js";import"./ChpRm8Gn.js";/* empty css        */import"./DEDqeEEc.js";import"./Cv6HhfEG.js";import"./B_pz4WW8.js";/* empty css        */import"./AxFzUV4T.js";import"./HBTAmbWO.js";import"./PR4uin41.js";import"./B1pEJPcQ.js";import"./JyVczL8-.js";import"./CDkfsEbM.js";import"./DLovRgFC.js";import"./Bi_VJcob.js";import"./CQg2_aaP.js";import"./B2TveoJZ.js";import"./CxOVJjFn.js";import"./KiH-Yvip.js";import"./eQB23YlX.js";import"./DlKZEFPo.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./Du5U7mLs.js";import"./BNnETjxs.js";import"./DzYuAvKJ.js";import"./DD0FCuyG.js";import"./BZihmA1U.js";import"./BEaZAbYp.js";import"./DOuG0VWa.js";/* empty css        */import"./BJjGH7Fu.js";import"./DZBDerIP.js";import"./BR22aT_k.js";import"./CS8toSro.js";import"./BKIPvIQu.js";/* empty css        *//* empty css        */import"./g2kDj8ia.js";import"./DL-oL0AQ.js";import"./ColTdd6U.js";import"./D9gCjm8O.js";import"./DH5ElBC0.js";import"./BvJaxNwU.js";import"./CkQdVShy.js";import"./CK--Z7ot.js";/* empty css        */import"./C6_W4ts7.js";import"./N_Utb1UR.js";import"./DAN4DPEX.js";import"./DBtYF0Kw.js";import"./CwaY6y1B.js";import"./CaCQHsJc.js";import"./CZJuLJc7.js";/* empty css        *//* empty css        */import"./BJYjuaUx.js";import"./67xbGseh.js";import"./B7b3FqZI.js";import"./B0CgdP3M.js";import"./VkjGXvXm.js";import"./BMzinL7R.js";import"./CcPlX2kz.js";import"./Dp1MVQvw.js";import"./pV4frWKs.js";import"./DL_1zoQf.js";import"./BelTv3Cq.js";import"./DRGiEii_.js";import"./CGwGs1cz.js";import"./gGI_59qD.js";import"./CL4WVfkz.js";import"./Csw1O_my.js";import"./DHfpjDXo.js";import"./CLWdBz_m.js";import"./oqO9xdap.js";import"./D-yY_b61.js";import"./BwFyg5UG.js";import"./DPaLabPE.js";import"./BjB-LrA7.js";import"./DoCC4aNE.js";import"./CQAB3DDw.js";import"./Cpg3PDWZ.js";const tt={class:"h-full flex"},ot=["onClick"],rt=["src"],et={class:"ml-[8px] line-clamp-1"},it={class:"flex items-center cursor-pointer"},pt={class:"text-xl flex-1 min-w-0"},mt={class:"sm:h-full py-[16px] pr-[16px] flex flex-col sm:flex-row flex-1 min-w-0"},nt={class:"sm:h-full flex-1 min-w-0 min-h-0 bg-body rounded-[12px]"},at=G({__name:"setting",async setup(st){let c,x;const i=M(),u=O();P();const v=j();v.getRobot();const _=d(!1),l=d(i.query.id),{data:b,refresh:k}=([c,x]=H(()=>U(()=>Z({id:l.value}),{transform(t){return(t==null?void 0:t.category_id)===0&&(t.category_id=""),t},default(){return{}},lazy:!0},"$3nIwi7TB6J")),c=await c,x(),c),p=d("edit"),q=[{name:"智能体设置",icon:"el-icon-Setting",key:"edit"},{name:"发布智能体",key:"release",icon:"el-icon-Position"},{name:"对话数据",key:"dialogue",icon:"el-icon-ChatDotRound"},{name:"立即对话",key:"chat",icon:"el-icon-ChatLineRound"}],S=t=>{switch(t){case"chat":u.push({path:"/application/chat",query:{id:l.value}});break;default:u.replace({path:i.path,query:{...i.query,currentTab:t}})}},C=async t=>{_.value=!1,t!=i.query.id&&(l.value=t,await k(),u.replace({path:i.path,query:{...i.query,id:t}}))};return K(()=>i.query,t=>{p.value=t.currentTab||"edit"},{immediate:!0}),(t,n)=>{const g=V,R=B,A=D,E=L,$=T,I=N;return a(),f("div",tt,[m($,{modelValue:o(p),"onUpdate:modelValue":[n[1]||(n[1]=r=>w(p)?p.value=r:null),S],"menu-list":q,"back-path":"/application/layout/robot"},{title:s(()=>[e("div",null,[m(E,{placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:o(_),"onUpdate:visible":n[0]||(n[0]=r=>w(_)?_.value=r:null)},{reference:s(()=>[e("div",it,[e("div",pt,[m(R,{content:o(b).name,teleported:!0,effect:"light"},null,8,["content"])]),m(A,{name:"el-icon-ArrowDown"})])]),default:s(()=>[e("div",null,[m(g,{style:{height:"250px"}},{default:s(()=>[(a(!0),f(Q,null,W(o(v).robotLists,r=>(a(),f("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-primary-light-9 px-[10px] my-[5px] rounded-[12px] hover:text-primary",key:r.id,onClick:lt=>C(r.id)},[e("img",{class:"rounded-[50%] w-[28px] h-[28px] flex-none",src:r.image,alt:""},null,8,rt),e("div",et,X(r.name),1)],8,ot))),128))]),_:1})])]),_:1},8,["visible"])])]),_:1},8,["modelValue"]),e("div",mt,[e("div",nt,[o(p)==="edit"?(a(),h(z,{key:0,"model-value":o(b),onSuccess:n[2]||(n[2]=r=>o(u).push("/application/layout/robot"))},null,8,["model-value"])):y("",!0),m(I,null,{default:s(()=>[o(p)==="release"?(a(),h(g,{key:0},{default:s(()=>[m(F,{"app-id":o(l)},null,8,["app-id"])]),_:1})):y("",!0)]),_:1}),o(p)==="dialogue"?(a(),h(J,{key:1,"app-id":o(l)},null,8,["app-id"])):y("",!0)])])])}}}),ur=Y(at,[["__scopeId","data-v-21d9b61a"]]);export{ur as default};
