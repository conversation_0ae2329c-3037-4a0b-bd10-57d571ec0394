import{E as d}from"./KiH-Yvip.js";import{cn as u}from"./DjCZV6kq.js";import"./eQB23YlX.js";import"./l0sNRNKZ.js";/* empty css        */import{_ as c}from"./CbyB5uyr.js";import{l as _,M as f,N as V,Z as o,O as s,u as l,y as g,a7 as x}from"./Dp9aCaJ6.js";const v={class:"flex gap-4 items-center pl-3"},q=_({__name:"sd-denoising-strength",props:{modelValue:{default:.75}},emits:["update:modelValue"],setup(a,{emit:n}){const r=n,m=a,{modelValue:e}=u(m,r);return(B,t)=>{const i=d;return f(),V("div",null,[o(c,{title:"重绘强度",required:"",tips:"低：越接近原图 高：越充满创意"}),s("div",v,[o(i,{modelValue:l(e),"onUpdate:modelValue":t[0]||(t[0]=p=>g(e)?e.value=p:null),step:.01,max:1},null,8,["modelValue"]),s("span",null,x(l(e)),1)])])}}});export{q as _};
