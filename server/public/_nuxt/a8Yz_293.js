import{E as B}from"./CQAw0cuz.js";import{_ as D}from"./uE1Ww79i.js";import{E as L}from"./DeQZaZZG.js";import{E as M}from"./BxTfM8xu.js";import{f as N}from"./B1MekKW7.js";/* empty css        *//* empty css        */import{u as S}from"./QLtE1ufq.js";import{e as j,b as A}from"./B_1915px.js";import{c as H}from"./CH5OZtOz.js";import{l as P,ak as R,M as a,N as n,O as e,Z as l,a0 as x,u as c,a1 as V,_ as u,aq as h,a7 as p}from"./Dp9aCaJ6.js";import{_ as q}from"./DlAUqK2U.js";import"./D3aLd1tg.js";const F={class:"h-full p-[15px] rounded-[12px]"},G={class:"h-full flex flex-col"},I={class:"flex justify-between items-center"},O={class:"flex-1 min-h-0 pt-[10px] mx-[-10px]"},Q={class:"flex flex-wrap items-stretch"},U={class:"p-[15px] bg-page rounded-[10px] h-full record-item hover:bg-primary-light-9"},Z={class:"text-lg font-medium line-clamp-1"},z=["onClick"],J={class:"whitespace-pre-line line-clamp-5 my-[10px] h-[105px]"},K={class:"flex items-center"},T={class:"mr-auto text-tx-secondary text-sm"},W=["onClick"],X={key:1,class:"h-full flex flex-col items-center justify-center"},Y=P({__name:"history-all",emits:["view","history"],async setup(ee,{emit:y}){let r,d;const m=y,{data:i,refresh:v}=([r,d]=R(()=>S(()=>j({type:4,page_type:0}),{transform(o){return o.lists},default(){return[]}},"$ffgCUBQ1Gx")),r=await r,d(),r),g=(o,t)=>{m("view",{id:o,text:t})},_=async o=>{i.value.length&&(await N.confirm(`确定${o?"删除":"清空"}记录？`),await A({type:4,id:o}),v())};return(o,t)=>{const k=B,w=D,C=L,b=M;return a(),n("div",F,[e("div",G,[e("div",I,[l(k,{onBack:t[0]||(t[0]=s=>m("history"))},{content:x(()=>t[2]||(t[2]=[e("span",{class:"text-xl font-medium"}," 生成记录 ",-1)])),_:1}),e("div",{class:"bg-page-base text-sm px-[15px] py-[5px] rounded-[4px] cursor-pointer ml-[10px]",onClick:t[1]||(t[1]=s=>_())}," 清空记录 ")]),e("div",O,[c(i).length?(a(),V(C,{key:0},{default:x(()=>[e("div",Q,[(a(!0),n(u,null,h(c(i),s=>(a(),n("div",{class:"2xl:w-1/4 xl:w-1/3 w-1/2 p-[8px]",key:s.id},[e("div",U,[e("div",Z," 帮我生成："+p(s.ask),1),(a(!0),n(u,null,h(s.reply,(f,E)=>(a(),n("div",{class:"cursor-pointer",key:E,onClick:$=>g(s.id,f)},[e("div",J,p(f),1),e("div",K,[e("div",T,p(s.create_time),1),e("div",{class:"cursor-pointer text-tx-secondary flex",onClick:$=>_(s.id)},[l(w,{name:"el-icon-Delete"})],8,W)])],8,z))),128))])]))),128))])]),_:1})):(a(),n("div",X,[l(b,{image:c(H)},null,8,["image"])]))])])])}}}),fe=q(Y,[["__scopeId","data-v-6a4d0ce4"]]);export{fe as default};
