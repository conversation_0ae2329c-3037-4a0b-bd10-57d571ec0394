import{a as _}from"./DqgnSgQL.js";import{b as h,a as d}from"./BsMqt_su.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import f from"./BS-crzzP.js";import{l as v,m as r,M as s,N as c,Z as k,a0 as w,_ as M,aq as x,u as a,a1 as B}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./BeID4H0V.js";import"./D1YDx4Yr.js";import"./DCTLXrZ8.js";import"./BPgEaOor.js";import"./C25gBBj_.js";import"./CcJ-n_gx.js";import"./BKnYJ4L1.js";const N={class:"menu"},b=v({__name:"menu",props:{isHome:{type:<PERSON>olean}},setup(g){const n=h(),m=r(()=>{var e;return((e=n.pageAside.menu)==null?void 0:e.filter(i=>Number(i.is_show)===1))||[]}),u=r(()=>n.pageAside.showNavIcon),t=d(),p=r(()=>{const e=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.parentPath||t.meta.activePath||e});return(e,i)=>{const l=_;return s(),c("div",N,[k(l,{"default-active":a(p)},{default:w(()=>[(s(!0),c(M,null,x(a(m),o=>(s(),B(f,{key:o.id,item:o,"is-show-icon":a(u),path:o.link.path,"is-active":a(p)===o.link.path},null,8,["item","is-show-icon","path","is-active"]))),128))]),_:1},8,["default-active"])])}}}),z=I(b,[["__scopeId","data-v-1b361228"]]);export{z as default};
