import{E as y}from"./CBkeCdOF.js";import{b,E as w}from"./Br7V4jS9.js";import{_ as E}from"./DVYAUH4R.js";/* empty css        */import{_ as S}from"./Cv1u9LLW.js";import{l as k,m as j,M as e,N as o,O as t,_ as B,aq as I,u as l,a7 as p,Z as a,a0 as _}from"./Dp9aCaJ6.js";import{_ as q}from"./DlAUqK2U.js";const N={class:"py-[40px]"},O={class:"min-[1200px]:w-[1200px] mx-auto"},z={class:"flex flex-col items-center"},A={class:"flex-1 my-[30px] px-[20px]"},C={class:"flex justify-center text-center"},D={class:"text-[24px] font-bold"},L={class:"mt-[20px] text-[16px]"},M={class:"flex-1 px-[20px]"},V={class:"flex justify-center"},F={class:"mt-[90px]"},P=k({__name:"intro",props:{prop:{}},setup(d){const m=d,f=b(),x=j(()=>m.prop.data.filter(n=>n.isShow));return(n,r)=>{const u=y,h=w,v=E;return e(),o("div",N,[t("div",O,[(e(!0),o(B,null,I(l(x),(s,g)=>{var c,i;return e(),o("div",{class:"mb-[100px]",key:g},[t("div",z,[t("div",A,[t("div",C,[t("div",null,[t("div",D,p(s.title),1),t("div",L,p(s.subtitle),1)])])]),t("div",M,[t("div",V,[a(u,{fit:"cover",src:l(f).getImageUrl(s.image)},null,8,["src"])])]),t("div",F,[a(v,{to:{path:(c=s.link)==null?void 0:c.path,query:(i=s.link)==null?void 0:i.query}},{default:_(()=>[a(h,{type:"primary",class:"enter-btn hover-to-right",size:"large"},{default:_(()=>r[0]||(r[0]=[t("div",{class:"flex justify-center items-center w-[50px] h-[50px] rounded-full bg-white"},[t("img",{src:S,class:"w-[24px] h-[24px]",alt:""})],-1),t("span",{class:"ml-4"},"马上体验",-1)])),_:1})]),_:2},1032,["to"])])])])}),128))])])}}}),T=q(P,[["__scopeId","data-v-5fa0cf3f"]]),Q=Object.freeze(Object.defineProperty({__proto__:null,default:T},Symbol.toStringTag,{value:"Module"}));export{Q as _};
