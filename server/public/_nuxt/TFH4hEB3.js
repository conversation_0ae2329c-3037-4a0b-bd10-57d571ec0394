import{E as c}from"./Uw_fYagh.js";import{E as g}from"./hOM-i1i4.js";import{E as x}from"./I7iWhwB5.js";import{b as y,v as D}from"./DAgm18qP.js";/* empty css        *//* empty css        */import{r as V,x as E,y as w,f as e,e as v}from"./DLSFF3if.js";import L from"./BWUw2P9b.js";import b from"./WUPpG_6k.js";import{_ as k}from"./qfUwkfyK.js";import S from"./B2VDHhJL.js";import A from"./BLMn3oMy.js";import P from"./Ca6ozkx6.js";import{D as z}from"./DnehfR36.js";import{DrawModeEnum as i}from"./tONJIxwY.js";import{l as U,F as B,u as o,M as p,N as a,Z as r,a0 as l,O as s,aa as M}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./D3kCNEsc.js";import"./D6pAoVka.js";import"./BkCbCnZg.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./DMHEbzLi.js";import"./CfiDmG6E.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./D_gfJTDn.js";import"./CFxUWFIJ.js";import"./DFChVafq.js";import"./9Bti1uB6.js";/* empty css        */import"./B7tJgJ_s.js";import"./TR-nE6Zk.js";/* empty css        */import"./BZieL5UA.js";import"./B5giXwus.js";import"./8JGb4G3V.js";import"./Ccin5anY.js";import"./Dh2YTmLQ.js";import"./Cpg3PDWZ.js";import"./Dbgaq24H.js";import"./C7FQ8gnX.js";import"./8CeC6szd.js";import"./Cv6HhfEG.js";import"./CXZvQ2S9.js";import"./CG1Y4RXB.js";import"./D5aRiEq0.js";/* empty css        *//* empty css        */import"./1sgdir_0.js";import"./BZh-pK-_.js";import"./Ckv4FekU.js";import"./DjwCd26w.js";import"./CVKgQtoK.js";import"./CcPlX2kz.js";import"./hQuw3WhI.js";import"./BVqQ2B7o.js";import"./BWdDF8rn.js";import"./DVmnGWD4.js";const q={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},C={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},F={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},I=U({__name:"dalle",setup(O){const n=y();return B(()=>{V({draw_api:i.DALLE3,draw_model:i.DALLE3,action:"generate",prompt:"",negative_prompt:"",size:"1024x1024"}),E.model=i.DALLE3,w()}),(R,t)=>{const d=c,u=g,f=x,_=D;return o(n).config.switch.dalle3_status?(p(),a("div",q,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:l(()=>[s("div",C,[r(L,{modelValue:o(e).prompt,"onUpdate:modelValue":t[0]||(t[0]=m=>o(e).prompt=m),model:o(i).DALLE3},null,8,["modelValue","model"]),r(S,{modelValue:o(e).size,"onUpdate:modelValue":t[1]||(t[1]=m=>o(e).size=m)},null,8,["modelValue"]),r(A,{modelValue:o(e).style,"onUpdate:modelValue":t[2]||(t[2]=m=>o(e).style=m)},null,8,["modelValue"]),r(P,{modelValue:o(e).quality,"onUpdate:modelValue":t[3]||(t[3]=m=>o(e).quality=m)},null,8,["modelValue"])]),r(k)]),_:1}),M(r(b,{"element-loading-text":"正在加载数据..."},null,512),[[_,o(v)]])])):(p(),a("div",F,[r(f,null,{icon:l(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(z)},null,8,["src"])]),title:l(()=>t[4]||(t[4]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),Ho=N(I,[["__scopeId","data-v-9879be3f"]]);export{Ho as default};
