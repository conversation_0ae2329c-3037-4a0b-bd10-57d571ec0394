import{_ as w}from"./BMw6WEOu.js";import{u as y}from"./CcPlX2kz.js";import{j as C,o as m,e as f,E as V,p as S,da as B,db as p,dc as _}from"./B5S_Er7H.js";import{a as L,E as I}from"./Db173UeR.js";import{l as M,j as x,m as N,r as R,M as F,N as O,O as c,a7 as U,u as e,Z as o,a0 as t,a6 as j}from"./Dp9aCaJ6.js";const D={class:"flex-1 flex flex-col"},T={class:"flex flex-1 flex-col pt-[30px] px-[30px] min-h-0"},q={class:"text-2xl font-medium text-tx-primary"},G={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},P=M({__name:"bind-mobile",setup(z){const s=C(),n=x(),u=x(),b={mobile:[{required:!0,message:"请输入手机号码",trigger:["change","blur"]}],code:[{required:!0,message:"请输入验证码",trigger:["change","blur"]}]},r=N(()=>!!s.userInfo.mobile),l=R({type:r.value?"change":"bind",mobile:"",code:""}),g=async()=>{var i,a;await((i=n.value)==null?void 0:i.validateField(["mobile"])),await B({scene:r.value?p.CHANGE_MOBILE:p.BIND_MOBILE,mobile:l.mobile}),(a=u.value)==null||a.start()},v=async()=>{var i;await((i=n.value)==null?void 0:i.validate()),s.isLogin?await _(l):(await _(l,{token:s.temToken}),s.login(s.temToken),location.reload(),await s.getUser()),s.toggleShowLogin(!1)},{lockFn:k,isLock:E}=y(v);return(i,a)=>{const h=w;return F(),O("div",D,[c("div",T,[c("span",q,U(e(r)?"更换手机号":"绑定手机号"),1),o(e(S),{ref_key:"formRef",ref:n,class:"mt-[35px]",size:"large",model:e(l),rules:b},{default:t(()=>[o(e(m),{prop:"mobile"},{default:t(()=>[o(e(f),{modelValue:e(l).mobile,"onUpdate:modelValue":a[0]||(a[0]=d=>e(l).mobile=d),placeholder:"请输入手机号"},{prepend:t(()=>[o(e(L),{"model-value":"+86",style:{width:"80px"}},{default:t(()=>[o(e(I),{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(e(m),{prop:"code"},{default:t(()=>[o(e(f),{modelValue:e(l).code,"onUpdate:modelValue":a[1]||(a[1]=d=>e(l).code=d),placeholder:"请输入验证码"},{suffix:t(()=>[c("div",G,[o(h,{ref_key:"verificationCodeRef",ref:u,onClickGet:g},null,512)])]),_:1},8,["modelValue"])]),_:1}),o(e(m),{class:"mt-[60px]"},{default:t(()=>[o(e(V),{class:"w-full",type:"primary",onClick:e(k),loading:e(E)},{default:t(()=>a[2]||(a[2]=[j(" 确认 ")])),_:1},8,["onClick","loading"])]),_:1})]),_:1},8,["model"])])])}}});export{P as _};
