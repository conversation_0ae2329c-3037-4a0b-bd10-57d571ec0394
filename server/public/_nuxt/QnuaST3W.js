import{K as k,al as y,M as h,J as v,O as b,P as g}from"./B1MekKW7.js";import{l as u,m as C,M as n,N as o,a1 as r,a0 as E,a3 as B,u as l,a4 as i,X as d,V as f}from"./Dp9aCaJ6.js";const P=k({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},href:{type:String,default:""},target:{type:String,default:"_self"},icon:{type:y}}),S={click:t=>t instanceof MouseEvent},_=["href","target"],$=u({name:"ElLink"}),w=u({...$,props:P,emits:S,setup(t,{emit:c}){const s=t,a=h("link"),p=C(()=>[a.b(),a.m(s.type),a.is("disabled",s.disabled),a.is("underline",s.underline&&!s.disabled)]);function m(e){s.disabled||c("click",e)}return(e,M)=>(n(),o("a",{class:d(l(p)),href:e.disabled||!e.href?void 0:e.href,target:e.disabled||!e.href?void 0:e.target,onClick:m},[e.icon?(n(),r(l(v),{key:0},{default:E(()=>[(n(),r(B(e.icon)))]),_:1})):i("v-if",!0),e.$slots.default?(n(),o("span",{key:1,class:d(l(a).e("inner"))},[f(e.$slots,"default")],2)):i("v-if",!0),e.$slots.icon?f(e.$slots,"icon",{key:2}):i("v-if",!0)],10,_))}});var L=b(w,[["__file","link.vue"]]);const K=g(L);export{K as E};
