import{_ as x}from"./Da1DFZhM.js";import{a as f,cm as h,_ as v}from"./BQ-RMI0l.js";import{l as u,m as y,M as n,N as p,O as s,_ as b,aq as L,a1 as N,a0 as i,X as g,a7 as k,Z as _}from"./Dp9aCaJ6.js";import{_ as d}from"./DlAUqK2U.js";const $={class:"flex"},B={class:"flex bg-body p-[8px] rounded-[10px]"},w=u({__name:"index",props:{navList:{}},setup(m){const a=f(),r=y(()=>{const t=a.path==="/"?a.path:a.path.replace(/\/$/,"");return a.meta.activePath||t}),l=t=>{const e=r.value;return!!(e===t||e.startsWith(t+"/"))};return(t,e)=>{const c=x;return n(),p("div",$,[s("div",B,[(n(!0),p(b,null,L(t.navList,o=>(n(),N(c,{key:o.path,to:o.path},{default:i(()=>[s("div",{class:g(["text-xl px-[17.5px] py-[5px] rounded-[7px] min-w-[85px] text-center font-bold",{"text-white bg-primary":l(o.path)}])},k(o.name),3)]),_:2},1032,["to"]))),128))])])}}}),C=d(w,[["__scopeId","data-v-f63ef853"]]),P={class:"h-full flex flex-col"},I={class:"flex-1 min-h-0"},V=u({__name:"layout",setup(m){const a=[{name:"智能体",path:"/application/layout/robot"},{name:"智能体形象",path:"/application/layout/digital"},{name:"知识库",path:"/application/layout/kb"}];return(r,l)=>{const t=C,e=h,c=v;return n(),p("div",null,[_(c,{name:"default"},{default:i(()=>[s("div",P,[_(t,{class:"px-[20px] pt-[16px]","nav-list":a}),s("div",I,[_(e)])])]),_:1})])}}}),E=d(V,[["__scopeId","data-v-8125b6d3"]]);export{E as default};
