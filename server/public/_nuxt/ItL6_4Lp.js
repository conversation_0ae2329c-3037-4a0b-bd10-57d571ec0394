import{_ as m}from"./B9JFrq5d.js";import{_ as p}from"./DW03xsNf.js";import{b as s}from"./DNaNbs6R.js";import{l as a,M as l,N as n,Z as t,X as c,u as r,O as i,V as _}from"./Dp9aCaJ6.js";import{_ as d}from"./DlAUqK2U.js";import"./Dd8X4-Ng.js";import"./CN8DIg3d.js";import"./BWd1nnnI.js";import"./B96Hvfyy.js";/* empty css        */import"./gnUhXrZa.js";import"./BXY3NVfX.js";import"./dIlF1NYp.js";import"./CMsEk3FA.js";import"./DCTLXrZ8.js";import"./B-XemFWQ.js";import"./BOI9rKCA.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./qk6NwY8c.js";import"./BI8nXEhZ.js";import"./ZgVTU2xh.js";import"./qwd2WECw.js";import"./CHyUIwEw.js";import"./DvxhPWqY.js";import"./W1Gt2nzg.js";import"./CCY2gEyH.js";/* empty css        */import"./IgaM3DW_.js";import"./BE7GAo-z.js";import"./DqXeR9kI.js";import"./BKaEX7Xa.js";import"./CQAjyP_F.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";const f={class:"layout-header h-full flex items-center"},u={class:"flex-1 min-w-0"},g={class:""},h=a({__name:"index",setup(x){const o=s();return(e,v)=>(l(),n("div",f,[t(m,{class:c("mr-[50px]"),logo:r(o).getWebsiteConfig.pc_logo,title:r(o).getWebsiteConfig.pc_name},null,8,["logo","title"]),i("div",u,[i("div",g,[_(e.$slots,"default",{},void 0,!0)])]),t(p,{class:"ml-auto"})]))}}),io=d(h,[["__scopeId","data-v-a4fd1a58"]]);export{io as default};
