import{j as N,b as R,dw as O,cW as x,dg as T,de as P,e as U,o as j,E as M,p as q,dx as D}from"./CylNgAGi.js";import{_ as G}from"./EAABSjZ0.js";import"./DP2rzg_V.js";/* empty css        */import{u as $}from"./CcPlX2kz.js";import{l as A,j as C,r as W,m as E,M as i,N as z,O as p,Z as a,a0 as l,u as o,a1 as u,a4 as c,a6 as f}from"./Dp9aCaJ6.js";const X={class:"pt-[10px]"},Z={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},H={class:"flex justify-end"},J={class:"flex-1 flex"},te=A({__name:"mailbox-login",setup(K){const r=N(),L=R(),_=C(),V={email:[{required:!0,message:"请输入邮箱账号"},{type:"email",message:"请输入正确的邮箱账号"}],password:[{required:!0,message:"请输入密码"}],code:[{required:!0,message:"请输入验证码"}]},s=W({code:"",email:"",password:"",scene:4,terminal:O}),k=E(()=>s.scene===4),y=E(()=>s.scene===2),w=t=>{s.scene=t},v=C(),B=async()=>{var t,e;await((t=_.value)==null?void 0:t.validateField(["email"])),await T({scene:P.LOGIN,email:s.email}),(e=v.value)==null||e.start()},{lockFn:F,isLock:I}=$(async()=>{var e;await((e=_.value)==null?void 0:e.validate());const t=await D(s);!t.mobile&&L.getLoginConfig.coerce_mobile?(r.temToken=t.token,r.setLoginPopupType(x.BIND_MOBILE)):(r.login(t.token),r.setUser(t),r.toggleShowLogin(!1),location.reload())});return(t,e)=>{const g=U,d=j,S=G,m=M,b=q;return i(),z("div",X,[p("div",null,[a(b,{ref_key:"formRef",ref:_,size:"large",model:o(s),rules:V},{default:l(()=>[a(d,{prop:"email"},{default:l(()=>[a(g,{modelValue:o(s).email,"onUpdate:modelValue":e[0]||(e[0]=n=>o(s).email=n),placeholder:"请输入邮箱账号"},null,8,["modelValue"])]),_:1}),o(y)?(i(),u(d,{key:0,prop:"password"},{default:l(()=>[a(g,{modelValue:o(s).password,"onUpdate:modelValue":e[1]||(e[1]=n=>o(s).password=n),type:"password","show-password":"",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):c("",!0),o(k)?(i(),u(d,{key:1,prop:"code"},{default:l(()=>[a(g,{modelValue:o(s).code,"onUpdate:modelValue":e[2]||(e[2]=n=>o(s).code=n),placeholder:"请输入验证码"},{suffix:l(()=>[p("div",Z,[a(S,{ref_key:"verificationCodeRef",ref:v,onClickGet:B},null,512)])]),_:1},8,["modelValue"])]),_:1})):c("",!0),p("div",H,[p("div",J,[o(y)?(i(),u(m,{key:0,type:"primary",link:"",onClick:e[3]||(e[3]=n=>w(4))},{default:l(()=>e[6]||(e[6]=[f(" 邮箱验证码登录 ")])),_:1})):c("",!0),o(k)?(i(),u(m,{key:1,type:"primary",link:"",onClick:e[4]||(e[4]=n=>w(2))},{default:l(()=>e[7]||(e[7]=[f(" 邮箱密码登录 ")])),_:1})):c("",!0)]),a(m,{link:"",onClick:e[5]||(e[5]=n=>o(r).setLoginPopupType(o(x).FORGOT_PWD_MAILBOX))},{default:l(()=>e[8]||(e[8]=[f(" 忘记密码？ ")])),_:1})]),a(d,{class:"my-[30px]"},{default:l(()=>[a(m,{class:"w-full",type:"primary",loading:o(I),onClick:o(F)},{default:l(()=>e[9]||(e[9]=[f(" 登录 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])])])}}});export{te as _};
