import{K as H,X as I,Z as V,aq as T,aM as W,M as j,cw as R,cH as M,U as q,ak as U,V as X,O as Z,$ as F,P as G,b as K}from"./BsMqt_su.js";import{l as y,j as h,b as v,m as z,c as L,F as Y,w as D,M as B,N as J,O as _,V as Q,X as w,u as a,a2 as b,a1 as ee,a0 as te,Z as x}from"./Dp9aCaJ6.js";import{_ as oe}from"./CNKTogib.js";import ae from"./D8eXLYkn.js";import{_ as se}from"./C9gRmyZn.js";import{_ as le}from"./DlAUqK2U.js";const ne=H({zIndex:{type:I([Number,String]),default:100},target:{type:String,default:""},offset:{type:Number,default:0},position:{type:String,values:["top","bottom"],default:"top"}}),re={scroll:({scrollTop:s,fixed:l})=>V(s)&&T(l),[W]:s=>T(s)},k="ElAffix",ce=y({name:k}),ie=y({...ce,props:ne,emits:re,setup(s,{expose:l,emit:c}){const e=s,n=j("affix"),r=h(),i=h(),u=h(),{height:f}=R(),{height:p,width:S,top:E,bottom:N,update:g}=M(i,{windowScroll:!1}),d=M(r),o=v(!1),$=v(0),m=v(0),A=z(()=>({height:o.value?`${p.value}px`:"",width:o.value?`${S.value}px`:""})),O=z(()=>{if(!o.value)return{};const t=e.offset?F(e.offset):0;return{height:`${p.value}px`,width:`${S.value}px`,top:e.position==="top"?t:"",bottom:e.position==="bottom"?t:"",transform:m.value?`translateY(${m.value}px)`:"",zIndex:e.zIndex}}),C=()=>{if(u.value)if($.value=u.value instanceof Window?document.documentElement.scrollTop:u.value.scrollTop||0,e.position==="top")if(e.target){const t=d.bottom.value-e.offset-p.value;o.value=e.offset>E.value&&d.bottom.value>0,m.value=t<0?t:0}else o.value=e.offset>E.value;else if(e.target){const t=f.value-d.top.value-e.offset-p.value;o.value=f.value-e.offset<N.value&&f.value>d.top.value,m.value=t<0?-t:0}else o.value=f.value-e.offset<N.value},P=()=>{g(),c("scroll",{scrollTop:$.value,fixed:o.value})};return L(o,t=>c("change",t)),Y(()=>{var t;e.target?(r.value=(t=document.querySelector(e.target))!=null?t:void 0,r.value||q(k,`Target does not exist: ${e.target}`)):r.value=document.documentElement,u.value=U(i.value,!0),g()}),X(u,"scroll",P),D(C),l({update:C,updateRoot:g}),(t,_e)=>(B(),J("div",{ref_key:"root",ref:i,class:w(a(n).b()),style:b(a(A))},[_("div",{class:w({[a(n).m("fixed")]:o.value}),style:b(a(O))},[Q(t.$slots,"default")],6)],6))}});var ue=Z(ie,[["__file","affix.vue"]]);const fe=G(ue),pe={class:"max-w-[1200px] w-full mx-auto flex items-center"},de={class:"flex-1 min-w-0"},me=y({__name:"header",props:{prop:{}},setup(s){const l=K(),c=v(0),e=({scrollTop:n})=>{c.value=n/80>.8?.8:n/80};return(n,r)=>{const i=fe;return B(),ee(i,{target:"",style:{height:"0",width:"100%"},offset:0,onScroll:e},{default:te(()=>[_("div",{class:"header w-full h-[60px] flex items-center justify-center",style:b({background:"rgba(256,256, 256,"+a(c)+")"})},[_("div",pe,[x(oe,{class:w("mr-[50px]"),logo:a(l).getWebsiteConfig.pc_logo,title:a(l).getWebsiteConfig.pc_name},null,8,["logo","title"]),_("div",de,[x(ae,{"is-home":!0})]),x(se,{class:"ml-auto",isHidden:!0})])],4)]),_:1})}}}),ve=le(me,[["__scopeId","data-v-85c3c1dc"]]),Se=Object.freeze(Object.defineProperty({__proto__:null,default:ve},Symbol.toStringTag,{value:"Module"}));export{Se as _};
