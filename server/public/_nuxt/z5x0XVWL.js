import{_ as k}from"./DKQXk0aC.js";import{_ as w}from"./CN8DIg3d.js";import{a as D,l as b}from"./CLVDtxqA.js";import{_ as h}from"./DiTtInwP.js";import g from"./DleZQl1v.js";import q from"./wgmJyVK-.js";import S from"./DKG6IKL3.js";import{b as U}from"./BNnETjxs.js";import{l as V,b as c,r as B,c as C,F as M,q as N,M as u,N as I,Z as d,u as r,y as O,O as n,a0 as R,a1 as $,a3 as T}from"./Dp9aCaJ6.js";import{_ as j}from"./DlAUqK2U.js";import"./C2uyROqA.js";import"./BAr3c1ZT.js";import"./DTPcp5_i.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./DEG58TaP.js";import"./DI1qIu00.js";import"./87qJxv_v.js";import"./JBMcerbz.js";/* empty css        *//* empty css        */import"./DPiLjow0.js";import"./D92sLaNt.js";import"./BBFeqFQi.js";import"./Bkygmrc1.js";import"./DLMIaim0.js";import"./C76tEu0I.js";import"./BQcHrgkb.js";import"./BjrRGs-I.js";import"./1ThCdcE_.js";import"./CkNWwKqv.js";import"./DpOMRpsE.js";import"./CjON5iPp.js";import"./DW2yWX9x.js";import"./Cv6HhfEG.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./67xbGseh.js";import"./DI4iq1ZM.js";import"./Hd0kM9t1.js";import"./B1txbFRp.js";import"./9Bti1uB6.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./CRlC0zID.js";import"./CcPlX2kz.js";import"./BiHhwkbt.js";import"./mq8TyIF0.js";import"./DY73EaVE.js";import"./D5Ye8pBY.js";import"./CRS4Ri59.js";import"./Ce8FGLE8.js";import"./DQo9WDrm.js";import"./Cpg3PDWZ.js";import"./lCX_iigg.js";import"./CVxgEqOS.js";import"./C4qLKnCc.js";import"./DkQpgURj.js";import"./5dUZFzqZ.js";import"./C03JKMbg.js";import"./B26RlKRG.js";import"./BaEiXiMm.js";/* empty css        */import"./GwRJPYxu.js";import"./D3Sqf4PW.js";import"./Dt8dy3ia.js";import"./BtQB-JFB.js";import"./BE7GAo-z.js";import"./DqV7Sug9.js";import"./BZXjPGYa.js";import"./BnJB0GR-.js";import"./CYs1NzuK.js";import"./DssWEouO.js";import"./BICGiF8V.js";import"./uz2DoZ_U.js";import"./g6LfHct_.js";import"./CD28ZGm9.js";/* empty css        */import"./UwKNTaZS.js";/* empty css        */import"./DwFObZc_.js";import"./3-tM77aG.js";import"./D_SQCo6f.js";import"./2OHGmSJj.js";const E={class:"h-full flex"},F={class:"flex-1 min-w-0 overflow-auto pr-[16px] py-[16px]"},K={class:"bg-body h-full rounded-2xl"},L={class:"import-data h-full"},Z=V({__name:"index",setup(z){const m=D(),_=b(),s=c(1),p=m.query.id,f={dataStudy:h,testData:g,teamData:S,setUp:q},i=B({type:"",name:"",image:"",intro:"",owned:1,power:1,qa_length:""}),o=c("dataStudy"),y=[{name:"数据学习",icon:"el-icon-Document",key:"dataStudy"},{name:"搜索测试",key:"testData",icon:"el-icon-Search"},{name:"团队成员",key:"teamData",icon:"el-icon-User"},{name:"知识库设置",key:"setUp",icon:"el-icon-Setting"}],l=async()=>{const e=await U({id:p});Object.keys(i).map(t=>{i[t]=e[t]}),m.query.type&&(o.value=m.query.type)};return C(()=>o.value,e=>{_.replace({path:"",query:{id:p,type:e}})}),M(()=>{l()}),N("knowDetail",i),(e,t)=>{const v=k,x=w;return u(),I("div",E,[d(v,{modelValue:r(o),"onUpdate:modelValue":t[0]||(t[0]=a=>O(o)?o.value=a:null),"menu-list":y,title:r(i).name,"back-path":"/application/layout/kb"},null,8,["modelValue","title"]),n("div",F,[d(x,null,{default:R(()=>[n("div",K,[n("div",L,[(u(),$(T(f[r(o)]),{id:r(p),type:r(i).type,onToImport:t[1]||(t[1]=a=>s.value=2),onSuccess:t[2]||(t[2]=a=>s.value=1),onUpdate:l},null,40,["id","type"]))])])]),_:1})])])}}}),Uo=j(Z,[["__scopeId","data-v-3affaafe"]]);export{Uo as default};
