import{E as S,a as B}from"./CJBWowqe.js";import{a as L,b as N}from"./BL75K97j.js";import{_ as T}from"./Dj1AZtWG.js";import{E as A}from"./CSeNE23V.js";import{E as D}from"./B0QXcJuZ.js";import{E as U}from"./CH0sQbfA.js";import{cM as z}from"./CzTOiozM.js";/* empty css        *//* empty css        *//* empty css        */import{u as P}from"./YCXpYPC4.js";import{u as R,I as $}from"./IiRUsTiH.js";import{e as j}from"./DEledyUV.js";import{l as q,r as F,ak as G,m as O,M as e,N as s,O as o,Z as l,a0 as r,_,aq as d,u as a,a1 as y,a6 as W,a7 as Z}from"./Dp9aCaJ6.js";import{_ as H}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./CPMpBhdt.js";import"./C_7xENts.js";import"./n7JpCWQv.js";import"./v-ANcAMI.js";import"./Cfoh3Adv.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./Cj46UrnO.js";import"./G3qDnfIf.js";const J={class:"maps-select h-full flex flex-col"},K={class:"mt-[5px] px-main"},Q={class:"flex-1 min-h-0"},X={class:"p-main pt-0"},Y={key:0,class:"flex flex-wrap mx-[-7px]"},tt=["onClick"],et={class:"bg-[#101010] rounded-md cursor-pointer"},ot={class:"pic-wrap h-0 pt-[100%] relative"},at={class:"absolute inset-0"},st=q({__name:"maps",async setup(lt){let p,u;const b=R(),v=[{type:1,label:"系统贴图"}],n=F({type:1,index:0}),{data:f}=([p,u]=G(()=>P(()=>z(),{lazy:!0},"$Ult9WlnVjn")),p=await p,u(),p),x=O(()=>f.value[n.index].decals||[]),g=i=>{b.addImage(i.url,$.MAPS,i)};return(i,m)=>{const E=B,h=S,k=N,V=L,w=T,C=A,I=D,M=U;return e(),s("div",J,[o("div",K,[l(h,{modelValue:a(n).type,"onUpdate:modelValue":m[0]||(m[0]=t=>a(n).type=t)},{default:r(()=>[(e(),s(_,null,d(v,t=>l(E,{key:t.type,label:t.label,name:t.type},null,8,["label","name"])),64))]),_:1},8,["modelValue"])]),o("div",Q,[l(M,null,{default:r(()=>[o("div",X,[l(w,{class:"mb-[10px] mt-[-5px]","default-height":42},{default:r(()=>[l(V,{modelValue:a(n).index,"onUpdate:modelValue":m[1]||(m[1]=t=>a(n).index=t),class:"el-radio-group-margin"},{default:r(()=>[(e(!0),s(_,null,d(a(f),(t,c)=>(e(),y(k,{key:c,label:c},{default:r(()=>[W(Z(t.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(x).length?(e(),s("div",Y,[(e(!0),s(_,null,d(a(x),t=>(e(),s("div",{key:t.id,class:"w-[33.33%]"},[o("div",{class:"px-[7px] mb-[14px]",onClick:c=>g(t)},[o("div",et,[o("div",null,[o("div",ot,[o("div",at,[l(C,{src:t.url,class:"w-full h-full",fit:"contain",lazy:""},null,8,["src"])])])])])],8,tt)]))),128))])):(e(),y(I,{key:1,image:a(j),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}}),Lt=H(st,[["__scopeId","data-v-4e32725f"]]);export{Lt as default};
