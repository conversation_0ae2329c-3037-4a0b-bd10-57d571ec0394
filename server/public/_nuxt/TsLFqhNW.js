import{bw as nt,b as rt,j as lt,l as it,bx as qe,by as ct,f as O,E as ut}from"./CLVDtxqA.js";import{_ as dt}from"./BBX5iu7R.js";import{_ as pt}from"./DcNltpix.js";import{_ as ft,a as _t,b as mt,c as vt}from"./CUCjj3Gy.js";import{_ as ht}from"./C2uyROqA.js";import{E as yt,a as gt}from"./uz2DoZ_U.js";import{E as bt}from"./CdI6jj9K.js";import{_ as wt}from"./DDaEm-_F.js";import{b as _,l as xt,j as K,r as pe,ak as fe,c as Q,m as kt,F as It,n as _e,k as St,M as u,N as T,O as l,u as e,a7 as P,a1 as g,a0 as c,Z as p,a6 as j,a4 as w,a2 as Le,X,_ as me,aq as ve,a9 as Ct,aa as Y,ab as ee,as as Rt}from"./Dp9aCaJ6.js";import{u as Et}from"./VJhqcMCs.js";import{u as he}from"./CPeHeFy-.js";import{u as Tt,a as qt}from"./BVaqSNJ5.js";import{u as Lt}from"./Bb2-23m7.js";import{g as Ft,c as $t,a as zt,b as At,r as Dt,v as Nt,d as Vt}from"./Bo3PTL3c.js";import{u as Mt}from"./CuLC4HB6.js";import{P as Pt}from"./Hd0kM9t1.js";import{_ as Bt}from"./DhEwYdTc.js";import{E as Ot}from"./JBMcerbz.js";import{_ as jt}from"./DlAUqK2U.js";function Ut(){const k=_([]);return{files:k,addFile:A=>{k.value=[...k.value,A]},removeFile:A=>{k.value=k.value.filter(I=>I.id!==A.id)},clear:()=>{k.value=[]}}}const Ht={class:"h-full flex flex-col"},Jt={class:"flex items-center p-4 px-[40px] border-b border-solid border-br-light"},Wt=["src"],Zt={class:"ml-[15px]"},Gt={class:"flex items-center"},Kt={class:"text-2xl line-clamp-1"},Qt={class:"text-tx-secondary mt-[4px] line-clamp-2"},Xt={class:"absolute top-0 left-0 w-full h-full flex flex-col z-10"},Yt={class:"flex-1 min-h-0"},eo={class:"py-4 px-8"},to={class:"my-[5px]"},oo={key:1,class:"flex flex-col",style:{"margin-left":"52px"}},ao=["onClick"],so={class:"mr-2 text-tx-primary"},no={class:"flex flex-col justify-center items-center"},ro=["width","height","id"],lo={class:"text-xs text-white"},io={class:"px-[30px]"},co={class:"flex items-center h-12 px-3 bg-page rounded-lg max-w-xs line-clamp-1 overflow-hidden"},uo={class:"absolute top-0 left-0 w-full h-full flex justify-center items-center"},po=["src"],fo=["src"],_o={class:"flex justify-center mt-4"},mo=xt({__name:"index",props:{robotId:{},squareId:{}},async setup(k){let x,q;const d=k,A=nt(),I=rt(),S=lt(),f=Mt(),D=Ut(),y=_(1),{copy:Fe}=Et(),te=K(),L=pe({_index:-1,robot_id:d.robotId,record_id:-1,content:""}),$e=(t,o)=>{var v;L.record_id=t.id,L._index=o,(v=te.value)==null||v.open()},ze=async()=>{var t;try{await $t(L),(t=te.value)==null||t.close(),L.content="",m.value[L._index].is_feedback=1}catch(o){console.log("反馈提交失败-->",o)}},ye=async()=>{if(!d.squareId)await f.getSessionLists(),f.setSessionSelect();else return[]};[x,q]=fe(()=>he(()=>ye(),{lazy:!0},"$kMIVzocZNF")),await x,q();const m=_([]),Ae=it();let oe=0;const B=async()=>{if(!f.sessionId&&!d.squareId)return[];const t=await zt({square_id:d.squareId,category_id:f.sessionId,robot_id:d.robotId,page_size:25e3});if(m.value=t.lists||[],y.value===2&&R.value==3){const o=m.value[m.value.length-1];o&&o.id!==oe&&(oe=o.id,Ge(oe))}};[x,q]=fe(()=>he(()=>B(),{lazy:!0},"$RvfqdxlKCY")),await x,q();const{data:s,refresh:De}=([x,q]=fe(()=>he(()=>Ft({id:d.robotId}),{default(){return{}},lazy:!0},"$atbZEM2Qb5")),x=await x,q(),x);Q(()=>d.robotId,()=>{De()});const Ne=async()=>{if(!S.isLogin)return S.toggleShowLogin();!f.sessionId&&!d.squareId||(await O.confirm("确定清空记录？"),await At({square_id:d.squareId,category_id:f.sessionId,robot_id:d.robotId}),B())},U=K(),Ve=()=>{var t;if(!S.isLogin)return(t=U.value)==null||t.blur(),S.toggleShowLogin();F()};let i=null;const C=_(!1);let ae=!1;const se=_([]),H=async(t,o="input")=>{var V;if(!S.isLogin)return S.toggleShowLogin();if(!t)return O.msgError("请输入问题");if(C.value||!d.robotId)return;b(3);const v=Date.now();C.value=!0;const $=D.files.value.map(n=>({name:n.name,type:"30",url:n.url}));m.value.push({type:1,content:t,files_plugin:$}),m.value.push({type:2,typing:!0,content:"",reasoning:"",key:v}),(V=U.value)==null||V.setInputValue();const h=m.value.find(n=>n.key===v);d.squareId||(f.sessionId||(ae=!0,await f.sessionAdd(),ae=!1),f.getCurrentSession.name==="新的会话"&&await f.sessionEdit({id:f.sessionId,name:t})),i=Dt({square_id:d.squareId,cate_id:f.sessionId,robot_id:d.robotId,question:t,stream:!0,files:D.files.value.map(n=>({...n,type:"30"}))}),i.addEventListener("reasoning",({data:n})=>{const{data:r,index:M}=n;h.reasoning||(h.reasoning=""),h.reasoning+=r}),i.addEventListener("chat",({data:n})=>{const{data:r,index:M}=n;h.content||(h.content=""),h.content+=r}),i.addEventListener("question",({data:n})=>{se.value=JSON.parse(n.data)}),i.addEventListener("file",({data:n})=>{try{const r=JSON.parse(n.data);h.files=r}catch(r){console.error(r)}}),i.addEventListener("image",({data:n})=>{try{const r=JSON.parse(n.data);h.images=r}catch(r){console.error(r)}}),i.addEventListener("video",({data:n})=>{try{const r=JSON.parse(n.data);h.videos=r}catch(r){console.error(r)}}),i.addEventListener("close",async()=>{await S.getUser(),h.typing=!1,C.value=!1,setTimeout(async()=>{await B(),await _e(),F()},1e3)}),i.addEventListener("error",async n=>{var r,M;if(N.value&&b(2),o==="input"&&((r=U.value)==null||r.setInputValue(t)),((M=n.data)==null?void 0:M.code)===1100){I.getIsShowRecharge?(await O.confirm(`${I.getTokenUnit}数量已用完，请前往充值`),Ae.push("/user/recharge")):O.msgError(`${I.getTokenUnit}数量已用完。请联系客服增加`);return}n.errorType==="connectError"&&O.msgError("请求失败，请重试"),["connectError","responseError"].includes(n.errorType)&&m.value.splice(m.value.length-2,2),h.typing=!1,setTimeout(()=>{C.value=!1},200)})},ne=K(),ge=_(),F=async()=>{var o,v,$;const t=(v=(o=ne.value)==null?void 0:o.wrapRef)==null?void 0:v.scrollHeight;($=ne.value)==null||$.setScrollTop(t)},{height:Me}=qe(ge);ct(Me,()=>{C.value&&F()},{throttle:500,immediate:!0});const be=K(),{height:Pe,width:Be}=qe(be),we=kt(()=>!(Be.value/Pe.value>1)),re=()=>{i==null||i.removeEventListener("close"),i==null||i.removeEventListener("chat"),i==null||i.removeEventListener("error"),i==null||i.abort(),C.value=!1};Q(()=>f.sessionId,async t=>{t&&!ae&&(re(),await B(),F())},{immediate:!0});const R=_(0),Oe=pe({0:"正在初始化对话...",1:"点击开始说话",2:"我在听，您请说...",3:"稍等，让我想一想",4:"正在回复中..."}),b=t=>{y.value===2&&(R.value=t)},le=_(!1),ie=_(0),ce=_(!1),xe=_(0),ke=_(0),E=pe({id:"audio-canvas",width:100,height:40,minHeight:5,scale:2}),{render:je,stopRender:Ue,draw:He}=Tt(E),{start:Je,stop:ue,isRecording:J,authorize:vo,close:ho,isOpen:yo}=qt({onstart(){ce.value=!1,ie.value=Date.now()},async onstop(t){if(Ue(),He(new Float64Array(new Array(128).fill(0)),0),!!le.value){le.value=!1,b(3);try{const o=await Nt({file:t.blob});if(!o.text){N.value&&b(2);return}H(o.text,"voice")}catch{N.value&&b(2)}}},ondata(t){je(t);const o=Date.now();t.powerLevel>15&&(clearTimeout(xe.value),R.value=2,ce.value=!0,ie.value=o,xe.value=setTimeout(()=>{le.value=!0,clearTimeout(ke.value),Ze(),ue()},2e3)),o-ie.value>=5e3&&ce.value}}),We=()=>{var t;ke.value=setTimeout(()=>{Xe()},((t=s.value.digital)==null?void 0:t.idle_time)*1e3||0)},{play:Ie,pause:Ze,audioPlaying:Se}=Lt({onstart(){R.value=4,de.value&&(de.value=!1,ue())},onstop(){We(),N.value?b(2):b(1)},onerror(){b(1)}}),Ce=async t=>{const{url:o}=await Vt(t);return o},Ge=async t=>{Ie(async()=>await Ce({type:2,record_id:t}),!1)},Ke=async()=>{J.value||y.value!==2||Je()},N=_(!0),Qe=async()=>{[4,3].includes(R.value)||(J.value?(N.value=!1,ue(),b(1)):(N.value=!0,b(2)))},W=_(""),de=_(!1),Xe=async()=>{if(y.value!==2||!s.value.is_digital||!s.value.digital_id||s.value.is_disable||(W.value||(W.value=await Ce({type:3,record_id:s.value.id})),!W.value))return Promise.reject();de.value=!0;const t=Date.now();m.value.push({type:2,typing:!1,content:s.value.digital.idle_reply,key:t}),await _e(),F(),Ie(W.value,!1)};return Q(R,t=>{switch(t){case 2:Ke()}}),Q(()=>d.robotId,async t=>{re(),f.setRobotId(t),await B(),F(),await ye()},{immediate:!0}),It(async()=>{await _e(),m.value.length&&F()}),St(()=>{re()}),(t,o)=>{const v=ut,$=dt,h=pt,V=ft,n=_t,r=ht,M=yt,Ye=gt,et=mt,tt=vt,ot=bt,at=wt;return u(),T("div",Ht,[l("div",Jt,[l("img",{class:"flex-none w-[40px] h-[40px] rounded-full",src:e(s).image,alt:""},null,8,Wt),l("div",Zt,[l("div",Gt,[l("div",Kt,P(e(s).name),1),t.squareId?w("",!0):(u(),g($,{key:0,to:"/application/layout/robot",class:"ml-[10px]"},{default:c(()=>[p(v,{type:"info",round:"",text:"",bg:"",style:{border:"none","--el-button-hover-text-color":`var(
                                    --el-color-info
                                )`}},{default:c(()=>o[3]||(o[3]=[j(" 切换智能体 ")])),_:1})]),_:1}))]),l("div",Qt,P(e(s).intro),1)])]),p(ot,{class:"flex-1 min-h-0",content:e(I).getChatConfig.watermark,font:{color:e(A)?"rgba(256,256,256,0.08)":"rgba(0,0,0,0.06)",fontSize:12}},{default:c(()=>{var Z,Re,Ee,Te;return[l("div",{ref_key:"containerRef",ref:be,class:"h-full flex flex-col rounded relative",style:Le({background:e(y)==2?e(s).digital_bg:""})},[l("div",Xt,[l("div",Yt,[p(e(Ot),{ref_key:"scrollbarRef",ref:ne},{default:c(()=>[l("div",eo,[l("div",{ref_key:"innerRef",ref:ge},[e(s).welcome_introducer?(u(),g(V,{key:0,class:X(["mb-[20px]",{"opacity-70":e(y)===2}]),type:"left",bg:"var(--el-bg-color-page)",avatar:e(s).icons?e(s).icons:e(s).image},{default:c(()=>[p(h,{content:e(s).welcome_introducer,onClickCustomLink:o[0]||(o[0]=a=>H(a,"link"))},null,8,["content"])]),_:1},8,["avatar","class"])):w("",!0),(u(!0),T(me,null,ve(e(m),(a,z)=>(u(),T("div",{key:a.id+""+z,class:"mt-4 sm:pb-[20px]"},[a.type==1?(u(),g(V,{key:0,type:"right",avatar:e(S).userInfo.avatar,color:"white",class:X({"opacity-70":e(y)===2})},{actions:c(()=>[l("div",to,[p(v,{link:"",type:"info",onClick:G=>e(Fe)(a.content)},{icon:c(()=>[p(r,{name:"el-icon-CopyDocument"})]),default:c(()=>[o[4]||(o[4]=j(" 复制 "))]),_:2},1032,["onClick"])])]),default:c(()=>[p(n,{content:String(a.content),"files-plugin":a.files_plugin},null,8,["content","files-plugin"])]),_:2},1032,["avatar","class"])):w("",!0),a.type==2?(u(),g(V,{key:1,type:"left",time:a.create_time,avatar:e(s).icons?e(s).icons:e(s).image,bg:"var(--el-bg-color-page)",class:X({"opacity-70":e(y)===2}),modelName:a.model},{outer_actions:c(()=>[a.create_time&&e(s).is_show_feedback?(u(),g(v,{key:0,class:"ml-[52px] mt-2",style:{"--el-button-border-color":"transparent","--el-color-info-light-8":"transparent"},type:a.is_feedback?"info":"primary",plain:!0,bg:"",size:"small",disabled:a.is_feedback,onClick:G=>$e(a,z)},{default:c(()=>[j(P(a.is_feedback?"已反馈":"反馈"),1)]),_:2},1032,["type","disabled","onClick"])):w("",!0),z===e(m).length-1&&!e(C)?(u(),T("div",oo,[(u(!0),T(me,null,ve(e(se).length?e(se):a.correlation,(G,st)=>(u(),T("div",{key:st,class:"inline-flex items-center rounded-[12px] bg-page cursor-pointer mt-[10px] hover:bg-primary-light-9",style:{padding:"8px 12px",width:"fit-content"},onClick:Ct(go=>H(G,"input"),["stop"])},[l("span",so,P(G),1),p(r,{name:"el-icon-Right",color:"#999",size:"20"})],8,ao))),128))])):w("",!0)]),default:c(()=>[a.reasoning?(u(),g(Ye,{key:0,"model-value":"the-chat-msg-collapse",class:"mb-2 the-chat-msg-collapse"},{default:c(()=>[p(M,{title:"深度思考",name:"the-chat-msg-collapse"},{default:c(()=>[p(n,{content:a.reasoning,class:"text-tx-secondary px-3 border-l-[3px] border-br-light"},null,8,["content"])]),_:2},1024)]),_:2},1024)):w("",!0),p(n,{content:String(a.content),type:"html",typing:a.typing,"line-numbers":!e(I).isMobile,"show-copy":"","show-context":!!e(s).is_show_context,"show-quote":!!e(s).is_show_quote,"show-voice":e(I).getIsVoiceOpen,context:a.context,"show-poster":"","record-list":e(m),quotes:a.quotes,images:a.images,files:a.files,videos:a.videos,"record-id":a.id,"record-type":2},null,8,["content","typing","line-numbers","show-context","show-quote","show-voice","context","record-list","quotes","images","files","videos","record-id"])]),_:2},1032,["time","avatar","class","modelName"])):w("",!0)]))),128))],512)])]),_:1},512)]),Y(l("div",no,[l("canvas",{style:Le({width:`${e(E).width}px`,height:`${e(E).height}px`}),width:e(E).width*e(E).scale,height:e(E).height*e(E).scale,id:e(E).id},null,12,ro),l("div",lo,[l("div",null,P(e(Oe)[e(R)]),1)])],512),[[ee,e(y)==2]]),l("div",io,[p(tt,{ref_key:"chatActionRef",ref:U,loading:e(C)||e(R)===3,"show-manual":!!e(s).is_artificial,"btn-color":e(A)?"#333":"#f6f6f6",onEnter:H,onClear:Ne,onPause:o[1]||(o[1]=a=>{var z;return(z=e(i))==null?void 0:z.abort()}),onFocus:Ve,menus:e(s).menus},Rt({btn:c(()=>[e(s).is_digital?(u(),g($,{key:0,target:"_blank",to:{path:"/digital/chat",query:{id:t.robotId,squareId:t.squareId,cateId:e(f).sessionId}},class:"flex items-center mr-[10px]"},{default:c(()=>[p(v,{type:"primary",round:"",plain:""},{default:c(()=>o[5]||(o[5]=[j(" 形象对话")])),_:1})]),_:1},8,["to"])):w("",!0),e(s).support_file?(u(),g(Bt,{key:1,class:"mr-3",type:"file","is-parse-content":!0,onOnSuccess:e(D).addFile},null,8,["onOnSuccess"])):w("",!0)]),_:2},[e(D).files.value.length?{name:"file-list",fn:c(()=>[(u(!0),T(me,null,ve(e(D).files.value,a=>(u(),g(et,{key:a.id,onClose:z=>e(D).removeFile(a)},{default:c(()=>[l("div",co,P(a.name),1)]),_:2},1032,["onClose"]))),128))]),key:"0"}:void 0]),1032,["loading","show-manual","btn-color","menus"])]),e(y)==2?(u(),T("div",{key:0,class:X(["recorder",{"recorder--stop":!e(J)}]),onClick:Qe},[e(J)?(u(),g(r,{key:0,name:"el-icon-Microphone",size:40})):(u(),g(r,{key:1,name:"el-icon-Mute",size:40}))],2)):w("",!0)]),Y(l("div",uo,[Y(l("video",{class:"h-full w-full object-scale-down",src:e(we)?(Z=e(s).digital)==null?void 0:Z.vertical_stay_video:(Re=e(s).digital)==null?void 0:Re.wide_stay_video,autoplay:"",muted:"",loop:""},null,8,po),[[ee,!e(Se)]]),Y(l("video",{class:"h-full w-full object-scale-down",src:e(we)?(Ee=e(s).digital)==null?void 0:Ee.vertical_talk_video:(Te=e(s).digital)==null?void 0:Te.wide_talk_video,autoplay:"",muted:"",loop:""},null,8,fo),[[ee,e(Se)]])],512),[[ee,e(y)==2]])],4)]}),_:1},8,["content","font"]),p(Pt,{ref_key:"popupRef",ref:te,async:!0,title:"问题反馈",appendToBody:!1,class:"feedback-pop"},{footer:c(()=>[l("div",_o,[p(v,{type:"primary",onClick:ze},{default:c(()=>o[6]||(o[6]=[j(" 提交反馈 ")])),_:1})])]),default:c(()=>[p(at,{modelValue:e(L).content,"onUpdate:modelValue":o[2]||(o[2]=Z=>e(L).content=Z),rows:"8",placeholder:"描述一下你遇到了什么问题"},null,8,["modelValue"])]),_:1},512)])}}}),Mo=jt(mo,[["__scopeId","data-v-d54adb5e"]]);export{Mo as _};
