import{_}from"./C2uyROqA.js";import{l as f,j as x,b as v,M as b,N as k,O as t,a7 as a,Z as r,a6 as p,u as c,n as q}from"./Dp9aCaJ6.js";const w={class:"flex items-center"},y={class:"bg-white px-4 rounded"},g={class:"mx-2 text-[#000] flex-1 line-clamp-1"},h={class:"mt-2"},E={class:"whitespace-pre-line"},N=["contenteditable"],T={class:"whitespace-pre-line"},B=["contenteditable"],V=f({__name:"cvs-data-item",props:{index:{},name:{},q:{},a:{}},emits:["delete","update:q","update:a"],setup(C,{emit:m}){const o=m,d=x(),i=v(!1),u=async()=>{i.value=!0,await q(),d.value.focus()};return(n,e)=>{const l=_;return b(),k("div",null,[t("div",w,[t("span",y," #"+a(n.index+1),1),t("span",g,a(n.name),1),r(l,{class:"icon-delete text-primary cursor-pointer mr-2",name:"el-icon-EditPen",onClick:u}),r(l,{class:"icon-delete text-primary cursor-pointer",name:"el-icon-Delete",onClick:e[0]||(e[0]=s=>o("delete"))})]),t("div",h,[t("div",E,[e[3]||(e[3]=p(" q: ")),t("span",{ref_key:"editRef",ref:d,contenteditable:c(i),onInput:e[1]||(e[1]=s=>o("update:q",s.target.innerText))},a(n.q),41,N)]),t("div",T,[e[4]||(e[4]=p(" a: ")),t("span",{contenteditable:c(i),onInput:e[2]||(e[2]=s=>o("update:a",s.target.innerText))},a(n.a),41,B)])])])}}});export{V as _};
