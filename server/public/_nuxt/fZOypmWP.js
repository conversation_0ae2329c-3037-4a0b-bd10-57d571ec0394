import{K as me,X as H,a4 as pe,Z as ue,a5 as ge,M as ye,a6 as Ie,J as N,T as _e,a7 as ze,a8 as Le,a9 as Se,aa as Ce,ab as Ee,ac as Ne,O as we,ad as xe,ae as Oe,V,af as A,ag as Te,P as be,ah as $e,ai as Ae,W as F,aj as Ve,ak as Me}from"./CylNgAGi.js";import{l as se,x as ve,b,j as Re,m as g,c as ce,F as ke,M as w,a1 as de,Z as c,a0 as z,O as y,X as d,u as e,a9 as Be,a4 as L,N as E,_ as te,a3 as Xe,aq as Ye,V as ae,a2 as fe,ac as Pe,at as De,e as Fe,n as he,aa as He,ab as je,a8 as Ke,W as We,H as Ze,a7 as qe}from"./Dp9aCaJ6.js";import{t as ie}from"./BGZ_z1Ox.js";import{i as Ge}from"./Cg11N2Sp.js";const Je=me({urlList:{type:H(Array),default:()=>pe([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:Boolean,teleported:Boolean,closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},crossorigin:{type:H(String)}}),Qe={close:()=>!0,switch:m=>ue(m),rotate:m=>ue(m)},Ue=["src","crossorigin"],ea=se({name:"ElImageViewer"}),aa=se({...ea,props:Je,emits:Qe,setup(m,{expose:x,emit:f}){var M;const u=m,p={CONTAIN:{name:"contain",icon:ve(xe)},ORIGINAL:{name:"original",icon:ve(Oe)}},{t:ne}=ge(),l=ye("image-viewer"),{nextZIndex:R}=Ie(),S=b(),C=b([]),O=Fe(),k=b(!0),r=b(u.initialIndex),T=Re(p.CONTAIN),n=b({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),B=b((M=u.zIndex)!=null?M:R()),oe=g(()=>{const{urlList:t}=u;return t.length<=1}),j=g(()=>r.value===0),K=g(()=>r.value===u.urlList.length-1),X=g(()=>u.urlList[r.value]),le=g(()=>[l.e("btn"),l.e("prev"),l.is("disabled",!u.infinite&&j.value)]),W=g(()=>[l.e("btn"),l.e("next"),l.is("disabled",!u.infinite&&K.value)]),Y=g(()=>{const{scale:t,deg:s,offsetX:o,offsetY:h,enableTransition:_}=n.value;let v=o/t,I=h/t;switch(s%360){case 90:case-270:[v,I]=[I,-v];break;case 180:case-180:[v,I]=[-v,-I];break;case 270:case-90:[v,I]=[-I,v];break}const $={transform:`scale(${t}) rotate(${s}deg) translate(${v}px, ${I}px)`,transition:_?"transform .3s":""};return T.value.name===p.CONTAIN.name&&($.maxWidth=$.maxHeight="100%"),$});function P(){Z(),f("close")}function re(){const t=ie(o=>{switch(o.code){case A.esc:u.closeOnPressEscape&&P();break;case A.space:U();break;case A.left:ee();break;case A.up:i("zoomIn");break;case A.right:a();break;case A.down:i("zoomOut");break}}),s=ie(o=>{const h=o.deltaY||o.deltaX;i(h<0?"zoomIn":"zoomOut",{zoomRate:u.zoomRate,enableTransition:!1})});O.run(()=>{V(document,"keydown",t),V(document,"wheel",s)})}function Z(){O.stop()}function q(){k.value=!1}function G(t){k.value=!1,t.target.alt=ne("el.image.error")}function J(t){if(k.value||t.button!==0||!S.value)return;n.value.enableTransition=!1;const{offsetX:s,offsetY:o}=n.value,h=t.pageX,_=t.pageY,v=ie($=>{n.value={...n.value,offsetX:s+$.pageX-h,offsetY:o+$.pageY-_}}),I=V(document,"mousemove",v);V(document,"mouseup",()=>{I()}),t.preventDefault()}function Q(){n.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function U(){if(k.value)return;const t=Te(p),s=Object.values(p),o=T.value.name,_=(s.findIndex(v=>v.name===o)+1)%t.length;T.value=p[t[_]],Q()}function D(t){const s=u.urlList.length;r.value=(t+s)%s}function ee(){j.value&&!u.infinite||D(r.value-1)}function a(){K.value&&!u.infinite||D(r.value+1)}function i(t,s={}){if(k.value)return;const{minScale:o,maxScale:h}=u,{zoomRate:_,rotateDeg:v,enableTransition:I}={zoomRate:u.zoomRate,rotateDeg:90,enableTransition:!0,...s};switch(t){case"zoomOut":n.value.scale>o&&(n.value.scale=Number.parseFloat((n.value.scale/_).toFixed(3)));break;case"zoomIn":n.value.scale<h&&(n.value.scale=Number.parseFloat((n.value.scale*_).toFixed(3)));break;case"clockwise":n.value.deg+=v,f("rotate",n.value.deg);break;case"anticlockwise":n.value.deg-=v,f("rotate",n.value.deg);break}n.value.enableTransition=I}return ce(X,()=>{he(()=>{const t=C.value[0];t!=null&&t.complete||(k.value=!0)})}),ce(r,t=>{Q(),f("switch",t)}),ke(()=>{var t,s;re(),(s=(t=S.value)==null?void 0:t.focus)==null||s.call(t)}),x({setActiveItem:D}),(t,s)=>(w(),de(De,{to:"body",disabled:!t.teleported},[c(Pe,{name:"viewer-fade",appear:""},{default:z(()=>[y("div",{ref_key:"wrapper",ref:S,tabindex:-1,class:d(e(l).e("wrapper")),style:fe({zIndex:B.value})},[y("div",{class:d(e(l).e("mask")),onClick:s[0]||(s[0]=Be(o=>t.hideOnClickModal&&P(),["self"]))},null,2),L(" CLOSE "),y("span",{class:d([e(l).e("btn"),e(l).e("close")]),onClick:P},[c(e(N),null,{default:z(()=>[c(e(_e))]),_:1})],2),L(" ARROW "),e(oe)?L("v-if",!0):(w(),E(te,{key:0},[y("span",{class:d(e(le)),onClick:ee},[c(e(N),null,{default:z(()=>[c(e(ze))]),_:1})],2),y("span",{class:d(e(W)),onClick:a},[c(e(N),null,{default:z(()=>[c(e(Le))]),_:1})],2)],64)),L(" ACTIONS "),y("div",{class:d([e(l).e("btn"),e(l).e("actions")])},[y("div",{class:d(e(l).e("actions__inner"))},[c(e(N),{onClick:s[1]||(s[1]=o=>i("zoomOut"))},{default:z(()=>[c(e(Se))]),_:1}),c(e(N),{onClick:s[2]||(s[2]=o=>i("zoomIn"))},{default:z(()=>[c(e(Ce))]),_:1}),y("i",{class:d(e(l).e("actions__divider"))},null,2),c(e(N),{onClick:U},{default:z(()=>[(w(),de(Xe(e(T).icon)))]),_:1}),y("i",{class:d(e(l).e("actions__divider"))},null,2),c(e(N),{onClick:s[3]||(s[3]=o=>i("anticlockwise"))},{default:z(()=>[c(e(Ee))]),_:1}),c(e(N),{onClick:s[4]||(s[4]=o=>i("clockwise"))},{default:z(()=>[c(e(Ne))]),_:1})],2)],2),L(" CANVAS "),y("div",{class:d(e(l).e("canvas"))},[(w(!0),E(te,null,Ye(t.urlList,(o,h)=>He((w(),E("img",{ref_for:!0,ref:_=>C.value[h]=_,key:o,src:o,style:fe(e(Y)),class:d(e(l).e("img")),crossorigin:t.crossorigin,onLoad:q,onError:G,onMousedown:J},null,46,Ue)),[[je,h===r.value]])),128))],2),ae(t.$slots,"default")],6)]),_:3})],8,["disabled"]))}});var ta=we(aa,[["__file","image-viewer.vue"]]);const sa=be(ta),na=me({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:H([String,Object])},previewSrcList:{type:H(Array),default:()=>pe([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},crossorigin:{type:H(String)}}),oa={load:m=>m instanceof Event,error:m=>m instanceof Event,switch:m=>ue(m),close:()=>!0,show:()=>!0},la=["src","loading","crossorigin"],ra={key:0},ia=se({name:"ElImage",inheritAttrs:!1}),ua=se({...ia,props:na,emits:oa,setup(m,{emit:x}){const f=m;let M="";const{t:u}=ge(),p=ye("image"),ne=Ke(),l=$e(),R=b(),S=b(!1),C=b(!0),O=b(!1),k=b(),r=b(),T=F&&"loading"in HTMLImageElement.prototype;let n,B;const oe=g(()=>[p.e("inner"),X.value&&p.e("preview"),C.value&&p.is("loading")]),j=g(()=>ne.style),K=g(()=>{const{fit:a}=f;return F&&a?{objectFit:a}:{}}),X=g(()=>{const{previewSrcList:a}=f;return Array.isArray(a)&&a.length>0}),le=g(()=>{const{previewSrcList:a,initialIndex:i}=f;let t=i;return i>a.length-1&&(t=0),t}),W=g(()=>f.loading==="eager"?!1:!T&&f.loading==="lazy"||f.lazy),Y=()=>{F&&(C.value=!0,S.value=!1,R.value=f.src)};function P(a){C.value=!1,S.value=!1,x("load",a)}function re(a){C.value=!1,S.value=!0,x("error",a)}function Z(){Ge(k.value,r.value)&&(Y(),J())}const q=Ae(Z,200,!0);async function G(){var a;if(!F)return;await he();const{scrollContainer:i}=f;Ve(i)?r.value=i:Ze(i)&&i!==""?r.value=(a=document.querySelector(i))!=null?a:void 0:k.value&&(r.value=Me(k.value)),r.value&&(n=V(r,"scroll",q),setTimeout(()=>Z(),100))}function J(){!F||!r.value||!q||(n==null||n(),r.value=void 0)}function Q(a){if(a.ctrlKey){if(a.deltaY<0)return a.preventDefault(),!1;if(a.deltaY>0)return a.preventDefault(),!1}}function U(){X.value&&(B=V("wheel",Q,{passive:!1}),M=document.body.style.overflow,document.body.style.overflow="hidden",O.value=!0,x("show"))}function D(){B==null||B(),document.body.style.overflow=M,O.value=!1,x("close")}function ee(a){x("switch",a)}return ce(()=>f.src,()=>{W.value?(C.value=!0,S.value=!1,J(),G()):Y()}),ke(()=>{W.value?G():Y()}),(a,i)=>(w(),E("div",{ref_key:"container",ref:k,class:d([e(p).b(),a.$attrs.class]),style:fe(e(j))},[S.value?ae(a.$slots,"error",{key:0},()=>[y("div",{class:d(e(p).e("error"))},qe(e(u)("el.image.error")),3)]):(w(),E(te,{key:1},[R.value!==void 0?(w(),E("img",We({key:0},e(l),{src:R.value,loading:a.loading,style:e(K),class:e(oe),crossorigin:a.crossorigin,onClick:U,onLoad:P,onError:re}),null,16,la)):L("v-if",!0),C.value?(w(),E("div",{key:1,class:d(e(p).e("wrapper"))},[ae(a.$slots,"placeholder",{},()=>[y("div",{class:d(e(p).e("placeholder"))},null,2)])],2)):L("v-if",!0)],64)),e(X)?(w(),E(te,{key:2},[O.value?(w(),de(e(sa),{key:0,"z-index":a.zIndex,"initial-index":e(le),infinite:a.infinite,"zoom-rate":a.zoomRate,"min-scale":a.minScale,"max-scale":a.maxScale,"url-list":a.previewSrcList,"hide-on-click-modal":a.hideOnClickModal,teleported:a.previewTeleported,"close-on-press-escape":a.closeOnPressEscape,onClose:D,onSwitch:ee},{default:z(()=>[a.$slots.viewer?(w(),E("div",ra,[ae(a.$slots,"viewer")])):L("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","zoom-rate","min-scale","max-scale","url-list","hide-on-click-modal","teleported","close-on-press-escape"])):L("v-if",!0)],64)):L("v-if",!0)],6))}});var ca=we(ua,[["__file","image.vue"]]);const pa=be(ca);export{pa as E,sa as a};
