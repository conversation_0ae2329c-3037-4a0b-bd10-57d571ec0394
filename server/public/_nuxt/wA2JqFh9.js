import{e as N,E as D}from"./CLVDtxqA.js";import{E as w}from"./JBMcerbz.js";import{_ as y}from"./C2uyROqA.js";/* empty css        */import{l as g,b as C,m as b,c as I,M as i,N as u,Z as l,O as r,u as v,a1 as S,y as B,a7 as z,_ as V,a4 as F,X as R,V as U,a0 as x,a6 as $,aq as q}from"./Dp9aCaJ6.js";import{_ as A}from"./DlAUqK2U.js";const L={class:"ml-[10px] flex-1 min-w-0"},M={key:1,class:"line-clamp-1"},O=g({__name:"item",props:{modelValue:{},name:{},itemId:{}},emits:["update:modelValue","edit","delete"],setup(k,{emit:E}){const p=k,n=E,s=C(""),m=C(!1),a=b({get(){return p.modelValue},set(d){n("update:modelValue",d)}}),e=b(()=>Number(a.value)===Number(p.itemId)),_=()=>{m.value=!1,n("edit",s.value)};return I(()=>p.modelValue,()=>{m.value=!1}),I(()=>p.name,d=>{s.value=d},{immediate:!0}),(d,o)=>{const t=y,c=N;return i(),u("div",{class:R(["px-[10px] h-[40px] flex items-center rounded-[8px] mb-[10px] cursor-pointer border border-solid border-[transparent]",{"!bg-[#ECEBF9] dark:!bg-[#333]":v(e)}]),onClick:o[4]||(o[4]=f=>a.value=d.itemId)},[l(t,{name:"el-icon-ChatDotRound"}),r("div",L,[v(m)?(i(),S(c,{key:0,modelValue:v(s),"onUpdate:modelValue":o[0]||(o[0]=f=>B(s)?s.value=f:null),size:"small"},null,8,["modelValue"])):(i(),u("div",M,z(d.name),1))]),v(e)?(i(),u(V,{key:0},[v(m)?(i(),u(V,{key:0},[r("div",{class:"cursor-pointer mr-[6px] flex",onClick:_},[l(t,{name:"el-icon-Select"})]),r("div",{class:"cursor-pointer flex",onClick:o[1]||(o[1]=f=>m.value=!1)},[l(t,{name:"el-icon-CloseBold"})])],64)):(i(),u(V,{key:1},[r("div",{class:"cursor-pointer mr-[6px] flex",onClick:o[2]||(o[2]=f=>m.value=!0)},[l(t,{name:"el-icon-EditPen"})]),r("div",{class:"cursor-pointer flex",onClick:o[3]||(o[3]=f=>n("delete",d.itemId))},[l(t,{name:"el-icon-Delete"})])],64))],64)):F("",!0)],2)}}}),P={class:"session bg-body rounded-[12px] flex flex-col"},T={class:"p-[16px]"},X={class:"flex-1 min-h-0"},Z={class:"px-[16px]"},j={class:"p-[16px]"},G=g({__name:"index",props:{data:{},modelValue:{}},emits:["add","clear","edit","delete","update:modelValue"],setup(k,{emit:E}){const p=k,n=E,s=b({get(){return p.modelValue},set(a){n("update:modelValue",a)}}),m=(a,e)=>{n("edit",{name:a,id:e})};return(a,e)=>{const _=D,d=w,o=y;return i(),u("div",P,[U(a.$slots,"top",{},void 0,!0),r("div",T,[l(_,{type:"primary",class:"w-full session-add-btn",size:"large",onClick:e[0]||(e[0]=t=>n("add"))},{default:x(()=>e[3]||(e[3]=[$(" + 新建会话 ")])),_:1})]),r("div",X,[l(d,null,{default:x(()=>[r("div",Z,[(i(!0),u(V,null,q(a.data,t=>(i(),u("div",{key:t.id},[l(O,{modelValue:v(s),"onUpdate:modelValue":e[1]||(e[1]=c=>B(s)?s.value=c:null),"item-id":t.id,name:t.name,onDelete:c=>n("delete",t.id),onEdit:c=>m(c,t.id)},null,8,["modelValue","item-id","name","onDelete","onEdit"])]))),128))])]),_:1})]),r("div",j,[l(_,{class:"w-full",plain:"",size:"large",onClick:e[2]||(e[2]=t=>n("clear"))},{icon:x(()=>[l(o,{name:"el-icon-Delete"})]),default:x(()=>[e[4]||(e[4]=$(" 清除所有会话 "))]),_:1})])])}}}),h=A(G,[["__scopeId","data-v-a88d677c"]]);export{h as _};
