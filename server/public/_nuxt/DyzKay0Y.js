import{_ as x}from"./DUH6hp3a.js";import{E as h,a as y}from"./C3WqRr9V.js";import"./DyU4wb-Q.js";import{u as $,a as w}from"./D4n1ODfq.js";import{l as T,m as k,b as B,c as D,M as r,N as p,O as m,Z as l,a0 as c,_ as M,aq as S,u as e,a4 as E,a1 as f,a7 as N,aa as z,a3 as L,ab as V}from"./Dp9aCaJ6.js";import{_ as j}from"./NpR3wH9t.js";import{_ as A}from"./4kRymzr7.js";import{_ as I}from"./BrYehgj2.js";import O from"./DP-Y8-wb.js";import P from"./jjyy7nWN.js";import{_ as q}from"./DasH8POW.js";import F from"./B2zKX0pA.js";import{_ as R}from"./CIUsJCUN.js";import{_ as Z}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./hdrcCGKH.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./BqRNHXMi.js";import"./Dp82IE7N.js";import"./noc3S0VN.js";import"./DNzFOP1K.js";import"./CH_T-XC0.js";import"./DQ0LyTZ-.js";import"./Cri6GG3T.js";import"./BDGxxzzg.js";/* empty css        *//* empty css        */import"./YbtjEuVe.js";import"./BNpY2ENe.js";import"./DBmuL_Q3.js";import"./B-7IXDGe.js";/* empty css        */import"./CZOxvSF0.js";/* empty css        */import"./Cmz8--1B.js";import"./C3h7pD7s.js";import"./DCa8BdSG.js";import"./DCTLXrZ8.js";import"./9MAnF5ll.js";import"./D4s3zOA5.js";import"./CQqsfOf-.js";import"./D-tOg06u.js";import"./kwcMwiMO.js";import"./DlKZEFPo.js";import"./CmCIYpsG.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./CI1jLEqI.js";import"./CmLVN9Ir.js";import"./C-srSIka.js";import"./BtNppg1N.js";const G={class:"h-full relative z-20 bg-white"},H={class:"flex flex-col items-center justify-center"},J={class:"text-lg mt-[6px]"},K={class:"w-[360px] h-full"},Q=T({__name:"index",setup(U){const u={Avatar:j,Dub:A,Music:I,Background:O,Text:P,Captions:q,Maps:F,Prospect:R},d=$(),{tabsState:a,initTabs:v,changeTabs:b}=w(),s=k({get(){return a.value.isCollapsed},set(i){a.value.isCollapsed=i}}),n=B(!1);return v(),D(()=>a.value.current,async i=>{d.setActiveObjectByType(i)}),(i,o)=>{const _=x,C=y,g=h;return r(),p("div",{class:"control-panel h-full",onMouseenter:o[2]||(o[2]=t=>n.value=!0),onMouseleave:o[3]||(o[3]=t=>n.value=!1)},[m("div",G,[l(g,{"tab-position":"left",class:"h-full",type:"card","model-value":e(a).current,onTabChange:o[0]||(o[0]=t=>e(b)(t))},{default:c(()=>[(r(!0),p(M,null,S(e(a).tabs,t=>(r(),f(C,{key:t.id,name:t.id,lazy:""},{label:c(()=>[m("div",H,[l(_,{name:t.icon,size:22},null,8,["name"]),m("span",J,N(t.label),1)])]),default:c(()=>[z(m("div",K,[(r(),f(L(u[t.component])))],512),[[V,!e(s)]])]),_:2},1032,["name"]))),128))]),_:1},8,["model-value"])]),e(s)||e(n)?(r(),p("div",{key:0,class:"panel-left-arrow",onClick:o[1]||(o[1]=t=>s.value=!e(s))},[l(_,{class:"mr-1",name:`el-icon-${e(s)?"CaretRight":"CaretLeft"}`},null,8,["name"])])):E("",!0)],32)}}}),Wt=Z(Q,[["__scopeId","data-v-8d50fa1f"]]);export{Wt as default};
