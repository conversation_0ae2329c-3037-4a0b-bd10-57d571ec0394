import{E as c}from"./L7ewHh_h.js";import{E as g}from"./DdkLcgv7.js";import{E as x}from"./DqzU5vn5.js";import{b as V,v as w}from"./BQ-RMI0l.js";/* empty css        *//* empty css        */import{r as y,x as D,y as b,f as e,e as k}from"./Cz8cblQP.js";import{DrawModeEnum as p,DrawTypeEnum as v}from"./tONJIxwY.js";import{_ as O}from"./D7GDeVUX.js";import U from"./Dqj5Wr4p.js";import E from"./wxC3ipXC.js";import B from"./C_FJdJEG.js";import{_ as A}from"./weOcB_-Y.js";import{D as S}from"./D2Ae7YcP.js";import z from"./D_Ppjy_b.js";import{_ as N}from"./BuooilRq.js";import C from"./CwCrR0bP.js";import{l as L,F as M,u as o,M as a,N as l,Z as r,a0 as i,O as s,a1 as P,a4 as $,aa as F}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./Ci86EFhe.js";import"./BBQxRZuk.js";import"./BOZQs96k.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./Ct8b-CPS.js";import"./B_BP6MI7.js";import"./DhTLUUeS.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CGQC2hmt.js";import"./BXLvtum9.js";import"./BHIHQvwt.js";import"./9Bti1uB6.js";/* empty css        */import"./Bbo2vmge.js";import"./De57gGYj.js";/* empty css        */import"./C2S7-d6r.js";import"./B36mVPSB.js";import"./Cwr-KwFA.js";import"./DpOQaFAg.js";import"./xixvWuCN.js";import"./C1aUxTFE.js";import"./WSQgGJ_U.js";import"./B5ZCswNG.js";import"./Bb7qOt1h.js";import"./Cpg3PDWZ.js";import"./BfAAiWRs.js";import"./DV66vBKK.js";import"./djnMiA6p.js";import"./Cv6HhfEG.js";import"./IZ5UIo3-.js";import"./DzZ0m6Bt.js";/* empty css        *//* empty css        */import"./1sgdir_0.js";import"./D49putpZ.js";import"./CMlLt_gO.js";import"./DjwCd26w.js";import"./B3UVgHox.js";import"./CcPlX2kz.js";import"./OWmhe4dw.js";import"./BVqQ2B7o.js";import"./BWdDF8rn.js";import"./oDt550x8.js";import"./CMYCTDrJ.js";import"./8yhmlZGP.js";import"./DlKZEFPo.js";import"./ajKoJK3T.js";import"./C2SkKptv.js";const R={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},T={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},j={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},Z=L({__name:"doubao",setup(q){const n=V();return M(()=>{y({draw_api:p.DOUBAO,draw_model:p.DOUBAO,action:"generate",prompt:"",engine:"high_aes_general_v20_L",negative_prompt:"",size:"512x512",complex_params:{seed:"",ddim_steps:20}}),D.model=p.DOUBAO,b()}),(G,t)=>{const d=c,u=g,_=x,f=w;return o(n).config.switch.doubao_status?(a(),l("div",R,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:i(()=>[s("div",T,[r(O,{modelValue:o(e).draw_type,"onUpdate:modelValue":t[0]||(t[0]=m=>o(e).draw_type=m)},null,8,["modelValue"]),r(U,{modelValue:o(e).prompt,"onUpdate:modelValue":t[1]||(t[1]=m=>o(e).prompt=m),model:o(p).DOUBAO},null,8,["modelValue","model"]),o(e).draw_type===o(v).img2img?(a(),P(E,{key:0,modelValue:o(e).image_mask,"onUpdate:modelValue":t[2]||(t[2]=m=>o(e).image_mask=m),type:"image"},null,8,["modelValue"])):$("",!0),r(z,{modelValue:o(e).size,"onUpdate:modelValue":t[3]||(t[3]=m=>o(e).size=m)},null,8,["modelValue"]),r(N,{modelValue:o(e).engine,"onUpdate:modelValue":t[4]||(t[4]=m=>o(e).engine=m),draw_type:o(e).draw_type},null,8,["modelValue","draw_type"]),r(C,{modelValue:o(e).complex_params,"onUpdate:modelValue":t[5]||(t[5]=m=>o(e).complex_params=m)},null,8,["modelValue"])]),r(A)]),_:1}),F(r(B,{"element-loading-text":"正在加载数据..."},null,512),[[f,o(k)]])])):(a(),l("div",j,[r(_,null,{icon:i(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(S)},null,8,["src"])]),title:i(()=>t[6]||(t[6]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),nt=I(Z,[["__scopeId","data-v-a401c716"]]);export{nt as default};
