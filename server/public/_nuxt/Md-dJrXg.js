import{E as N,a as O}from"./Bz4jch3v.js";import{j as b,dd as q,de as g,dg as j,e as B,o as L,p as A,dh as M}from"./BsMqt_su.js";import{_ as W}from"./CQ4ha0wy.js";import{P as $}from"./Ds7ZwOv-.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{u as z}from"./CcPlX2kz.js";import{l as G,j as u,r as Z,c as V,M as c,a1 as f,a0 as t,O as E,Z as s,u as l,a4 as v}from"./Dp9aCaJ6.js";const H={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},ne=G({__name:"changePwdPop",props:{mobile:{},email:{}},emits:["close"],setup(y,{expose:C,emit:J}){const _=y,p=u();b(),b();const d=u(),w=u(),S={mobile:[{required:!0,message:"请输入手机号码"}],email:[{required:!0,message:"请输入邮箱账号"},{type:"email",message:"请输入正确的邮箱账号"}],code:[{required:!0,message:"请输入验证码",trigger:["change","blur"]}],password:[{required:!0,message:"请输入6-20位数字+字母或符号组合",trigger:["change","blur"]},{min:6,max:20,message:"密码长度应为6-20",trigger:["change","blur"]}],password_confirm:[{validator(o,a,m){a===""?m(new Error("请再次输入密码")):a!==e.password?m(new Error("两次输入的密码不一致")):m()},trigger:["change","blur"]}]},e=Z({email:"",mobile:"",password:"",code:"",password_confirm:""});V(()=>_.mobile,o=>{o&&(e.mobile=o)},{immediate:!0}),V(()=>_.email,o=>{o&&(e.email=o)},{immediate:!0});const k=async()=>{var o;e.mobile?await x():await F(),(o=w.value)==null||o.start()},x=async()=>{var o;await((o=d.value)==null?void 0:o.validateField(["mobile"])),await q({scene:g.FIND_PASSWORD,mobile:e.mobile})},F=async()=>{var o;await((o=d.value)==null?void 0:o.validateField(["email"])),await j({scene:g.FIND_PASSWORD,email:e.email})},R=async()=>{var o;await((o=d.value)==null?void 0:o.validate()),await M({...e,scene:e.mobile?1:2}),p.value.close()},{lockFn:h,isLock:K}=z(R);return C({open:()=>{p.value.open()}}),(o,a)=>{const m=N,P=O,i=B,n=L,I=W,U=A,D=$;return c(),f(D,{ref_key:"popRef",ref:p,title:"设置登录密码",async:!0,"confirm-button-text":"确认",onConfirm:l(h),onClose:a[5]||(a[5]=r=>o.$emit("close"))},{default:t(()=>[E("div",null,[s(U,{ref_key:"formRef",ref:d,size:"large",model:l(e),rules:S},{default:t(()=>[l(e).mobile?(c(),f(n,{key:0,prop:"mobile"},{default:t(()=>[s(i,{modelValue:l(e).mobile,"onUpdate:modelValue":a[0]||(a[0]=r=>l(e).mobile=r),placeholder:"请输入手机号",disabled:""},{prepend:t(()=>[s(P,{"model-value":"+86",style:{width:"80px"},disabled:""},{default:t(()=>[s(m,{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1})):v("",!0),l(e).email?(c(),f(n,{key:1,prop:"email",disabled:""},{default:t(()=>[s(i,{modelValue:l(e).email,"onUpdate:modelValue":a[1]||(a[1]=r=>l(e).email=r),placeholder:"请输入邮箱账号"},null,8,["modelValue"])]),_:1})):v("",!0),s(n,{prop:"code"},{default:t(()=>[s(i,{modelValue:l(e).code,"onUpdate:modelValue":a[2]||(a[2]=r=>l(e).code=r),placeholder:"请输入验证码"},{suffix:t(()=>[E("div",H,[s(I,{ref_key:"verificationCodeRef",ref:w,onClickGet:k},null,512)])]),_:1},8,["modelValue"])]),_:1}),s(n,{prop:"password"},{default:t(()=>[s(i,{modelValue:l(e).password,"onUpdate:modelValue":a[3]||(a[3]=r=>l(e).password=r),placeholder:"请输入6-20位数字+字母或符号组合",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),s(n,{prop:"password_confirm"},{default:t(()=>[s(i,{modelValue:l(e).password_confirm,"onUpdate:modelValue":a[4]||(a[4]=r=>l(e).password_confirm=r),placeholder:"请再次输入密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["onConfirm"])}}});export{ne as _};
