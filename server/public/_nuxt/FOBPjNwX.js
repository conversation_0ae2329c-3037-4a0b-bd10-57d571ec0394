import{_ as F}from"./Bj9H9xtu.js";import{j as M,l as T,E as q,v as A}from"./BBthjZaB.js";import{E as O}from"./vwcCUF2-.js";import{E as P}from"./Bwa6YuoW.js";import{E as U}from"./CEYTy2ef.js";import{E as V}from"./Dxaq3UbQ.js";import{E as $}from"./C5kPIjdK.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{u as j}from"./CcPlX2kz.js";import{c as H}from"./Cq2NhlyP.js";import{ModelEnums as p}from"./BLV0QRdm.js";import{l as Y,b as k,r as Z,ax as G,M as e,N as c,Z as n,a0 as r,aa as _,a1 as m,O as t,u as s,_ as J,aq as K,a7 as E}from"./Dp9aCaJ6.js";const Q={class:"h-full flex flex-col"},W={class:"flex items-center"},X={class:"flex-1 min-h-0"},oo=["onClick"],eo={class:"line-clamp-2"},to={class:"flex items-center mt-1 text-tx-secondary"},so={class:"ml-1 text-xs"},yo=Y({__name:"search-history",setup(io){const x=M(),y=T(),u=k(),f=k(),S={[p.BASE]:"local-icon-search_base",[p.ENHANCE]:"local-icon-search_copilot",[p.STUDY]:"local-icon-search_research"},d=()=>{var i;(i=s(f))==null||i.hide()},o=Z({pageNo:1,count:1,pageSize:15,lists:[]}),g=async()=>{if(!x.isLogin)return;const i=await H({page_no:o.pageNo,page_size:o.pageSize});o.count=i.count,o.pageNo===1&&(o.lists=[]),o.lists.push(...i.lists)},N=async()=>{o.count>=o.pageNo*o.pageSize&&(o.pageNo++,await g())},{lockFn:b,isLock:w}=j(async()=>{o.pageNo=1,await g()});return(i,v)=>{const l=F,h=q,z=O,C=P,L=U,B=V,R=G("click-outside"),D=$,I=A;return e(),c("div",null,[n(z,{effect:"light",content:"历史搜索",placement:"bottom"},{default:r(()=>[_((e(),m(h,{link:"",ref_key:"buttonRef",ref:u},{icon:r(()=>[n(l,{name:"local-icon-clock",size:18})]),_:1})),[[R,d]])]),_:1}),n(B,{ref_key:"popoverRef",ref:f,"virtual-ref":s(u),trigger:"click",width:"300px","virtual-triggering":"","popper-style":{bottom:"20px"},onShow:s(b)},{default:r(()=>[t("div",Q,[t("div",W,[v[0]||(v[0]=t("span",{class:"mr-auto"}," 历史搜索 ",-1)),t("div",null,[n(h,{link:"",onClick:d},{icon:r(()=>[n(l,{name:"el-icon-Close",size:18})]),_:1})])]),_((e(),c("div",X,[s(o).count>0?(e(),m(C,{key:0,class:"h-full"},{default:r(()=>[_((e(),c("div",null,[(e(!0),c(J,null,K(s(o).lists,a=>(e(),c("div",{key:a.id,class:"cursor-pointer p-[12px] hover:bg-page",onClick:no=>s(y).push({query:{id:a.id}})},[t("div",eo,E(a.ask),1),t("div",to,[n(l,{name:S[a.model],size:14},null,8,["name"]),t("span",so,E(a.create_time),1)])],8,oo))),128))])),[[D,N]])]),_:1})):(e(),m(L,{key:1,"image-size":150}))])),[[I,s(w)]])])]),_:1},8,["virtual-ref","onShow"])])}}});export{yo as _};
