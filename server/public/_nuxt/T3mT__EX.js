import{d8 as s}from"./DjCZV6kq.js";import{j as y,l as t}from"./Dp9aCaJ6.js";function I(u,e,r){var a=-1,f=u.length;e<0&&(e=-e>f?0:f+e),r=r>f?f:r,r<0&&(r+=f),f=e>r?0:r-e>>>0,e>>>=0;for(var o=Array(f);++a<f;)o[a]=u[a+e];return o}function W(u,e,r){var a=u.length;return r=r===void 0?a:r,!e&&r>=a?u:I(u,e,r)}var D="\\ud800-\\udfff",H="\\u0300-\\u036f",N="\\ufe20-\\ufe2f",J="\\u20d0-\\u20ff",V=H+N+J,F="\\ufe0e\\ufe0f",G="\\u200d",P=RegExp("["+G+D+V+F+"]");function R(u){return P.test(u)}function _(u){return u.split("")}var p="\\ud800-\\udfff",Y="\\u0300-\\u036f",q="\\ufe20-\\ufe2f",B="\\u20d0-\\u20ff",K=Y+q+B,Q="\\ufe0e\\ufe0f",X="["+p+"]",x="["+K+"]",c="\\ud83c[\\udffb-\\udfff]",uu="(?:"+x+"|"+c+")",m="[^"+p+"]",C="(?:\\ud83c[\\udde6-\\uddff]){2}",v="[\\ud800-\\udbff][\\udc00-\\udfff]",eu="\\u200d",A=uu+"?",$="["+Q+"]?",ru="(?:"+eu+"(?:"+[m,C,v].join("|")+")"+$+A+")*",fu=$+A+ru,au="(?:"+[m+x+"?",x,C,v,X].join("|")+")",ou=RegExp(c+"(?="+c+")|"+au+fu,"g");function nu(u){return u.match(ou)||[]}function su(u){return R(u)?nu(u):_(u)}function du(u){return function(e){e=s(e);var r=R(e)?su(e):void 0,a=r?r[0]:e.charAt(0),f=r?W(r,1).join(""):e.slice(1);return a[u]()+f}}var xu=du("toUpperCase");function cu(u){return xu(s(u).toLowerCase())}function tu(u,e,r,a){for(var f=-1,o=u==null?0:u.length;++f<o;)r=e(r,u[f],f,u);return r}function iu(u){return function(e){return u==null?void 0:u[e]}}var bu={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},lu=iu(bu),gu=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Ru="\\u0300-\\u036f",pu="\\ufe20-\\ufe2f",mu="\\u20d0-\\u20ff",Cu=Ru+pu+mu,vu="["+Cu+"]",Au=RegExp(vu,"g");function $u(u){return u=s(u),u&&u.replace(gu,lu).replace(Au,"")}var hu=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function Ou(u){return u.match(hu)||[]}var Uu=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function Su(u){return Uu.test(u)}var h="\\ud800-\\udfff",Eu="\\u0300-\\u036f",Mu="\\ufe20-\\ufe2f",Lu="\\u20d0-\\u20ff",ku=Eu+Mu+Lu,O="\\u2700-\\u27bf",U="a-z\\xdf-\\xf6\\xf8-\\xff",zu="\\xac\\xb1\\xd7\\xf7",Zu="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",wu="\\u2000-\\u206f",Tu=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",S="A-Z\\xc0-\\xd6\\xd8-\\xde",ju="\\ufe0e\\ufe0f",E=zu+Zu+wu+Tu,M="['’]",i="["+E+"]",yu="["+ku+"]",L="\\d+",Iu="["+O+"]",k="["+U+"]",z="[^"+h+E+L+O+U+S+"]",Wu="\\ud83c[\\udffb-\\udfff]",Du="(?:"+yu+"|"+Wu+")",Hu="[^"+h+"]",Z="(?:\\ud83c[\\udde6-\\uddff]){2}",w="[\\ud800-\\udbff][\\udc00-\\udfff]",n="["+S+"]",Nu="\\u200d",b="(?:"+k+"|"+z+")",Ju="(?:"+n+"|"+z+")",l="(?:"+M+"(?:d|ll|m|re|s|t|ve))?",g="(?:"+M+"(?:D|LL|M|RE|S|T|VE))?",T=Du+"?",j="["+ju+"]?",Vu="(?:"+Nu+"(?:"+[Hu,Z,w].join("|")+")"+j+T+")*",Fu="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Gu="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Pu=j+T+Vu,_u="(?:"+[Iu,Z,w].join("|")+")"+Pu,Yu=RegExp([n+"?"+k+"+"+l+"(?="+[i,n,"$"].join("|")+")",Ju+"+"+g+"(?="+[i,n+b,"$"].join("|")+")",n+"?"+b+"+"+l,n+"+"+g,Gu,Fu,L,_u].join("|"),"g");function qu(u){return u.match(Yu)||[]}function Bu(u,e,r){return u=s(u),e=e,e===void 0?Su(u)?qu(u):Ou(u):u.match(e)||[]}var Ku="['’]",Qu=RegExp(Ku,"g");function Xu(u){return function(e){return tu(Bu($u(e).replace(Qu,"")),u,"")}}var u0=Xu(function(u,e,r){return e=e.toLowerCase(),u+(r?cu(e):e)});function e0(u){const e={};for(const r in u)e[u0(r)]=u[r];return e}const a0=()=>{const u=y(),e=t({setup(a,{slots:f}){return()=>{u.value=f.default}}}),r=t({setup(a,{attrs:f,slots:o}){return()=>{if(!u.value)throw new Error("你还没定义复用模板呢！");const d=u.value({...e0(f),$slots:o});return d.length===1?d[0]:d}}});return[e,r]};export{a0 as u};
