import{E as l}from"./Bwa6YuoW.js";import{b as c,_}from"./BBthjZaB.js";/* empty css        */import u from"./hecKYscm.js";import{l as d,M as t,N as r,Z as p,a0 as m,O as i,_ as f,aq as x,u as e,a1 as h,a3 as k,a4 as v}from"./Dp9aCaJ6.js";import"./BS7au2R4.js";import"./JOeTFF2G.js";import"./DBBnJi1E.js";import"./DiPtOrlW.js";import"./B4Y9LdPA.js";import"./CPfnsc5I.js";import"./C-hk8zs7.js";import"./DnsJb5in.js";/* empty css        */import"./DlAUqK2U.js";import"./CfATbMAF.js";import"./Cs2AL6UI.js";import"./D6zuOZjd.js";import"./LcgJfbgu.js";import"./B8jTR_IR.js";import"./CEhNSbAj.js";import"./vwcCUF2-.js";import"./DCTLXrZ8.js";import"./BeV_2vTx.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./CiMbYkUj.js";import"./CkBwdOBh.js";import"./CN8DIg3d.js";import"./Bj9H9xtu.js";import"./rSTVYGS_.js";/* empty css        */import"./Dg-Ifo-9.js";import"./DQyO8kBT.js";import"./Dxaq3UbQ.js";import"./C3aKVaZD.js";import"./9Bti1uB6.js";/* empty css        */import"./ChrTBnYp.js";import"./BzHpYkC6.js";import"./iyxDVKf8.js";import"./1TXbi9j3.js";import"./CEYTy2ef.js";import"./BE7GAo-z.js";import"./DTN-hSKQ.js";import"./BxlVRfMS.js";import"./ND-j-PcX.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";import"./CozUkQBb.js";import"./Cv1u9LLW.js";import"./5N1gywom.js";const N={class:"h-full"},S={class:"index"},Eo=d({__name:"index",setup(g){const a=c();return(y,B)=>{const n=l,s=_;return t(),r("div",null,[p(s,{name:"single-row"},{default:m(()=>[i("div",N,[p(n,null,{default:m(()=>[i("div",S,[(t(!0),r(f,null,x(e(a).pageIndex,o=>(t(),r("div",{key:o.id},[o.isShow?(t(),h(k(e(u)[o.name]),{key:0,prop:o.prop},null,8,["prop"])):v("",!0)]))),128))])]),_:1})])]),_:1})])}}});export{Eo as default};
