import{cn as n}from"./BsMqt_su.js";import{_ as m}from"./B7hDsg13.js";import{l as d,M as a,N as s,Z as u,O as o,_,aq as v,X as f,u as k,a7 as y}from"./Dp9aCaJ6.js";import{_ as x}from"./DlAUqK2U.js";import"./DBRSLWAE.js";import"./BjwsZjjh.js";import"./D1YDx4Yr.js";import"./DCTLXrZ8.js";import"./CieRD4VA.js";import"./Cpwj6wt2.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */const V={class:"mt-[15px]"},h={class:"mt-[10px]"},C=["onClick"],b=d({__name:"dalle-style-picker",props:{modelValue:{default:"vivid"}},emits:["update:modelValue"],setup(l,{emit:r}){const i=r,c=l,{modelValue:t}=n(c,i),p=[{value:"vivid",label:"生动"},{value:"natural",label:"自然"}];return t.value="vivid",(B,N)=>(a(),s("div",V,[u(m,{title:"风格选择",tips:"",required:""}),o("div",h,[(a(),s(_,null,v(p,e=>o("div",{key:e.value,class:f(["picture-style-picker rounded-[12px]",{"picture-style-picker__active":e.value===k(t)}]),onClick:g=>t.value=e.value},y(e.label),11,C)),64))])]))}}),Z=x(b,[["__scopeId","data-v-eca3acfd"]]);export{Z as default};
