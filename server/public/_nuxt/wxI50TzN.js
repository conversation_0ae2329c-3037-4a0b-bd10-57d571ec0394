import{_ as q}from"./DBRSLWAE.js";import{_ as C}from"./CcJ-n_gx.js";import{E}from"./DlvGt6PY.js";import{E as B}from"./Cpwj6wt2.js";import{_ as R}from"./CaGJl-Ve.js";import{a as L,_ as z}from"./BsMqt_su.js";/* empty css        *//* empty css        */import{u as I}from"./CFea67Vu.js";import{G as S}from"./Bo3PTL3c.js";import{l as V,ak as A,m as u,M as i,N as x,Z as e,a0 as a,O as t,_ as D,aq as F,u as r,a1 as f,a4 as O,X as h,a7 as b}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./Bhzc5F_3.js";import"./DugIyZO8.js";import"./D05sCSvx.js";import"./LZhrqBSu.js";import"./BkVrqO-Y.js";import"./Dbma4Whm.js";/* empty css        */import"./BwgbIscv.js";import"./BUuWSjTP.js";import"./DiWHXvJr.js";import"./Cpg3PDWZ.js";import"./CHWQlbQz.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./DVFa_PDB.js";import"./Bb2-23m7.js";import"./DdgDjo0O.js";import"./DwFObZc_.js";import"./B_1915px.js";import"./Cw-YHvx4.js";import"./Bg2e09vA.js";import"./BKnYJ4L1.js";import"./DhvjYsFQ.js";import"./BEVfyfzl.js";import"./Boo8ogMh.js";import"./BATbmIFb.js";import"./HKVwdJ-h.js";import"./BeID4H0V.js";import"./BmDolvjN.js";import"./DDaEm-_F.js";import"./DuliK8cm.js";import"./Ws6-mz4j.js";import"./Ds7ZwOv-.js";import"./BWsz6aKL.js";import"./D__nZHUr.js";const X={class:"h-full flex"},Z={class:"p-4 h-full"},$={class:"w-[300px] h-full flex flex-col bg-body rounded-lg"},G={class:"p-[15px]"},M={class:"flex items-center"},T={class:"flex-1 min-h-0"},j={class:"px-[15px]"},H={class:"flex-1 min-w-0 ml-[15px]"},J={class:"line-clamp-1 text-xl font-medium"},K={class:"flex-1 min-w-0 pr-4 py-4"},P={class:"bg-body rounded-[10px] h-full"},jt=V({__name:"chat",async setup(Q){let s,p;const y=L(),{data:l}=([s,p]=A(()=>I(()=>S(),{default(){return[]},lazy:!0},"$X7Lic9ZOFl")),s=await s,p(),s),m=u(()=>y.query.id),n=u(()=>l.value.find(c=>c.id===Number(m.value))||{});return(c,d)=>{const v=q,_=C,g=E,w=B,k=R,N=z;return i(),x("div",null,[e(N,{name:"default"},{default:a(()=>[t("div",X,[t("div",Z,[t("div",$,[t("div",G,[t("div",M,[e(_,{class:"flex bg-body p-[5px] text-bold rounded-[50%] text-primary shadow-light",to:"/robot_square",replace:!0},{default:a(()=>[e(v,{name:"el-icon-Back",size:18})]),_:1}),d[0]||(d[0]=t("div",{class:"text-xl flex-1 min-w-0 ml-[10px]"}," 智能体广场 ",-1))])]),t("div",T,[e(w,null,{default:a(()=>[t("div",j,[(i(!0),x(D,null,F(r(l),o=>(i(),f(_,{key:o.id,to:{path:"",query:{id:o.id}},class:h(["flex mb-[15px] rounded-[10px] px-[15px] py-[10px] items-center border border-br-light bg-body",{"text-white !border-primary !bg-primary":r(m)==o.id}]),replace:!0},{default:a(()=>[e(g,{class:"w-[50px] h-[50px] rounded-[50%]",src:o.image,alt:""},null,8,["src"]),t("div",H,[t("div",J,b(o.name),1),t("div",{class:h(["line-clamp-1 mt-[4px] text-tx-secondary",{"!text-white":r(m)==o.id}])},b(o.intro),3)])]),_:2},1032,["to","class"]))),128))])]),_:1})])])]),t("div",K,[t("div",P,[r(n).id?(i(),f(k,{key:0,"robot-id":r(n).robot_id,"square-id":r(n).id},null,8,["robot-id","square-id"])):O("",!0)])])])]),_:1})])}}});export{jt as default};
