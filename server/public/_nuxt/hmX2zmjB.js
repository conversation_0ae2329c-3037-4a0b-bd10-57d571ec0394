import{E as f}from"./DySxZBuW.js";import{_ as u}from"./C8ZjkaO0.js";import{b as _}from"./B5S_Er7H.js";/* empty css        */import{l as v,m as r,M as i,N as m,O as p,Z as e,a0 as n,aa as C,ab as b,u as s,V as h,a4 as x}from"./Dp9aCaJ6.js";import g from"./Bn8r6RG7.js";import w from"./BMOEXvyH.js";import S from"./BRLWcgZ9.js";import{_ as N}from"./DlAUqK2U.js";import"./BPrqYaQ_.js";import"./WBO1DCcA.js";import"./DbRYflOL.js";import"./DCTLXrZ8.js";import"./IItDHZjE.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./D1Rl-T7B.js";import"./B8Nk-MqZ.js";import"./DuLVFwrw.js";import"./DBEz5tOh.js";const $={class:"layout-aside h-full flex"},k={class:"h-full flex justify-between flex-col"},y={class:"h-full"},E=v({__name:"index",setup(M){const o=_(),a=r(()=>o.isMobile);return r({get(){return!o.isCollapsed&&a.value},set(t){o.toggleCollapsed(!t)}}),(t,l)=>{const c=f,d=u;return i(),m("div",$,[p("div",k,[e(c,{class:"w-[80px] el-scrollbar"},{default:n(()=>[e(S,{class:"mb-auto"})]),_:1}),e(w)]),C(p("div",y,[e(g,null,{default:n(()=>[h(t.$slots,"aside",{},void 0,!0)]),_:3})],512),[[b,!s(o).isCollapsed]]),t.$slots.panel&&!s(a)?(i(),m("div",{key:0,class:"panel-left-arrow",onClick:l[0]||(l[0]=V=>s(o).toggleCollapsed())},[e(d,{class:"mr-1",name:`el-icon-${s(o).isCollapsed?"CaretRight":"CaretLeft"}`},null,8,["name"])])):x("",!0)])}}}),X=N(E,[["__scopeId","data-v-e34fcc87"]]);export{X as default};
