import{h as v,e as w,o as E,E as y,p as b}from"./DAgm18qP.js";import{_ as U}from"./DMHEbzLi.js";import{_ as g}from"./BCVRiZaz.js";import{E as B}from"./Uw_fYagh.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */import{l as N,b as F,c as I,M as k,N as q,Z as o,a0 as t,O as a,u as n,y as z,a6 as C}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./j7hld3TB.js";import"./BVL3rOLc.js";import"./B4CR6vtE.js";import"./CXZvQ2S9.js";import"./TR-nE6Zk.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */const D={class:"manual-import"},M={class:"py-4"},P={class:"flex-1"},h={class:"flex-1"},O={class:"max-w-[600px]"},le=N({__name:"manual",props:{modelValue:{}},emits:["update:modelValue"],setup(d,{emit:_}){const l=v(d,"modelValue",_),i=F([]);return I(i,p=>{l.value.images=p.map(({url:e})=>e)}),(p,e)=>{const r=w,m=E,f=U,u=g,c=y,x=b,V=B;return k(),q("div",D,[o(V,null,{default:t(()=>[a("div",M,[o(x,{"label-width":"0px"},{default:t(()=>[o(m,null,{default:t(()=>[o(r,{modelValue:n(l).question,"onUpdate:modelValue":e[0]||(e[0]=s=>n(l).question=s),placeholder:"请输入问题"},null,8,["modelValue"])]),_:1}),o(m,null,{default:t(()=>[o(r,{modelValue:n(l).answer,"onUpdate:modelValue":e[1]||(e[1]=s=>n(l).answer=s),placeholder:"请输入问题答案，10000个字以内。",type:"textarea",resize:"none",rows:15,maxlength:"10000"},null,8,["modelValue"])]),_:1}),o(m,null,{default:t(()=>[a("div",P,[a("div",null,[o(u,{files:n(i),"onUpdate:files":e[2]||(e[2]=s=>z(i)?i.value=s:null),type:"image","list-type":"picture-card",limit:9,multiple:"","show-file-list":""},{default:t(()=>[o(f,{name:"el-icon-Plus",size:20})]),_:1},8,["files"])]),e[4]||(e[4]=a("div",{class:"form-tips"},"最多支持上传 9 张图",-1))])]),_:1}),o(m,null,{default:t(()=>[a("div",h,[a("div",O,[o(u,{files:n(l).files,"onUpdate:files":e[3]||(e[3]=s=>n(l).files=s),type:"file","show-file-list":""},{tip:t(()=>e[6]||(e[6]=[a("div",{class:"el-upload__tip"}," 支持上传PDF、docx、excel、等文件格式 ",-1)])),default:t(()=>[o(c,null,{default:t(()=>e[5]||(e[5]=[C("上传附件")])),_:1})]),_:1},8,["files"])])])]),_:1})]),_:1})])]),_:1})])}}});export{le as default};
