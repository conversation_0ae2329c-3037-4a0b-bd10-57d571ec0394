import{E as G}from"./CH0sQbfA.js";import{_ as H}from"./C_7xENts.js";import{E,e as J,s as K,f as Q}from"./CzTOiozM.js";/* empty css        */import{u as R}from"./67xbGseh.js";import{u as Y}from"./CcPlX2kz.js";import{l as ee,b as m,r as se,M as r,N as c,Z as i,a0 as n,O as s,u as o,a6 as V,a4 as P,_ as I,aq as U,a7 as y,X as te,a9 as _}from"./Dp9aCaJ6.js";import{g as le,p as oe}from"./DwFObZc_.js";import{E as h}from"./Bvuso-iR.js";import{E as ie}from"./DdwyL6lX.js";import{E as ae}from"./Pei5_z3G.js";import{E as D}from"./DQS3pGu2.js";import{E as ne}from"./CLJ-nna1.js";import{_ as de}from"./DlAUqK2U.js";import"./CmG-gwTN.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";import"./CaD5i18d.js";import"./DflfJMcq.js";const re={class:"locality-draw-popup"},ce={class:"w-1/2"},ue={class:"px-4"},pe={class:"flex items-center"},ve={class:"flex flex-col"},xe={key:0,style:{height:"500px"},class:"flex justify-center items-center text-tx-placeholder"},fe={class:"mt-4"},me={class:"my-2 px-4"},_e=["onClick"],he={class:"ml-2 text-xs"},ke={key:0,class:"text-tx-placeholder"},ge={class:"flex flex-col px-4"},ye={class:"text-base"},Ce={class:"flex items-center"},we={class:"ml-2 text-tx-regular text-xs"},Ee={class:"flex items-center"},Ve={class:"cursor-pointer"},Pe=["onClick"],Se=["onClick"],$e=["onClick"],ze=["onClick"],Me={class:"text-xs"},Ae=["onClick"],Ie={class:"flex justify-end"},Ue=ee({__name:"add-user",props:{id:{type:Number,default:0}},emits:["success"],setup(N,{expose:B,emit:L}){const T={1:"可管理",2:"可编辑",3:"可查看"},b=L,S=N,x=m(!1),C=m(!1),w=m(!1),$=m(-1),k=se({keyword:"",kb_id:S.id,page_type:0}),a=m([]),{pager:u,getLists:F,resetPage:z,resetParams:De}=R({fetchFun:le,params:k}),j=()=>{if(!k.keyword){Q.msgWarning("请输入用户ID");return}z(),F()},M=(l,e)=>{if(l.is_added)return;const f=a.value.findIndex(g=>g.id===l.id);f!==-1?(l.permission=0,a.value.splice(f,1)):l.is_added||(l.permission=3,a.value.push(l)),e==="box"?l.isSelected=l.isSelected?0:1:l.isSelected=l.isSelected?1:0},q=l=>{u.lists.forEach(e=>{e.is_added||(e.isSelected=l?1:0,e.permission=l?3:0)}),a.value=l?u.lists.filter(e=>!e.is_added):[]},O=l=>{a.value=a.value.filter(e=>e.id!==l.id),u.lists.find(e=>e.id===l.id).isSelected=!1},W=l=>{$.value=l,w.value=!0},A=()=>{w.value=!1},p=(l,e)=>{l.permission=e,A()},{lockFn:X,isLock:Z}=Y(async()=>{const l={};a.value.forEach(e=>{l[e.sn]=e.permission}),await oe({kb_id:S.id,users:l}),b("success"),x.value=!1});return B({show:()=>{x.value=!0,z(),a.value=[],C.value=!1}}),(l,e)=>{const f=G,g=H;return r(),c("div",re,[i(o(ae),{modelValue:x.value,"onUpdate:modelValue":e[3]||(e[3]=t=>x.value=t),width:"980px",class:"!rounded-[12px]",center:!0,draggable:!0,"destroy-on-close":!0,"close-on-click-modal":!1},{header:n(()=>e[4]||(e[4]=[s("div",{class:"w-full text-left"},[s("div",{class:"text-lg font-medium"},"添加成员")],-1)])),footer:n(()=>[s("div",Ie,[i(o(E),{onClick:e[2]||(e[2]=t=>x.value=!1)},{default:n(()=>e[9]||(e[9]=[V("取消")])),_:1}),i(o(E),{type:"primary",loading:o(Z),onClick:o(X)},{default:n(()=>e[10]||(e[10]=[V(" 确认 ")])),_:1},8,["loading","onClick"])])]),default:n(()=>[s("div",{class:"flex",onClick:A},[s("div",ce,[s("div",ue,[s("div",pe,[i(o(J),{modelValue:k.keyword,"onUpdate:modelValue":e[0]||(e[0]=t=>k.keyword=t),style:{width:"100%"},size:"large",placeholder:"请输入用户ID","prefix-icon":o(K)},null,8,["modelValue","prefix-icon"]),i(o(E),{type:"primary",class:"ml-2",onClick:j},{default:n(()=>e[5]||(e[5]=[V(" 搜索 ")])),_:1})])]),i(f,{height:"500px"},{default:n(()=>[s("div",ve,[o(u).lists.length===0?(r(),c("div",xe," 请搜索成员添加 ")):P("",!0),s("div",fe,[s("div",me,[i(o(h),{modelValue:C.value,"onUpdate:modelValue":e[1]||(e[1]=t=>C.value=t),"true-value":1,"false-value":0,label:"全选",size:"large",onChange:q},null,8,["modelValue"])]),o(u).lists.length!==0?(r(!0),c(I,{key:0},U(o(u).lists,t=>(r(),c("div",{class:te(["my-4 mr-4 py-2 px-4 flex items-center cursor-pointer hover:bg-primary-light-9 rounded-[12px]",{"!cursor-not-allowed":t.is_added}]),key:t.id,onClick:v=>M(t,"box")},[i(o(h),{modelValue:t.isSelected,"onUpdate:modelValue":v=>t.isSelected=v,"true-value":1,"false-value":0,label:"",size:"large",disabled:t.is_added,onClick:_(v=>M(t,"checkbox"),["stop"])},null,8,["modelValue","onUpdate:modelValue","disabled","onClick"]),i(o(D),{src:t.avatar,size:"26",class:"flex-none ml-2"},null,8,["src"]),s("div",he,[s("span",null,y(t.nickname),1),t.is_added?(r(),c("div",ke," 已添加 ")):P("",!0)])],10,_e))),128)):P("",!0)])])]),_:1})]),i(o(ie),{direction:"vertical",style:{height:"500px"}}),i(f,{height:"500px",class:"w-1/2"},{default:n(()=>[s("div",ge,[s("div",ye," 已选择："+y(a.value.length)+" 个 ",1),(r(!0),c(I,null,U(a.value,(t,v)=>(r(),c("div",{class:"mt-4 py-2 px-4 flex items-center justify-between cursor-pointer hover:bg-primary-light-9 rounded-[12px]",key:t.id},[s("div",Ce,[i(o(D),{src:t.avatar,size:"26",class:"flex-none ml-2"},null,8,["src"]),s("div",we,y(t.nickname),1)]),s("div",Ee,[i(o(ne),{placement:"bottom-end",width:380,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:w.value&&$.value===v},{reference:n(()=>[s("div",{class:"flex items-center cursor-pointer",onClick:_(d=>W(v),["stop"])},[s("span",Me,y(T[t.permission]),1),i(g,{name:"el-icon-ArrowDown"})],8,ze)]),default:n(()=>[s("div",Ve,[s("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(t,2)},[e[6]||(e[6]=s("div",{style:{width:"320px"}},[s("div",{class:"text-base text-tx-primary"}," 可编辑 "),s("div",{class:"text-xs text-tx-placeholder mt-2"}," 只能操作数据学习，增删改查自己的数据，不能修改他人 ")],-1)),i(o(h),{"model-value":t.permission,"true-value":2,label:"",size:"large",onClick:_(d=>p(t,2),["stop"])},null,8,["model-value","true-value","onClick"])],8,Pe),s("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(t,3)},[e[7]||(e[7]=s("div",{style:{width:"320px"}},[s("div",{class:"text-base text-tx-primary"}," 可查看 "),s("div",{class:"text-xs text-tx-placeholder mt-2"}," 查看知识库所有数据 ")],-1)),i(o(h),{"model-value":t.permission,"true-value":3,label:"",size:"large",onClick:_(d=>p(t,3),["stop"])},null,8,["model-value","true-value","onClick"])],8,Se),s("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(t,1)},[e[8]||(e[8]=s("div",{style:{width:"320px"}},[s("div",{class:"text-base text-tx-primary"}," 可管理 "),s("div",{class:"text-xs text-tx-placeholder mt-2"}," 管理整个知识库数据和信息 ")],-1)),i(o(h),{"model-value":t.permission,"true-value":1,label:"",size:"large",onClick:_(d=>p(t,1),["stop"])},null,8,["model-value","true-value","onClick"])],8,$e)])]),_:2},1032,["visible"]),s("div",{class:"flex items-center ml-6",onClick:d=>O(t)},[i(g,{name:"el-icon-CloseBold"})],8,Ae)])]))),128))])]),_:1})])]),_:1},8,["modelValue"])])}}}),es=de(Ue,[["__scopeId","data-v-67d3e35c"]]);export{es as default};
