import{K as $,X as q,aL as H,Y as j,ap as k,Z as E,aM as N,M as G,ar as J,S as K,aN as Q,aO as U,aP as W,aQ as Y,a0 as Z,O as ee,aR as se,P as ae}from"./CylNgAGi.js";import{H as V,l as B,b as te,r as le,m as u,c as C,M as g,N as v,O as m,a2 as ne,u as t,X as r,_ as oe,aq as ie,V as re,a6 as de,a7 as ce,J as h}from"./Dp9aCaJ6.js";const ue=$({options:{type:q(Array),default:()=>[]},modelValue:{type:[String,Number,Boolean],default:void 0},block:Boolean,size:H,disabled:Boolean,validateEvent:{type:Boolean,default:!0},id:String,name:String,...j(["ariaLabel"])}),me={[k]:i=>V(i)||E(i),[N]:i=>V(i)||E(i)},be=["id","aria-label","aria-labelledby"],pe=["name","disabled","checked","onChange"],fe=B({name:"ElSegmented"}),ge=B({...fe,props:ue,emits:me,setup(i,{emit:_}){const n=i,a=G("segmented"),w=J(),L=K(),P=Q(),{formItem:d}=U(),{inputId:z,isLabeledByFormItem:y}=W(n,{formItemContext:d}),c=te(null),O=Y(),s=le({isInit:!1,width:0,translateX:0,disabled:!1,focusVisible:!1}),X=e=>{const l=b(e);_(k,l),_(N,l)},b=e=>h(e)?e.value:e,F=e=>h(e)?e.label:e,p=e=>!!(P.value||h(e)&&e.disabled),I=e=>n.modelValue===b(e),A=e=>n.options.find(l=>b(l)===e),D=e=>[a.e("item"),a.is("selected",I(e)),a.is("disabled",p(e))],f=()=>{if(!c.value)return;const e=c.value.querySelector(".is-selected"),l=c.value.querySelector(".is-selected input");if(!e||!l){s.width=0,s.translateX=0,s.disabled=!1,s.focusVisible=!1;return}const o=e.getBoundingClientRect();s.isInit=!0,s.width=o.width,s.translateX=e.offsetLeft,s.disabled=p(A(n.modelValue));try{s.focusVisible=l.matches(":focus-visible")}catch{}},R=u(()=>[a.b(),a.m(L.value),a.is("block",n.block)]),T=u(()=>({width:`${s.width}px`,transform:`translateX(${s.translateX}px)`,display:s.isInit?"block":"none"})),x=u(()=>[a.e("item-selected"),a.is("disabled",s.disabled),a.is("focus-visible",s.focusVisible)]),M=u(()=>n.name||w.value);return Z(c,f),C(O,f),C(()=>n.modelValue,()=>{var e;f(),n.validateEvent&&((e=d==null?void 0:d.validate)==null||e.call(d,"change").catch(l=>se()))},{flush:"post"}),(e,l)=>(g(),v("div",{id:t(z),ref_key:"segmentedRef",ref:c,class:r(t(R)),role:"radiogroup","aria-label":t(y)?void 0:e.ariaLabel||"segmented","aria-labelledby":t(y)?t(d).labelId:void 0},[m("div",{class:r(t(a).e("group"))},[m("div",{style:ne(t(T)),class:r(t(x))},null,6),(g(!0),v(oe,null,ie(e.options,(o,S)=>(g(),v("label",{key:S,class:r(D(o))},[m("input",{class:r(t(a).e("item-input")),type:"radio",name:t(M),disabled:p(o),checked:I(o),onChange:he=>X(o)},null,42,pe),m("div",{class:r(t(a).e("item-label"))},[re(e.$slots,"default",{item:o},()=>[de(ce(F(o)),1)])],2)],2))),128))],2)],10,be))}});var ve=ee(ge,[["__file","segmented.vue"]]);const Ie=ae(ve);export{Ie as E};
