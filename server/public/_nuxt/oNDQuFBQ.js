import{K as c,X as p,M as u,O as f,P as m}from"./CylNgAGi.js";import{l as a,m as v,M as s,N as o,X as i,u as r,V as y,a4 as S,a2 as _}from"./Dp9aCaJ6.js";const P=c({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:p(String),default:"solid"}}),b=a({name:"ElDivider"}),h=a({...b,props:P,setup(n){const l=n,e=u("divider"),d=v(()=>e.cssVar({"border-style":l.borderStyle}));return(t,z)=>(s(),o("div",{class:i([r(e).b(),r(e).m(t.direction)]),style:_(r(d)),role:"separator"},[t.$slots.default&&t.direction!=="vertical"?(s(),o("div",{key:0,class:i([r(e).e("text"),r(e).is(t.contentPosition)])},[y(t.$slots,"default")],2)):S("v-if",!0)],6))}});var g=f(h,[["__file","divider.vue"]]);const C=m(g);export{C as E};
