import{_ as F}from"./uE1Ww79i.js";import{cn as N,E as T,o as L,v as D,d as I}from"./B1MekKW7.js";import{_ as S}from"./DDaEm-_F.js";import"./DP2rzg_V.js";import{u as z}from"./CcPlX2kz.js";import{t as R}from"./B5TkE_dZ.js";import{l as $,m as v,b as q,M as p,a1 as O,a0 as l,O as r,a7 as P,u as s,aa as U,N as d,Z as i,a4 as g,a6 as Z,y as j}from"./Dp9aCaJ6.js";const A={class:"font-bold text-tx-primary"},G={class:"flex-1","element-loading-text":"正在翻译"},H={class:"flex p-[10px]"},J={class:"flex-1 flex items-center"},oe=$({__name:"prompt",props:{modelValue:{default:""},config:{default:()=>({})},type:{default:1},showTranslate:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(y,{emit:h}){const k=h,n=y,{modelValue:a}=N(n,k),V=v(()=>n.type===1),E={1:{label:"视频场景",placeholder:"在此描述你的视频场景，包含内容主体+动作/场景"},2:{label:"描述词",placeholder:"描述视频中需要变化的内容即可～"}},u=v(()=>E[n.type]||{}),f=q(-1),B=()=>{var e;const o=(e=n.config.data)==null?void 0:e.length;if(o){let t=Math.round(Math.random()*(o-1));f.value===t&&(t<o-1?t++:t--),t<0&&(t=0),f.value=t;const m=n.config.data[t];m&&(a.value=m)}},{lockFn:x,isLock:C}=z(async()=>{if(!a.value)return I.error("请输入描述词");const o=await R({prompt:a.value});a.value=o.result});return(o,e)=>{const t=F,m=T,b=S,w=L,M=D;return p(),O(w,{prop:"prompt",required:""},{label:l(()=>[r("span",A,P(s(u).label),1)]),default:l(()=>[U((p(),d("div",G,[i(b,{modelValue:s(a),"onUpdate:modelValue":e[2]||(e[2]=c=>j(a)?a.value=c:null),placeholder:s(u).placeholder,contentStyle:{height:"120px"}},{"length-suffix":l(()=>{var c;return[r("div",H,[r("div",J,[s(V)&&o.config.status&&((c=o.config.data)!=null&&c.length)?(p(),d("div",{key:0,class:"flex items-center cursor-pointer text-[#6F7E8E] text-sm mr-2 hover:text-primary",onClick:B},[i(t,{name:"el-icon-Refresh"}),e[3]||(e[3]=r("span",{class:"ml-[4px]"},"试试示例",-1))])):g("",!0),o.showTranslate?(p(),d("div",{key:1,class:"flex items-center cursor-pointer text-[#6F7E8E] text-sm hover:text-primary",onClick:e[0]||(e[0]=(..._)=>s(x)&&s(x)(..._))},[i(t,{name:"el-icon-Switch"}),e[4]||(e[4]=r("span",{class:"ml-[4px]"},"翻译成英文",-1))])):g("",!0)]),i(m,{link:"",size:"small",type:"primary",onClick:e[1]||(e[1]=_=>a.value="")},{icon:l(()=>[i(t,{name:"el-icon-Delete",size:12})]),default:l(()=>[e[5]||(e[5]=Z(" 清空 "))]),_:1})])]}),_:1},8,["modelValue","placeholder"])])),[[M,s(C)]])]),_:1})}}});export{oe as _};
