import{_ as u}from"./CN8DIg3d.js";import{E as d}from"./hOM-i1i4.js";import{E as x}from"./I7iWhwB5.js";import{b as y,_ as h}from"./DAgm18qP.js";/* empty css        */import k from"./DXN6XGmn.js";import v from"./BsKQqN1f.js";import{e as w}from"./BOfxv3LJ.js";import{l as E,j as R,M as m,N as p,Z as t,a0 as r,u as i,O as n}from"./Dp9aCaJ6.js";import"./D3kCNEsc.js";import"./D6pAoVka.js";import"./BkCbCnZg.js";import"./DMHEbzLi.js";import"./DlAUqK2U.js";import"./CfiDmG6E.js";import"./DCTLXrZ8.js";import"./CFxUWFIJ.js";import"./DFChVafq.js";import"./Uw_fYagh.js";import"./9Bti1uB6.js";import"./DDaEm-_F.js";import"./Vn-TIQB4.js";import"./l0sNRNKZ.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./DLAzaSQT.js";import"./CcPlX2kz.js";import"./Bu_nKEGp.js";import"./DkuCHv6r.js";import"./BZh-pK-_.js";import"./b1FdU0wQ.js";import"./Byevvg3X.js";import"./CpnG3y1q.js";import"./DlKZEFPo.js";import"./8CeC6szd.js";import"./B5giXwus.js";import"./Cv6HhfEG.js";import"./CXZvQ2S9.js";import"./CG1Y4RXB.js";import"./D5aRiEq0.js";/* empty css        *//* empty css        */import"./DjwCd26w.js";import"./CVKgQtoK.js";import"./TR-nE6Zk.js";/* empty css        */import"./DuLE2t5J.js";const g={key:0,class:"h-full p-[16px] flex"},N={class:"flex-1 min-w-0 h-full pl-[16px]"},B={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Bt=E({__name:"index",setup(j){const l=y(),e=R();return(C,o)=>{const a=u,_=d,c=x,f=h;return m(),p("div",null,[t(f,{name:"default"},{default:r(()=>[i(l).config.switch.music_status?(m(),p("div",g,[t(k,{onUpdate:o[0]||(o[0]=I=>{var s;return(s=i(e))==null?void 0:s.refresh()})}),n("div",N,[t(a,null,{default:r(()=>[t(v,{ref_key:"recordRef",ref:e},null,512)]),_:1})])])):(m(),p("div",B,[t(c,null,{icon:r(()=>[t(_,{class:"w-[150px] dark:opacity-60",src:i(w)},null,8,["src"])]),title:r(()=>o[1]||(o[1]=[n("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Bt as default};
