import{E as w}from"./D92sLaNt.js";import{_ as C}from"./C2uyROqA.js";import{E}from"./CLVDtxqA.js";/* empty css        */import{l as g,b as B,M as a,N as n,O as l,Z as r,u as s,X as c,a9 as u,a4 as V,a7 as N,a0 as z,a6 as M,a2 as P}from"./Dp9aCaJ6.js";import{u as $}from"./Bb2-23m7.js";import{_ as h}from"./DlAUqK2U.js";import"./BBFeqFQi.js";import"./Bkygmrc1.js";import"./DLMIaim0.js";const D={class:"relative flex-none flex"},S={key:0,class:"absolute inset-0 flex items-center justify-center bg-[rgba(0,0,0,0.5)] rounded-full"},j={class:"flex-1 line-clamp-2 min-w-0 ml-[10px]"},A=g({__name:"dub-item",props:{activeId:{},itemId:{},name:{},pic:{},url:{},disabled:{type:Boolean,default:!1}},emits:["click"],setup(f,{emit:v}){const b=f,y=v,i=B(!1),{play:k,audioPlaying:d,pause:m}=$(),x=()=>{b.disabled||y("click")};return(t,e)=>{const I=w,p=C,_=E;return a(),n("div",{class:c(["dub-item flex items-center p-main cursor-pointer",{"is-hover":s(i),"is-active":t.activeId==t.itemId,"is-disable":t.disabled}]),onMouseenter:e[2]||(e[2]=o=>i.value=!0),onMouseleave:e[3]||(e[3]=o=>i.value=!1),onClick:x},[l("div",D,[r(I,{src:t.pic,class:"w-[40px] h-[40px] rounded-full overflow-hidden"},null,8,["src"]),s(i)||s(d)?(a(),n("div",S,[l("div",{class:c(["audio-btn cursor-pointer",{"audio-btn--animation":s(d)}])},[s(d)?(a(),n("span",{key:0,class:"text-white flex",onClick:e[0]||(e[0]=u((...o)=>s(m)&&s(m)(...o),["stop"]))},[r(p,{size:24,name:"el-icon-VideoPause"})])):(a(),n("span",{key:1,class:"text-white flex",onClick:e[1]||(e[1]=u(o=>s(k)(t.url),["stop"]))},[r(p,{size:24,name:"el-icon-VideoPlay"})]))],2)])):V("",!0)]),l("div",j,N(t.name),1),l("div",{class:"flex-none",style:P({visibility:s(i)?"visible":"hidden"})},[r(_,{type:"primary",disabled:t.disabled},{default:z(()=>e[4]||(e[4]=[M("使用")])),_:1},8,["disabled"])],4)],34)}}}),L=h(A,[["__scopeId","data-v-56d9fccd"]]);export{L as default};
