import{b as E,l as R,e as A,o as F,p as I}from"./DAgm18qP.js";import{_ as D}from"./DMHEbzLi.js";import{E as z}from"./CFxUWFIJ.js";import{_ as B}from"./CE6guEdZ.js";import{_ as C}from"./D2kMgjCU.js";import{P as K}from"./CVKgQtoK.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import{k as M,a as Q,b as j}from"./BNnETjxs.js";import{l as N,j as g,b as O,r as b,M as $,a1 as S,a0 as a,Z as s,u as t,O as n}from"./Dp9aCaJ6.js";const Z={class:"flex items-center cursor-pointer text-[#666]"},G={class:"flex items-center cursor-pointer text-[#666]"},de=N({__name:"addPop",emits:["close","success"],setup(H,{expose:w,emit:v}){E();const x=v,u=g(),p=g(),d=O(-1),o=b({name:"",image:"",intro:"",documents_model_id:"",documents_model_sub_id:"",embedding_model_id:""}),y=b({name:[{required:!0,message:"请输入库的名称",trigger:"change"}],image:[{required:!0,message:"请选择封面图标",trigger:"change"}],type:[{required:!0,message:"Please select Activity zone",trigger:"change"}],embedding_model_id:[{required:!0,message:"请选择向量模型"}],documents_model_id:[{required:!0,message:"请选择文件处理通道"}],documents_model_sub_id:[{required:!0,message:"请选择文件处理模型"}],sort:[{required:!0,message:"请输入排序",trigger:"change"}],is_enable:[{required:!0,message:"请选择库的状态",trigger:"change"}]}),k=R(),V=async()=>{var r;if(await((r=p.value)==null?void 0:r.validate()),d.value!=-1)await M({id:d.value,...o});else{const{id:e}=await Q({...o});k.push({path:"/application/kb/detail",query:{id:e}})}x("success"),u.value.close()},q=async r=>{const e=await j({id:r});Object.keys(e).map(i=>{o[i]=e[i]})};return w({open:async r=>{var e;(e=p.value)==null||e.resetFields(),d.value=-1,u.value.open(),r!=null&&r.id&&(d.value=r.id,await q(d.value))}}),(r,e)=>{const i=A,m=F,_=D,c=z,f=B,P=C,U=I,h=K;return $(),S(h,{ref_key:"popRef",ref:u,title:`${t(d)!=-1?"编辑":"新增"}知识库`,width:"500px",async:"",onConfirm:V},{default:a(()=>[s(U,{ref_key:"formRef",ref:p,"label-width":"130px",model:t(o),rules:t(y)},{default:a(()=>[s(m,{label:"知识库名称",prop:"name"},{default:a(()=>[s(i,{modelValue:t(o).name,"onUpdate:modelValue":e[0]||(e[0]=l=>t(o).name=l),placeholder:"请输入知识库名称",class:"w-[240px]"},null,8,["modelValue"])]),_:1}),s(m,{label:"知识库简介"},{default:a(()=>[s(i,{type:"textarea",modelValue:t(o).intro,"onUpdate:modelValue":e[1]||(e[1]=l=>t(o).intro=l),placeholder:"请用一句话描述知识库",class:"w-[240px]",rows:3},null,8,["modelValue"])]),_:1}),s(m,{label:"向量模型",prop:"embedding_model_id"},{label:a(()=>[s(c,{placement:"right",width:300,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"向量模型可以将自然语言转成向量(即数据训练), 用于进行语义检索, 注意: 不同向量模型无法一起使用, 选择完后将无法修改。"},{reference:a(()=>[n("div",Z,[e[6]||(e[6]=n("span",{class:"mr-1"},"向量模型",-1)),s(_,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),default:a(()=>[s(f,{class:"flex-1",id:t(o).embedding_model_id,"onUpdate:id":e[2]||(e[2]=l=>t(o).embedding_model_id=l),"set-default":!1,type:"vectorModels",disabled:t(d)!=-1},null,8,["id","disabled"])]),_:1}),s(m,{label:"文件处理模型",prop:"documents_model_sub_id"},{label:a(()=>[s(c,{placement:"right",width:300,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"文件模型用于QA拆分功能(导入数据->自动拆分问答对), 利用该AI模型对导入的文本进行处理，最终拆分成一问一答的数据形式。"},{reference:a(()=>[n("div",G,[e[7]||(e[7]=n("span",{class:"mr-1"},"文件处理模型",-1)),s(_,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),default:a(()=>[s(f,{class:"flex-1",id:t(o).documents_model_id,"onUpdate:id":e[3]||(e[3]=l=>t(o).documents_model_id=l),sub_id:t(o).documents_model_sub_id,"onUpdate:sub_id":e[4]||(e[4]=l=>t(o).documents_model_sub_id=l),"set-default":!1,disabled:""},null,8,["id","sub_id"])]),_:1}),s(m,{label:"封面",prop:"image"},{default:a(()=>[n("div",null,[s(P,{modelValue:t(o).image,"onUpdate:modelValue":e[5]||(e[5]=l=>t(o).image=l)},null,8,["modelValue"]),e[8]||(e[8]=n("div",{class:"form-tips"},"建议尺寸：200*160px",-1))])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title"])}}});export{de as _};
