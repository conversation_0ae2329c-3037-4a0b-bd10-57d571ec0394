import{P as i}from"./CjlEN6_J.js";import{l as _,j as u,b as n,M as f,a1 as m,a0 as d,O as r,a7 as h,u as l}from"./Dp9aCaJ6.js";const v={class:"whitespace-pre-wrap"},B=_({__name:"reply-popup",setup(w,{expose:c}){const o=u(),p=n(""),t=n("");return c({open:(a,s)=>{var e;p.value=s,(e=o.value)==null||e.open(),t.value=a}}),(a,s)=>{const e=i;return f(),m(e,{ref_key:"popRef",ref:o,width:"700px",title:l(p)},{default:d(()=>[r("div",null,[r("div",v,h(l(t)),1)])]),_:1},8,["title"])}}});export{B as _};
