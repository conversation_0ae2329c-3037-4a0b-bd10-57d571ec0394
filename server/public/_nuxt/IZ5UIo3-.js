import{bh as O,k as P,bj as p,bk as A,bl as b,b1 as y,bm as M,bn as _,bo as g,a1 as m}from"./BQ-RMI0l.js";import{b as c}from"./DpOQaFAg.js";var C=1,I=2;function L(n,r,e,t){var i=e.length,u=i;if(n==null)return!u;for(n=Object(n);i--;){var f=e[i];if(f[2]?f[1]!==n[f[0]]:!(f[0]in n))return!1}for(;++i<u;){f=e[i];var s=f[0],o=n[s],h=f[1];if(f[2]){if(o===void 0&&!(s in n))return!1}else{var E=new O,a;if(!(a===void 0?c(h,o,C|I,t,E):a))return!1}}return!0}function l(n){return n===n&&!P(n)}function d(n){for(var r=p(n),e=r.length;e--;){var t=r[e],i=n[t];r[e]=[t,i,l(i)]}return r}function R(n,r){return function(e){return e==null?!1:e[n]===r&&(r!==void 0||n in Object(e))}}function D(n){var r=d(n);return r.length==1&&r[0][2]?R(r[0][0],r[0][1]):function(e){return e===n||L(e,n,r)}}var G=1,w=2;function F(n,r){return A(n)&&l(r)?R(b(n),r):function(e){var t=y(e,n);return t===void 0&&t===r?M(e,n):c(r,t,G|w)}}function S(n){return function(r){return r==null?void 0:r[n]}}function x(n){return function(r){return _(r,n)}}function K(n){return A(n)?S(b(n)):x(n)}function U(n){return typeof n=="function"?n:n==null?g:typeof n=="object"?m(n)?F(n[0],n[1]):D(n):K(n)}export{U as b};
