import{E as g}from"./CSeNE23V.js";import{E as f}from"./Pei5_z3G.js";import{j as d,b as y,cW as s}from"./CzTOiozM.js";/* empty css        *//* empty css        */import E from"./C_0NHSGf.js";import{_ as I}from"./C_jV95_r.js";import h from"./CCBsX3Z-.js";import{_ as x}from"./CkAH9eA7.js";import{_ as T}from"./DUysaqVv.js";import{l as w,m as L,c as k,M as t,a1 as p,a0 as B,O as m,u as o,N as O,Z as P,a4 as i,y as N}from"./Dp9aCaJ6.js";const D={class:"flex"},b={key:0},v={class:"flex-1 text-tx-primary flex flex-col w-[400px]"},j=w({__name:"index",setup(M){const e=d(),n=y(),l=L({get(){return e.showLogin},set(a){e.showLogin=a}});return k(()=>e.showLogin,a=>{a||(e.temToken=null)}),(a,r)=>{const c=g,u=f;return t(),p(u,{modelValue:o(l),"onUpdate:modelValue":r[0]||(r[0]=_=>N(l)?l.value=_:null),width:"auto",class:"login-popup","append-to-body":"","show-close":o(e).loginPopupType!==o(s).BIND_MOBILE,"close-on-click-modal":!1,style:{"border-radius":"16px",overflow:"hidden",padding:"0"}},{default:B(()=>[m("div",D,[o(n).getWebsiteConfig.pc_login_image&&o(e).loginPopupType==o(s).LOGIN&&!o(n).isMobile?(t(),O("div",b,[P(c,{class:"w-[320px] h-full",fit:"cover",src:o(n).getWebsiteConfig.pc_login_image},null,8,["src"])])):i("",!0),m("div",v,[o(e).loginPopupType==o(s).LOGIN?(t(),p(E,{key:0})):i("",!0),o(e).loginPopupType==o(s).FORGOT_PWD_MAILBOX||o(e).loginPopupType==o(s).FORGOT_PWD_MOBILE?(t(),p(I,{key:1})):i("",!0),o(e).loginPopupType==o(s).REGISTER?(t(),p(h,{key:2})):i("",!0),o(e).loginPopupType==o(s).BIND_MOBILE?(t(),p(x,{key:3})):i("",!0),o(e).loginPopupType==o(s).BIND_WEIXIN?(t(),p(T,{key:4})):i("",!0)])])]),_:1},8,["modelValue","show-close"])}}});export{j as _};
