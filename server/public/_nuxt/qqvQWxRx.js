import{_ as Me}from"./C2uyROqA.js";import{c as Pe,a as Ne,_ as $e}from"./CUCjj3Gy.js";import{E as Fe,a as Oe}from"./uz2DoZ_U.js";import{l as Ue,j as We,a as Ge,b as Je,bx as Xe,by as Ze,cw as Ke,f as D,E as Qe}from"./CLVDtxqA.js";import{E as Ye}from"./CFmElfgq.js";import{l as et,j as C,m as $,b as f,ak as tt,r as _e,k as at,F as ot,n as ge,M as u,N as b,aa as ye,ab as xe,u as t,O as s,a2 as we,Z as g,a7 as be,X as st,a4 as I,a1 as S,a0 as T,_ as ke,aq as rt,a6 as Re}from"./Dp9aCaJ6.js";import{u as it}from"./CPeHeFy-.js";import{u as nt,a as lt}from"./BVaqSNJ5.js";import{u as ct}from"./Bb2-23m7.js";import{_ as ut}from"./BaTX5GrR.js";import{g as dt,b as pt,r as ft,a as vt,v as mt,d as ht}from"./Bo3PTL3c.js";import{c as _t}from"./DULTnOEc.js";import{E as gt}from"./JBMcerbz.js";import{_ as yt}from"./DlAUqK2U.js";import"./C76tEu0I.js";/* empty css        */import"./DcNltpix.js";import"./D92sLaNt.js";import"./BBFeqFQi.js";import"./Bkygmrc1.js";import"./DLMIaim0.js";import"./CDqF2odB.js";/* empty css        */import"./BtQB-JFB.js";import"./DQo9WDrm.js";import"./Cpg3PDWZ.js";import"./B1txbFRp.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./DILeaQhY.js";import"./VJhqcMCs.js";/* empty css        */import"./DwFObZc_.js";import"./B_1915px.js";import"./B-7_EDyl.js";import"./Bg2e09vA.js";import"./BCsdKxyx.js";import"./DY73EaVE.js";import"./D5Ye8pBY.js";import"./CRS4Ri59.js";import"./1ThCdcE_.js";import"./DI1qIu00.js";const xt={class:"h-screen w-screen flex justify-center items-center"},wt=["width","height"],bt={class:"p-[20px] flex h-full relative z-10"},kt={class:"flex-1 h-full flex flex-col"},Rt={class:"flex-1 min-h-0"},Et={class:"flex items-center cursor-pointer"},Ct={class:"text-xl flex-1 min-w-0 ml-[10px] text-white"},St={class:"h-full flex"},Tt={class:"h-full flex flex-col items-center w-[160px] justify-end"},Lt=["width","height","id"],zt={class:"text-xs text-white bg-[rgba(51,51,51,0.3)] py-[5px] px-[10px] rounded my-[10px]"},It={class:"w-[400px] h-full flex flex-col mr-[20px] pt-[100px]"},jt={class:"flex-1 min-h-0 bg-[rgba(0,0,0,0.5)] rounded-[20px] overflow-hidden flex flex-col"},qt={class:"flex-1 min-h-0"},Vt={class:"py-4 px-[20px]"},At={key:1,class:"h-full flex justify-center text-tx-secondary items-center"},Bt={key:0,class:"flex justify-center items-center py-[10px]"},Dt={class:"flex flex-col justify-center items-center"},Ht={key:1,class:"h-screen w-screen flex flex-col items-center justify-center"},Mt=et({__name:"chat",async setup(Pt){let oe,se;C();const F=Ue(),k=We(),O=Ge(),j=$(()=>O.query.id),H=$(()=>O.query.squareId),U=$(()=>O.query.cateId),W=Je(),r=f({}),Ee=async()=>{r.value=await dt({id:j.value})},m=f([]);let G=0;const J=async()=>{const a=await vt({square_id:H.value,category_id:U.value,robot_id:j.value,page_size:25e3});if(m.value=a.lists||[],v.value==3){const e=m.value[m.value.length-1];e&&e.id!==G&&(G=e.id,Ve(G))}};[oe,se]=tt(()=>it(()=>J(),{lazy:!0},"$Mtlbt2SHcf")),await oe,se();const re=()=>{H.value?F.replace({path:"/robot_square"}):F.replace({path:"/application/layout/robot"})},v=f(0),Ce=_e({0:"正在初始化对话...",1:"点击开始说话",2:"我在听，您请说...",3:"稍等，让我想一想",4:"正在回复中..."}),X=C();$(()=>v.value==4?r.value.digital.wide_talk_video:r.value.digital.wide_stay_video);const Se=async()=>{if(!k.isLogin)return k.toggleShowLogin();y.value||(await D.confirm("确定清空记录？"),await pt({square_id:H.value,category_id:U.value,robot_id:j.value}),J())},q=C(),Te=()=>{var a;if(!k.isLogin)return(a=q.value)==null||a.blur(),k.toggleShowLogin();V()};let i=null;const y=f(!1),ie=async(a,e="input")=>{var d;if(!k.isLogin)return k.toggleShowLogin();if(!a)return D.msgError("请输入问题");if(y.value||!j.value)return;A(),h(3);const n=Date.now();y.value=!0,m.value.push({type:1,content:a}),m.value.push({type:2,typing:!0,content:"",reasoning:"",key:n}),(d=q.value)==null||d.setInputValue();const o=m.value.find(l=>l.key===n);i=ft({square_id:H.value,cate_id:U.value,robot_id:j.value,question:a,stream:!0}),i.addEventListener("reasoning",({data:l})=>{const{data:c,index:_}=l;o.reasoning||(o.reasoning=""),o.reasoning+=c}),i.addEventListener("chat",({data:l})=>{const{data:c,index:_}=l;o.content||(o.content=""),o.content+=c}),i.addEventListener("file",({data:l})=>{try{const c=JSON.parse(l.data);o.files=c}catch(c){console.error(c)}}),i.addEventListener("image",({data:l})=>{try{const c=JSON.parse(l.data);o.images=c}catch(c){console.error(c)}}),i.addEventListener("close",async()=>{setTimeout(async()=>{await J(),o.typing=!1,y.value=!1,V()},500)}),i.addEventListener("error",async l=>{var c,_,E;if(h(1),((c=l.data)==null?void 0:c.code)===1100){try{W.getIsShowRecharge?(await D.confirm(`${W.getTokenUnit}数量已用完，请前往充值`),F.push("/user/recharge")):D.msgError(`${W.getTokenUnit}数量已用完。请联系客服增加`)}finally{e==="input"&&((_=q.value)==null||_.setInputValue(a))}return}l.errorType==="connectError"&&D.msgError("请求失败，请重试"),["connectError","responseError"].includes(l.errorType)&&(m.value.splice(m.value.length-2,2),e==="input"&&((E=q.value)==null||E.setInputValue(a))),o.typing=!1,setTimeout(()=>{y.value=!1},200)})},Z=C(),ne=f(),V=async()=>{var e,n,o;const a=(n=(e=Z.value)==null?void 0:e.wrapRef)==null?void 0:n.scrollHeight;(o=Z.value)==null||o.setScrollTop(a)},{height:Le}=Xe(ne);Ze(Le,()=>{y.value&&V()},{throttle:500,immediate:!0});const le=f(),L=f(!0),h=a=>{v.value=a},K=f(!1),Q=f(0),R=f(!1),ce=f(0),x=_e({id:"audio-canvas",width:80,height:40,minHeight:5,scale:2}),{render:ze,stopRender:Ie,draw:Nt}=nt(x),{start:je,stop:A,isRecording:B,authorize:qe,close:$t,isOpen:Ft}=lt({onstart(){h(2),clearTimeout(le.value),R.value=!1,Q.value=Date.now()},async onstop(a){if(Ie(),R.value=!1,!K.value){h(1);return}K.value=!1,h(3);try{const e=await mt({file:a.blob});if(!e.text){L.value&&z();return}ie(e.text,"voice")}catch{L.value&&z()}},ondata(a){var n;const e=Date.now();R.value&&ze(a),a.powerLevel>=10&&(clearTimeout(ce.value),v.value=2,R.value=!0,Q.value=e,ce.value=setTimeout(()=>{K.value=!0,clearTimeout(le.value),Y(),A()},2e3)),e-Q.value>=((n=r.value.digital)==null?void 0:n.idle_time)*1e3&&(R.value||(Be(),A()))}}),{play:ue,pause:Y,audioPlaying:de}=ct({onstart(){v.value=4,ee.value&&(ee.value=!1)},onstop(){h(2),L.value?z():h(1)},onerror(){h(1)}}),Ve=async a=>{ue(async()=>await pe({type:2,record_id:a}),!1)},z=async()=>{B.value||je()},Ae=async()=>{if(v.value==4){Y(),z();return}v.value!=3&&(B.value?(L.value=!1,A(),h(1)):(L.value=!0,z()))},pe=async a=>{try{const{url:e}=await ht(a);return e}catch{return h(1),Promise.reject()}},M=f(""),ee=f(!1),Be=async()=>{if(!r.value.is_digital||!r.value.digital_id||r.value.digital.is_disable||(M.value||(M.value=await pe({type:3,record_id:r.value.id})),!M.value))return Promise.reject();ee.value=!0;const a=Date.now();m.value.push({type:2,typing:!1,content:r.value.digital.idle_reply,key:a}),await ge(),V(),ue(M.value,!1)},De=()=>{i==null||i.removeEventListener("close"),i==null||i.removeEventListener("chat"),i==null||i.removeEventListener("error"),i==null||i.abort(),y.value=!1};at(()=>{De(),Y(),A()});const He=C(),{width:fe,height:ve}=Ke(),te=C(),ae=C(),me=async a=>new Promise((e,n)=>{const o=document.createElement("video");o.src=a,o.preload="auto",o.loop=!0,o.muted=!0,o.addEventListener("loadedmetadata",d=>{o.width=o.videoWidth,o.height=o.videoHeight,e(o)}),o.addEventListener("error",d=>{n(d)}),o.addEventListener("play",d=>{he()})}),he=()=>{if(!X.value)return;const a=fe.value*2,e=ve.value*2,n=X.value.getContext("2d");if(!n)return;const o=v.value===4?ae.value:te.value;if(!o)return;n.clearRect(0,0,a,e);const{videoHeight:d,videoWidth:l}=o;let c=0,_=0,E=l,P=d;if(l/d>=a/e){const w=a*d/e;c=(l-w)/2,E=w}else{const w=e*l/a;_=(d-w)/2,P=w}n.drawImage(o,c,_,E,P,0,0,a,e),requestAnimationFrame(he)};return ot(async()=>{if(await Ee(),!(!r.value.digital_id||r.value.digital.is_disable)){L.value=!0,te.value=await me(r.value.digital.wide_stay_video),ae.value=await me(r.value.digital.wide_talk_video),te.value.play(),ae.value.play();try{await qe(),z()}catch{h(1)}await ge(),V()}}),(a,e)=>{var w;const n=Me,o=Ne,d=$e,l=Fe,c=Oe,_=Qe,E=Pe,P=Ye;return u(),b(ke,null,[ye(s("div",xt,e[1]||(e[1]=[s("img",{class:"w-[400px]",src:ut,alt:""},null,-1)]),512),[[xe,t(v)===0]]),ye(s("div",null,[t(r).digital_id&&!((w=t(r).digital)!=null&&w.is_disable)?(u(),b("div",{key:0,ref_key:"containRef",ref:He,class:"h-screen w-screen relative overflow-hidden",style:we({background:t(r).digital_bg})},[s("canvas",{ref_key:"canvasRef",ref:X,id:"digital-canvas",width:t(fe)*2,height:t(ve)*2},null,8,wt),s("div",bt,[s("div",kt,[s("div",Rt,[s("div",Et,[s("div",{class:"flex bg-white p-[5px] text-bold rounded-[50%] text-primary shadow-light",onClick:re},[g(n,{name:"el-icon-Back",size:18})]),s("div",Ct,be(t(r).name),1)])]),e[2]||(e[2]=s("div",{class:"flex justify-center"},null,-1))]),s("div",St,[s("div",Tt,[s("div",{class:st(["recorder gradient-button",{"recorder--stop":!t(B)&&!t(de)}]),onClick:Ae},[t(R)?(u(),b("canvas",{key:0,style:we({width:`${t(x).width}px`,height:`${t(x).height}px`}),width:t(x).width*t(x).scale,height:t(x).height*t(x).scale,id:t(x).id},null,12,Lt)):I("",!0),t(B)&&!t(R)?(u(),S(n,{key:1,name:"el-icon-Microphone",size:40})):t(de)?(u(),S(n,{key:2,name:"local-icon-pause",size:40})):t(B)?I("",!0):(u(),S(n,{key:3,name:"el-icon-Mute",size:40}))],2),s("div",zt,[s("div",null,be(t(Ce)[t(v)]),1)])]),s("div",It,[s("div",jt,[s("div",qt,[t(m).length?(u(),S(t(gt),{key:0,ref_key:"scrollbarRef",ref:Z},{default:T(()=>[s("div",Vt,[s("div",{ref_key:"innerRef",ref:ne},[(u(!0),b(ke,null,rt(t(m),(p,N)=>(u(),b("div",{key:p.id+""+N,class:"mt-4 sm:pb-[20px]"},[p.type==1?(u(),S(d,{key:0,type:"right",avatar:t(k).userInfo.avatar,color:"white"},{default:T(()=>[g(o,{content:String(p.content)},null,8,["content"])]),_:2},1032,["avatar"])):I("",!0),p.type==2?(u(),S(d,{key:1,type:"left",time:p.create_time,avatar:t(r).icons?t(r).icons:t(r).image,bg:"#fff"},{default:T(()=>[p.reasoning?(u(),S(c,{key:0,"model-value":"the-chat-msg-collapse",class:"mb-2 the-chat-msg-collapse"},{default:T(()=>[g(l,{title:"深度思考",name:"the-chat-msg-collapse"},{default:T(()=>[g(o,{content:p.reasoning,class:"text-tx-secondary px-3 border-l-[3px] border-br-light"},null,8,["content"])]),_:2},1024)]),_:2},1024)):I("",!0),g(o,{content:String(p.content),type:"html",typing:p.typing,images:p.images,files:p.files,"record-id":p.id,"record-type":2},null,8,["content","typing","images","files","record-id"])]),_:2},1032,["time","avatar"])):I("",!0)]))),128))],512)])]),_:1},512)):(u(),b("div",At," 暂无聊天记录 "))]),t(y)?(u(),b("div",Bt,[g(_,{color:"#fff",round:"",onClick:e[0]||(e[0]=p=>{var N;return(N=t(i))==null?void 0:N.abort()})},{default:T(()=>e[3]||(e[3]=[Re(" 停止 ")])),_:1})])):I("",!0)]),s("div",null,[g(E,{ref_key:"chatActionRef",ref:q,loading:[3,4].includes(t(v)),menus:t(r).menus,"show-pause":!1,"show-clear":!1,onEnter:ie,onFocus:Te},null,8,["loading","menus"])])]),s("div",Dt,[s("div",{class:"gradient-button",onClick:Se},[g(n,{name:"local-icon-clear",size:24})])])])])],4)):(u(),b("div",Ht,[g(P,{description:"该智能体暂未配置形象或形象已被禁用",image:t(_t)},null,8,["image"]),g(_,{type:"primary",round:"",onClick:re},{default:T(()=>e[4]||(e[4]=[Re(" 返回智能体 ")])),_:1})]))],512),[[xe,t(v)!==0]])],64)}}}),qa=yt(Mt,[["__scopeId","data-v-352cd70c"]]);export{qa as default};
