import{a as j,E as k}from"./Db173UeR.js";import{cn as w}from"./B5S_Er7H.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{a as t,f as n}from"./Dlvoorzj.js";import{_ as x}from"./DhEhpSjL.js";import{l as y,c as B,u as l,M as p,N as c,Z as d,O as E,a0 as N,_ as b,aq as g,y as h,a4 as C,a1 as M}from"./Dp9aCaJ6.js";import{_ as O}from"./DlAUqK2U.js";import"./DbRYflOL.js";import"./DCTLXrZ8.js";import"./DySxZBuW.js";import"./Di6b97gu.js";import"./Cv6HhfEG.js";import"./BzS1o01N.js";import"./GM0TYtnE.js";import"./BmXQ27ak.js";import"./IItDHZjE.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";import"./C8ZjkaO0.js";import"./Dv--ZGyq.js";import"./BgJCO7ll.js";import"./9Bti1uB6.js";/* empty css        */const q={key:0,class:"mj-version"},z=y({__name:"mj-version",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(_,{emit:f}){const v=f,V=_,{modelValue:e}=w(V,v);return B(()=>{var o;return[n.value.draw_model,(o=t.value)==null?void 0:o.mj_version]},([o,s])=>{var r,a;(r=t.value)!=null&&r.mj_version&&e.value!==o&&(e.value=(a=t.value)==null?void 0:a.mj_version[n.value.draw_model][0])}),(o,s)=>{var u;const r=k,a=j;return(u=l(t))!=null&&u.mj_version?(p(),c("div",q,[d(x,{title:"版本选择",tips:"指定midjourney的渲染版本"}),E("div",null,[d(a,{modelValue:l(e),"onUpdate:modelValue":s[0]||(s[0]=m=>h(e)?e.value=m:null),placeholder:"请选择版本",class:"w-full mt-[8px]",size:"large"},{default:N(()=>{var m;return[(p(!0),c(b,null,g((m=l(t))==null?void 0:m.mj_version[l(n).draw_model],(i,D)=>(p(),M(r,{key:i,label:i,value:i},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])])])):C("",!0)}}}),ie=O(z,[["__scopeId","data-v-4b0030cc"]]);export{ie as default};
