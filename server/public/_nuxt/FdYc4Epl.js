import{_ as t}from"./CPWhsYSd.js";import{l as r,M as m,N as p,V as i,Z as e}from"./Dp9aCaJ6.js";import"./fZOypmWP.js";import"./CylNgAGi.js";import"./BGZ_z1Ox.js";import"./qb8OCGLI.js";import"./Cg11N2Sp.js";import"./Za7Ic34B.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        *//* empty css        */import"./B3fbx9iT.js";import"./DmsLvQBk.js";import"./Cv6HhfEG.js";import"./BRjkVUgK.js";import"./4QYjDMhx.js";import"./L6C4zwM6.js";import"./Db_grgJe.js";import"./DJ1U04Bw.js";import"./dYUcznge.js";import"./CBuTaJhf.js";import"./BjDNN4zb.js";import"./D8pK6L_e.js";import"./Dke71UIn.js";import"./EAABSjZ0.js";import"./DlAUqK2U.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./CcPlX2kz.js";import"./IhwYmnnx.js";import"./GUFYbOms.js";import"./a_MrTnou.js";import"./BE7GAo-z.js";import"./CsIYA7Q7.js";import"./BJMSn3uR.js";import"./DV6omn8C.js";import"./C1fKbcEo.js";const a={class:"layout-blank"},X=r({__name:"blank",setup(s){return(o,n)=>(m(),p("section",a,[i(o.$slots,"default"),e(t)]))}});export{X as default};
