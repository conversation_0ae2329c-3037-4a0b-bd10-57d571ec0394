import{E as b,a as w,b as k,d as E,c as C}from"./WYMZj5L7.js";import{b as M,j as B,cV as V,bw as z,cw as A,dB as c}from"./CzTOiozM.js";import D from"./Cj2SPdL2.js";import F from"./BySdohJ6.js";import{_ as H}from"./TKQBD5D-.js";import{_ as L}from"./Plo0F-_A.js";import N from"./BQMBWF9_.js";import{_ as I}from"./WOdEUh6v.js";import{l as P,m as R,F as T,ar as j,M as m,a1 as a,a0 as e,Z as t,V as p,a4 as s,as as U,X as W,u as n,a2 as X}from"./Dp9aCaJ6.js";import{_ as Z}from"./DlAUqK2U.js";import"./aGe0gxwC.js";import"./9wPy3dd6.js";import"./DN35sEUo.js";import"./CN8DIg3d.js";import"./C_7xENts.js";import"./DQS3pGu2.js";/* empty css        */import"./Cu2rUTDp.js";import"./rynrWmSY.js";import"./CLJ-nna1.js";import"./CaD5i18d.js";import"./DCTLXrZ8.js";import"./DflfJMcq.js";import"./CH0sQbfA.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./7WLOh-Vd.js";import"./YCXpYPC4.js";import"./CxUYHGl0.js";import"./Bn0JIVQp.js";import"./CSeNE23V.js";import"./n7JpCWQv.js";import"./v-ANcAMI.js";import"./Cfoh3Adv.js";/* empty css        */import"./B0QXcJuZ.js";import"./BE7GAo-z.js";import"./BKtyF0vH.js";import"./BoqFxUqQ.js";import"./Pei5_z3G.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";/* empty css        */import"./Bn8r6RG7.js";import"./DlIaLjGw.js";import"./CwWopofY.js";import"./DhT69RLv.js";import"./CQleycmY.js";/* empty css        */import"./l0sNRNKZ.js";import"./S5pYavP5.js";import"./Cftw5q4O.js";import"./6MP-Yojp.js";import"./CJ4MpRUP.js";import"./C_0NHSGf.js";import"./CJBWowqe.js";import"./Cv6HhfEG.js";import"./CPMpBhdt.js";import"./Cyd9DnnY.js";import"./CLdudPaH.js";import"./4kyntJ1V.js";import"./CmG-gwTN.js";import"./D13MzIoa.js";import"./f5OkUKn2.js";/* empty css        *//* empty css        *//* empty css        */import"./CSIVEPx4.js";import"./6Txs1_kD.js";import"./C_jV95_r.js";import"./CCBsX3Z-.js";import"./CkAH9eA7.js";import"./DUysaqVv.js";import"./DGB8FSGX.js";import"./CdNB8B4x.js";import"./DTD7sUKy.js";const q=P({__name:"default",setup(G){const f=M(),h=B(),d=V(),g=z();R(()=>f.isMobile?{"--header-height":"50px","--main-padding":"12px"}:{"--main-padding":"15px"});const{height:l}=A(),u=()=>{g.value=d.isDark,d.setTheme()};return T(()=>{u()}),j(()=>{u()}),(o,J)=>{const y=b,v=w,S=k,$=E,i=C;return m(),a(i,{class:"bg-body h-full layout-default",style:X([{height:`${n(l)=="Infinity"?"100vh":n(l)+"px"}`}])},{default:e(()=>[t(y,{height:"var(--header-height)",style:{padding:"0"}},{default:e(()=>[t(D,null,{default:e(()=>{var r;return[(r=o.$slots)!=null&&r.header?p(o.$slots,"header",{key:0},void 0,!0):s("",!0)]}),_:3})]),_:3}),t(i,{class:"min-h-0"},{default:e(()=>{var r;return[t(v,{width:"auto",class:"!overflow-visible"},{default:e(()=>{var _;return[t(F,null,U({_:2},[(_=o.$slots)!=null&&_.aside?{name:"aside",fn:e(()=>[p(o.$slots,"aside",{},void 0,!0)]),key:"0"}:void 0]),1024)]}),_:3}),t(i,{class:W(["overflow-hidden layout-bg rounded-[12px]",{"":(r=o.$slots)==null?void 0:r.aside,"!rounded-none ":(o._.provides[c]||o.$route).meta.hiddenRounded}])},{default:e(()=>[t(S,{class:"scrollbar",style:{padding:"0"}},{default:e(()=>[p(o.$slots,"default",{},void 0,!0)]),_:3}),(o._.provides[c]||o.$route).meta.hiddenFooter?s("",!0):(m(),a($,{key:0,height:"auto"},{default:e(()=>[t(H)]),_:1}))]),_:3},8,["class"])]}),_:3}),n(h).showLogin?(m(),a(L,{key:0})):s("",!0),t(N),t(I)]),_:3},8,["style"])}}}),St=Z(q,[["__scopeId","data-v-1a51c74d"]]);export{St as default};
