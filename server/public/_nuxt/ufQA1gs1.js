import{a as V,b as B}from"./DSvHt5sr.js";import{_ as L}from"./Cz8pxCeT.js";import{E as N}from"./CEYTy2ef.js";import{E as D}from"./Bwa6YuoW.js";import{cK as S,cL as $}from"./BBthjZaB.js";/* empty css        *//* empty css        */import{u as f}from"./BzHpYkC6.js";import{u as A}from"./D4MHNy-o.js";import I from"./TKbFOiod.js";import{e as R}from"./DXfM9A1O.js";import{l as q,r as z,ak as g,M as a,N as n,O as i,Z as m,a0 as l,u as o,_ as y,aq as x,a1 as d,a6 as O,a7 as F,A as G}from"./Dp9aCaJ6.js";const H={class:"h-full flex flex-col"},J={class:"pt-[15px] px-main"},K={class:"flex-1 min-h-0"},T={class:"p-main"},U={key:0},le=q({__name:"music",async setup(X){let t,s;const u=A(),c=z({keyword:"",category_id:0}),{data:v}=([t,s]=g(()=>f(()=>S(),{lazy:!0},"$62OqCgsHxi")),t=await t,s(),t),{data:_,refresh:h,pending:Z}=([t,s]=g(()=>f(()=>$(c),{lazy:!0},"$XfxmNfcMJb")),t=await t,s(),t),k=p=>{u.music=G(p)};return(p,r)=>{const E=B,b=V,C=L,w=N,M=D;return a(),n("div",H,[i("div",null,[i("div",J,[m(C,{class:"my-[-5px]","default-height":42},{default:l(()=>[m(b,{modelValue:o(c).category_id,"onUpdate:modelValue":r[0]||(r[0]=e=>o(c).category_id=e),class:"el-radio-group-margin",onChange:r[1]||(r[1]=e=>o(h)())},{default:l(()=>[(a(!0),n(y,null,x(o(v),e=>(a(),d(E,{key:e.id,label:e.id},{default:l(()=>[O(F(e.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),i("div",K,[m(M,null,{default:l(()=>[i("div",T,[o(_).length?(a(),n("div",U,[(a(!0),n(y,null,x(o(_),e=>(a(),d(I,{"active-id":o(u).music.id,"item-id":e.id,key:e.id,class:"mb-[15px]",name:e.name,pic:e.cover,url:e.url,onClick:j=>k(e)},null,8,["active-id","item-id","name","pic","url","onClick"]))),128))])):(a(),d(w,{key:1,image:o(R),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}});export{le as _};
