import{_ as u}from"./CN8DIg3d.js";import{E as d}from"./DlvGt6PY.js";import{E as x}from"./BbVfuIzq.js";import{b as y,_ as h}from"./BsMqt_su.js";/* empty css        */import k from"./C5mZK60S.js";import v from"./BAnZqc6O.js";import{e as w}from"./B7E4a2p6.js";import{l as E,j as R,M as m,N as p,Z as t,a0 as r,u as i,O as n}from"./Dp9aCaJ6.js";import"./Bhzc5F_3.js";import"./DugIyZO8.js";import"./D05sCSvx.js";import"./DBRSLWAE.js";import"./DlAUqK2U.js";import"./D1YDx4Yr.js";import"./DCTLXrZ8.js";import"./BjwsZjjh.js";import"./CieRD4VA.js";import"./Cpwj6wt2.js";import"./9Bti1uB6.js";import"./DDaEm-_F.js";import"./C9-ctX7D.js";import"./l0sNRNKZ.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./CFea67Vu.js";import"./CcPlX2kz.js";import"./Bu_nKEGp.js";import"./DnhMoxbE.js";import"./Cw-YHvx4.js";import"./CKqDBfy_.js";import"./DlEdU_jR.js";import"./C5OYjNdj.js";import"./DlKZEFPo.js";import"./Bz4jch3v.js";import"./Dbma4Whm.js";import"./Cv6HhfEG.js";import"./BATbmIFb.js";import"./BcmWjB0N.js";import"./BPgEaOor.js";/* empty css        *//* empty css        */import"./DjwCd26w.js";import"./Ds7ZwOv-.js";import"./CHWQlbQz.js";/* empty css        */import"./BDqBXPYa.js";const g={key:0,class:"h-full p-[16px] flex"},N={class:"flex-1 min-w-0 h-full pl-[16px]"},B={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Bt=E({__name:"index",setup(j){const l=y(),e=R();return(C,o)=>{const a=u,_=d,c=x,f=h;return m(),p("div",null,[t(f,{name:"default"},{default:r(()=>[i(l).config.switch.music_status?(m(),p("div",g,[t(k,{onUpdate:o[0]||(o[0]=I=>{var s;return(s=i(e))==null?void 0:s.refresh()})}),n("div",N,[t(a,null,{default:r(()=>[t(v,{ref_key:"recordRef",ref:e},null,512)]),_:1})])])):(m(),p("div",B,[t(c,null,{icon:r(()=>[t(_,{class:"w-[150px] dark:opacity-60",src:i(w)},null,8,["src"])]),title:r(()=>o[1]||(o[1]=[n("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Bt as default};
