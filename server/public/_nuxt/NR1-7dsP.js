import{_ as S}from"./BWd1nnnI.js";import{E as j}from"./CMsEk3FA.js";import{e as B,E as R,cT as D,f as F}from"./DNaNbs6R.js";import"./l0sNRNKZ.js";/* empty css        */import{useImageEditor as N}from"./BVqQ2B7o.js";import{E as U}from"./CQAjyP_F.js";import{l as $,b as u,M as L,N as O,Z as l,a0 as r,O as s,u as e,X as b,y as _,n as A}from"./Dp9aCaJ6.js";import{_ as J}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./BWdDF8rn.js";import"./9Bti1uB6.js";const X={class:"locality-draw-popup"},Z={class:"flex justify-center"},q={class:"w-[940px] h-[500px]"},G={class:"dialog-footer flex items-center mt-6 mb-4 px-10 cursor-pointer"},H={class:"flex flex-1 items-center"},K={class:"flex h-full items-end flex-1 rounded-sm overflow-hidden"},P={class:"flex items-center w-full min-h-full px-[6px] bg-page rounded-full"},Q=$({__name:"image-editor",props:{drawFunc:{type:Function}},emits:["success"],setup(h,{expose:k,emit:C}){const E=h,{initCanvas:M,changeTool:f,currentTool:x,onMouseDown:v,onMouseMove:g,onMouseUp:d,captureCombinedSelections:V,undo:y,clearState:z}=N({async onData(n){try{console.log("result",n),await E.drawFunc({...w.value,image_mask:n,prompt:i.value},"inpaint"),I("success"),a.value=!1}finally{p.value=!1}}}),I=C,a=u(!1),i=u(""),p=u(!1),w=u(),T=()=>{if(i.value.trim()===""){F.msgError("请输入重绘描述");return}p.value=!0,V()};return k({open:async n=>{a.value=!0,w.value=n,await A(),M("locality-canvas",n.image)}}),(n,o)=>{const c=S,m=j;return L(),O("div",X,[l(e(U),{modelValue:e(a),"onUpdate:modelValue":o[8]||(o[8]=t=>_(a)?a.value=t:null),width:"980px",class:"!rounded-[12px]",center:!0,draggable:!0,"destroy-on-close":!0,"close-on-click-modal":!1,onClose:e(z)},{header:r(()=>o[9]||(o[9]=[s("div",{class:"w-full text-left"},[s("div",{class:"text-base font-medium"},"MJ 局部重绘 设置"),s("div",{class:"text-xs text-tx-secondary"},"局部重绘")],-1)])),default:r(()=>[s("div",Z,[s("div",q,[s("canvas",{id:"locality-canvas",width:"940",height:"500",onMousedown:o[0]||(o[0]=(...t)=>e(v)&&e(v)(...t)),onMousemove:o[1]||(o[1]=(...t)=>e(g)&&e(g)(...t)),onMouseup:o[2]||(o[2]=(...t)=>e(d)&&e(d)(...t)),onMouseleave:o[3]||(o[3]=(...t)=>e(d)&&e(d)(...t))},null,32)])]),s("div",G,[l(m,{effect:"dark",content:"矩形工具",placement:"top"},{default:r(()=>[s("div",{class:b(["flex justify-center items-center w-[43px] h-[43px] bg-page rounded-full mr-2 hover:bg-primary hover:text-white",[e(x)==="rect"?"bg-primary text-white":""]]),onClick:o[4]||(o[4]=t=>e(f)("RECT"))},[l(c,{name:"local-icon-rect",size:"24"})],2)]),_:1}),l(m,{effect:"dark",content:"套索工具",placement:"top"},{default:r(()=>[s("div",{class:b(["flex justify-center items-center w-[43px] h-[43px] bg-page rounded-full mr-2 hover:bg-primary hover:text-white",[e(x)==="lasso"?"bg-primary text-white":""]]),onClick:o[5]||(o[5]=t=>e(f)("LASSO"))},[l(c,{name:"local-icon-lasso",size:"24"})],2)]),_:1}),l(m,{effect:"dark",content:"返回上一步",placement:"top"},{default:r(()=>[s("div",{class:"flex justify-center items-center w-[43px] h-[43px] bg-page rounded-full mr-2 hover:bg-primary hover:text-white",onClick:o[6]||(o[6]=(...t)=>e(y)&&e(y)(...t))},[l(c,{name:"local-icon-back",size:"24"})])]),_:1}),s("div",H,[s("div",K,[s("div",P,[l(e(B),{modelValue:e(i),"onUpdate:modelValue":o[7]||(o[7]=t=>_(i)?i.value=t:null),"input-style":{"border-radius":"50px",backgroundColor:"var(--el-bg-color-page)"},class:"min-h-full py-[6px]",placeholder:"请输入重绘描述 [推荐英文]",type:"textarea",autosize:{maxRows:3},resize:"none"},null,8,["modelValue"]),l(e(R),{loading:e(p),type:"primary",icon:e(D),circle:!0,onClick:T},null,8,["loading","icon"])])])])])]),_:1},8,["modelValue","onClose"])])}}}),pe=J(Q,[["__scopeId","data-v-2043d927"]]);export{pe as default};
