import{E as k}from"./hOM-i1i4.js";import{_ as w}from"./DMHEbzLi.js";import{E}from"./j7hld3TB.js";import{cn as V,d as z,bF as G}from"./DAgm18qP.js";/* empty css        */import"./BVL3rOLc.js";import{l as I,b as M,m as N,M as m,N as n,Z as r,a0 as P,O as s,u as o,a9 as B,a6 as C,a7 as U,y as $}from"./Dp9aCaJ6.js";import{_ as J}from"./xixvWuCN.js";import{a,b as j}from"./DLSFF3if.js";import{_ as q}from"./D_gfJTDn.js";import{_ as F}from"./DlAUqK2U.js";import"./D3kCNEsc.js";import"./D6pAoVka.js";import"./BkCbCnZg.js";import"./B4CR6vtE.js";import"./CXZvQ2S9.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";import"./CFxUWFIJ.js";import"./CfiDmG6E.js";import"./DCTLXrZ8.js";import"./DFChVafq.js";import"./Uw_fYagh.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */const L={key:0,class:"flex justify-center items-center h-[150px] relative"},D={key:1,class:"uploader-container"},R={class:"el-upload__tip text-[#798696]"},S=I({__name:"uploader",props:{modelValue:{default:""},type:{},files:{}},emits:["update:modelValue"],setup(f,{emit:d}){const u=d,p=f,{modelValue:i}=V(p,u),l=M([]),_=e=>j()?!1:parseInt(a.value.file_size)>0&&e.size>parseInt(a.value.file_size)*1024*1024?(z.error(`文件大小不能超过${a.value.file_size}MB`),!1):!0,v=e=>{u("update:modelValue",e.uri),i.value=e.uri,l.value=[{name:e.name,url:e.uri}]},g=e=>G(p.type,{file:e.file,name:"file",header:{},data:{type:"draw"}}),x=N(()=>{switch(p.type){case"image":return".jpg,.png,.jpeg";case"video":return".wmv,.avi,.mpg,.mpeg,.3gp,.mov,.mp4,.flv,.rmvb,.mkv";case"audio":return;default:return"*"}});return(e,t)=>{const b=k,h=w,y=E;return m(),n("div",null,[r(q,{title:"上传参考图",required:"",tips:"上传一张图片做为基底，用模型在其上面重新生成新的图片"}),r(y,{"file-list":o(l),"onUpdate:fileList":t[1]||(t[1]=c=>$(l)?l.value=c:null),class:"uploader",drag:"",multiple:!1,"show-file-list":!1,"on-success":v,"http-request":g,"before-upload":_,accept:o(x)},{default:P(()=>[s("div",null,[o(i)?(m(),n("div",L,[r(b,{class:"!block h-[100%]",src:o(i),fit:"contain"},null,8,["src"]),r(h,{class:"!absolute right-0 top-0 z-10 drop-shadow",name:"el-icon-CircleCloseFilled",color:"#ffffff",onClick:t[0]||(t[0]=B(c=>i.value="",["stop"]))})])):(m(),n("div",D,[t[2]||(t[2]=s("img",{src:J,alt:"文件上传",class:"w-8 mx-auto mb-2"},null,-1)),t[3]||(t[3]=s("div",{class:"el-upload__text text-[#798696] !text-[13px]"},[C(" 拖拽文件到此处或者"),s("em",null,"点击上传")],-1)),s("div",R,U(parseInt(o(a).file_size)>0?`支持图片格式：JPG/JPEG/PNG，低于${o(a).file_size}MB`:"支持图片格式：JPG/JPEG/PNG"),1)]))])]),_:1},8,["file-list","accept"])])}}}),ge=F(S,[["__scopeId","data-v-3c65f031"]]);export{ge as default};
