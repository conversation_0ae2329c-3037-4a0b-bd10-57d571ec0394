import{E as U}from"./BOI9rKCA.js";import{E as A}from"./CHyUIwEw.js";import{E as K}from"./Du3XpPBg.js";import{E as M}from"./DNaNbs6R.js";import{E as R}from"./CQAjyP_F.js";/* empty css        *//* empty css        *//* empty css        */import{b as q,k as z,f as $,i as O}from"./BFZxX7k8.js";import{l as X,b as d,w as G,F as H,M as r,N as s,O as l,V as J,Z as u,a0 as i,a7 as f,a6 as C,_,aq as w,a4 as Q,u as W,n as Y,X as b}from"./Dp9aCaJ6.js";import{_ as ee}from"./DlAUqK2U.js";import"./DvxhPWqY.js";import"./W1Gt2nzg.js";import"./CCY2gEyH.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";const te=""+new URL("drawing_empty.4ZSZFbZC.png",import.meta.url).href,oe={class:"keyword-dialog"},ae={class:"flex"},le={class:"bg-[var(--el-bg-color)]"},re=["onClick"],se={class:"py-[15px] text-base font-medium text-[#101010] dark:text-white"},ne=["onClick"],ie={key:0,class:"keyword-container"},pe=["onClick"],ue={key:1,class:"flex items-center justify-center w-full h-full"},me={class:"flex justify-between"},de={class:"flex justify-center items-center text-tx-secondary text-base"},ce={class:"mx-1 text-primary"},ve={class:"dialog-footer"},fe=X({__name:"prompt-selector",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(j,{emit:N}){const g=N,x=j,c=d(!1),p=d(-1),h=d([]),m=d({prompt:[],cate_prompt:[]}),o=d([]),y=d([]);G(()=>{x.modelValue==""&&(o.value=[],y.value=[])});const V=t=>{const e=o.value.findIndex(k=>k==t);if(e>=0){o.value.splice(e,1),y.value.splice(e,1);return}o.value.push(t),y.value.push(p.value)},S=()=>{if(c.value=!1,x.modelValue.trim()==""){const t=o.value.length?o.value.join(","):"";g("update:modelValue",t)}else{const t=o.value.length?o.value.join(","):"",e=`${x.modelValue} ${t?`,${t}`:""}`;g("update:modelValue",e)}o.value=[]},B=()=>{c.value=!1;const t=o.value.length?o.value.join(","):"";g("update:modelValue",t),o.value=[]},D=()=>{q()||(c.value=!0)},L=t=>{p.value!=t&&(console.log(t),p.value=t,E())},Z=async()=>{try{await Y(),h.value=await z({model:$.value.draw_api}),p.value=0,await E()}catch(t){console.log("获取关键词分类错误",t)}},E=async()=>{try{m.value=await O({model:$.value.draw_api,id:h.value[p.value].id})}catch(t){console.log("获取关键词错误",t)}};return H(()=>{Z()}),(t,e)=>{const k=U,F=A,I=K,P=M,T=R;return r(),s("div",oe,[l("div",{class:"dialog__trigger",onClick:D},[J(t.$slots,"trigger",{},void 0,!0)]),u(T,{modelValue:c.value,"onUpdate:modelValue":e[2]||(e[2]=a=>c.value=a),width:"870px","align-center":!0,style:{"border-radius":"12px"}},{header:i(()=>e[3]||(e[3]=[l("div",{class:"text-xl font-medium text-[#101010] dark:text-white"}," 描述词推荐 ",-1)])),footer:i(()=>[l("div",me,[l("div",de,[e[6]||(e[6]=l("span",null," 已选择 ",-1)),l("span",ce,f(o.value.length),1),e[7]||(e[7]=l("span",null,"个Tag",-1))]),l("div",ve,[u(P,{type:"primary",onClick:e[0]||(e[0]=a=>S())},{default:i(()=>e[8]||(e[8]=[C(" 添加到文本描述 ")])),_:1}),u(P,{type:"primary",class:"ml-[10px]",plain:!0,onClick:e[1]||(e[1]=a=>B())},{default:i(()=>e[9]||(e[9]=[C(" 替换当前文本描述 ")])),_:1})])])]),default:i(()=>[l("div",ae,[u(k,{class:"w-[110px] bg-page-base",height:"500px"},{default:i(()=>[l("div",le,[(r(!0),s(_,null,w(h.value,(a,n)=>(r(),s("div",{key:n,class:b(["keyword-cate-item",{"keyword-cate-item-active":n==p.value,"keyword-cate-item-prev":n==p.value-1,"keyword-cate-item-next":n==p.value+1,"keyword-cate-item-has-prompt":y.value.includes(n)}]),onClick:v=>L(n)},[l("span",null,f(a.name),1)],10,re))),128))])]),_:1}),u(k,{class:"flex-1",height:"500px"},{default:i(()=>[m.value.prompt.length||m.value.cate_prompt.length?(r(),s(_,{key:0},[(r(!0),s(_,null,w(m.value.cate_prompt,(a,n)=>(r(),s("div",{key:n,class:"keyword-container"},[l("div",se,f(a.name),1),(r(!0),s(_,null,w(a.prompt,v=>(r(),s("div",{key:v.text,class:b(["keyword-item",{"keyword-item-active":o.value.includes(v.prompt_en)}]),onClick:_e=>V(v.prompt_en)},f(v.prompt),11,ne))),128))]))),128)),m.value.prompt.length?(r(),s("div",ie,[e[4]||(e[4]=l("div",{class:"py-[15px] text-base font-medium text-[#101010] dark:text-white"}," 其它 ",-1)),(r(!0),s(_,null,w(m.value.prompt,a=>(r(),s("div",{key:a.text,class:b(["keyword-item",{"keyword-item-active":o.value.includes(a.prompt_en)}]),onClick:n=>V(a.prompt_en)},f(a.prompt),11,pe))),128))])):Q("",!0)],64)):(r(),s("div",ue,[u(I,{title:"","sub-title":"暂无关键词推荐"},{icon:i(()=>[u(F,{class:"w-[200px] h-[200px]",src:W(te)},null,8,["src"])]),default:i(()=>[e[5]||(e[5]=C("> "))]),_:1})]))]),_:1})])]),_:1},8,["modelValue"])])}}}),Fe=ee(fe,[["__scopeId","data-v-7f6d7181"]]);export{Fe as default};
