import{a as j,E as k}from"./C3h7pD7s.js";import{cn as w}from"./DyU4wb-Q.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{a as t,f as n}from"./BX6AFV3P.js";import{_ as x}from"./C0E0SCMn.js";import{l as y,c as B,u as l,M as p,N as c,Z as d,O as E,a0 as N,_ as b,aq as g,y as h,a4 as C,a1 as M}from"./Dp9aCaJ6.js";import{_ as O}from"./DlAUqK2U.js";import"./DCa8BdSG.js";import"./DCTLXrZ8.js";import"./BDGxxzzg.js";import"./9MAnF5ll.js";import"./Cv6HhfEG.js";import"./D4s3zOA5.js";import"./CH_T-XC0.js";import"./CQqsfOf-.js";import"./D-tOg06u.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./tONJIxwY.js";import"./DUH6hp3a.js";import"./BolOpO9r.js";import"./C27mmGOG.js";import"./9Bti1uB6.js";/* empty css        */const q={key:0,class:"mj-version"},z=y({__name:"mj-version",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(_,{emit:f}){const v=f,V=_,{modelValue:e}=w(V,v);return B(()=>{var o;return[n.value.draw_model,(o=t.value)==null?void 0:o.mj_version]},([o,s])=>{var r,a;(r=t.value)!=null&&r.mj_version&&e.value!==o&&(e.value=(a=t.value)==null?void 0:a.mj_version[n.value.draw_model][0])}),(o,s)=>{var u;const r=k,a=j;return(u=l(t))!=null&&u.mj_version?(p(),c("div",q,[d(x,{title:"版本选择",tips:"指定midjourney的渲染版本"}),E("div",null,[d(a,{modelValue:l(e),"onUpdate:modelValue":s[0]||(s[0]=m=>h(e)?e.value=m:null),placeholder:"请选择版本",class:"w-full mt-[8px]",size:"large"},{default:N(()=>{var m;return[(p(!0),c(b,null,g((m=l(t))==null?void 0:m.mj_version[l(n).draw_model],(i,D)=>(p(),M(r,{key:i,label:i,value:i},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])])])):C("",!0)}}}),ie=O(z,[["__scopeId","data-v-4b0030cc"]]);export{ie as default};
