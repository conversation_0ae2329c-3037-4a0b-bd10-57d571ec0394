import{a as F,E as K}from"./DwdGJTJg.js";import{_ as y}from"./DBRSLWAE.js";import{E as O}from"./DlvGt6PY.js";import{E as A}from"./Cpwj6wt2.js";import{E as Z}from"./BDqBXPYa.js";import{l as G,j as H,b as J,f as b}from"./BsMqt_su.js";/* empty css        *//* empty css        */import{l as Q,b as S,r as C,j as W,F as X,M as n,N as l,O as e,Z as a,a0 as E,u as p,aa as Y,_ as N,aq as ee,a4 as d,a1 as te,n as v,a9 as w,a7 as h}from"./Dp9aCaJ6.js";import{_ as oe}from"./BrRZXllZ.js";import{_ as se}from"./ibXWx_qH.js";import{u as ae,c as ie}from"./BNnETjxs.js";import{u as ne}from"./DN8m6sJe.js";import{_ as re}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./Ck3hJ2J3.js";import"./Bhzc5F_3.js";import"./DugIyZO8.js";import"./D05sCSvx.js";import"./BjwsZjjh.js";import"./D1YDx4Yr.js";import"./DCTLXrZ8.js";import"./CieRD4VA.js";import"./9Bti1uB6.js";import"./CDqp_Mv_.js";import"./Bz4jch3v.js";import"./Dbma4Whm.js";import"./BATbmIFb.js";import"./BcmWjB0N.js";import"./BPgEaOor.js";import"./HKVwdJ-h.js";import"./BeID4H0V.js";/* empty css        *//* empty css        *//* empty css        */import"./Ds7ZwOv-.js";import"./CHWQlbQz.js";/* empty css        */import"./DOF_zc-p.js";import"./6g5VST7z.js";import"./DhvjYsFQ.js";import"./BEVfyfzl.js";import"./Boo8ogMh.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */const le={class:"h-full flex flex-col"},pe={class:"px-[20px] py-[16px] mt-[-8px]"},ce={class:"flex-1 min-h-0"},me={key:0,class:"flex flex-wrap px-[20px]","infinite-scroll-distance":"50"},de=["onClick"],fe={class:"flex relative"},ue=["onClick"],_e={class:"ml-1 text-sm"},xe={key:0,class:"kb-btns absolute bottom-0 left-0 w-full flex bg-[rgba(0,0,0,0.4)] py-[6px] text-white"},ge=["onClick"],be=["onClick"],ve={class:"px-[15px] py-[12px]"},we={class:"text-[18px] truncate"},he={class:"flex items-center mt-[10px]"},ke={class:"text-info flex-1 min-w-0 truncate"},ye={key:1},Se=Q({__name:"kb",setup(Ce){const $=G(),r=H(),D=ne(),I=J(),f=S({type:"0"});C({show:!1,num:0,kbId:0,isOwner:!1});const m=S(!1),u=W(),o=C({pageNo:1,count:0,pageSize:15,lists:[]}),L=async()=>{o.pageNo=1,await v(),await c()},c=async()=>{const i=await ae({...f.value,page_no:o.pageNo,page_size:o.pageSize});o.count=i.count,o.pageNo===1&&(o.lists=[]),o.lists.push(...i.lists)},R=()=>{r.isLogin&&o.count>=o.pageNo*o.pageSize&&(o.pageNo++,c())},V=async()=>{if(r.userInfo.kb_num<=0){I.getIsShowRecharge?(await b.confirm("知识库数量已用完，请前往充值"),D.toggleShow(!0)):b.msgError("知识库数量已用完。请联系客服增加");return}if(!r.isLogin)return r.toggleShowLogin();m.value=!0,await v(),u.value.open()},j=async i=>{m.value=!0,await v(),u.value.open({id:i})},z=async(i,t)=>{await b.confirm(`确认删除 ${t} 吗？`),await ie({id:i}),c()},k=(i,t)=>{$.push(`/application/kb/detail?id=${i}&type=${t}`)};return X(()=>{c()}),(i,t)=>{const _=F,B=K,P=y,T=O,x=y,M=A,U=Z;return n(),l(N,null,[e("div",le,[e("div",pe,[a(B,{modelValue:p(f).type,"onUpdate:modelValue":t[0]||(t[0]=s=>p(f).type=s),class:"demo-tabs",onTabChange:L},{default:E(()=>[a(_,{label:"全部知识库",name:"0"}),a(_,{label:"我的知识库",name:"1"}),a(_,{label:"共享给我",name:"2"})]),_:1},8,["modelValue"])]),e("div",ce,[a(M,null,{default:E(()=>[p(r).isLogin?Y((n(),l("div",me,[e("div",{class:"sm:w-[200px] w-full bg-body rounded-[12px] overflow-hidden cursor-pointer mr-[20px] flex-none mb-[20px] flex flex-col items-center justify-center min-h-[150px]",onClick:V},[a(P,{name:"el-icon-Plus",size:24}),t[2]||(t[2]=e("div",{class:"mt-[10px]"},"新增知识库",-1))]),(n(!0),l(N,null,ee(p(o).lists,(s,q)=>(n(),l("div",{key:q,class:"kb-item sm:w-[200px] w-full bg-body rounded-[12px] overflow-hidden cursor-pointer mr-[20px] flex-none mb-[20px]",onClick:g=>k(s.id,"dataStudy")},[e("div",fe,[a(T,{src:s.image,class:"w-full h-[160px]",fit:"cover"},null,8,["src"]),e("div",{class:"bg-[rgba(0,0,0,0.4)] text-white absolute px-1 right-[10px] top-[10px] rounded flex items-center",onClick:w(g=>k(s.id,"teamData"),["stop"])},[a(x,{name:"el-icon-User"}),e("span",_e,h(s.team_people),1)],8,ue),s.is_super?(n(),l("div",xe,[e("div",{class:"flex flex-1 items-center justify-center",onClick:w(g=>j(s.id),["stop"])},[a(x,{name:"el-icon-Edit"}),t[3]||(t[3]=e("span",{class:"ml-1"}," 编辑 ",-1))],8,ge),e("div",{class:"flex flex-1 items-center justify-center",onClick:w(g=>z(s.id,s.name),["stop"])},[a(x,{name:"el-icon-Delete"}),t[4]||(t[4]=e("span",{class:"ml-1"}," 删除 ",-1))],8,be)])):d("",!0)]),e("div",ve,[e("div",we,h(s.name),1),e("div",he,[e("div",ke,h(s.intro||"这个知识库还没介绍呢～"),1)])])],8,de))),128))])),[[U,R]]):d("",!0),p(r).isLogin?d("",!0):(n(),l("div",ye,[a(se)]))]),_:1})])]),p(m)?(n(),te(oe,{key:0,ref_key:"popRef",ref:u,onSuccess:t[1]||(t[1]=()=>{m.value=!1,c()})},null,512)):d("",!0)],64)}}}),bt=re(Se,[["__scopeId","data-v-7db50a3a"]]);export{bt as default};
