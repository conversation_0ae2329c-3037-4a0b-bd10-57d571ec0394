import{d3 as k,d4 as w,E as C,d5 as B}from"./CmRxzTqw.js";import{E as z,a as N}from"./DHUC3PVh.js";import{E as R,_ as T}from"./BildjBiE.js";import{E as P}from"./oVx59syQ.js";import{E as j}from"./llRQJmEG.js";import{_ as L}from"./DJi8L2lq.js";/* empty css        */import{u as A}from"./DRe575WM.js";import{l as F,r as M,E as S,M as i,N as p,O as r,a3 as a,a1 as s,u as e,a0 as x,y as U,ag as d,_ as q,ao as I}from"./CUZG7cWw.js";import{_ as O}from"./DlAUqK2U.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";/* empty css        */import"./C-n0m2hZ.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./DCzKTodP.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";/* empty css        *//* empty css        *//* empty css        */const $={class:"p-[20px] flex bg-body rounded-[12px] flex-col h-full"},D={class:"flex justify-between relative"},G={class:"flex-1 min-h-0 flex flex-col"},H={class:"flex-1 min-h-0"},J={key:1,class:"flex items-center justify-center h-full"},K={class:"flex justify-end mt-4"},Q=F({__name:"notification",setup(W){const m=M({type:1}),{pager:t,getLists:l,resetPage:X}=A({fetchFun:k,params:m}),g=async()=>{await w(),await l()};return S(()=>{l()}),(Y,o)=>{var c,f;const _=B,u=N,y=z,b=C,E=T,h=P,v=j,V=L;return i(),p("div",$,[r("div",D,[a(y,{class:"flex-1",modelValue:e(m).type,"onUpdate:modelValue":o[0]||(o[0]=n=>e(m).type=n),onTabChange:e(l)},{default:s(()=>[a(u,{label:"系统通知",name:1},{label:s(()=>[a(_,{class:"flex-none text-xl font-medium",value:e(t).system_unread,"show-zero":!!e(t).system_unread,offset:[10,0]},{default:s(()=>o[2]||(o[2]=[d(" 系统通知 ")])),_:1},8,["value","show-zero"])]),_:1}),a(u,{label:"审核通知",name:2},{label:s(()=>[a(_,{class:"flex-none text-xl font-medium",value:e(t).audit_unread,"show-zero":!!e(t).audit_unread,offset:[10,0]},{default:s(()=>o[3]||(o[3]=[d(" 审核通知 ")])),_:1},8,["value","show-zero"])]),_:1})]),_:1},8,["modelValue","onTabChange"]),a(b,{class:"absolute right-0 top-[10px]",type:"primary",link:!0,disabled:!e(t).unread,onClick:g},{default:s(()=>o[4]||(o[4]=[d(" 全部已读 ")])),_:1},8,["disabled"])]),r("div",G,[r("div",H,[(f=(c=e(t))==null?void 0:c.lists)!=null&&f.length?(i(),x(h,{key:0,height:"100%"},{default:s(()=>[(i(!0),p(q,null,I(e(t).lists,n=>(i(),x(E,{key:n.id,data:n,size:"large",onRead:e(l)},null,8,["data","onRead"]))),128))]),_:1})):(i(),p("div",J,[a(v,{image:e(R),"image-size":250,description:"暂无消息通知"},null,8,["image"])]))]),r("div",K,[a(V,{modelValue:e(t),"onUpdate:modelValue":o[1]||(o[1]=n=>U(t)?t.value=n:null),onChange:e(l)},null,8,["modelValue","onChange"])])])])}}}),Ne=O(Q,[["__scopeId","data-v-afc3971f"]]);export{Ne as default};
