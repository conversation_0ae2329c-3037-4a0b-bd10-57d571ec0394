import{_ as b}from"./eFgaMLiC.js";import{E as N}from"./DNRqakyH.js";import{_ as U}from"./Cs0_Uid5.js";import{E as $}from"./ArzC3z2d.js";import{b as I,bH as M,v as j}from"./CmRxzTqw.js";import"./DikNcrXK.js";/* empty css        */import{l as A,az as H,j as C,b as x,c as L,n as k,m as O,E as P,u as a,M as r,N as p,O as c,a7 as T,a0 as Z,a1 as w,V as q,a3 as d,Z as f,a6 as G,a4 as V,y as J}from"./CUZG7cWw.js";import{_ as K}from"./DlAUqK2U.js";const Q={key:0},W={class:"flex"},X={key:0,class:"el-upload flex-col bg-fill-lighter"},Y={key:1,class:"imgContiner relative"},ee=["src"],te=A({__name:"index",props:{modelValue:{type:String,default:""},excludeDomain:{type:Boolean,default:!1},canClose:{type:Boolean,default:!0},size:{type:String,default:"100px"}},emits:["change","update:modelValue"],setup(s,{emit:z}){H(t=>({"4c5b0256":s.size}));const h=z,{getImageUrl:E}=I(),u=s,v=C(),n=x(!1),y=x(!1);L(n,t=>{t?k(()=>{var e;(e=v.value)==null||e.play()}):k(()=>{var e;(e=v.value)==null||e.pause()})});const m=x(!1),l=O({get(){return u.excludeDomain?E(u.modelValue):u.modelValue},set(t){h("update:modelValue",t)}}),_=C(),R=async({raw:t})=>{var e,i;try{m.value=!0;const o=await M("video",{file:t});m.value=!1,l.value=u.excludeDomain?o.url:o.uri,h("change",o.uri),(e=_.value)==null||e.clearFiles()}catch{m.value=!1,(i=_.value)==null||i.clearFiles()}};return P(()=>{y.value=!0}),(t,e)=>{const i=b,o=b,B=N,D=U,S=$,F=j;return a(y)?(r(),p("div",Q,[c("div",W,[T((r(),Z(B,{"element-loading-text":"上传中...",ref_key:"uploadRef",ref:_,class:"avatar-uploader flex","show-file-list":!1,limit:1,"on-change":R,"auto-upload":!1,accept:".wmv,.avi,.mpg,.mpeg,.3gp,.mov,.mp4,.flv,.rmvb,.mkv"},{default:w(()=>[q(t.$slots,"default",{},()=>[a(l)?f("",!0):(r(),p("div",X,[d(i,{name:"el-icon-Plus",size:20}),e[3]||(e[3]=c("div",{class:"text-tx-secondary mt-[2px]"},"添加视频",-1))])),a(l)?(r(),p("div",Y,[c("div",{class:"border border-solid border-br-light rounded-[6px] relative cursor-pointer",style:G({width:s.size,height:s.size})},[c("video",{class:"rounded-lg w-full h-full",src:a(l)},null,8,ee),c("div",{class:"z-[10px] absolute left-1/2 top-1/2 translate-x-[-50%] translate-y-[-50%] rounded-full w-5 h-5 flex justify-center items-center bg-[rgba(0,0,0,0.3)]",onClick:e[0]||(e[0]=V(g=>n.value=!0,["stop"]))},[d(o,{name:"el-icon-CaretRight",size:18,color:"#fff"})])],4),s.canClose?(r(),p("div",{key:0,class:"icon absolute top-[-10px] right-[-10px] text-tx-secondary",onClick:e[1]||(e[1]=V(g=>l.value="",["stop"]))},[d(i,{size:"20",name:"el-icon-CircleCloseFilled"})])):f("",!0)])):f("",!0)],!0)]),_:3})),[[F,a(m)]])]),d(S,{modelValue:a(n),"onUpdate:modelValue":e[2]||(e[2]=g=>J(n)?n.value=g:null),width:"740px",title:"视频预览"},{default:w(()=>[d(D,{ref_key:"playerRef",ref:v,src:a(l),width:"100%",height:"450px"},null,8,["src"])]),_:1},8,["modelValue"])])):f("",!0)}}}),ue=K(te,[["__scopeId","data-v-6025f73c"]]);export{ue as _};
