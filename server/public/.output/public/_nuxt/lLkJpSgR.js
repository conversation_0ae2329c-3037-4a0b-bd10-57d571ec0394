import{P,_ as T,p as A,b as W,a as $}from"./C9pcNxkS.js";import{E as K}from"./oVx59syQ.js";import{l as M,b as O,j as q,dd as H,de as X,df as b,E as Z,f as h,B as G}from"./CmRxzTqw.js";/* empty css        */import{u as J}from"./Bj_9-7Jh.js";import{g as Q,r as Y}from"./5SHXd8at.js";import{l as ee,b as f,m as E,E as te,M as a,N as n,a3 as d,a1 as w,u as o,O as e,ag as k,Z as _,a5 as r,_ as C,ao as I,$ as S,y as se}from"./CUZG7cWw.js";import{_ as oe}from"./DlAUqK2U.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./eFgaMLiC.js";/* empty css        */import"./DqGsTvs3.js";const ae={class:"relative flex flex-col min-h-0 h-full"},ne={class:"grid grid-cols-3 md:grid-cols-3 gap-4 h-[150px] bg-[#EEF2FF] py-[20px] rounded-[12px] flex-none dark:bg-body"},re={class:"flex flex-col pl-[30px] items-start justify-center"},le={class:"flex flex-col items-center"},ie=["src"],ce={class:"mt-[10px] text-sm"},de={class:"flex flex-col items-center justify-center"},pe={class:"font-medium text-[25px] text-[#0256FF]"},ue={class:"mt-[4px]"},_e={class:"flex flex-col items-end justify-center pr-[30px]"},xe={class:"flex flex-col items-center"},me={class:"font-medium text-[25px] text-[#0256FF]"},fe={key:0,class:"bg-[#EEF2FF] mt-[16px] rounded-[12px] flex-1 min-h-0 dark:bg-body"},ge={class:"p-[26px] pb-[120px] bg-[#EEF2FF] dark:bg-[#1D2025]"},ve={class:"flex flex-col"},ye={class:"recharge-lists flex flex-wrap"},be=["onClick"],he={key:0,class:"absolute top-[-8px] left-[-1px] bg-[#FF7272] px-[12px] py-[2px] text-white rounded-tl-[10px] rounded-br-[10px] text-xs"},ke={class:"text-xl font-medium mb-[10px] line-clamp-1"},Fe={class:"border-t border-solid border-br-light pt-[10px]"},Ee={class:"flex"},we={class:"text-tx-secondary mr-auto"},Ce={class:"relative"},Ie={key:0},Se={key:1},Ue={key:0,class:"py-[8px] text-primary text-sm"},De={key:0},Ne={key:1},ze={class:"mt-[10px]"},Be={key:1,class:"w-full h-full bg-[#EEF2FF] mt-[16px] p-[26px] rounded-[12px] flex items-center justify-center"},Re={key:0,class:"absolute left-0 bottom-0 w-full px-[26px]"},Ve={class:"mt-[40px] flex justify-between rounded-[12px] bg-white py-[15px] px-[20px] dark:bg-page"},je=ee({__name:"recharge",setup(Le){const U=M(),c=O(),g=q(),x=f([]),D=E(()=>[{key:"chat_balance",giveKey:"give_chat_balance",label:`${c.getTokenUnit}数量`,unit:c.getTokenUnit},{key:"robot_number",giveKey:"give_robot_number",label:"智能体个数",unit:"个"}]),v=f({balance:"",robot_num:"",video_num:""}),m=f(0),N=G(),i=f(P.WECHAT),y=E(()=>x.value[m.value]||{});(async()=>{const l=await Q();x.value=l.lists,v.value=l.extend;const t=x.value.findIndex(p=>p.is_recommend);m.value=t===-1?0:t})();const{lockFn:z,isLock:B}=J(async()=>{y.value.id||h.msgError("请选择充值套餐"),i.value||h.msgError("请选择支付方式");const l=await Y({package_id:y.value.id,pay_way:i.value}),t=await A({...l,pay_way:i.value,redirect:`${N.app.baseURL}user/record?type=recharge`,code:b.getAuthData().code});await W.run({payWay:i.value,orderId:l.order_id,from:l.from,config:t.config}),await g.getUser(),await h.alertSuccess("支付成功"),U.push({path:"/user/record",query:{id:l.order_id,type:"recharge"}})});return te(async()=>{H()==X.WEIXIN_OA&&b.getAuthData().code==""&&await b.getUrl()}),(l,t)=>{const p=T,R=$,V=K,j=Z;return a(),n("div",ae,[d(V,{class:"scrollbar rounded-[12px]"},{default:w(()=>[e("div",ne,[e("div",re,[e("div",le,[e("img",{src:o(g).userInfo.avatar,class:"w-[64px] h-[64px] rounded-full"},null,8,ie),e("div",ce," 用户ID: "+r(o(g).userInfo.sn),1)])]),e("div",de,[e("div",pe,r(o(v).balance),1),e("div",ue,"剩余"+r(o(c).getTokenUnit),1)]),e("div",_e,[e("div",xe,[e("div",me,r(o(v).robot_num),1),t[1]||(t[1]=e("div",{class:"mt-[4px]"},"智能体数量",-1))])])]),o(c).getIsShowRecharge?(a(),n("div",fe,[e("div",ge,[e("div",ve,[t[3]||(t[3]=e("div",{class:"text-2xl font-medium"},"选择套餐",-1)),e("div",ye,[(a(!0),n(C,null,I(o(x),(s,F)=>(a(),n("div",{key:s.id,class:S(["recharge-item relative",{active:F===o(m)}]),onClick:u=>m.value=F},[s.tags!=""?(a(),n("div",he,r(s.tags),1)):_("",!0),e("div",null,[e("div",null,[e("div",ke,r(s.name),1),d(p,{content:s.sell_price,"main-size":"28px","minor-size":"16px"},null,8,["content"]),e("div",{class:S([{"opacity-0":s.line_price==="0.00"},"mb-[20px]"])},[d(p,{prefix:"原价",content:s.line_price,"main-size":"14px","line-through":"",color:"#999"},null,8,["content"])],2)]),e("div",Fe,[(a(!0),n(C,null,I(o(D),(u,L)=>(a(),n("div",{class:"text-sm py-[8px]",key:L},[e("div",Ee,[e("span",we,r(u.label),1),e("div",Ce,[s[u.key]>0?(a(),n("span",Ie,r(Number(s[u.key]))+r(u.unit),1)):(a(),n("span",Se,"-"))])])]))),128)),s.is_give?(a(),n("div",Ue,[t[2]||(t[2]=k(" 赠: ")),s.give_chat_balance?(a(),n("span",De,r(s.give_chat_balance)+r(o(c).getTokenUnit)+" "+r(s.give_robot_number?"，":""),1)):_("",!0),s.give_robot_number?(a(),n("span",Ne,r(s.give_robot_number)+"智能体 ",1)):_("",!0)])):_("",!0)])])],10,be))),128))])]),e("div",ze,[t[4]||(t[4]=e("div",{class:"text-2xl font-medium mb-[5px]"}," 支付方式 ",-1)),d(R,{modelValue:o(i),"onUpdate:modelValue":t[0]||(t[0]=s=>se(i)?i.value=s:null),from:"recharge"},null,8,["modelValue"])])])])):(a(),n("div",Be,t[5]||(t[5]=[e("div",{class:"text-xl"},"功能未开启!",-1)])))]),_:1}),o(c).getIsShowRecharge?(a(),n("div",Re,[e("div",Ve,[e("div",null,[t[6]||(t[6]=k(" 实付金额： ")),d(p,{content:o(y).sell_price,"main-size":"24px","minor-size":"14px",color:"#FF7021"},null,8,["content"])]),d(j,{type:"primary",size:"large",loading:o(B),onClick:o(z),style:{border:"none","border-radius":"6px",padding:"0 54px"}},{default:w(()=>t[7]||(t[7]=[k(" 立即购买 ")])),_:1},8,["loading","onClick"])])])):_("",!0)])}}}),et=oe(je,[["__scopeId","data-v-494a1cbe"]]);export{et as default};
