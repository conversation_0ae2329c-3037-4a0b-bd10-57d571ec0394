import{_ as t}from"./BX9LuVNS.js";import{_ as s}from"./DlorTuIx.js";import{_ as d}from"./Cu1bEeyC.js";import{_ as m}from"./nO7O23Ti.js";import{_ as c}from"./xFN416HV.js";import{l,b as u,M as e,N as _,u as o,a0 as r,Z as i}from"./CUZG7cWw.js";const k={class:"p-main"},v=l({__name:"index",props:{appId:{}},setup(f){const p=u(),n=(a="")=>{p.value=a};return(a,y)=>(e(),_("div",k,[o(p)?i("",!0):(e(),r(c,{key:0,onClickItem:n})),o(p)==="web"?(e(),r(t,{key:1,"app-id":a.appId,onBack:n},null,8,["app-id"])):i("",!0),o(p)==="js"?(e(),r(d,{key:2,"app-id":a.appId,onBack:n},null,8,["app-id"])):i("",!0),o(p)==="oa"?(e(),r(m,{key:3,"app-id":a.appId,onBack:n},null,8,["app-id"])):i("",!0),o(p)==="api"?(e(),r(s,{key:4,type:4,"app-id":a.appId,onBack:n},null,8,["app-id"])):i("",!0),o(p)==="qwx"?(e(),r(s,{key:5,type:5,"app-id":a.appId,onBack:n},null,8,["app-id"])):i("",!0),o(p)==="yd"?(e(),r(s,{key:6,type:7,"app-id":a.appId,onBack:n},null,8,["app-id"])):i("",!0)]))}});export{v as _};
