import{_ as l}from"./eFgaMLiC.js";import{b as u,E as f}from"./CmRxzTqw.js";import{useSearch as d}from"./CaJo29OT.js";import{l as x,M as a,a0 as g,a1 as n,a3 as k,ag as o,O as y,u as e,N as r,_ as c,a5 as p,Z as B}from"./CUZG7cWw.js";const N={class:"text-xs ml-1"},F=x({__name:"search-btn",setup(S){const{config:t}=d(),i=u();return(V,s)=>{const m=l,_=f;return a(),g(_,{type:"primary",style:{padding:"8px"}},{icon:n(()=>[k(m,{name:"el-icon-Search"})]),default:n(()=>[s[0]||(s[0]=o(" 搜索 ")),y("span",N,[e(t).isVipFree?(a(),r(c,{key:0},[o(" 会员免费 ")],64)):e(t).price>0?(a(),r(c,{key:1},[o(" -"+p(e(t).price)+p(e(i).getTokenUnit),1)],64)):B("",!0)])]),_:1})}}});export{F as _};
