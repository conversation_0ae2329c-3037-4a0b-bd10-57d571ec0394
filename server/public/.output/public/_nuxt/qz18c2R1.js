import{_ as et}from"./CH6wv3Pu.js";import{b as tt,_ as ot,a as at}from"./CCGM0zxW.js";import{_ as st}from"./eFgaMLiC.js";import{b as rt,a as it,j as nt,cD as ze,bz as lt,bA as ct,u as dt,bD as ne,bG as le,f as K,E as ut}from"./CmRxzTqw.js";import{E as pt}from"./Do9LV2MU.js";import{_ as ft}from"./D5RYEqFL.js";import{u as vt}from"./DAOx25wS.js";import{l as mt,j as T,b as y,ai as ht,r as ce,m as Pe,c as _t,E as yt,k as gt,M as u,N as x,O as a,u as e,$ as je,Z as g,a5 as V,a3 as f,a1 as v,ag as H,a7 as $e,a8 as Ae,a6 as de,a0 as k,_ as Ve,ao as Ie,n as xt}from"./CUZG7cWw.js";import{u as wt}from"./DNOp0HuO.js";import{u as bt,a as kt}from"./C-cKpkeq.js";import{u as Rt}from"./DoCT-qbH.js";import{_ as Ct}from"./GcP5Frf5.js";import{_ as Et}from"./D7FKS3pM.js";import{C as Tt,b as St,r as Lt,c as zt,a as Pt,v as jt,d as $t}from"./qRM0tN96.js";import{P as At}from"./CaNlADry.js";import{E as Be}from"./oVx59syQ.js";import{_ as Vt}from"./DlAUqK2U.js";import"./DCzKTodP.js";/* empty css        */import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./BYMcWg3Q.js";/* empty css        */import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        */import"./DjHPV-Am.js";/* empty css        */import"./DwFObZc_.js";import"./DQUFgXGm.js";import"./CRNANWso.js";import"./CHg9aK2B.js";import"./DecTOTC8.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./B7GaOiDz.js";import"./D6yUe_Nr.js";import"./DP2rzg_V.js";/* empty css        */const Oe=""+new URL("user_avatar.B42E77Pp.png",import.meta.url).href,It={class:"h-full flex flex-col max-w-[720px] mx-auto bg-page rounded-[10px] overflow-hidden",style:{"box-shadow":"0px 5px 40px 0px rgba(0, 0, 0, 0.05)"}},Bt={class:"flex p-main items-center bg-body"},Ot=["src"],Dt={class:"text-2xl line-clamp-1"},Mt={class:"text-tx-secondary line-clamp-1"},Nt={class:"flex-1 min-h-0"},Ht={ref:"containerRef",class:"h-full flex flex-col rounded relative"},qt={class:"absolute top-0 left-0 w-full h-full flex flex-col z-10"},Ft={class:"flex-1 min-h-0"},Wt={class:"p-main"},Ut={class:"my-[5px]"},Jt={class:"bg-body"},Gt={key:0,class:"pb-[10px] text-center text-tx-regular"},Zt={key:1,class:"h-full relative"},Kt=["width","height"],Qt={class:"h-full flex justify-center items-center"},Yt={class:"p-[20px] h-full flex relative z-10"},Xt={class:"flex-1 h-full flex flex-col"},eo={class:"flex-1 min-h-0"},to={class:"flex items-center cursor-pointer"},oo={class:"text-xl flex-1 min-w-0 text-white"},ao={class:"h-full flex"},so={class:"h-full flex flex-col items-center w-[160px] justify-end"},ro=["width","height","id"],io={class:"text-xs text-white bg-[rgba(51,51,51,0.3)] py-[5px] px-[10px] rounded my-[10px]"},no={class:"w-[400px] h-full flex flex-col mr-[20px] pt-[100px]"},lo={class:"flex-1 min-h-0 bg-[rgba(0,0,0,0.5)] rounded-[20px] overflow-hidden flex flex-col"},co={class:"flex-1 min-h-0"},uo={class:"py-4 px-[20px]"},po={key:1,class:"h-full flex justify-center text-tx-secondary items-center"},fo={key:0,class:"flex justify-center items-center py-[10px]"},vo={class:"flex flex-col justify-center items-center"},mo={class:"flex justify-center mt-4"},ho=mt({__name:"[key]",async setup(_o){let q,ue;const F=rt(),De=it(),C=nt(),{copy:Me}=vt(),Q=T(),{key:w=""}=De.params,S=y(""),{height:pe,width:yo}=ze(),{data:r}=([q,ue]=ht(()=>wt(()=>Tt({apikey:w}),{default(){return{robot:{}}}},"$89nQiJhyNZ")),q=await q,ue(),q),Y=T(),L=ce({_index:-1,robot_id:r.value.robot.id,record_id:-1,content:""}),Ne=(o,t)=>{var c;L.record_id=o.id,L._index=t,(c=Y.value)==null||c.open()},He=async()=>{var o;try{await zt(L),(o=Y.value)==null||o.close(),L.content="",h.value[L._index].is_feedback=1}catch(t){console.log("反馈提交失败-->",t)}},I=Pe(()=>r.value.chat_type===2&&r.value.robot.is_digital&&r.value.digital.id&&!r.value.digital.is_disable?2:1);F.isMobile&&I.value===2&&window.location.replace(`/mobile/packages/pages/digital_chat/share_chat?key=${w}`);const fe=Pe(()=>{var o;return((o=r.value.menus)==null?void 0:o.map(t=>({keyword:t})))||[]}),O=async()=>{var t;const o=ne(le,{});if(S.value=o.value[w]||"",r.value.pwd&&!S.value)return(t=Q.value)==null||t.open(),Promise.reject()},qe=async o=>{var c;const t=ne(le,{});S.value=o.password,t.value=Object.assign(t.value,{[w]:o.password}),(c=Q.value)==null||c.close(),Le()},ve=()=>{const o=ne(le,{});o.value=Object.assign(o.value,{[w]:""})},h=y([]);let X=0;const ee=async()=>{try{const o=await Pt({share_apikey:w,identity:C.visitorId,page_size:25e3},{password:S.value,authorization:w,identity:C.visitorId});if(h.value=o.lists||[],setTimeout(()=>{D()}),I.value===2&&_.value==3){const t=h.value[h.value.length-1];t&&t.id!==X&&(X=t.id,Ze(X))}}catch(o){return o=="访问密码错误!"&&(ve(),await O()),Promise.reject()}},me=async()=>{await O(),await K.confirm("确定清空记录？"),await St({},{password:S.value,authorization:w,identity:C.visitorId}),ee()};let p=null;const z=y(!1),W=T(),U=async(o,t="input")=>{var m;if(await C.getFingerprint(),await O(),!o)return K.msgError("请输入问题");if(z.value)return;z.value=!0,b(3);const c=Date.now();h.value.push({type:1,content:o}),h.value.push({type:2,typing:!0,content:"",key:c}),(m=W.value)==null||m.setInputValue();const s=h.value.find(i=>i.key===c);p=Lt({question:o,stream:!0},{password:S.value,authorization:w,identity:C.visitorId}),p.addEventListener("chat",({data:i})=>{const{data:n,index:R}=i;s.content||(s.content=""),s.content+=n}),p.addEventListener("file",({data:i})=>{try{const n=JSON.parse(i.data);s.files=n}catch(n){console.error(n)}}),p.addEventListener("image",({data:i})=>{try{const n=JSON.parse(i.data);s.images=n}catch(n){console.error(n)}}),p.addEventListener("video",({data:i})=>{try{const n=JSON.parse(i.data);s.videos=n}catch(n){console.error(n)}}),p.addEventListener("close",()=>{setTimeout(async()=>{await ee(),s.typing=!1,z.value=!1,D()},600)}),p.addEventListener("error",i=>{var n,R,$;b(1),i.errorType==="connectError"&&K.msgError("请求失败，请重试"),((n=i.data)==null?void 0:n.code)===1200&&(K.msgError((R=i.data)==null?void 0:R.message),ve(),setTimeout(()=>{O()},10)),["connectError","responseError"].includes(i.errorType)&&(h.value.splice(h.value.length-2,2),t==="input"&&(($=W.value)==null||$.setInputValue(o))),s.typing=!1,setTimeout(()=>{z.value=!1},200)})},J=T(),te=y(),D=async()=>{var t,c,s;const o=(c=(t=J.value)==null?void 0:t.wrapRef)==null?void 0:c.scrollHeight;(s=J.value)==null||s.setScrollTop(o)},{height:Fe}=lt(te);ct(Fe,()=>{z.value&&D()},{throttle:500,immediate:!0});const _=y(0),We=ce({0:"正在初始化对话...",1:"点击开始说话",2:"我在听，您请说...",3:"稍等，让我想一想",4:"正在回复中..."}),b=o=>{I.value===2&&(_.value=o)},he=y(),B=y(!0),oe=y(!1),ae=y(0),P=y(!1),_e=y(0),E=ce({id:"audio-canvas",width:80,height:40,minHeight:5,scale:2}),{render:Ue,stopRender:Je,draw:go}=bt(E),{start:Ge,stop:se,isRecording:M,authorize:ye,close:xo,isOpen:wo}=kt({onstart(){b(2),clearTimeout(he.value),P.value=!1,ae.value=Date.now()},async onstop(o){if(Je(),P.value=!1,!oe.value){b(1);return}oe.value=!1,b(3);try{const t=await jt({file:o.blob});if(!t.text){B.value&&j();return}U(t.text,"voice")}catch{B.value&&j()}},async ondata(o){var c;const t=Date.now();P.value&&Ue(o),o.powerLevel>=10&&(clearTimeout(_e.value),_.value=2,P.value=!0,ae.value=t,_e.value=setTimeout(()=>{oe.value=!0,clearTimeout(he.value),xe(),se()},2e3)),t-ae.value>=((c=r.value.digital)==null?void 0:c.idle_time)*1e3&&(P.value||(Qe(),se()))}}),{play:ge,pause:xe,audioPlaying:we}=Rt({onstart(){_.value=4,re.value&&(re.value=!1)},onstop(){b(2),B.value?j():b(1)},onerror(){b(1)}}),Ze=async o=>{ge(async()=>await be({type:2,record_id:o}),!1)},j=async()=>{M.value||(await ye(),Ge())},Ke=async()=>{if(_.value==4){xe(),j();return}_.value!=3&&(M.value?(B.value=!1,se(),b(1)):(B.value=!0,j()))},be=async o=>{try{const{url:t}=await $t(o,{password:S.value,authorization:w,identity:C.visitorId});return t}catch{return b(1),Promise.reject()}},G=y(""),re=y(!1),Qe=async()=>{if(!r.value.robot.is_digital||!r.value.digital.id||(G.value||(G.value=await be({type:3,record_id:r.value.robot.id})),!G.value))return Promise.reject();re.value=!0;const o=Date.now();h.value.push({type:2,typing:!1,content:r.value.digital.idle_reply,key:o}),await xt(),D(),ge(G.value,!1)};_t(_,o=>{switch(o){case 2:j()}}),T();const ie=T(),{width:ke,height:Re}=ze(),Ce=T(),Ee=T(),Te=async o=>new Promise((t,c)=>{const s=document.createElement("video");s.src=o,s.preload="auto",s.loop=!0,s.muted=!0,s.autoplay=!1,s.playsInline=!0,s.play(),s.addEventListener("loadedmetadata",m=>{s.width=s.videoWidth,s.height=s.videoHeight,t(s)}),s.addEventListener("error",m=>{c(m)}),s.addEventListener("play",m=>{Se()})}),Se=()=>{if(!ie.value)return;const o=ke.value*2,t=Re.value*2,c=ie.value.getContext("2d");if(!c)return;const s=_.value===4?Ee.value:Ce.value;if(!s)return;c.clearRect(0,0,o,t);const{videoHeight:m,videoWidth:i}=s;let n=0,R=0,$=i,Z=m;if(i/m>=o/t){const l=o*m/t;n=(i-l)/2,$=l}else{const l=t*i/o;R=(m-l)/2,Z=l}c.drawImage(s,n,R,$,Z,0,0,o,t),requestAnimationFrame(Se)},Le=async()=>{if(await ee(),I.value==2){Ce.value=await Te(r.value.digital.wide_stay_video),Ee.value=await Te(r.value.digital.wide_talk_video),B.value=!0;try{await ye(),j()}catch{b(1)}setTimeout(()=>{D()},100)}};yt(async()=>{await C.getFingerprint(),await O(),Le()});const Ye=()=>{p==null||p.removeEventListener("chat"),p==null||p.removeEventListener("close"),p==null||p.removeEventListener("error"),p==null||p.abort()};return gt(()=>{Ye()}),dt({title:r.value.name}),(o,t)=>{const c=et,s=ot,m=at,i=st,n=ut,R=tt,$=pt,Z=ft;return u(),x("div",null,[a("div",{class:"layout-bg",style:de({height:`${e(pe)=="Infinity"?"100vh":e(pe)+"px"}`})},[e(I)===1?(u(),x("div",{key:0,class:je(["h-full",{"p-main":!e(F).isMobile}])},[a("div",It,[a("div",Bt,[e(r).robot.image?(u(),x("img",{key:0,src:e(r).robot.image,class:"w-[40px] h-[40px] mr-[10px] flex-none rounded-full",alt:""},null,8,Ot)):g("",!0),a("div",null,[a("div",Dt,V(e(r).robot.name),1),a("div",Mt,V(e(r).robot.intro),1)])]),a("div",Nt,[f($,{class:"h-full",content:e(F).getChatConfig.watermark,font:{color:"rgba(0,0,0,0.06)",fontSize:12}},{default:v(()=>{var l,A;return[a("div",Ht,[a("div",qt,[a("div",Ft,[f(e(Be),{ref_key:"scrollbarRef",ref:J},{default:v(()=>[a("div",Wt,[a("div",{ref_key:"innerRef",ref:te},[e(r).robot.welcome_introducer?(u(),k(s,{key:0,class:"mb-[20px]",type:"left",avatar:`${e(r).robot.icons?e(r).robot.icons:e(r).robot.image}`,bg:"var(--el-bg-color)"},{default:v(()=>[f(c,{content:e(r).robot.welcome_introducer,onClickCustomLink:t[0]||(t[0]=d=>U(d,"link"))},null,8,["content"])]),_:1},8,["avatar"])):g("",!0),(u(!0),x(Ve,null,Ie(e(h),(d,N)=>(u(),x("div",{key:d.id+""+N,class:"mt-4"},[d.type==1?(u(),k(s,{key:0,type:"right",bg:"var(--el-color-primary)",color:"white",avatar:e(Oe)},{actions:v(()=>[a("div",Ut,[f(n,{link:"",type:"info",onClick:Xe=>e(Me)(d.content)},{icon:v(()=>[f(i,{name:"el-icon-CopyDocument"})]),default:v(()=>[t[4]||(t[4]=H(" 复制 "))]),_:2},1032,["onClick"])])]),default:v(()=>[f(m,{content:d.content},null,8,["content"])]),_:2},1032,["avatar"])):g("",!0),d.type==2?(u(),k(s,{key:1,type:"left",time:d.create_time,avatar:`${e(r).robot.icons?e(r).robot.icons:e(r).robot.image}`,bg:"var(--el-bg-color)"},{outer_actions:v(()=>[d.create_time?(u(),k(n,{key:0,class:"ml-[52px] mt-2",style:{"--el-button-border-color":"transparent","--el-color-info-light-8":"transparent"},type:d.is_feedback?"info":"primary",plain:!0,bg:"",size:"small",disabled:d.is_feedback,onClick:Xe=>Ne(d,N)},{default:v(()=>[H(V(d.is_feedback?"已反馈":"反馈"),1)]),_:2},1032,["type","disabled","onClick"])):g("",!0)]),default:v(()=>[f(m,{content:String(d.content),type:"html",typing:d.typing,"show-copy":"","show-context":!!e(r).robot.is_show_context,"show-quote":!!e(r).robot.is_show_quote,"show-voice":e(F).getIsVoiceOpen,context:d.context,quotes:d.quotes,images:d.images,files:d.files,videos:d.videos,"record-id":d.id,"record-type":2,channel:e(w),"user-id":e(C).visitorId},null,8,["content","typing","show-context","show-quote","show-voice","context","quotes","images","files","videos","record-id","channel","user-id"])]),_:2},1032,["time","avatar"])):g("",!0)]))),128))],512)])]),_:1},512)]),a("div",Jt,[f(R,{ref_key:"chatActionRef",ref:W,loading:e(z),menus:e(fe),"btn-color":"#f6f6f6",onEnter:U,onClear:me,onPause:t[1]||(t[1]=d=>{var N;return(N=e(p))==null?void 0:N.abort()})},null,8,["loading","menus"]),(l=e(r).robot)!=null&&l.copyright?(u(),x("div",Gt,V((A=e(r).robot)==null?void 0:A.copyright),1)):g("",!0)])])],512)]}),_:1},8,["content"])])])],2)):g("",!0),e(I)===2?(u(),x("div",Zt,[a("canvas",{ref_key:"canvasRef",ref:ie,id:"digital-canvas",width:e(ke)*2,height:e(Re)*2},null,8,Kt),H(" "+V(e(_))+" ",1),$e(a("div",Qt,t[5]||(t[5]=[a("img",{class:"w-[400px]",src:Ct,alt:""},null,-1)]),512),[[Ae,e(_)===0]]),$e(a("div",{class:"h-full",style:de({background:e(r).robot.digital_bg})},[a("div",Yt,[a("div",Xt,[a("div",eo,[a("div",to,[a("div",oo,V(e(r).name),1)])]),t[6]||(t[6]=a("div",{class:"flex justify-center"},null,-1))]),a("div",ao,[a("div",so,[a("div",{class:je(["recorder gradient-button",{"recorder--stop":!e(M)&&!e(we)}]),onClick:Ke},[e(P)?(u(),x("canvas",{key:0,style:de({width:`${e(E).width}px`,height:`${e(E).height}px`}),width:e(E).width*e(E).scale,height:e(E).height*e(E).scale,id:e(E).id},null,12,ro)):g("",!0),e(M)&&!e(P)?(u(),k(i,{key:1,name:"el-icon-Microphone",size:40})):e(we)?(u(),k(i,{key:2,name:"local-icon-pause",size:40})):e(M)?g("",!0):(u(),k(i,{key:3,name:"el-icon-Mute",size:40}))],2),a("div",io,[a("div",null,V(e(We)[e(_)]),1)])]),a("div",no,[a("div",lo,[a("div",co,[e(h).length?(u(),k(e(Be),{key:0,ref_key:"scrollbarRef",ref:J},{default:v(()=>[a("div",uo,[a("div",{ref_key:"innerRef",ref:te},[(u(!0),x(Ve,null,Ie(e(h),(l,A)=>(u(),x("div",{key:l.id+""+A,class:"mt-4 sm:pb-[20px]"},[l.type==1?(u(),k(s,{key:0,type:"right",avatar:e(Oe),color:"white"},{default:v(()=>[f(m,{content:String(l.content)},null,8,["content"])]),_:2},1032,["avatar"])):g("",!0),l.type==2?(u(),k(s,{key:1,type:"left",time:l.create_time,avatar:e(r).robot.icons?e(r).robot.icons:e(r).robot.image,bg:"#fff"},{default:v(()=>[f(m,{content:String(l.content),type:"html",typing:l.typing,images:l.images,files:l.files,videos:l.videos,"record-id":l.id,"record-type":2},null,8,["content","typing","images","files","videos","record-id"])]),_:2},1032,["time","avatar"])):g("",!0)]))),128))],512)])]),_:1},512)):(u(),x("div",po," 暂无聊天记录 "))]),e(z)?(u(),x("div",fo,[f(n,{color:"#fff",round:"",onClick:t[2]||(t[2]=l=>{var A;return(A=e(p))==null?void 0:A.abort()})},{default:v(()=>t[7]||(t[7]=[H(" 停止 ")])),_:1})])):g("",!0)]),a("div",null,[f(R,{ref_key:"chatActionRef",ref:W,loading:[3,4].includes(e(_)),menus:e(fe),"show-pause":!1,"show-clear":!1,onEnter:U},null,8,["loading","menus"])])]),a("div",vo,[a("div",{class:"gradient-button",onClick:me},[f(i,{name:"local-icon-clear",size:24})])])])])],4),[[Ae,e(_)!==0]])])):g("",!0)],4),f(Et,{ref_key:"loginRef",ref:Q,onConfirm:qe},null,512),f(At,{ref_key:"popupRef",ref:Y,async:!0,width:"390",title:"问题反馈",appendToBody:!1,class:"feedback-pop"},{footer:v(()=>[a("div",mo,[f(n,{type:"primary",onClick:He},{default:v(()=>t[8]||(t[8]=[H(" 提交反馈 ")])),_:1})])]),default:v(()=>[f(Z,{modelValue:e(L).content,"onUpdate:modelValue":t[3]||(t[3]=l=>e(L).content=l),rows:"8",placeholder:"描述一下你遇到了什么问题"},null,8,["modelValue"])]),_:1},512)])}}}),ya=Vt(ho,[["__scopeId","data-v-b030a425"]]);export{ya as default};
