import{DrawingTool as O}from"./BWdDF8rn.js";import{r as M,s as X,b as y,k as Y,m as U}from"./CUZG7cWw.js";function H(f){const S=M({width:null,height:null,name:"Stage",children:[]}),h={RECT:"rect",LASSO:"lasso"},e=X({canvas:null,ctx:null,lineType:h.LASSO,stepIndex:-1,stepList:[],stepAttr:S}),l=y(new Image),u=new O,w=y([]);let c=[],g=!1;const T=(t,s)=>{const o=document.getElementById(t);o&&(e.canvas=o,e.ctx=o.getContext("2d"),o.style.cursor="crosshair",o.style.pointerEvents="none",e.ctx&&(e.ctx.fillStyle="white",e.ctx.fillRect(0,0,o.width,o.height),e.ctx.font="20px Arial",e.ctx.fillStyle="black",e.ctx.fillText("Loading...",o.width/2-40,o.height/2),e.ctx.imageSmoothingEnabled=!0,e.ctx.imageSmoothingQuality="high",l.value.onload=()=>{var m;o.style.pointerEvents="auto";const a=l.value.width/l.value.height;let i,n;a>o.width/o.height?(i=o.width,n=o.width/a):(i=o.height*a,n=o.height);const r=(o.width-i)/2,d=(o.height-n)/2;(m=e.ctx)==null||m.drawImage(l.value,r,d,i,n),u.init(e),u.saveCurrentState()},l.value.onerror=a=>{var i,n;(i=e.ctx)==null||i.clearRect(0,0,o.width,o.height),(n=e.ctx)==null||n.fillText("Image load failed",o.width/2-40,o.height/2)},l.value.setAttribute("crossOrigin",""),l.value.setAttribute("src",s)))},C=t=>{e.lineType=h[t]},p=(...t)=>{const{x:s,y:o,w:a,h:i,color:n,move:r,closed:d}=t[0];switch(e.lineType){case h.RECT:u.drawRect({x:s,y:o,w:a-s,h:i-o,color:n});break;case h.LASSO:r&&c.push(a,i),u.drawLasso(c,n,2,d);break}},L=t=>{var s;g=!0,(s=e.ctx)==null||s.beginPath(),p({x:t.offsetX,y:t.offsetY,color:"#11bdf7"}),c=[t.offsetX,t.offsetY]},A=t=>{g&&p({x:c[0],y:c[1],w:t.offsetX,h:t.offsetY,color:"#11bdf7",move:!0})},E=t=>{var s;if(!c.length||c[0]===t.offsetX&&c[1]===t.offsetY){c=[],g=!1;return}p({x:c[0],y:c[1],w:t.offsetX,h:t.offsetY,closed:!0}),R({x:c[0],y:c[1],w:t.offsetX,h:t.offsetY}),(s=e.ctx)==null||s.closePath(),u.saveCurrentState(),g=!1},R=t=>{const{x:s,y:o,w:a,h:i}=t;switch(e.lineType){case h.RECT:I({x:s,y:o,w:a-s,h:i-o});break;case h.LASSO:P(c);break}c=[]},v=t=>{w.value.push(t)},I=({x:t,y:s,w:o,h:a})=>{v({type:h.RECT,x:t,y:s,w:o,h:a})},P=t=>{v({type:h.LASSO,points:[...t]})},b=()=>{var o;if(!l.value)return;const t=document.createElement("canvas"),s=t.getContext("2d");t.width=l.value.width,t.height=l.value.height,s.fillStyle="black",s.fillRect(0,0,t.width,t.height),s.fillStyle="white",w.value.forEach(a=>{if(a.type===h.LASSO){const i=a.points.map((n,r)=>r%2===0?n/e.canvas.width*l.value.width:n/e.canvas.height*l.value.height);s.beginPath(),s.moveTo(i[0],i[1]);for(let n=2;n<i.length;n+=2)s.lineTo(i[n],i[n+1]);s.closePath(),s.fill()}else if(a.type===h.RECT){const i=a.x/e.canvas.width*l.value.width,n=a.y/e.canvas.height*l.value.height,r=a.w/e.canvas.width*l.value.width,d=a.h/e.canvas.height*l.value.height;s.beginPath(),s.rect(i,n,r,d),s.closePath(),s.fill()}}),(o=f==null?void 0:f.onData)==null||o.call(f,t.toDataURL())},D=()=>{e.stepIndex<=0||(e.stepIndex--,u.reapplyState(),e.stepList.pop(),w.value.pop())},x=()=>{c=[],w.value=[]};return window.addEventListener("focus",()=>{var t,s;(t=e.canvas)==null||t.focus(),(s=e.ctx)==null||s.putImageData(e.stepList[e.stepIndex],0,0)}),Y(x),{initCanvas:T,currentTool:U(()=>e.lineType),changeTool:C,onMouseDown:L,onMouseMove:A,onMouseUp:E,undo:D,clearState:x,captureCombinedSelections:b}}export{H as useImageEditor};
