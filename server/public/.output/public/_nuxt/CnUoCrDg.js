import{E as f}from"./D5Svi-lq.js";import{_ as y}from"./D3znQkH1.js";import{l as x,a as v}from"./CmRxzTqw.js";import{_ as b}from"./BfrBixNE.js";import g from"./CtxqhU8e.js";import{_ as h}from"./HLh0o2jg.js";import{l as k,m as w,M as e,N as V,O as i,a6 as B,a3 as l,u as m,y as N,a1 as A,a0 as a,Z as s}from"./CUZG7cWw.js";import"./DKYoP2z-.js";import"./eFgaMLiC.js";import"./DlAUqK2U.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./CRNANWso.js";import"./Bu_nKEGp.js";import"./DrxPZuc-.js";import"./DYjlFFbo.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./CXDY_LVT.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";/* empty css        *//* empty css        *//* empty css        */import"./DjwCd26w.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./Bj_9-7Jh.js";import"./C9jirCEY.js";import"./llRQJmEG.js";import"./BZBRZdpQ.js";import"./-CaxLuW0.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./tONJIxwY.js";import"./GytdR_nJ.js";import"./DlmZcWvX.js";import"./DAOx25wS.js";import"./CoT3rpTv.js";import"./CfDE0MAs.js";import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";import"./CUKNHy7a.js";/* empty css        */import"./7Aafpuyn.js";import"./B5TkE_dZ.js";const C={class:"flex flex-col h-full bg-body rounded-lg"},E={class:"px-4 pt-4"},I={class:"flex-1 min-h-0"},Ft=k({__name:"index",setup(R){const n=x(),u=v(),p=[{label:"AI绘画",value:"draw"},{label:"AI音乐",value:"music"},{label:"AI视频",value:"video"}],t=w({get(){const o=p.find(r=>r.value===u.query.type);return(o==null?void 0:o.value)||"draw"},set(o){n.replace({path:"",query:{type:o}})}});return(o,r)=>{const c=f,d=y;return e(),V("div",C,[i("div",E,[i("div",{class:"bg-page p-[5px] rounded-[10px]",style:B({width:`${100*p.length}px`})},[l(c,{style:{"--el-border-radius-base":"10px"},modelValue:m(t),"onUpdate:modelValue":r[0]||(r[0]=_=>N(t)?t.value=_:null),class:"!bg-[transparent] h-[34px]",block:!0,options:p},null,8,["modelValue"])],4)]),i("div",I,[l(d,null,{default:A(()=>[m(t)==="music"?(e(),a(b,{key:0})):s("",!0)]),_:1}),m(t)==="draw"?(e(),a(g,{key:0})):s("",!0),m(t)==="video"?(e(),a(h,{key:1})):s("",!0)])])}}});export{Ft as default};
