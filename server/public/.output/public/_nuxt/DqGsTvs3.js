import{b as d}from"./CUZG7cWw.js";const l={};function h(T,p){const{key:t,time:D=2e3,totalTime:r,count:c,callback:s=()=>!1}=p??{};let n=null,o=null,u=0;const a=d(null),f=d(null),m=()=>{if(o&&o<=Date.now()){e(),s();return}if(c&&u>=c){e(),s();return}u++,n=setTimeout(()=>{T().then(i=>{a.value=i,m()}).catch(i=>{f.value=i})},D)},b=()=>{e(),t&&l[t]&&(l[t].end(),delete l[t]),o=r?Date.now()+r:null,m(),t&&(l[t]={end:e})},e=()=>{n&&setTimeout(()=>{clearTimeout(n),n=null,o=null,u=0,t&&delete l[t]},0)};return{start:b,end:e,error:f,result:a}}export{h as u};
