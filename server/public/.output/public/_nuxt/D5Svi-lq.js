import{I as R,V as J,aJ as q,W as H,ar as N,X as E,aK as k,J as W,at as j,Q as G,aL as K,aM as Q,aN as U,aO as Z,Z as Y,aP as ee,M as se,N as ae}from"./CmRxzTqw.js";import{H as V,l as B,b as te,r as le,m as u,c as C,M as g,N as v,O as m,a6 as ne,u as t,$ as r,_ as oe,ao as ie,V as re,ag as de,a5 as ce,J as h}from"./CUZG7cWw.js";const ue=R({options:{type:J(Array),default:()=>[]},modelValue:{type:[String,Number,Boolean],default:void 0},block:Boolean,size:q,disabled:Boolean,validateEvent:{type:Boolean,default:!0},id:String,name:String,...H(["ariaLabel"])}),me={[N]:i=>V(i)||E(i),[k]:i=>V(i)||E(i)},be=["id","aria-label","aria-labelledby"],fe=["name","disabled","checked","onChange"],pe=B({name:"ElSegmented"}),ge=B({...pe,props:ue,emits:me,setup(i,{emit:I}){const n=i,a=W("segmented"),w=j(),L=G(),z=K(),{formItem:d}=Q(),{inputId:P,isLabeledByFormItem:_}=U(n,{formItemContext:d}),c=te(null),F=Z(),s=le({isInit:!1,width:0,translateX:0,disabled:!1,focusVisible:!1}),O=e=>{const l=b(e);I(N,l),I(k,l)},b=e=>h(e)?e.value:e,X=e=>h(e)?e.label:e,f=e=>!!(z.value||h(e)&&e.disabled),y=e=>n.modelValue===b(e),A=e=>n.options.find(l=>b(l)===e),D=e=>[a.e("item"),a.is("selected",y(e)),a.is("disabled",f(e))],p=()=>{if(!c.value)return;const e=c.value.querySelector(".is-selected"),l=c.value.querySelector(".is-selected input");if(!e||!l){s.width=0,s.translateX=0,s.disabled=!1,s.focusVisible=!1;return}const o=e.getBoundingClientRect();s.isInit=!0,s.width=o.width,s.translateX=e.offsetLeft,s.disabled=f(A(n.modelValue));try{s.focusVisible=l.matches(":focus-visible")}catch{}},T=u(()=>[a.b(),a.m(L.value),a.is("block",n.block)]),$=u(()=>({width:`${s.width}px`,transform:`translateX(${s.translateX}px)`,display:s.isInit?"block":"none"})),x=u(()=>[a.e("item-selected"),a.is("disabled",s.disabled),a.is("focus-visible",s.focusVisible)]),M=u(()=>n.name||w.value);return Y(c,p),C(F,p),C(()=>n.modelValue,()=>{var e;p(),n.validateEvent&&((e=d==null?void 0:d.validate)==null||e.call(d,"change").catch(l=>ee()))},{flush:"post"}),(e,l)=>(g(),v("div",{id:t(P),ref_key:"segmentedRef",ref:c,class:r(t(T)),role:"radiogroup","aria-label":t(_)?void 0:e.ariaLabel||"segmented","aria-labelledby":t(_)?t(d).labelId:void 0},[m("div",{class:r(t(a).e("group"))},[m("div",{style:ne(t($)),class:r(t(x))},null,6),(g(!0),v(oe,null,ie(e.options,(o,S)=>(g(),v("label",{key:S,class:r(D(o))},[m("input",{class:r(t(a).e("item-input")),type:"radio",name:t(M),disabled:f(o),checked:y(o),onChange:he=>O(o)},null,42,fe),m("div",{class:r(t(a).e("item-label"))},[re(e.$slots,"default",{item:o},()=>[de(ce(X(o)),1)])],2)],2))),128))],2)],10,be))}});var ve=se(ge,[["__file","segmented.vue"]]);const ye=ae(ve);export{ye as E};
