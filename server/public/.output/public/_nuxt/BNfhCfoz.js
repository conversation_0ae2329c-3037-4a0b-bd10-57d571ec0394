import{E as B}from"./BCqAdQ5e.js";import{_ as D}from"./eFgaMLiC.js";import{E as L}from"./oVx59syQ.js";import{E as M}from"./llRQJmEG.js";import{f as N}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{u as S}from"./DNOp0HuO.js";import{b as j,e as A}from"./DQUFgXGm.js";import{c as H}from"./BluXXrgj.js";import{l as P,ai as R,M as a,N as n,O as e,a3 as l,a1 as x,u as c,a0 as V,_ as u,ao as h,a5 as p}from"./CUZG7cWw.js";import{_ as F}from"./DlAUqK2U.js";import"./YwtsEmdS.js";const G={class:"h-full p-[15px] rounded-[12px]"},I={class:"h-full flex flex-col"},O={class:"flex justify-between items-center"},Q={class:"flex-1 min-h-0 pt-[10px] mx-[-10px]"},U={class:"flex flex-wrap items-stretch"},q={class:"p-[15px] bg-page rounded-[10px] h-full record-item hover:bg-primary-light-9"},z={class:"text-lg font-medium line-clamp-1"},J=["onClick"],K={class:"whitespace-pre-line line-clamp-5 my-[10px] h-[105px]"},T={class:"flex items-center"},W={class:"mr-auto text-tx-secondary text-sm"},X=["onClick"],Y={key:1,class:"h-full flex flex-col items-center justify-center"},Z=P({__name:"history-all",emits:["view","history"],async setup(ee,{emit:y}){let r,d;const m=y,{data:i,refresh:v}=([r,d]=R(()=>S(()=>j({type:4,page_type:0}),{transform(o){return o.lists},default(){return[]}},"$ffgCUBQ1Gx")),r=await r,d(),r),g=(o,t)=>{m("view",{id:o,text:t})},_=async o=>{i.value.length&&(await N.confirm(`确定${o?"删除":"清空"}记录？`),await A({type:4,id:o}),v())};return(o,t)=>{const k=B,w=D,C=L,b=M;return a(),n("div",G,[e("div",I,[e("div",O,[l(k,{onBack:t[0]||(t[0]=s=>m("history"))},{content:x(()=>t[2]||(t[2]=[e("span",{class:"text-xl font-medium"}," 生成记录 ",-1)])),_:1}),e("div",{class:"bg-page-base text-sm px-[15px] py-[5px] rounded-[4px] cursor-pointer ml-[10px]",onClick:t[1]||(t[1]=s=>_())}," 清空记录 ")]),e("div",Q,[c(i).length?(a(),V(C,{key:0},{default:x(()=>[e("div",U,[(a(!0),n(u,null,h(c(i),s=>(a(),n("div",{class:"2xl:w-1/4 xl:w-1/3 w-1/2 p-[8px]",key:s.id},[e("div",q,[e("div",z," 帮我生成："+p(s.ask),1),(a(!0),n(u,null,h(s.reply,(f,E)=>(a(),n("div",{class:"cursor-pointer",key:E,onClick:$=>g(s.id,f)},[e("div",K,p(f),1),e("div",T,[e("div",W,p(s.create_time),1),e("div",{class:"cursor-pointer text-tx-secondary flex",onClick:$=>_(s.id)},[l(w,{name:"el-icon-Delete"})],8,X)])],8,J))),128))])]))),128))])]),_:1})):(a(),n("div",Y,[l(b,{image:c(H)},null,8,["image"])]))])])])}}}),fe=F(Z,[["__scopeId","data-v-6a4d0ce4"]]);export{fe as default};
