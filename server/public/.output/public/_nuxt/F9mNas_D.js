import{E as y}from"./oVx59syQ.js";import{a as R,E as w}from"./DHUC3PVh.js";import{f as k,E as q}from"./CmRxzTqw.js";import{a as I}from"./B7GaOiDz.js";/* empty css        *//* empty css        */import{_ as N}from"./BjmMA-ez.js";import{_ as h}from"./zeSNwnEI.js";import{_ as j}from"./CqEm55x_.js";import{_ as A}from"./ng09i8Gy.js";import{u as B}from"./qRM0tN96.js";import{c as C}from"./Bs9Zhtqd.js";import{l as U,b as $,m as O,w as S,j as T,s as D,u as i,M as F,a0 as M,a1 as r,Z as P,O as f,a3 as o,y as p,ag as Z}from"./CUZG7cWw.js";import{_ as z}from"./DlAUqK2U.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./Dhda0m3Y.js";import"./eFgaMLiC.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./D8e5izeA.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./DCzKTodP.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./C9f7n97H.js";import"./DYjlFFbo.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import"./DIUf2-0l.js";import"./Dl64kDm5.js";import"./BXBQD0li.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";import"./DB7Ysqj9.js";import"./Bf_xRNbS.js";import"./BOx_5T3X.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./DdtGP7XX.js";/* empty css        */import"./sfCUuwOk.js";import"./HA5sEeDs.js";/* empty css        *//* empty css        */import"./CdCxqKUj.js";import"./D-n7HwjM.js";import"./ttFW0yUc.js";import"./CbQsrhNE.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./BscXL5XZ.js";/* empty css        */import"./C6_W4ts7.js";const G={class:"flex-1 min-h-0"},H={class:"my-[15px] flex justify-center"},J=U({__name:"index",props:{modelValue:{}},emits:["success"],setup(_,{emit:c}){const b=_,g=c,e=$({}),V=O(()=>!!Object.keys(e.value).length);S(()=>{e.value=C(b.modelValue)});const u=T(),n=D({image:[{required:!0,type:"string",message:"请选择应用图标"}],name:[{required:!0,message:"请输入应用名称"}],model_id:[{required:!0,message:"请选择AI通道",trigger:["blur"]}],model_sub_id:[{required:!0,message:"请选择AI模型",trigger:["blur"]}],cate_id:[{required:!0,message:"请选择分类",trigger:["blur"]}],digital_id:[{required:!0,message:"请选择形象",trigger:["change"]},{validator(s,t,m){Number(t)===0&&m(new Error("请选择形象")),m()}}]}),v=async()=>{var s,t;try{await((s=u.value)==null?void 0:s.validate()),await B(e.value),g("success")}catch(m){for(const a in m){Object.keys(n).includes(a)&&k.msgError((t=m[a][0])==null?void 0:t.message);break}}};return(s,t)=>{const m=y,a=R,d=w,E=q,x=I;return i(V)?(F(),M(x,{key:0,ref_key:"formRef",ref:u,model:i(e),"label-width":"140px",rules:i(n),class:"app-edit flex flex-col"},{default:r(()=>[f("div",G,[o(d,{"model-value":"base"},{default:r(()=>[o(a,{label:"基本配置",name:"base"},{default:r(()=>[o(m,null,{default:r(()=>[o(N,{modelValue:i(e),"onUpdate:modelValue":t[0]||(t[0]=l=>p(e)?e.value=l:null)},null,8,["modelValue"])]),_:1})]),_:1}),o(a,{label:"AI模型/搜索配置",name:"search"},{default:r(()=>[o(m,null,{default:r(()=>[o(h,{modelValue:i(e),"onUpdate:modelValue":t[1]||(t[1]=l=>p(e)?e.value=l:null)},null,8,["modelValue"])]),_:1})]),_:1}),o(a,{label:"界面配置",name:"interface"},{default:r(()=>[o(m,null,{default:r(()=>[o(j,{modelValue:i(e),"onUpdate:modelValue":t[2]||(t[2]=l=>p(e)?e.value=l:null)},null,8,["modelValue"])]),_:1})]),_:1}),o(a,{label:"形象配置",name:"digital"},{default:r(()=>[o(m,null,{default:r(()=>[o(A,{modelValue:i(e),"onUpdate:modelValue":t[3]||(t[3]=l=>p(e)?e.value=l:null)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),f("div",H,[o(E,{type:"primary",onClick:v},{default:r(()=>t[4]||(t[4]=[Z(" 保存")])),_:1})])]),_:1},8,["model","rules"])):P("",!0)}}}),pt=z(J,[["__scopeId","data-v-046fb527"]]);export{pt as default};
