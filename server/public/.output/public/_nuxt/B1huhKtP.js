import{l as R,Y as M,m as b,b as E,M as o,a0 as O,a3 as l,a1 as f,a7 as V,u as e,O as A,W as q,a4 as U,$ as t,N as i,V as c,a5 as Y,Z as r,a8 as J,a9 as W,as as Z}from"./CUZG7cWw.js";import{I as j,am as G,J as K,a5 as Q,Y as X,an as x,ao as ee,H as ae,R as se,M as te,N as oe}from"./CmRxzTqw.js";import{b as re,c as le,u as ie}from"./CDwN27aR.js";const de=j({...re,direction:{type:String,default:"rtl",values:["ltr","rtl","ttb","btt"]},size:{type:[String,Number],default:"30%"},withHeader:{type:Boolean,default:!0},modalFade:{type:Boolean,default:!0},headerAriaLevel:{type:String,default:"2"}}),ne=le,ue=["aria-label","aria-labelledby","aria-describedby"],fe=["id","aria-level"],ce=["aria-label"],pe=["id"],me=R({name:"ElDrawer",inheritAttrs:!1}),ve=R({...me,props:de,emits:ne,setup(g,{expose:S}){const d=g,F=M();G({scope:"el-drawer",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/drawer.html#slots"},b(()=>!!F.title));const p=E(),y=E(),s=K("drawer"),{t:L}=Q(),{afterEnter:h,afterLeave:w,beforeLeave:B,visible:m,rendered:D,titleId:v,bodyId:k,zIndex:$,onModalClick:z,onOpenAutoFocus:P,onCloseAutoFocus:T,onFocusoutPrevented:I,onCloseRequested:N,handleClose:n}=ie(d,p),_=b(()=>d.direction==="rtl"||d.direction==="ltr"),C=b(()=>X(d.size));return S({handleClose:n,afterEnter:h,afterLeave:w}),(a,u)=>(o(),O(Z,{to:"body",disabled:!a.appendToBody},[l(W,{name:e(s).b("fade"),onAfterEnter:e(h),onAfterLeave:e(w),onBeforeLeave:e(B),persisted:""},{default:f(()=>[V(l(e(x),{mask:a.modal,"overlay-class":a.modalClass,"z-index":e($),onClick:e(z)},{default:f(()=>[l(e(ee),{loop:"",trapped:e(m),"focus-trap-el":p.value,"focus-start-el":y.value,onFocusAfterTrapped:e(P),onFocusAfterReleased:e(T),onFocusoutPrevented:e(I),onReleaseRequested:e(N)},{default:f(()=>[A("div",q({ref_key:"drawerRef",ref:p,"aria-modal":"true","aria-label":a.title||void 0,"aria-labelledby":a.title?void 0:e(v),"aria-describedby":e(k)},a.$attrs,{class:[e(s).b(),a.direction,e(m)&&"open"],style:e(_)?"width: "+e(C):"height: "+e(C),role:"dialog",onClick:u[1]||(u[1]=U(()=>{},["stop"]))}),[A("span",{ref_key:"focusStartRef",ref:y,class:t(e(s).e("sr-focus")),tabindex:"-1"},null,2),a.withHeader?(o(),i("header",{key:0,class:t(e(s).e("header"))},[a.$slots.title?c(a.$slots,"title",{key:1},()=>[r(" DEPRECATED SLOT ")]):c(a.$slots,"header",{key:0,close:e(n),titleId:e(v),titleClass:e(s).e("title")},()=>[a.$slots.title?r("v-if",!0):(o(),i("span",{key:0,id:e(v),role:"heading","aria-level":a.headerAriaLevel,class:t(e(s).e("title"))},Y(a.title),11,fe))]),a.showClose?(o(),i("button",{key:2,"aria-label":e(L)("el.drawer.close"),class:t(e(s).e("close-btn")),type:"button",onClick:u[0]||(u[0]=(...H)=>e(n)&&e(n)(...H))},[l(e(ae),{class:t(e(s).e("close"))},{default:f(()=>[l(e(se))]),_:1},8,["class"])],10,ce)):r("v-if",!0)],2)):r("v-if",!0),e(D)?(o(),i("div",{key:1,id:e(k),class:t(e(s).e("body"))},[c(a.$slots,"default")],10,pe)):r("v-if",!0),a.$slots.footer?(o(),i("div",{key:2,class:t(e(s).e("footer"))},[c(a.$slots,"footer")],2)):r("v-if",!0)],16,ue)]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])]),_:3},8,["mask","overlay-class","z-index","onClick"]),[[J,e(m)]])]),_:3},8,["name","onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["disabled"]))}});var be=te(ve,[["__file","drawer.vue"]]);const ke=oe(be);export{ke as E};
