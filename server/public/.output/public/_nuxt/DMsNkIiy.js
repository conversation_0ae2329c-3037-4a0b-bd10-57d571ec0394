import{E as d,a as h,b as g,c as w}from"./BCtbxh46.js";import{j as v,cD as C,a as b,T as y,cX as D,f as E}from"./CmRxzTqw.js";import{u as x,a as S}from"./DymDsCmz.js";import{_ as L}from"./DuT8liHz.js";import k from"./DFjF4P0T.js";import{_ as B}from"./g1O7ZX_R.js";import R from"./BwSvjoul.js";import{l as T,c as r,M as $,N as z,a3 as t,a1 as i,a6 as H,u as M}from"./CUZG7cWw.js";import"./DoCT-qbH.js";import"./CiYvFM4x.js";import"./DY7CbrCZ.js";import"./Bfmn7p7A.js";import"./eFgaMLiC.js";import"./DlAUqK2U.js";import"./BCqAdQ5e.js";import"./YwtsEmdS.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./CbQsrhNE.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        *//* empty css        *//* empty css        */import"./Bj_9-7Jh.js";import"./DHUC3PVh.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./Btsr68UI.js";import"./llRQJmEG.js";import"./oVx59syQ.js";/* empty css        */import"./DNOp0HuO.js";import"./BhXe-NXN.js";import"./BLeEUk17.js";import"./C9f7n97H.js";import"./Bv29pan0.js";/* empty css        */import"./DwB10QAP.js";import"./_IsPce8C.js";import"./Bxy8Ff7l.js";import"./B2IX6jAp.js";import"./B7GaOiDz.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";/* empty css        */import"./Cw-OnHz-.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./DCzKTodP.js";import"./D8e5izeA.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./CXsrG8JM.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./7h9ARPvN.js";import"./DdtGP7XX.js";import"./Bs9Zhtqd.js";import"./CpBm7YaS.js";import"./ttFW0yUc.js";import"./DqxK3r1f.js";import"./Dn5Z8lNl.js";import"./zzCq5IAa.js";import"./BbnoZrPC.js";import"./CWu5I6V-.js";import"./BvSuqySp.js";import"./l0sNRNKZ.js";import"./C2aUBVXw.js";import"./BejSeAE6.js";import"./DNRqakyH.js";import"./DikNcrXK.js";/* empty css        */import"./D8_C1Kwf.js";const lo=T({__name:"design",setup(N){const n=v(),o=x(),{height:c}=C(),u=b(),{initTabs:l}=S();y(window,"beforeunload",e=>{o.isChangeData&&(e.preventDefault(),e.returnValue="内容已修改，确认离开页面吗？")}),D(async(e,f)=>{try{o.isChangeData&&n.isLogin&&await E.confirm("内容已修改，确认离开页面吗？")}catch{return!1}});const m=()=>{o.isChangeData=!0};return r(()=>o.canvasJson,m),r(()=>o.music,m),r(()=>o.dub,m),r(()=>o.voiceContent,m,{deep:!0}),r(()=>u.query,()=>{l()}),(e,f)=>{const p=h,a=g,_=w,s=d;return $(),z("div",{class:"overflow-x-auto",style:H({height:`${M(c)}px`})},[t(s,{class:"bg-page !min-w-[1200px] h-full"},{default:i(()=>[t(p,{height:"auto",style:{padding:"0"}},{default:i(()=>[t(L)]),_:1}),t(s,{class:"min-h-0"},{default:i(()=>[t(a,{width:"auto",style:{overflow:"visible"}},{default:i(()=>[t(k)]),_:1}),t(_,{style:{padding:"0"}},{default:i(()=>[t(B)]),_:1}),t(a,{width:"auto"},{default:i(()=>[t(R)]),_:1})]),_:1})]),_:1})],4)}}});export{lo as default};
