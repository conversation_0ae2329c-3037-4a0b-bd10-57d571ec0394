import{bO as y,bP as l,bj as _,bQ as N,bR as F,b8 as S,bS as v,a0 as C,bT as u,bU as E,k as R,bV as K,bb as W,bW as Y,bX as Q,bc as V,$ as X,bY as q}from"./CmRxzTqw.js";import{k as B,b as U,s as H,c as J,a as j,g as Z}from"./gj6kus5n.js";function z(e,r){for(var n=-1,s=e==null?0:e.length;++n<s&&r(e[n],n,e)!==!1;);return e}function k(e,r){return e&&y(r,B(r),e)}function ee(e,r){return e&&y(r,l(r),e)}function re(e,r){return y(e,U(e),r)}var te=Object.getOwnPropertySymbols,x=te?function(e){for(var r=[];e;)_(r,U(e)),e=N(e);return r}:H;function ne(e,r){return y(e,x(e),r)}function ae(e){return J(e,l,x)}var oe=Object.prototype,se=oe.hasOwnProperty;function ce(e){var r=e.length,n=new e.constructor(r);return r&&typeof e[0]=="string"&&se.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function ie(e,r){var n=r?F(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}var be=/\w*$/;function ge(e){var r=new e.constructor(e.source,be.exec(e));return r.lastIndex=e.lastIndex,r}var O=S?S.prototype:void 0,w=O?O.valueOf:void 0;function fe(e){return w?Object(w.call(e)):{}}var ue="[object Boolean]",ye="[object Date]",Te="[object Map]",le="[object Number]",je="[object RegExp]",pe="[object Set]",Ae="[object String]",de="[object Symbol]",$e="[object ArrayBuffer]",me="[object DataView]",Se="[object Float32Array]",Oe="[object Float64Array]",we="[object Int8Array]",Ie="[object Int16Array]",he="[object Int32Array]",Fe="[object Uint8Array]",Ce="[object Uint8ClampedArray]",Ee="[object Uint16Array]",Be="[object Uint32Array]";function Ue(e,r,n){var s=e.constructor;switch(r){case $e:return F(e);case ue:case ye:return new s(+e);case me:return ie(e,n);case Se:case Oe:case we:case Ie:case he:case Fe:case Ce:case Ee:case Be:return v(e,n);case Te:return new s;case le:case Ae:return new s(e);case je:return ge(e);case pe:return new s;case de:return fe(e)}}var xe="[object Map]";function Le(e){return C(e)&&j(e)==xe}var I=u&&u.isMap,Me=I?E(I):Le,Pe="[object Set]";function De(e){return C(e)&&j(e)==Pe}var h=u&&u.isSet,Ge=h?E(h):De,_e=1,Ne=2,ve=4,L="[object Arguments]",Re="[object Array]",Ke="[object Boolean]",We="[object Date]",Ye="[object Error]",M="[object Function]",Qe="[object GeneratorFunction]",Ve="[object Map]",Xe="[object Number]",P="[object Object]",qe="[object RegExp]",He="[object Set]",Je="[object String]",Ze="[object Symbol]",ze="[object WeakMap]",ke="[object ArrayBuffer]",er="[object DataView]",rr="[object Float32Array]",tr="[object Float64Array]",nr="[object Int8Array]",ar="[object Int16Array]",or="[object Int32Array]",sr="[object Uint8Array]",cr="[object Uint8ClampedArray]",ir="[object Uint16Array]",br="[object Uint32Array]",t={};t[L]=t[Re]=t[ke]=t[er]=t[Ke]=t[We]=t[rr]=t[tr]=t[nr]=t[ar]=t[or]=t[Ve]=t[Xe]=t[P]=t[qe]=t[He]=t[Je]=t[Ze]=t[sr]=t[cr]=t[ir]=t[br]=!0;t[Ye]=t[M]=t[ze]=!1;function T(e,r,n,s,p,c){var a,g=r&_e,f=r&Ne,D=r&ve;if(a!==void 0)return a;if(!R(e))return e;var A=X(e);if(A){if(a=ce(e),!g)return K(e,a)}else{var b=j(e),d=b==M||b==Qe;if(W(e))return Y(e,g);if(b==P||b==L||d&&!p){if(a=f||d?{}:Q(e),!g)return f?ne(e,ee(a,e)):re(e,k(a,e))}else{if(!t[b])return p?e:{};a=Ue(e,b,g)}}c||(c=new V);var $=c.get(e);if($)return $;c.set(e,a),Ge(e)?e.forEach(function(o){a.add(T(o,r,n,o,e,c))}):Me(e)&&e.forEach(function(o,i){a.set(i,T(o,r,n,i,e,c))});var G=D?f?ae:Z:f?l:B,m=A?void 0:G(e);return z(m||e,function(o,i){m&&(i=o,o=e[i]),q(a,i,T(o,r,n,i,e,c))}),a}export{T as b};
