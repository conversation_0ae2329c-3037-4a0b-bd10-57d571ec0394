import{b as _,j as u,E as f}from"./CmRxzTqw.js";import{c as b,a as g,f as k,b as n,d as a}from"./-CaxLuW0.js";import{l as y,M as t,N as s,a3 as h,a1 as x,u as e,O as w,Z as r,a5 as i}from"./CUZG7cWw.js";const B={class:"absolute bottom-0 left-0 bg-body p-4 w-full z-10"},S={key:0},v={key:1},C={key:0,class:"text-sm ml-2"},N={key:1,class:"text-sm ml-2"},D=y({__name:"create-button",props:{disabled:{type:Boolean,default:!1}},emits:["create"],setup(l,{emit:E}){const c=_(),d=u(),m=()=>{b()||g(k.value)};return(L,o)=>{const p=f;return t(),s("div",B,[h(p,{size:"large",type:"primary",class:"w-full",disabled:l.disabled,onClick:m,loading:e(n)},{default:x(()=>[e(n)?(t(),s("div",S,"正在请求中")):(t(),s("div",v,[o[0]||(o[0]=w("span",{class:"text-base font-bold"},"立即生成",-1)),e(a).is_member?(t(),s("span",C,"会员免费")):r("",!0),e(a).power!=0&&!e(a).is_member&&e(d).isLogin?(t(),s("span",N,"消耗"+i(e(a).power||"--")+i(e(c).getTokenUnit),1)):r("",!0)]))]),_:1},8,["disabled","loading"])])}}});export{D as _};
