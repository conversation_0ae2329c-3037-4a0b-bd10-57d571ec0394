import{a as B,l as I,f as C,e as F,E as N}from"./CmRxzTqw.js";import{E as z,a as D}from"./B7GaOiDz.js";import{_ as M}from"./eFgaMLiC.js";import{E as Q}from"./C7tIPmrK.js";import{_ as S}from"./DB7Ysqj9.js";import{_ as q}from"./Dhda0m3Y.js";import{E as A}from"./oVx59syQ.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{k as K,c as O}from"./Dl64kDm5.js";import{l as P,r as R,c as j,M as T,N as $,a3 as o,a1 as l,u as i,O as n,ag as f}from"./CUZG7cWw.js";import{_ as G}from"./DlAUqK2U.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";import"./CXDY_LVT.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./Bf_xRNbS.js";import"./BOx_5T3X.js";/* empty css        *//* empty css        */import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./DdtGP7XX.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";const H={class:"h-full"},J={class:"flex items-center cursor-pointer text-[#666]"},L={class:"flex items-center cursor-pointer text-[#666]"},W=P({__name:"base-setting",props:{data:{default:()=>({})}},emits:["update"],setup(b,{emit:g}){const w=b,E=g,m=B(),v=I(),t=R({name:"",image:"",intro:"",documents_model_id:"",documents_model_sub_id:"",embedding_model_id:"",is_owner:1});j(()=>w.data,r=>{Object.assign(t,r)});const x=async()=>{await K({...t,id:m.query.id}),E("update")},V=async()=>{await C.confirm("确认删除吗？"),await O({id:m.query.id}),v.push("/application/layout/kb")};return(r,e)=>{const d=F,a=z,p=M,u=Q,_=S,k=q,c=N,y=D,U=A;return T(),$("div",H,[o(U,null,{default:l(()=>[o(y,{"label-position":"top",class:"setup-form"},{default:l(()=>[o(a,{label:"知识库名称"},{default:l(()=>[o(d,{placeholder:"知识库名称",modelValue:i(t).name,"onUpdate:modelValue":e[0]||(e[0]=s=>i(t).name=s)},null,8,["modelValue"])]),_:1}),o(a,{label:"简介"},{default:l(()=>[o(d,{type:"textarea",placeholder:"知识库名称",rows:5,resize:"none",modelValue:i(t).intro,"onUpdate:modelValue":e[1]||(e[1]=s=>i(t).intro=s)},null,8,["modelValue"])]),_:1}),o(a,{label:"向量模型"},{label:l(()=>[o(u,{placement:"right",width:300,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"向量模型可以将自然语言转成向量(即数据训练), 用于进行语义检索, 注意: 不同向量模型无法一起使用, 选择完后将无法修改。"},{reference:l(()=>[n("div",J,[e[6]||(e[6]=n("span",{class:"mr-1"},"向量模型",-1)),o(p,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),default:l(()=>[o(_,{class:"flex-1",id:i(t).embedding_model_id,"onUpdate:id":e[2]||(e[2]=s=>i(t).embedding_model_id=s),"set-default":!1,type:"vectorModels",disabled:!0},null,8,["id"])]),_:1}),o(a,{label:"文件处理模型"},{label:l(()=>[o(u,{placement:"right",width:300,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"文件模型用于QA拆分功能(导入数据->自动拆分问答对), 利用该AI模型对导入的文本进行处理，最终拆分成一问一答的数据形式。"},{reference:l(()=>[n("div",L,[e[7]||(e[7]=n("span",{class:"mr-1"},"文件处理模型",-1)),o(p,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),default:l(()=>[o(_,{class:"flex-1",id:i(t).documents_model_id,"onUpdate:id":e[3]||(e[3]=s=>i(t).documents_model_id=s),sub_id:i(t).documents_model_sub_id,"onUpdate:sub_id":e[4]||(e[4]=s=>i(t).documents_model_sub_id=s),"set-default":!1},null,8,["id","sub_id"])]),_:1}),o(a,{label:"设置封面"},{default:l(()=>[o(k,{modelValue:i(t).image,"onUpdate:modelValue":e[5]||(e[5]=s=>i(t).image=s)},null,8,["modelValue"])]),_:1}),o(a,null,{default:l(()=>[o(c,{type:"primary",disabled:r.data.power>=2,onClick:x},{default:l(()=>e[8]||(e[8]=[f(" 保存设置 ")])),_:1},8,["disabled"]),o(c,{onClick:V,disabled:r.data.power!==1},{default:l(()=>e[9]||(e[9]=[f(" 删除 ")])),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1})])}}}),Ae=G(W,[["__scopeId","data-v-50067880"]]);export{Ae as default};
