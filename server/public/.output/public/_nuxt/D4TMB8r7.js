import{E as y}from"./CiabO6Xq.js";import{b,E as w}from"./CmRxzTqw.js";import{_ as E}from"./mBG0LxMu.js";/* empty css        */import{_ as S}from"./Cv1u9LLW.js";import{l as k,m as j,M as e,N as o,O as t,_ as B,ao as I,u as l,a5 as p,a3 as a,a1 as _}from"./CUZG7cWw.js";import{_ as N}from"./DlAUqK2U.js";const O={class:"py-[40px]"},q={class:"min-[1200px]:w-[1200px] mx-auto"},z={class:"flex flex-col items-center"},A={class:"flex-1 my-[30px] px-[20px]"},C={class:"flex justify-center text-center"},D={class:"text-[24px] font-bold"},L={class:"mt-[20px] text-[16px]"},M={class:"flex-1 px-[20px]"},V={class:"flex justify-center"},F={class:"mt-[90px]"},P=k({__name:"intro",props:{prop:{}},setup(d){const m=d,f=b(),x=j(()=>m.prop.data.filter(n=>n.isShow));return(n,r)=>{const u=y,h=w,v=E;return e(),o("div",O,[t("div",q,[(e(!0),o(B,null,I(l(x),(s,g)=>{var c,i;return e(),o("div",{class:"mb-[100px]",key:g},[t("div",z,[t("div",A,[t("div",C,[t("div",null,[t("div",D,p(s.title),1),t("div",L,p(s.subtitle),1)])])]),t("div",M,[t("div",V,[a(u,{fit:"cover",src:l(f).getImageUrl(s.image)},null,8,["src"])])]),t("div",F,[a(v,{to:{path:(c=s.link)==null?void 0:c.path,query:(i=s.link)==null?void 0:i.query}},{default:_(()=>[a(h,{type:"primary",class:"enter-btn hover-to-right",size:"large"},{default:_(()=>r[0]||(r[0]=[t("div",{class:"flex justify-center items-center w-[50px] h-[50px] rounded-full bg-white"},[t("img",{src:S,class:"w-[24px] h-[24px]",alt:""})],-1),t("span",{class:"ml-4"},"马上体验",-1)])),_:1})]),_:2},1032,["to"])])])])}),128))])])}}}),T=N(P,[["__scopeId","data-v-5fa0cf3f"]]),R=Object.freeze(Object.defineProperty({__proto__:null,default:T},Symbol.toStringTag,{value:"Module"}));export{R as _};
