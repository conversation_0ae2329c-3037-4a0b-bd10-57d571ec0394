import{_ as S}from"./eFgaMLiC.js";import{E as U}from"./C7tIPmrK.js";import{E as k}from"./DYjlFFbo.js";import{E as z,a as B}from"./CXDY_LVT.js";import{cA as C,e as I}from"./CmRxzTqw.js";import{a as L,E as M}from"./Bf_xRNbS.js";/* empty css        *//* empty css        */import"./06MVqVCl.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{w as N,c as O}from"./-CaxLuW0.js";import{l as Q,b as _,E as D,M as p,N as f,a3 as s,a1 as i,u as l,y as P,O as e,a5 as v,_ as $,ao as j,a0 as A}from"./CUZG7cWw.js";import{_ as R}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";import"./_i9izYtZ.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./BOx_5T3X.js";import"./CtvQKSRC.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./tONJIxwY.js";const q={class:"flex flex-col gap-2"},G={class:"flex items-center gap-2"},H={class:"flex items-center cursor-pointer text-[#999999]"},J={class:"flex gap-4 items-center pl-3"},K={class:"flex items-center gap-2"},T={class:"flex items-center cursor-pointer text-[#999999]"},W={class:"flex gap-4 items-center pl-3"},X={class:"flex items-center gap-2 mb-2"},Y={class:"flex items-center cursor-pointer text-[#999999]"},Z={class:"flex gap-4 items-center"},ee={class:"flex items-center gap-2 mb-2"},te={class:"flex items-center cursor-pointer text-[#999999]"},oe={class:"flex gap-4 items-center"},se=Q({__name:"sd-options",props:{modelValue:{type:Object,default:{step:"",sampling:"",seed:"",cfg:""}}},emits:["update:modelValue"],setup(g,{emit:x}){const V=x,h=g,{modelValue:n}=C(h,V),d=_([]),m=_("1");return D(async()=>{N().then(c=>{d.value=c})}),(c,t)=>{const r=S,a=U,u=k,w=z,E=B,b=I,y=M,F=L;return p(),f("div",null,[s(F,{modelValue:l(m),"onUpdate:modelValue":t[5]||(t[5]=o=>P(m)?m.value=o:null),class:"complex_params"},{default:i(()=>[s(y,{title:"高级参数",name:"1"},{title:i(()=>t[6]||(t[6]=[e("div",{class:"flex items-center gap-2"},[e("span",null,"高级参数")],-1)])),default:i(()=>[e("div",q,[e("div",null,[e("div",G,[t[7]||(t[7]=e("span",null,"绘画步数",-1)),s(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"越低：细节简练，耗时更短；越高：细节丰富，耗时变长；注*步数过高可能产生细节扭曲"},{reference:i(()=>[e("div",H,[s(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",J,[s(u,{modelValue:l(n).step,"onUpdate:modelValue":t[0]||(t[0]=o=>l(n).step=o),step:1,max:150},null,8,["modelValue"]),e("span",null,v(l(n).step),1)])]),e("div",null,[e("div",K,[t[8]||(t[8]=e("span",null,"文本强度",-1)),s(a,{placement:"right","show-arrow":!1,transition:"custom-popover",width:200,trigger:"hover",content:"低：淡化输入的特征，淡化风格；高：强化输入的特征，强化风格；最佳使用区间7-12，推荐不超过15"},{reference:i(()=>[e("div",T,[s(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",W,[s(u,{modelValue:l(n).cfg_scale,"onUpdate:modelValue":t[1]||(t[1]=o=>l(n).cfg_scale=o),step:.5,max:30},null,8,["modelValue"]),e("span",null,v(l(n).cfg_scale),1)])]),e("div",null,[e("div",X,[t[9]||(t[9]=e("span",null,"采样模式",-1)),s(a,{placement:"right","show-arrow":!1,transition:"custom-popover",width:200,trigger:"hover",content:"靠前的采样（euler）：适合动漫，细节简练，快速；靠后的采样（DPM）：适合写实，细节丰富，较慢"},{reference:i(()=>[e("div",Y,[s(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",Z,[s(E,{modelValue:l(n).sampler_name,"onUpdate:modelValue":t[2]||(t[2]=o=>l(n).sampler_name=o),placeholder:"请选择采样模式"},{default:i(()=>[(p(!0),f($,null,j(l(d),o=>(p(),A(w,{key:o.name,label:o.name,value:o.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),e("div",null,[e("div",ee,[t[10]||(t[10]=e("span",null,"随机种子",-1)),s(a,{placement:"right","show-arrow":!1,transition:"custom-popover",width:200,trigger:"hover",content:"每次生成图的初始画布，种子、提示词、参数和模型相同的情况下，可复原绘画结果"},{reference:i(()=>[e("div",te,[s(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",oe,[s(b,{modelValue:l(n).seed,"onUpdate:modelValue":t[3]||(t[3]=o=>l(n).seed=o),type:"number",min:-1,maxlength:18,onFocus:t[4]||(t[4]=o=>l(O)()),placeholder:"请选择采样模式"},null,8,["modelValue"])])])])]),_:1})]),_:1},8,["modelValue"])])}}}),Qe=R(se,[["__scopeId","data-v-f7ef8053"]]);export{Qe as default};
