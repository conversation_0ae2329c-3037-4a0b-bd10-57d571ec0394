import{I as z,P as k,X as v,aq as _,V as b,J as C,Y as N,H as P,M as B,N as w}from"./CmRxzTqw.js";import{l as d,b as A,m as i,H as I,c as V,M as o,N as u,a6 as f,u as n,a0 as m,a1 as q,a2 as H,V as L,$ as M}from"./CUZG7cWw.js";const T=z({size:{type:[Number,String],values:k,default:"",validator:a=>v(a)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:_},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:b(String),default:"cover"}}),$={error:a=>a instanceof Event},j=["src","alt","srcset"],D=d({name:"ElAvatar"}),F=d({...D,props:T,emits:$,setup(a,{emit:y}){const t=a,e=C("avatar"),c=A(!1),S=i(()=>{const{size:s,icon:l,shape:p}=t,r=[e.b()];return I(s)&&r.push(e.m(s)),l&&r.push(e.m("icon")),p&&r.push(e.m(p)),r}),h=i(()=>{const{size:s}=t;return v(s)?e.cssVarBlock({size:N(s)||""}):void 0}),E=i(()=>({objectFit:t.fit}));V(()=>t.src,()=>c.value=!1);function g(s){c.value=!0,y("error",s)}return(s,l)=>(o(),u("span",{class:M(n(S)),style:f(n(h))},[(s.src||s.srcSet)&&!c.value?(o(),u("img",{key:0,src:s.src,alt:s.alt,srcset:s.srcSet,style:f(n(E)),onError:g},null,44,j)):s.icon?(o(),m(n(P),{key:1},{default:q(()=>[(o(),m(H(s.icon)))]),_:1})):L(s.$slots,"default",{key:2})],6))}});var J=B(F,[["__file","avatar.vue"]]);const Y=w(J);export{Y as E};
