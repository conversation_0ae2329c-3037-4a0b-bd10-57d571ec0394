import{h as c,e as d}from"./CmRxzTqw.js";import{a as _,E as f}from"./B7GaOiDz.js";import"./DP2rzg_V.js";/* empty css        */import{l as V,M as x,N as E,O as a,a3 as e,a1 as l,u as s}from"./CUZG7cWw.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";const h={class:"manual-import"},F={class:"py-4 flex flex-col"},v={class:"flex-1 min-w-0"},D=V({__name:"manual-doc",props:{modelValue:{}},emits:["update:modelValue"],setup(m,{emit:n}){const o=c(m,"modelValue",n);return(I,t)=>{const r=d,p=f,i=_;return x(),E("div",h,[a("div",F,[e(i,null,{default:l(()=>[e(p,null,{default:l(()=>[a("div",v,[e(r,{modelValue:s(o).question,"onUpdate:modelValue":t[0]||(t[0]=u=>s(o).question=u),placeholder:"请输入文本内容，10000个字以内。",type:"textarea",resize:"none",rows:15,maxlength:"10000"},null,8,["modelValue"])])]),_:1})]),_:1})])])}}});export{D as default};
