import{E as f}from"./B7GaOiDz.js";import{cA as v}from"./CmRxzTqw.js";import"./DP2rzg_V.js";import{l as V,r as h,M as t,a0 as w,a1 as o,O as s,N as c,ao as z,$ as r,u as i,a5 as k,_ as b}from"./CUZG7cWw.js";import{_ as y}from"./DlAUqK2U.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";const C={class:"flex-1 min-w-0 overflow-hidden"},B={class:"flex flex-wrap mx-[-6px] mb-[-10px]"},E=["onClick"],S={class:"flex justify-center items-center h-[20px]"},g={class:"text-base text-[#101010] dark:text-white mt-[4px] size-scale"},F=V({__name:"video-size",props:{modelValue:{default:"1:1"}},emits:["update:modelValue"],setup(p,{emit:n}){const u=n,d=p,{modelValue:a}=v(d,u),x=h({lists:[{scaleValue:"1:1",class:"w-[20px] h-[20px]"},{scaleValue:"3:4",class:"w-[15px] h-[20px]"},{scaleValue:"4:3",class:"w-[20px] h-[15px]"},{scaleValue:"9:16",class:"w-[13px] h-[20px]"},{scaleValue:"16:9",class:"w-[20px] h-[12px]"}]});return a.value="1:1",(I,l)=>{const _=f;return t(),w(_,{prop:"scale",required:""},{label:o(()=>l[0]||(l[0]=[s("span",{class:"font-bold text-tx-primary"}," 生成尺寸 ",-1)])),default:o(()=>[s("div",C,[s("div",B,[(t(!0),c(b,null,z(i(x).lists,(e,m)=>(t(),c("div",{key:m,class:"w-[33.3%] px-[6px]"},[s("div",{class:r(["picture-size cursor-pointer text-center hover:text-primary",{"picture-size-active":i(a)==(e==null?void 0:e.scaleValue),"picture-size-disable":!(e!=null&&e.scaleValue)}]),onClick:M=>a.value=e.scaleValue},[s("div",S,[s("div",{class:r(["rect",e.class])},null,2)]),s("div",g,k(e.scaleValue),1)],10,E)]))),128))])])]),_:1})}}}),G=y(F,[["__scopeId","data-v-ea183e0a"]]);export{G as default};
