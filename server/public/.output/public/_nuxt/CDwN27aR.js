import{I as T,aq as _,V as B,ar as E,as as $,a6 as q,at as D,au as Z,Y as G,av as Y,aw as j,ax as x,U as H}from"./CmRxzTqw.js";import{i as J}from"./DCTLXrZ8.js";import{b as c,m as I,c as P,n as K,E as Q,a as W}from"./CUZG7cWw.js";const X=T({center:Boolean,alignCenter:Boolean,closeIcon:{type:_},draggable:Boolean,overflow:Boolean,fullscreen:Boolean,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),ne={close:()=>!0},te=T({...X,appendToBody:Boolean,appendTo:{type:B(String),default:"body"},beforeClose:{type:B(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:<PERSON>olean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:{type:Boolean,default:!1},headerAriaLevel:{type:String,default:"2"}}),ae={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[E]:e=>$(e),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},se=(e,a)=>{var m;const l=W().emit,{nextZIndex:C}=q();let p="";const F=D(),S=D(),n=c(!1),d=c(!1),i=c(!1),r=c((m=e.zIndex)!=null?m:C());let s,u;const h=Z("namespace",j),O=I(()=>{const o={},t=`--${h.value}-dialog`;return e.fullscreen||(e.top&&(o[`${t}-margin-top`]=e.top),e.width&&(o[`${t}-width`]=G(e.width))),o}),w=I(()=>e.alignCenter?{display:"flex"}:{});function A(){l("opened")}function k(){l("closed"),l(E,!1),e.destroyOnClose&&(i.value=!1)}function L(){l("close")}function b(){u==null||u(),s==null||s(),e.openDelay&&e.openDelay>0?{stop:s}=x(()=>g(),e.openDelay):g()}function f(){s==null||s(),u==null||u(),e.closeDelay&&e.closeDelay>0?{stop:u}=x(()=>v(),e.closeDelay):v()}function y(){function o(t){t||(d.value=!0,n.value=!1)}e.beforeClose?e.beforeClose(o):f()}function N(){e.closeOnClickModal&&y()}function g(){H&&(n.value=!0)}function v(){n.value=!1}function M(){l("openAutoFocus")}function V(){l("closeAutoFocus")}function z(o){var t;((t=o.detail)==null?void 0:t.focusReason)==="pointer"&&o.preventDefault()}e.lockScroll&&Y(n);function U(){e.closeOnPressEscape&&y()}return P(()=>e.modelValue,o=>{o?(d.value=!1,b(),i.value=!0,r.value=J(e.zIndex)?C():r.value++,K(()=>{l("open"),a.value&&(a.value.scrollTop=0)})):n.value&&f()}),P(()=>e.fullscreen,o=>{a.value&&(o?(p=a.value.style.transform,a.value.style.transform=""):a.value.style.transform=p)}),Q(()=>{e.modelValue&&(n.value=!0,i.value=!0,b())}),{afterEnter:A,afterLeave:k,beforeLeave:L,handleClose:y,onModalClick:N,close:f,doClose:v,onOpenAutoFocus:M,onCloseAutoFocus:V,onCloseRequested:U,onFocusoutPrevented:z,titleId:F,bodyId:S,closed:d,style:O,overlayDialogStyle:w,rendered:i,visible:n,zIndex:r}};export{ne as a,te as b,ae as c,X as d,se as u};
