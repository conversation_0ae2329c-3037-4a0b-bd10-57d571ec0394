import{E as k}from"./CiabO6Xq.js";import{_ as w}from"./eFgaMLiC.js";import{E}from"./DNRqakyH.js";import{cA as V,d as z,bH as G}from"./CmRxzTqw.js";/* empty css        */import"./DikNcrXK.js";import{l as I,b as M,m as N,M as m,N as n,a3 as r,a1 as P,u as o,y as B,O as s,a4 as C,ag as U,a5 as $}from"./CUZG7cWw.js";import{_ as J}from"./xixvWuCN.js";import{c as j,d as a}from"./-CaxLuW0.js";import{_ as q}from"./CXAJ--Vj.js";import{_ as L}from"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./tONJIxwY.js";import"./C7tIPmrK.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";/* empty css        *//* empty css        */const A={key:0,class:"flex justify-center items-center h-[150px] relative"},D={key:1,class:"uploader-container"},F={class:"el-upload__tip text-[#798696]"},R=I({__name:"uploader",props:{modelValue:{default:""},type:{},files:{}},emits:["update:modelValue"],setup(f,{emit:d}){const u=d,p=f,{modelValue:i}=V(p,u),l=M([]),_=e=>j()?!1:parseInt(a.value.file_size)>0&&e.size>parseInt(a.value.file_size)*1024*1024?(z.error(`文件大小不能超过${a.value.file_size}MB`),!1):!0,v=e=>{u("update:modelValue",e.uri),i.value=e.uri,l.value=[{name:e.name,url:e.uri}]},g=e=>G(p.type,{file:e.file,name:"file",header:{},data:{type:"draw"}}),x=N(()=>{switch(p.type){case"image":return".jpg,.png,.jpeg";case"video":return".wmv,.avi,.mpg,.mpeg,.3gp,.mov,.mp4,.flv,.rmvb,.mkv";case"audio":return;default:return"*"}});return(e,t)=>{const h=k,y=w,b=E;return m(),n("div",null,[r(q,{title:"上传参考图",required:"",tips:"上传一张图片做为基底，用模型在其上面重新生成新的图片"}),r(b,{"file-list":o(l),"onUpdate:fileList":t[1]||(t[1]=c=>B(l)?l.value=c:null),class:"uploader",drag:"",multiple:!1,"show-file-list":!1,"on-success":v,"http-request":g,"before-upload":_,accept:o(x)},{default:P(()=>[s("div",null,[o(i)?(m(),n("div",A,[r(h,{class:"!block h-[100%]",src:o(i),fit:"contain"},null,8,["src"]),r(y,{class:"!absolute right-0 top-0 z-10 drop-shadow",name:"el-icon-CircleCloseFilled",color:"#ffffff",onClick:t[0]||(t[0]=C(c=>i.value="",["stop"]))})])):(m(),n("div",D,[t[2]||(t[2]=s("img",{src:J,alt:"文件上传",class:"w-8 mx-auto mb-2"},null,-1)),t[3]||(t[3]=s("div",{class:"el-upload__text text-[#798696] !text-[13px]"},[U(" 拖拽文件到此处或者"),s("em",null,"点击上传")],-1)),s("div",F,$(parseInt(o(a).file_size)>0?`支持图片格式：JPG/JPEG/PNG，低于${o(a).file_size}MB`:"支持图片格式：JPG/JPEG/PNG"),1)]))])]),_:1},8,["file-list","accept"])])}}}),ge=L(R,[["__scopeId","data-v-3c65f031"]]);export{ge as default};
