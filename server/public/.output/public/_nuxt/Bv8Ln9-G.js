import{a as f,E as V}from"./CXDY_LVT.js";import{cA as y}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{d as v}from"./-CaxLuW0.js";import{_ as j}from"./CXAJ--Vj.js";import{l as x,M as o,N as m,a3 as a,O as k,a1 as B,u as s,y as E,_ as g,ao as M,a0 as N}from"./CUZG7cWw.js";import{_ as S}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./tONJIxwY.js";import"./eFgaMLiC.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";/* empty css        */const b={class:"mj-styles"},h=x({__name:"mj-styles",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(p,{emit:i}){const n=i,c=p,{modelValue:t}=y(c,n);return(w,r)=>{const u=V,d=f;return o(),m("div",b,[a(j,{title:"风格选择",tips:"指定midjourney的渲染风格"}),k("div",null,[a(d,{modelValue:s(t),"onUpdate:modelValue":r[0]||(r[0]=e=>E(t)?t.value=e:null),placeholder:"请选择版本",class:"w-full mt-[8px]",size:"large"},{default:B(()=>{var e;return[(o(!0),m(g,null,M((e=s(v))==null?void 0:e.mj_style,(l,_)=>(o(),N(u,{key:l,label:l,value:_},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])])])}}}),me=S(h,[["__scopeId","data-v-dc4ec9a9"]]);export{me as default};
