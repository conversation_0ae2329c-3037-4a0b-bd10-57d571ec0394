import{d as k,j as S,a as M,cY as y}from"./CmRxzTqw.js";import{u as C}from"./DRe575WM.js";import{u as P}from"./DqGsTvs3.js";import{DrawModeEnum as w,DrawTypeEnum as T,DrawLink as h}from"./tONJIxwY.js";import{r as j,b as s}from"./CUZG7cWw.js";function z(e){return $request.post({url:"/draw.draw/drawing",params:e})}function U(e){return $request.post({url:"/draw.draw_records/detail",params:e})}function F(e){return $request.get({url:"/draw.draw_records/records",params:e})}function I(e){return $request.post({url:"/draw.draw_records/delete",params:e})}function ae(e){return $request.get({url:"/draw.draw_prompt/category",params:e})}function re(e){return $request.get({url:"/draw.draw_prompt/prompt",params:e})}function te(e){return $request.get({url:"/draw.draw_prompt/example",params:e})}function se(e){return $request.get({url:"/draw.draw_prompt/translate",params:e})}function O(e){return $request.get({url:"/draw.draw/getSdModel",params:e})}function H(){return $request.get({url:"/draw.draw/getSdModelCategory"})}function oe(){return $request.get({url:"/draw.draw/getSdSamplers"})}function L(e){return $request.get({url:"/draw.draw/config",params:e})}const m=j({status:-1,model:w.SD}),l=s([]),g=s(!1),o=s(!1),u=s(!1),p=s({}),q=s([]),f=s([]),d=s(0),D=s([]),t=s({draw_api:w.SD,draw_type:"txt2img",draw_model:"",draw_loras:[],denoising_strength:.75,size:"512x512",prompt:"",negative_prompt:"",action:"generate",image_mask:"",image_id:"",complex_params:{step:20,sampler_name:"Euler a",seed:-1,cfg_scale:7},engine:"",quality:"",style:"",version:"",origin_task_id:""}),{pager:$,getLists:n}=C({fetchFun:F,params:m}),R=async e=>{x()||(m.status=e,o.value=!0,await n(),o.value=!1)},_=()=>{l.value=J($.lists),l.value.length>0&&B()},A=async()=>{try{o.value=!0,await n(),p.value=await L({draw_api:t.value.draw_api}),o.value=!1,_()}catch{o.value=!1}},N=async e=>{await I({ids:[e]}),k.success("删除成功"),n()},Y=async()=>{const e=M();try{if(!l.value.length)return c();const a=await U({records_id:l.value});return(a.filter(r=>(r.status===3&&!e.fullPath.includes("/draw")?y({title:"绘画成功",type:"success",dangerouslyUseHTMLString:!0,message:`<div>点击前往<a class="text-primary font-bold" href="${h[t.value.draw_api]}">绘画记录</a>查看</div>`,duration:1e4}):r.status===2&&!e.fullPath.includes(h[t.value.draw_api])&&y({title:"绘画失败",message:r.fail_reason,type:"error",duration:1e4}),r.status===3||r.status===2)).length||!a.length)&&E(),a}catch(a){c(),console.log("获取详情失败=>",a)}},E=async()=>{c(),console.log("获取详情结束=>"),S().getUser(),await n(),_(),p.value=await L({draw_api:t.value.draw_api})},{start:B,end:c}=P(Y,{key:"draw",totalTime:10*60*1e3,time:2e3,callback:E}),G=async e=>{try{c(),await K(e),e.draw_type!=="img2img"&&e.draw_api===w.SD&&(e.image_mask=""),u.value=!0,await z(e),await n(),u.value=!1,g.value=!g.value,_()}catch{u.value=!1}finally{b({action:"generate"})}},J=e=>e.filter(a=>a.status===1).map(a=>a.id),K=e=>new Promise((a,i)=>{try{const r=e||t.value;if(r.draw_type===T.img2img&&r.image_mask==="")throw new Error("请上传参考图");if(r.prompt==="")throw new Error("请输入提示词");if(r.draw_api==="")throw new Error("请选择主要模型");a(!0)}catch(r){k.error(r.message),i(r)}}),Q=async()=>H().then(async e=>{D.value=[{label:"全部",value:0},...e.map(a=>({value:a.id,label:a.name}))],d.value=0,await v()}),v=()=>{O({category_id:d.value}).then(e=>{f.value=e})},b=async e=>{if(e){t.value={...t.value,...e};const a=f.value.find(i=>i.model_name===e.draw_model);a&&(d.value=a==null?void 0:a.category_id,await v(),q.value=a.loras)}else t.value={...t.value,draw_model:"",draw_loras:[],denoising_strength:.75,size:"512x512",prompt:"",negative_prompt:"",action:"generate",image_mask:"",image_id:"",complex_params:{step:20,sampler_name:"Euler a",seed:-1,cfg_scale:7}}},x=()=>{const e=S();return e.isLogin||e.toggleShowLogin(),!e.isLogin},ne=Object.freeze(Object.defineProperty({__proto__:null,checkOngoingTask:A,checkUserLogin:x,config:p,createLoading:u,createTask:G,deleteHandle:N,formData:t,getLists:n,getModel:v,getModelCategory:Q,loraList:q,modelCategory:d,modelCategoryList:D,modelList:f,pageLoading:o,pager:$,resetFormData:b,scroll:g,taskIds:l,taskStatusChange:R,taskStatusParams:m},Symbol.toStringTag,{value:"Module"}));export{ne as A,G as a,u as b,x as c,p as d,o as e,t as f,n as g,N as h,re as i,te as j,ae as k,se as l,q as m,Q as n,d as o,$ as p,D as q,b as r,g as s,R as t,f as u,v,oe as w,m as x,A as y,F as z};
