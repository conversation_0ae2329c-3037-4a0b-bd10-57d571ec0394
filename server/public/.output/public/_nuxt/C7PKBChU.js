import{e as V,E as q}from"./CmRxzTqw.js";import{E as C,a as D}from"./B7GaOiDz.js";import{E as L}from"./CiabO6Xq.js";import{E as N}from"./DikNcrXK.js";import{E as B}from"./oVx59syQ.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import{u as I}from"./Bj_9-7Jh.js";import{q as M}from"./Dl64kDm5.js";import{l as S,b as u,M as n,a0 as T,a1 as s,O as e,a3 as o,u as t,ag as U,N as p,Z as _,_ as j,ao as O,a5 as d}from"./CUZG7cWw.js";import{_ as P}from"./DlAUqK2U.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";const R=""+new URL("empty.C6MrDaor.png",import.meta.url).href,Z={class:"p-main"},z={class:"grid lg:grid-cols-2 gap-4 grid-cols-1 h-full"},A={class:"px-[10px] py-[5px] h-full lg:borderLeft"},G={key:0,class:"flex flex-col items-center justify-center h-full"},H={key:0},J={class:"text-sm text-tx-secondary mt-[5px] whitespace-pre-line"},K={class:"text-sm text-tx-secondary whitespace-pre-line"},Q=S({__name:"testData",props:{id:{type:Number,default:0},type:{type:String,default:""}},setup(f){const x=f,i=u([]),l=u({kb_id:x.id,question:""}),g=async()=>{i.value=await M({...l.value})},{lockFn:b,isLock:y}=I(g);return(W,a)=>{const h=V,c=C,v=q,k=D,E=L,w=N,m=B;return n(),T(m,null,{default:s(()=>[e("div",Z,[e("div",z,[e("div",null,[o(k,{"label-width":"90px"},{default:s(()=>[o(c,{label:"测试文本"},{default:s(()=>[o(h,{modelValue:t(l).question,"onUpdate:modelValue":a[0]||(a[0]=r=>t(l).question=r),type:"textarea",rows:"20"},null,8,["modelValue"])]),_:1}),o(c,null,{default:s(()=>[e("div",null,[o(v,{loading:t(y),disabled:t(l).question==""||t(l).kb_id=="",type:"primary",onClick:t(b)},{default:s(()=>a[1]||(a[1]=[U(" 测试 ")])),_:1},8,["loading","disabled","onClick"])])]),_:1})]),_:1})]),e("div",A,[t(i).length==0?(n(),p("div",G,[o(E,{src:t(R)},null,8,["src"]),a[2]||(a[2]=e("div",{class:"mt-[10px] text-[#5a646e]"}," 测试结果将在这里展示 ",-1))])):_("",!0),o(m,null,{default:s(()=>[t(i).length!=0?(n(),p("div",H,[(n(!0),p(j,null,O(t(i),(r,F)=>(n(),p("div",{key:F,class:"p-[10px] border border-solid border-br-light mb-[10px] rounded"},[o(w,{percentage:Math.abs(r.score/1)*100,color:"var(--el-text-color-disabled)"},{default:s(()=>[e("span",null,d(Math.abs(r.score).toFixed(5)),1)]),_:2},1032,["percentage"]),e("div",J,d(r.question),1),e("div",K,d(r.answer),1)]))),128))])):_("",!0)]),_:1})])])])]),_:1})}}}),xt=P(Q,[["__scopeId","data-v-efecb610"]]);export{xt as default};
