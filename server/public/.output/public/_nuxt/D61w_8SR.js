import{I as P,V,X as W,as as C,aK as j,J as H,cD as R,cO as z,Y,S as q,ah as D,T as G,M as J,N as K,b as L}from"./CmRxzTqw.js";import{l as S,j as h,b as v,m as B,c as U,E as X,w as F,M,N as Q,O as _,V as Z,$ as b,u as a,a6 as w,a0 as ee,a1 as te,a3 as x}from"./CUZG7cWw.js";import{_ as oe}from"./DBz5lpK8.js";import ae from"./FkYCnHE4.js";import{_ as se}from"./QHNTKww7.js";import{_ as le}from"./DlAUqK2U.js";const ne=P({zIndex:{type:V([Number,String]),default:100},target:{type:String,default:""},offset:{type:Number,default:0},position:{type:String,values:["top","bottom"],default:"top"}}),re={scroll:({scrollTop:s,fixed:l})=>W(s)&&C(l),[j]:s=>C(s)},A="ElAffix",ce=S({name:A}),ie=S({...ce,props:ne,emits:re,setup(s,{expose:l,emit:c}){const e=s,n=H("affix"),r=h(),i=h(),u=h(),{height:f}=R(),{height:p,width:y,top:E,bottom:N,update:g}=z(i,{windowScroll:!1}),d=z(r),o=v(!1),$=v(0),m=v(0),I=B(()=>({height:o.value?`${p.value}px`:"",width:o.value?`${y.value}px`:""})),O=B(()=>{if(!o.value)return{};const t=e.offset?Y(e.offset):0;return{height:`${p.value}px`,width:`${y.value}px`,top:e.position==="top"?t:"",bottom:e.position==="bottom"?t:"",transform:m.value?`translateY(${m.value}px)`:"",zIndex:e.zIndex}}),T=()=>{if(u.value)if($.value=u.value instanceof Window?document.documentElement.scrollTop:u.value.scrollTop||0,e.position==="top")if(e.target){const t=d.bottom.value-e.offset-p.value;o.value=e.offset>E.value&&d.bottom.value>0,m.value=t<0?t:0}else o.value=e.offset>E.value;else if(e.target){const t=f.value-d.top.value-e.offset-p.value;o.value=f.value-e.offset<N.value&&f.value>d.top.value,m.value=t<0?-t:0}else o.value=f.value-e.offset<N.value},k=()=>{g(),c("scroll",{scrollTop:$.value,fixed:o.value})};return U(o,t=>c("change",t)),X(()=>{var t;e.target?(r.value=(t=document.querySelector(e.target))!=null?t:void 0,r.value||q(A,`Target does not exist: ${e.target}`)):r.value=document.documentElement,u.value=D(i.value,!0),g()}),G(u,"scroll",k),F(T),l({update:T,updateRoot:g}),(t,_e)=>(M(),Q("div",{ref_key:"root",ref:i,class:b(a(n).b()),style:w(a(I))},[_("div",{class:b({[a(n).m("fixed")]:o.value}),style:w(a(O))},[Z(t.$slots,"default")],6)],6))}});var ue=J(ie,[["__file","affix.vue"]]);const fe=K(ue),pe={class:"max-w-[1200px] w-full mx-auto flex items-center"},de={class:"flex-1 min-w-0"},me=S({__name:"header",props:{prop:{}},setup(s){const l=L(),c=v(0),e=({scrollTop:n})=>{c.value=n/80>.8?.8:n/80};return(n,r)=>{const i=fe;return M(),ee(i,{target:"",style:{height:"0",width:"100%"},offset:0,onScroll:e},{default:te(()=>[_("div",{class:"header w-full h-[60px] flex items-center justify-center",style:w({background:"rgba(256,256, 256,"+a(c)+")"})},[_("div",pe,[x(oe,{class:b("mr-[50px]"),logo:a(l).getWebsiteConfig.pc_logo,title:a(l).getWebsiteConfig.pc_name},null,8,["logo","title"]),_("div",de,[x(ae,{"is-home":!0})]),x(se,{class:"ml-auto",isHidden:!0})])],4)]),_:1})}}}),ve=le(me,[["__scopeId","data-v-85c3c1dc"]]),ye=Object.freeze(Object.defineProperty({__proto__:null,default:ve},Symbol.toStringTag,{value:"Module"}));export{ye as _};
