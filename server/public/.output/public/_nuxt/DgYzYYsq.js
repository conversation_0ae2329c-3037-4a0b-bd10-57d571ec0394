import{E as u}from"./HA5sEeDs.js";import{l as d,b as p,c as v,M as f,N as x,O as o,a5 as i,a3 as b,u as V}from"./CUZG7cWw.js";import{_}from"./DlAUqK2U.js";import"./CmRxzTqw.js";import"./D8e5izeA.js";import"./gj6kus5n.js";const g={class:"flex-1"},h={class:"font-medium text-tx-primary"},N={class:"text-tx-placeholder text-xs mt-2"},k=d({__name:"permission-option",props:{label:String,description:String,value:Number,modelValue:Number},emits:["change"],setup(e,{emit:n}){const t=e,r=n,a=p(t.modelValue);v(()=>t.modelValue,l=>{a.value=l});const c=()=>{r("change",t.value)};return(l,s)=>(f(),x("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:c},[o("div",g,[o("div",h,i(e.label),1),o("div",N,i(e.description),1)]),b(V(u),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=m=>a.value=m),"true-value":e.value,label:"",size:"large",class:"ml-2"},null,8,["modelValue","true-value"])]))}}),M=_(k,[["__scopeId","data-v-bfb962ae"]]);export{M as default};
