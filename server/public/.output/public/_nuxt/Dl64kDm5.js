function e(t){return $request.get({url:"/kb.know/lists",params:t})}function r(t){return $request.get({url:"/kb.know/detail",params:t})}function n(t){return $request.post({url:"/kb.know/add",params:t})}function u(t){return $request.post({url:"/kb.know/edit",params:t})}function a(t){return $request.post({url:"/kb.know/del",params:t})}function s(t){return $request.get({url:"/kb.know/files",params:t})}function o(t){return $request.post({url:"/kb.teach/import",params:t})}function i(t){return $request.get({url:"/kb.teach/datas",params:t})}function l(t){return $request.post({url:"/kb.know/fileRename",params:t})}function c(t){return $request.post({url:"/kb.know/fileRemove",params:t})}function k(t){return $request.post({url:"/kb.teach/qaRetry",params:t})}function f(t){return $request.post({url:"/kb.teach/tests",params:t})}function p(t){return $request.post({url:"/kb.teach/insert",params:t})}function b(t){return $request.post({url:"/kb.teach/update",params:t})}function q(t){return $request.post({url:"/kb.teach/reset",params:t})}function d(t){return $request.post({url:"/kb.teach/delete",params:t})}function w(t){return $request.get({url:"/kb.teach/detail",params:t})}function $(t){return $request.post({url:"/kb.teach/capture",params:t})}function m(t){return $request.post({url:"/kb.teach/detection",params:t})}export{n as a,r as b,a as c,c as d,k as e,s as f,o as g,q as h,i,d as j,u as k,m as l,l as m,b as n,p as o,w as p,f as q,e as r,$ as w};
