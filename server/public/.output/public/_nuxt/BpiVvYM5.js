import{E as f}from"./oVx59syQ.js";import{_ as u}from"./eFgaMLiC.js";import{b as _}from"./CmRxzTqw.js";/* empty css        */import{l as v,m as r,M as i,N as m,O as p,a3 as e,a1 as n,a7 as C,a8 as h,u as s,Z as b,V as x}from"./CUZG7cWw.js";import g from"./DGy9z-W2.js";import w from"./B_3nBLl5.js";import S from"./CXtYBCvR.js";import{_ as N}from"./DlAUqK2U.js";import"./9CYoqqXX.js";import"./BOx_5T3X.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./Ddo5WWE5.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./cdDY9IAx.js";import"./mBG0LxMu.js";import"./CbQsrhNE.js";import"./DecTOTC8.js";const $={class:"layout-aside h-full flex"},k={class:"h-full flex justify-between flex-col"},y={class:"h-full"},E=v({__name:"index",setup(M){const o=_(),a=r(()=>o.isMobile);return r({get(){return!o.isCollapsed&&a.value},set(t){o.toggleCollapsed(!t)}}),(t,l)=>{const c=f,d=u;return i(),m("div",$,[p("div",k,[e(c,{class:"w-[80px] el-scrollbar"},{default:n(()=>[e(S,{class:"mb-auto"})]),_:1}),e(w)]),C(p("div",y,[e(g,null,{default:n(()=>[x(t.$slots,"aside",{},void 0,!0)]),_:3})],512),[[h,!s(o).isCollapsed]]),t.$slots.panel&&!s(a)?(i(),m("div",{key:0,class:"panel-left-arrow",onClick:l[0]||(l[0]=V=>s(o).toggleCollapsed())},[e(d,{class:"mr-1",name:`el-icon-${s(o).isCollapsed?"CaretRight":"CaretLeft"}`},null,8,["name"])])):b("",!0)])}}}),X=N(E,[["__scopeId","data-v-e34fcc87"]]);export{X as default};
