var o=Object.defineProperty;var p=(i,t,s)=>t in i?o(i,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[t]=s;var a=(i,t,s)=>p(i,typeof t!="symbol"?t+"":t,s);const d="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAC+SURBVHhe7djBDYAwDATBuED6LydAAfiTj8UN3yhCshyfd2vvfa3mq6rueD332/Pp90sBdIAnYAYYglLguwLTY+w0ht+Qb2fA6Q/G31cAHeAJmAHdHjB+iJ3CmiGYPgThMByGw3AYDsPhYByOj0GbYPomqAN0ACFCiBAiTQV+b4TiFyEFIEQIEUKEECFEgoUIGkSDaBANosFkGpQC6SkAh+EwHIbDcBgOB+NwfAzaBNM3QR2gAxghRogRCjZCN8AuE04B1B91AAAAAElFTkSuQmCC";class B{constructor(){a(this,"ctx",null);a(this,"canvas",null);a(this,"stepList",[]);a(this,"stage",null);a(this,"mosaicImage");this.mosaicImage=new Image,this.mosaicImage.src=d}init(t){this.ctx=t.ctx,this.canvas=t.canvas,this.stepList=t.stepList,this.stage=t}saveCurrentState(){var t;this.stage.stepIndex<this.stepList.length-1&&this.stepList.splice(this.stage.stepIndex+1),this.stepList.push((t=this.ctx)==null?void 0:t.getImageData(0,0,this.canvas.width,this.canvas.height)),this.stage.stepIndex++}reapplyState(){var t,s;this.stage.stepIndex<0||((t=this.ctx)==null||t.clearRect(0,0,this.canvas.width,this.canvas.height),(s=this.ctx)==null||s.putImageData(this.stepList[this.stage.stepIndex],0,0))}drawLasso(t,s,A=1.5,l=!1){var h,c,r,x,n,I,m;this.reapplyState();const g=t.length;(h=this.ctx)==null||h.beginPath(),(c=this.ctx)==null||c.moveTo(t[0],t[1]);for(let e=2;e<g-3;e+=4)(r=this.ctx)==null||r.bezierCurveTo(t[e],t[e+1],t[e+2],t[e+3],t[e+4],t[e+5]);l&&((x=this.ctx)==null||x.closePath()),this.ctx.strokeStyle=s,this.ctx.lineWidth=A,(n=this.ctx)==null||n.stroke(),this.ctx.fillStyle=(I=this.ctx)==null?void 0:I.createPattern(this.mosaicImage,"repeat"),(m=this.ctx)==null||m.fill()}drawRect({x:t,y:s,w:A,h:l,color:g}){var h,c;this.reapplyState(),this.ctx.strokeStyle=g,this.ctx.lineWidth=2,this.ctx.rect(t,s,A,l),this.ctx.stroke(),this.ctx.beginPath(),this.ctx.fillStyle=(h=this.ctx)==null?void 0:h.createPattern(this.mosaicImage,"repeat"),(c=this.ctx)==null||c.fillRect(t,s,A,l)}}export{B as DrawingTool};
