import{_ as g}from"./eFgaMLiC.js";import{b as M,by as R,E as T}from"./CmRxzTqw.js";import{E as F}from"./oVx59syQ.js";import{a as q,E as O}from"./DHUC3PVh.js";import{E as P}from"./ArzC3z2d.js";/* empty css        *//* empty css        */import{u as Q}from"./DNOp0HuO.js";import{f as U}from"./DQUFgXGm.js";import{u as Z}from"./DdtGP7XX.js";import{l as G,ai as H,m as C,b as J,M as s,N as o,O as n,_ as i,ao as x,u as c,a3 as a,a1 as m,y as K,Z as E,$ as W,a5 as f,ag as X,a0 as Y}from"./CUZG7cWw.js";import{_ as ee}from"./DlAUqK2U.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";const te={class:"sample-lists-container sm:px-[10px]"},se={class:"flex sample-lists"},oe={class:"flex justify-center items-center mb-[20px]"},ne=["src"],ae={class:"text-2xl font-medium ml-3 text-tx-primary"},le=["onClick"],re={class:"flex-1 text-center line-clamp-1 text-sm sm:text-base"},ie={class:"flex-none flex items-center"},ce={class:"h-[50vh]"},me=["onClick"],pe={class:"line-clamp-2"},y=3,w=3,ue=G({__name:"sample-lists",emits:["click-item"],async setup(de,{emit:D}){let v,k;const b=D;M();const B=R(),{data:p,suspense:S}=Z(["samplesList"],{queryFn:U,placeholderData:[]});[v,k]=H(()=>Q(()=>S(),{lazy:!0},"$BCxmeyzygt")),await v,k();const h=(l,e)=>l.slice(0,e),V=C(()=>h(p.value,y)),$=C(()=>p.value.reduce((l,e)=>{var d;return l+=((d=e.sample)==null?void 0:d.length)||0,l},0)>y*w||p.value.length>y),u=J(!1);return(l,e)=>{const d=g,L=g,N=T,j=F,z=q,A=O,I=P;return s(),o("div",te,[e[3]||(e[3]=n("div",{class:"sm:my-[60px] my-[30px] text-center text-[30px] font-medium"},null,-1)),n("div",se,[(s(!0),o(i,null,x(c(V),t=>(s(),o("div",{key:t.id,class:W(["flex-1 sm:mx-[10px] mx-[5px] p-[20px] sample-lists-item",{"is-dark":c(B)}])},[n("div",oe,[t.image?(s(),o("img",{key:0,class:"w-[35px] h-[35px]",src:t.image,alt:""},null,8,ne)):E("",!0),n("div",ae,f(t.name),1)]),n("div",null,[(s(!0),o(i,null,x(h(t.sample,w),r=>(s(),o("div",{key:r.id,class:"bg-body sm:mb-[15px] mb-[10px] p-[10px] flex justify-center rounded-[12px] cursor-pointer",onClick:_=>b("click-item",r.content)},[n("div",re,f(r.content),1),n("div",ie,[a(d,{name:"el-icon-Right",color:"inherit",size:"16"})])],8,le))),128))])],2))),128))]),c($)?(s(),o(i,{key:0},[n("div",{class:"flex justify-center mt-10",onClick:e[0]||(e[0]=t=>u.value=!0)},[a(N,{link:""},{default:m(()=>[e[2]||(e[2]=X(" 查看更多 ")),a(L,{name:"el-icon-ArrowRight"})]),_:1})]),a(I,{modelValue:c(u),"onUpdate:modelValue":e[1]||(e[1]=t=>K(u)?u.value=t:null),width:"600px",title:"问题示例",class:"sample-popup"},{default:m(()=>[a(A,{"model-value":0},{default:m(()=>[(s(!0),o(i,null,x(c(p),(t,r)=>(s(),Y(z,{key:t.id,label:t.name,name:r},{default:m(()=>[n("div",ce,[a(j,null,{default:m(()=>[(s(!0),o(i,null,x(t.sample,_=>(s(),o("div",{key:_.id,class:"bg-page mb-[10px] p-[10px] flex justify-center rounded-[2px] cursor-pointer",onClick:_e=>b("click-item",_.content)},[n("div",pe,f(_.content),1)],8,me))),128))]),_:2},1024)])]),_:2},1032,["label","name"]))),128))]),_:1})]),_:1},8,["modelValue"])],64)):E("",!0)])}}}),Ne=ee(ue,[["__scopeId","data-v-aff0f892"]]);export{Ne as default};
