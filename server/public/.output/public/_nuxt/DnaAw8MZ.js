import{_ as w}from"./FAfxnQR5.js";import{u as y}from"./Bj_9-7Jh.js";import{j as C,e as u,E as V,dm as S,dn as p,dl as _}from"./CmRxzTqw.js";import{E as m,a as B}from"./B7GaOiDz.js";import{a as L,E as I}from"./CXDY_LVT.js";import{l as M,j as x,m as N,r as R,M as F,N as O,O as c,a5 as U,u as e,a3 as o,a1 as t,ag as j}from"./CUZG7cWw.js";const D={class:"flex-1 flex flex-col"},T={class:"flex flex-1 flex-col pt-[30px] px-[30px] min-h-0"},q={class:"text-2xl font-medium text-tx-primary"},G={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},W=M({__name:"bind-mobile",setup(z){const s=C(),r=x(),f=x(),g={mobile:[{required:!0,message:"请输入手机号码",trigger:["change","blur"]}],code:[{required:!0,message:"请输入验证码",trigger:["change","blur"]}]},n=N(()=>!!s.userInfo.mobile),l=R({type:n.value?"change":"bind",mobile:"",code:""}),b=async()=>{var i,a;await((i=r.value)==null?void 0:i.validateField(["mobile"])),await S({scene:n.value?p.CHANGE_MOBILE:p.BIND_MOBILE,mobile:l.mobile}),(a=f.value)==null||a.start()},v=async()=>{var i;await((i=r.value)==null?void 0:i.validate()),s.isLogin?await _(l):(await _(l,{token:s.temToken}),s.login(s.temToken),location.reload(),await s.getUser()),s.toggleShowLogin(!1)},{lockFn:k,isLock:E}=y(v);return(i,a)=>{const h=w;return F(),O("div",D,[c("div",T,[c("span",q,U(e(n)?"更换手机号":"绑定手机号"),1),o(e(B),{ref_key:"formRef",ref:r,class:"mt-[35px]",size:"large",model:e(l),rules:g},{default:t(()=>[o(e(m),{prop:"mobile"},{default:t(()=>[o(e(u),{modelValue:e(l).mobile,"onUpdate:modelValue":a[0]||(a[0]=d=>e(l).mobile=d),placeholder:"请输入手机号"},{prepend:t(()=>[o(e(L),{"model-value":"+86",style:{width:"80px"}},{default:t(()=>[o(e(I),{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(e(m),{prop:"code"},{default:t(()=>[o(e(u),{modelValue:e(l).code,"onUpdate:modelValue":a[1]||(a[1]=d=>e(l).code=d),placeholder:"请输入验证码"},{suffix:t(()=>[c("div",G,[o(h,{ref_key:"verificationCodeRef",ref:f,onClickGet:b},null,512)])]),_:1},8,["modelValue"])]),_:1}),o(e(m),{class:"mt-[60px]"},{default:t(()=>[o(e(V),{class:"w-full",type:"primary",onClick:e(k),loading:e(E)},{default:t(()=>a[2]||(a[2]=[j(" 确认 ")])),_:1},8,["onClick","loading"])]),_:1})]),_:1},8,["model"])])])}}});export{W as _};
