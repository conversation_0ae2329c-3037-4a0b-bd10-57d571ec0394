import{cA as n}from"./CmRxzTqw.js";import{_ as d}from"./CXAJ--Vj.js";import{l as m,M as a,N as s,a3 as u,O as o,_,ao as v,$ as f,u as k,a5 as y}from"./CUZG7cWw.js";import{_ as x}from"./DlAUqK2U.js";import"./eFgaMLiC.js";import"./C7tIPmrK.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";/* empty css        *//* empty css        */const V={class:"mt-[15px]"},h={class:"mt-[10px]"},C=["onClick"],b=m({__name:"dalle-style-picker",props:{modelValue:{default:"vivid"}},emits:["update:modelValue"],setup(l,{emit:r}){const i=r,c=l,{modelValue:t}=n(c,i),p=[{value:"vivid",label:"生动"},{value:"natural",label:"自然"}];return t.value="vivid",(B,N)=>(a(),s("div",V,[u(d,{title:"风格选择",tips:"",required:""}),o("div",h,[(a(),s(_,null,v(p,e=>o("div",{key:e.value,class:f(["picture-style-picker rounded-[12px]",{"picture-style-picker__active":e.value===k(t)}]),onClick:$=>t.value=e.value},y(e.label),11,C)),64))])]))}}),O=x(b,[["__scopeId","data-v-eca3acfd"]]);export{O as default};
