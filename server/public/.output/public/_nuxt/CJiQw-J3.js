import{E as V}from"./D5Svi-lq.js";import{E as M}from"./oVx59syQ.js";import{_ as $}from"./CfDE0MAs.js";import{_ as w}from"./eFgaMLiC.js";import{E as B}from"./llRQJmEG.js";import{cA as L}from"./CmRxzTqw.js";/* empty css        */import{n as S,o as l,q as z,u as r,m as _,v as I}from"./-CaxLuW0.js";import{_ as N}from"./CXAJ--Vj.js";import{l as q,E as A,M as a,N as i,a3 as t,a1 as u,O as n,u as s,y as F,_ as j,ao as D,$ as O,a5 as R,a0 as U}from"./CUZG7cWw.js";import{_ as G}from"./DlAUqK2U.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";/* empty css        */import"./DRe575WM.js";import"./DqGsTvs3.js";import"./tONJIxwY.js";import"./C7tIPmrK.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";/* empty css        *//* empty css        */const H={class:"mb-2",style:{"--el-border-radius-base":"12px"}},J={key:0,class:"grid grid-cols-2 gap-4"},K=["onClick"],P={class:"relative rounded-[12px] overflow-hidden"},Q={class:"text-hidden-2 text-center"},T=q({__name:"sd-model",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(f,{emit:g}){const v=g,y=f,{modelValue:m}=L(y,v),c=o=>{o==="clear"?(m.value="",_.value=[]):(m.value=r.value[o].model_name,_.value=r.value[o].loras)},h=o=>{c("clear"),l.value=o,I()};return A(async()=>{S()}),(o,d)=>{const b=V,p=M,x=$,C=w,k=B;return a(),i("div",null,[t(N,{title:"主要模型",required:"",tips:"让AI根据此模型的风格绘制图片，修改合适的描述词和参数可以让生成效果更加精美"}),t(p,null,{default:u(()=>[n("div",H,[t(b,{block:!1,class:"h-[38px] !bg-[transparent]",id:"ddddd",modelValue:s(l),"onUpdate:modelValue":d[0]||(d[0]=e=>F(l)?l.value=e:null),options:s(z),onChange:h},null,8,["modelValue","options"])])]),_:1}),t(p,{"max-height":"360px"},{default:u(()=>[s(r).length>0?(a(),i("div",J,[(a(!0),i(j,null,D(s(r),(e,E)=>(a(),i("div",{key:e.id,class:"flex flex-col gap-2",onClick:W=>c(E)},[n("div",P,[t(x,{class:"rounded-[12px] overflow-hidden bg-[var(--el-bg-color-page)]",src:e.cover,fit:"cover",ratio:[144,100]},null,8,["src"]),n("div",{class:O(["absolute top-0 left-0 bg-[rgba(0,0,0,0.4)] w-full h-full flex justify-center items-center transition-opacity opacity-0",{"opacity-100":e.model_name===s(m)}])},[t(C,{name:"el-icon-CircleCheckFilled",size:20,color:"#fff"})],2)]),n("div",Q,R(e.title||e.model_name),1)],8,K))),128))])):(a(),U(k,{key:1,description:"暂无模型数据","image-size":50}))]),_:1})])}}}),Ve=G(T,[["__scopeId","data-v-25bc3d68"]]);export{Ve as default};
