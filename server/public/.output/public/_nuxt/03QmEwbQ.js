import{E as v}from"./oVx59syQ.js";import"./CmRxzTqw.js";/* empty css        */import{_ as k}from"./Cc32Zcz_.js";import B from"./CcXEqQU4.js";import{_ as N}from"./THcMaKcC.js";import{l as y,b as s,E,M as a,N as I,u as o,a0 as i,Z as l,a1 as c,O as p,a3 as f}from"./CUZG7cWw.js";const b={class:"h-full"},S={class:"p-main"},x={class:"p-main"},D=y({__name:"data-study",props:{id:{type:Number,default:0}},setup(n){const m=s(0),r=s(""),e=s(1),_=(d,t)=>{m.value=d,r.value=t,e.value=3};return E(()=>{e.value=1}),(d,t)=>{const u=v;return a(),I("div",b,[o(e)==1?(a(),i(k,{key:0,class:"h-full",onToImport:t[0]||(t[0]=C=>e.value=2),onToItemList:_,id:n.id},null,8,["id"])):l("",!0),o(e)==2?(a(),i(u,{key:1},{default:c(()=>[p("div",S,[f(B,{id:n.id,onBack:t[1]||(t[1]=()=>{e.value=1})},null,8,["id"])])]),_:1})):l("",!0),o(e)==3?(a(),i(u,{key:2},{default:c(()=>[p("div",x,[f(N,{onBack:t[2]||(t[2]=()=>{e.value=1}),"item-id":o(m),"item-name":o(r)},null,8,["item-id","item-name"])])]),_:1})):l("",!0)])}}});export{D as _};
