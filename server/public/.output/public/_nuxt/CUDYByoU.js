import{_ as ee}from"./eFgaMLiC.js";import{E as te}from"./Zz2DnF66.js";import{b as oe,j as se,E as M,f as h,e as le}from"./CmRxzTqw.js";import{E as ne,a as ie}from"./B7GaOiDz.js";import{E as re}from"./C7tIPmrK.js";import{_ as ae}from"./D5RYEqFL.js";import{E as me}from"./ttFW0yUc.js";import{E as ce}from"./CiabO6Xq.js";import{E as pe}from"./oVx59syQ.js";import"./l0sNRNKZ.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{u as de}from"./DNOp0HuO.js";import{u as z}from"./Bj_9-7Jh.js";import{g as ue,p as _e,a as fe}from"./Bu_nKEGp.js";import{l as xe,b as $,j as ye,r as ve,m as k,c as ge,M as r,N as a,O as o,_ as E,ao as C,u as t,a3 as i,a1 as n,$ as I,a5 as m,a0 as _,Z as x,ag as S}from"./CUZG7cWw.js";import{_ as he}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./u6CVc_ZE.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";const ke={class:"h-full py-[16px] bg-body w-[350px] rounded-[12px] flex flex-col"},be={class:"px-[16px]"},we={class:"bg-page p-[8px] flex rounded-[10px]"},Ee=["onClick"],Ce={class:"mr-[5px]"},Se={class:"flex"},Ve={class:"flex-1 min-h-0"},Fe={class:"p-[16px]"},Le={class:"flex-1"},Me={class:"inline-flex w-[96%]"},ze={class:"flex-1 mr-auto flex items-center"},$e={class:"font-bold text-tx-primary mr-[8px]"},Ie={class:"flex items-center cursor-pointer text-[#999999]"},Ue={class:"flex-1"},Be={class:"flex p-[10px]"},je={class:"flex-1"},Ne={key:0},Te={class:"w-full flex items-center"},De={key:0,class:"flex-1 min-w-0 flex flex-wrap mx-[-6px]"},Re=["onClick"],qe={class:"pt-[100%] relative h-0 rounded-[12px] overflow-hidden"},Ge={class:"absolute inset-0 left-0 top-0"},Ae={key:0,class:"absolute bg-[var(--el-overlay-color-lighter)] inset-0 left-0 top-0 flex items-center justify-center text-white"},Oe={class:"text-center text-xs"},Pe={class:"px-[16px] pt-[16px]"},Qe={key:0,class:"text-sm ml-[4px]"},We={key:1,class:"text-sm ml-[4px] font-normal"},Ze=xe({__name:"index",emits:["update"],setup(He,{emit:U}){const B=U,V=oe(),y=se(),j=$([{mode:0,name:"灵感模式",tips:"只需输入一段歌曲描述，即可生成一首歌"},{mode:1,name:"歌词模式",tips:"输入自己的歌词，即可生成一首歌"}]),N=ye(),e=ve({channel:"go_api",custom_mode:0,title:"",prompt:"",style_id:[],is_style_custom:0,make_instrumental:0,style_custom:"",version:""}),T=()=>{e.is_style_custom=e.is_style_custom?0:1};$(8);const D=c=>{const s=e.style_id.findIndex(u=>u===c);s!==-1?e.style_id.splice(s,1):e.style_id.push(c)},p=k(()=>e.custom_mode===0),R=k(()=>({title:[{required:!0,message:"请输入歌曲名称"}],prompt:[{required:!0,message:`请输入${p.value?"歌曲描述":"歌词"}`}],version:[{required:!0,message:"请选择版本"}]})),b=()=>{y.isLogin||y.toggleShowLogin()},{data:d,refresh:q}=de(()=>ue(),{default(){return{model:{},style:[],imagine:{}}},lazy:!0},"$MBLbBPlEM9");ge(d,c=>{e.channel=c.channel,e.version||(e.version=Object.keys(v.value.version)[0]||"")});const G=k(()=>!p.value&&d.value.imagine.status),v=k(()=>d.value.model[e.channel]||{}),{lockFn:A,isLock:O}=z(async()=>{if(!e.title)return h.msgError("请输入歌曲名称");const c=await _e({prompt:e.title});y.getUser(),e.prompt=c.content}),{lockFn:P,isLock:Q}=z(async()=>{try{if(!e.title)return h.msgError("请输入歌曲名称");if(!e.prompt)return h.msgError(`请输入${p.value?"歌曲描述":"歌词"}`);if(!e.version)return h.msgError("请选择版本");await fe({...e,style_id:e.is_style_custom?[]:e.style_id}),e.prompt="",e.title="",e.style_id=[],e.style_custom="",y.getUser(),q(),B("update")}catch{}finally{}});return(c,s)=>{const u=ee,W=te,Z=le,f=ne,H=re,F=M,w=M,L=ae,J=me,K=ce,X=ie,Y=pe;return r(),a("div",ke,[o("div",be,[o("div",we,[(r(!0),a(E,null,C(t(j),l=>(r(),a("div",{class:I(["flex-1 flex items-center justify-center cursor-pointer py-[7px] text-tx-regular rounded-[10px]",{"!bg-primary !text-white":t(e).custom_mode===l.mode}]),key:l.mode,onClick:g=>t(e).custom_mode=l.mode},[o("span",Ce,m(l.name),1),i(W,{content:l.tips,placement:"bottom",effect:"light",teleported:!0},{default:n(()=>[o("span",Se,[i(u,{name:"el-icon-QuestionFilled"})])]),_:2},1032,["content"])],10,Ee))),128))])]),o("div",Ve,[i(Y,null,{default:n(()=>[o("div",Fe,[i(X,{ref_key:"formRef",ref:N,model:t(e),"label-position":"top",rules:t(R),"show-message":!1},{default:n(()=>[i(f,{prop:"title"},{label:n(()=>s[5]||(s[5]=[o("span",{class:"font-bold text-tx-primary"},"歌曲名称",-1)])),default:n(()=>[o("div",Le,[i(Z,{class:"custom-input",modelValue:t(e).title,"onUpdate:modelValue":s[0]||(s[0]=l=>t(e).title=l),size:"large",placeholder:"给你的歌曲起个好听的名字",onFocus:b},null,8,["modelValue"])])]),_:1}),i(f,{prop:"prompt"},{label:n(()=>[o("div",Me,[o("div",ze,[o("span",$e,m(t(p)?"歌曲描述":"歌词"),1),t(p)?x("",!0):(r(),_(H,{key:0,placement:"right","show-arrow":!1,transition:"custom-popover",width:200,trigger:"hover",content:"建议歌词长度100-1250，太长或太短容易生成失败"},{reference:n(()=>[o("div",Ie,[i(u,{name:"el-icon-QuestionFilled",size:14})])]),_:1}))]),i(F,{link:"",size:"small",onClick:s[1]||(s[1]=l=>t(e).prompt="")},{icon:n(()=>[i(u,{name:"el-icon-Delete",size:12})]),default:n(()=>[s[6]||(s[6]=S(" 清空 "))]),_:1})])]),default:n(()=>[o("div",Ue,[i(L,{modelValue:t(e).prompt,"onUpdate:modelValue":s[2]||(s[2]=l=>t(e).prompt=l),placeholder:`请输入${t(p)?"灵感，例如写一首关于夏天的歌曲":"歌词"}`,contentStyle:{height:"160px"},showWordLimit:"",maxlength:"1300",onFocus:b},{"length-suffix":n(()=>[o("div",Be,[o("div",je,[t(G)?(r(),_(w,{key:0,size:"small",type:"primary",plain:"",style:{border:"none","--el-button-hover-bg-color":`var(
                                                            --el-button-bg-color
                                                        )`,"--el-button-hover-text-color":`var(
                                                            --el-button-text-color
                                                        )`},loading:t(O),onClick:t(A)},{icon:n(()=>[i(u,{name:"local-icon-bulb",size:12})]),default:n(()=>[s[7]||(s[7]=o("span",null," 智能联想 ",-1)),t(d).imagine.price>0?(r(),a("span",Ne,"：消耗"+m(t(d).imagine.price)+m(t(V).getTokenUnit),1)):x("",!0)]),_:1},8,["loading","onClick"])):x("",!0)])])]),_:1},8,["modelValue","placeholder"])])]),_:1}),i(f,{prop:"version"},{label:n(()=>s[8]||(s[8]=[o("span",{class:"font-bold text-tx-primary"}," 选择版本 ",-1)])),default:n(()=>[o("div",null,[(r(!0),a(E,null,C(t(v).version,(l,g)=>(r(),_(w,{key:l,class:I({"!text-primary !border-primary":t(e).version===String(g)}),onClick:Je=>t(e).version=String(g)},{default:n(()=>[S(m(l),1)]),_:2},1032,["class","onClick"]))),128))])]),_:1}),t(p)?(r(),_(f,{key:0},{label:n(()=>s[9]||(s[9]=[o("span",{class:"font-bold text-tx-primary"}," 纯音乐 ",-1)])),default:n(()=>[o("div",null,[i(J,{modelValue:t(e).make_instrumental,"onUpdate:modelValue":s[3]||(s[3]=l=>t(e).make_instrumental=l),"active-value":1,"inactive-value":0},null,8,["modelValue"])])]),_:1})):(r(),_(f,{key:1},{label:n(()=>[o("div",Te,[s[10]||(s[10]=o("span",{class:"font-bold text-tx-primary flex-1"}," 音乐风格 ",-1)),i(F,{link:"",type:"primary",onClick:T},{default:n(()=>[S(m(t(e).is_style_custom?"系统风格":"自定义风格"),1)]),_:1})])]),default:n(()=>[t(e).is_style_custom?(r(),_(L,{key:1,modelValue:t(e).style_custom,"onUpdate:modelValue":s[4]||(s[4]=l=>t(e).style_custom=l),placeholder:"请输入自定义风格，多个风格以,隔开",contentStyle:{height:"120px"},onFocus:b},null,8,["modelValue"])):(r(),a("div",De,[(r(!0),a(E,null,C(t(d).style,l=>(r(),a("div",{class:"w-[25%] px-[6px]",key:l.id},[o("div",{class:"h-full cursor-pointer",onClick:g=>D(l.id)},[o("div",qe,[o("div",Ge,[i(K,{src:l.image,class:"h-full w-full"},null,8,["src"])]),t(e).style_id.includes(l.id)?(r(),a("div",Ae,[i(u,{name:"el-icon-SuccessFilled",size:20})])):x("",!0)]),o("div",Oe,m(l.name),1)],8,Re)]))),128))]))]),_:1}))]),_:1},8,["model","rules"])])]),_:1})]),o("div",Pe,[i(w,{size:"large",class:"w-full",type:"primary",loading:t(Q),onClick:t(P)},{default:n(()=>[o("div",null,[s[11]||(s[11]=o("span",{class:"text-base font-bold"},"立即生成",-1)),t(d).is_member?(r(),a("span",Qe,"会员免费")):t(v).price>0?(r(),a("span",We," 消耗 "+m(t(v).price)+" "+m(t(V).getTokenUnit),1)):x("",!0)])]),_:1},8,["loading","onClick"])])])}}}),St=he(Ze,[["__scopeId","data-v-30a57f21"]]);export{St as default};
