import{E as k,a as w}from"./DHUC3PVh.js";import{E as C}from"./CiabO6Xq.js";import{E as I}from"./llRQJmEG.js";import{E as M}from"./oVx59syQ.js";import{cU as S}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{u as V}from"./DNOp0HuO.js";import{u as B,I as L}from"./DymDsCmz.js";import{e as P}from"./BhXe-NXN.js";import{l as T,r as N,ai as U,M as s,N as a,O as t,a3 as n,a1 as _,u as o,_ as d,ao as u,a0 as z}from"./CUZG7cWw.js";const A={class:"h-full flex flex-col"},O={class:"mt-[5px] px-main"},R={class:"flex-1 min-h-0"},$={class:"p-main pt-0"},D={key:0,class:"flex flex-wrap mx-[-7px]"},F=["onClick"],j={class:"border border-solid border-br-light rounded-md cursor-pointer p-[10px]"},q={class:"pic-wrap h-0 pt-[100%] relative"},G={class:"absolute inset-0"},le=T({__name:"prospect",async setup(H){let l,c;const f=B(),y=[{type:1,label:"系统前景"}],i=N({type:1}),{data:p}=([l,c]=U(()=>V(()=>S(),{lazy:!0},"$MMRLoo0MmU")),l=await l,c(),l),b=r=>{f.addImage(r.url,L.PROSPECT,r)};return(r,m)=>{const x=w,h=k,v=C,E=I,g=M;return s(),a("div",A,[t("div",O,[n(h,{modelValue:o(i).type,"onUpdate:modelValue":m[0]||(m[0]=e=>o(i).type=e)},{default:_(()=>[(s(),a(d,null,u(y,e=>n(x,{key:e.type,label:e.label,name:e.type},null,8,["label","name"])),64))]),_:1},8,["modelValue"])]),t("div",R,[n(g,null,{default:_(()=>[t("div",$,[o(p).length?(s(),a("div",D,[(s(!0),a(d,null,u(o(p),e=>(s(),a("div",{key:e.id,class:"w-[50%]"},[t("div",{class:"px-[7px] mb-[14px]",onClick:J=>b(e)},[t("div",j,[t("div",null,[t("div",q,[t("div",G,[n(v,{src:e.url,class:"w-full h-full",fit:"contain",lazy:""},null,8,["src"])])])])])],8,F)]))),128))])):(s(),z(E,{key:1,image:o(P),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}});export{le as _};
