import{_ as u}from"./CfDE0MAs.js";import{_}from"./eFgaMLiC.js";import{cA as f}from"./CmRxzTqw.js";import{_ as v}from"./CXAJ--Vj.js";import{l as g,M as a,N as l,a3 as t,O as o,_ as h,ao as x,$ as j,u as C,a5 as y}from"./CUZG7cWw.js";const V=""+new URL("mj.BVSeH6C7.png",import.meta.url).href,k=""+new URL("nj.DRE2TxC_.png",import.meta.url).href,b={class:"grid grid-cols-2 gap-4"},w=["onClick"],$={class:"relative rounded-[12px] overflow-hidden"},B={class:"text-hidden-2 text-center"},S=g({__name:"mj-model",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(r,{emit:n}){const c=n,i=r,{modelValue:s}=f(i,c),d=[{value:"mj",title:"真实感强",cover:V},{value:"niji",title:"卡通动漫",cover:k}];return(L,M)=>{const m=u,p=_;return a(),l("div",null,[t(v,{title:"模型选择",required:"",tips:"指定midjourney的渲染模型"}),o("div",b,[(a(),l(h,null,x(d,(e,N)=>o("div",{key:e.cover,class:"flex flex-col gap-2",onClick:I=>s.value=e.value},[o("div",$,[t(m,{class:"rounded-[12px] overflow-hidden bg-[var(--el-bg-color-page)]",src:e.cover,fit:"cover",ratio:[144,100]},null,8,["src"]),o("div",{class:j(["absolute top-0 left-0 bg-[rgba(0,0,0,0.4)] w-full h-full flex justify-center items-center transition-opacity opacity-0",{"opacity-100":e.value===C(s)}])},[t(p,{name:"el-icon-CircleCheckFilled",size:20,color:"#fff"})],2)]),o("div",B,y(e.title),1)],8,w)),64))])])}}});export{S as _};
