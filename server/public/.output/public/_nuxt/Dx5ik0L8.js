import{_ as Q}from"./eFgaMLiC.js";import{I as M,aK as W,X as q,J as F,M as K,aq as Y,H as B,bL as ee,R as se,N as te,O as ae}from"./CmRxzTqw.js";import{u as ne}from"./Dbi96Hzd.js";import{l as E,c as O,q as ie,M as n,N as f,V as w,$ as r,u as e,a as R,b as z,i as re,E as oe,o as le,m as c,r as ce,Z as k,O as C,a6 as D,a0 as u,a1 as $,a2 as ue,a3 as A,a5 as L,ag as H,ao as pe,_ as ve}from"./CUZG7cWw.js";import{useSearch as de}from"./CaJo29OT.js";import{TypeEnums as V}from"./BLV0QRdm.js";const me=M({space:{type:[Number,String],default:""},active:{type:Number,default:0},direction:{type:String,default:"horizontal",values:["horizontal","vertical"]},alignCenter:{type:Boolean},simple:{type:Boolean},finishStatus:{type:String,values:["wait","process","finish","error","success"],default:"finish"},processStatus:{type:String,values:["wait","process","finish","error","success"],default:"process"}}),fe={[W]:(S,d)=>[S,d].every(q)},Se=E({name:"ElSteps"}),_e=E({...Se,props:me,emits:fe,setup(S,{emit:d}){const s=S,l=F("steps"),{children:p,addChild:v,removeChild:a}=ne(R(),"ElStep");return O(p,()=>{p.value.forEach((i,o)=>{i.setIndex(o)})}),ie("ElSteps",{props:s,steps:p,addStep:v,removeStep:a}),O(()=>s.active,(i,o)=>{d(W,i,o)}),(i,o)=>(n(),f("div",{class:r([e(l).b(),e(l).m(i.simple?"simple":i.direction)])},[w(i.$slots,"default")],2))}});var ye=K(_e,[["__file","steps.vue"]]);const he=M({title:{type:String,default:""},icon:{type:Y},description:{type:String,default:""},status:{type:String,values:["","wait","process","finish","error","success"],default:""}}),ge=E({name:"ElStep"}),Ce=E({...ge,props:he,setup(S){const d=S,s=F("step"),l=z(-1),p=z({}),v=z(""),a=re("ElSteps"),i=R();oe(()=>{O([()=>a.props.active,()=>a.props.processStatus,()=>a.props.finishStatus],([t])=>{Z(t)},{immediate:!0})}),le(()=>{a.removeStep(T.uid)});const o=c(()=>d.status||v.value),N=c(()=>{const t=a.steps.value[l.value-1];return t?t.currentStatus:"wait"}),h=c(()=>a.props.alignCenter),_=c(()=>a.props.direction==="vertical"),m=c(()=>a.props.simple),b=c(()=>a.steps.value.length),P=c(()=>{var t;return((t=a.steps.value[b.value-1])==null?void 0:t.uid)===(i==null?void 0:i.uid)}),g=c(()=>m.value?"":a.props.space),G=c(()=>[s.b(),s.is(m.value?"simple":a.props.direction),s.is("flex",P.value&&!g.value&&!h.value),s.is("center",h.value&&!_.value&&!m.value)]),J=c(()=>{const t={flexBasis:q(g.value)?`${g.value}px`:g.value?g.value:`${100/(b.value-(h.value?0:1))}%`};return _.value||P.value&&(t.maxWidth=`${100/b.value}%`),t}),U=t=>{l.value=t},X=t=>{const y=t==="wait",I={transitionDelay:`${y?"-":""}${150*l.value}ms`},x=t===a.props.processStatus||y?0:100;I.borderWidth=x&&!m.value?"1px":0,I[a.props.direction==="vertical"?"height":"width"]=`${x}%`,p.value=I},Z=t=>{t>l.value?v.value=a.props.finishStatus:t===l.value&&N.value!=="error"?v.value=a.props.processStatus:v.value="wait";const y=a.steps.value[l.value-1];y&&y.calcProgress(v.value)},T=ce({uid:i.uid,currentStatus:o,setIndex:U,calcProgress:X});return a.addStep(T),(t,y)=>(n(),f("div",{style:D(e(J)),class:r(e(G))},[k(" icon & line "),C("div",{class:r([e(s).e("head"),e(s).is(e(o))])},[e(m)?k("v-if",!0):(n(),f("div",{key:0,class:r(e(s).e("line"))},[C("i",{class:r(e(s).e("line-inner")),style:D(p.value)},null,6)],2)),C("div",{class:r([e(s).e("icon"),e(s).is(t.icon||t.$slots.icon?"icon":"text")])},[w(t.$slots,"icon",{},()=>[t.icon?(n(),u(e(B),{key:0,class:r(e(s).e("icon-inner"))},{default:$(()=>[(n(),u(ue(t.icon)))]),_:1},8,["class"])):e(o)==="success"?(n(),u(e(B),{key:1,class:r([e(s).e("icon-inner"),e(s).is("status")])},{default:$(()=>[A(e(ee))]),_:1},8,["class"])):e(o)==="error"?(n(),u(e(B),{key:2,class:r([e(s).e("icon-inner"),e(s).is("status")])},{default:$(()=>[A(e(se))]),_:1},8,["class"])):e(m)?k("v-if",!0):(n(),f("div",{key:3,class:r(e(s).e("icon-inner"))},L(l.value+1),3))])],2)],2),k(" title & description "),C("div",{class:r(e(s).e("main"))},[C("div",{class:r([e(s).e("title"),e(s).is(e(o))])},[w(t.$slots,"title",{},()=>[H(L(t.title),1)])],2),e(m)?(n(),f("div",{key:0,class:r(e(s).e("arrow"))},null,2)):(n(),f("div",{key:1,class:r([e(s).e("description"),e(s).is(e(o))])},[w(t.$slots,"description",{},()=>[H(L(t.description),1)])],2))],2)],6))}});var j=K(Ce,[["__file","item.vue"]]);const $e=te(ye,{Step:j}),Ee=ae(j),ze=E({__name:"steps",setup(S){const{options:d,result:s}=de(),l=c(()=>{switch(d.value.type){case V.ALL:return"全网";case V.DOC:return"文档";case V.SCHOLAR:return"学术"}}),p=c(()=>[{title:"问题分析"},{title:`${l.value}搜索`},{title:"整理答案"},{title:"完成"}]);return(v,a)=>{const i=Q,o=Ee,N=$e;return n(),u(N,{active:e(s).status,style:{"max-width":"500px"},"align-center":"","process-status":"finish"},{default:$(()=>[(n(!0),f(ve,null,pe(e(p),(h,_)=>(n(),u(o,{key:_,title:h.title},{icon:$(()=>[e(s).status===_?(n(),u(i,{key:0,name:"el-icon-Loading",style:{animation:"loading-rotate 2s linear infinite"},size:24})):e(s).status>_?(n(),u(i,{key:1,name:"el-icon-SuccessFilled",size:24})):(n(),u(i,{key:2,name:"local-icon-circular",size:24}))]),_:2},1032,["title"]))),128))]),_:1},8,["active"])}}});export{ze as _};
