import{E as f}from"./oVx59syQ.js";import{a_ as _}from"./CmRxzTqw.js";/* empty css        */import{u as m}from"./DNOp0HuO.js";import{n as y}from"./qRM0tN96.js";import{l as h,C as x,b,c as k,M as o,N as a,a3 as C,a1 as v,O as t,_ as w,ao as g,a5 as i,u as s}from"./CUZG7cWw.js";const V={class:"h-full w-full overflow-hidden"},E={class:"flex flex-wrap py-[20px] mx-[-10px]"},B={class:"bg-[#fafafc] flex flex-col items-center rounded-[10px] p-main border border-solid border-br-light h-full dark:bg-page"},D={class:"text-lg"},F={class:"text-[30px] mt-[12px]"},$=h({__name:"data",props:{appId:{}},setup(l){const r=l,{appId:n}=x(r),d=b([{title:"今日对话次数",key:"robot.todayChatCount"},{title:"昨日对话次数",key:"robot.yesterdayChatCount"},{title:"本周对话次数",key:"robot.weekChatCount"},{title:"全部对话次数",key:"robot.wholeChatCount"},{title:"今日访问用户/人",key:"visitor.todayVisitorCount"},{title:"昨日访问用户/人",key:"visitor.yesterdayVisitorCount"},{title:"本周访问用户/人",key:"visitor.weekVisitorCount"},{title:"全部用户/人",key:"visitor.wholeVisitorCount"}]),{data:c,refresh:p}=m(()=>y({robot_id:n.value}),{default(){return{robot:{},visitor:{}}},lazy:!0},"$cREFFgsltp");return k(()=>r.appId,()=>{p()}),(I,N)=>{const u=f;return o(),a("div",V,[C(u,null,{default:v(()=>[t("div",E,[(o(!0),a(w,null,g(s(d),(e,R)=>(o(),a("div",{class:"md:w-[25%] w-[50%] px-[10px] mb-[20px]",key:e.key},[t("div",B,[t("div",D,i(e.title),1),t("div",F,i(s(_)(s(c),e.key)),1)])]))),128))])]),_:1})])}}});export{$ as _};
