import{E as X}from"./D5Svi-lq.js";import{E as Y}from"./DCzKTodP.js";import{_ as ee}from"./eFgaMLiC.js";import{E as te}from"./Zz2DnF66.js";import{_ as oe}from"./CfDE0MAs.js";import{E as se}from"./C7tIPmrK.js";import{E as ae}from"./CiabO6Xq.js";import{E as re}from"./CUKNHy7a.js";import{E as le}from"./C9jirCEY.js";import{b as ne,v as ie,bC as ce,f as E,d as de}from"./CmRxzTqw.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{b as pe}from"./CRNANWso.js";import{D as ue}from"./7Aafpuyn.js";import{d as me}from"./C3s9J3qB.js";import{d as fe,g as _e}from"./B5TkE_dZ.js";import ge from"./CL6uIcv0.js";import{E as ve}from"./oVx59syQ.js";import{l as be,r as he,b as S,j as C,k as ye,M as r,N as c,O as a,a3 as l,u as n,y as ke,a7 as R,a0 as _,a1 as i,Z as d,_ as D,ao as xe,ag as B,a5 as g,n as we}from"./CUZG7cWw.js";import{_ as Ee}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./u6CVc_ZE.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./CXDY_LVT.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";/* empty css        *//* empty css        */import"./DjwCd26w.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./Bj_9-7Jh.js";const Se={class:"bg-body h-full flex-1 rounded-[12px] p-4 flex flex-col gap-4 relative"},Ce={class:"sticky top-0"},Re={class:"mt-4",style:{"--el-border-radius-base":"12px"}},Ne={key:0,"infinite-scroll-distance":"50"},ze={class:"grid grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4"},Ve={class:"flex justify-between"},Te={key:0,class:"flex items-center"},$e=["onClick"],De=["onClick"],Be=["onClick"],Le=["onClick"],je=["onClick"],Ie={class:"relative flex-1"},Ue={class:"bg-[var(--el-bg-color-page)] rounded-[12px] overflow-hidden"},Me={key:0,class:"w-full pb-[75%]"},Oe={class:"w-full h-full px-4 flex flex-col justify-center items-center absolute left-0 top-0"},Pe=["src"],qe={class:"text-xs text-[#798696] dark:text-white line-clamp-3 w-full break-all text-center"},Ae={key:1,class:"video-loading w-full pb-[75%]","element-loading-text":"正在生成中..."},Fe={class:"w-full box-border"},He={class:"line-clamp-1"},Ge={class:"flex justify-between items-center"},Ze={class:"text-[#8794A3]"},Je={key:1,class:"h-full flex items-center justify-center"},Ke=be({__name:"video-result",emits:["regenerate"],setup(Qe,{expose:L,emit:j}){const I=ne(),U=j,o=he({pageNo:1,count:0,pageSize:10,loading:!0,lists:[]}),N={1:{label:"生成中",type:"warning"},3:{label:"生成失败",type:"danger"},2:{label:"生成成功",type:"success"}},u=S(-1),M=[{label:"全部",value:-1},{label:"生成中",value:1},{label:"生成成功",value:2},{label:"生成失败",value:3}],h=S(!1),z=C(null),V=S([]),O=async s=>{u.value=s,T()},y=C(),P=async()=>{clearTimeout(y.value),o.lists.filter(t=>t.status===1).map(t=>t.id).length>0&&(y.value=setTimeout(()=>{x()},6e3))},q=async s=>{await E.confirm("确定删除？"),await fe({id:s}),de.success("删除成功"),x()},A=async(s,t)=>{if(V.value.includes(s)||t){E.msgError("该视频已分享过了！");return}h.value=!0,await we(),z.value.open(s)},F=async(s,t)=>{try{const m=await $request.get({url:s,responseType:"blob",baseURL:""},{isReturnDefaultResponse:!0,apiPrefix:""});console.log(m);const v=new Blob([m._data],{type:m.headers.get("Content-Type")}),p=window.URL.createObjectURL(v);pe(p,t)}catch{E.msgError("文件下载失败")}},k=async()=>{try{const s=await _e({status:u.value,page_no:o.pageNo,page_size:o.pageSize});o.count=s.count,o.pageNo===1&&(o.lists=[]),o.lists.push(...s.lists)}catch{}finally{o.loading=!1,P()}},H=()=>{o.count>=o.pageNo*o.pageSize&&(o.pageNo++,k())},G=s=>{const t={type:s.type,prompt:s.prompt,scale:s.scale,image:s.image,style_id:s.style_id};U("regenerate",t)},x=async()=>{o.pageSize=o.pageNo*o.pageSize,o.pageNo=1,await k()},w=C(),T=async()=>{var s;o.loading=!0,o.pageSize=10,o.pageNo=1,await k(),(s=w.value)==null||s.setScrollTop(0)};return T(),ye(()=>{clearTimeout(y.value)}),L({refresh:async()=>{u.value=-1,await x(),w.value.setScrollTop(0)}}),(s,t)=>{const m=X,v=Y,p=ee,f=te,Z=oe,J=se,K=ae,Q=re,$=ie,W=le;return r(),c("div",Se,[a("div",Ce,[t[3]||(t[3]=a("div",{class:"border-b border-b-[#eff0f2] dark:border-[#333333] pb-4 text-2xl font-medium"}," 生成记录 ",-1)),a("div",Re,[l(m,{class:"task-type !bg-[transparent]",modelValue:n(u),"onUpdate:modelValue":t[0]||(t[0]=e=>ke(u)?u.value=e:null),options:M,onChange:O},null,8,["modelValue"])])]),R((r(),_(n(ve),{class:"video-result flex-1",ref_key:"scrollBarRef",ref:w},{default:i(()=>[n(o).lists.length>0?R((r(),c("div",Ne,[a("div",ze,[(r(!0),c(D,null,xe(n(o).lists,(e,We)=>(r(),c("div",{key:e.id,class:"rounded-[12px] p-4 flex flex-col gap-2 border border-[#eff0f2] dark:border-[#333333]"},[a("div",Ve,[l(v,{type:N[e.status].type,effect:"light"},{default:i(()=>[B(g(N[e.status].label),1)]),_:2},1032,["type"]),e.status!==1||e.status===0?(r(),c("div",Te,[e.status===2?(r(),_(f,{key:0,effect:"dark",content:"复制提示词",placement:"bottom"},{default:i(()=>[a("div",{onClick:b=>n(ce)(e.prompt)},[l(p,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-CopyDocument",size:"18",color:"#556477"})],8,$e)]),_:2},1024)):d("",!0),e.status===2?(r(),c(D,{key:1},[l(f,{effect:"dark",content:"下载视频",placement:"bottom"},{default:i(()=>[a("div",{onClick:b=>F(e.video_url,"视频")},[l(p,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Download",size:"18",color:"#556477"})],8,De)]),_:2},1024),n(I).getSquareConfig.video_award.is_open?(r(),_(f,{key:0,effect:"dark",content:"分享至广场",placement:"bottom"},{default:i(()=>[a("div",{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md pb-[7px] p-1 box-content",onClick:b=>A(e.id,e.is_share)},[l(p,{name:"local-icon-share",size:"17",color:"#556477"})],8,Be)]),_:2},1024)):d("",!0)],64)):d("",!0),l(f,{effect:"dark",content:"重新生成",placement:"bottom"},{default:i(()=>[a("div",{onClick:b=>G(e)},[l(p,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-RefreshRight",size:"18",color:"#556477"})],8,Le)]),_:2},1024),l(f,{effect:"dark",content:"删除",placement:"bottom"},{default:i(()=>[a("div",{onClick:b=>q(e.id)},[l(p,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Delete",size:"18",color:"#556477"})],8,je)]),_:2},1024)])):d("",!0)]),a("div",Ie,[a("div",Ue,[e.status===2?(r(),_(Z,{key:0,src:e.video_url,type:"video",ratio:[4,3]},null,8,["src"])):d("",!0)]),e.status===3?(r(),c("div",Me,[a("div",Oe,[a("img",{class:"w-1/2 mb-4",src:n(me),alt:"生成视频失败"},null,8,Pe),t[4]||(t[4]=a("div",{class:"my-[10px]"},"生成视频失败",-1)),a("div",qe," 错误信息："+g(e.fail_reason),1)])])):d("",!0),e.status===0||e.status===1?R((r(),c("div",Ae,null,512)),[[$,!0]]):d("",!0)]),l(J,{placement:"bottom",title:"提示词","show-arrow":!1,transition:"custom-popover",width:"300px",trigger:"hover",content:e.prompt},{reference:i(()=>[a("div",Fe,[a("div",He,g(e.prompt),1)])]),_:2},1032,["content"]),a("div",Ge,[a("span",Ze,g(e.create_time),1),l(v,null,{default:i(()=>[B(g(e.type_desc),1)]),_:2},1024)])]))),128))])])),[[W,H]]):n(o).loading?d("",!0):(r(),c("div",Je,[l(Q,null,{icon:i(()=>[l(K,{class:"w-[150px] dark:opacity-60",src:n(ue)},null,8,["src"])]),title:i(()=>t[5]||(t[5]=[a("div",{class:"text-xl"},"当前任务是空的哦",-1)])),"sub-title":i(()=>t[6]||(t[6]=[a("div",{class:"text-info"}," 在左侧输入描述，创建你的作品吧! ",-1)])),_:1})]))]),_:1})),[[$,n(o).loading]]),n(h)?(r(),_(ge,{key:0,ref_key:"shareRef",ref:z,onClose:t[1]||(t[1]=e=>h.value=!1),onSuccess:t[2]||(t[2]=e=>n(V).push(e))},null,512)):d("",!0)])}}}),Ht=Ee(Ke,[["__scopeId","data-v-05d8ce9d"]]);export{Ht as default};
