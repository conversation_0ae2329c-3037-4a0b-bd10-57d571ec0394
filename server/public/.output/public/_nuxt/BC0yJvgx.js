import{E as g}from"./CiabO6Xq.js";import{E as d}from"./ArzC3z2d.js";import{j as f,b as y,d1 as s}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import E from"./BVTdJXKU.js";import{_ as I}from"./DqKCLwOu.js";import h from"./ezienwJz.js";import{_ as x}from"./DnaAw8MZ.js";import{_ as T}from"./D8CLlV3f.js";import{l as w,m as L,c as k,M as t,a0 as p,a1 as B,u as o,y as O,O as m,N as P,a3 as N,Z as i}from"./CUZG7cWw.js";const D={class:"flex"},b={key:0},v={class:"flex-1 text-tx-primary flex flex-col w-[400px]"},j=w({__name:"index",setup(M){const e=f(),n=y(),l=L({get(){return e.showLogin},set(a){e.showLogin=a}});return k(()=>e.showLogin,a=>{a||(e.temToken=null)}),(a,r)=>{const c=g,u=d;return t(),p(u,{modelValue:o(l),"onUpdate:modelValue":r[0]||(r[0]=_=>O(l)?l.value=_:null),width:"auto",class:"login-popup","append-to-body":"","show-close":o(e).loginPopupType!==o(s).BIND_MOBILE,"close-on-click-modal":!1,style:{"border-radius":"16px",overflow:"hidden",padding:"0"}},{default:B(()=>[m("div",D,[o(n).getWebsiteConfig.pc_login_image&&o(e).loginPopupType==o(s).LOGIN&&!o(n).isMobile?(t(),P("div",b,[N(c,{class:"w-[320px] h-full",fit:"cover",src:o(n).getWebsiteConfig.pc_login_image},null,8,["src"])])):i("",!0),m("div",v,[o(e).loginPopupType==o(s).LOGIN?(t(),p(E,{key:0})):i("",!0),o(e).loginPopupType==o(s).FORGOT_PWD_MAILBOX||o(e).loginPopupType==o(s).FORGOT_PWD_MOBILE?(t(),p(I,{key:1})):i("",!0),o(e).loginPopupType==o(s).REGISTER?(t(),p(h,{key:2})):i("",!0),o(e).loginPopupType==o(s).BIND_MOBILE?(t(),p(x,{key:3})):i("",!0),o(e).loginPopupType==o(s).BIND_WEIXIN?(t(),p(T,{key:4})):i("",!0)])])]),_:1},8,["modelValue","show-close"])}}});export{j as _};
