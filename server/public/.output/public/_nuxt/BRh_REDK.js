import{j as Z,l as G,bx as H,cV as J,f as C,cW as Q,cK as X,E as R,e as Y,v as ee}from"./CmRxzTqw.js";import{_ as te}from"./CbQsrhNE.js";import{_ as oe}from"./eFgaMLiC.js";import{a as se,E as ne}from"./sfCUuwOk.js";import{a as ie,_ as ae}from"./BYMcWg3Q.js";import{E as le}from"./DikNcrXK.js";import{_ as re}from"./DJi8L2lq.js";import{E as me}from"./oVx59syQ.js";/* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{u as de}from"./DRe575WM.js";import{a as pe}from"./CiYvFM4x.js";import{l as ce,r as T,b as _e,ai as ue,c as fe,k as he,M as l,N as _,a3 as o,a1 as s,u as n,O as i,a5 as u,ag as d,a7 as ge,a0 as f,Z as v,_ as ke,y as ve,n as ye}from"./CUZG7cWw.js";import"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./DSuLZIN6.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./HA5sEeDs.js";import"./Ddo5WWE5.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./BscXL5XZ.js";/* empty css        */import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./C-n0m2hZ.js";import"./CXDY_LVT.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D6yUe_Nr.js";/* empty css        */const xe={class:"h-full"},be={class:"p-main"},Ce={class:"bg-white rounded-lg p-[20px]"},Ve={class:"border-b border-solid border-br-light pb-[10px]"},we={class:"text-tx-secondary ml-[10px]"},Ee={class:"py-main"},$e={class:"flex items-center flex-wrap"},Se={class:"flex-1"},Be=["href"],Re={class:"flex-none flex"},Te={class:"flex-1 min-h-0"},De={class:"flex items-center"},Fe={class:"ml-2"},Ne={key:0},Ue={key:1,class:"text-warning"},Ie={key:2,class:"text-success"},Le=["onClick"],Pe=["href"],je={class:"flex justify-end mt-4"},Rt=ce({__name:"video_compositing",async setup(ze){let V,w;const E=Z();G();const h=T({show:!1,url:""}),y=T({keyword:""}),{pager:r,getLists:p,resetPage:D}=de({fetchFun:J,params:y}),x=_e([]),F=a=>{x.value=a.map(t=>t.id)},$=async a=>{await C.confirm("确定要删除？"),await Q({ids:a}),p()},N=async(a,t)=>{const{value:g}=await C.prompt("修改视频名称","",{inputValue:t});await X({name:g,id:a}),p()},U=H(()=>{D()},1e3),I=a=>{C.confirm(`错误信息：${a}`,"失败原因",{showConfirmButton:!1,type:"error",cancelButtonText:"关闭"})},L=async a=>{h.show=!0,await ye(),h.url=a},P=(a,t)=>{pe(a,t)};[V,w]=ue(()=>p()),await V,w();let b=null;return fe(()=>r.extend,a=>{a.unfinished_num>0?b=setTimeout(()=>{p()},30*1e3):E.getUser()},{immediate:!0}),he(()=>{b&&clearTimeout(b)}),(a,t)=>{const g=R,S=te,B=oe,k=R,j=Y,m=se,z=ae,M=le,q=ne,A=re,K=me,O=ie,W=ee;return l(),_("div",xe,[o(K,null,{default:s(()=>[i("div",be,[i("div",Ce,[i("div",Ve,[t[4]||(t[4]=i("span",{class:"text-2xl font-medium"},"视频合成",-1)),i("span",we," 剩余合成时长："+u(n(E).userInfo.video_num)+"分钟 ",1)]),i("div",Ee,[i("div",$e,[i("div",Se,[o(S,{custom:"",to:"/digital_human/design"},{default:s(({href:e})=>[i("a",{href:e,class:"mr-[10px]",target:"_blank"},[o(g,{type:"primary"},{default:s(()=>t[5]||(t[5]=[d("新建视频")])),_:1})],8,Be)]),_:1}),o(g,{disabled:!n(x).length,onClick:t[0]||(t[0]=e=>$(n(x)))},{default:s(()=>t[6]||(t[6]=[d("批量删除")])),_:1},8,["disabled"])]),i("div",Re,[o(j,{modelValue:n(y).keyword,"onUpdate:modelValue":t[1]||(t[1]=e=>n(y).keyword=e),placeholder:"搜索",onInput:n(U)},{prepend:s(()=>[o(k,{loading:n(r).loading},{icon:s(()=>[o(B,{name:"el-icon-Search"})]),_:1},8,["loading"])]),_:1},8,["modelValue","onInput"]),o(g,{circle:"",plain:"",class:"ml-[10px]",onClick:n(p)},{icon:s(()=>[o(B,{name:"el-icon-Refresh"})]),_:1},8,["onClick"])])])]),i("div",Te,[ge((l(),f(q,{height:"100%",size:"large",data:n(r).lists,onSelectionChange:F},{default:s(()=>[o(m,{type:"selection",width:"55"}),o(m,{label:"文件名称","min-width":"200"},{default:s(({row:e})=>[i("div",De,[e.video_url?(l(),f(z,{key:0,onClick:c=>L(e.video_url),class:"cursor-pointer",type:"video","file-size":"70px",uri:e.video_url},null,8,["onClick","uri"])):v("",!0),i("div",Fe,u(e.name),1)])]),_:1}),o(m,{label:"生成进度","min-width":"200"},{default:s(({row:e})=>[e.status!==1?(l(),f(M,{key:0,percentage:e.synthetic_schedule},null,8,["percentage"])):(l(),_(ke,{key:1},[d("-")],64))]),_:1}),o(m,{label:"任务状态","min-width":"120"},{default:s(({row:e})=>[e.status==1?(l(),_("span",Ne,u(e.status_desc),1)):e.status==2?(l(),_("span",Ue,u(e.status_desc),1)):e.status==3?(l(),_("span",Ie,u(e.status_desc),1)):(l(),_("span",{key:3,class:"text-error cursor-pointer",onClick:c=>I(e.fail_reason)},u(e.status_desc),9,Le))]),_:1}),o(m,{label:"消耗时长",prop:"consume_time","min-width":"120"}),o(m,{label:"最后更新时间",prop:"update_time","min-width":"180","show-tooltip-when-overflow":""}),o(m,{label:"操作",width:"180",fixed:"right"},{default:s(({row:e})=>[e.status==3&&e.video_url?(l(),f(k,{key:0,type:"primary",link:"",onClick:c=>P(e.video_url,e.name)},{default:s(()=>t[7]||(t[7]=[d(" 下载 ")])),_:2},1032,["onClick"])):v("",!0),e.status==1||e.status==4?(l(),f(S,{key:1,custom:"",to:`/digital_human/design?id=${e.id}`},{default:s(({href:c})=>[i("a",{href:c,class:"mr-[10px]",target:"_blank"},[o(k,{type:"primary",link:""},{default:s(()=>t[8]||(t[8]=[d(" 编辑 ")])),_:1})],8,Pe)]),_:2},1032,["to"])):v("",!0),o(k,{type:"primary",link:"",onClick:c=>N(e.id,e.name)},{default:s(()=>t[9]||(t[9]=[d(" 重命名 ")])),_:2},1032,["onClick"]),e.delete_btn?(l(),f(k,{key:2,type:"danger",link:"",onClick:c=>$([e.id])},{default:s(()=>t[10]||(t[10]=[d(" 删除 ")])),_:2},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"])),[[W,n(r).loading]])]),i("div",je,[o(A,{modelValue:n(r),"onUpdate:modelValue":t[2]||(t[2]=e=>ve(r)?r.value=e:null),onChange:n(p)},null,8,["modelValue","onChange"])])])])]),_:1}),o(O,{modelValue:n(h).show,"onUpdate:modelValue":t[3]||(t[3]=e=>n(h).show=e),url:n(h).url,type:"video"},null,8,["modelValue","url"])])}}});export{Rt as default};
