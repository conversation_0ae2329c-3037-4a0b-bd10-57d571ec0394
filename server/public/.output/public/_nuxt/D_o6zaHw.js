import{e as k,E as P}from"./CmRxzTqw.js";import{_ as b}from"./eFgaMLiC.js";import{_ as D}from"./D-n7HwjM.js";import{_ as U}from"./DUp2AN3X.js";import{P as C}from"./CaNlADry.js";import{u as B}from"./Bj_9-7Jh.js";import{n as E,o as R,p as j}from"./Dl64kDm5.js";import{l as q,j as F,b as r,c as I,M,a0 as N,a1 as d,u as l,O as i,a3 as a,y as O,ag as $}from"./CUZG7cWw.js";const z={class:"grid grid-cols-2 gap-x-[20px]"},L={class:"mt-4"},T={class:"flex flex-col items-center justify-center"},A={class:"mt-4"},G={class:"mt-4"},ee=q({__name:"editPop",emits:["success","close"],setup(H,{expose:f,emit:_}){const c=_,p=F(),n=r(""),o=r({kb_id:"",fd_id:"",question:"",answer:"",files:[],images:[],video:[],uuid:""});I(n,t=>{o.value.video=[{url:t,name:""}]});const{lockFn:v}=B(async()=>{console.log(o.value),o.value.uuid?await E({...o.value}):await R({...o.value}),c("success")}),x=async()=>{var e;const t=await j({uuid:o.value.uuid});Object.keys(o.value).map(u=>{o.value[u]=t[u]}),n.value=((e=t.video[0])==null?void 0:e.url)||"",console.log(o.value)};return f({open:t=>{p.value.open(),[o.value.kb_id,o.value.fd_id,o.value.uuid]=[t.kb_id,t.fd_id,t.uuid||""],t.hasOwnProperty("uuid")&&x()}}),(t,e)=>{const u=k,w=b,m=D,y=U,V=P,g=C;return M(),N(g,{ref_key:"popRef",ref:p,title:"录入数据",width:"800px",async:"",onConfirm:l(v),onClose:e[5]||(e[5]=s=>t.$emit("close"))},{default:d(()=>[i("div",null,[i("div",z,[a(u,{modelValue:l(o).question,"onUpdate:modelValue":e[0]||(e[0]=s=>l(o).question=s),type:"textarea",placeholder:"请输入文档内容，你可以理解为提问的问题（必填）",rows:"10"},null,8,["modelValue"]),a(u,{modelValue:l(o).answer,"onUpdate:modelValue":e[1]||(e[1]=s=>l(o).answer=s),type:"textarea",placeholder:"请填入补充内容，你可以理解为问题的答案",rows:"10"},null,8,["modelValue"])]),i("div",L,[a(m,{files:l(o).images,"onUpdate:files":e[2]||(e[2]=s=>l(o).images=s),type:"image","list-type":"picture-card",limit:9,multiple:"","show-file-list":""},{default:d(()=>[i("div",T,[a(w,{name:"el-icon-Plus",size:20}),e[6]||(e[6]=i("div",{class:"text-info mt-2 text-sm"},"上传图片",-1))])]),_:1},8,["files"]),e[7]||(e[7]=i("div",{class:"form-tips"},"最多上传9张",-1))]),i("div",A,[a(y,{modelValue:l(n),"onUpdate:modelValue":e[3]||(e[3]=s=>O(n)?n.value=s:null),size:"80px"},null,8,["modelValue"]),e[8]||(e[8]=i("div",{class:"form-tips"},"格式为MP4，大小不能超过20M",-1))]),i("div",G,[a(m,{files:l(o).files,"onUpdate:files":e[4]||(e[4]=s=>l(o).files=s),type:"file","show-file-list":""},{tip:d(()=>e[10]||(e[10]=[i("div",{class:"el-upload__tip"}," 支持上传PDF、docx、excel、等文件格式 ",-1)])),default:d(()=>[a(V,null,{default:d(()=>e[9]||(e[9]=[$("上传附件")])),_:1})]),_:1},8,["files"])])])]),_:1},8,["onConfirm"])}}});export{ee as _};
