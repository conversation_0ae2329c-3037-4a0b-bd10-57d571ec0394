import{l as R,i as B,m as v,M as f,N as T,O as k,V as r,$ as t,u as e,a5 as H,a3 as w,a1 as n,a0 as S,a2 as W,Z as L,a6 as P,Y as Z,b as F,q as G,a7 as Q,W as X,aq as _,a8 as x,a9 as ee,as as oe}from"./CUZG7cWw.js";import{a5 as se,aj as ae,ak as le,H as te,M as q,al as ne,am as re,J as ie,an as de,ao as ce,ap as fe,N as ue}from"./CmRxzTqw.js";import{d as pe,a as ge,b as me,c as ve,u as be}from"./CDwN27aR.js";import{c as ye}from"./C3XldtMC.js";const z=Symbol("dialogInjectionKey"),Ce=["aria-level"],he=["aria-label"],ke=["id"],we=R({name:"ElDialogContent"}),Re=R({...we,props:pe,emits:ge,setup($){const l=$,{t:u}=se(),{Close:E}=ne,{dialogRef:i,headerRef:p,bodyId:A,ns:s,style:g}=B(z),{focusTrapRef:b}=B(ae),y=v(()=>[s.b(),s.is("fullscreen",l.fullscreen),s.is("draggable",l.draggable),s.is("align-center",l.alignCenter),{[s.m("center")]:l.center}]),D=ye(b,i),I=v(()=>l.draggable),C=v(()=>l.overflow);return le(i,p,I,C),(a,h)=>(f(),T("div",{ref:e(D),class:t(e(y)),style:P(e(g)),tabindex:"-1"},[k("header",{ref_key:"headerRef",ref:p,class:t([e(s).e("header"),{"show-close":a.showClose}])},[r(a.$slots,"header",{},()=>[k("span",{role:"heading","aria-level":a.ariaLevel,class:t(e(s).e("title"))},H(a.title),11,Ce)]),a.showClose?(f(),T("button",{key:0,"aria-label":e(u)("el.dialog.close"),class:t(e(s).e("headerbtn")),type:"button",onClick:h[0]||(h[0]=M=>a.$emit("close"))},[w(e(te),{class:t(e(s).e("close"))},{default:n(()=>[(f(),S(W(a.closeIcon||e(E))))]),_:1},8,["class"])],10,he)):L("v-if",!0)],2),k("div",{id:e(A),class:t(e(s).e("body"))},[r(a.$slots,"default")],10,ke),a.$slots.footer?(f(),T("footer",{key:0,class:t(e(s).e("footer"))},[r(a.$slots,"footer")],2)):L("v-if",!0)],6))}});var $e=q(Re,[["__file","dialog-content.vue"]]);const Ee=["aria-label","aria-labelledby","aria-describedby"],Ae=R({name:"ElDialog",inheritAttrs:!1}),De=R({...Ae,props:me,emits:ve,setup($,{expose:l}){const u=$,E=Z();re({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},v(()=>!!E.title));const i=ie("dialog"),p=F(),A=F(),s=F(),{visible:g,titleId:b,bodyId:y,style:D,overlayDialogStyle:I,rendered:C,zIndex:a,afterEnter:h,afterLeave:M,beforeLeave:O,handleClose:N,onModalClick:j,onOpenAutoFocus:K,onCloseAutoFocus:V,onCloseRequested:J,onFocusoutPrevented:U}=be(u,p);G(z,{dialogRef:p,headerRef:A,bodyId:y,ns:i,rendered:C,style:D});const d=fe(j),Y=v(()=>u.draggable&&!u.fullscreen);return l({visible:g,dialogContentRef:s}),(o,c)=>(f(),S(oe,{to:o.appendTo,disabled:o.appendTo!=="body"?!1:!o.appendToBody},[w(ee,{name:"dialog-fade",onAfterEnter:e(h),onAfterLeave:e(M),onBeforeLeave:e(O),persisted:""},{default:n(()=>[Q(w(e(de),{"custom-mask-event":"",mask:o.modal,"overlay-class":o.modalClass,"z-index":e(a)},{default:n(()=>[k("div",{role:"dialog","aria-modal":"true","aria-label":o.title||void 0,"aria-labelledby":o.title?void 0:e(b),"aria-describedby":e(y),class:t(`${e(i).namespace.value}-overlay-dialog`),style:P(e(I)),onClick:c[0]||(c[0]=(...m)=>e(d).onClick&&e(d).onClick(...m)),onMousedown:c[1]||(c[1]=(...m)=>e(d).onMousedown&&e(d).onMousedown(...m)),onMouseup:c[2]||(c[2]=(...m)=>e(d).onMouseup&&e(d).onMouseup(...m))},[w(e(ce),{loop:"",trapped:e(g),"focus-start-el":"container",onFocusAfterTrapped:e(K),onFocusAfterReleased:e(V),onFocusoutPrevented:e(U),onReleaseRequested:e(J)},{default:n(()=>[e(C)?(f(),S($e,X({key:0,ref_key:"dialogContentRef",ref:s},o.$attrs,{center:o.center,"align-center":o.alignCenter,"close-icon":o.closeIcon,draggable:e(Y),overflow:o.overflow,fullscreen:o.fullscreen,"show-close":o.showClose,title:o.title,"aria-level":o.headerAriaLevel,onClose:e(N)}),_({header:n(()=>[o.$slots.title?r(o.$slots,"title",{key:1}):r(o.$slots,"header",{key:0,close:e(N),titleId:e(b),titleClass:e(i).e("title")})]),default:n(()=>[r(o.$slots,"default")]),_:2},[o.$slots.footer?{name:"footer",fn:n(()=>[r(o.$slots,"footer")])}:void 0]),1040,["center","align-center","close-icon","draggable","overflow","fullscreen","show-close","title","aria-level","onClose"])):L("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,Ee)]),_:3},8,["mask","overlay-class","z-index"]),[[x,e(g)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["to","disabled"]))}});var Ie=q(De,[["__file","dialog.vue"]]);const Me=ue(Ie);export{Me as E};
