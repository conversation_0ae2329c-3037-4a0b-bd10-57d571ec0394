import{I,V as j,a2 as oe,S as J,J as D,Z as le,M as re,cw as ge,cx as Ne,H as V,a7 as Te,a8 as Ce,R as Pe,ad as F,aV as we,ar as ie,aR as ae,X as Se,cy as se,N as $e,O as Ee}from"./CmRxzTqw.js";import{c as O}from"./7tQUKVT9.js";import{l as A,i as G,b as g,c as z,n as Q,M as ce,N as ue,$ as de,u as x,a6 as xe,a as K,m as L,E as be,ar as Be,a3 as b,q as Oe,V as Z,H as ke,Y as Re,r as ze,k as Ae,a7 as Me,a8 as Ve,Z as Fe}from"./CUZG7cWw.js";import{u as Le}from"./Dbi96Hzd.js";const H=Symbol("tabsRootContextKey"),Ie=I({tabs:{type:j(Array),default:()=>oe([])}}),ve="ElTabBar",De=A({name:ve}),Ke=A({...De,props:Ie,setup(e,{expose:l}){const m=e,k=K(),c=G(H);c||J(ve,"<el-tabs><el-tab-bar /></el-tabs>");const t=D("tabs"),w=g(),B=g(),u=()=>{let i=0,C=0;const d=["top","bottom"].includes(c.props.tabPosition)?"width":"height",n=d==="width"?"x":"y",E=n==="x"?"left":"top";return m.tabs.every(a=>{var f,P;const N=(P=(f=k.parent)==null?void 0:f.refs)==null?void 0:P[`tab-${a.uid}`];if(!N)return!1;if(!a.active)return!0;i=N[`offset${O(E)}`],C=N[`client${O(d)}`];const S=window.getComputedStyle(N);return d==="width"&&(m.tabs.length>1&&(C-=Number.parseFloat(S.paddingLeft)+Number.parseFloat(S.paddingRight)),i+=Number.parseFloat(S.paddingLeft)),!1}),{[d]:`${C}px`,transform:`translate${O(n)}(${i}px)`}},v=()=>B.value=u();return z(()=>m.tabs,async()=>{await Q(),v()},{immediate:!0}),le(w,()=>v()),l({ref:w,update:v}),(i,C)=>(ce(),ue("div",{ref_key:"barRef",ref:w,class:de([x(t).e("active-bar"),x(t).is(x(c).props.tabPosition)]),style:xe(B.value)},null,6))}});var He=re(Ke,[["__file","tab-bar.vue"]]);const Ue=I({panes:{type:j(Array),default:()=>oe([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),qe={tabClick:(e,l,m)=>m instanceof Event,tabRemove:(e,l)=>l instanceof Event},ne="ElTabNav",We=A({name:ne,props:Ue,emits:qe,setup(e,{expose:l,emit:m}){const k=K(),c=G(H);c||J(ne,"<el-tabs><tab-nav /></el-tabs>");const t=D("tabs"),w=ge(),B=Ne(),u=g(),v=g(),i=g(),C=g(),d=g(!1),n=g(0),E=g(!1),a=g(!0),f=L(()=>["top","bottom"].includes(c.props.tabPosition)?"width":"height"),P=L(()=>({transform:`translate${f.value==="width"?"X":"Y"}(-${n.value}px)`})),N=()=>{if(!u.value)return;const o=u.value[`offset${O(f.value)}`],r=n.value;if(!r)return;const s=r>o?r-o:0;n.value=s},S=()=>{if(!u.value||!v.value)return;const o=v.value[`offset${O(f.value)}`],r=u.value[`offset${O(f.value)}`],s=n.value;if(o-s<=r)return;const y=o-s>r*2?s+r:o-r;n.value=y},M=async()=>{const o=v.value;if(!d.value||!i.value||!u.value||!o)return;await Q();const r=i.value.querySelector(".is-active");if(!r)return;const s=u.value,y=["top","bottom"].includes(c.props.tabPosition),_=r.getBoundingClientRect(),h=s.getBoundingClientRect(),$=y?o.offsetWidth-h.width:o.offsetHeight-h.height,T=n.value;let p=T;y?(_.left<h.left&&(p=T-(h.left-_.left)),_.right>h.right&&(p=T+_.right-h.right)):(_.top<h.top&&(p=T-(h.top-_.top)),_.bottom>h.bottom&&(p=T+(_.bottom-h.bottom))),p=Math.max(p,0),n.value=Math.min(p,$)},ee=()=>{var o;if(!v.value||!u.value)return;e.stretch&&((o=C.value)==null||o.update());const r=v.value[`offset${O(f.value)}`],s=u.value[`offset${O(f.value)}`],y=n.value;s<r?(d.value=d.value||{},d.value.prev=y,d.value.next=y+s<r,r-y<s&&(n.value=r-s)):(d.value=!1,y>0&&(n.value=0))},me=o=>{const r=o.code,{up:s,down:y,left:_,right:h}=F;if(![s,y,_,h].includes(r))return;const $=Array.from(o.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)")),T=$.indexOf(o.target);let p;r===_||r===s?T===0?p=$.length-1:p=T-1:T<$.length-1?p=T+1:p=0,$[p].focus({preventScroll:!0}),$[p].click(),te()},te=()=>{a.value&&(E.value=!0)},U=()=>E.value=!1;return z(w,o=>{o==="hidden"?a.value=!1:o==="visible"&&setTimeout(()=>a.value=!0,50)}),z(B,o=>{o?setTimeout(()=>a.value=!0,50):a.value=!1}),le(i,ee),be(()=>setTimeout(()=>M(),0)),Be(()=>ee()),l({scrollToActiveTab:M,removeFocus:U}),z(()=>e.panes,()=>k.update(),{flush:"post",deep:!0}),()=>{const o=d.value?[b("span",{class:[t.e("nav-prev"),t.is("disabled",!d.value.prev)],onClick:N},[b(V,null,{default:()=>[b(Te,null,null)]})]),b("span",{class:[t.e("nav-next"),t.is("disabled",!d.value.next)],onClick:S},[b(V,null,{default:()=>[b(Ce,null,null)]})])]:null,r=e.panes.map((s,y)=>{var _,h,$,T;const p=s.uid,q=s.props.disabled,W=(h=(_=s.props.name)!=null?_:s.index)!=null?h:`${y}`,X=!q&&(s.isClosable||e.editable);s.index=`${y}`;const he=X?b(V,{class:"is-icon-close",onClick:R=>m("tabRemove",s,R)},{default:()=>[b(Pe,null,null)]}):null,ye=((T=($=s.slots).label)==null?void 0:T.call($))||s.props.label,_e=!q&&s.active?0:-1;return b("div",{ref:`tab-${p}`,class:[t.e("item"),t.is(c.props.tabPosition),t.is("active",s.active),t.is("disabled",q),t.is("closable",X),t.is("focus",E.value)],id:`tab-${W}`,key:`tab-${p}`,"aria-controls":`pane-${W}`,role:"tab","aria-selected":s.active,tabindex:_e,onFocus:()=>te(),onBlur:()=>U(),onClick:R=>{U(),m("tabClick",s,W,R)},onKeydown:R=>{X&&(R.code===F.delete||R.code===F.backspace)&&m("tabRemove",s,R)}},[ye,he])});return b("div",{ref:i,class:[t.e("nav-wrap"),t.is("scrollable",!!d.value),t.is(c.props.tabPosition)]},[o,b("div",{class:t.e("nav-scroll"),ref:u},[b("div",{class:[t.e("nav"),t.is(c.props.tabPosition),t.is("stretch",e.stretch&&["top","bottom"].includes(c.props.tabPosition))],ref:v,style:P.value,role:"tablist",onKeydown:me},[e.type?null:b(He,{ref:C,tabs:[...e.panes]},null),r])])])}}}),Xe=I({type:{type:String,values:["card","border-card",""],default:""},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:j(Function),default:()=>!0},stretch:Boolean}),Y=e=>ke(e)||Se(e),Ye={[ie]:e=>Y(e),tabClick:(e,l)=>l instanceof Event,tabChange:e=>Y(e),edit:(e,l)=>["remove","add"].includes(l),tabRemove:e=>Y(e),tabAdd:()=>!0},Ze=A({name:"ElTabs",props:Xe,emits:Ye,setup(e,{emit:l,slots:m,expose:k}){var c;const t=D("tabs"),{children:w,addChild:B,removeChild:u}=Le(K(),"ElTabPane"),v=g(),i=g((c=e.modelValue)!=null?c:"0"),C=async(a,f=!1)=>{var P,N,S;if(!(i.value===a||ae(a)))try{await((P=e.beforeLeave)==null?void 0:P.call(e,a,i.value))!==!1&&(i.value=a,f&&(l(ie,a),l("tabChange",a)),(S=(N=v.value)==null?void 0:N.removeFocus)==null||S.call(N))}catch{}},d=(a,f,P)=>{a.props.disabled||(C(f,!0),l("tabClick",a,P))},n=(a,f)=>{a.props.disabled||ae(a.props.name)||(f.stopPropagation(),l("edit",a.props.name,"remove"),l("tabRemove",a.props.name))},E=()=>{l("edit",void 0,"add"),l("tabAdd")};return z(()=>e.modelValue,a=>C(a)),z(i,async()=>{var a;await Q(),(a=v.value)==null||a.scrollToActiveTab()}),Oe(H,{props:e,currentName:i,registerPane:B,unregisterPane:u}),k({currentName:i}),()=>{const a=m["add-icon"],f=e.editable||e.addable?b("span",{class:t.e("new-tab"),tabindex:"0",onClick:E,onKeydown:S=>{S.code===F.enter&&E()}},[a?Z(m,"add-icon"):b(V,{class:t.is("icon-plus")},{default:()=>[b(we,null,null)]})]):null,P=b("div",{class:[t.e("header"),t.is(e.tabPosition)]},[f,b(We,{ref:v,currentName:i.value,editable:e.editable,type:e.type,panes:w.value,stretch:e.stretch,onTabClick:d,onTabRemove:n},null)]),N=b("div",{class:t.e("content")},[Z(m,"default")]);return b("div",{class:[t.b(),t.m(e.tabPosition),{[t.m("card")]:e.type==="card",[t.m("border-card")]:e.type==="border-card"}]},[...e.tabPosition!=="bottom"?[P,N]:[N,P]])}}}),je=I({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),Je=["id","aria-hidden","aria-labelledby"],fe="ElTabPane",Ge=A({name:fe}),Qe=A({...Ge,props:je,setup(e){const l=e,m=K(),k=Re(),c=G(H);c||J(fe,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const t=D("tab-pane"),w=g(),B=L(()=>l.closable||c.props.closable),u=se(()=>{var n;return c.currentName.value===((n=l.name)!=null?n:w.value)}),v=g(u.value),i=L(()=>{var n;return(n=l.name)!=null?n:w.value}),C=se(()=>!l.lazy||v.value||u.value);z(u,n=>{n&&(v.value=!0)});const d=ze({uid:m.uid,slots:k,props:l,paneName:i,active:u,index:w,isClosable:B});return be(()=>{c.registerPane(d)}),Ae(()=>{c.unregisterPane(d.uid)}),(n,E)=>x(C)?Me((ce(),ue("div",{key:0,id:`pane-${x(i)}`,class:de(x(t).b()),role:"tabpanel","aria-hidden":!x(u),"aria-labelledby":`tab-${x(i)}`},[Z(n.$slots,"default")],10,Je)),[[Ve,x(u)]]):Fe("v-if",!0)}});var pe=re(Qe,[["__file","tab-pane.vue"]]);const nt=$e(Ze,{TabPane:pe}),ot=Ee(pe);export{nt as E,ot as a};
