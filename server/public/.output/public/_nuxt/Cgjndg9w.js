import{l as w,b as h,j as v,M as l,N as r,a3 as n,a1 as c,Z as b,aG as k,_ as C,ao as V,O as g,a5 as x,$ as y,u as S,aH as B}from"./CUZG7cWw.js";import{_ as N}from"./DlAUqK2U.js";const D={key:0,class:"bg-[var(--el-bg-color-page)] rounded-[12px] h-[50px]"},j=["onClick"],I=w({__name:"draw-api",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(L,{emit:d}){const i=d,o=h("sd"),a=[{name:"SD绘图",model:"sd",balance:0,default:!1,member_free:!0}],s=v(),p=e=>{s.value=e},u=e=>{i("update:modelValue",a[e].model),o.value=a[e].model,s.value&&s.value.slideTo(--e,500,!1)};return(e,M)=>{const m=B,_=k;return a.length>1?(l(),r("div",D,[n(_,{class:"draw_type_swiper h-full",slidesPerView:"auto",spaceBetween:16,speed:500,onSwiper:p},{default:c(()=>[(l(),r(C,null,V(a,(t,f)=>n(m,{class:"cursor-pointer",key:t.model,style:{width:"auto"}},{default:c(()=>[g("div",{class:y(["tabs-item h-full flex justify-center pt-[10px]",{"tabs-item__active":t.model==S(o)}]),onClick:$=>u(f)},x(t.name),11,j)]),_:2},1024)),64))]),_:1})])):b("",!0)}}}),E=N(I,[["__scopeId","data-v-fcdf8f1e"]]);export{E as default};
