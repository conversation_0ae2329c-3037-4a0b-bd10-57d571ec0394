import{E as Pe}from"./06MVqVCl.js";import{b as Ee,E as Te}from"./Zz2DnF66.js";import{I as ve,V as le,aJ as ye,W as Me,ar as te,aQ as fe,aK as Be,X as me,T as ze,aM as Ie,ad as pe,S as Xe,aP as Ve,J as ge,M as xe,a5 as Fe,aN as Ke,Q as Ye,am as Ae,N as Re}from"./CmRxzTqw.js";import{I as $e,b as Z,E as We,n as se,m as v,j as Ue,i as Oe,c as re,l as ae,r as Ne,C as ce,M as L,N as Y,a3 as Ce,a1 as he,O as G,a5 as je,u as t,$ as A,a6 as q,H as we,p as He,q as Je,a0 as ie,Z as ne,_ as oe,ao as ue}from"./CUZG7cWw.js";import{d as Se}from"./_i9izYtZ.js";const Le=Symbol("sliderContextKey"),Qe=ve({modelValue:{type:le([Number,Array]),default:0},id:{type:String,default:void 0},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},showInput:Boolean,showInputControls:{type:Boolean,default:!0},size:ye,inputSize:ye,showStops:Boolean,showTooltip:{type:Boolean,default:!0},formatTooltip:{type:le(Function),default:void 0},disabled:Boolean,range:Boolean,vertical:Boolean,height:String,debounce:{type:Number,default:300},label:{type:String,default:void 0},rangeStartLabel:{type:String,default:void 0},rangeEndLabel:{type:String,default:void 0},formatValueText:{type:le(Function),default:void 0},tooltipClass:{type:String,default:void 0},placement:{type:String,values:Ee,default:"top"},marks:{type:le(Object)},validateEvent:{type:Boolean,default:!0},...Me(["ariaLabel"])}),de=e=>me(e)||$e(e)&&e.every(me),_e={[te]:de,[fe]:de,[Be]:de},qe=(e,l,u)=>{const a=Z();return We(async()=>{e.range?(Array.isArray(e.modelValue)?(l.firstValue=Math.max(e.min,e.modelValue[0]),l.secondValue=Math.min(e.max,e.modelValue[1])):(l.firstValue=e.min,l.secondValue=e.max),l.oldValue=[l.firstValue,l.secondValue]):(typeof e.modelValue!="number"||Number.isNaN(e.modelValue)?l.firstValue=e.min:l.firstValue=Math.min(e.max,Math.max(e.min,e.modelValue)),l.oldValue=l.firstValue),ze(window,"resize",u),await se(),u()}),{sliderWrapper:a}},Ge=e=>v(()=>e.marks?Object.keys(e.marks).map(Number.parseFloat).sort((u,a)=>u-a).filter(u=>u<=e.max&&u>=e.min).map(u=>({point:u,position:(u-e.min)*100/(e.max-e.min),mark:e.marks[u]})):[]),Ze=(e,l,u)=>{const{form:a,formItem:s}=Ie(),c=Ue(),i=Z(),V=Z(),h={firstButton:i,secondButton:V},d=v(()=>e.disabled||(a==null?void 0:a.disabled)||!1),f=v(()=>Math.min(l.firstValue,l.secondValue)),o=v(()=>Math.max(l.firstValue,l.secondValue)),x=v(()=>e.range?`${100*(o.value-f.value)/(e.max-e.min)}%`:`${100*(l.firstValue-e.min)/(e.max-e.min)}%`),N=v(()=>e.range?`${100*(f.value-e.min)/(e.max-e.min)}%`:"0%"),P=v(()=>e.vertical?{height:e.height}:{}),T=v(()=>e.vertical?{height:x.value,bottom:N.value}:{width:x.value,left:N.value}),M=()=>{c.value&&(l.sliderSize=c.value[`client${e.vertical?"Height":"Width"}`])},I=m=>{const w=e.min+m*(e.max-e.min)/100;if(!e.range)return i;let E;return Math.abs(f.value-w)<Math.abs(o.value-w)?E=l.firstValue<l.secondValue?"firstButton":"secondButton":E=l.firstValue>l.secondValue?"firstButton":"secondButton",h[E]},S=m=>{const w=I(m);return w.value.setPosition(m),w},p=m=>{l.firstValue=m,g(e.range?[f.value,o.value]:m)},k=m=>{l.secondValue=m,e.range&&g([f.value,o.value])},g=m=>{u(te,m),u(fe,m)},y=async()=>{await se(),u(Be,e.range?[f.value,o.value]:e.modelValue)},j=m=>{var w,E,J,Q,_,O;if(d.value||l.dragging)return;M();let K=0;if(e.vertical){const C=(J=(E=(w=m.touches)==null?void 0:w.item(0))==null?void 0:E.clientY)!=null?J:m.clientY;K=(c.value.getBoundingClientRect().bottom-C)/l.sliderSize*100}else{const C=(O=(_=(Q=m.touches)==null?void 0:Q.item(0))==null?void 0:_.clientX)!=null?O:m.clientX,z=c.value.getBoundingClientRect().left;K=(C-z)/l.sliderSize*100}if(!(K<0||K>100))return S(K)};return{elFormItem:s,slider:c,firstButton:i,secondButton:V,sliderDisabled:d,minValue:f,maxValue:o,runwayStyle:P,barStyle:T,resetSize:M,setPosition:S,emitChange:y,onSliderWrapperPrevent:m=>{var w,E;((w=h.firstButton.value)!=null&&w.dragging||(E=h.secondButton.value)!=null&&E.dragging)&&m.preventDefault()},onSliderClick:m=>{j(m)&&y()},onSliderDown:async m=>{const w=j(m);w&&(await se(),w.value.onButtonDown(m))},setFirstValue:p,setSecondValue:k}},{left:De,down:el,right:ll,up:tl,home:al,end:nl,pageUp:ol,pageDown:rl}=pe,sl=(e,l,u)=>{const a=Z(),s=Z(!1),c=v(()=>l.value instanceof Function),i=v(()=>c.value&&l.value(e.modelValue)||e.modelValue),V=Se(()=>{u.value&&(s.value=!0)},50),h=Se(()=>{u.value&&(s.value=!1)},50);return{tooltip:a,tooltipVisible:s,formatValue:i,displayTooltip:V,hideTooltip:h}},il=(e,l,u)=>{const{disabled:a,min:s,max:c,step:i,showTooltip:V,precision:h,sliderSize:d,formatTooltip:f,emitChange:o,resetSize:x,updateDragging:N}=Oe(Le),{tooltip:P,tooltipVisible:T,formatValue:M,displayTooltip:I,hideTooltip:S}=sl(e,f,V),p=Z(),k=v(()=>`${(e.modelValue-s.value)/(c.value-s.value)*100}%`),g=v(()=>e.vertical?{bottom:k.value}:{left:k.value}),y=()=>{l.hovering=!0,I()},j=()=>{l.hovering=!1,l.dragging||S()},H=n=>{a.value||(n.preventDefault(),K(n),window.addEventListener("mousemove",C),window.addEventListener("touchmove",C),window.addEventListener("mouseup",z),window.addEventListener("touchend",z),window.addEventListener("contextmenu",z),p.value.focus())},R=n=>{a.value||(l.newPosition=Number.parseFloat(k.value)+n/(c.value-s.value)*100,$(l.newPosition),o())},U=()=>{R(-i.value)},m=()=>{R(i.value)},w=()=>{R(-i.value*4)},E=()=>{R(i.value*4)},J=()=>{a.value||($(0),o())},Q=()=>{a.value||($(100),o())},_=n=>{let b=!0;[De,el].includes(n.key)?U():[ll,tl].includes(n.key)?m():n.key===al?J():n.key===nl?Q():n.key===rl?w():n.key===ol?E():b=!1,b&&n.preventDefault()},O=n=>{let b,X;return n.type.startsWith("touch")?(X=n.touches[0].clientY,b=n.touches[0].clientX):(X=n.clientY,b=n.clientX),{clientX:b,clientY:X}},K=n=>{l.dragging=!0,l.isClick=!0;const{clientX:b,clientY:X}=O(n);e.vertical?l.startY=X:l.startX=b,l.startPosition=Number.parseFloat(k.value),l.newPosition=l.startPosition},C=n=>{if(l.dragging){l.isClick=!1,I(),x();let b;const{clientX:X,clientY:W}=O(n);e.vertical?(l.currentY=W,b=(l.startY-l.currentY)/d.value*100):(l.currentX=X,b=(l.currentX-l.startX)/d.value*100),l.newPosition=l.startPosition+b,$(l.newPosition)}},z=()=>{l.dragging&&(setTimeout(()=>{l.dragging=!1,l.hovering||S(),l.isClick||$(l.newPosition),o()},0),window.removeEventListener("mousemove",C),window.removeEventListener("touchmove",C),window.removeEventListener("mouseup",z),window.removeEventListener("touchend",z),window.removeEventListener("contextmenu",z))},$=async n=>{if(n===null||Number.isNaN(+n))return;n<0?n=0:n>100&&(n=100);const b=100/((c.value-s.value)/i.value);let W=Math.round(n/b)*b*(c.value-s.value)*.01+s.value;W=Number.parseFloat(W.toFixed(h.value)),W!==e.modelValue&&u(te,W),!l.dragging&&e.modelValue!==l.oldValue&&(l.oldValue=e.modelValue),await se(),l.dragging&&I(),P.value.updatePopper()};return re(()=>l.dragging,n=>{N(n)}),{disabled:a,button:p,tooltip:P,tooltipVisible:T,showTooltip:V,wrapperStyle:g,formatValue:M,handleMouseEnter:y,handleMouseLeave:j,onButtonDown:H,onKeyDown:_,setPosition:$}},ul=(e,l,u,a)=>({stops:v(()=>{if(!e.showStops||e.min>e.max)return[];if(e.step===0)return[];const i=(e.max-e.min)/e.step,V=100*e.step/(e.max-e.min),h=Array.from({length:i-1}).map((d,f)=>(f+1)*V);return e.range?h.filter(d=>d<100*(u.value-e.min)/(e.max-e.min)||d>100*(a.value-e.min)/(e.max-e.min)):h.filter(d=>d>100*(l.firstValue-e.min)/(e.max-e.min))}),getStopStyle:i=>e.vertical?{bottom:`${i}%`}:{left:`${i}%`}}),dl=(e,l,u,a,s,c)=>{const i=d=>{s(te,d),s(fe,d)},V=()=>e.range?![u.value,a.value].every((d,f)=>d===l.oldValue[f]):e.modelValue!==l.oldValue,h=()=>{var d,f;e.min>e.max&&Xe("Slider","min should not be greater than max.");const o=e.modelValue;e.range&&Array.isArray(o)?o[1]<e.min?i([e.min,e.min]):o[0]>e.max?i([e.max,e.max]):o[0]<e.min?i([e.min,o[1]]):o[1]>e.max?i([o[0],e.max]):(l.firstValue=o[0],l.secondValue=o[1],V()&&(e.validateEvent&&((d=c==null?void 0:c.validate)==null||d.call(c,"change").catch(x=>Ve())),l.oldValue=o.slice())):!e.range&&typeof o=="number"&&!Number.isNaN(o)&&(o<e.min?i(e.min):o>e.max?i(e.max):(l.firstValue=o,V()&&(e.validateEvent&&((f=c==null?void 0:c.validate)==null||f.call(c,"change").catch(x=>Ve())),l.oldValue=o)))};h(),re(()=>l.dragging,d=>{d||h()}),re(()=>e.modelValue,(d,f)=>{l.dragging||Array.isArray(d)&&Array.isArray(f)&&d.every((o,x)=>o===f[x])&&l.firstValue===d[0]&&l.secondValue===d[1]||h()},{deep:!0}),re(()=>[e.min,e.max],()=>{h()})},ml=ve({modelValue:{type:Number,default:0},vertical:Boolean,tooltipClass:String,placement:{type:String,values:Ee,default:"top"}}),cl={[te]:e=>me(e)},vl=["tabindex"],fl=ae({name:"ElSliderButton"}),gl=ae({...fl,props:ml,emits:cl,setup(e,{expose:l,emit:u}){const a=e,s=ge("slider"),c=Ne({hovering:!1,dragging:!1,isClick:!1,startX:0,currentX:0,startY:0,currentY:0,startPosition:0,newPosition:0,oldValue:a.modelValue}),{disabled:i,button:V,tooltip:h,showTooltip:d,tooltipVisible:f,wrapperStyle:o,formatValue:x,handleMouseEnter:N,handleMouseLeave:P,onButtonDown:T,onKeyDown:M,setPosition:I}=il(a,c,u),{hovering:S,dragging:p}=ce(c);return l({onButtonDown:T,onKeyDown:M,setPosition:I,hovering:S,dragging:p}),(k,g)=>(L(),Y("div",{ref_key:"button",ref:V,class:A([t(s).e("button-wrapper"),{hover:t(S),dragging:t(p)}]),style:q(t(o)),tabindex:t(i)?-1:0,onMouseenter:g[0]||(g[0]=(...y)=>t(N)&&t(N)(...y)),onMouseleave:g[1]||(g[1]=(...y)=>t(P)&&t(P)(...y)),onMousedown:g[2]||(g[2]=(...y)=>t(T)&&t(T)(...y)),onTouchstart:g[3]||(g[3]=(...y)=>t(T)&&t(T)(...y)),onFocus:g[4]||(g[4]=(...y)=>t(N)&&t(N)(...y)),onBlur:g[5]||(g[5]=(...y)=>t(P)&&t(P)(...y)),onKeydown:g[6]||(g[6]=(...y)=>t(M)&&t(M)(...y))},[Ce(t(Te),{ref_key:"tooltip",ref:h,visible:t(f),placement:k.placement,"fallback-placements":["top","bottom","right","left"],"stop-popper-mouse-event":!1,"popper-class":k.tooltipClass,disabled:!t(d),persistent:""},{content:he(()=>[G("span",null,je(t(x)),1)]),default:he(()=>[G("div",{class:A([t(s).e("button"),{hover:t(S),dragging:t(p)}])},null,2)]),_:1},8,["visible","placement","popper-class","disabled"])],46,vl))}});var ke=xe(gl,[["__file","button.vue"]]);const bl=ve({mark:{type:le([String,Object]),default:void 0}});var yl=ae({name:"ElSliderMarker",props:bl,setup(e){const l=ge("slider"),u=v(()=>we(e.mark)?e.mark:e.mark.label),a=v(()=>we(e.mark)?void 0:e.mark.style);return()=>He("div",{class:l.e("marks-text"),style:a.value},u.value)}});const Vl=["id","role","aria-label","aria-labelledby"],hl={key:1},wl=ae({name:"ElSlider"}),Sl=ae({...wl,props:Qe,emits:_e,setup(e,{expose:l,emit:u}){const a=e,s=ge("slider"),{t:c}=Fe(),i=Ne({firstValue:0,secondValue:0,oldValue:0,dragging:!1,sliderSize:1}),{elFormItem:V,slider:h,firstButton:d,secondButton:f,sliderDisabled:o,minValue:x,maxValue:N,runwayStyle:P,barStyle:T,resetSize:M,emitChange:I,onSliderWrapperPrevent:S,onSliderClick:p,onSliderDown:k,setFirstValue:g,setSecondValue:y}=Ze(a,i,u),{stops:j,getStopStyle:H}=ul(a,i,x,N),{inputId:R,isLabeledByFormItem:U}=Ke(a,{formItemContext:V}),m=Ye(),w=v(()=>a.inputSize||m.value),E=v(()=>a.label||a.ariaLabel||c("el.slider.defaultLabel",{min:a.min,max:a.max})),J=v(()=>a.range?a.rangeStartLabel||c("el.slider.defaultRangeStartLabel"):E.value),Q=v(()=>a.formatValueText?a.formatValueText(n.value):`${n.value}`),_=v(()=>a.rangeEndLabel||c("el.slider.defaultRangeEndLabel")),O=v(()=>a.formatValueText?a.formatValueText(b.value):`${b.value}`),K=v(()=>[s.b(),s.m(m.value),s.is("vertical",a.vertical),{[s.m("with-input")]:a.showInput}]),C=Ge(a);dl(a,i,x,N,u,V);const z=v(()=>{const r=[a.min,a.max,a.step].map(F=>{const D=`${F}`.split(".")[1];return D?D.length:0});return Math.max.apply(null,r)}),{sliderWrapper:$}=qe(a,i,M),{firstValue:n,secondValue:b,sliderSize:X}=ce(i),W=r=>{i.dragging=r};return Je(Le,{...ce(a),sliderSize:X,disabled:o,precision:z,emitChange:I,resetSize:M,updateDragging:W}),Ae({from:"label",replacement:"aria-label",version:"2.8.0",scope:"el-slider",ref:"https://element-plus.org/en-US/component/slider.html"},v(()=>!!a.label)),l({onSliderClick:p}),(r,F)=>{var D,be;return L(),Y("div",{id:r.range?t(R):void 0,ref_key:"sliderWrapper",ref:$,class:A(t(K)),role:r.range?"group":void 0,"aria-label":r.range&&!t(U)?t(E):void 0,"aria-labelledby":r.range&&t(U)?(D=t(V))==null?void 0:D.labelId:void 0,onTouchstart:F[2]||(F[2]=(...B)=>t(S)&&t(S)(...B)),onTouchmove:F[3]||(F[3]=(...B)=>t(S)&&t(S)(...B))},[G("div",{ref_key:"slider",ref:h,class:A([t(s).e("runway"),{"show-input":r.showInput&&!r.range},t(s).is("disabled",t(o))]),style:q(t(P)),onMousedown:F[0]||(F[0]=(...B)=>t(k)&&t(k)(...B)),onTouchstart:F[1]||(F[1]=(...B)=>t(k)&&t(k)(...B))},[G("div",{class:A(t(s).e("bar")),style:q(t(T))},null,6),Ce(ke,{id:r.range?void 0:t(R),ref_key:"firstButton",ref:d,"model-value":t(n),vertical:r.vertical,"tooltip-class":r.tooltipClass,placement:r.placement,role:"slider","aria-label":r.range||!t(U)?t(J):void 0,"aria-labelledby":!r.range&&t(U)?(be=t(V))==null?void 0:be.labelId:void 0,"aria-valuemin":r.min,"aria-valuemax":r.range?t(b):r.max,"aria-valuenow":t(n),"aria-valuetext":t(Q),"aria-orientation":r.vertical?"vertical":"horizontal","aria-disabled":t(o),"onUpdate:modelValue":t(g)},null,8,["id","model-value","vertical","tooltip-class","placement","aria-label","aria-labelledby","aria-valuemin","aria-valuemax","aria-valuenow","aria-valuetext","aria-orientation","aria-disabled","onUpdate:modelValue"]),r.range?(L(),ie(ke,{key:0,ref_key:"secondButton",ref:f,"model-value":t(b),vertical:r.vertical,"tooltip-class":r.tooltipClass,placement:r.placement,role:"slider","aria-label":t(_),"aria-valuemin":t(n),"aria-valuemax":r.max,"aria-valuenow":t(b),"aria-valuetext":t(O),"aria-orientation":r.vertical?"vertical":"horizontal","aria-disabled":t(o),"onUpdate:modelValue":t(y)},null,8,["model-value","vertical","tooltip-class","placement","aria-label","aria-valuemin","aria-valuemax","aria-valuenow","aria-valuetext","aria-orientation","aria-disabled","onUpdate:modelValue"])):ne("v-if",!0),r.showStops?(L(),Y("div",hl,[(L(!0),Y(oe,null,ue(t(j),(B,ee)=>(L(),Y("div",{key:ee,class:A(t(s).e("stop")),style:q(t(H)(B))},null,6))),128))])):ne("v-if",!0),t(C).length>0?(L(),Y(oe,{key:2},[G("div",null,[(L(!0),Y(oe,null,ue(t(C),(B,ee)=>(L(),Y("div",{key:ee,style:q(t(H)(B.position)),class:A([t(s).e("stop"),t(s).e("marks-stop")])},null,6))),128))]),G("div",{class:A(t(s).e("marks"))},[(L(!0),Y(oe,null,ue(t(C),(B,ee)=>(L(),ie(t(yl),{key:ee,mark:B.mark,style:q(t(H)(B.position))},null,8,["mark","style"]))),128))],2)],64)):ne("v-if",!0)],38),r.showInput&&!r.range?(L(),ie(t(Pe),{key:0,ref:"input","model-value":t(n),class:A(t(s).e("input")),step:r.step,disabled:t(o),controls:r.showInputControls,min:r.min,max:r.max,debounce:r.debounce,size:t(w),"onUpdate:modelValue":t(g),onChange:t(I)},null,8,["model-value","class","step","disabled","controls","min","max","debounce","size","onUpdate:modelValue","onChange"])):ne("v-if",!0)],42,Vl)}}});var kl=xe(Sl,[["__file","slider.vue"]]);const Ll=Re(kl);export{Ll as E};
