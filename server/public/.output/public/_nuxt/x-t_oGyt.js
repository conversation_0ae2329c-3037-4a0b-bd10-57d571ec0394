import{_ as S}from"./eFgaMLiC.js";import{E as V}from"./Zz2DnF66.js";import{E as I}from"./D5Svi-lq.js";import{b as B,e as R,E as c}from"./CmRxzTqw.js";import{E as $}from"./oVx59syQ.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{useAiPPTStore as z,useSearchEx as U}from"./CrcCiVkl.js";import{_ as L}from"./C4sa7gEr.js";import{l as N,ae as j,M as a,N as m,a3 as n,a1 as i,u as l,O as t,a6 as A,$ as D,a5 as u,ag as _,_ as F,ao as K}from"./CUZG7cWw.js";import{_ as O}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./R2n930gq.js";import"./DecTOTC8.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        *//* empty css        */import"./Bj_9-7Jh.js";const G=""+new URL("ai_ppt_title.BGhjFYvC.png",import.meta.url).href,M={class:"h-full prompt-input"},Y={class:"max-w-[1200px] mx-auto"},q={class:"flex flex-col justify-center items-center py-[120px]"},H={class:"mt-[28px]"},J={class:"py-[10px]"},Q={class:"flex items-center justify-center"},W={class:"ml-1"},X={class:"mt-[30px]"},Z={class:"bg-page rounded-[15px] overflow-hidden p-[10px] w-[560px]"},oo={class:"flex items-center"},to={class:"mr-auto"},eo={class:"flex flex-wrap mx-[-7px] mb-[-14px] mt-[30px]"},so=["onClick"],no={class:"flex-1 line-clamp-1 text-tx-secondary"},lo={class:"text-primary flex ml-[10px]"},io=N({__name:"prompt-input",setup(po){const s=z();B();const d=[{label:"基础",value:1,icon:"local-icon-cube",desc:"基于描述生成PPT"},{label:"增强",value:2,icon:"local-icon-light-bulb",desc:"基于描述及模板生成PPT"},{label:"深入",value:3,icon:"local-icon-bottle",desc:"基于大纲及模板生成PPT"}],x=p=>{if(!(p.shiftKey&&p.keyCode===13)&&p.keyCode===13)return s.genPPT(),p.preventDefault()},f=async()=>{await s.genPPTSubmit(s.options),s.showTemplate=!1},{searchEx:v,getSearchEx:b}=U(),y=p=>{s.options.prompt=p};return b(),(p,o)=>{const r=S,P=V,g=I,h=R,T=c,w=j("RouterLink"),E=c,k=$;return a(),m("div",M,[n(k,{class:"scroll-bar"},{default:i(()=>[t("div",Y,[t("div",q,[o[7]||(o[7]=t("img",{class:"h-[68px] w-[688px]",src:G,alt:""},null,-1)),o[8]||(o[8]=t("div",{class:"text-[20px] text-tx-regular mt-3"}," 输入主题描述，快速生成专属PPT ",-1)),o[9]||(o[9]=t("div",{class:"text-tx-secondary mt-2"}," 免费预览，满意再付费。PPT内容由AI生成，仅供参考 ",-1)),t("div",H,[n(g,{modelValue:l(s).options.type,"onUpdate:modelValue":o[0]||(o[0]=e=>l(s).options.type=e),options:d,style:A({width:`${d.length*90}px`,"--el-border-radius-base":"10px","--el-segmented-color":"var(--el-text-color-primary)"})},{default:i(({item:e})=>[n(P,{effect:"dark",content:e.desc,disabled:!e.desc,placement:"top"},{default:i(()=>[t("div",J,[t("div",Q,[t("span",{class:D({"!text-primary":l(s).options.type!==e.value})},[n(r,{size:"15",name:e.icon},null,8,["name"])],2),t("div",W,u(e.label),1)])])]),_:2},1032,["content","disabled"])]),_:1},8,["modelValue","style"])]),t("div",X,[t("div",Z,[t("div",null,[n(h,{modelValue:l(s).options.prompt,"onUpdate:modelValue":o[1]||(o[1]=e=>l(s).options.prompt=e),autosize:{minRows:2,maxRows:4},type:"textarea",placeholder:"简单输入一个标题即可生成PPT",resize:"none",onKeydown:x},null,8,["modelValue"])]),t("div",oo,[t("div",to,[n(w,{to:"/ai_ppt/history"},{default:i(()=>[n(T,{link:""},{icon:i(()=>[n(r,{name:"el-icon-Clock"})]),default:i(()=>[o[5]||(o[5]=_(" 历史记录 "))]),_:1})]),_:1})]),t("div",null,[n(E,{type:"primary",style:{padding:"8px"},onClick:o[2]||(o[2]=e=>l(s).genPPT())},{icon:i(()=>[n(r,{name:"el-icon-Promotion"})]),default:i(()=>[o[6]||(o[6]=_(" 快速生成 "))]),_:1})])])])]),t("div",eo,[(a(!0),m(F,null,K(l(v),(e,C)=>(a(),m("div",{class:"flex max-w-full items-center mx-[7px] cursor-pointer hover:bg-fill-light mb-[14px] px-[15px] py-[10px] border border-br-light border-solid rounded-[12px]",key:C,onClick:ro=>y(e)},[t("div",no,u(e),1),t("span",lo,[n(r,{name:"el-icon-Right"})])],8,so))),128))])])])]),_:1}),n(L,{visible:l(s).showTemplate,"onUpdate:visible":o[3]||(o[3]=e=>l(s).showTemplate=e),"cover-id":l(s).options.cover_id,"onUpdate:coverId":o[4]||(o[4]=e=>l(s).options.cover_id=e),prompt:l(s).options.prompt,onConfirm:f},null,8,["visible","cover-id","prompt"])])}}}),Uo=O(io,[["__scopeId","data-v-8e6c4445"]]);export{Uo as default};
