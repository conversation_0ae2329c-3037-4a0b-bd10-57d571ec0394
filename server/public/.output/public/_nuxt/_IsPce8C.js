import{b as V,a as B}from"./C9f7n97H.js";import{_ as D}from"./Bv29pan0.js";import{E as N}from"./llRQJmEG.js";import{E as S}from"./oVx59syQ.js";import{bx as L,cR as R,cS as $}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{u as g}from"./DNOp0HuO.js";import{u as A}from"./DymDsCmz.js";import I from"./DwB10QAP.js";import{e as z}from"./BhXe-NXN.js";import{l as F,r as O,ai as y,M as a,N as r,O as i,a3 as m,a1 as l,u as o,_ as x,ao as v,a0 as u,ag as q,a5 as G,A as H}from"./CUZG7cWw.js";const J={class:"h-full flex flex-col"},T={class:"pt-[15px] px-main"},U={class:"flex-1 min-h-0"},X={class:"p-main"},j={key:0},ce=F({__name:"music",async setup(K){let t,s;const d=A(),c=O({keyword:"",category_id:0}),{data:h}=([t,s]=y(()=>g(()=>R(),{lazy:!0},"$62OqCgsHxi")),t=await t,s(),t),{data:_,refresh:p,pending:P}=([t,s]=y(()=>g(()=>$(c),{lazy:!0},"$XfxmNfcMJb")),t=await t,s(),t);L(()=>{p()},1e3);const b=f=>{d.music=H(f)};return(f,n)=>{const k=V,E=B,C=D,w=N,M=S;return a(),r("div",J,[i("div",null,[i("div",T,[m(C,{class:"my-[-5px]","default-height":42},{default:l(()=>[m(E,{modelValue:o(c).category_id,"onUpdate:modelValue":n[0]||(n[0]=e=>o(c).category_id=e),class:"el-radio-group-margin",onChange:n[1]||(n[1]=e=>o(p)())},{default:l(()=>[(a(!0),r(x,null,v(o(h),e=>(a(),u(k,{key:e.id,label:e.id},{default:l(()=>[q(G(e.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),i("div",U,[m(M,null,{default:l(()=>[i("div",X,[o(_).length?(a(),r("div",j,[(a(!0),r(x,null,v(o(_),e=>(a(),u(I,{"active-id":o(d).music.id,"item-id":e.id,key:e.id,class:"mb-[15px]",name:e.name,pic:e.cover,url:e.url,onClick:Q=>b(e)},null,8,["active-id","item-id","name","pic","url","onClick"]))),128))])):(a(),u(w,{key:1,image:o(z),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}});export{ce as _};
