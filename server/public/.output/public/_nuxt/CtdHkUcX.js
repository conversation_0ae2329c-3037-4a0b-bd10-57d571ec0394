import{_ as N}from"./CbQsrhNE.js";import{h as $,e as L,s as B}from"./CmRxzTqw.js";import{E as D}from"./CiabO6Xq.js";import{_ as R}from"./eFgaMLiC.js";import{l as b,b as g,M as o,N as r,O as e,a5 as _,$ as y,u as i,a3 as a,a1 as u,a6 as j,V as z,Z as A,a9 as M,y as U,_ as h,ao as v,a0 as F}from"./CUZG7cWw.js";import{_ as k}from"./DlAUqK2U.js";/* empty css        */import{E as O}from"./oVx59syQ.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";const T={class:"cursor-default"},Z=b({__name:"index",props:{title:{type:String,default:""},length:{type:Number,default:0}},setup(d){const t=g(!0),c=()=>{t.value==!0?t.value=!1:t.value=!0};return(f,p)=>{const m=R;return o(),r("div",null,[e("div",{class:"flex items-center justify-between mt-[15px] text-info",onClick:c},[e("div",T,_(d.title),1),e("div",{class:y(["transition-transform rotate-",{"rotate-180":!i(t)}])},[a(m,{name:"el-icon-ArrowUp"})],2)]),a(M,null,{default:u(()=>[i(t)?(o(),r("div",{key:0,style:j({"max-height":d.length*110+"px"}),class:"dropDownList overflow-hidden"},[z(f.$slots,"menu",{},void 0,!0)],4)):A("",!0)]),_:3})])}}}),q=k(Z,[["__scopeId","data-v-2b7032c7"]]),G={class:"bg-body rounded-[12px] flex flex-col h-full overflow-hidden text-tx-primary",style:{width:"var(--aside-panel-width)"}},H={class:"flex items-center justify-around text-xl font-medium px-[16px] pt-[16px] cursor-pointer"},J={class:"px-[16px] pt-[16px] pb-[6px]"},K={class:"flex-1 min-h-0"},P={class:"px-[16px] pb-[16px]"},Q=["onClick"],W={class:"ml-2 flex-1"},X={class:"text-base font-bold role-name"},Y={class:"text-xs role-desc text-tx-placeholder line-clamp-1"},ee=b({__name:"role-sidebar",props:{sidebarList:{default:()=>[]},currentId:{default:()=>0},keyword:{default:""}},emits:["ontoggle","update:keyword"],setup(d,{emit:t}){const c=t,p=$(d,"keyword",c),m=g();return(x,s)=>{const w=N,C=L,I=D,V=q;return o(),r("div",G,[e("div",H,[a(w,{to:"/dialogue/chat"},{default:u(()=>s[1]||(s[1]=[e("div",{class:"pb-[8px]"},"问答助手",-1)])),_:1}),s[2]||(s[2]=e("div",{class:"pb-[6px] text-primary border-solid border-b-[2px] border-primary"}," 角色助手 ",-1))]),e("div",J,[a(C,{class:"w-full leading-[32px] role-search",modelValue:i(p),"onUpdate:modelValue":s[0]||(s[0]=n=>U(p)?p.value=n:null),"prefix-icon":i(B),placeholder:"请输入关键词搜索"},null,8,["modelValue","prefix-icon"])]),e("div",K,[a(i(O),{class:"",ref_key:"sidebarRef",ref:m},{default:u(()=>[e("div",P,[(o(!0),r(h,null,v(x.sidebarList,(n,E)=>(o(),F(V,{title:n.name,length:n.skill.length,key:E},{menu:u(()=>[(o(!0),r(h,null,v(n.skill,(l,S)=>(o(),r("div",{class:y(["flex items-center mt-[15px] p-[10px] cursor-pointer rounded-[12px]",{"role-active":x.currentId==l.id}]),key:S,onClick:te=>c("ontoggle",l)},[a(I,{src:l.image,class:"w-[42px] h-[42px] rounded-[8px]"},null,8,["src"]),e("div",W,[e("div",X,_(l.name),1),e("div",Y,_(l.describe),1)])],10,Q))),128))]),_:2},1032,["title","length"]))),128))])]),_:1},512)])])}}}),me=k(ee,[["__scopeId","data-v-5ed71039"]]);export{me as default};
