import{H as w,l as L,m as d,b as S,c as z,E as Z,M as i,N as v,O as E,u as a,$ as u,af as _,a0 as c,a1 as p,a2 as y,Z as r,a5 as N,a3 as j,V as F,a6 as x,a4 as ee,n as ae,aF as O}from"./CUZG7cWw.js";import{I as ie,cB as te,aq as g,V as ne,W as se,ar as C,as as V,X as T,aK as P,aQ as B,aM as le,Q as oe,J as ce,aN as re,aL as ue,Y as de,aP as ve,am as fe,H as h,ct as pe,M as he,S as me,N as ye}from"./CmRxzTqw.js";const be=ie({modelValue:{type:[Boolean,String,Number],default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},size:{type:String,validator:te},width:{type:[String,Number],default:""},inlinePrompt:{type:Boolean,default:!1},inactiveActionIcon:{type:g},activeActionIcon:{type:g},activeIcon:{type:g},inactiveIcon:{type:g},activeText:{type:String,default:""},inactiveText:{type:String,default:""},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},name:{type:String,default:""},validateEvent:{type:Boolean,default:!0},beforeChange:{type:ne(Function)},id:String,tabindex:{type:[String,Number]},label:{type:String,default:void 0},...se(["ariaLabel"])}),Ie={[C]:l=>V(l)||w(l)||T(l),[P]:l=>V(l)||w(l)||T(l),[B]:l=>V(l)||w(l)||T(l)},ke=["onClick"],ge=["id","aria-checked","aria-disabled","aria-label","name","true-value","false-value","disabled","tabindex","onKeydown"],Ve=["aria-hidden"],we=["aria-hidden"],Se=["aria-hidden"],U="ElSwitch",Ee=L({name:U}),Ne=L({...Ee,props:be,emits:Ie,setup(l,{expose:H,emit:f}){const t=l,{formItem:b}=le(),$=oe(),n=ce("switch"),{inputId:Q}=re(t,{formItemContext:b}),I=ue(d(()=>t.loading)),A=S(t.modelValue!==!1),m=S(),W=S(),q=d(()=>[n.b(),n.m($.value),n.is("disabled",I.value),n.is("checked",s.value)]),G=d(()=>[n.e("label"),n.em("label","left"),n.is("active",!s.value)]),J=d(()=>[n.e("label"),n.em("label","right"),n.is("active",s.value)]),R=d(()=>({width:de(t.width)}));z(()=>t.modelValue,()=>{A.value=!0});const M=d(()=>A.value?t.modelValue:!1),s=d(()=>M.value===t.activeValue);[t.activeValue,t.inactiveValue].includes(M.value)||(f(C,t.inactiveValue),f(P,t.inactiveValue),f(B,t.inactiveValue)),z(s,e=>{var o;m.value.checked=e,t.validateEvent&&((o=b==null?void 0:b.validate)==null||o.call(b,"change").catch(Y=>ve()))});const k=()=>{const e=s.value?t.inactiveValue:t.activeValue;f(C,e),f(P,e),f(B,e),ae(()=>{m.value.checked=s.value})},D=()=>{if(I.value)return;const{beforeChange:e}=t;if(!e){k();return}const o=e();[O(o),V(o)].includes(!0)||me(U,"beforeChange must return type `Promise<boolean>` or `boolean`"),O(o)?o.then(K=>{K&&k()}).catch(K=>{}):o&&k()},X=()=>{var e,o;(o=(e=m.value)==null?void 0:e.focus)==null||o.call(e)};return Z(()=>{m.value.checked=s.value}),fe({from:"label",replacement:"aria-label",version:"2.8.0",scope:"el-switch",ref:"https://element-plus.org/en-US/component/switch.html"},d(()=>!!t.label)),H({focus:X,checked:s}),(e,o)=>(i(),v("div",{class:u(a(q)),onClick:ee(D,["prevent"])},[E("input",{id:a(Q),ref_key:"input",ref:m,class:u(a(n).e("input")),type:"checkbox",role:"switch","aria-checked":a(s),"aria-disabled":a(I),"aria-label":e.label||e.ariaLabel,name:e.name,"true-value":e.activeValue,"false-value":e.inactiveValue,disabled:a(I),tabindex:e.tabindex,onChange:k,onKeydown:_(D,["enter"])},null,42,ge),!e.inlinePrompt&&(e.inactiveIcon||e.inactiveText)?(i(),v("span",{key:0,class:u(a(G))},[e.inactiveIcon?(i(),c(a(h),{key:0},{default:p(()=>[(i(),c(y(e.inactiveIcon)))]),_:1})):r("v-if",!0),!e.inactiveIcon&&e.inactiveText?(i(),v("span",{key:1,"aria-hidden":a(s)},N(e.inactiveText),9,Ve)):r("v-if",!0)],2)):r("v-if",!0),E("span",{ref_key:"core",ref:W,class:u(a(n).e("core")),style:x(a(R))},[e.inlinePrompt?(i(),v("div",{key:0,class:u(a(n).e("inner"))},[e.activeIcon||e.inactiveIcon?(i(),c(a(h),{key:0,class:u(a(n).is("icon"))},{default:p(()=>[(i(),c(y(a(s)?e.activeIcon:e.inactiveIcon)))]),_:1},8,["class"])):e.activeText||e.inactiveText?(i(),v("span",{key:1,class:u(a(n).is("text")),"aria-hidden":!a(s)},N(a(s)?e.activeText:e.inactiveText),11,we)):r("v-if",!0)],2)):r("v-if",!0),E("div",{class:u(a(n).e("action"))},[e.loading?(i(),c(a(h),{key:0,class:u(a(n).is("loading"))},{default:p(()=>[j(a(pe))]),_:1},8,["class"])):a(s)?F(e.$slots,"active-action",{key:1},()=>[e.activeActionIcon?(i(),c(a(h),{key:0},{default:p(()=>[(i(),c(y(e.activeActionIcon)))]),_:1})):r("v-if",!0)]):a(s)?r("v-if",!0):F(e.$slots,"inactive-action",{key:2},()=>[e.inactiveActionIcon?(i(),c(a(h),{key:0},{default:p(()=>[(i(),c(y(e.inactiveActionIcon)))]),_:1})):r("v-if",!0)])],2)],6),!e.inlinePrompt&&(e.activeIcon||e.activeText)?(i(),v("span",{key:1,class:u(a(J))},[e.activeIcon?(i(),c(a(h),{key:0},{default:p(()=>[(i(),c(y(e.activeIcon)))]),_:1})):r("v-if",!0),!e.activeIcon&&e.activeText?(i(),v("span",{key:1,"aria-hidden":!a(s)},N(e.activeText),9,Se)):r("v-if",!0)],2)):r("v-if",!0)],10,ke))}});var Te=he(Ne,[["__file","switch.vue"]]);const Be=ye(Te);export{Be as E};
