import{_ as ue}from"./DB7Ysqj9.js";import{_ as ce}from"./D3znQkH1.js";import{a as pe,b as me,j as fe,l as ve,cF as ge,$ as he,_ as ye,f as L}from"./CmRxzTqw.js";import{u as R}from"./DNOp0HuO.js";import{u as _e}from"./Bj_9-7Jh.js";import xe from"./D-_txtLt.js";import{C as we,a as Se}from"./BA8DZlI0.js";import Ce from"./DVjRi2Oc.js";import{b as be,e as Ie,c as ke}from"./DQUFgXGm.js";import{g as Le,a as Re,b as Ee}from"./BdjxQtGL.js";import{u as Me}from"./CsJP_4je.js";import{c as Z}from"./Bs9Zhtqd.js";import{l as $e,b as f,j as ze,q as De,r as G,ai as E,c as A,k as Ne,M,N as $,a3 as S,a1 as O,O as v,_ as J,ao as Ve,$ as Ue,u as l,a5 as Fe,Z as qe,y as z,n as Te}from"./CUZG7cWw.js";import{_ as Ae}from"./DlAUqK2U.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./eFgaMLiC.js";import"./Bf_xRNbS.js";import"./BOx_5T3X.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./DdtGP7XX.js";import"./B7GaOiDz.js";import"./Bh-PoUNP.js";import"./DP2rzg_V.js";/* empty css        */import"./C9f7n97H.js";/* empty css        */import"./HA5sEeDs.js";import"./D5Svi-lq.js";import"./CH6wv3Pu.js";import"./DoCT-qbH.js";import"./llRQJmEG.js";import"./BvSuqySp.js";import"./l0sNRNKZ.js";import"./B1huhKtP.js";import"./C9jirCEY.js";import"./BscXL5XZ.js";import"./CpufhUzm.js";import"./DAOx25wS.js";import"./BluXXrgj.js";import"./CbQsrhNE.js";import"./CiabO6Xq.js";/* empty css        */const Oe=(x,o="yyyy-mm-dd")=>{x||(x=Number(new Date)),x.toString().length===10&&(x*=1e3);const i=new Date(x);let p;const w={"y+":i.getFullYear().toString(),"m+":(i.getMonth()+1).toString(),"d+":i.getDate().toString(),"h+":i.getHours().toString(),"M+":i.getMinutes().toString(),"s+":i.getSeconds().toString()};for(const g in w)p=new RegExp(`(${g})`).exec(o),p&&(o=o.replace(p[1],p[1].length===1?w[g]:w[g].padStart(p[1].length,"0")));return o},He={class:"flex flex-col h-full"},Ke={class:"px-[16px] pt-[16px] round-[12px]"},je={class:"flex flex-wrap gap-y-2 my-swiper category-lists"},Be=["onClick"],Pe={class:"flex-1 min-h-0 flex"},Ze={class:"py-[16px] pl-[16px]"},Ge={class:"flex-1 min-w-0 p-4"},Je={class:"h-full flex bg-body rounded-[12px]"},Xe={class:"h-full border-r border-solid border-br-light"},Ye={class:"flex-1 min-w-0"},Qe=$e({__name:"produce",async setup(x){let o,i;const p=f(""),w=pe();Me();const g=f(!0),H=f([]),c=f({}),D=me(),X=fe(),N=ve(),{cateId:Y,modelId:Q}=w.query,C=f("current"),b=ze({});De("chatModel",b);const s=G({cateId:Y||"0",modelId:Q,modelKey:""}),W=async e=>{s.cateId=e,await K();const[t]=q.value||[];N.replace({path:"",query:{cateId:e,modelId:t==null?void 0:t.id}})},{data:V,pending:We,refresh:et}=([o,i]=E(()=>R(()=>Le(),{default(){return[]},lazy:!0},"$3iMOmfmgi4")),o=await o,i(),o);f(!1),f(0);let U=null;const F=e=>{try{U==null||U.slideTo(e)}catch(t){console.error(t)}},{data:q,refresh:K}=([o,i]=E(()=>R(()=>Re({keyword:p.value,category_id:s.cateId,page_size:999}),{lazy:!0},"$VSSSR0UZJy")),o=await o,i(),o);ge(p,e=>{K()},{debounce:500});const ee=async()=>{await L.confirm("确定清空创作记录？"),await Ie({type:2,other_id:s.modelId}),d.value=[],k()},{data:I,refresh:te}=([o,i]=E(()=>R(()=>Ee({id:s.modelId}),{default(){return{}},transform(e){return e},lazy:!0},"$dzxelPzSXF")),o=await o,i(),o),u=G({pageNo:1,count:0,pageSize:15,lists:[]}),{refresh:T,pending:oe}=([o,i]=E(()=>R(()=>be({other_id:s.modelId||0,page_size:u.pageSize,page_no:u.pageNo,type:2}),{default(){return[]},transform(e){u.count=e.count;const t=e.lists.map(n=>{let m="";if(he(n.ask)){const _=(n==null?void 0:n.ask[0])||{};m=`${_.title}：${_.value}`}else m=n.ask;return{...n,title:m}});return u.pageNo===1&&(u.lists=[]),u.lists.push(...t),t},lazy:!0},"$T4LGrdB7sx")),o=await o,i(),o),k=async()=>{Object.assign(u,{pageNo:1,count:0,pageSize:15,lists:[]}),await T()},re=()=>{oe.value||u.count>=u.pageNo*u.pageSize&&(u.pageNo++,T())},ae=e=>{console.log("model",e),s.modelId=e.id,k()},{lockFn:le}=_e(async e=>{e.extra&&(c.value=e.extra,await Te(),j())}),se=()=>{var e,t;(t=(e=I.value)==null?void 0:e.form)==null||t.forEach(n=>{n.props.placeholder&&!n.props.defaultValue&&(c.value[n.props.field]=n.props.placeholder)})},ne=()=>{var e;(e=I.value)==null||e.form.forEach(t=>{t.props.defaultValue?c.value[t.props.field]=Z(t.props.defaultValue):c.value[t.props.field]=void 0})},ie=()=>{g.value?(g.value=!1,H.value=[]):(H.value=V.value.map(e=>e.id),g.value=!0)},d=f([]),de=()=>{var t;const e=((t=I.value)==null?void 0:t.form[0])||{};return`${e.props.title}：${c.value[e.props.field]||""}`},h=f(!1);let r;const j=async()=>{if(!h.value){C.value="current",h.value=!0,d.value=[],F(1);try{r=ke({other_id:s.modelId,question:c.value,type:2,creation_type:Se.Normal,model:s.modelKey}),r.addEventListener("close",async()=>{X.getUser(),setTimeout(async()=>{h.value=!1,await k();const e=d.value.length;d.value[e-1].id=u.lists[0].id},200)}),r.addEventListener("error",async e=>{var t;if(((t=e.data)==null?void 0:t.code)===1100){D.getIsShowRecharge?(await L.confirm(`${D.getTokenUnit}数量已用完，请前往充值`),N.push("/user/recharge")):L.msgError(`${D.getTokenUnit}数量已用完。请联系客服增加`);return}e.errorType==="connectError"&&L.msgError("请求失败，请重试"),setTimeout(()=>{h.value=!1,F(0)},200)}),r.addEventListener("chat",({data:e})=>{const{id:t,event:n,data:m,code:_,index:y}=e;let a=d.value.findIndex(P=>P.id===t);a===-1&&(d.value.push({create_time:Oe(Date.now(),"yyyy-mm-dd hh:MM:ss"),title:c.value.question?c.value.question:de(),reply:[],extra:Z(c.value),id:t}),a=d.value.length-1),m&&(d.value[a].reply[y]||(d.value[a].reply[y]=""),d.value[a].reply[y]+=m)}),r.addEventListener("finish",({data:e})=>{const{data:t,index:n,id:m}=e,_=d.value.findIndex(y=>y.id===m);t&&(d.value[_].reply[n]+=t),ne()})}catch{h.value=!1}}};A(V,e=>{g.value=!1,ie()},{immediate:!0});const B=()=>{r==null||r.removeEventListener("close"),r==null||r.removeEventListener("chat"),r==null||r.removeEventListener("error"),r==null||r.removeEventListener("finish"),r==null||r.abort(),h.value=!1};return A(()=>w.query,({cateId:e,modelId:t})=>{s.cateId=e,s.modelId=t,d.value=[],C.value="current",F(0),B(),t?(te(),k()):I.value={}}),A(()=>q.value,e=>{e.length&&s.modelId==null&&N.replace({path:"",query:{cateId:s.cateId,modelId:e[0].id}})},{deep:!0}),Ne(()=>{B()}),(e,t)=>{const n=ue,m=ce,_=ye;return M(),$("div",null,[S(_,{name:"default"},{default:O(()=>{var y;return[v("div",He,[v("div",Ke,[v("div",je,[(M(!0),$(J,null,Ve(l(V),(a,P)=>(M(),$(J,null,[Object.keys(a).includes("name")?(M(),$("div",{key:0,class:Ue(["category-item",{"is-active":l(s).cateId==a.id}]),onClick:tt=>W(a.id)},Fe(a.name),11,Be)):qe("",!0)],64))),256))])]),v("div",Pe,[v("div",Ze,[S(Ce,{modelValue:l(p),"onUpdate:modelValue":t[0]||(t[0]=a=>z(p)?p.value=a:null),"model-state":l(s),"current-model-list":((y=l(q))==null?void 0:y.lists)||[],onSelect:ae},null,8,["modelValue","model-state","current-model-list"])]),v("div",Ge,[v("div",Je,[v("div",Xe,[S(xe,{modelValue:l(c),"onUpdate:modelValue":t[3]||(t[3]=a=>z(c)?c.value=a:null),class:"h-full 2xl:w-[400px] xl:w-[350px] lg:w-[320px] w-[250px]","model-data":l(I),loading:l(h),onInsert:se,onCreate:j},{actions:O(()=>[S(n,{class:"w-full my-[10px]",sub_id:l(s).modelKey,"onUpdate:sub_id":t[1]||(t[1]=a=>l(s).modelKey=a),modelConfig:l(b),"onUpdate:modelConfig":t[2]||(t[2]=a=>z(b)?b.value=a:null)},null,8,["sub_id","modelConfig"])]),_:1},8,["modelValue","model-data","loading"])]),v("div",Ye,[S(m,null,{default:O(()=>[S(we,{current:l(C),"onUpdate:current":t[4]||(t[4]=a=>z(C)?C.value=a:null),chatModel:l(b),"current-creation-history":l(d),loading:l(h),"page-info":l(u),onLoad:re,onClean:ee,onRewrite:l(le),onRefresh:l(T)},null,8,["current","chatModel","current-creation-history","loading","page-info","onRewrite","onRefresh"])]),_:1})])])])])])]}),_:1})])}}}),uo=Ae(Qe,[["__scopeId","data-v-78b01304"]]);export{uo as default};
