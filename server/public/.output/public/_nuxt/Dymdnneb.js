import{_ as $,a as P}from"./DKYoP2z-.js";import{E as j}from"./oVx59syQ.js";import{u as D,_ as U}from"./DrxPZuc-.js";import{_ as V}from"./eFgaMLiC.js";import{E as F}from"./C9jirCEY.js";import{j as G,v as H}from"./CmRxzTqw.js";/* empty css        */import{b as O}from"./Bu_nKEGp.js";import{l as Z,j as b,b as S,r as q,E as A,c as J,k as K,M as a,N as n,O as s,_ as Q,ao as W,a7 as w,u as c,a3 as p,a1 as X,Z as Y,$ as tt,a5 as et}from"./CUZG7cWw.js";import{_ as ot}from"./DlAUqK2U.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./CRNANWso.js";import"./CXDY_LVT.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";/* empty css        *//* empty css        */import"./DjwCd26w.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./Bj_9-7Jh.js";import"./DYjlFFbo.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";const st={class:"h-full bg-body rounded-[12px] p-[16px] flex flex-col"},it={class:"flex-1 min-h-0 flex flex-col"},at={class:"category-lists"},nt=["onClick"],lt={class:"flex-1 min-h-0"},rt={key:0,class:"h-full flex flex-col"},ct={class:"flex-1 min-h-0 flex"},pt={class:"flex-1 min-w-0 h-full"},mt={"infinite-scroll-distance":"50"},dt={class:"mt-[16px]"},ut={key:1,class:"h-full flex flex-col items-center justify-center"},ft={class:"text-tx-secondary"},_t=Z({__name:"index",setup(gt,{expose:N}){G();const i=b(),k=[{name:"全部",type:-1},{name:"生成中",type:1},{name:"生成成功",type:2},{name:"生成失败",type:3}],{getMusic:v,currentId:u,pause:R}=D(),m=S(-1),t=q({pageNo:1,count:0,pageSize:15,loading:!0,lists:[]}),f=b(),y=S(0),T=async()=>{clearTimeout(f.value);const o=t.lists.filter(e=>e.status===1).map(e=>e.id);o.length>0&&(f.value=setTimeout(()=>{g()},6e3)),o.length!==y.value&&v(),y.value=o.length},_=async()=>{try{const o=await O({status:m.value,page_no:t.pageNo,page_size:t.pageSize});t.count=o.count,t.pageNo===1&&(t.lists=[]),t.lists.push(...o.lists)}catch{}finally{t.loading=!1,T()}};v();const z=()=>{t.count>=t.pageNo*t.pageSize&&(t.pageNo++,_())},g=async()=>{t.pageSize=t.pageNo*t.pageSize,t.pageNo=1,await _()},x=async()=>{t.loading=!0,t.pageSize=15,t.pageNo=1,await _()},h=()=>{const o=t.lists.find(e=>e.status===2);o&&(u.value=o.id)},B=async o=>{R(),m.value=o,u.value=-1,await x(),h()};return N({refresh:async()=>{m.value=-1,await g(),i.value.setScrollTop(0)}}),A(async()=>{await x(),h()}),J(u,o=>{if(i.value){const e=document.getElementById(`music-item-${o}`);if(!e)return;const l=e==null?void 0:e.getBoundingClientRect(),r=i.value.wrapRef.getBoundingClientRect();l.top<r.top&&i.value.setScrollTop(e==null?void 0:e.offsetTop),l.bottom>r.bottom&&i.value.setScrollTop((e==null?void 0:e.offsetTop)-r.height+l.height)}}),K(()=>{clearTimeout(f.value)}),(o,e)=>{const l=P,r=j,C=$,E=U,M=V,I=F,L=H;return a(),n("div",st,[e[2]||(e[2]=s("div",{class:"text-2xl font-bold border-b border-solid border-br-light pb-[16px]"}," 生成记录 ",-1)),s("div",it,[s("div",at,[(a(),n(Q,null,W(k,(d,vt)=>s("div",{key:d.type},[s("div",{class:tt(["category-item bg-white",{"is-active":c(m)===d.type}]),onClick:yt=>B(d.type)},et(d.name),11,nt)])),64))]),w((a(),n("div",lt,[c(t).lists.length?(a(),n("div",rt,[s("div",ct,[s("div",pt,[p(r,{ref_key:"scrollBarRef",ref:i},{default:X(()=>[w((a(),n("div",mt,[p(l,{"music-list":c(t).lists,onUpdate:g},null,8,["music-list"])])),[[I,z]])]),_:1},512)]),p(C)]),s("div",dt,[p(E,{ref:"musicPlayerRef",class:"bg-page rounded-[12px]"},null,512)])])):c(t).loading?Y("",!0):(a(),n("div",ut,[s("div",ft,[p(M,{size:45,name:"local-icon-music1"})]),e[0]||(e[0]=s("div",{class:"my-[10px]"},"当前还没有音乐哦",-1)),e[1]||(e[1]=s("div",{class:"text-tx-secondary text-sm"}," 在左侧输入描述，创建你的作品吧！ ",-1))]))])),[[L,c(t).loading]])])])}}}),ae=ot(_t,[["__scopeId","data-v-1898024f"]]);export{ae as default};
