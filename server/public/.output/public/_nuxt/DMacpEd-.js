import{_ as n}from"./eFgaMLiC.js";import{_ as i}from"./B60T7KBp.js";import{d0 as s}from"./CmRxzTqw.js";import{l as m,M as p,N as a,a3 as t}from"./CUZG7cWw.js";import"./DlAUqK2U.js";import"./CXsrG8JM.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./BscXL5XZ.js";import"./Ddo5WWE5.js";import"./_i9izYtZ.js";import"./B1huhKtP.js";import"./CDwN27aR.js";const $=m({__name:"index",setup(c){const e=s(),o=()=>{e.setSetting({key:"showDrawer",value:!0})};return(_,l)=>{const r=n;return p(),a("div",{class:"setting flex cursor-pointer h-full items-center pl-2",onClick:o},[t(r,{size:20,name:"local-icon-dianpu_fengge"}),t(i)])}}});export{$ as default};
