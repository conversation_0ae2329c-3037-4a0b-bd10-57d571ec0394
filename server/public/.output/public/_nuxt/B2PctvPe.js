import{_ as F}from"./eFgaMLiC.js";import{cA as N,d as T,E as L,v as D}from"./CmRxzTqw.js";import{_ as I}from"./D5RYEqFL.js";import{E as S}from"./B7GaOiDz.js";import"./DP2rzg_V.js";import{u as z}from"./Bj_9-7Jh.js";import{t as R}from"./B5TkE_dZ.js";import{l as $,m as v,b as q,M as c,a0 as A,a1 as l,O as r,a5 as O,u as s,a7 as P,N as d,a3 as i,Z as g,ag as U,y as Z}from"./CUZG7cWw.js";const j={class:"font-bold text-tx-primary"},G={class:"flex-1","element-loading-text":"正在翻译"},H={class:"flex p-[10px]"},J={class:"flex-1 flex items-center"},ae=$({__name:"prompt",props:{modelValue:{default:""},config:{default:()=>({})},type:{default:1},showTranslate:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(y,{emit:h}){const k=h,n=y,{modelValue:a}=N(n,k),E=v(()=>n.type===1),V={1:{label:"视频场景",placeholder:"在此描述你的视频场景，包含内容主体+动作/场景"},2:{label:"描述词",placeholder:"描述视频中需要变化的内容即可～"}},u=v(()=>V[n.type]||{}),f=q(-1),B=()=>{var e;const o=(e=n.config.data)==null?void 0:e.length;if(o){let t=Math.round(Math.random()*(o-1));f.value===t&&(t<o-1?t++:t--),t<0&&(t=0),f.value=t;const m=n.config.data[t];m&&(a.value=m)}},{lockFn:x,isLock:C}=z(async()=>{if(!a.value)return T.error("请输入描述词");const o=await R({prompt:a.value});a.value=o.result});return(o,e)=>{const t=F,m=L,b=I,w=S,M=D;return c(),A(w,{prop:"prompt",required:""},{label:l(()=>[r("span",j,O(s(u).label),1)]),default:l(()=>[P((c(),d("div",G,[i(b,{modelValue:s(a),"onUpdate:modelValue":e[2]||(e[2]=p=>Z(a)?a.value=p:null),placeholder:s(u).placeholder,contentStyle:{height:"120px"}},{"length-suffix":l(()=>{var p;return[r("div",H,[r("div",J,[s(E)&&o.config.status&&((p=o.config.data)!=null&&p.length)?(c(),d("div",{key:0,class:"flex items-center cursor-pointer text-[#6F7E8E] text-sm mr-2 hover:text-primary",onClick:B},[i(t,{name:"el-icon-Refresh"}),e[3]||(e[3]=r("span",{class:"ml-[4px]"},"试试示例",-1))])):g("",!0),o.showTranslate?(c(),d("div",{key:1,class:"flex items-center cursor-pointer text-[#6F7E8E] text-sm hover:text-primary",onClick:e[0]||(e[0]=(..._)=>s(x)&&s(x)(..._))},[i(t,{name:"el-icon-Switch"}),e[4]||(e[4]=r("span",{class:"ml-[4px]"},"翻译成英文",-1))])):g("",!0)]),i(m,{link:"",size:"small",type:"primary",onClick:e[1]||(e[1]=_=>a.value="")},{icon:l(()=>[i(t,{name:"el-icon-Delete",size:12})]),default:l(()=>[e[5]||(e[5]=U(" 清空 "))]),_:1})])]}),_:1},8,["modelValue","placeholder"])])),[[M,s(C)]])]),_:1})}}});export{ae as _};
