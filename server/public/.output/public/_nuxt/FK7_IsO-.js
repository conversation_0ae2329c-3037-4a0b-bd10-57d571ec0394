import{a as d}from"./CmRxzTqw.js";import{_ as m}from"./BNIxm8Lt.js";import{_ as u}from"./Ce0U9aAs.js";import{l as _,r as f,x as i,b as k,m as x,E as y,M as e,N as s,O as p,_ as v,ao as h,u as n,a0 as g,a2 as C,Z as b,$ as B,a5 as $}from"./CUZG7cWw.js";const w={class:"p-main h-full flex flex-col"},N={class:"flex"},D={class:"flex bg-page p-[5px] rounded-[10px]"},E=["onClick"],I={key:0,class:"flex-1 min-h-0"},K=_({__name:"index",props:{appId:{}},setup(L){const l=d(),r=f([{name:"对话数据",key:"data",component:i(m)},{name:"对话记录",key:"record",component:i(u)}]),a=k("data"),c=x(()=>r.find(t=>t.key==a.value));return y(()=>{l.query.dialogue&&(a.value="record")}),(t,M)=>(e(),s("div",w,[p("div",N,[p("div",D,[(e(!0),s(v,null,h(n(r),o=>(e(),s("div",{class:B(["leading-8 w-[120px] text-center rounded-[10px] cursor-pointer",{"bg-primary text-white":n(a)==o.key}]),key:o.key,onClick:R=>a.value=o.key},$(o.name),11,E))),128))])]),n(c)?(e(),s("div",I,[(e(),g(C(n(c).component),{"app-id":t.appId},null,8,["app-id"]))])):b("",!0)]))}});export{K as _};
