import{_ as C}from"./eFgaMLiC.js";import{_ as E}from"./CbQsrhNE.js";import{E as q}from"./CiabO6Xq.js";import{E as B}from"./oVx59syQ.js";import{_ as R}from"./DfULzLLs.js";import{a as L,_ as z}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{u as F}from"./DNOp0HuO.js";import{F as I}from"./qRM0tN96.js";import{l as S,ai as V,m as u,M as i,N as x,a3 as r,a1 as a,O as t,_ as $,ao as A,a0 as f,a5 as h,$ as b,u as e,Z as D}from"./CUZG7cWw.js";import"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./CH6wv3Pu.js";import"./CCGM0zxW.js";import"./DCzKTodP.js";/* empty css        */import"./BYMcWg3Q.js";import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        */import"./DjHPV-Am.js";import"./DoCT-qbH.js";import"./DAOx25wS.js";import"./DwFObZc_.js";import"./DQUFgXGm.js";import"./CRNANWso.js";import"./CHg9aK2B.js";import"./DecTOTC8.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./Do9LV2MU.js";import"./D5RYEqFL.js";import"./C-cKpkeq.js";import"./Ce6KOvmZ.js";import"./CaNlADry.js";const O={class:"h-full flex"},Z={class:"p-4 h-full"},M={class:"w-[300px] h-full flex flex-col bg-body rounded-lg"},T={class:"p-[15px]"},X={class:"flex items-center"},j={class:"flex-1 min-h-0"},G={class:"px-[15px]"},H={class:"flex-1 min-w-0 ml-[15px]"},J={class:"line-clamp-1 text-xl font-medium"},K={class:"flex-1 min-w-0 pr-4 py-4"},P={class:"bg-body rounded-[10px] h-full"},jt=S({__name:"chat",async setup(Q){let s,p;const y=L(),{data:l}=([s,p]=V(()=>F(()=>I(),{default(){return[]},lazy:!0},"$X7Lic9ZOFl")),s=await s,p(),s),n=u(()=>y.query.id),m=u(()=>l.value.find(c=>c.id===Number(n.value))||{});return(c,d)=>{const v=C,_=E,g=q,w=B,k=R,N=z;return i(),x("div",null,[r(N,{name:"default"},{default:a(()=>[t("div",O,[t("div",Z,[t("div",M,[t("div",T,[t("div",X,[r(_,{class:"flex bg-body p-[5px] text-bold rounded-[50%] text-primary shadow-light",to:"/robot_square",replace:!0},{default:a(()=>[r(v,{name:"el-icon-Back",size:18})]),_:1}),d[0]||(d[0]=t("div",{class:"text-xl flex-1 min-w-0 ml-[10px]"}," 智能体广场 ",-1))])]),t("div",j,[r(w,null,{default:a(()=>[t("div",G,[(i(!0),x($,null,A(e(l),o=>(i(),f(_,{key:o.id,to:{path:"",query:{id:o.id}},class:b(["flex mb-[15px] rounded-[10px] px-[15px] py-[10px] items-center border border-br-light bg-body",{"text-white !border-primary !bg-primary":e(n)==o.id}]),replace:!0},{default:a(()=>[r(g,{class:"w-[50px] h-[50px] rounded-[50%]",src:o.image,alt:""},null,8,["src"]),t("div",H,[t("div",J,h(o.name),1),t("div",{class:b(["line-clamp-1 mt-[4px] text-tx-secondary",{"!text-white":e(n)==o.id}])},h(o.intro),3)])]),_:2},1032,["to","class"]))),128))])]),_:1})])])]),t("div",K,[t("div",P,[e(m).id?(i(),f(k,{key:0,"robot-id":e(m).robot_id,"square-id":e(m).id},null,8,["robot-id","square-id"])):D("",!0)])])])]),_:1})])}}});export{jt as default};
