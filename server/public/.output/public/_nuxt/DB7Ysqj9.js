import{E as H,a as J}from"./CXDY_LVT.js";import{_ as K}from"./eFgaMLiC.js";import{E as W}from"./oVx59syQ.js";import{E as X,a as Y}from"./Bf_xRNbS.js";import{h as F,b as I,c8 as ee}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{P as te}from"./CaNlADry.js";import{u as se}from"./DdtGP7XX.js";import{l as oe,j as le,b as ae,r as ne,m as L,c as ie,M as s,N as o,a0 as E,a1 as d,_ as g,ao as k,u as l,O as a,a5 as _,y as D,Z as x,$ as N,a3 as h}from"./CUZG7cWw.js";import{_ as de}from"./DlAUqK2U.js";const re={class:"flex items-center"},ce={class:"my-1"},pe={class:"leading-6"},ue={class:"line-clamp-1 flex-1"},me={key:0},fe={key:1,class:"text-[#a8abb2]"},_e={key:2},xe={key:3},he={class:"flex-none ml-2 flex items-center"},ve={class:"model-container"},ye={class:"flex items-center h-[46px] py-2"},be=["src"],ge={class:"mx-2 leading-[24px] mt-[2px] font-medium"},ke={key:1,class:"bg-[#E3FFF2] text-[#23B571] px-[5px] py-[2px] leading-[20px] rounded-[5px]"},Ce=["onClick"],Me={class:"flex items-center"},we={class:"mr-2"},Le={key:0,class:"text-tx-placeholder"},Ee={key:1,class:"text-tx-placeholder"},Ne={key:0,class:"flex items-center"},Se={key:1,class:"flex items-center"},$e=oe({__name:"index",props:{id:{type:[String,Number],default:""},sub_id:{type:[String,Number],default:""},setDefault:{type:Boolean,default:!0},type:{type:String,default:"chatModels"},disabled:{type:Boolean,default:!1}},emits:["update:id","update:sub_id","update:modelConfig"],setup(m,{emit:j}){const C=j,v=m,f=F(v,"id",C),c=F(v,"sub_id",C),S=I(),M=le(),b=ae(-1),i=ne({modelList:[]}),R=L(()=>i.modelList.filter((e,t)=>t%2===0)),U=L(()=>i.modelList.filter((e,t)=>t%2!==0)),p=L(()=>v.type=="chatModels"?i.modelList.flatMap(e=>e.models).find(e=>e.id===c.value)||{}:i.modelList.find(e=>e.id===f.value)||{});ie(()=>p.value,e=>{C("update:modelConfig",e)});const{suspense:T}=se(["modelLists"],{queryFn:ee,cacheTime:1e3}),z=async()=>{try{const{data:e}=await T();i.modelList=e[v.type],v.setDefault&&O()}catch(e){console.log("获取聊天模型数据错误=>",e)}},O=()=>{const e=i.modelList.findIndex(y=>y.is_default)||0,t=i.modelList[e].models[0];t&&(f.value=i.modelList[e].id,c.value=t.id,b.value=e)},$=(e,t)=>e===0?t*2:t*2+1,P=(e,t)=>i.modelList[$(e,t)].models.some(y=>y.id===c.value),q=(e,t)=>{f.value=e,c.value=t,M.value.close()};return z(),(e,t)=>{const y=H,G=J,A=K,B=W,Q=X,Z=Y;return s(),o("div",re,[m.type==="vectorModels"?(s(),E(G,{key:0,class:"flex-1",modelValue:l(f),"onUpdate:modelValue":t[0]||(t[0]=n=>D(f)?f.value=n:null),filterable:"",disabled:m.disabled},{default:d(()=>[(s(!0),o(g,null,k(l(i).modelList,n=>(s(),E(y,{class:"!h-fit",value:n.id,key:n.name,label:n.alias},{default:d(()=>[a("div",ce,[a("div",pe,_(n.alias),1)])]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue","disabled"])):x("",!0),m.type==="chatModels"?(s(),o("div",{key:1,class:N(["select-input flex items-center justify-between flex-1 cursor-pointer rounded-[8px] w-[266px] h-[32px] px-[15px]",[l(c)?"":"text-tx-placeholder",m.disabled?"text-tx-placeholder cursor-no-drop bg-[--el-disabled-bg-color]":""]]),onClick:t[1]||(t[1]=n=>l(M).open())},[a("div",ue,[l(p).alias?(s(),o("span",me,_(l(p).alias),1)):(s(),o("span",fe,"请选择")),l(p).alias&&l(p).price=="0"?(s(),o("span",_e," (免费) ")):l(p).alias?(s(),o("span",xe," ("+_(`消耗${l(p).price}${l(S).getTokenUnit}/1000字符`)+") ",1)):x("",!0)]),a("div",he,[h(A,{name:"el-icon-ArrowDown"})])],2)):x("",!0),m.type==="chatModels"?(s(),E(te,{key:2,ref_key:"popupRef",ref:M,width:"780px",title:"模型选择",customClass:"!rounded-[15px]"},{footer:d(()=>t[3]||(t[3]=[a("div",null,null,-1)])),default:d(()=>[h(B,{height:"50vh","max-height":"70vh"},{default:d(()=>[a("div",ve,[h(Z,{"active-name":l(b),"onUpdate:activeName":t[2]||(t[2]=n=>D(b)?b.value=n:null),class:"flex flex-wrap justify-between",accordion:""},{default:d(()=>[(s(!0),o(g,null,k([l(R),l(U)],(n,w)=>(s(),o("div",{key:w},[(s(!0),o(g,null,k(n,(u,V)=>(s(),o("div",{key:u.id,class:"w-[350px] mt-[15px]"},[h(Q,{class:N(["bg-[#f8f8f8] dark:bg-[#0d0e10] border border-solid border-[transparent]",{"el-collapse-item--active":P(w,V)}]),name:$(w,V)},{title:d(()=>[a("div",null,[a("div",ye,[u.logo?(s(),o("img",{key:0,src:u.logo,class:"w-[30px] h-[30px]",alt:"模型logo"},null,8,be)):x("",!0),a("span",ge,_(u.name),1),u.is_free?(s(),o("span",ke," 会员免费 ")):x("",!0)])])]),default:d(()=>[h(B,{height:"100%","max-height":"250px"},{default:d(()=>[(s(!0),o(g,null,k(u.models,r=>(s(),o("div",{key:r.id,class:N(["flex justify-between mb-[14px] px-[15px] cursor-pointer hover:text-primary",{"text-primary":l(c)===r.id}]),onClick:Ae=>q(u.id,r.id)},[a("div",Me,[a("span",we,_(r.alias||"请选择"),1),r.alias&&r.price=="0"?(s(),o("span",Le," (免费) ")):(s(),o("span",Ee," ("+_(`消耗${r.price}${l(S).getTokenUnit}/1千字符`)+") ",1))]),l(c)===r.id?(s(),o("div",Ne,[h(A,{name:"el-icon-CircleCheck",size:"20"})])):(s(),o("div",Se,t[4]||(t[4]=[a("div",{class:"w-[18px] h-[18px] rounded-full border border-solid border-[#cacbd3]"},null,-1)])))],10,Ce))),128))]),_:2},1024)]),_:2},1032,["class","name"])]))),128))]))),128))]),_:1},8,["active-name"])])]),_:1})]),_:1},512)):x("",!0)])}}}),Qe=de($e,[["__scopeId","data-v-648801bb"]]);export{Qe as _};
