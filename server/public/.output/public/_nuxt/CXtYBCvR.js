import{a as _}from"./9CYoqqXX.js";import{b as h,a as d}from"./CmRxzTqw.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import f from"./cdDY9IAx.js";import{l as v,m as r,M as s,N as c,a3 as w,a1 as k,u as o,_ as N,ao as x,a0 as B}from"./CUZG7cWw.js";import{_ as I}from"./DlAUqK2U.js";import"./BOx_5T3X.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./Ddo5WWE5.js";import"./mBG0LxMu.js";import"./CbQsrhNE.js";import"./DecTOTC8.js";const M={class:"menu"},g=v({__name:"nav",props:{isHome:{type:Boolean}},setup(A){const n=h(),m=r(()=>{var e;return((e=n.pageAside.nav)==null?void 0:e.filter(i=>Number(i.is_show)===1))||[]}),u=r(()=>n.pageAside.showNavIcon),t=d(),p=r(()=>{const e=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.parentPath||t.meta.activePath||e});return(e,i)=>{const l=_;return s(),c("div",M,[w(l,{"default-active":o(p)},{default:k(()=>[(s(!0),c(N,null,x(o(m),a=>(s(),B(f,{key:a.id,item:a,showName:!0,"is-show-icon":o(u),path:a.link.path,"is-active":o(p)===a.link.path},null,8,["item","is-show-icon","path","is-active"]))),128))]),_:1},8,["default-active"])])}}}),D=I(g,[["__scopeId","data-v-818826ac"]]);export{D as default};
