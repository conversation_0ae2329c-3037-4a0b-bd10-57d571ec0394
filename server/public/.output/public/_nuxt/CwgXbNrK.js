import{I as $e,V as Q,M as G,T as Te,at as ce,ad as M,E as pe,H as fe,aS as Fe,J as ee,a5 as Re,Y as Se,Q as ke,aj as Oe,N as Ne,O as ve}from"./CmRxzTqw.js";import{c as O,E as Pe,O as De,w as ie}from"./Zz2DnF66.js";import{E as Be}from"./oVx59syQ.js";import{c as Ke,E as Le,d as Me,a as me,C as Ge,b as Ae,e as ze,f as Ue,g as Ye,F as He,L as Ve}from"./u6CVc_ZE.js";import{l as A,b as y,i as _,m as h,q as W,F as Je,t as H,u as k,c as ge,V as B,ae as b,M as P,a0 as U,a1 as v,a3 as F,aj as We,ak as je,n as qe,o as Qe,N as j,aq as Ze,W as V,$ as Z,Z as X,a as be,O as Xe,a2 as xe,a4 as x,_ as eo,a6 as oo}from"./CUZG7cWw.js";import{c as no}from"./D6yUe_Nr.js";import{c as we}from"./C3XldtMC.js";const to=$e({style:{type:Q([String,Array,Object])},currentTabId:{type:Q(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:Q(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:ro,ElCollectionItem:lo,COLLECTION_INJECTION_KEY:oe,COLLECTION_ITEM_INJECTION_KEY:so}=Ke("RovingFocusGroup"),ne=Symbol("elRovingFocusGroup"),Ie=Symbol("elRovingFocusGroupItem"),ao={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},io=(e,o)=>e,uo=(e,o,d)=>{const l=io(e.key);return ao[l]},co=(e,o)=>e.map((d,l)=>e[(l+o)%e.length]),te=e=>{const{activeElement:o}=document;for(const d of e)if(d===o||(d.focus(),o!==document.activeElement))return},de="currentTabIdChange",ue="rovingFocusGroup.entryFocus",po={bubbles:!1,cancelable:!0},fo=A({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:to,emits:[de,"entryFocus"],setup(e,{emit:o}){var d;const l=y((d=e.currentTabId||e.defaultCurrentTabId)!=null?d:null),c=y(!1),u=y(!1),s=y(null),{getItems:r}=_(oe,void 0),p=h(()=>[{outline:"none"},e.style]),m=a=>{o(de,a)},g=()=>{c.value=!0},w=O(a=>{var f;(f=e.onMousedown)==null||f.call(e,a)},()=>{u.value=!0}),E=O(a=>{var f;(f=e.onFocus)==null||f.call(e,a)},a=>{const f=!k(u),{target:D,currentTarget:R}=a;if(D===R&&f&&!k(c)){const K=new Event(ue,po);if(R==null||R.dispatchEvent(K),!K.defaultPrevented){const I=r().filter(T=>T.focusable),C=I.find(T=>T.active),$=I.find(T=>T.id===k(l)),L=[C,$,...I].filter(Boolean).map(T=>T.ref);te(L)}}u.value=!1}),n=O(a=>{var f;(f=e.onBlur)==null||f.call(e,a)},()=>{c.value=!1}),i=(...a)=>{o("entryFocus",...a)};W(ne,{currentTabbedId:Je(l),loop:H(e,"loop"),tabIndex:h(()=>k(c)?-1:0),rovingFocusGroupRef:s,rovingFocusGroupRootStyle:p,orientation:H(e,"orientation"),dir:H(e,"dir"),onItemFocus:m,onItemShiftTab:g,onBlur:n,onFocus:E,onMousedown:w}),ge(()=>e.currentTabId,a=>{l.value=a??null}),Te(s,ue,i)}});function vo(e,o,d,l,c,u){return B(e.$slots,"default")}var mo=G(fo,[["render",vo],["__file","roving-focus-group-impl.vue"]]);const go=A({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:ro,ElRovingFocusGroupImpl:mo}});function bo(e,o,d,l,c,u){const s=b("el-roving-focus-group-impl"),r=b("el-focus-group-collection");return P(),U(r,null,{default:v(()=>[F(s,We(je(e.$attrs)),{default:v(()=>[B(e.$slots,"default")]),_:3},16)]),_:3})}var wo=G(go,[["render",bo],["__file","roving-focus-group.vue"]]);const Io=A({components:{ElRovingFocusCollectionItem:lo},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:o}){const{currentTabbedId:d,loop:l,onItemFocus:c,onItemShiftTab:u}=_(ne,void 0),{getItems:s}=_(oe,void 0),r=ce(),p=y(null),m=O(n=>{o("mousedown",n)},n=>{e.focusable?c(k(r)):n.preventDefault()}),g=O(n=>{o("focus",n)},()=>{c(k(r))}),w=O(n=>{o("keydown",n)},n=>{const{key:i,shiftKey:a,target:f,currentTarget:D}=n;if(i===M.tab&&a){u();return}if(f!==D)return;const R=uo(n);if(R){n.preventDefault();let I=s().filter(C=>C.focusable).map(C=>C.ref);switch(R){case"last":{I.reverse();break}case"prev":case"next":{R==="prev"&&I.reverse();const C=I.indexOf(D);I=l.value?co(I,C+1):I.slice(C+1);break}}qe(()=>{te(I)})}}),E=h(()=>d.value===k(r));return W(Ie,{rovingFocusGroupItemRef:p,tabIndex:h(()=>k(E)?0:-1),handleMousedown:m,handleFocus:g,handleKeydown:w}),{id:r,handleKeydown:w,handleFocus:g,handleMousedown:m}}});function Eo(e,o,d,l,c,u){const s=b("el-roving-focus-collection-item");return P(),U(s,{id:e.id,focusable:e.focusable,active:e.active},{default:v(()=>[B(e.$slots,"default")]),_:3},8,["id","focusable","active"])}var _o=G(Io,[["render",Eo],["__file","roving-focus-item.vue"]]);const q=Symbol("elDropdown"),{ButtonGroup:ho}=pe,Co=A({name:"ElDropdown",components:{ElButton:pe,ElButtonGroup:ho,ElScrollbar:Be,ElDropdownCollection:Le,ElTooltip:Pe,ElRovingFocusGroup:wo,ElOnlyChild:De,ElIcon:fe,ArrowDown:Fe},props:Me,emits:["visible-change","click","command"],setup(e,{emit:o}){const d=be(),l=ee("dropdown"),{t:c}=Re(),u=y(),s=y(),r=y(null),p=y(null),m=y(null),g=y(null),w=y(!1),E=[M.enter,M.space,M.down],n=h(()=>({maxHeight:Se(e.maxHeight)})),i=h(()=>[l.m(C.value)]),a=h(()=>no(e.trigger)),f=ce().value,D=h(()=>e.id||f);ge([u,a],([t,S],[z])=>{var le,se,ae;(le=z==null?void 0:z.$el)!=null&&le.removeEventListener&&z.$el.removeEventListener("pointerenter",N),(se=t==null?void 0:t.$el)!=null&&se.removeEventListener&&t.$el.removeEventListener("pointerenter",N),(ae=t==null?void 0:t.$el)!=null&&ae.addEventListener&&S.includes("hover")&&t.$el.addEventListener("pointerenter",N)},{immediate:!0}),Qe(()=>{var t,S;(S=(t=u.value)==null?void 0:t.$el)!=null&&S.removeEventListener&&u.value.$el.removeEventListener("pointerenter",N)});function R(){K()}function K(){var t;(t=r.value)==null||t.onClose()}function I(){var t;(t=r.value)==null||t.onOpen()}const C=ke();function $(...t){o("command",...t)}function N(){var t,S;(S=(t=u.value)==null?void 0:t.$el)==null||S.focus()}function L(){}function T(){const t=k(p);a.value.includes("hover")&&(t==null||t.focus()),g.value=null}function re(t){g.value=t}function J(t){w.value||(t.preventDefault(),t.stopImmediatePropagation())}function Y(){o("visible-change",!0)}function Ce(t){(t==null?void 0:t.type)==="keydown"&&p.value.focus()}function ye(){o("visible-change",!1)}return W(q,{contentRef:p,role:h(()=>e.role),triggerId:D,isUsingKeyboard:w,onItemEnter:L,onItemLeave:T}),W("elDropdown",{instance:d,dropdownSize:C,handleClick:R,commandHandler:$,trigger:H(e,"trigger"),hideOnClick:H(e,"hideOnClick")}),{t:c,ns:l,scrollbar:m,wrapStyle:n,dropdownTriggerKls:i,dropdownSize:C,triggerId:D,triggerKeys:E,currentTabId:g,handleCurrentTabIdChange:re,handlerMainButtonClick:t=>{o("click",t)},handleEntryFocus:J,handleClose:K,handleOpen:I,handleBeforeShowTooltip:Y,handleShowTooltip:Ce,handleBeforeHideTooltip:ye,onFocusAfterTrapped:t=>{var S,z;t.preventDefault(),(z=(S=p.value)==null?void 0:S.focus)==null||z.call(S,{preventScroll:!0})},popperRef:r,contentRef:p,triggeringElementRef:u,referenceElementRef:s}}});function yo(e,o,d,l,c,u){var s;const r=b("el-dropdown-collection"),p=b("el-roving-focus-group"),m=b("el-scrollbar"),g=b("el-only-child"),w=b("el-tooltip"),E=b("el-button"),n=b("arrow-down"),i=b("el-icon"),a=b("el-button-group");return P(),j("div",{class:Z([e.ns.b(),e.ns.is("disabled",e.disabled)])},[F(w,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":e.trigger==="hover"?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":(s=e.referenceElementRef)==null?void 0:s.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":e.trigger==="hover"?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:e.teleported,pure:"",persistent:"",onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},Ze({content:v(()=>[F(m,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:v(()=>[F(p,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:v(()=>[F(r,null,{default:v(()=>[B(e.$slots,"dropdown")]),_:3})]),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])]),_:3},8,["wrap-style","view-class"])]),_:2},[e.splitButton?void 0:{name:"default",fn:v(()=>[F(g,{id:e.triggerId,ref:"triggeringElementRef",role:"button",tabindex:e.tabindex},{default:v(()=>[B(e.$slots,"default")]),_:3},8,["id","tabindex"])])}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","teleported","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(P(),U(a,{key:0},{default:v(()=>[F(E,V({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:v(()=>[B(e.$slots,"default")]),_:3},16,["size","type","disabled","tabindex","onClick"]),F(E,V({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:v(()=>[F(i,{class:Z(e.ns.e("icon"))},{default:v(()=>[F(n)]),_:1},8,["class"])]),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])]),_:3})):X("v-if",!0)],2)}var $o=G(Co,[["render",yo],["__file","dropdown.vue"]]);const To=A({name:"DropdownItemImpl",components:{ElIcon:fe},props:me,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:o}){const d=ee("dropdown"),{role:l}=_(q,void 0),{collectionItemRef:c}=_(Ge,void 0),{collectionItemRef:u}=_(so,void 0),{rovingFocusGroupItemRef:s,tabIndex:r,handleFocus:p,handleKeydown:m,handleMousedown:g}=_(Ie,void 0),w=we(c,u,s),E=h(()=>l.value==="menu"?"menuitem":l.value==="navigation"?"link":"button"),n=O(i=>{const{code:a}=i;if(a===M.enter||a===M.space)return i.preventDefault(),i.stopImmediatePropagation(),o("clickimpl",i),!0},m);return{ns:d,itemRef:w,dataset:{[Ae]:""},role:E,tabIndex:r,handleFocus:p,handleKeydown:n,handleMousedown:g}}}),Fo=["aria-disabled","tabindex","role"];function Ro(e,o,d,l,c,u){const s=b("el-icon");return P(),j(eo,null,[e.divided?(P(),j("li",V({key:0,role:"separator",class:e.ns.bem("menu","item","divided")},e.$attrs),null,16)):X("v-if",!0),Xe("li",V({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:o[0]||(o[0]=r=>e.$emit("clickimpl",r)),onFocus:o[1]||(o[1]=(...r)=>e.handleFocus&&e.handleFocus(...r)),onKeydown:o[2]||(o[2]=x((...r)=>e.handleKeydown&&e.handleKeydown(...r),["self"])),onMousedown:o[3]||(o[3]=(...r)=>e.handleMousedown&&e.handleMousedown(...r)),onPointermove:o[4]||(o[4]=r=>e.$emit("pointermove",r)),onPointerleave:o[5]||(o[5]=r=>e.$emit("pointerleave",r))}),[e.icon?(P(),U(s,{key:0},{default:v(()=>[(P(),U(xe(e.icon)))]),_:1})):X("v-if",!0),B(e.$slots,"default")],16,Fo)],64)}var So=G(To,[["render",Ro],["__file","dropdown-item-impl.vue"]]);const Ee=()=>{const e=_("elDropdown",{}),o=h(()=>e==null?void 0:e.dropdownSize);return{elDropdown:e,_elDropdownSize:o}},ko=A({name:"ElDropdownItem",components:{ElDropdownCollectionItem:ze,ElRovingFocusItem:_o,ElDropdownItemImpl:So},inheritAttrs:!1,props:me,emits:["pointermove","pointerleave","click"],setup(e,{emit:o,attrs:d}){const{elDropdown:l}=Ee(),c=be(),u=y(null),s=h(()=>{var n,i;return(i=(n=k(u))==null?void 0:n.textContent)!=null?i:""}),{onItemEnter:r,onItemLeave:p}=_(q,void 0),m=O(n=>(o("pointermove",n),n.defaultPrevented),ie(n=>{if(e.disabled){p(n);return}const i=n.currentTarget;i===document.activeElement||i.contains(document.activeElement)||(r(n),n.defaultPrevented||i==null||i.focus())})),g=O(n=>(o("pointerleave",n),n.defaultPrevented),ie(n=>{p(n)})),w=O(n=>{if(!e.disabled)return o("click",n),n.type!=="keydown"&&n.defaultPrevented},n=>{var i,a,f;if(e.disabled){n.stopImmediatePropagation();return}(i=l==null?void 0:l.hideOnClick)!=null&&i.value&&((a=l.handleClick)==null||a.call(l)),(f=l.commandHandler)==null||f.call(l,e.command,c,n)}),E=h(()=>({...e,...d}));return{handleClick:w,handlePointerMove:m,handlePointerLeave:g,textContent:s,propsAndAttrs:E}}});function Oo(e,o,d,l,c,u){var s;const r=b("el-dropdown-item-impl"),p=b("el-roving-focus-item"),m=b("el-dropdown-collection-item");return P(),U(m,{disabled:e.disabled,"text-value":(s=e.textValue)!=null?s:e.textContent},{default:v(()=>[F(p,{focusable:!e.disabled},{default:v(()=>[F(r,V(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:v(()=>[B(e.$slots,"default")]),_:3},16,["onPointerleave","onPointermove","onClickimpl"])]),_:3},8,["focusable"])]),_:3},8,["disabled","text-value"])}var _e=G(ko,[["render",Oo],["__file","dropdown-item.vue"]]);const No=A({name:"ElDropdownMenu",props:Ue,setup(e){const o=ee("dropdown"),{_elDropdownSize:d}=Ee(),l=d.value,{focusTrapRef:c,onKeydown:u}=_(Oe,void 0),{contentRef:s,role:r,triggerId:p}=_(q,void 0),{collectionRef:m,getItems:g}=_(Ye,void 0),{rovingFocusGroupRef:w,rovingFocusGroupRootStyle:E,tabIndex:n,onBlur:i,onFocus:a,onMousedown:f}=_(ne,void 0),{collectionRef:D}=_(oe,void 0),R=h(()=>[o.b("menu"),o.bm("menu",l==null?void 0:l.value)]),K=we(s,m,c,w,D),I=O($=>{var N;(N=e.onKeydown)==null||N.call(e,$)},$=>{const{currentTarget:N,code:L,target:T}=$;if(N.contains(T),M.tab===L&&$.stopImmediatePropagation(),$.preventDefault(),T!==k(s)||!He.includes(L))return;const J=g().filter(Y=>!Y.disabled).map(Y=>Y.ref);Ve.includes(L)&&J.reverse(),te(J)});return{size:l,rovingFocusGroupRootStyle:E,tabIndex:n,dropdownKls:R,role:r,triggerId:p,dropdownListWrapperRef:K,handleKeydown:$=>{I($),u($)},onBlur:i,onFocus:a,onMousedown:f}}}),Po=["role","aria-labelledby"];function Do(e,o,d,l,c,u){return P(),j("ul",{ref:e.dropdownListWrapperRef,class:Z(e.dropdownKls),style:oo(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:o[0]||(o[0]=(...s)=>e.onBlur&&e.onBlur(...s)),onFocus:o[1]||(o[1]=(...s)=>e.onFocus&&e.onFocus(...s)),onKeydown:o[2]||(o[2]=x((...s)=>e.handleKeydown&&e.handleKeydown(...s),["self"])),onMousedown:o[3]||(o[3]=x((...s)=>e.onMousedown&&e.onMousedown(...s),["self"]))},[B(e.$slots,"default")],46,Po)}var he=G(No,[["render",Do],["__file","dropdown-menu.vue"]]);const Ho=Ne($o,{DropdownItem:_e,DropdownMenu:he}),Vo=ve(_e),Jo=ve(he);export{Vo as E,Jo as a,Ho as b};
