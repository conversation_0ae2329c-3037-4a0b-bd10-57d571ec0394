import{c9 as q,ca as T,cb as K,cc as w,cd as D,ce as j,cf as B,cg as V,ch as P,ci as z,cj as N,ck as W,cl as A,cm as G,cn as H}from"./CmRxzTqw.js";import{a as J,i as X,m as k,r as Y,c as O,B as Z,C as _,F as $,u as C}from"./CUZG7cWw.js";class ee extends q{constructor(e,t){super(),this.client=e,this.options=t,this.trackedProps=new Set,this.selectError=null,this.bindMethods(),this.setOptions(t)}bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.currentQuery.addObserver(this),x(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return S(this.currentQuery,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return S(this.currentQuery,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.clearStaleTimeout(),this.clearRefetchInterval(),this.currentQuery.removeObserver(this)}setOptions(e,t){const s=this.options,u=this.currentQuery;if(this.options=this.client.defaultQueryOptions(e),T(s,this.options)||this.client.getQueryCache().notify({type:"observerOptionsUpdated",query:this.currentQuery,observer:this}),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=s.queryKey),this.updateQuery();const i=this.hasListeners();i&&M(this.currentQuery,u,this.options,s)&&this.executeFetch(),this.updateResult(t),i&&(this.currentQuery!==u||this.options.enabled!==s.enabled||this.options.staleTime!==s.staleTime)&&this.updateStaleTimeout();const n=this.computeRefetchInterval();i&&(this.currentQuery!==u||this.options.enabled!==s.enabled||n!==this.currentRefetchInterval)&&this.updateRefetchInterval(n)}getOptimisticResult(e){const t=this.client.getQueryCache().build(this.client,e),s=this.createResult(t,e);return se(this,s,e)&&(this.currentResult=s,this.currentResultOptions=this.options,this.currentResultState=this.currentQuery.state),s}getCurrentResult(){return this.currentResult}trackResult(e){const t={};return Object.keys(e).forEach(s=>{Object.defineProperty(t,s,{configurable:!1,enumerable:!0,get:()=>(this.trackedProps.add(s),e[s])})}),t}getCurrentQuery(){return this.currentQuery}remove(){this.client.getQueryCache().remove(this.currentQuery)}refetch({refetchPage:e,...t}={}){return this.fetch({...t,meta:{refetchPage:e}})}fetchOptimistic(e){const t=this.client.defaultQueryOptions(e),s=this.client.getQueryCache().build(this.client,t);return s.isFetchingOptimistic=!0,s.fetch().then(()=>this.createResult(s,t))}fetch(e){var t;return this.executeFetch({...e,cancelRefetch:(t=e.cancelRefetch)!=null?t:!0}).then(()=>(this.updateResult(),this.currentResult))}executeFetch(e){this.updateQuery();let t=this.currentQuery.fetch(this.options,e);return e!=null&&e.throwOnError||(t=t.catch(K)),t}updateStaleTimeout(){if(this.clearStaleTimeout(),w||this.currentResult.isStale||!D(this.options.staleTime))return;const t=j(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout(()=>{this.currentResult.isStale||this.updateResult()},t)}computeRefetchInterval(){var e;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(e=this.options.refetchInterval)!=null?e:!1}updateRefetchInterval(e){this.clearRefetchInterval(),this.currentRefetchInterval=e,!(w||this.options.enabled===!1||!D(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(()=>{(this.options.refetchIntervalInBackground||B.isFocused())&&this.executeFetch()},this.currentRefetchInterval))}updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())}clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)}clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)}createResult(e,t){const s=this.currentQuery,u=this.options,i=this.currentResult,n=this.currentResultState,a=this.currentResultOptions,l=e!==s,d=l?e.state:this.currentQueryInitialState,o=l?this.currentResult:this.previousQueryResult,{state:c}=e;let{dataUpdatedAt:f,error:v,errorUpdatedAt:m,fetchStatus:R,status:y}=c,F=!1,U=!1,p;if(t._optimisticResults){const h=this.hasListeners(),I=!h&&x(e,t),L=h&&M(e,s,t,u);(I||L)&&(R=V(e.options.networkMode)?"fetching":"paused",f||(y="loading")),t._optimisticResults==="isRestoring"&&(R="idle")}if(t.keepPreviousData&&!c.dataUpdatedAt&&o!=null&&o.isSuccess&&y!=="error")p=o.data,f=o.dataUpdatedAt,y=o.status,F=!0;else if(t.select&&typeof c.data<"u")if(i&&c.data===(n==null?void 0:n.data)&&t.select===this.selectFn)p=this.selectResult;else try{this.selectFn=t.select,p=t.select(c.data),p=P(i==null?void 0:i.data,p,t),this.selectResult=p,this.selectError=null}catch(h){this.selectError=h}else p=c.data;if(typeof t.placeholderData<"u"&&typeof p>"u"&&y==="loading"){let h;if(i!=null&&i.isPlaceholderData&&t.placeholderData===(a==null?void 0:a.placeholderData))h=i.data;else if(h=typeof t.placeholderData=="function"?t.placeholderData():t.placeholderData,t.select&&typeof h<"u")try{h=t.select(h),this.selectError=null}catch(I){this.selectError=I}typeof h<"u"&&(y="success",p=P(i==null?void 0:i.data,h,t),U=!0)}this.selectError&&(v=this.selectError,p=this.selectResult,m=Date.now(),y="error");const b=R==="fetching",Q=y==="loading",g=y==="error";return{status:y,fetchStatus:R,isLoading:Q,isSuccess:y==="success",isError:g,isInitialLoading:Q&&b,data:p,dataUpdatedAt:f,error:v,errorUpdatedAt:m,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>d.dataUpdateCount||c.errorUpdateCount>d.errorUpdateCount,isFetching:b,isRefetching:b&&!Q,isLoadingError:g&&c.dataUpdatedAt===0,isPaused:R==="paused",isPlaceholderData:U,isPreviousData:F,isRefetchError:g&&c.dataUpdatedAt!==0,isStale:E(e,t),refetch:this.refetch,remove:this.remove}}updateResult(e){const t=this.currentResult,s=this.createResult(this.currentQuery,this.options);if(this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,T(s,t))return;this.currentResult=s;const u={cache:!0},i=()=>{if(!t)return!0;const{notifyOnChangeProps:n}=this.options,a=typeof n=="function"?n():n;if(a==="all"||!a&&!this.trackedProps.size)return!0;const l=new Set(a??this.trackedProps);return this.options.useErrorBoundary&&l.add("error"),Object.keys(this.currentResult).some(d=>{const o=d;return this.currentResult[o]!==t[o]&&l.has(o)})};(e==null?void 0:e.listeners)!==!1&&i()&&(u.listeners=!0),this.notify({...u,...e})}updateQuery(){const e=this.client.getQueryCache().build(this.client,this.options);if(e===this.currentQuery)return;const t=this.currentQuery;this.currentQuery=e,this.currentQueryInitialState=e.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(t==null||t.removeObserver(this),e.addObserver(this))}onQueryUpdate(e){const t={};e.type==="success"?t.onSuccess=!e.manual:e.type==="error"&&!z(e.error)&&(t.onError=!0),this.updateResult(t),this.hasListeners()&&this.updateTimers()}notify(e){N.batch(()=>{if(e.onSuccess){var t,s,u,i;(t=(s=this.options).onSuccess)==null||t.call(s,this.currentResult.data),(u=(i=this.options).onSettled)==null||u.call(i,this.currentResult.data,null)}else if(e.onError){var n,a,l,d;(n=(a=this.options).onError)==null||n.call(a,this.currentResult.error),(l=(d=this.options).onSettled)==null||l.call(d,void 0,this.currentResult.error)}e.listeners&&this.listeners.forEach(({listener:o})=>{o(this.currentResult)}),e.cache&&this.client.getQueryCache().notify({query:this.currentQuery,type:"observerResultsUpdated"})})}}function te(r,e){return e.enabled!==!1&&!r.state.dataUpdatedAt&&!(r.state.status==="error"&&e.retryOnMount===!1)}function x(r,e){return te(r,e)||r.state.dataUpdatedAt>0&&S(r,e,e.refetchOnMount)}function S(r,e,t){if(e.enabled!==!1){const s=typeof t=="function"?t(r):t;return s==="always"||s!==!1&&E(r,e)}return!1}function M(r,e,t,s){return t.enabled!==!1&&(r!==e||s.enabled===!1)&&(!t.suspense||r.state.status!=="error")&&E(r,t)}function E(r,e){return r.isStaleByTime(e.staleTime)}function se(r,e,t){return t.keepPreviousData?!1:t.placeholderData!==void 0?e.isPlaceholderData:r.getCurrentResult()!==e}function re(r=""){var e;if(!((e=J())==null?void 0:e.proxy))throw new Error("vue-query hooks can only be used inside setup() function.");const s=W(r),u=X(s);if(!u)throw new Error("No 'queryClient' found in Vue context, use 'VueQueryPlugin' to properly initialize the library.");return u}function ie(r,e,t={},s={}){var u;const i=k(()=>ne(e,t,s)),n=(u=i.value.queryClient)!=null?u:re(i.value.queryClientKey),a=k(()=>{const f=n.defaultQueryOptions(i.value);return f._optimisticResults=n.isRestoring.value?"isRestoring":"optimistic",f}),l=new r(n,a.value),d=Y(l.getCurrentResult());let o=()=>{};O(n.isRestoring,f=>{f||(o(),o=l.subscribe(v=>{A(d,v)}))},{immediate:!0}),O(a,()=>{l.setOptions(a.value),A(d,l.getCurrentResult())},{deep:!0}),Z(()=>{o()});const c=()=>new Promise(f=>{let v=()=>{};const m=()=>{if(a.value.enabled!==!1){const R=l.getOptimisticResult(a.value);R.isStale?(v(),f(l.fetchOptimistic(a.value))):(v(),f(R))}};m(),v=O(a,m,{deep:!0})});return{..._($(d)),suspense:c}}function ne(r,e={},t={}){const s=C(r),u=C(e),i=C(t);let n=s;return G(s)?typeof u=="function"?n={...i,queryKey:s,queryFn:u}:n={...u,queryKey:s}:n=s,H(n)}function le(r,e,t){const s=ie(ee,r,e,t);return{...s,refetch:s.refetch.value,remove:s.remove.value}}export{le as u};
