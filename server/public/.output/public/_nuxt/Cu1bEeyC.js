import{E as A}from"./BCqAdQ5e.js";import{E as M,v as q,f as H}from"./CmRxzTqw.js";import{E as J,a as O}from"./sfCUuwOk.js";import{_ as G}from"./eFgaMLiC.js";import{E as K,a as Q,b as W}from"./CwgXbNrK.js";import{_ as X}from"./DJi8L2lq.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import{u as Y}from"./DRe575WM.js";import{_ as Z}from"./DKolA9W0.js";import{_ as ee}from"./gIlbrd_1.js";import{_ as te}from"./WjDLTBxx.js";import{w as ae,x as ne,A as oe,y as se,z as le}from"./qRM0tN96.js";import{l as ie,j as y,b as re,C as me,r as de,c as pe,M as h,N as ce,a3 as a,O as f,a1 as n,a7 as _e,u as p,a0 as fe,y as ue,_ as ge,ag as r}from"./CUZG7cWw.js";const ye={class:"mt-4"},we={class:"mt-4"},ke={class:"flex items-center"},be={class:"flex justify-end mt-4"},ze=ie({__name:"js",props:{appId:{}},emits:["back"],setup(E,{emit:R}){const w=E,x=R,u=y(),k=y(),c=y(),b=re({}),{appId:S}=me(w),_=de({robot_id:S,type:2}),{pager:m,getLists:s}=Y({fetchFun:ae,params:_});s();const $=async t=>{await H.confirm("确定删除？"),await le({id:t,type:_.type}),s()},j=(t,e)=>{switch(t){case"delete":$(e.id);break;case"edit":v(e);break;case"usage":V(e)}},v=t=>{var o;let e=null;t&&(e={id:t.id,name:t.name,password:t.secret}),(o=u.value)==null||o.open(e)},B=async(t,e)=>{var o;await(e=="add"?ne({...t,..._}):oe({id:t.id,name:t.name,password:t.password})),(o=u.value)==null||o.close(),s()},D=t=>{b.value=t},I=t=>{var e;D(t),(e=k.value)==null||e.open()},V=t=>{var e,o;(e=c.value)==null||e.open(),(o=c.value)==null||o.setFormData(t)},N=async t=>{var e;await se({...t,..._}),(e=c.value)==null||e.close(),s()};return pe(()=>w.appId,()=>{s()}),(t,e)=>{const o=A,l=M,d=O,P=G,g=K,T=Q,F=W,L=J,U=X,z=q;return h(),ce(ge,null,[a(o,{content:"发布JS嵌入",onBack:e[0]||(e[0]=i=>x("back"))}),f("div",ye,[a(l,{type:"primary",onClick:e[1]||(e[1]=i=>v())},{default:n(()=>e[3]||(e[3]=[r(" 创建链接 ")])),_:1})]),f("div",we,[_e((h(),fe(L,{data:p(m).lists,size:"large"},{default:n(()=>[a(d,{label:"apikey",prop:"apikey","min-width":"200"}),a(d,{label:"分享名称",prop:"name","min-width":"180","show-tooltip-when-overflow":""}),a(d,{label:"访问密码",prop:"secret","min-width":"120"}),a(d,{label:"最后使用时间",prop:"use_time","min-width":"180"}),a(d,{label:"操作","min-width":"150",fixed:"right"},{default:n(({row:i})=>[f("div",ke,[a(l,{type:"primary",link:"",onClick:C=>I(i)},{default:n(()=>e[4]||(e[4]=[r(" 查看代码 ")])),_:2},1032,["onClick"]),a(F,{class:"ml-[10px]",onCommand:C=>j(C,i)},{dropdown:n(()=>[a(T,null,{default:n(()=>[a(g,{command:"edit"},{default:n(()=>[a(l,{type:"primary",link:""},{default:n(()=>e[6]||(e[6]=[r(" 编辑 ")])),_:1})]),_:1}),a(g,{command:"usage"},{default:n(()=>[a(l,{type:"primary",link:""},{default:n(()=>e[7]||(e[7]=[r(" 用量设置 ")])),_:1})]),_:1}),a(g,{command:"delete"},{default:n(()=>[a(l,{type:"danger",link:""},{default:n(()=>e[8]||(e[8]=[r(" 删除 ")])),_:1})]),_:1})]),_:1})]),default:n(()=>[a(l,{type:"primary",link:""},{default:n(()=>[e[5]||(e[5]=r(" 更多 ")),a(P,{name:"el-icon-ArrowDown"})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[z,p(m).loading]]),f("div",be,[a(U,{modelValue:p(m),"onUpdate:modelValue":e[2]||(e[2]=i=>ue(m)?m.value=i:null),onChange:p(s)},null,8,["modelValue","onChange"])])]),a(Z,{ref_key:"createShareRef",ref:u,isShowChatType:!1,onConfirm:B},null,512),a(ee,{ref_key:"jsEmbeddingRef",ref:k,"channel-id":p(b).apikey},null,8,["channel-id"]),a(te,{ref_key:"usageSettingsRef",ref:c,onConfirm:N},null,512)],64)}}});export{ze as _};
