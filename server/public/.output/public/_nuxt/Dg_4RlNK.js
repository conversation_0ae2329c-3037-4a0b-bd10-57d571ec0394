import{b as u}from"./CUZG7cWw.js";function p(){const r=u([]);return{images:r,splitImage:c=>new Promise((g,m)=>{const e=document.createElement("canvas"),t=e.getContext("2d"),o=new Image;o.setAttribute("crossOrigin",""),o.onload=()=>{const{width:n,height:h}=o,s=n/2,a=h/2;e.width=s,e.height=a,r.value=[];for(let i=0;i<2;i++)for(let l=0;l<2;l++)t==null||t.clearRect(0,0,s,a),t==null||t.drawImage(o,-i*s,-l*a),r.value.push(e==null?void 0:e.toDataURL());g(r.value)},o.onerror=n=>{m(n)},o.src=c})}}export{p as useImageSplit};
