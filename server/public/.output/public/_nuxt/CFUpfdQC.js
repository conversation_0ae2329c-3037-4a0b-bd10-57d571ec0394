import{a as L,l as M,b as T,j as U,m as j,_ as O,s as F,e as H}from"./CmRxzTqw.js";import{u as J}from"./DNOp0HuO.js";import I from"./5UV0D3wj.js";import Z from"./DqCCFNAt.js";import G from"./yYY54G-s.js";import K from"./fiVT4wSQ.js";import{l as P,b as p,ai as Q,m as W,E as X,M as l,a0 as N,a1 as Y,O as r,a6 as tt,u as e,$ as S,a5 as V,N as A,ao as et,_ as ot,a3 as rt,y as at,a2 as st,Z as pt,n as lt}from"./CUZG7cWw.js";import{_ as it}from"./DlAUqK2U.js";import"./DluKwKHO.js";import"./BZBRZdpQ.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./C9jirCEY.js";/* empty css        *//* empty css        */import"./qRM0tN96.js";import"./BhXe-NXN.js";import"./eFgaMLiC.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CJgd20ip.js";import"./DjwCd26w.js";import"./GytdR_nJ.js";import"./Cg9HM-1v.js";import"./YwtsEmdS.js";import"./oVx59syQ.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        *//* empty css        *//* empty css        */import"./DAOx25wS.js";import"./CRNANWso.js";import"./CHg9aK2B.js";import"./CbQsrhNE.js";import"./DrxPZuc-.js";import"./DYjlFFbo.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./Bu_nKEGp.js";import"./CfDE0MAs.js";import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";const mt={class:"h-full flex flex-col 4xl:w-[2000px] mx-auto"},nt={class:"tabs-list grid grid-cols-4 gap-4 mt-4"},ct=["onClick"],ut={class:"2xl:max-w-[880px] xl:max-w-[780px] lg:max-w-[680px] max-w-[680px] search w-full mt-4"},xt={key:0,class:"flex-1 min-h-0 mx-[16px] relative"},dt=P({__name:"index",async setup(_t){let s,x;const z=L(),D=M(),R=T();U();const i=[{label:"智能体",value:0,component:I},{label:"AI绘画",value:1,component:Z},{label:"AI音乐",value:2,component:G},{label:"AI视频",value:3,component:K}],o=p(0),a=p(""),m=p(I),n=p(!0),{data:c}=([s,x]=Q(()=>J(()=>j({id:6}),{lazy:!0,default(){return[]},transform:t=>JSON.parse(t.data)},"$mrRHbfubzc")),s=await s,x(),s),B=W(()=>t=>{switch(t){case 1:return"text-black";case 2:return"text-white";case 3:return"text-primary"}}),$=async t=>{o.value!==t&&(a.value="",o.value=t,n.value=!1,m.value=i[o.value].component,await lt(),n.value=!0,D.replace({path:"",query:{type:t}}))};return X(async()=>{const t=z.query.type;t&&(o.value=Number(t),m.value=i[o.value].component)}),(t,d)=>{const q=H,E=O;return l(),N(E,{name:"default"},{default:Y(()=>{var _,f,v,b,y,h,w,g,k;return[r("div",mt,[r("header",{class:"robot-square-header flex flex-col justify-center items-center px-[16px] m-[16px] rounded-[12px] overflow-hidden",style:tt({"background-image":`url(${e(R).getImageUrl((v=(f=(_=e(c))==null?void 0:_[0])==null?void 0:f.prop)==null?void 0:v.banner_bg)})`})},[r("div",{class:S(["font-medium 2xl:text-[50px] xl:text-[40px] lg:text-[36px] text-[36px]",e(B)((h=(y=(b=e(c))==null?void 0:b[0])==null?void 0:y.prop)==null?void 0:h.title_color)])},V((k=(g=(w=e(c))==null?void 0:w[0])==null?void 0:g.prop)==null?void 0:k.title),3),r("div",nt,[(l(),A(ot,null,et(i,(u,C)=>r("div",{class:S(["tabs-item bg-white",{"is-active":e(o)===C}]),onClick:ft=>$(C)},V(u.label),11,ct)),64))]),r("div",ut,[rt(q,{size:"large",class:"2xl:h-[54px] xl:h-[48px] lg:h-[44px] rounded-[12px]",style:{"--el-border-color":"transparent"},modelValue:e(a),"onUpdate:modelValue":d[0]||(d[0]=u=>at(a)?a.value=u:null),clearable:!0,"prefix-icon":e(F),placeholder:"请输入关键词搜索"},null,8,["modelValue","prefix-icon"])])],4),e(n)?(l(),A("div",xt,[(l(),N(st(e(m)),{keyword:e(a)},null,8,["keyword"]))])):pt("",!0)])]}),_:1})}}}),xe=it(dt,[["__scopeId","data-v-411df7fd"]]);export{xe as default};
