import{_ as I}from"./eFgaMLiC.js";import{l as R,a as T,E as O,cJ as j,f as D,cK as P}from"./CmRxzTqw.js";import{E as U}from"./BCqAdQ5e.js";import{E as $}from"./CiabO6Xq.js";import{_ as H}from"./CbQsrhNE.js";import{E as J}from"./ArzC3z2d.js";/* empty css        *//* empty css        *//* empty css        */import{u as C}from"./Bj_9-7Jh.js";import{u as K}from"./DymDsCmz.js";import{l as M,b as w,c as Z,k as A,M as l,N as r,a3 as a,a1 as c,O as s,u as o,y as G,ag as u,a5 as Q,_ as x,Z as f,a0 as W}from"./CUZG7cWw.js";const X={class:"bg-primary h-[60px] flex items-center text-white px-main"},Y={class:"text-white text-base"},ee={class:"text-white"},te={class:"ml-auto"},oe={class:"flex flex-col items-center justify-center"},se={class:"w-[400px] h-[200px] relative text-white rounded-lg overflow-hidden"},ae={class:"loading absolute z-10 bg-[rgba(0,0,0,0.5)] inset-0 flex flex-col justify-center items-center"},ne={class:"my-[20px] flex items-center"},ke=M({__name:"design-header",setup(ie){const t=K(),_=R(),v=T(),i=w(!1),m=w(-1),h=()=>{_.push("/digital_human/aside/video_compositing")},{isLock:b,lockFn:E}=C(async()=>{await t.savaOrComposite(),_.replace({path:"",query:{...v.query,id:t.id}})}),{isLock:V,lockFn:L}=C(async()=>{await t.putImgCover(),await t.savaOrComposite(2),await k(),i.value=!0});let d=null;const k=async()=>{const{status:n}=await j({id:t.id});m.value=n,n==2&&(d=setTimeout(()=>{k()},30*1e3))};Z(i,n=>{console.log(n),n||d&&clearTimeout(d)}),A(()=>{d&&clearTimeout(d)});const S=async()=>{const{value:n}=await D.prompt("修改视频名称","",{inputValue:t.name});t.id&&await P({name:n,id:t.id}),t.name=n},B=()=>{t.voiceContent.text="",t.voiceContent.voice_url="",i.value=!1,t.id=void 0,_.replace({path:"",query:{...v.query,id:void 0}})};return(n,e)=>{const p=I,g=O,N=U,F=$,q=H,z=J;return l(),r("div",X,[a(N,{onBack:h},{content:c(()=>[s("span",Y,[u(Q(o(t).name)+" ",1),a(g,{link:"",text:"",onClick:S},{default:c(()=>[s("span",ee,[a(p,{name:"el-icon-EditPen"})])]),_:1})])]),_:1}),s("div",te,[a(g,{plain:"",style:{"--el-button-bg-color":"transparent","--el-button-text-color":"#fff"},loading:o(b),onClick:o(E)},{default:c(()=>e[2]||(e[2]=[u(" 存为草稿 ")])),_:1},8,["loading","onClick"]),a(g,{onClick:o(L),loading:o(V)},{default:c(()=>e[3]||(e[3]=[u(" 合成视频 ")])),_:1},8,["onClick","loading"])]),a(z,{modelValue:o(i),"onUpdate:modelValue":e[1]||(e[1]=y=>G(i)?i.value=y:null),title:"视频生成",width:"500px","close-on-click-modal":!1,"show-close":!1},{default:c(()=>[s("div",oe,[s("div",se,[s("div",ae,[o(m)==2?(l(),r(x,{key:0},[a(p,{class:"el-icon is-loading",size:30,name:"el-icon-Loading"}),e[4]||(e[4]=s("div",{class:"mt-[10px] text-lg"},"视频生成中",-1))],64)):f("",!0),o(m)==3?(l(),r(x,{key:1},[a(p,{size:30,name:"el-icon-SuccessFilled"}),e[5]||(e[5]=s("div",{class:"mt-[10px] text-lg"},"视频合成成功",-1))],64)):f("",!0),o(m)==4?(l(),r(x,{key:2},[a(p,{size:30,name:"el-icon-CircleCloseFilled"}),e[6]||(e[6]=s("div",{class:"mt-[10px] text-lg"},"视频合成失败",-1))],64)):f("",!0)]),o(t).cover?(l(),W(F,{key:0,class:"w-full h-full",src:o(t).cover,fit:"contain"},null,8,["src"])):f("",!0)]),s("div",ne,[e[8]||(e[8]=u(" 可在前往 ")),a(q,{to:"/digital_human/aside/video_compositing",replace:!0},{default:c(()=>e[7]||(e[7]=[s("span",{class:"cursor-pointer text-primary"}," 视频合成 ",-1)])),_:1}),e[9]||(e[9]=u(" 处查看 ，或留在当前页面 ")),o(m)==4?(l(),r("span",{key:0,class:"cursor-pointer text-primary",onClick:e[0]||(e[0]=y=>i.value=!1)}," 重新合成视频 ")):(l(),r("span",{key:1,class:"cursor-pointer text-primary",onClick:B}," 继续创作 "))])])]),_:1},8,["modelValue"])])}}});export{ke as _};
