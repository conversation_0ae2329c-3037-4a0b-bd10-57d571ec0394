import{_ as f}from"./eFgaMLiC.js";import{_ as x}from"./BvSuqySp.js";import{E as b}from"./CmRxzTqw.js";import{u as v,a as h}from"./DymDsCmz.js";import{u as k}from"./DoCT-qbH.js";import{l as C,M as t,N as a,u as o,_ as g,O as i,a3 as s,$ as w,a1 as y}from"./CUZG7cWw.js";import{_ as B}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CiYvFM4x.js";import"./DY7CbrCZ.js";import"./Bfmn7p7A.js";const V={key:0,class:"px-[10px]"},$={class:"flex-1 min-w-0 mx-[5px]"},E=C({__name:"select-dub",setup(N){const e=v(),{changeTabs:r}=h(),{play:l,audioPlaying:n,pause:m}=k(),p=()=>{e.dub.VoiceUrl?n.value?m():l(e.dub.VoiceUrl):r("dub")},u=()=>{e.dub={}};return(z,D)=>{const c=f,_=x,d=b;return t(),a("div",{class:"h-[40px] px-[10px] max-w-[220px] flex items-center shadow-[0_2px_6px_#ebefff] rounded-full bg-white cursor-pointer",onClick:p},[o(e).dub.Voice?(t(),a(g,{key:1},[i("div",{class:w(["flex",{playing:o(n)}])},[s(c,{name:"local-icon-dub",size:"18"})],2),i("div",$,[s(_,{content:`${o(e).dub.Name}-${o(e).dub.Desc}`},null,8,["content"])]),s(d,{link:"",onClick:u},{default:y(()=>[s(c,{name:"el-icon-Close",size:"20"})]),_:1})],64)):(t(),a("div",V,"选择配音"))])}}}),K=B(E,[["__scopeId","data-v-ad3e9966"]]);export{K as default};
