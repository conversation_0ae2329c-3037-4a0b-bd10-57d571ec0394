import{_ as m}from"./eFgaMLiC.js";import{l as d,M as s,N as t,_,ao as f,O as a,a5 as u,a3 as h}from"./CUZG7cWw.js";const g={class:"flex flex-wrap mx-[-7px] mb-[-14px]"},b=["onClick"],k={class:"flex-1 line-clamp-1 text-tx-secondary"},v={class:"text-primary flex ml-[10px]"},w=d({__name:"search-ex",props:{lists:{default:()=>[]},prop:{default:""}},emits:["click-item"],setup(c,{emit:l}){const o=c,p=l,r=e=>(console.log(e),e[o.prop]?e[o.prop]:e);return(e,y)=>{const i=m;return s(),t("div",g,[(s(!0),t(_,null,f(e.lists,(n,x)=>(s(),t("div",{class:"flex max-w-full items-center mx-[7px] cursor-pointer hover:bg-fill-light mb-[14px] px-[15px] py-[10px] border border-br-light border-solid rounded-[12px]",key:x,onClick:B=>p("click-item",r(n))},[a("div",k,u(r(n)),1),a("span",v,[h(i,{name:"el-icon-Right"})])],8,b))),128))])}}});export{w as _};
