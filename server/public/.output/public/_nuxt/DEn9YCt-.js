import{E as f}from"./oVx59syQ.js";import{b as _,dH as x,dI as v,E as g}from"./CmRxzTqw.js";/* empty css        */import{E as b}from"./ArzC3z2d.js";import{l as C,b as E,m as u,c as N,M as T,a0 as w,a1 as n,u as B,O as l,a3 as p,ag as V}from"./CUZG7cWw.js";import{_ as h}from"./DlAUqK2U.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";const y=["innerHTML"],S={class:"flex-1 flex justify-center items-center bg-body pt-[20px]"},k=C({__name:"index",setup(D){const i=_(),o=E(!1),c=u(()=>i.getBulletinConfig.bulletin_content),m=u(()=>i.getBulletinConfig.is_bulletin),d=t=>{const e=x(v),a=new Date().toDateString(),s=!e.value||e.value!==a;return s&&t&&(e.value=a),s};return N(()=>m.value,t=>{t&&d(t)&&(o.value=!0)},{deep:!0,immediate:!0}),(t,e)=>{const a=f,s=g;return T(),w(B(b),{modelValue:o.value,"onUpdate:modelValue":e[1]||(e[1]=r=>o.value=r),width:"600","append-to-body":""},{header:n(()=>e[2]||(e[2]=[l("div",{class:"text-lg text-center font-medium"},"公告",-1)])),default:n(()=>[p(a,{"max-height":"400px"},{default:n(()=>[l("div",{class:"richText",innerHTML:c.value},null,8,y)]),_:1}),l("div",S,[p(s,{type:"primary",size:"large",onClick:e[0]||(e[0]=r=>o.value=!1)},{default:n(()=>e[3]||(e[3]=[V(" 我知道了 ")])),_:1})])]),_:1},8,["modelValue"])}}}),P=h(k,[["__scopeId","data-v-cac163f6"]]);export{P as default};
