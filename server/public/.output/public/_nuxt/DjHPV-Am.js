import{I as k,aq as y,J as h,H as v,M as b,N as g}from"./CmRxzTqw.js";import{l as u,m as C,M as n,N as o,a0 as r,a1 as E,a2 as B,u as l,Z as i,$ as d,V as f}from"./CUZG7cWw.js";const $=k({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},href:{type:String,default:""},target:{type:String,default:"_self"},icon:{type:y}}),N={click:t=>t instanceof MouseEvent},S=["href","target"],_=u({name:"ElLink"}),w=u({..._,props:$,emits:N,setup(t,{emit:c}){const s=t,a=h("link"),p=C(()=>[a.b(),a.m(s.type),a.is("disabled",s.disabled),a.is("underline",s.underline&&!s.disabled)]);function m(e){s.disabled||c("click",e)}return(e,L)=>(n(),o("a",{class:d(l(p)),href:e.disabled||!e.href?void 0:e.href,target:e.disabled||!e.href?void 0:e.target,onClick:m},[e.icon?(n(),r(l(v),{key:0},{default:E(()=>[(n(),r(B(e.icon)))]),_:1})):i("v-if",!0),e.$slots.default?(n(),o("span",{key:1,class:d(l(a).e("inner"))},[f(e.$slots,"default")],2)):i("v-if",!0),e.$slots.icon?f(e.$slots,"icon",{key:2}):i("v-if",!0)],10,S))}});var I=b(w,[["__file","link.vue"]]);const V=g(I);export{V as E};
