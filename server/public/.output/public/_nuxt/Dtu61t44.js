import{h as I,f as v,e as N,E as U}from"./CmRxzTqw.js";import{a as q,E as L}from"./B7GaOiDz.js";import{_ as $}from"./eFgaMLiC.js";import"./DP2rzg_V.js";/* empty css        */import{u as z}from"./Bj_9-7Jh.js";import{w as M}from"./Dl64kDm5.js";import{l as O,b as H,M as s,N as m,O as u,a3 as a,a1 as r,_ as k,ao as y,u as i,y as R,ag as V,a5 as x,a0 as S}from"./CUZG7cWw.js";const T={class:"web-page"},j={class:"py-4"},A={class:"flex-1"},G={class:"mb-2 text-tx-primary font-medium text-lg"},oe=O({__name:"web-page",props:{modelValue:{}},emits:["update:modelValue"],setup(E,{emit:g}){const t=I(E,"modelValue",g),o=H(""),w=async l=>{await v.confirm(`确定删除：${l.name}？`);const e=t.value.indexOf(l);e!==-1&&t.value.splice(e,1)},{lockFn:b,isLock:h}=z(async()=>{if(!o.value)return v.msgError("请输入网页链接");const l=await M({url:o.value.split(`
`).filter(Boolean)});t.value=[...l.map(e=>({data:[{a:"",q:e.content}],path:"",name:e.url})),...t.value],o.value=""});return(l,e)=>{const p=N,d=L,_=U,B=q,C=$;return s(),m("div",T,[u("div",j,[a(B,null,{default:r(()=>[a(d,null,{default:r(()=>[u("div",A,[a(p,{modelValue:i(o),"onUpdate:modelValue":e[0]||(e[0]=n=>R(o)?o.value=n:null),placeholder:"请输入要解析的网页链接，添加多个请按回车键分隔",type:"textarea",resize:"none",rows:6},null,8,["modelValue"])])]),_:1}),a(d,null,{default:r(()=>[a(_,{type:"primary",loading:i(h),onClick:i(b)},{default:r(()=>e[1]||(e[1]=[V(" 解析 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1}),u("div",null,[(s(!0),m(k,null,y(i(t),(n,f)=>(s(),m("div",{key:f,class:"mb-4"},[u("div",G,[V(" #"+x(f+1)+" "+x(n.name)+" ",1),a(_,{link:"",type:"primary"},{default:r(()=>[a(C,{name:"el-icon-Delete",onClick:c=>w(n)},null,8,["onClick"])]),_:2},1024)]),(s(!0),m(k,null,y(n.data,(c,F)=>(s(),S(p,{key:F,modelValue:c.q,"onUpdate:modelValue":D=>c.q=D,placeholder:"文件内容，空内容会自动省略",type:"textarea",resize:"none",rows:15},null,8,["modelValue","onUpdate:modelValue"]))),128))]))),128))])])])}}});export{oe as _};
