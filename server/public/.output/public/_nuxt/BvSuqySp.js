import{a as p,E as n}from"./Zz2DnF66.js";import{T as f}from"./CmRxzTqw.js";import"./l0sNRNKZ.js";/* empty css        */import{l as c,j as u,b as d,M as m,N as v,a3 as w,a1 as y,O as h,a5 as _,a6 as b,W as x,u as g}from"./CUZG7cWw.js";const T={class:"overflow-tooltip"},C=c({__name:"index",props:{...p,teleported:{type:Boolean,default:!1},placement:{type:String,default:"top"},overflowType:{type:String,default:"ellipsis"},line:{type:Number,default:1}},setup(a){const r=a,e=u(),t=d(!1);return f(e,"mouseenter",()=>{var o,s,l,i;if(r.disabled){t.value=!0;return}((o=e.value)==null?void 0:o.scrollWidth)>((s=e.value)==null?void 0:s.offsetWidth)||((l=e.value)==null?void 0:l.scrollHeight)>((i=e.value)==null?void 0:i.offsetHeight)?t.value=!1:t.value=!0}),(o,s)=>{const l=n;return m(),v("div",T,[w(l,x(r,{"popper-class":"overflow-tooltip-popper whitespace-pre-wrap",disabled:g(t)}),{default:y(()=>[h("div",{ref_key:"textRef",ref:e,class:"overflow-text line-clamp-1",style:b({textOverflow:a.overflowType,"-webkit-line-clamp":a.line})},_(o.content),5)]),_:1},16,["disabled"])])}}});export{C as _};
