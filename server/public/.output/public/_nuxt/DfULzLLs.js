import{by as tt,b as ot,j as at,l as st,bz as Re,bA as nt,f as V,E as rt}from"./CmRxzTqw.js";import{_ as it}from"./CbQsrhNE.js";import{_ as lt}from"./CH6wv3Pu.js";import{_ as ct,a as dt,b as ut}from"./CCGM0zxW.js";import{_ as ft}from"./eFgaMLiC.js";import{E as pt}from"./Do9LV2MU.js";import{_ as _t}from"./D5RYEqFL.js";import{l as mt,b as v,j as J,r as le,ai as ce,c as Z,m as vt,E as ht,n as de,k as yt,M as f,N as q,O as n,u as e,a5 as D,a0 as C,a1 as c,a3 as h,ag as M,Z as S,a6 as Ce,$ as G,_ as Se,ao as Ee,a4 as gt,a7 as W,a8 as K}from"./CUZG7cWw.js";import{u as bt}from"./DAOx25wS.js";import{u as ue}from"./DNOp0HuO.js";import{u as wt,a as xt}from"./C-cKpkeq.js";import{u as kt}from"./DoCT-qbH.js";import{g as It,v as Rt,c as Ct,a as St,b as Et,r as Tt,d as qt}from"./qRM0tN96.js";import{u as Lt}from"./Ce6KOvmZ.js";import{P as zt}from"./CaNlADry.js";import{E as At}from"./oVx59syQ.js";import{_ as $t}from"./DlAUqK2U.js";const Nt={class:"h-full flex flex-col"},Vt={class:"flex items-center p-4 px-[40px] border-b border-solid border-br-light"},Dt=["src"],Mt={class:"ml-[15px]"},Pt={class:"flex items-center"},Bt={class:"text-2xl line-clamp-1"},jt={class:"text-tx-secondary mt-[4px] line-clamp-2"},Ft={class:"absolute top-0 left-0 w-full h-full flex flex-col z-10"},Ot={class:"flex-1 min-h-0"},Ut={class:"py-4 px-8"},Ht={class:"my-[5px]"},Jt={key:1,class:"flex flex-col",style:{"margin-left":"52px"}},Zt=["onClick"],Gt={class:"mr-2 text-tx-primary"},Wt={class:"flex flex-col justify-center items-center"},Kt=["width","height","id"],Qt={class:"text-xs text-white"},Yt={class:"px-[30px]"},Xt={class:"absolute top-0 left-0 w-full h-full flex justify-center items-center"},eo=["src"],to=["src"],oo={class:"flex justify-center mt-4"},ao=mt({__name:"index",props:{robotId:{},squareId:{}},async setup(Te){let b,L;const d=Te,fe=tt(),z=ot(),w=at(),u=Lt(),y=v(1),{copy:qe}=bt(),Q=J(),E=le({_index:-1,robot_id:d.robotId,record_id:-1,content:""}),Le=(t,o)=>{var _;E.record_id=t.id,E._index=o,(_=Q.value)==null||_.open()},ze=async()=>{var t;try{await Ct(E),(t=Q.value)==null||t.close(),E.content="",p.value[E._index].is_feedback=1}catch(o){console.log("反馈提交失败-->",o)}},pe=async()=>{if(!d.squareId)await u.getSessionLists(),u.setSessionSelect();else return[]};[b,L]=ce(()=>ue(()=>pe(),{lazy:!0},"$kMIVzocZNF")),await b,L();const p=v([]),Ae=st();let Y=0;const N=async()=>{if(!u.sessionId&&!d.squareId)return[];const t=await St({square_id:d.squareId,category_id:u.sessionId,robot_id:d.robotId,page_size:25e3});if(p.value=t.lists||[],y.value===2&&k.value==3){const o=p.value[p.value.length-1];o&&o.id!==Y&&(Y=o.id,Ze(Y))}};[b,L]=ce(()=>ue(()=>N(),{lazy:!0},"$RvfqdxlKCY")),await b,L();const{data:a,refresh:$e}=([b,L]=ce(()=>ue(()=>It({id:d.robotId}),{default(){return{}},lazy:!0},"$atbZEM2Qb5")),b=await b,L(),b);Z(()=>d.robotId,()=>{$e()});const Ne=async()=>{if(!w.isLogin)return w.toggleShowLogin();!u.sessionId&&!d.squareId||(await V.confirm("确定清空记录？"),await Et({square_id:d.squareId,category_id:u.sessionId,robot_id:d.robotId}),N())},P=J(),Ve=()=>{var t;if(!w.isLogin)return(t=P.value)==null||t.blur(),w.toggleShowLogin();T()};let r=null;const x=v(!1);let X=!1;const ee=v([]),B=async(t,o="input")=>{var O;if(!w.isLogin)return w.toggleShowLogin();if(!t)return V.msgError("请输入问题");if(x.value||!d.robotId)return;g(3);const _=Date.now();x.value=!0,p.value.push({type:1,content:t}),p.value.push({type:2,typing:!0,content:"",key:_}),(O=P.value)==null||O.setInputValue();const m=p.value.find(i=>i.key===_);d.squareId||(u.sessionId||(X=!0,await u.sessionAdd(),X=!1),u.getCurrentSession.name==="新的会话"&&await u.sessionEdit({id:u.sessionId,name:t})),r=Tt({square_id:d.squareId,cate_id:u.sessionId,robot_id:d.robotId,question:t,stream:!0}),r.addEventListener("chat",({data:i})=>{const{data:l,index:R}=i;m.content||(m.content=""),m.content+=l}),r.addEventListener("question",({data:i})=>{ee.value=JSON.parse(i.data)}),r.addEventListener("file",({data:i})=>{try{const l=JSON.parse(i.data);m.files=l}catch(l){console.error(l)}}),r.addEventListener("image",({data:i})=>{try{const l=JSON.parse(i.data);m.images=l}catch(l){console.error(l)}}),r.addEventListener("video",({data:i})=>{try{const l=JSON.parse(i.data);m.videos=l}catch(l){console.error(l)}}),r.addEventListener("close",async()=>{await w.getUser(),setTimeout(async()=>{await N(),m.typing=!1,x.value=!1,await de(),T()},500)}),r.addEventListener("error",async i=>{var l,R;if(A.value&&g(2),o==="input"&&((l=P.value)==null||l.setInputValue(t)),((R=i.data)==null?void 0:R.code)===1100){z.getIsShowRecharge?(await V.confirm(`${z.getTokenUnit}数量已用完，请前往充值`),Ae.push("/user/recharge")):V.msgError(`${z.getTokenUnit}数量已用完。请联系客服增加`);return}i.errorType==="connectError"&&V.msgError("请求失败，请重试"),["connectError","responseError"].includes(i.errorType)&&p.value.splice(p.value.length-2,2),m.typing=!1,setTimeout(()=>{x.value=!1},200)})},te=J(),_e=v(),T=async()=>{var o,_,m;const t=(_=(o=te.value)==null?void 0:o.wrapRef)==null?void 0:_.scrollHeight;(m=te.value)==null||m.setScrollTop(t)},{height:De}=Re(_e);nt(De,()=>{x.value&&T()},{throttle:500,immediate:!0});const me=J(),{height:Me,width:Pe}=Re(me),ve=vt(()=>!(Pe.value/Me.value>1)),oe=()=>{r==null||r.removeEventListener("close"),r==null||r.removeEventListener("chat"),r==null||r.removeEventListener("error"),r==null||r.abort(),x.value=!1};Z(()=>u.sessionId,async t=>{t&&!X&&(oe(),await N(),T())},{immediate:!0});const k=v(0),Be=le({0:"正在初始化对话...",1:"点击开始说话",2:"我在听，您请说...",3:"稍等，让我想一想",4:"正在回复中..."}),g=t=>{y.value===2&&(k.value=t)},ae=v(!1),se=v(0),ne=v(!1),he=v(0),ye=v(0),I=le({id:"audio-canvas",width:100,height:40,minHeight:5,scale:2}),{render:je,stopRender:Fe,draw:Oe}=wt(I),{start:Ue,stop:re,isRecording:j,authorize:so,close:no,isOpen:ro}=xt({onstart(){ne.value=!1,se.value=Date.now()},async onstop(t){if(Fe(),Oe(new Float64Array(new Array(128).fill(0)),0),!!ae.value){ae.value=!1,g(3);try{const o=await Rt({file:t.blob});if(!o.text){A.value&&g(2);return}B(o.text,"voice")}catch{A.value&&g(2)}}},ondata(t){je(t);const o=Date.now();t.powerLevel>15&&(clearTimeout(he.value),k.value=2,ne.value=!0,se.value=o,he.value=setTimeout(()=>{ae.value=!0,clearTimeout(ye.value),Je(),re()},2e3)),o-se.value>=5e3&&ne.value}}),He=()=>{var t;ye.value=setTimeout(()=>{Ke()},((t=a.value.digital)==null?void 0:t.idle_time)*1e3||0)},{play:ge,pause:Je,audioPlaying:be}=kt({onstart(){k.value=4,ie.value&&(ie.value=!1,re())},onstop(){He(),A.value?g(2):g(1)},onerror(){g(1)}}),we=async t=>{const{url:o}=await qt(t);return o},Ze=async t=>{ge(async()=>await we({type:2,record_id:t}),!1)},Ge=async()=>{j.value||y.value!==2||Ue()},A=v(!0),We=async()=>{[4,3].includes(k.value)||(j.value?(A.value=!1,re(),g(1)):(A.value=!0,g(2)))},F=v(""),ie=v(!1),Ke=async()=>{if(y.value!==2||!a.value.is_digital||!a.value.digital_id||a.value.is_disable||(F.value||(F.value=await we({type:3,record_id:a.value.id})),!F.value))return Promise.reject();ie.value=!0;const t=Date.now();p.value.push({type:2,typing:!1,content:a.value.digital.idle_reply,key:t}),await de(),T(),ge(F.value,!1)};return Z(k,t=>{switch(t){case 2:Ge()}}),Z(()=>d.robotId,async t=>{oe(),u.setRobotId(t),await N(),T(),await pe()},{immediate:!0}),ht(async()=>{await de(),p.value.length&&T()}),yt(()=>{oe()}),(t,o)=>{const _=rt,m=it,O=lt,i=ct,l=dt,R=ft,Qe=ut,Ye=pt,Xe=_t;return f(),q("div",Nt,[n("div",Vt,[n("img",{class:"flex-none w-[40px] h-[40px] rounded-full",src:e(a).image,alt:""},null,8,Dt),n("div",Mt,[n("div",Pt,[n("div",Bt,D(e(a).name),1),t.squareId?S("",!0):(f(),C(m,{key:0,to:"/application/layout/robot",class:"ml-[10px]"},{default:c(()=>[h(_,{type:"info",round:"",text:"",bg:"",style:{border:"none","--el-button-hover-text-color":`var(
                                    --el-color-info
                                )`}},{default:c(()=>o[3]||(o[3]=[M(" 切换智能体 ")])),_:1})]),_:1}))]),n("div",jt,D(e(a).intro),1)])]),h(Ye,{class:"flex-1 min-h-0",content:e(z).getChatConfig.watermark,font:{color:e(fe)?"rgba(256,256,256,0.08)":"rgba(0,0,0,0.06)",fontSize:12}},{default:c(()=>{var U,xe,ke,Ie;return[n("div",{ref_key:"containerRef",ref:me,class:"h-full flex flex-col rounded relative",style:Ce({background:e(y)==2?e(a).digital_bg:""})},[n("div",Ft,[n("div",Ot,[h(e(At),{ref_key:"scrollbarRef",ref:te},{default:c(()=>[n("div",Ut,[n("div",{ref_key:"innerRef",ref:_e},[e(a).welcome_introducer?(f(),C(i,{key:0,class:G(["mb-[20px]",{"opacity-70":e(y)===2}]),type:"left",bg:"var(--el-bg-color-page)",avatar:e(a).icons?e(a).icons:e(a).image},{default:c(()=>[h(O,{content:e(a).welcome_introducer,onClickCustomLink:o[0]||(o[0]=s=>B(s,"link"))},null,8,["content"])]),_:1},8,["avatar","class"])):S("",!0),(f(!0),q(Se,null,Ee(e(p),(s,$)=>(f(),q("div",{key:s.id+""+$,class:"mt-4 sm:pb-[20px]"},[s.type==1?(f(),C(i,{key:0,type:"right",avatar:e(w).userInfo.avatar,color:"white",class:G({"opacity-70":e(y)===2})},{actions:c(()=>[n("div",Ht,[h(_,{link:"",type:"info",onClick:H=>e(qe)(s.content)},{icon:c(()=>[h(R,{name:"el-icon-CopyDocument"})]),default:c(()=>[o[4]||(o[4]=M(" 复制 "))]),_:2},1032,["onClick"])])]),default:c(()=>[h(l,{content:String(s.content)},null,8,["content"])]),_:2},1032,["avatar","class"])):S("",!0),s.type==2?(f(),C(i,{key:1,type:"left",time:s.create_time,avatar:e(a).icons?e(a).icons:e(a).image,bg:"var(--el-bg-color-page)",class:G({"opacity-70":e(y)===2}),modelName:s.model},{outer_actions:c(()=>[s.create_time?(f(),C(_,{key:0,class:"ml-[52px] mt-2",style:{"--el-button-border-color":"transparent","--el-color-info-light-8":"transparent"},type:s.is_feedback?"info":"primary",plain:!0,bg:"",size:"small",disabled:s.is_feedback,onClick:H=>Le(s,$)},{default:c(()=>[M(D(s.is_feedback?"已反馈":"反馈"),1)]),_:2},1032,["type","disabled","onClick"])):S("",!0),$===e(p).length-1&&!e(x)?(f(),q("div",Jt,[(f(!0),q(Se,null,Ee(e(ee).length?e(ee):s.correlation,(H,et)=>(f(),q("div",{key:et,class:"inline-flex items-center rounded-[12px] bg-page cursor-pointer mt-[10px] hover:bg-primary-light-9",style:{padding:"8px 12px",width:"fit-content"},onClick:gt(io=>B(H,"input"),["stop"])},[n("span",Gt,D(H),1),h(R,{name:"el-icon-Right",color:"#999",size:"20"})],8,Zt))),128))])):S("",!0)]),default:c(()=>[h(l,{content:String(s.content),type:"html",typing:s.typing,"line-numbers":!e(z).isMobile,"show-copy":"","show-context":!!e(a).is_show_context,"show-quote":!!e(a).is_show_quote,"show-voice":e(z).getIsVoiceOpen,context:s.context,"show-poster":"","record-list":e(p),quotes:s.quotes,images:s.images,files:s.files,videos:s.videos,"record-id":s.id,"record-type":2},null,8,["content","typing","line-numbers","show-context","show-quote","show-voice","context","record-list","quotes","images","files","videos","record-id"])]),_:2},1032,["time","avatar","class","modelName"])):S("",!0)]))),128))],512)])]),_:1},512)]),W(n("div",Wt,[n("canvas",{style:Ce({width:`${e(I).width}px`,height:`${e(I).height}px`}),width:e(I).width*e(I).scale,height:e(I).height*e(I).scale,id:e(I).id},null,12,Kt),n("div",Qt,[n("div",null,D(e(Be)[e(k)]),1)])],512),[[K,e(y)==2]]),n("div",Yt,[h(Qe,{ref_key:"chatActionRef",ref:P,loading:e(x)||e(k)===3,"show-manual":!!e(a).is_artificial,"btn-color":e(fe)?"#333":"#f6f6f6",onEnter:B,onClear:Ne,onPause:o[1]||(o[1]=s=>{var $;return($=e(r))==null?void 0:$.abort()}),onFocus:Ve,menus:e(a).menus},{btn:c(()=>[e(a).is_digital?(f(),C(m,{key:0,target:"_blank",to:{path:"/digital/chat",query:{id:t.robotId,squareId:t.squareId,cateId:e(u).sessionId}},class:"flex items-center mr-[10px]"},{default:c(()=>[h(_,{type:"primary",round:"",plain:""},{default:c(()=>o[5]||(o[5]=[M(" 形象对话")])),_:1})]),_:1},8,["to"])):S("",!0)]),_:1},8,["loading","show-manual","btn-color","menus"])]),e(y)==2?(f(),q("div",{key:0,class:G(["recorder",{"recorder--stop":!e(j)}]),onClick:We},[e(j)?(f(),C(R,{key:0,name:"el-icon-Microphone",size:40})):(f(),C(R,{key:1,name:"el-icon-Mute",size:40}))],2)):S("",!0)]),W(n("div",Xt,[W(n("video",{class:"h-full w-full object-scale-down",src:e(ve)?(U=e(a).digital)==null?void 0:U.vertical_stay_video:(xe=e(a).digital)==null?void 0:xe.wide_stay_video,autoplay:"",muted:"",loop:""},null,8,eo),[[K,!e(be)]]),W(n("video",{class:"h-full w-full object-scale-down",src:e(ve)?(ke=e(a).digital)==null?void 0:ke.vertical_talk_video:(Ie=e(a).digital)==null?void 0:Ie.wide_talk_video,autoplay:"",muted:"",loop:""},null,8,to),[[K,e(be)]])],512),[[K,e(y)==2]])],4)]}),_:1},8,["content","font"]),h(zt,{ref_key:"popupRef",ref:Q,async:!0,title:"问题反馈",appendToBody:!1,class:"feedback-pop"},{footer:c(()=>[n("div",oo,[h(_,{type:"primary",onClick:ze},{default:c(()=>o[6]||(o[6]=[M(" 提交反馈 ")])),_:1})])]),default:c(()=>[h(Xe,{modelValue:e(E).content,"onUpdate:modelValue":o[2]||(o[2]=U=>e(E).content=U),rows:"8",placeholder:"描述一下你遇到了什么问题"},null,8,["modelValue"])]),_:1},512)])}}}),Co=$t(ao,[["__scopeId","data-v-9289f797"]]);export{Co as _};
