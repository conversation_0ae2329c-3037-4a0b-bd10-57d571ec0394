import{s as Pt}from"./CRNANWso.js";import{g as Lt}from"./CmRxzTqw.js";import{b as Rt,j as Ht,o as Ot,c as Nt}from"./CUZG7cWw.js";var Et={exports:{}};(function(z1){(function(q1){var k1=typeof window=="object"&&!!window.document,Y1=k1?window:Object;(function(Q,S1){var Z1=function(){},o1=function(l){return typeof l=="number"},r1=function(l){return JSON.stringify(l)},s1=function(l){return new p1(l)},y1=s1.LM="2024-10-20 22:15",R1="https://github.com/xiangyuecn/Recorder",u1="Recorder",C1="getUserMedia",x1="srcSampleRate",v1="sampleRate",d1="bitRate",_1="catch",A1=Q[u1];if(A1&&A1.LM==y1)return A1.CLog(A1.i18n.$T("K8zP::重复导入{1}",0,u1),3);s1.IsOpen=function(){var l=s1.Stream;if(l){var w=M1(l),m=w[0];if(m){var M=m.readyState;return M=="live"||M==m.LIVE}}return!1},s1.BufferSize=4096,s1.Destroy=function(){for(var l in c1(u1+" Destroy"),E1(),C)C[l]()};var C={};s1.BindDestroy=function(l,w){C[l]=w},s1.Support=function(){if(!S1)return!1;var l=navigator.mediaDevices||{};return l[C1]||(l=navigator)[C1]||(l[C1]=l.webkitGetUserMedia||l.mozGetUserMedia||l.msGetUserMedia),!!l[C1]&&(s1.Scope=l,!!s1.GetContext())},s1.GetContext=function(l){if(!S1)return null;var w=window.AudioContext;if(w||(w=window.webkitAudioContext),!w)return null;var m=s1.Ctx,M=0;return m||(m=s1.Ctx=new w,M=1,s1.NewCtxs=s1.NewCtxs||[],s1.BindDestroy("Ctx",function(){var i=s1.Ctx;i&&i.close&&(t1(i),s1.Ctx=0);var y=s1.NewCtxs;s1.NewCtxs=[];for(var L=0;L<y.length;L++)t1(y[L])})),l&&m.close&&(M||(m._useC||t1(m),m=new w),m._useC=1,s1.NewCtxs.push(m)),m},s1.CloseNewCtx=function(l){if(l&&l.close){t1(l);for(var w=s1.NewCtxs||[],m=w.length,M=0;M<w.length;M++)if(w[M]==l){w.splice(M,1);break}c1(N("mSxV::剩{1}个GetContext未close",0,m+"-1="+w.length),w.length?3:0)}};var t1=function(l){if(l&&l.close&&!l._isC&&(l._isC=1,l.state!="closed"))try{l.close()}catch(w){c1("ctx close err",1,w)}},i1=s1.ResumeCtx=function(l,w,m,M){var i=0,y=0,L=0,J=0,j="EventListener",X="ResumeCtx ",T=function(r,n){y&&e(),i||(i=1,r&&M(r,J),n&&m(J)),n&&(!l._LsSC&&l["add"+j]&&l["add"+j]("statechange",t),l._LsSC=1,L=1)},e=function(r){if(!r||!y){y=r?1:0;for(var n=["focus","mousedown","mouseup","touchstart","touchend"],s=0;s<n.length;s++)window[(r?"add":"remove")+j](n[s],t,!0)}},t=function(){var r=l.state,n=f1(r);if(!i&&!w(n?++J:J))return T();n?(L&&c1(X+"sc "+r,3),e(1),l.resume().then(function(){L&&c1(X+"sc "+l.state),T(0,1)})[_1](function(s){c1(X+"error",1,s),f1(l.state)||T(s.message||"error")})):r=="closed"?(L&&!l._isC&&c1(X+"sc "+r,1),T("ctx closed")):T(0,1)};t()},f1=s1.CtxSpEnd=function(l){return l=="suspended"||l=="interrupted"},w1=function(l){var w=l.state,m="ctx.state="+w;return f1(w)&&(m+=N("nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）")),m},m1="ConnectEnableWebM";s1[m1]=!0;var a1="ConnectEnableWorklet";s1[a1]=!1;var E=function(l){var w=l.BufferSize||s1.BufferSize,m=l.Stream,M=m._c,i=M[v1],y={},L=M1(m),J=L[0],j=null,X="";if(J&&J.getSettings){var T=(j=J.getSettings())[v1];T&&T!=i&&(X=N("eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，",0,T,i))}m._ts=j,c1(X+"Stream TrackSet: "+r1(j),X?3:0);var e,t,r,n=function(V){var P=m._m=M.createMediaStreamSource(m),S=M.destination,v="createMediaStreamDestination";M[v]&&(S=m._d=M[v]()),P.connect(V),V.connect(S)},s="",a=m._call,u=function(V,P){for(var S in a){if(P!=i){y.index=0;var v=(y=s1.SampleData([V],P,i,y,{_sum:1})).data,A=y._sum}else{y={};for(var D=V.length,v=new Int16Array(D),A=0,F=0;F<D;F++){var B=Math.max(-1,Math.min(1,V[F]));B=B<0?32768*B:32767*B,v[F]=B,A+=Math.abs(B)}}for(var R in a)a[R](v,A);return}},p="ScriptProcessor",f="audioWorklet",c=u1+" "+f,o="RecProc",_="MediaRecorder",b=_+".WebM.PCM",h=M.createScriptProcessor||M.createJavaScriptNode,g=N("ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。",0,f),k=function(){t=m.isWorklet=!1,V1(m),c1(N("7TU0::Connect采用老的{1}，",0,p)+Y.get(N(s1[a1]?"JwCL::但已设置{1}尝试启用{2}":"VGjB::可设置{1}尝试启用{2}",2),[u1+"."+a1+"=true",f])+s+g,3);var V=m._p=h.call(M,w,1,1);n(V),V.onaudioprocess=function(P){var S=P.inputBuffer.getChannelData(0);u(S,i)}},x=function(){e=m.isWebM=!1,T1(m),t=m.isWorklet=!h||s1[a1];var V=window.AudioWorkletNode;if(t&&M[f]&&V){var P=function(){return t&&m._na},S=m._na=function(){r!==""&&(clearTimeout(r),r=setTimeout(function(){r=0,P()&&(c1(N("MxX1::{1}未返回任何音频，恢复使用{2}",0,f,p),3),h&&k())},500))},v=function(){if(P()){var D=m._n=new V(M,o,{processorOptions:{bufferSize:w}});n(D),D.port.onmessage=function(F){r&&(clearTimeout(r),r=""),P()?u(F.data.val,i):t||c1(N("XUap::{1}多余回调",0,f),3)},c1(N("yOta::Connect采用{1}，设置{2}可恢复老式{3}",0,f,u1+"."+a1+"=false",p)+s+g,3)}},A=function(){if(P())if(M[o])v();else{var D,F,B=(F="class "+o+" extends AudioWorkletProcessor{",F+="constructor "+(D=function(R){return R.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,c)})(function(R){DEL_super(R);var H=this,O=R.processorOptions.bufferSize;H.bufferSize=O,H.buffer=new Float32Array(2*O),H.pos=0,H.port.onmessage=function(z){z.data.kill&&(H.kill=!0,$C.log("$RA kill call"))},$C.log("$RA .ctor call",R)}),F+="process "+D(function(R,H,O){var z=this,Z=z.bufferSize,q=z.buffer,$=z.pos;if((R=(R[0]||[])[0]||[]).length){q.set(R,$);var K=~~(($+=R.length)/Z)*Z;if(K){this.port.postMessage({val:q.slice(0,K)});var e1=q.subarray(K,$);(q=new Float32Array(2*Z)).set(e1),$=e1.length,z.buffer=q}z.pos=$}return!z.kill}),F=(F+='}try{registerProcessor("'+o+'", '+o+')}catch(e){$C.error("'+c+' Reg Error",e)}').replace(/\$C\./g,"console."),"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(F))));M[f].addModule(B).then(function(R){P()&&(M[o]=1,v(),r&&S())})[_1](function(R){c1(f+".addModule Error",1,R),P()&&k()})}};i1(M,function(){return P()},A,A)}else k()};(function(){var V=window[_],P="ondataavailable",S="audio/webm; codecs=pcm";e=m.isWebM=s1[m1];var v=V&&P in V.prototype&&V.isTypeSupported(S);if(s=v?"":N("VwPd::（此浏览器不支持{1}）",0,b),!e||!v)return x();var A=function(){return e&&m._ra};m._ra=function(){r!==""&&(clearTimeout(r),r=setTimeout(function(){A()&&(c1(N("vHnb::{1}未返回任何音频，降级使用{2}",0,_,f),3),x())},500))};var D=Object.assign({mimeType:S},s1.ConnectWebMOptions),F=m._r=new V(m,D),B=m._rd={};F[P]=function(R){var H=new FileReader;H.onloadend=function(){if(A()){var O=B1(new Uint8Array(H.result),B);if(!O)return;if(O==-1)return void x();r&&(clearTimeout(r),r=""),u(O,B.webmSR)}else e||c1(N("O9P7::{1}多余回调",0,_),3)},H.readAsArrayBuffer(R.data)},F.start(~~(w/48)),c1(N("LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}",0,b,u1+"."+m1+"=false",f,p))})()},H1=function(l){l._na&&l._na(),l._ra&&l._ra()},V1=function(l){l._na=null,l._n&&(l._n.port.postMessage({kill:!0}),l._n.disconnect(),l._n=null)},T1=function(l){if(l._ra=null,l._r){try{l._r.stop()}catch(w){c1("mr stop err",1,w)}l._r=null}},E1=function(l){var w=(l=l||s1)==s1,m=l.Stream;m&&(m._m&&(m._m.disconnect(),m._m=null),!m._RC&&m._c&&s1.CloseNewCtx(m._c),m._RC=null,m._c=null,m._d&&(O1(m._d.stream),m._d=null),m._p&&(m._p.disconnect(),m._p.onaudioprocess=m._p=null),V1(m),T1(m),w&&O1(m)),l.Stream=0},O1=s1.StopS_=function(l){for(var w=M1(l),m=0;m<w.length;m++){var M=w[m];M.stop&&M.stop()}l.stop&&l.stop()},M1=function(l){var w=0,m=0,M=[];l.getAudioTracks&&(w=l.getAudioTracks(),m=l.getVideoTracks()),w||(w=l.audioTracks,m=l.videoTracks);for(var i=0,y=w?w.length:0;i<y;i++)M.push(w[i]);for(var i=0,y=m?m.length:0;i<y;i++)M.push(m[i]);return M};s1.SampleData=function(l,w,m,M,i){var y="SampleData";M||(M={});var L=M.index||0,J=M.offset||0,j=M.raisePrev||0,X=M.filter;if(X&&X.fn&&(X.sr&&X.sr!=w||X.srn&&X.srn!=m)&&(X=null,c1(N("d48C::{1}的filter采样率变了，重设滤波",0,y),3)),!X)if(m<=w){var T=3*w/4<m?0:m/2*3/4;X={fn:T?s1.IIRFilter(!0,w,T):0}}else{var T=3*m/4<w?0:w/2*3/4;X={fn:T?s1.IIRFilter(!0,m,T):0}}X.sr=w,X.srn=m;var e=X.fn,t=M.frameNext||[];i||(i={});var r=i.frameSize||1;i.frameType&&(r=i.frameType=="mp3"?1152:1);var n=i._sum,s=0,a=l.length;a+1<L&&c1(N("tlbC::{1}似乎传入了未重置chunk {2}",0,y,L+">"+a),3);for(var u=0,p=L;p<a;p++)u+=l[p].length;var f=w/m;if(1<f)u=Math.max(0,u-Math.floor(J)),u=Math.floor(u/f);else if(f<1){var c=1/f;u=Math.floor(u*c)}u+=t.length;for(var o=new Int16Array(u),_=0,p=0;p<t.length;p++)o[_]=t[p],_++;for(;L<a;L++){var b=l[L],h=b instanceof Float32Array,p=J,g=b.length,k=e&&e.Embed,x=0,V=0,P=0,S=0;if(f<1){for(var v=_+p,A=j,D=0;D<g;D++){var F=b[D];h&&(F=(F=Math.max(-1,Math.min(1,F)))<0?32768*F:32767*F);var B=Math.floor(v);v+=c;for(var R=Math.floor(v),H=(F-A)/(R-B),O=1;B<R;B++,O++){var z=Math.floor(A+O*H);k?(P=z,S=k.b0*P+k.b1*k.x1+k.b0*k.x2-k.a1*k.y1-k.a2*k.y2,k.x2=k.x1,k.x1=P,k.y2=k.y1,k.y1=S,z=S):z=e?e(z):z,32767<z?z=32767:z<-32768&&(z=-32768),n&&(s+=Math.abs(z)),o[B]=z,_++}A=j=F,p+=c}J=p%1}else{for(var D=0,Z=0;D<g;D++,Z++){if(Z<g){var F=b[Z];h&&(F=(F=Math.max(-1,Math.min(1,F)))<0?32768*F:32767*F),k?(P=F,S=k.b0*P+k.b1*k.x1+k.b0*k.x2-k.a1*k.y1-k.a2*k.y2,k.x2=k.x1,k.x1=P,k.y2=k.y1,k.y1=S):S=e?e(F):F}if(x=V,V=S,Z!=0){var q=Math.floor(p);if(D==q){var $=Math.ceil(p),K=p-q,e1=x,g1=$<g?V:e1,G=e1+(g1-e1)*K;32767<G?G=32767:G<-32768&&(G=-32768),n&&(s+=Math.abs(G)),o[_]=G,_++,p+=f}}else D--}J=Math.max(0,p-g)}}f<1&&_+1==u&&(u--,o=new Int16Array(o.buffer.slice(0,2*u))),_-1!=u&&_!=u&&c1(y+" idx:"+_+" != size:"+u,3),t=null;var n1=u%r;if(0<n1){var b1=2*(u-n1);t=new Int16Array(o.buffer.slice(b1)),o=new Int16Array(o.buffer.slice(0,b1))}var U={index:L,offset:J,raisePrev:j,filter:X,frameNext:t,sampleRate:m,data:o};return n&&(U._sum=s),U},s1.IIRFilter=function(l,w,m){var M=2*Math.PI*m/w,i=Math.sin(M),y=Math.cos(M),L=i/2,J=1+L,j=-2*y/J,X=(1-L)/J;if(l)var T=(1-y)/2/J,e=(1-y)/J;else var T=(1+y)/2/J,e=-(1+y)/J;var t=0,r=0,n=0,s=0,a=0,u=function(p){return n=T*p+e*t+T*r-j*s-X*a,r=t,t=p,a=s,s=n};return u.Embed={x1:0,x2:0,y1:0,y2:0,b0:T,b1:e,a1:j,a2:X},u},s1.PowerLevel=function(l,w){var m=l/w||0;return m<1251?Math.round(m/1250*10):Math.round(Math.min(100,Math.max(0,100*(1+Math.log(m/1e4)/Math.log(10)))))},s1.PowerDBFS=function(l){var w=Math.max(.1,l||0);return w=Math.min(w,32767),w=20*Math.log(w/32767)/Math.log(10),Math.max(-100,Math.round(w))},s1.CLog=function(l,w){if(typeof console=="object"){var m=new Date,M=("0"+m.getMinutes()).substr(-2)+":"+("0"+m.getSeconds()).substr(-2)+"."+("00"+m.getMilliseconds()).substr(-3),i=this&&this.envIn&&this.envCheck&&this.id,y=["["+M+" "+u1+(i?":"+i:"")+"]"+l],L=arguments,J=s1.CLog,j=2,X=J.log||console.log;for(o1(w)?X=w==1?J.error||console.error:w==3?J.warn||console.warn:X:j=1;j<L.length;j++)y.push(L[j]);L1?X&&X("[IsLoser]"+y[0],1<y.length?y:""):X.apply(console,y)}};var c1=function(){s1.CLog.apply(this,arguments)},L1=!0;try{L1=!console.log.apply}catch{}var d=0;function p1(l){var w=this;w.id=++d,l1();var m={type:"mp3",onProcess:Z1};for(var M in l)m[M]=l[M];var i=(w.set=m)[d1],y=m[v1];(i&&!o1(i)||y&&!o1(y))&&w.CLog(N.G("IllegalArgs-1",[N("VtS4::{1}和{2}必须是数值",0,v1,d1)]),1,l),m[d1]=+i||16,m[v1]=+y||16e3,w.state=0,w._S=9,w.Sync={O:9,C:9}}s1.Sync={O:9,C:9},s1.prototype=p1.prototype={CLog:c1,_streamStore:function(){return this.set.sourceStream?this:s1},_streamGet:function(){return this._streamStore().Stream},_streamCtx:function(){var l=this._streamGet();return l&&l._c},open:function(l,w){var m=this,M=m.set,i=m._streamStore(),y=0;l=l||Z1;var L=function(o,_){_=!!_,m.CLog(N("5tWi::录音open失败：")+o+",isUserNotAllow:"+_,1),y&&s1.CloseNewCtx(y),w&&w(o,_)};m._streamTag=C1;var J=function(){m.CLog("open ok, id:"+m.id+" stream:"+m._streamTag),l(),m._SO=0},j=i.Sync,X=++j.O,T=j.C;if(m._O=m._O_=X,m._SO=m._S,S1){var e=m.envCheck({envName:"H5",canProcess:!0});if(e)L(N("A5bm::不能录音：")+e);else{var t,r=function(){(t=M.runningContext)||(t=y=s1.GetContext(!0))};if(M.sourceStream){if(m._streamTag="set.sourceStream",!s1.GetContext())return void L(N("1iU7::不支持此浏览器从流中获取录音"));r(),E1(i);var n=m.Stream=M.sourceStream;n._c=t,n._RC=M.runningContext,n._call={};try{E(i)}catch(o){return E1(i),void L(N("BTW2::从流中打开录音失败：")+o.message)}J()}else{var s=function(o,_){try{window.top.a}catch{return void L(N("Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})",0,'allow="camera;microphone"'))}a(1,o)&&(/Found/i.test(o)?L(_+N("jBa9::，无可用麦克风")):L(_))},a=function(o,_){if(/Permission|Allow/i.test(_))o&&L(N("gyO5::用户拒绝了录音权限"),!0);else{if(window.isSecureContext!==!1)return 1;o&&L(N("oWNo::浏览器禁止不安全页面录音，可开启https解决"))}};if(s1.IsOpen())J();else if(s1.Support()){r();var u=function(o){setTimeout(function(){o._call={};var _=s1.Stream;_&&(E1(),o._call=_._call),(s1.Stream=o)._c=t,o._RC=M.runningContext,function(){if(T!=j.C||!m._O){var b=N("dFm8::open被取消");return X==j.O?m.close():b=N("VtJO::open被中断"),L(b),!0}}()||(s1.IsOpen()?(_&&m.CLog(N("upb8::发现同时多次调用open"),1),E(i),J()):L(N("Q1GA::录音功能无效：无音频流")))},100)},p=function(o){var _=o.name||o.message||o.code+":"+o,b="";f==1&&a(0,_)&&(b=N("KxE2::，将尝试禁用回声消除后重试")),m.CLog(N("xEQR::请求录音权限错误")+b+"|"+o,b?3:1,o),b?c(1):s(_,N("bDOG::无法录音：")+o)},f=0,c=function(o){f++;var _="audioTrackSet",b="autoGainControl",h="echoCancellation",g="noiseSuppression",k=JSON.parse(r1(M[_]||!0));m.CLog("open... "+f+" "+_+":"+r1(k)),o&&(typeof k!="object"&&(k={}),k[b]=!1,k[h]=!1,k[g]=!1),k[v1]&&m.CLog(N("IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象",0,_+"."+v1),3);var x={audio:k,video:M.videoTrackSet||!1};try{var V=s1.Scope[C1](x,u,p)}catch(P){m.CLog(C1,3,P),x={audio:!0,video:!1},V=s1.Scope[C1](x,u,p)}m.CLog(C1+"("+r1(x)+") "+w1(t)+N("RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置",0,"audioTrackSet:{echoCancellation,noiseSuppression,autoGainControl}",_)+"("+R1+") LM:"+y1+" UA:"+navigator.userAgent),V&&V.then&&V.then(u)[_1](p)};c()}else s("",N("COxc::此浏览器不支持录音"))}}}else L(N.G("NonBrowser-1",["open"])+N("EMJq::，可尝试使用RecordApp解决方案")+"("+R1+"/tree/master/app-support-sample)")},close:function(l){l=l||Z1;var w=this,m=w._streamStore();w._stop();var M=" stream:"+w._streamTag,i=m.Sync;if(w._O=0,w._O_!=i.O)return w.CLog(N("hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）")+M,3),void l();i.C++,E1(m),w.CLog("close,"+M),l()},mock:function(l,w){var m=this;return m._stop(),m.isMock=1,m.mockEnvInfo=null,m.buffers=[l],m.recSize=l.length,m._setSrcSR(w),m._streamTag="mock",m},_setSrcSR:function(l){var w=this.set,m=w[v1];l<m?w[v1]=l:m=0,this[x1]=l,this.CLog(x1+": "+l+" set."+v1+": "+w[v1]+(m?" "+N("UHvm::忽略")+": "+m:""),m?3:0)},envCheck:function(l){var w,m=this.set,M="CPU_BE";if(w||s1[M]||typeof Int8Array!="function"||new Int8Array(new Int32Array([1]).buffer)[0]||(l1(M),w=N("Essp::不支持{1}架构",0,M)),!w){var i=m.type,y=this[i+"_envCheck"];m.takeoffEncodeChunk&&(y?l.canProcess||(w=N("7uMV::{1}环境不支持实时处理",0,l.envName)):w=N("2XBl::{1}类型不支持设置takeoffEncodeChunk",0,i)+(this[i]?"":N("LG7e::(未加载编码器)"))),!w&&y&&(w=this[i+"_envCheck"](l,m))}return w||""},envStart:function(l,w){var m=this,M=m.set;if(m.isMock=l?1:0,m.mockEnvInfo=l,m.buffers=[],m.recSize=0,l&&(m._streamTag="env$"+l.envName),m.state=1,m.envInLast=0,m.envInFirst=0,m.envInFix=0,m.envInFixTs=[],m._setSrcSR(w),m.engineCtx=0,m[M.type+"_start"]){var i=m.engineCtx=m[M.type+"_start"](M);i&&(i.pcmDatas=[],i.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(l,w){var m=this,M=m.set,i=m.engineCtx;if(m.state==1){var y=m[x1],L=l.length,J=s1.PowerLevel(w,L),j=m.buffers,X=j.length;j.push(l);var T=j,e=X,t=Date.now(),r=Math.round(L/y*1e3);m.envInLast=t,m.buffers.length==1&&(m.envInFirst=t-r);var n=m.envInFixTs;n.splice(0,0,{t,d:r});for(var s=t,a=0,u=0;u<n.length;u++){var p=n[u];if(3e3<t-p.t){n.length=u;break}s=p.t,a+=p.d}var f=n[1],c=t-s,o=c-a;if(c/3<o&&(f&&1e3<c||6<=n.length)){var _=t-f.t-r;if(r/5<_){var b=!M.disableEnvInFix;if(m.CLog("["+t+"]"+Y.get(N(b?"4Kfd::补偿{1}ms":"bM5i::未补偿{1}ms",1),[_]),3),m.envInFix+=_,b){var h=new Int16Array(_*y/1e3);L+=h.length,j.push(h)}}}var g=m.recSize,k=L,x=g+k;if(m.recSize=x,i){var V=s1.SampleData(j,y,M[v1],i.chunkInfo);i.chunkInfo=V,g=i.pcmSize,k=V.data.length,x=g+k,i.pcmSize=x,j=i.pcmDatas,X=j.length,j.push(V.data),y=V[v1]}var P=Math.round(x/y*1e3),S=j.length,v=T.length,A=function(){for(var H=D?0:-k,O=j[0]==null,z=X;z<S;z++){var Z=j[z];Z==null?O=1:(H+=Z.length,i&&Z.length&&m[M.type+"_encode"](i,Z))}if(O&&i){var z=e;for(T[0]&&(z=0);z<v;z++)T[z]=null}O&&(H=D?k:0,j[0]=null),i?i.pcmSize+=H:m.recSize+=H},D=0,F="rec.set.onProcess";try{D=M.onProcess(j,J,P,y,X,A)}catch(H){console.error(F+N("gFUF::回调出错是不允许的，需保证不会抛异常"),H)}var B=Date.now()-t;if(10<B&&1e3<m.envInFirst-t&&m.CLog(F+N("2ghS::低性能，耗时{1}ms",0,B),3),D===!0){for(var R=0,u=X;u<S;u++)j[u]==null?R=1:j[u]=new Int16Array(0);R?m.CLog(N("ufqH::未进入异步前不能清除buffers"),3):i?i.pcmSize-=k:m.recSize-=k}else A()}else m.state||m.CLog("envIn at state=0",3)},start:function(){var l=this,w=1;if(l.set.sourceStream?l.Stream||(w=0):s1.IsOpen()||(w=0),w){var m=l._streamCtx();if(l.CLog(N("kLDN::start 开始录音，")+w1(m)+" stream:"+l._streamTag),l._stop(),l.envStart(null,m[v1]),l.state=3,l._SO&&l._SO+1!=l._S)l.CLog(N("Bp2y::start被中断"),3);else{l._SO=0;var M=function(){l.state==3&&(l.state=1,l.resume())},i="AudioContext resume: ",y=l._streamGet();y._call[l.id]=function(){l.CLog(i+m.state+"|stream ok"),M()},i1(m,function(L){return L&&l.CLog(i+"wait..."),l.state==3},function(L){L&&l.CLog(i+m.state),M()},function(L){l.CLog(i+m.state+N("upkE::，可能无法录音：")+L,1),M()})}}else l.CLog(N("6WmN::start失败：未open"),1)},pause:function(){var l=this,w=l._streamGet();l.state&&(l.state=2,l.CLog("pause"),w&&delete w._call[l.id])},resume:function(){var l=this,w=l._streamGet(),m="resume(wait ctx)";if(l.state==3)l.CLog(m);else if(l.state){l.state=1,l.CLog("resume"),l.envResume(),w&&(w._call[l.id]=function(i,y){l.state==1&&l.envIn(i,y)},H1(w));var M=l._streamCtx();M&&i1(M,function(i){return i&&l.CLog(m+"..."),l.state==1},function(i){i&&l.CLog(m+M.state),H1(w)},function(i){l.CLog(m+M.state+"[err]"+i,1)})}},_stop:function(l){var w=this,m=w.set;w.isMock||w._S++,w.state&&(w.pause(),w.state=0),!l&&w[m.type+"_stop"]&&(w[m.type+"_stop"](w.engineCtx),w.engineCtx=0)},stop:function(l,w,m){var M,i=this,y=i.set,L=i.envInLast-i.envInFirst,J=L&&i.buffers.length;i.CLog(N("Xq4s::stop 和start时差:")+(L?L+"ms "+N("3CQP::补偿:")+i.envInFix+"ms envIn:"+J+" fps:"+(J/L*1e3).toFixed(1):"-")+" stream:"+i._streamTag+" ("+R1+") LM:"+y1);var j=function(){i._stop(),m&&i.close()},X=function(p){i.CLog(N("u8JG::结束录音失败：")+p,1),w&&w(p),j()},T=function(p,f,c){var o="arraybuffer",_="dataType",b="DefaultDataType",h=i[_]||s1[b]||"blob",g=_+"="+h,k=p instanceof ArrayBuffer,x=0,V=k?p.byteLength:p.size;if(h==o?k||(x=1):h=="blob"?typeof Blob!="function"?x=N.G("NonBrowser-1",[g])+N("1skY::，请设置{1}",0,u1+"."+b+'="'+o+'"'):(k&&(p=new Blob([p],{type:f})),p instanceof Blob||(x=1),f=p.type||f):x=N.G("NotSupport-1",[g]),i.CLog(N("Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b",0,Date.now()-M,c,V)+" "+g+","+f),x)X(x!=1?x:N("Vkbd::{1}编码器返回的不是{2}",0,y.type,h)+", "+g);else{if(y.takeoffEncodeChunk)i.CLog(N("QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"),3);else if(V<Math.max(50,c/5))return void X(N("Sz2H::生成的{1}无效",0,y.type));l&&l(p,c,f),j()}};if(!i.isMock){var e=i.state==3;if(!i.state||e)return void X(N("wf9t::未开始录音")+(e?N("Dl2c::，开始录音前无用户交互导致AudioContext未运行"):""))}i._stop(!0);var t=i.recSize;if(t)if(i[y.type]){if(i.isMock){var r=i.envCheck(i.mockEnvInfo||{envName:"mock",canProcess:!1});if(r)return void X(N("AxOH::录音错误：")+r)}var n=i.engineCtx;if(i[y.type+"_complete"]&&n){var s=Math.round(n.pcmSize/y[v1]*1e3);return M=Date.now(),void i[y.type+"_complete"](n,function(p,f){T(p,f,s)},X)}if(M=Date.now(),i.buffers[0]){var a=s1.SampleData(i.buffers,i[x1],y[v1]);y[v1]=a[v1];var u=a.data,s=Math.round(u.length/y[v1]*1e3);i.CLog(N("CxeT::采样:{1} 花:{2}ms",0,t+"->"+u.length,Date.now()-M)),setTimeout(function(){M=Date.now(),i[y.type](u,function(f,c){T(f,c,s)},function(f){X(f)})})}else X(N("xkKd::音频buffers被释放"))}else X(N("xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载",0,y.type,u1));else X(N("Ltz3::未采集到录音"))}};var B1=function(l,w){w.pos||(w.pos=[0],w.tracks={},w.bytes=[]);var m=w.tracks,M=[w.pos[0]],i=function(){w.pos[0]=M[0]},y=w.bytes.length,L=new Uint8Array(y+l.length);if(L.set(w.bytes),L.set(l,y),w.bytes=L,!w._ht){if(I(L,M),W(L,M),!I1(I(L,M),[24,83,128,103]))return;for(I(L,M);M[0]<L.length;){var J=I(L,M),j=W(L,M),X=[0],T=0;if(!j)return;if(I1(J,[22,84,174,107])){for(;X[0]<j.length;){var e=I(j,X),t=W(j,X),r=[0],n={channels:0,sampleRate:0};if(I1(e,[174]))for(;r[0]<t.length;){var s=I(t,r),a=W(t,r),u=[0];if(I1(s,[215])){var p=h1(a);n.number=p,m[p]=n}else if(I1(s,[131])){var p=h1(a);p==1?n.type="video":p==2?(n.type="audio",T||(w.track0=n),n.idx=T++):n.type="Type-"+p}else if(I1(s,[134])){for(var f="",c=0;c<a.length;c++)f+=String.fromCharCode(a[c]);n.codec=f}else if(I1(s,[225]))for(;u[0]<a.length;){var o=I(a,u),_=W(a,u);if(I1(o,[181])){var p=0,b=new Uint8Array(_.reverse()).buffer;_.length==4?p=new Float32Array(b)[0]:_.length==8?p=new Float64Array(b)[0]:c1("WebM Track !Float",1,_),n[v1]=Math.round(p)}else I1(o,[98,100])?n.bitDepth=h1(_):I1(o,[159])&&(n.channels=h1(_))}}}w._ht=1,c1("WebM Tracks",m),i();break}}}var h=w.track0;if(h){var g=h[v1];if(w.webmSR=g,h.bitDepth==16&&/FLOAT/i.test(h.codec)&&(h.bitDepth=32,c1("WebM 16->32 bit",3)),g<8e3||h.bitDepth!=32||h.channels<1||!/(\b|_)PCM\b/i.test(h.codec))return w.bytes=[],w.bad||c1("WebM Track Unexpected",3,w),-(w.bad=1);for(var k=[],x=0;M[0]<L.length;){var e=I(L,M),t=W(L,M);if(!t)break;if(I1(e,[163])){var V=15&t[0],n=m[V];if(n){if(n.idx===0){for(var P=new Uint8Array(t.length-4),c=4;c<t.length;c++)P[c-4]=t[c];k.push(P),x+=P.length}}else c1("WebM !Track"+V,1,m)}i()}if(x){var S=new Uint8Array(L.length-w.pos[0]);S.set(L.subarray(w.pos[0])),w.bytes=S,w.pos[0]=0;for(var P=new Uint8Array(x),c=0,v=0;c<k.length;c++)P.set(k[c],v),v+=k[c].length;var b=new Float32Array(P.buffer);if(1<h.channels){for(var A=[],c=0;c<b.length;)A.push(b[c]),c+=h.channels;b=new Float32Array(A)}return b}}},I1=function(l,w){if(!l||l.length!=w.length)return!1;if(l.length==1)return l[0]==w[0];for(var m=0;m<l.length;m++)if(l[m]!=w[m])return!1;return!0},h1=function(l){for(var w="",m=0;m<l.length;m++){var M=l[m];w+=(M<16?"0":"")+M.toString(16)}return parseInt(w,16)||0},I=function(l,w,m){var M=w[0];if(!(M>=l.length)){var i=l[M],y=("0000000"+i.toString(2)).substr(-8),L=/^(0*1)(\d*)$/.exec(y);if(L){var J=L[1].length,j=[];if(!(M+J>l.length)){for(var X=0;X<J;X++)j[X]=l[M],M++;return m&&(j[0]=parseInt(L[2]||"0",2)),w[0]=M,j}}}},W=function(l,w){var m=I(l,w,1);if(m){var M=h1(m),i=w[0],y=[];if(M<2147483647){if(i+M>l.length)return;for(var L=0;L<M;L++)y[L]=l[i],i++}return w[0]=i,y}},Y=s1.i18n={lang:"zh-CN",alias:{"zh-CN":"zh","en-US":"en"},locales:{},data:{},put:function(l,w){var m=u1+".i18n.put: ",M=l.overwrite;M=M==null||M;var i=l.lang;if(!(i=Y.alias[i]||i))throw new Error(m+"set.lang?");var y=Y.locales[i];y||(y={},Y.locales[i]=y);for(var L,J=/^([\w\-]+):/,j=0;j<w.length;j++){var X=w[j];if(L=J.exec(X)){var T=L[1],X=X.substr(T.length+1);!M&&y[T]||(y[T]=X)}else c1(m+"'key:'? "+X,3,l)}},get:function(){return Y.v_G.apply(null,arguments)},v_G:function(l,w,m){w=w||[],m=m||Y.lang,m=Y.alias[m]||m;var M=Y.locales[m],i=M&&M[l]||"";return i||m=="zh"?(Y.lastLang=m,i=="=Empty"?"":i.replace(/\{(\d+)(\!?)\}/g,function(y,L,J){return y=w[(L=+L||0)-1],(L<1||L>w.length)&&(y="{?}",c1("i18n["+l+"] no {"+L+"}: "+i,3)),J?"":y})):m=="en"?Y.v_G(l,w,"zh"):Y.v_G(l,w,"en")},$T:function(){return Y.v_T.apply(null,arguments)},v_T:function(){for(var l,w=arguments,m="",M=[],i=0,y=u1+".i18n.$T:",L=/^([\w\-]*):/,J=0;J<w.length;J++){var j=w[J];if(J==0){if(l=L.exec(j),!(m=l&&l[1]))throw new Error(y+"0 'key:'?");j=j.substr(m.length+1)}if(i===-1)M.push(j);else{if(i)throw new Error(y+" bad args");if(j===0)i=-1;else if(o1(j)){if(j<1)throw new Error(y+" bad args");i=j}else{var X=J==1?"en":J?"":"zh";if((l=L.exec(j))&&(X=l[1]||X,j=j.substr(l[1].length+1)),!l||!X)throw new Error(y+J+" 'lang:'?");Y.put({lang:X,overwrite:!1},[m+":"+j])}}}return m?0<i?m:Y.v_G(m,M):""}},N=Y.$T;N.G=Y.get,N("NonBrowser-1::非浏览器环境，不支持{1}",1),N("IllegalArgs-1::参数错误：{1}",1),N("NeedImport-2::调用{1}需要先导入{2}",2),N("NotSupport-1::不支持：{1}",1),s1.TrafficImgUrl="//ia.51.la/go1?id=20469973&pvFlag=1";var l1=s1.Traffic=function(l){if(S1){l=l?"/"+u1+"/Report/"+l:"";var w=s1.TrafficImgUrl;if(w){var m=s1.Traffic,M=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],i=M[1]||"http://file/",y=(M[0]||i)+l;if(w.indexOf("//")==0&&(w=/^https:/i.test(y)?"https:"+w:"http:"+w),l&&(w=w+"&cu="+encodeURIComponent(i+l)),!m[y]){m[y]=1;var L=new Image;L.src=w,c1("Traffic Analysis Image: "+(l||u1+".TrafficImgUrl="+s1.TrafficImgUrl))}}}};A1&&(c1(N("8HO5::覆盖导入{1}",0,u1),1),A1.Destroy()),Q[u1]=s1})(Y1,k1),z1.exports&&(z1.exports=Y1.Recorder)})(),function(q1){var k1=typeof window=="object"&&!!window.document,Y1=(k1?window:Object).Recorder,Q=Y1.i18n;(function(S1,Z1,o1,r1){var s1="48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000",y1="8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160, 192, 224, 256, 320";S1.prototype.enc_mp3={stable:!0,takeEC:"full",getTestMsg:function(){return o1("Zm7L::采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）",0,s1,y1)}};var R1,u1=function(C){var t1=C.bitRate,i1=C.sampleRate,f1=i1;if((" "+y1+",").indexOf(" "+t1+",")==-1&&S1.CLog(o1("eGB9::{1}不在mp3支持的取值范围：{2}",0,"bitRate="+t1,y1),3),(" "+s1+",").indexOf(" "+i1+",")==-1){for(var w1=s1.split(", "),m1=[],a1=0;a1<w1.length;a1++)m1.push({v:+w1[a1],s:Math.abs(w1[a1]-i1)});m1.sort(function(E,H1){return E.s-H1.s}),f1=m1[0].v,C.sampleRate=f1,S1.CLog(o1("zLTa::sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}",0,f1,i1,s1),3)}},C1=function(){return o1.G("NeedImport-2",["mp3.js","src/engine/mp3-engine.js"])},x1=r1&&typeof Worker=="function";S1.prototype.mp3=function(C,t1,i1){var f1=this,w1=f1.set,m1=C.length;if(S1.lamejs){if(x1){var a1=f1.mp3_start(w1);if(a1){if(a1.isW)return f1.mp3_encode(a1,C),void f1.mp3_complete(a1,t1,i1,1);f1.mp3_stop(a1)}}u1(w1);var E=new S1.lamejs.Mp3Encoder(1,w1.sampleRate,w1.bitRate),H1=new Int8Array(5e5),V1=0,T1=0,E1=0,O1=function(){try{if(T1<m1)var M1=E.encodeBuffer(C.subarray(T1,T1+57600));else{E1=1;var M1=E.flush()}}catch(B1){if(console.error(B1),!E1)try{E.flush()}catch(I1){console.error(I1)}return void i1("MP3 Encoder: "+B1.message)}var c1=M1.length;if(0<c1){if(V1+c1>H1.length){var L1=new Int8Array(H1.length+Math.max(5e5,c1));L1.set(H1.subarray(0,V1)),H1=L1}H1.set(M1,V1),V1+=c1}if(T1<m1)T1+=57600,setTimeout(O1);else{var d=[H1.buffer.slice(0,V1)],p1=_1.fn(d,V1,m1,w1.sampleRate);A1(p1,w1),t1(d[0]||new ArrayBuffer(0),"audio/mp3")}};O1()}else i1(C1())},S1.BindDestroy("mp3Worker",function(){R1&&(S1.CLog("mp3Worker Destroy"),R1.terminate(),R1=null)}),S1.prototype.mp3_envCheck=function(C,t1){var i1="";return t1.takeoffEncodeChunk&&(d1()||(i1=o1("yhUs::当前浏览器版本太低，无法实时处理"))),i1||S1.lamejs||(i1=C1()),i1},S1.prototype.mp3_start=function(C){return d1(C)};var v1={id:0},d1=function(C,t1){var i1,f1=function(O1){var M1=O1.data,c1=i1.wkScope.wk_ctxs,L1=i1.wkScope.wk_lame,d=i1.wkScope.wk_mp3TrimFix,p1=c1[M1.id];if(M1.action=="init")c1[M1.id]={sampleRate:M1.sampleRate,bitRate:M1.bitRate,takeoff:M1.takeoff,pcmSize:0,memory:new Int8Array(5e5),mOffset:0,encObj:new L1.Mp3Encoder(1,M1.sampleRate,M1.bitRate)};else if(!p1)return;var B1=function(W){var Y=W.length;if(p1.mOffset+Y>p1.memory.length){var N=new Int8Array(p1.memory.length+Math.max(5e5,Y));N.set(p1.memory.subarray(0,p1.mOffset)),p1.memory=N}p1.memory.set(W,p1.mOffset),p1.mOffset+=Y};switch(M1.action){case"stop":if(!p1.isCp)try{p1.encObj.flush()}catch(W){console.error(W)}p1.encObj=null,delete c1[M1.id];break;case"encode":if(p1.isCp)break;p1.pcmSize+=M1.pcm.length;try{var I1=p1.encObj.encodeBuffer(M1.pcm)}catch(W){p1.err=W,console.error(W)}I1&&0<I1.length&&(p1.takeoff?a1.onmessage({action:"takeoff",id:M1.id,chunk:I1}):B1(I1));break;case"complete":p1.isCp=1;try{var I1=p1.encObj.flush()}catch(W){p1.err=W,console.error(W)}if(I1&&0<I1.length&&(p1.takeoff?a1.onmessage({action:"takeoff",id:M1.id,chunk:I1}):B1(I1)),p1.err){a1.onmessage({action:M1.action,id:M1.id,err:"MP3 Encoder: "+p1.err.message});break}var h1=[p1.memory.buffer.slice(0,p1.mOffset)],I=d.fn(h1,p1.mOffset,p1.pcmSize,p1.sampleRate);a1.onmessage({action:M1.action,id:M1.id,blob:h1[0]||new ArrayBuffer(0),meta:I})}},w1=function(O1){a1.onmessage=function(M1){var c1=M1;O1&&(c1=M1.data);var L1=v1[c1.id];L1&&(c1.action=="takeoff"?L1.set.takeoffEncodeChunk(new Uint8Array(c1.chunk.buffer)):(L1.call&&L1.call(c1),L1.call=null))}},m1=function(){var O1={worker:a1,set:C};return C?(O1.id=++v1.id,v1[O1.id]=O1,u1(C),a1.postMessage({action:"init",id:O1.id,sampleRate:C.sampleRate,bitRate:C.bitRate,takeoff:!!C.takeoffEncodeChunk,x:new Int16Array(5)})):a1.postMessage({x:new Int16Array(5)}),O1},a1=R1;if(t1||!x1)return S1.CLog(o1("k9PT::当前环境不支持Web Worker，mp3实时编码器运行在主线程中"),3),a1={postMessage:function(O1){f1({data:O1})}},i1={wkScope:{wk_ctxs:{},wk_lame:S1.lamejs,wk_mp3TrimFix:_1}},w1(),m1();try{if(!a1){var E=(f1+"").replace(/[\w\$]+\.onmessage/g,"self.postMessage"),H1=");wk_lame();self.onmessage="+(E=E.replace(/[\w\$]+\.wkScope/g,"wkScope"));H1+=";var wkScope={ wk_ctxs:{},wk_lame:wk_lame",H1+=",wk_mp3TrimFix:{rm:"+_1.rm+",fn:"+_1.fn+"} }";var V1=S1.lamejs.toString(),T1=(window.URL||webkitURL).createObjectURL(new Blob(["var wk_lame=(",V1,H1],{type:"text/javascript"}));a1=new Worker(T1),setTimeout(function(){(window.URL||webkitURL).revokeObjectURL(T1)},1e4),w1(1)}var E1=m1();return E1.isW=1,R1=a1,E1}catch(O1){return a1&&a1.terminate(),console.error(O1),d1(C,1)}};S1.prototype.mp3_stop=function(C){if(C&&C.worker){C.worker.postMessage({action:"stop",id:C.id}),C.worker=null,delete v1[C.id];var t1=-1;for(var i1 in v1)t1++;t1&&S1.CLog(o1("fT6M::mp3 worker剩{1}个未stop",0,t1),3)}},S1.prototype.mp3_encode=function(C,t1){C&&C.worker&&C.worker.postMessage({action:"encode",id:C.id,pcm:t1})},S1.prototype.mp3_complete=function(C,t1,i1,f1){var w1=this;C&&C.worker?(C.call=function(m1){f1&&w1.mp3_stop(C),m1.err?i1(m1.err):(A1(m1.meta,C.set),t1(m1.blob,"audio/mp3"))},C.worker.postMessage({action:"complete",id:C.id})):i1(o1("mPxH::mp3编码器未start"))},S1.mp3ReadMeta=function(C,t1){var i1=typeof window<"u"&&window.parseInt||typeof self<"u"&&self.parseInt||parseInt,f1=new Uint8Array(C[0]||[]);if(f1.length<4)return null;var w1=function(W,Y){return("0000000"+((Y||f1)[W]||0).toString(2)).substr(-8)},m1=w1(0)+w1(1),a1=w1(2)+w1(3);if(!/^1{11}/.test(m1))return null;var E={"00":2.5,10:2,11:1}[m1.substr(11,2)],H1={"01":3}[m1.substr(13,2)],V1={1:[44100,48e3,32e3],2:[22050,24e3,16e3],2.5:[11025,12e3,8e3]}[E];V1&&(V1=V1[i1(a1.substr(4,2),2)]);var T1=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320]][E==1?1:0][i1(a1.substr(0,4),2)];if(!(E&&H1&&T1&&V1))return null;for(var E1=Math.round(8*t1/T1),O1=H1==1?384:H1==2||E==1?1152:576,M1=O1/V1*1e3,c1=Math.floor(O1*T1/8/V1*1e3),L1=0,d=0,p1=0;p1<C.length;p1++){var B1=C[p1];if(d+=B1.byteLength,c1+3<=d){var I1=new Uint8Array(B1),h1=B1.byteLength-(d-(c1+3)+1),I=w1(h1,I1);L1=I.charAt(6)=="1";break}}return L1&&c1++,{version:E,layer:H1,sampleRate:V1,bitRate:T1,duration:E1,size:t1,hasPadding:L1,frameSize:c1,frameDurationFloat:M1}};var _1={rm:S1.mp3ReadMeta,fn:function(C,t1,i1,f1){var w1=this.rm(C,t1);if(!w1)return{size:t1,err:"mp3 unknown format"};var m1=Math.round(i1/f1*1e3),a1=Math.floor((w1.duration-m1)/w1.frameDurationFloat);if(0<a1){var E=a1*w1.frameSize-(w1.hasPadding?1:0);t1-=E;for(var H1=0,V1=[],T1=0;T1<C.length;T1++){var E1=C[T1];if(E<=0)break;E>=E1.byteLength?(E-=E1.byteLength,V1.push(E1),C.splice(T1,1),T1--):(C[T1]=E1.slice(E),H1=E1,E=0)}var O1=this.rm(C,t1);if(!O1){H1&&(C[0]=H1);for(var T1=0;T1<V1.length;T1++)C.splice(T1,0,V1[T1]);w1.err="mp3 fix error: 已还原，错误原因不明"}var M1=w1.trimFix={};M1.remove=a1,M1.removeDuration=Math.round(a1*w1.frameDurationFloat),M1.duration=Math.round(8*t1/w1.bitRate)}return w1}},A1=function(C,t1){var i1="MP3 Info: ";(C.sampleRate&&C.sampleRate!=t1.sampleRate||C.bitRate&&C.bitRate!=t1.bitRate)&&(S1.CLog(i1+o1("uY9i::和设置的不匹配{1}，已更新成{2}",0,"set:"+t1.bitRate+"kbps "+t1.sampleRate+"hz","set:"+C.bitRate+"kbps "+C.sampleRate+"hz"),3,t1),t1.sampleRate=C.sampleRate,t1.bitRate=C.bitRate);var f1=C.trimFix;f1?(i1+=o1("iMSm::Fix移除{1}帧",0,f1.remove)+" "+f1.removeDuration+"ms -> "+f1.duration+"ms",2<f1.remove&&(C.err=(C.err?C.err+", ":"")+o1("b9zm::移除帧数过多"))):i1+=(C.duration||"-")+"ms",C.err?S1.CLog(i1,C.size?1:0,C.err,C):S1.CLog(i1,C)}})(Y1,0,Q.$T,k1)}(),function(q1){function k1(){var Y1=function(I){return Math.log(I)/Math.log(10)},Q=function(I){throw new Error("abort("+I+")")};function S1(I){return new Int8Array(I)}function Z1(I){return new Int16Array(I)}function o1(I){return new Int32Array(I)}function r1(I){return new Float32Array(I)}function s1(I){return new Float64Array(I)}function y1(I){if(I.length==1)return r1(I[0]);var W=I[0];I=I.slice(1);for(var Y=[],N=0;N<W;N++)Y.push(y1(I));return Y}function R1(I){if(I.length==1)return o1(I[0]);var W=I[0];I=I.slice(1);for(var Y=[],N=0;N<W;N++)Y.push(R1(I));return Y}function u1(I){if(I.length==1)return Z1(I[0]);var W=I[0];I=I.slice(1);for(var Y=[],N=0;N<W;N++)Y.push(u1(I));return Y}function C1(I){if(I.length==1)return new Array(I[0]);var W=I[0];I=I.slice(1);for(var Y=[],N=0;N<W;N++)Y.push(C1(I));return Y}var x1={fill:function(I,W,Y,N){if(arguments.length==2)for(var l1=0;l1<I.length;l1++)I[l1]=W;else for(var l1=W;l1<Y;l1++)I[l1]=N}},v1={arraycopy:function(I,W,Y,N,l1){for(var l=W+l1;W<l;)Y[N++]=I[W++]}},d1={};function _1(I){this.ordinal=I}d1.SQRT2=1.4142135623730951,d1.FAST_LOG10=function(I){return Y1(I)},d1.FAST_LOG10_X=function(I,W){return Y1(I)*W},_1.short_block_allowed=new _1(0),_1.short_block_coupled=new _1(1),_1.short_block_dispensed=new _1(2),_1.short_block_forced=new _1(3);var A1={};function C(I){this.ordinal=I}function t1(I){var W=I;this.ordinal=function(){return W}}function i1(){var I=null;function W(t){this.bits=0|t}this.qupvt=null,this.setModules=function(t){this.qupvt=t,I=t};var Y=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function N(t,r,n,s,a,u){var p=.5946/r;for(t>>=1;t--!=0;)a[u++]=p>n[s++]?0:1,a[u++]=p>n[s++]?0:1}function l1(t,r,n,s,a,u){var p=(t>>=1)%2;for(t>>=1;t--!=0;){var f,c,o,_,b,h,g,k;f=n[s++]*r,c=n[s++]*r,b=0|f,o=n[s++]*r,h=0|c,_=n[s++]*r,g=0|o,f+=I.adj43[b],k=0|_,c+=I.adj43[h],a[u++]=0|f,o+=I.adj43[g],a[u++]=0|c,_+=I.adj43[k],a[u++]=0|o,a[u++]=0|_}p!=0&&(f=n[s++]*r,c=n[s++]*r,b=0|f,h=0|c,f+=I.adj43[b],c+=I.adj43[h],a[u++]=0|f,a[u++]=0|c)}var l=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function w(t,r,n,s){var a=function(f,c,o){var _=0,b=0;do{var h=f[c++],g=f[c++];_<h&&(_=h),b<g&&(b=g)}while(c<o);return _<b&&(_=b),_}(t,r,n);switch(a){case 0:return a;case 1:return function(f,c,o,_){var b=0,h=E.ht[1].hlen;do{var g=2*f[c+0]+f[c+1];c+=2,b+=h[g]}while(c<o);return _.bits+=b,1}(t,r,n,s);case 2:case 3:return function(f,c,o,_,b){var h,g,k=0,x=E.ht[_].xlen;g=_==2?E.table23:E.table56;do{var V=f[c+0]*x+f[c+1];c+=2,k+=g[V]}while(c<o);return(h=65535&k)<(k>>=16)&&(k=h,_++),b.bits+=k,_}(t,r,n,l[a-1],s);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return function(f,c,o,_,b){var h=0,g=0,k=0,x=E.ht[_].xlen,V=E.ht[_].hlen,P=E.ht[_+1].hlen,S=E.ht[_+2].hlen;do{var v=f[c+0]*x+f[c+1];c+=2,h+=V[v],g+=P[v],k+=S[v]}while(c<o);var A=_;return g<h&&(h=g,A++),k<h&&(h=k,A=_+2),b.bits+=h,A}(t,r,n,l[a-1],s);default:var u,p;for(E1.IXMAX_VAL<a&&Q(),a-=15,u=24;u<32&&!(E.ht[u].linmax>=a);u++);for(p=u-8;p<24&&!(E.ht[p].linmax>=a);p++);return function(f,c,o,_,b,h){var g,k=65536*E.ht[_].xlen+E.ht[b].xlen,x=0;do{var V=f[c++],P=f[c++];V!=0&&(14<V&&(V=15,x+=k),V*=16),P!=0&&(14<P&&(P=15,x+=k),V+=P),x+=E.largetbl[V]}while(c<o);return(g=65535&x)<(x>>=16)&&(x=g,_=b),h.bits+=x,_}(t,r,n,p,u,s)}}function m(t,r,n,s,a,u,p,f){for(var c=r.big_values,o=2;o<d.SBMAX_l+1;o++){var _=t.scalefac_band.l[o];if(c<=_)break;var b=a[o-2]+r.count1bits;if(n.part2_3_length<=b)break;var h=new W(b),g=w(s,_,c,h);b=h.bits,n.part2_3_length<=b||(n.assign(r),n.part2_3_length=b,n.region0_count=u[o-2],n.region1_count=o-2-u[o-2],n.table_select[0]=p[o-2],n.table_select[1]=f[o-2],n.table_select[2]=g)}}this.noquant_count_bits=function(t,r,n){var s=r.l3_enc,a=Math.min(576,r.max_nonzero_coeff+2>>1<<1);for(n!=null&&(n.sfb_count1=0);1<a&&!(s[a-1]|s[a-2]);a-=2);r.count1=a;for(var u=0,p=0;3<a;a-=4){var f;if(1<(2147483647&(s[a-1]|s[a-2]|s[a-3]|s[a-4])))break;f=2*(2*(2*s[a-4]+s[a-3])+s[a-2])+s[a-1],u+=E.t32l[f],p+=E.t33l[f]}var c=u;if(r.count1table_select=0,p<u&&(c=p,r.count1table_select=1),r.count1bits=c,(r.big_values=a)==0)return c;if(r.block_type==d.SHORT_TYPE)(u=3*t.scalefac_band.s[3])>r.big_values&&(u=r.big_values),p=r.big_values;else if(r.block_type==d.NORM_TYPE){if(u=r.region0_count=t.bv_scf[a-2],p=r.region1_count=t.bv_scf[a-1],p=t.scalefac_band.l[u+p+2],u=t.scalefac_band.l[u+1],p<a){var o=new W(c);r.table_select[2]=w(s,p,a,o),c=o.bits}}else r.region0_count=7,r.region1_count=d.SBMAX_l-1-7-1,u=t.scalefac_band.l[8],(p=a)<u&&(u=p);if(u=Math.min(u,a),p=Math.min(p,a),0<u){var o=new W(c);r.table_select[0]=w(s,0,u,o),c=o.bits}if(u<p){var o=new W(c);r.table_select[1]=w(s,u,p,o),c=o.bits}if(t.use_best_huffman==2&&Q(),n!=null&&r.block_type==d.NORM_TYPE){for(var _=0;t.scalefac_band.l[_]<r.big_values;)_++;n.sfb_count1=_}return c},this.count_bits=function(t,r,n,s){var a=n.l3_enc,u=E1.IXMAX_VAL/I.IPOW20(n.global_gain);return n.xrpow_max>u?E1.LARGE_BITS:(function(p,f,c,o,_){var b,h,g,k=0,x=0,V=0,P=0,S=f,v=0,A=S,D=0,F=p,B=0;for(g=_!=null&&o.global_gain==_.global_gain,h=o.block_type==d.SHORT_TYPE?38:21,b=0;b<=h;b++){var R=-1;if((g||o.block_type==d.NORM_TYPE)&&(R=o.global_gain-(o.scalefac[b]+(o.preflag!=0?I.pretab[b]:0)<<o.scalefac_scale+1)-8*o.subblock_gain[o.window[b]]),g&&_.step[b]==R)x!=0&&(l1(x,c,F,B,A,D),x=0),V!=0&&Q();else{var H,O=o.width[b];if(k+o.width[b]>o.max_nonzero_coeff&&(H=o.max_nonzero_coeff-k+1,x1.fill(f,o.max_nonzero_coeff,576,0),(O=H)<0&&(O=0),b=h+1),x==0&&V==0&&(A=S,D=v,F=p,B=P),_!=null&&0<_.sfb_count1&&b>=_.sfb_count1&&0<_.step[b]&&R>=_.step[b]?(x!=0&&(l1(x,c,F,B,A,D),x=0,A=S,D=v,F=p,B=P),V+=O):(V!=0&&(N(V,c,F,B,A,D),V=0,A=S,D=v,F=p,B=P),x+=O),O<=0){V!=0&&Q(),x!=0&&Q();break}}b<=h&&(v+=o.width[b],P+=o.width[b],k+=o.width[b])}x!=0&&(l1(x,c,F,B,A,D),x=0),V!=0&&Q()}(r,a,I.IPOW20(n.global_gain),n,s),2&t.substep_shaping&&Q(),this.noquant_count_bits(t,n,s))},this.best_huffman_divide=function(t,r){var n=new O1,s=r.l3_enc,a=o1(23),u=o1(23),p=o1(23),f=o1(23);if(r.block_type!=d.SHORT_TYPE||t.mode_gr!=1){n.assign(r),r.block_type==d.NORM_TYPE&&(function(g,k,x,V,P,S,v){for(var A=k.big_values,D=0;D<=22;D++)V[D]=E1.LARGE_BITS;for(var D=0;D<16;D++){var F=g.scalefac_band.l[D+1];if(A<=F)break;var B=0,R=new W(B),H=w(x,0,F,R);B=R.bits;for(var O=0;O<8;O++){var z=g.scalefac_band.l[D+O+2];if(A<=z)break;var Z=B;R=new W(Z);var q=w(x,F,z,R);Z=R.bits,V[D+O]>Z&&(V[D+O]=Z,P[D+O]=D,S[D+O]=H,v[D+O]=q)}}}(t,r,s,a,u,p,f),m(t,n,r,s,a,u,p,f));var c=n.big_values;if(!(c==0||1<(s[c-2]|s[c-1])||576<(c=r.count1+2))){n.assign(r),n.count1=c;for(var o=0,_=0;c>n.big_values;c-=4){var b=2*(2*(2*s[c-4]+s[c-3])+s[c-2])+s[c-1];o+=E.t32l[b],_+=E.t33l[b]}if(n.big_values=c,n.count1table_select=0,_<o&&(o=_,n.count1table_select=1),n.count1bits=o,n.block_type==d.NORM_TYPE)m(t,n,r,s,a,u,p,f);else{if(n.part2_3_length=o,o=t.scalefac_band.l[8],c<o&&(o=c),0<o){var h=new W(n.part2_3_length);n.table_select[0]=w(s,0,o,h),n.part2_3_length=h.bits}if(o<c){var h=new W(n.part2_3_length);n.table_select[1]=w(s,o,c,h),n.part2_3_length=h.bits}r.part2_3_length>n.part2_3_length&&r.assign(n)}}}};var M=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],i=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],y=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],L=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];i1.slen1_tab=y,i1.slen2_tab=L,this.best_scalefac_store=function(t,r,n,s){var a,u,p,f,c=s.tt[r][n],o=0;for(a=p=0;a<c.sfbmax;a++){var _=c.width[a];for(p+=_,f=-_;f<0&&c.l3_enc[f+p]==0;f++);f==0&&(c.scalefac[a]=o=-2)}if(c.scalefac_scale==0&&c.preflag==0){var b=0;for(a=0;a<c.sfbmax;a++)0<c.scalefac[a]&&(b|=c.scalefac[a]);if(!(1&b)&&b!=0){for(a=0;a<c.sfbmax;a++)0<c.scalefac[a]&&(c.scalefac[a]>>=1);c.scalefac_scale=o=1}}if(c.preflag==0&&c.block_type!=d.SHORT_TYPE&&t.mode_gr==2){for(a=11;a<d.SBPSY_l&&!(c.scalefac[a]<I.pretab[a]&&c.scalefac[a]!=-2);a++);if(a==d.SBPSY_l){for(a=11;a<d.SBPSY_l;a++)0<c.scalefac[a]&&(c.scalefac[a]-=I.pretab[a]);c.preflag=o=1}}for(u=0;u<4;u++)s.scfsi[n][u]=0;for(t.mode_gr==2&&r==1&&s.tt[0][n].block_type!=d.SHORT_TYPE&&s.tt[1][n].block_type!=d.SHORT_TYPE&&(function(h,g){for(var k,x=g.tt[1][h],V=g.tt[0][h],P=0;P<E.scfsi_band.length-1;P++){for(k=E.scfsi_band[P];k<E.scfsi_band[P+1]&&!(V.scalefac[k]!=x.scalefac[k]&&0<=x.scalefac[k]);k++);if(k==E.scfsi_band[P+1]){for(k=E.scfsi_band[P];k<E.scfsi_band[P+1];k++)x.scalefac[k]=-1;g.scfsi[h][P]=1}}var S=0,v=0;for(k=0;k<11;k++)x.scalefac[k]!=-1&&(v++,S<x.scalefac[k]&&(S=x.scalefac[k]));for(var A=0,D=0;k<d.SBPSY_l;k++)x.scalefac[k]!=-1&&(D++,A<x.scalefac[k]&&(A=x.scalefac[k]));for(var P=0;P<16;P++)if(S<M[P]&&A<i[P]){var F=y[P]*v+L[P]*D;x.part2_length>F&&(x.part2_length=F,x.scalefac_compress=P)}}(n,s),o=0),a=0;a<c.sfbmax;a++)c.scalefac[a]==-2&&(c.scalefac[a]=0);o!=0&&(t.mode_gr==2?this.scale_bitcount(c):this.scale_bitcount_lsf(t,c))};var J=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],j=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],X=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(t){var r,n,s,a=0,u=0,p=t.scalefac;if(t.block_type==d.SHORT_TYPE)s=J,t.mixed_block_flag!=0&&(s=j);else if(s=X,t.preflag==0){for(n=11;n<d.SBPSY_l&&!(p[n]<I.pretab[n]);n++);if(n==d.SBPSY_l)for(t.preflag=1,n=11;n<d.SBPSY_l;n++)p[n]-=I.pretab[n]}for(n=0;n<t.sfbdivide;n++)a<p[n]&&(a=p[n]);for(;n<t.sfbmax;n++)u<p[n]&&(u=p[n]);for(t.part2_length=E1.LARGE_BITS,r=0;r<16;r++)a<M[r]&&u<i[r]&&t.part2_length>s[r]&&(t.part2_length=s[r],t.scalefac_compress=r);return t.part2_length==E1.LARGE_BITS};var T=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(t,r){var n,s,a,u,p,f,c,o,_=o1(4),b=r.scalefac;for(n=r.preflag!=0?2:0,c=0;c<4;c++)_[c]=0;if(r.block_type==d.SHORT_TYPE){s=1;var h=I.nr_of_sfb_block[n][s];for(a=o=0;a<4;a++)for(u=h[a]/3,c=0;c<u;c++,o++)for(p=0;p<3;p++)b[3*o+p]>_[a]&&(_[a]=b[3*o+p])}else{s=0;var h=I.nr_of_sfb_block[n][s];for(a=o=0;a<4;a++)for(u=h[a],c=0;c<u;c++,o++)b[o]>_[a]&&(_[a]=b[o])}for(f=!1,a=0;a<4;a++)_[a]>T[n][a]&&(f=!0);if(!f){var g,k,x,V;for(r.sfb_partition_table=I.nr_of_sfb_block[n][s],a=0;a<4;a++)r.slen[a]=e[_[a]];switch(g=r.slen[0],k=r.slen[1],x=r.slen[2],V=r.slen[3],n){case 0:r.scalefac_compress=(5*g+k<<4)+(x<<2)+V;break;case 1:r.scalefac_compress=400+(5*g+k<<2)+x;break;case 2:r.scalefac_compress=500+3*g+k}}if(!f)for(r.part2_length=0,a=0;a<4;a++)r.part2_length+=r.slen[a]*r.sfb_partition_table[a];return f};var e=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(t){for(var r=2;r<=576;r+=2){for(var n,s=0;t.scalefac_band.l[++s]<r;);for(n=Y[s][0];t.scalefac_band.l[n+1]>r;)n--;for(n<0&&(n=Y[s][0]),t.bv_scf[r-2]=n,n=Y[s][1];t.scalefac_band.l[n+t.bv_scf[r-2]+2]>r;)n--;n<0&&(n=Y[s][1]),t.bv_scf[r-1]=n}}}function f1(){}function w1(){this.setModules=function(W,Y,N){};var I=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];this.updateMusicCRC=function(W,Y,N,l1){for(var l=0;l<l1;++l)W[0]=(w=Y[N+l],m=(m=W[0])>>8^I[255&(m^w)]);var w,m}}function m1(){var I=this,W=null,Y=null;this.setModules=function(e,t,r,n){W=r,Y=n};var N=null,l1=0,l=0,w=0;function m(e,t,r){for(;0<r;){var n;w==0&&(w=8,l++,e.header[e.w_ptr].write_timing==l1&&(s=e,v1.arraycopy(s.header[s.w_ptr].buf,0,N,l,s.sideinfo_len),l+=s.sideinfo_len,l1+=8*s.sideinfo_len,s.w_ptr=s.w_ptr+1&B1.MAX_HEADER_BUF-1),N[l]=0),n=Math.min(r,w),r-=n,w-=n,N[l]|=t>>r<<w,l1+=n}var s}function M(e,t){var r,n=e.internal_flags;if(8<=t&&(m(n,76,8),t-=8),8<=t&&(m(n,65,8),t-=8),8<=t&&(m(n,77,8),t-=8),8<=t&&(m(n,69,8),t-=8),32<=t){var s=W.getLameShortVersion();if(32<=t)for(r=0;r<s.length&&8<=t;++r)t-=8,m(n,s.charCodeAt(r),8)}for(;1<=t;t-=1)m(n,n.ancillary_flag,1),n.ancillary_flag^=e.disable_reservoir?0:1}function i(e,t,r){for(var n=e.header[e.h_ptr].ptr;0<r;){var s=Math.min(r,8-(7&n));r-=s,e.header[e.h_ptr].buf[n>>3]|=t>>r<<8-(7&n)-s,n+=s}e.header[e.h_ptr].ptr=n}function y(e,t){var r,n=E.ht[t.count1table_select+32],s=0,a=t.big_values,u=t.big_values;for(r=(t.count1-t.big_values)/4;0<r;--r){var p=0,f=0;t.l3_enc[a+0]!=0&&(f+=8,t.xr[u+0]<0&&p++),t.l3_enc[a+1]!=0&&(f+=4,p*=2,t.xr[u+1]<0&&p++),t.l3_enc[a+2]!=0&&(f+=2,p*=2,t.xr[u+2]<0&&p++),t.l3_enc[a+3]!=0&&(f++,p*=2,t.xr[u+3]<0&&p++),a+=4,u+=4,m(e,p+n.table[f],n.hlen[f]),s+=n.hlen[f]}return s}function L(e,t,r,n,s){var a=E.ht[t],u=0;if(t==0)return u;for(var p=r;p<n;p+=2){var f=0,c=0,o=a.xlen,_=a.xlen,b=0,h=s.l3_enc[p],g=s.l3_enc[p+1];if(h!=0&&(s.xr[p]<0&&b++,f--),15<t){if(14<h){var k=h-15;b|=k<<1,c=o,h=15}if(14<g){var x=g-15;b<<=o,b|=x,c+=o,g=15}_=16}g!=0&&(b<<=1,s.xr[p+1]<0&&b++,f--),h=h*_+g,c-=f,f+=a.hlen[h],m(e,a.table[h],f),m(e,b,c),u+=f+c}return u}function J(e,t){var r=3*e.scalefac_band.s[3];r>t.big_values&&(r=t.big_values);var n=L(e,t.table_select[0],0,r,t);return n+=L(e,t.table_select[1],r,t.big_values,t)}function j(e,t){var r,n,s,a;r=t.big_values;var u=t.region0_count+1;return s=e.scalefac_band.l[u],u+=t.region1_count+1,a=e.scalefac_band.l[u],r<s&&(s=r),r<a&&(a=r),n=L(e,t.table_select[0],0,s,t),n+=L(e,t.table_select[1],s,a,t),n+=L(e,t.table_select[2],a,r,t)}function X(){this.total=0}function T(e,t){var r,n,s,a=e.internal_flags;return a.w_ptr,(s=a.h_ptr-1)==-1&&(s=B1.MAX_HEADER_BUF-1),r=a.header[s].write_timing-l1,0<=(t.total=r)&&Q(),n=I.getframebits(e),r+=n,t.total+=n,t.total%8!=0?t.total=1+t.total/8:t.total=t.total/8,t.total+=l+1,r}this.getframebits=function(e){var t,r=e.internal_flags;t=r.bitrate_index!=0?E.bitrate_table[e.version][r.bitrate_index]:e.brate;var n=0|72e3*(e.version+1)*t/e.out_samplerate+r.padding;return 8*n},this.flush_bitstream=function(e){var t,r,n=e.internal_flags;n.h_ptr-1,t=n.l3_side,(r=T(e,new X))<0||(M(e,r),n.ResvSize=0,t.main_data_begin=0,n.findReplayGain&&Q(),n.findPeakSample&&Q())},this.format_bitstream=function(e){var t,r=e.internal_flags;t=r.l3_side;var n=this.getframebits(e);M(e,t.resvDrain_pre),function(u,p){var f,c,o,_=u.internal_flags;if(f=_.l3_side,_.header[_.h_ptr].ptr=0,x1.fill(_.header[_.h_ptr].buf,0,_.sideinfo_len,0),u.out_samplerate<16e3?i(_,4094,12):i(_,4095,12),i(_,u.version,1),i(_,1,2),i(_,u.error_protection?0:1,1),i(_,_.bitrate_index,4),i(_,_.samplerate_index,2),i(_,_.padding,1),i(_,u.extension,1),i(_,u.mode.ordinal(),2),i(_,_.mode_ext,2),i(_,u.copyright,1),i(_,u.original,1),i(_,u.emphasis,2),u.error_protection&&i(_,0,16),u.version==1){for(i(_,f.main_data_begin,9),_.channels_out==2?i(_,f.private_bits,3):i(_,f.private_bits,5),o=0;o<_.channels_out;o++){var b;for(b=0;b<4;b++)i(_,f.scfsi[o][b],1)}for(c=0;c<2;c++)for(o=0;o<_.channels_out;o++){var h=f.tt[c][o];i(_,h.part2_3_length+h.part2_length,12),i(_,h.big_values/2,9),i(_,h.global_gain,8),i(_,h.scalefac_compress,4),h.block_type!=d.NORM_TYPE?(i(_,1,1),i(_,h.block_type,2),i(_,h.mixed_block_flag,1),h.table_select[0]==14&&(h.table_select[0]=16),i(_,h.table_select[0],5),h.table_select[1]==14&&(h.table_select[1]=16),i(_,h.table_select[1],5),i(_,h.subblock_gain[0],3),i(_,h.subblock_gain[1],3),i(_,h.subblock_gain[2],3)):(i(_,0,1),h.table_select[0]==14&&(h.table_select[0]=16),i(_,h.table_select[0],5),h.table_select[1]==14&&(h.table_select[1]=16),i(_,h.table_select[1],5),h.table_select[2]==14&&(h.table_select[2]=16),i(_,h.table_select[2],5),i(_,h.region0_count,4),i(_,h.region1_count,3)),i(_,h.preflag,1),i(_,h.scalefac_scale,1),i(_,h.count1table_select,1)}}else for(i(_,f.main_data_begin,8),i(_,f.private_bits,_.channels_out),o=c=0;o<_.channels_out;o++){var h=f.tt[c][o];i(_,h.part2_3_length+h.part2_length,12),i(_,h.big_values/2,9),i(_,h.global_gain,8),i(_,h.scalefac_compress,9),h.block_type!=d.NORM_TYPE?(i(_,1,1),i(_,h.block_type,2),i(_,h.mixed_block_flag,1),h.table_select[0]==14&&(h.table_select[0]=16),i(_,h.table_select[0],5),h.table_select[1]==14&&(h.table_select[1]=16),i(_,h.table_select[1],5),i(_,h.subblock_gain[0],3),i(_,h.subblock_gain[1],3),i(_,h.subblock_gain[2],3)):(i(_,0,1),h.table_select[0]==14&&(h.table_select[0]=16),i(_,h.table_select[0],5),h.table_select[1]==14&&(h.table_select[1]=16),i(_,h.table_select[1],5),h.table_select[2]==14&&(h.table_select[2]=16),i(_,h.table_select[2],5),i(_,h.region0_count,4),i(_,h.region1_count,3)),i(_,h.scalefac_scale,1),i(_,h.count1table_select,1)}u.error_protection&&Q();var g=_.h_ptr;_.h_ptr=g+1&B1.MAX_HEADER_BUF-1,_.header[_.h_ptr].write_timing=_.header[g].write_timing+p,_.h_ptr,_.w_ptr}(e,n);var s=8*r.sideinfo_len;if(s+=function(u){var p,f,c,o,_=0,b=u.internal_flags,h=b.l3_side;if(u.version==1)for(p=0;p<2;p++)for(f=0;f<b.channels_out;f++){var g=h.tt[p][f],k=i1.slen1_tab[g.scalefac_compress],x=i1.slen2_tab[g.scalefac_compress];for(c=o=0;c<g.sfbdivide;c++)g.scalefac[c]!=-1&&(m(b,g.scalefac[c],k),o+=k);for(;c<g.sfbmax;c++)g.scalefac[c]!=-1&&(m(b,g.scalefac[c],x),o+=x);g.block_type==d.SHORT_TYPE?o+=J(b,g):o+=j(b,g),o+=y(b,g),_+=o}else for(f=p=0;f<b.channels_out;f++){var V,P,g=h.tt[p][f],S=0;if(P=c=o=0,g.block_type==d.SHORT_TYPE){for(;P<4;P++){var v=g.sfb_partition_table[P]/3,A=g.slen[P];for(V=0;V<v;V++,c++)m(b,Math.max(g.scalefac[3*c+0],0),A),m(b,Math.max(g.scalefac[3*c+1],0),A),m(b,Math.max(g.scalefac[3*c+2],0),A),S+=3*A}o+=J(b,g)}else{for(;P<4;P++){var v=g.sfb_partition_table[P],A=g.slen[P];for(V=0;V<v;V++,c++)m(b,Math.max(g.scalefac[c],0),A),S+=A}o+=j(b,g)}o+=y(b,g),_+=S+o}return _}(e),M(e,t.resvDrain_post),s+=t.resvDrain_post,t.main_data_begin+=(n-s)/8,T(e,new X),r.ResvSize,8*t.main_data_begin!=r.ResvSize&&(r.ResvSize=8*t.main_data_begin),1e9<l1){var a;for(a=0;a<B1.MAX_HEADER_BUF;++a)r.header[a].write_timing-=l1;l1=0}return 0},this.copy_buffer=function(e,t,r,n,s){var a=l+1;if(a<=0)return 0;if(n!=0&&n<a)return-1;if(v1.arraycopy(N,0,t,r,a),l=-1,(w=0)!=s){var u=o1(1);u[0]=e.nMusicCRC,Y.updateMusicCRC(u,t,r,a),e.nMusicCRC=u[0],0<a&&(e.VBR_seek_table.nBytesWritten+=a),e.decode_on_the_fly&&Q()}return a},this.init_bit_stream_w=function(e){N=S1(h1.LAME_MAXMP3BUFFER),e.h_ptr=e.w_ptr=0,e.header[e.h_ptr].write_timing=0,l=-1,l1=w=0}}function a1(I,W,Y,N){this.xlen=I,this.linmax=W,this.table=Y,this.hlen=N}A1.MAX_VALUE=34028235e31,C.vbr_off=new C(0),C.vbr_mt=new C(1),C.vbr_rh=new C(2),C.vbr_abr=new C(3),C.vbr_mtrh=new C(4),C.vbr_default=C.vbr_mtrh,t1.STEREO=new t1(0),t1.JOINT_STEREO=new t1(1),t1.DUAL_CHANNEL=new t1(2),t1.MONO=new t1(3),t1.NOT_SET=new t1(4),f1.STEPS_per_dB=100,f1.MAX_dB=120,f1.GAIN_NOT_ENOUGH_SAMPLES=-24601,f1.GAIN_ANALYSIS_ERROR=0,f1.GAIN_ANALYSIS_OK=1,f1.INIT_GAIN_ANALYSIS_ERROR=0,f1.INIT_GAIN_ANALYSIS_OK=1,f1.MAX_ORDER=f1.YULE_ORDER=10,f1.MAX_SAMPLES_PER_WINDOW=(f1.MAX_SAMP_FREQ=48e3)*(f1.RMS_WINDOW_TIME_NUMERATOR=1)/(f1.RMS_WINDOW_TIME_DENOMINATOR=20)+1,w1.NUMTOCENTRIES=100,w1.MAXFRAMESIZE=2880,m1.EQ=function(I,W){return Math.abs(I)>Math.abs(W)?Math.abs(I-W)<=1e-6*Math.abs(I):Math.abs(I-W)<=1e-6*Math.abs(W)},m1.NEQ=function(I,W){return!m1.EQ(I,W)};var E={};function H1(I){this.bits=I}function V1(){this.over_noise=0,this.tot_noise=0,this.max_noise=0,this.over_count=0,this.over_SSD=0,this.bits=0}function T1(I,W,Y,N){this.l=o1(1+d.SBMAX_l),this.s=o1(1+d.SBMAX_s),this.psfb21=o1(1+d.PSFB21),this.psfb12=o1(1+d.PSFB12);var l1=this.l,l=this.s;arguments.length==4&&(this.arrL=I,this.arrS=W,this.arr21=Y,this.arr12=N,v1.arraycopy(this.arrL,0,l1,0,Math.min(this.arrL.length,this.l.length)),v1.arraycopy(this.arrS,0,l,0,Math.min(this.arrS.length,this.s.length)),v1.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),v1.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function E1(){var I=null,W=null,Y=null;this.setModules=function(e,t,r){I=e,W=t,Y=r},this.IPOW20=function(e){return L[e]};var N=2220446049250313e-31,l1=E1.IXMAX_VAL,l=l1+2,w=E1.Q_MAX,m=E1.Q_MAX2,M=100;this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var i=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=i,this.sfBandIndex=[new T1([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new T1([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new T1([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new T1([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new T1([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new T1([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new T1([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new T1([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new T1([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var y=r1(w+m+1),L=r1(w),J=r1(l),j=r1(l);function X(e,t){var r=Y.ATHformula(t,e);return r-=M,r=Math.pow(10,r/10+e.ATHlower)}function T(e){this.s=e}this.adj43=j,this.iteration_init=function(e){var t,r=e.internal_flags,n=r.l3_side;if(r.iteration_init_init==0){for(r.iteration_init_init=1,n.main_data_begin=0,function(c){for(var o=c.internal_flags.ATH.l,_=c.internal_flags.ATH.psfb21,b=c.internal_flags.ATH.s,h=c.internal_flags.ATH.psfb12,g=c.internal_flags,k=c.out_samplerate,x=0;x<d.SBMAX_l;x++){var V=g.scalefac_band.l[x],P=g.scalefac_band.l[x+1];o[x]=A1.MAX_VALUE;for(var S=V;S<P;S++){var v=S*k/1152,A=X(c,v);o[x]=Math.min(o[x],A)}}for(var x=0;x<d.PSFB21;x++){var V=g.scalefac_band.psfb21[x],P=g.scalefac_band.psfb21[x+1];_[x]=A1.MAX_VALUE;for(var S=V;S<P;S++){var v=S*k/1152,A=X(c,v);_[x]=Math.min(_[x],A)}}for(var x=0;x<d.SBMAX_s;x++){var V=g.scalefac_band.s[x],P=g.scalefac_band.s[x+1];b[x]=A1.MAX_VALUE;for(var S=V;S<P;S++){var v=S*k/384,A=X(c,v);b[x]=Math.min(b[x],A)}b[x]*=g.scalefac_band.s[x+1]-g.scalefac_band.s[x]}for(var x=0;x<d.PSFB12;x++){var V=g.scalefac_band.psfb12[x],P=g.scalefac_band.psfb12[x+1];h[x]=A1.MAX_VALUE;for(var S=V;S<P;S++){var v=S*k/384,A=X(c,v);h[x]=Math.min(h[x],A)}h[x]*=g.scalefac_band.s[13]-g.scalefac_band.s[12]}c.noATH&&Q(),g.ATH.floor=10*Y1(X(c,-1))}(e),J[0]=0,t=1;t<l;t++)J[t]=Math.pow(t,4/3);for(t=0;t<l-1;t++)j[t]=t+1-Math.pow(.5*(J[t]+J[t+1]),.75);for(j[t]=.5,t=0;t<w;t++)L[t]=Math.pow(2,-.1875*(t-210));for(t=0;t<=w+m;t++)y[t]=Math.pow(2,.25*(t-210-m));var s,a,u,p;for(I.huffman_init(r),32<=(t=e.exp_nspsytune>>2&63)&&(t-=64),s=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>8&63)&&(t-=64),a=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>14&63)&&(t-=64),u=Math.pow(10,t/4/10),32<=(t=e.exp_nspsytune>>20&63)&&(t-=64),p=u*Math.pow(10,t/4/10),t=0;t<d.SBMAX_l;t++)f=t<=6?s:t<=13?a:t<=20?u:p,r.nsPsy.longfact[t]=f;for(t=0;t<d.SBMAX_s;t++){var f;f=t<=5?s:t<=10?a:t<=11?u:p,r.nsPsy.shortfact[t]=f}}},this.on_pe=function(e,t,r,n,s,a){var u,p,f=e.internal_flags,c=0,o=o1(2),_=new H1(c),b=W.ResvMaxBits(e,n,_,a),h=(c=_.bits)+b;for(B1.MAX_BITS_PER_GRANULE<h&&(h=B1.MAX_BITS_PER_GRANULE),p=u=0;p<f.channels_out;++p)r[p]=Math.min(B1.MAX_BITS_PER_CHANNEL,c/f.channels_out),o[p]=0|r[p]*t[s][p]/700-r[p],o[p]>3*n/4&&(o[p]=3*n/4),o[p]<0&&(o[p]=0),o[p]+r[p]>B1.MAX_BITS_PER_CHANNEL&&(o[p]=Math.max(0,B1.MAX_BITS_PER_CHANNEL-r[p])),u+=o[p];if(b<u)for(p=0;p<f.channels_out;++p)o[p]=b*o[p]/u;for(p=0;p<f.channels_out;++p)r[p]+=o[p],b-=o[p];for(p=u=0;p<f.channels_out;++p)u+=r[p];return B1.MAX_BITS_PER_GRANULE<u&&Q(),h},this.athAdjust=function(e,t,r){var n=90.30873362,s=d1.FAST_LOG10_X(t,10),a=e*e,u=0;return s-=r,1e-20<a&&(u=1+d1.FAST_LOG10_X(a,10/n)),u<0&&(u=0),s*=u,s+=r+n-94.82444863,Math.pow(10,.1*s)},this.calc_xmin=function(e,t,r,n){var s,a=0,u=e.internal_flags,p=0,f=0,c=u.ATH,o=r.xr,_=e.VBR==C.vbr_mtrh?1:0,b=u.masking_lower;for(e.VBR!=C.vbr_mtrh&&e.VBR!=C.vbr_mt||(b=1),s=0;s<r.psy_lmax;s++){for(v=e.VBR==C.vbr_rh||e.VBR==C.vbr_mtrh?athAdjust(c.adjust,c.l[s],c.floor):c.adjust*c.l[s],V=r.width[s],A=v/V,D=N,B=V>>1,F=0;R=o[p]*o[p],F+=R,D+=R<A?R:A,H=o[++p]*o[p],F+=H,D+=H<A?H:A,p++,0<--B;);if(v<F&&f++,s==d.SBPSY_l&&Q(),_!=0&&(v=D),!e.ATHonly){var h=t.en.l[s];0<h&&(O=F*t.thm.l[s]*b/h,_!=0&&(O*=u.nsPsy.longfact[s]),v<O&&(v=O))}n[a++]=_!=0?v:v*u.nsPsy.longfact[s]}var g=575;if(r.block_type!=d.SHORT_TYPE)for(var k=576;k--!=0&&m1.EQ(o[k],0);)g=k;r.max_nonzero_coeff=g;for(var x=r.sfb_smin;s<r.psymax;x++,s+=3){var V,P,S;for(S=e.VBR==C.vbr_rh||e.VBR==C.vbr_mtrh?athAdjust(c.adjust,c.s[x],c.floor):c.adjust*c.s[x],V=r.width[s],P=0;P<3;P++){var v,A,D,F=0,B=V>>1;A=S/V,D=N;do{var R,H;R=o[p]*o[p],F+=R,D+=R<A?R:A,H=o[++p]*o[p],F+=H,D+=H<A?H:A,p++}while(0<--B);if(S<F&&f++,x==d.SBPSY_s&&Q(),v=_!=0?D:S,!e.ATHonly&&!e.ATHshort){var O,h=t.en.s[x][P];0<h&&(O=F*t.thm.s[x][P]*b/h,_!=0&&(O*=u.nsPsy.shortfact[x]),v<O&&(v=O))}n[a++]=_!=0?v:v*u.nsPsy.shortfact[x]}e.useTemporal&&(n[a-3]>n[a-3+1]&&(n[a-3+1]+=(n[a-3]-n[a-3+1])*u.decay),n[a-3+1]>n[a-3+2]&&(n[a-3+2]+=(n[a-3+1]-n[a-3+2])*u.decay))}return f},this.calc_noise_core=function(e,t,r,n){var s=0,a=t.s,u=e.l3_enc;if(a>e.count1)for(;r--!=0;)f=e.xr[a],a++,s+=f*f,f=e.xr[a],a++,s+=f*f;else if(a>e.big_values){var p=r1(2);for(p[0]=0,p[1]=n;r--!=0;)f=Math.abs(e.xr[a])-p[u[a]],a++,s+=f*f,f=Math.abs(e.xr[a])-p[u[a]],a++,s+=f*f}else for(;r--!=0;){var f;f=Math.abs(e.xr[a])-J[u[a]]*n,a++,s+=f*f,f=Math.abs(e.xr[a])-J[u[a]]*n,a++,s+=f*f}return t.s=a,s},this.calc_noise=function(e,t,r,n,s){var a,u,p=0,f=0,c=0,o=0,_=0,b=-20,h=0,g=e.scalefac,k=0;for(n.over_SSD=0,a=0;a<e.psymax;a++){var x,V=e.global_gain-(g[k++]+(e.preflag!=0?i[a]:0)<<e.scalefac_scale+1)-8*e.subblock_gain[e.window[a]],P=0;if(s!=null&&s.step[a]==V)P=s.noise[a],h+=e.width[a],r[p++]=P/t[f++],P=s.noise_log[a];else{var S,v=y[V+E1.Q_MAX2];u=e.width[a]>>1,h+e.width[a]>e.max_nonzero_coeff&&(S=e.max_nonzero_coeff-h+1,u=0<S?S>>1:0);var A=new T(h);P=this.calc_noise_core(e,A,u,v),h=A.s,s!=null&&(s.step[a]=V,s.noise[a]=P),P=r[p++]=P/t[f++],P=d1.FAST_LOG10(Math.max(P,1e-20)),s!=null&&(s.noise_log[a]=P)}s!=null&&(s.global_gain=e.global_gain),_+=P,0<P&&(x=Math.max(0|10*P+.5,1),n.over_SSD+=x*x,c++,o+=P),b=Math.max(b,P)}return n.over_count=c,n.tot_noise=_,n.over_noise=o,n.max_noise=b,c}}function O1(){this.xr=r1(576),this.l3_enc=o1(576),this.scalefac=o1(M1.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=o1(3),this.subblock_gain=o1(4),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=o1(M1.SFBMAX),this.window=o1(M1.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=o1(4),this.max_nonzero_coeff=0;var I=this;function W(Y){return new Int32Array(Y)}this.assign=function(Y){var N;I.xr=(N=Y.xr,new Float32Array(N)),I.l3_enc=W(Y.l3_enc),I.scalefac=W(Y.scalefac),I.xrpow_max=Y.xrpow_max,I.part2_3_length=Y.part2_3_length,I.big_values=Y.big_values,I.count1=Y.count1,I.global_gain=Y.global_gain,I.scalefac_compress=Y.scalefac_compress,I.block_type=Y.block_type,I.mixed_block_flag=Y.mixed_block_flag,I.table_select=W(Y.table_select),I.subblock_gain=W(Y.subblock_gain),I.region0_count=Y.region0_count,I.region1_count=Y.region1_count,I.preflag=Y.preflag,I.scalefac_scale=Y.scalefac_scale,I.count1table_select=Y.count1table_select,I.part2_length=Y.part2_length,I.sfb_lmax=Y.sfb_lmax,I.sfb_smin=Y.sfb_smin,I.psy_lmax=Y.psy_lmax,I.sfbmax=Y.sfbmax,I.psymax=Y.psymax,I.sfbdivide=Y.sfbdivide,I.width=W(Y.width),I.window=W(Y.window),I.count1bits=Y.count1bits,I.sfb_partition_table=Y.sfb_partition_table.slice(0),I.slen=W(Y.slen),I.max_nonzero_coeff=Y.max_nonzero_coeff}}E.t1HB=[1,1,1,0],E.t2HB=[1,2,1,3,1,1,3,2,0],E.t3HB=[3,2,1,1,1,1,3,2,0],E.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],E.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],E.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],E.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],E.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],E.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],E.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],E.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],E.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],E.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],E.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],E.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],E.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],E.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],E.t1l=[1,4,3,5],E.t2l=[1,4,7,4,5,7,6,7,8],E.t3l=[2,3,7,4,4,7,6,7,8],E.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],E.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],E.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],E.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],E.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],E.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],E.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],E.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],E.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],E.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],E.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],E.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],E.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],E.t32l=[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],E.t33l=[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8],E.ht=[new a1(0,0,null,null),new a1(2,0,E.t1HB,E.t1l),new a1(3,0,E.t2HB,E.t2l),new a1(3,0,E.t3HB,E.t3l),new a1(0,0,null,null),new a1(4,0,E.t5HB,E.t5l),new a1(4,0,E.t6HB,E.t6l),new a1(6,0,E.t7HB,E.t7l),new a1(6,0,E.t8HB,E.t8l),new a1(6,0,E.t9HB,E.t9l),new a1(8,0,E.t10HB,E.t10l),new a1(8,0,E.t11HB,E.t11l),new a1(8,0,E.t12HB,E.t12l),new a1(16,0,E.t13HB,E.t13l),new a1(0,0,null,E.t16_5l),new a1(16,0,E.t15HB,E.t15l),new a1(1,1,E.t16HB,E.t16l),new a1(2,3,E.t16HB,E.t16l),new a1(3,7,E.t16HB,E.t16l),new a1(4,15,E.t16HB,E.t16l),new a1(6,63,E.t16HB,E.t16l),new a1(8,255,E.t16HB,E.t16l),new a1(10,1023,E.t16HB,E.t16l),new a1(13,8191,E.t16HB,E.t16l),new a1(4,15,E.t24HB,E.t24l),new a1(5,31,E.t24HB,E.t24l),new a1(6,63,E.t24HB,E.t24l),new a1(7,127,E.t24HB,E.t24l),new a1(8,255,E.t24HB,E.t24l),new a1(9,511,E.t24HB,E.t24l),new a1(11,2047,E.t24HB,E.t24l),new a1(13,8191,E.t24HB,E.t24l),new a1(0,0,E.t32HB,E.t32l),new a1(0,0,E.t33HB,E.t33l)],E.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366],E.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296],E.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369],E.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]],E.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]],E.scfsi_band=[0,6,11,16,21],E1.Q_MAX=257,E1.Q_MAX2=116,E1.LARGE_BITS=1e5,E1.IXMAX_VAL=8206;var M1={};function c1(){var I,W;this.rv=null,this.qupvt=null;var Y,N=new function(){this.setModules=function(M,i){}};function l1(M){this.ordinal=M}function l(M){for(var i=0;i<M.sfbmax;i++)if(M.scalefac[i]+M.subblock_gain[M.window[i]]==0)return!1;return!0}function w(M,i,y,L,J){var j;switch(M){default:case 9:0<i.over_count?(j=y.over_SSD<=i.over_SSD,y.over_SSD==i.over_SSD&&(j=y.bits<i.bits)):j=y.max_noise<0&&10*y.max_noise+y.bits<=10*i.max_noise+i.bits;break;case 0:j=y.over_count<i.over_count||y.over_count==i.over_count&&y.over_noise<i.over_noise||y.over_count==i.over_count&&m1.EQ(y.over_noise,i.over_noise)&&y.tot_noise<i.tot_noise;break;case 8:Q();case 1:j=y.max_noise<i.max_noise;break;case 2:j=y.tot_noise<i.tot_noise;break;case 3:j=y.tot_noise<i.tot_noise&&y.max_noise<i.max_noise;break;case 4:j=y.max_noise<=0&&.2<i.max_noise||y.max_noise<=0&&i.max_noise<0&&i.max_noise>y.max_noise-.2&&y.tot_noise<i.tot_noise||y.max_noise<=0&&0<i.max_noise&&i.max_noise>y.max_noise-.2&&y.tot_noise<i.tot_noise+i.over_noise||0<y.max_noise&&-.05<i.max_noise&&i.max_noise>y.max_noise-.1&&y.tot_noise+y.over_noise<i.tot_noise+i.over_noise||0<y.max_noise&&-.1<i.max_noise&&i.max_noise>y.max_noise-.15&&y.tot_noise+y.over_noise+y.over_noise<i.tot_noise+i.over_noise+i.over_noise;break;case 5:j=y.over_noise<i.over_noise||m1.EQ(y.over_noise,i.over_noise)&&y.tot_noise<i.tot_noise;break;case 6:j=y.over_noise<i.over_noise||m1.EQ(y.over_noise,i.over_noise)&&(y.max_noise<i.max_noise||m1.EQ(y.max_noise,i.max_noise)&&y.tot_noise<=i.tot_noise);break;case 7:j=y.over_count<i.over_count||y.over_noise<i.over_noise}return i.over_count==0&&(j=j&&y.bits<i.bits),j}function m(M,i,y,L,J){var j=M.internal_flags;(function(T,e,t,r,n){var s,a=T.internal_flags;s=e.scalefac_scale==0?1.2968395546510096:1.6817928305074292;for(var u=0,p=0;p<e.sfbmax;p++)u<t[p]&&(u=t[p]);var f=a.noise_shaping_amp;switch(f==3&&Q(),f){case 2:break;case 1:1<u?u=Math.pow(u,.5):u*=.95;break;case 0:default:1<u?u=1:u*=.95}for(var c=0,p=0;p<e.sfbmax;p++){var o,_=e.width[p];if(c+=_,!(t[p]<u)){for(2&a.substep_shaping&&Q(),e.scalefac[p]++,o=-_;o<0;o++)r[c+o]*=s,r[c+o]>e.xrpow_max&&(e.xrpow_max=r[c+o]);if(a.noise_shaping_amp==2)return}}})(M,i,y,L);var X=l(i);return!(X||(X=j.mode_gr==2?Y.scale_bitcount(i):Y.scale_bitcount_lsf(j,i))&&(1<j.noise_shaping&&(x1.fill(j.pseudohalf,0),i.scalefac_scale==0?(function(T,e){for(var t=0,r=0;r<T.sfbmax;r++){var n=T.width[r],s=T.scalefac[r];if(T.preflag!=0&&(s+=W.pretab[r]),t+=n,(1&s)!=0){s++;for(var a=-n;a<0;a++)e[t+a]*=1.2968395546510096,e[t+a]>T.xrpow_max&&(T.xrpow_max=e[t+a])}T.scalefac[r]=s>>1}T.preflag=0,T.scalefac_scale=1}(i,L),X=!1):i.block_type==d.SHORT_TYPE&&0<j.subblock_gain&&(X=function(T,e,t){var r,n=e.scalefac;for(r=0;r<e.sfb_lmax;r++)if(16<=n[r])return!0;for(var s=0;s<3;s++){var a=0,u=0;for(r=e.sfb_lmax+s;r<e.sfbdivide;r+=3)a<n[r]&&(a=n[r]);for(;r<e.sfbmax;r+=3)u<n[r]&&(u=n[r]);if(!(a<16&&u<8)){if(7<=e.subblock_gain[s])return!0;e.subblock_gain[s]++;var p=T.scalefac_band.l[e.sfb_lmax];for(r=e.sfb_lmax+s;r<e.sfbmax;r+=3){var f=e.width[r],c=n[r];if(0<=(c-=4>>e.scalefac_scale))n[r]=c,p+=3*f;else{n[r]=0;var o=210+(c<<e.scalefac_scale+1);b=W.IPOW20(o),p+=f*(s+1);for(var _=-f;_<0;_++)t[p+_]*=b,t[p+_]>e.xrpow_max&&(e.xrpow_max=t[p+_]);p+=f*(3-s-1)}}var b=W.IPOW20(202);p+=e.width[r]*(s+1);for(var _=-e.width[r];_<0;_++)t[p+_]*=b,t[p+_]>e.xrpow_max&&(e.xrpow_max=t[p+_])}}return!1}(j,i,L)||l(i))),X||(X=j.mode_gr==2?Y.scale_bitcount(i):Y.scale_bitcount_lsf(j,i)),X))}this.setModules=function(M,i,y,L){I=i,this.rv=i,W=y,this.qupvt=y,Y=L,N.setModules(W,Y)},this.init_xrpow=function(M,i,y){var L=0,J=0|i.max_nonzero_coeff;if(i.xrpow_max=0,x1.fill(y,J,576,0),1e-20<(L=function(T,e,t,r){for(var n=r=0;n<=t;++n){var s=Math.abs(T.xr[n]);r+=s,e[n]=Math.sqrt(s*Math.sqrt(s)),e[n]>T.xrpow_max&&(T.xrpow_max=e[n])}return r}(i,y,J,L))){var j=0;2&M.substep_shaping&&(j=1);for(var X=0;X<i.psymax;X++)M.pseudohalf[X]=j;return!0}return x1.fill(i.l3_enc,0,576,0),!1},this.init_outer_loop=function(M,i){i.part2_3_length=0,i.big_values=0,i.count1=0,i.global_gain=210,i.scalefac_compress=0,i.table_select[0]=0,i.table_select[1]=0,i.table_select[2]=0,i.subblock_gain[0]=0,i.subblock_gain[1]=0,i.subblock_gain[2]=0,i.subblock_gain[3]=0,i.region0_count=0,i.region1_count=0,i.preflag=0,i.scalefac_scale=0,i.count1table_select=0,i.part2_length=0,i.sfb_lmax=d.SBPSY_l,i.sfb_smin=d.SBPSY_s,i.psy_lmax=M.sfb21_extra?d.SBMAX_l:d.SBPSY_l,i.psymax=i.psy_lmax,i.sfbmax=i.sfb_lmax,i.sfbdivide=11;for(var y=0;y<d.SBMAX_l;y++)i.width[y]=M.scalefac_band.l[y+1]-M.scalefac_band.l[y],i.window[y]=3;if(i.block_type==d.SHORT_TYPE){var L=r1(576);i.sfb_smin=0,(i.sfb_lmax=0)!=i.mixed_block_flag&&Q(),i.psymax=i.sfb_lmax+3*((M.sfb21_extra?d.SBMAX_s:d.SBPSY_s)-i.sfb_smin),i.sfbmax=i.sfb_lmax+3*(d.SBPSY_s-i.sfb_smin),i.sfbdivide=i.sfbmax-18,i.psy_lmax=i.sfb_lmax;var J=M.scalefac_band.l[i.sfb_lmax];v1.arraycopy(i.xr,0,L,0,576);for(var y=i.sfb_smin;y<d.SBMAX_s;y++)for(var j=M.scalefac_band.s[y],X=M.scalefac_band.s[y+1],T=0;T<3;T++)for(var e=j;e<X;e++)i.xr[J++]=L[3*e+T];for(var t=i.sfb_lmax,y=i.sfb_smin;y<d.SBMAX_s;y++)i.width[t]=i.width[t+1]=i.width[t+2]=M.scalefac_band.s[y+1]-M.scalefac_band.s[y],i.window[t]=0,i.window[t+1]=1,i.window[t+2]=2,t+=3}i.count1bits=0,i.sfb_partition_table=W.nr_of_sfb_block[0][0],i.slen[0]=0,i.slen[1]=0,i.slen[2]=0,i.slen[3]=0,i.max_nonzero_coeff=575,x1.fill(i.scalefac,0),function(r,n){var s=r.ATH,a=n.xr;if(n.block_type!=d.SHORT_TYPE)for(var u=!1,p=d.PSFB21-1;0<=p&&!u;p--){var f=r.scalefac_band.psfb21[p],c=r.scalefac_band.psfb21[p+1],o=W.athAdjust(s.adjust,s.psfb21[p],s.floor);1e-12<r.nsPsy.longfact[21]&&(o*=r.nsPsy.longfact[21]);for(var _=c-1;f<=_;_--){if(!(Math.abs(a[_])<o)){u=!0;break}a[_]=0}}else for(var b=0;b<3;b++)for(var u=!1,p=d.PSFB12-1;0<=p&&!u;p--){var f=3*r.scalefac_band.s[12]+(r.scalefac_band.s[13]-r.scalefac_band.s[12])*b+(r.scalefac_band.psfb12[p]-r.scalefac_band.psfb12[0]),c=f+(r.scalefac_band.psfb12[p+1]-r.scalefac_band.psfb12[p]),h=W.athAdjust(s.adjust,s.psfb12[p],s.floor);1e-12<r.nsPsy.shortfact[12]&&(h*=r.nsPsy.shortfact[12]);for(var _=c-1;f<=_;_--){if(!(Math.abs(a[_])<h)){u=!0;break}a[_]=0}}}(M,i)},l1.BINSEARCH_NONE=new l1(0),l1.BINSEARCH_UP=new l1(1),l1.BINSEARCH_DOWN=new l1(2),this.outer_loop=function(M,i,y,L,J,j){var X=M.internal_flags,T=new O1,e=r1(576),t=r1(M1.SFBMAX),r=new V1,n=new function(){this.global_gain=0,this.sfb_count1=0,this.step=o1(39),this.noise=r1(39),this.noise_log=r1(39)},s=9999999,a=!1;if(function(_,b,h,g,k){var x,V=_.CurrentStep[g],P=!1,S=_.OldValue[g],v=l1.BINSEARCH_NONE;for(b.global_gain=S,h-=b.part2_length;;){var A;if(x=Y.count_bits(_,k,b,null),V==1||x==h)break;h<x?(v==l1.BINSEARCH_DOWN&&(P=!0),P&&(V/=2),v=l1.BINSEARCH_UP,A=V):(v==l1.BINSEARCH_UP&&(P=!0),P&&(V/=2),v=l1.BINSEARCH_DOWN,A=-V),b.global_gain+=A,b.global_gain<0&&Q(),255<b.global_gain&&Q()}for(;h<x&&b.global_gain<255;)b.global_gain++,x=Y.count_bits(_,k,b,null);_.CurrentStep[g]=4<=S-b.global_gain?4:2,_.OldValue[g]=b.global_gain,b.part2_3_length=x}(X,i,j,J,L),X.noise_shaping==0)return 100;W.calc_noise(i,y,t,r,n),r.bits=i.part2_3_length,T.assign(i);var u=0;for(v1.arraycopy(L,0,e,0,576);!a;){do{var p,f=new V1,c=255;if(p=2&X.substep_shaping?20:3,X.sfb21_extra&&Q(),!m(M,T,t,L))break;T.scalefac_scale!=0&&(c=254);var o=j-T.part2_length;if(o<=0)break;for(;(T.part2_3_length=Y.count_bits(X,L,T,n))>o&&T.global_gain<=c;)T.global_gain++;if(T.global_gain>c)break;if(r.over_count==0){for(;(T.part2_3_length=Y.count_bits(X,L,T,n))>s&&T.global_gain<=c;)T.global_gain++;if(T.global_gain>c)break}if(W.calc_noise(T,y,t,f,n),f.bits=T.part2_3_length,(w(i.block_type!=d.SHORT_TYPE?M.quant_comp:M.quant_comp_short,r,f)?1:0)!=0)s=i.part2_3_length,r=f,i.assign(T),u=0,v1.arraycopy(L,0,e,0,576);else if(X.full_outer_loop==0){if(++u>p&&r.over_count==0)break;X.noise_shaping_amp,X.noise_shaping_amp}}while(T.global_gain+T.scalefac_scale<255);X.noise_shaping_amp==3?Q():a=!0}return M.VBR==C.vbr_rh||M.VBR==C.vbr_mtrh?v1.arraycopy(e,0,L,0,576):1&X.substep_shaping&&Q(),r.over_count},this.iteration_finish_one=function(M,i,y){var L=M.l3_side,J=L.tt[i][y];Y.best_scalefac_store(M,i,y,L),M.use_best_huffman==1&&Y.best_huffman_divide(M,J),I.ResvAdjust(M,J)}}function L1(){this.thm=new p1,this.en=new p1}function d(){var I=d.MPG_MD_MS_LR,W=null,Y=this.psy=null,N=null;this.setModules=function(l,w,m,M){W=l,this.psy=w,Y=w,N=M};var l1=new function(){var l=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*d1.SQRT2*.5/2384e-9,.017876148*d1.SQRT2*.5/2384e-9,.003134727*d1.SQRT2*.5/2384e-9,.002457142*d1.SQRT2*.5/2384e-9,971317e-9*d1.SQRT2*.5/2384e-9,218868e-9*d1.SQRT2*.5/2384e-9,101566e-9*d1.SQRT2*.5/2384e-9,13828e-9*d1.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,-29.20218120805369],w=[[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,940084909404969e-27,6423305872147839e-28,2382191739347918e-28,5456116108943412e-27,4878985199565852e-27,4240448995017367e-27,3559909094758252e-27,2858043359288075e-27,2156177623817898e-27,1475637723558783e-27,8371015190102974e-28,2599706096327376e-28,-5456116108943412e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758252e-27,-2858043359288076e-27,-2156177623817898e-27,-1475637723558783e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347923e-28,-6423305872147843e-28,-9400849094049696e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049694e-28,-642330587214784e-27,-2382191739347918e-28],[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,9400849094049688e-28,6423305872147841e-28,2382191739347918e-28,5456116108943413e-27,4878985199565852e-27,4240448995017367e-27,3559909094758253e-27,2858043359288075e-27,2156177623817898e-27,1475637723558782e-27,8371015190102975e-28,2599706096327376e-28,-5461314069809755e-27,-4921085770524055e-27,-4343405037091838e-27,-3732668368707687e-27,-3093523840190885e-27,-2430835727329465e-27,-1734679010007751e-27,-974825365660928e-27,-2797435120168326e-28,0,0,0,0,0,0,-2283748241799531e-28,-4037858874020686e-28,-2146547464825323e-28],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2283748241799531e-28,4037858874020686e-28,2146547464825323e-28,5461314069809755e-27,4921085770524055e-27,4343405037091838e-27,3732668368707687e-27,3093523840190885e-27,2430835727329466e-27,1734679010007751e-27,974825365660928e-27,2797435120168326e-28,-5456116108943413e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758253e-27,-2858043359288075e-27,-2156177623817898e-27,-1475637723558782e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347913e-28,-6423305872147834e-28,-9400849094049688e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049688e-28,-6423305872147841e-28,-2382191739347918e-28]],m=w[d.SHORT_TYPE],M=w[d.SHORT_TYPE],i=w[d.SHORT_TYPE],y=w[d.SHORT_TYPE],L=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function J(T,e,t){for(var r,n,s,a=10,u=e+238-14-286,p=-15;p<0;p++){var f,c,o;f=l[a+-10],c=T[u+-224]*f,o=T[e+224]*f,f=l[a+-9],c+=T[u+-160]*f,o+=T[e+160]*f,f=l[a+-8],c+=T[u+-96]*f,o+=T[e+96]*f,f=l[a+-7],c+=T[u+-32]*f,o+=T[e+32]*f,f=l[a+-6],c+=T[u+32]*f,o+=T[e+-32]*f,f=l[a+-5],c+=T[u+96]*f,o+=T[e+-96]*f,f=l[a+-4],c+=T[u+160]*f,o+=T[e+-160]*f,f=l[a+-3],c+=T[u+224]*f,o+=T[e+-224]*f,f=l[a+-2],c+=T[e+-256]*f,o-=T[u+256]*f,f=l[a+-1],c+=T[e+-192]*f,o-=T[u+192]*f,f=l[a+0],c+=T[e+-128]*f,o-=T[u+128]*f,f=l[a+1],c+=T[e+-64]*f,o-=T[u+64]*f,f=l[a+2],c+=T[e+0]*f,o-=T[u+0]*f,f=l[a+3],c+=T[e+64]*f,o-=T[u+-64]*f,f=l[a+4],c+=T[e+128]*f,o-=T[u+-128]*f,f=l[a+5],c+=T[e+192]*f,o-=T[u+-192]*f,c*=l[a+6],f=o-c,t[30+2*p]=o+c,t[31+2*p]=l[a+7]*f,a+=18,e--,u++}o=T[e+-16]*l[a+-10],c=T[e+-32]*l[a+-2],o+=(T[e+-48]-T[e+16])*l[a+-9],c+=T[e+-96]*l[a+-1],o+=(T[e+-80]+T[e+48])*l[a+-8],c+=T[e+-160]*l[a+0],o+=(T[e+-112]-T[e+80])*l[a+-7],c+=T[e+-224]*l[a+1],o+=(T[e+-144]+T[e+112])*l[a+-6],c-=T[e+32]*l[a+2],o+=(T[e+-176]-T[e+144])*l[a+-5],c-=T[e+96]*l[a+3],o+=(T[e+-208]+T[e+176])*l[a+-4],c-=T[e+160]*l[a+4],o+=(T[e+-240]-T[e+208])*l[a+-3],c-=T[e+224],r=c-o,n=c+o,o=t[14],c=t[15]-o,t[31]=n+o,t[30]=r+c,t[15]=r-c,t[14]=n-o,s=t[28]-t[0],t[0]+=t[28],t[28]=s*l[a+-36+7],s=t[29]-t[1],t[1]+=t[29],t[29]=s*l[a+-36+7],s=t[26]-t[2],t[2]+=t[26],t[26]=s*l[a+-72+7],s=t[27]-t[3],t[3]+=t[27],t[27]=s*l[a+-72+7],s=t[24]-t[4],t[4]+=t[24],t[24]=s*l[a+-108+7],s=t[25]-t[5],t[5]+=t[25],t[25]=s*l[a+-108+7],s=t[22]-t[6],t[6]+=t[22],t[22]=s*d1.SQRT2,s=t[23]-t[7],t[7]+=t[23],t[23]=s*d1.SQRT2-t[7],t[7]-=t[6],t[22]-=t[7],t[23]-=t[22],s=t[6],t[6]=t[31]-s,t[31]=t[31]+s,s=t[7],t[7]=t[30]-s,t[30]=t[30]+s,s=t[22],t[22]=t[15]-s,t[15]=t[15]+s,s=t[23],t[23]=t[14]-s,t[14]=t[14]+s,s=t[20]-t[8],t[8]+=t[20],t[20]=s*l[a+-180+7],s=t[21]-t[9],t[9]+=t[21],t[21]=s*l[a+-180+7],s=t[18]-t[10],t[10]+=t[18],t[18]=s*l[a+-216+7],s=t[19]-t[11],t[11]+=t[19],t[19]=s*l[a+-216+7],s=t[16]-t[12],t[12]+=t[16],t[16]=s*l[a+-252+7],s=t[17]-t[13],t[13]+=t[17],t[17]=s*l[a+-252+7],s=-t[20]+t[24],t[20]+=t[24],t[24]=s*l[a+-216+7],s=-t[21]+t[25],t[21]+=t[25],t[25]=s*l[a+-216+7],s=t[4]-t[8],t[4]+=t[8],t[8]=s*l[a+-216+7],s=t[5]-t[9],t[5]+=t[9],t[9]=s*l[a+-216+7],s=t[0]-t[12],t[0]+=t[12],t[12]=s*l[a+-72+7],s=t[1]-t[13],t[1]+=t[13],t[13]=s*l[a+-72+7],s=t[16]-t[28],t[16]+=t[28],t[28]=s*l[a+-72+7],s=-t[17]+t[29],t[17]+=t[29],t[29]=s*l[a+-72+7],s=d1.SQRT2*(t[2]-t[10]),t[2]+=t[10],t[10]=s,s=d1.SQRT2*(t[3]-t[11]),t[3]+=t[11],t[11]=s,s=d1.SQRT2*(-t[18]+t[26]),t[18]+=t[26],t[26]=s-t[18],s=d1.SQRT2*(-t[19]+t[27]),t[19]+=t[27],t[27]=s-t[19],s=t[2],t[19]-=t[3],t[3]-=s,t[2]=t[31]-s,t[31]+=s,s=t[3],t[11]-=t[19],t[18]-=s,t[3]=t[30]-s,t[30]+=s,s=t[18],t[27]-=t[11],t[19]-=s,t[18]=t[15]-s,t[15]+=s,s=t[19],t[10]-=s,t[19]=t[14]-s,t[14]+=s,s=t[10],t[11]-=s,t[10]=t[23]-s,t[23]+=s,s=t[11],t[26]-=s,t[11]=t[22]-s,t[22]+=s,s=t[26],t[27]-=s,t[26]=t[7]-s,t[7]+=s,s=t[27],t[27]=t[6]-s,t[6]+=s,s=d1.SQRT2*(t[0]-t[4]),t[0]+=t[4],t[4]=s,s=d1.SQRT2*(t[1]-t[5]),t[1]+=t[5],t[5]=s,s=d1.SQRT2*(t[16]-t[20]),t[16]+=t[20],t[20]=s,s=d1.SQRT2*(t[17]-t[21]),t[17]+=t[21],t[21]=s,s=-d1.SQRT2*(t[8]-t[12]),t[8]+=t[12],t[12]=s-t[8],s=-d1.SQRT2*(t[9]-t[13]),t[9]+=t[13],t[13]=s-t[9],s=-d1.SQRT2*(t[25]-t[29]),t[25]+=t[29],t[29]=s-t[25],s=-d1.SQRT2*(t[24]+t[28]),t[24]-=t[28],t[28]=s-t[24],s=t[24]-t[16],t[24]=s,s=t[20]-s,t[20]=s,s=t[28]-s,t[28]=s,s=t[25]-t[17],t[25]=s,s=t[21]-s,t[21]=s,s=t[29]-s,t[29]=s,s=t[17]-t[1],t[17]=s,s=t[9]-s,t[9]=s,s=t[25]-s,t[25]=s,s=t[5]-s,t[5]=s,s=t[21]-s,t[21]=s,s=t[13]-s,t[13]=s,s=t[29]-s,t[29]=s,s=t[1]-t[0],t[1]=s,s=t[16]-s,t[16]=s,s=t[17]-s,t[17]=s,s=t[8]-s,t[8]=s,s=t[9]-s,t[9]=s,s=t[24]-s,t[24]=s,s=t[25]-s,t[25]=s,s=t[4]-s,t[4]=s,s=t[5]-s,t[5]=s,s=t[20]-s,t[20]=s,s=t[21]-s,t[21]=s,s=t[12]-s,t[12]=s,s=t[13]-s,t[13]=s,s=t[28]-s,t[28]=s,s=t[29]-s,t[29]=s,s=t[0],t[0]+=t[31],t[31]-=s,s=t[1],t[1]+=t[30],t[30]-=s,s=t[16],t[16]+=t[15],t[15]-=s,s=t[17],t[17]+=t[14],t[14]-=s,s=t[8],t[8]+=t[23],t[23]-=s,s=t[9],t[9]+=t[22],t[22]-=s,s=t[24],t[24]+=t[7],t[7]-=s,s=t[25],t[25]+=t[6],t[6]-=s,s=t[4],t[4]+=t[27],t[27]-=s,s=t[5],t[5]+=t[26],t[26]-=s,s=t[20],t[20]+=t[11],t[11]-=s,s=t[21],t[21]+=t[10],t[10]-=s,s=t[12],t[12]+=t[19],t[19]-=s,s=t[13],t[13]+=t[18],t[18]-=s,s=t[28],t[28]+=t[3],t[3]-=s,s=t[29],t[29]+=t[2],t[2]-=s}function j(T,e){for(var t=0;t<3;t++){var r,n,s,a,u,p;a=T[e+6]*w[d.SHORT_TYPE][0]-T[e+15],r=T[e+0]*w[d.SHORT_TYPE][2]-T[e+9],n=a+r,s=a-r,a=T[e+15]*w[d.SHORT_TYPE][0]+T[e+6],r=T[e+9]*w[d.SHORT_TYPE][2]+T[e+0],u=a+r,p=-a+r,r=2069978111953089e-26*(T[e+3]*w[d.SHORT_TYPE][1]-T[e+12]),a=2069978111953089e-26*(T[e+12]*w[d.SHORT_TYPE][1]+T[e+3]),T[e+0]=190752519173728e-25*n+r,T[e+15]=190752519173728e-25*-u+a,s=.8660254037844387*s*1907525191737281e-26,u=.5*u*1907525191737281e-26+a,T[e+3]=s-u,T[e+6]=s+u,n=.5*n*1907525191737281e-26-r,p=.8660254037844387*p*1907525191737281e-26,T[e+9]=n+p,T[e+12]=n-p,e++}}function X(T,e,t){var r,n,s,a,u,p,f,c,o,_,b,h,g,k,x,V,P,S;s=t[17]-t[9],u=t[15]-t[11],p=t[14]-t[12],f=t[0]+t[8],c=t[1]+t[7],o=t[2]+t[6],_=t[3]+t[5],T[e+17]=f+o-_-(c-t[4]),n=(f+o-_)*M[19]+(c-t[4]),r=(s-u-p)*M[18],T[e+5]=r+n,T[e+6]=r-n,a=(t[16]-t[10])*M[18],c=c*M[19]+t[4],r=s*M[12]+a+u*M[13]+p*M[14],n=-f*M[16]+c-o*M[17]+_*M[15],T[e+1]=r+n,T[e+2]=r-n,r=s*M[13]-a-u*M[14]+p*M[12],n=-f*M[17]+c-o*M[15]+_*M[16],T[e+9]=r+n,T[e+10]=r-n,r=s*M[14]-a+u*M[12]-p*M[13],n=f*M[15]-c+o*M[16]-_*M[17],T[e+13]=r+n,T[e+14]=r-n,b=t[8]-t[0],g=t[6]-t[2],k=t[5]-t[3],x=t[17]+t[9],V=t[16]+t[10],P=t[15]+t[11],S=t[14]+t[12],T[e+0]=x+P+S+(V+t[13]),r=(x+P+S)*M[19]-(V+t[13]),n=(b-g+k)*M[18],T[e+11]=r+n,T[e+12]=r-n,h=(t[7]-t[1])*M[18],V=t[13]-V*M[19],r=x*M[15]-V+P*M[16]+S*M[17],n=b*M[14]+h+g*M[12]+k*M[13],T[e+3]=r+n,T[e+4]=r-n,r=-x*M[17]+V-P*M[15]-S*M[16],n=b*M[13]+h-g*M[14]-k*M[12],T[e+7]=r+n,T[e+8]=r-n,r=-x*M[16]+V-P*M[17]-S*M[15],n=b*M[12]-h+g*M[13]-k*M[14],T[e+15]=r+n,T[e+16]=r-n}this.mdct_sub48=function(T,e,t){for(var r=e,n=286,s=0;s<T.channels_out;s++){for(var a=0;a<T.mode_gr;a++){for(var u,p=T.l3_side.tt[a][s],f=p.xr,c=0,o=T.sb_sample[s][1-a],_=0,b=0;b<9;b++)for(J(r,n,o[_]),J(r,n+32,o[_+1]),_+=2,n+=64,u=1;u<32;u+=2)o[_-1][u]*=-1;for(u=0;u<32;u++,c+=18){var h=p.block_type,g=T.sb_sample[s][a],k=T.sb_sample[s][1-a];if(p.mixed_block_flag!=0&&u<2&&(h=0),T.amp_filter[u]<1e-12)x1.fill(f,c+0,c+18,0);else if(T.amp_filter[u]<1&&Q(),h==d.SHORT_TYPE){for(var b=-3;b<0;b++){var x=w[d.SHORT_TYPE][b+3];f[c+3*b+9]=g[9+b][L[u]]*x-g[8-b][L[u]],f[c+3*b+18]=g[14-b][L[u]]*x+g[15+b][L[u]],f[c+3*b+10]=g[15+b][L[u]]*x-g[14-b][L[u]],f[c+3*b+19]=k[2-b][L[u]]*x+k[3+b][L[u]],f[c+3*b+11]=k[3+b][L[u]]*x-k[2-b][L[u]],f[c+3*b+20]=k[8-b][L[u]]*x+k[9+b][L[u]]}j(f,c)}else{for(var V=r1(18),b=-9;b<0;b++){var P,S;P=w[h][b+27]*k[b+9][L[u]]+w[h][b+36]*k[8-b][L[u]],S=w[h][b+9]*g[b+9][L[u]]-w[h][b+18]*g[8-b][L[u]],V[b+9]=P-S*m[3+b+9],V[b+18]=P*m[3+b+9]+S}X(f,c,V)}if(h!=d.SHORT_TYPE&&u!=0)for(var b=7;0<=b;--b){var v,A;v=f[c+b]*i[20+b]+f[c+-1-b]*y[28+b],A=f[c+b]*y[28+b]-f[c+-1-b]*i[20+b],f[c+-1-b]=v,f[c+b]=A}}}if(r=t,n=286,T.mode_gr==1)for(var D=0;D<18;D++)v1.arraycopy(T.sb_sample[s][1][D],0,T.sb_sample[s][0][D],0,32)}}};this.lame_encode_mp3_frame=function(l,w,m,M,i,y){var L,J=C1([2,2]);J[0][0]=new L1,J[0][1]=new L1,J[1][0]=new L1,J[1][1]=new L1;var j,X=C1([2,2]);X[0][0]=new L1,X[0][1]=new L1,X[1][0]=new L1,X[1][1]=new L1;var T,e,t,r=[null,null],n=l.internal_flags,s=y1([2,4]),a=[[0,0],[0,0]],u=[[0,0],[0,0]];if(r[0]=w,r[1]=m,n.lame_encode_frame_init==0&&function(g,k){var x,V,P=g.internal_flags;if(P.lame_encode_frame_init==0){var S,v,A=r1(2014),D=r1(2014);for(P.lame_encode_frame_init=1,v=S=0;S<286+576*(1+P.mode_gr);++S)S<576*P.mode_gr?(A[S]=0,P.channels_out==2&&(D[S]=0)):(A[S]=k[0][v],P.channels_out==2&&(D[S]=k[1][v]),++v);for(V=0;V<P.mode_gr;V++)for(x=0;x<P.channels_out;x++)P.l3_side.tt[V][x].block_type=d.SHORT_TYPE;l1.mdct_sub48(P,A,D)}}(l,r),n.padding=0,(n.slot_lag-=n.frac_SpF)<0&&(n.slot_lag+=l.out_samplerate,n.padding=1),n.psymodel!=0){var p,f=[null,null],c=0,o=o1(2);for(t=0;t<n.mode_gr;t++){for(e=0;e<n.channels_out;e++)f[e]=r[e],c=576+576*t-d.FFTOFFSET;if(l.VBR==C.vbr_mtrh||l.VBR==C.vbr_mt?Q():p=Y.L3psycho_anal_ns(l,f,c,t,J,X,a[t],u[t],s[t],o),p!=0)return-4;for(l.mode==t1.JOINT_STEREO&&Q(),e=0;e<n.channels_out;e++){var _=n.l3_side.tt[t][e];_.block_type=o[e],_.mixed_block_flag=0}}}else Q();if(function(g){var k,x;if(g.ATH.useAdjust==0)return g.ATH.adjust=1;if(x=g.loudness_sq[0][0],k=g.loudness_sq[1][0],g.channels_out==2?Q():(x+=x,k+=k),g.mode_gr==2&&(x=Math.max(x,k)),x*=.5,.03125<(x*=g.ATH.aaSensitivityP))1<=g.ATH.adjust?g.ATH.adjust=1:g.ATH.adjust<g.ATH.adjustLimit&&(g.ATH.adjust=g.ATH.adjustLimit),g.ATH.adjustLimit=1;else{var V=31.98*x+625e-6;g.ATH.adjust>=V?(g.ATH.adjust*=.075*V+.925,g.ATH.adjust<V&&(g.ATH.adjust=V)):g.ATH.adjustLimit>=V?g.ATH.adjust=V:g.ATH.adjust<g.ATH.adjustLimit&&(g.ATH.adjust=g.ATH.adjustLimit),g.ATH.adjustLimit=V}}(n),l1.mdct_sub48(n,r[0],r[1]),n.mode_ext=d.MPG_MD_LR_LR,l.force_ms?n.mode_ext=d.MPG_MD_MS_LR:l.mode==t1.JOINT_STEREO&&Q(),n.mode_ext==I?(j=X,T=u):(j=J,T=a),l.analysis&&n.pinfo!=null&&Q(),l.VBR==C.vbr_off||l.VBR==C.vbr_abr){var b,h;for(b=0;b<18;b++)n.nsPsy.pefirbuf[b]=n.nsPsy.pefirbuf[b+1];for(t=h=0;t<n.mode_gr;t++)for(e=0;e<n.channels_out;e++)h+=T[t][e];for(n.nsPsy.pefirbuf[18]=h,h=n.nsPsy.pefirbuf[9],b=0;b<9;b++)h+=(n.nsPsy.pefirbuf[b]+n.nsPsy.pefirbuf[18-b])*d.fircoef[b];for(h=3350*n.mode_gr*n.channels_out/h,t=0;t<n.mode_gr;t++)for(e=0;e<n.channels_out;e++)T[t][e]*=h}return n.iteration_loop.iteration_loop(l,T,[.5,.5],j),W.format_bitstream(l),L=W.copy_buffer(n,M,i,y,1),l.bWriteVbrTag&&N.addVbrFrame(l),l.analysis&&n.pinfo!=null&&Q(),function(g){var k,x;for(g.bitrate_stereoMode_Hist[g.bitrate_index][4]++,g.bitrate_stereoMode_Hist[15][4]++,g.channels_out==2&&Q(),k=0;k<g.mode_gr;++k)for(x=0;x<g.channels_out;++x){var V=0|g.l3_side.tt[k][x].block_type;g.l3_side.tt[k][x].mixed_block_flag!=0&&(V=4),g.bitrate_blockType_Hist[g.bitrate_index][V]++,g.bitrate_blockType_Hist[g.bitrate_index][5]++,g.bitrate_blockType_Hist[15][V]++,g.bitrate_blockType_Hist[15][5]++}}(n),L}}function p1(){this.l=r1(d.SBMAX_l),this.s=y1([d.SBMAX_s,3]);var I=this;this.assign=function(W){v1.arraycopy(W.l,0,I.l,0,d.SBMAX_l);for(var Y=0;Y<d.SBMAX_s;Y++)for(var N=0;N<3;N++)I.s[Y][N]=W.s[Y][N]}}function B1(){var I=40;function W(){this.write_timing=0,this.ptr=0,this.buf=S1(I)}this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=y1([2,B1.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new function(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[o1(4),o1(4)];for(var N=0;N<2;N++)for(var l1=0;l1<2;l1++)this.tt[N][l1]=new O1},this.ms_ratio=r1(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=o1(2),this.CurrentStep=o1(2),this.masking_lower=0,this.bv_scf=o1(576),this.pseudohalf=o1(M1.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*B1.BPC+1),this.itime=s1(2),this.sideinfo_len=0,this.sb_sample=y1([2,2,18,d.SBLIMIT]),this.amp_filter=r1(32),this.header=new Array(B1.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new T1,this.minval_l=r1(d.CBANDS),this.minval_s=r1(d.CBANDS),this.nb_1=y1([4,d.CBANDS]),this.nb_2=y1([4,d.CBANDS]),this.nb_s1=y1([4,d.CBANDS]),this.nb_s2=y1([4,d.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=r1(4),this.loudness_sq=y1([2,2]),this.loudness_sq_save=r1(2),this.mld_l=r1(d.SBMAX_l),this.mld_s=r1(d.SBMAX_s),this.bm_l=o1(d.SBMAX_l),this.bo_l=o1(d.SBMAX_l),this.bm_s=o1(d.SBMAX_s),this.bo_s=o1(d.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=R1([d.CBANDS,2]),this.s3ind_s=R1([d.CBANDS,2]),this.numlines_s=o1(d.CBANDS),this.numlines_l=o1(d.CBANDS),this.rnumlines_l=r1(d.CBANDS),this.mld_cb_l=r1(d.CBANDS),this.mld_cb_s=r1(d.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=r1(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=o1(2),this.nsPsy=new function(){this.last_en_subshort=y1([4,9]),this.lastAttacks=o1(4),this.pefirbuf=r1(19),this.longfact=r1(d.SBMAX_l),this.shortfact=r1(d.SBMAX_s),this.attackthre=0,this.attackthre_s=0},this.VBR_seek_table=new function(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0},this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=R1([16,5]),this.bitrate_blockType_Hist=R1([16,6]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var Y=0;Y<this.en.length;Y++)this.en[Y]=new p1;for(var Y=0;Y<this.thm.length;Y++)this.thm[Y]=new p1;for(var Y=0;Y<this.header.length;Y++)this.header[Y]=new W}function I1(){var I=new function(){var S=r1(d.BLKSIZE),v=r1(d.BLKSIZE_s/2),A=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function D(B,R,H){var O,z,Z,q=0,$=R+(H<<=1);O=4;do{var K,e1,g1,G,n1,b1,U;for(U=O>>1,b1=(n1=(G=O)<<1)+G,O=n1<<1,Z=(z=R)+U;G1=B[z+0]-B[z+G],J1=B[z+0]+B[z+G],Q1=B[z+n1]-B[z+b1],U1=B[z+n1]+B[z+b1],B[z+n1]=J1-U1,B[z+0]=J1+U1,B[z+b1]=G1-Q1,B[z+G]=G1+Q1,G1=B[Z+0]-B[Z+G],J1=B[Z+0]+B[Z+G],Q1=d1.SQRT2*B[Z+b1],U1=d1.SQRT2*B[Z+n1],B[Z+n1]=J1-U1,B[Z+0]=J1+U1,B[Z+b1]=G1-Q1,B[Z+G]=G1+Q1,Z+=O,(z+=O)<$;);for(e1=A[q+0],K=A[q+1],g1=1;g1<U;g1++){var P1,N1;P1=1-2*K*K,N1=2*K*e1,z=R+g1,Z=R+G-g1;do{var F1,X1,j1,J1,G1,tt,U1,st,Q1,nt;X1=N1*B[z+G]-P1*B[Z+G],F1=P1*B[z+G]+N1*B[Z+G],G1=B[z+0]-F1,J1=B[z+0]+F1,tt=B[Z+0]-X1,j1=B[Z+0]+X1,X1=N1*B[z+b1]-P1*B[Z+b1],F1=P1*B[z+b1]+N1*B[Z+b1],Q1=B[z+n1]-F1,U1=B[z+n1]+F1,nt=B[Z+n1]-X1,st=B[Z+n1]+X1,X1=K*U1-e1*nt,F1=e1*U1+K*nt,B[z+n1]=J1-F1,B[z+0]=J1+F1,B[Z+b1]=tt-X1,B[Z+G]=tt+X1,X1=e1*st-K*Q1,F1=K*st+e1*Q1,B[Z+n1]=j1-F1,B[Z+0]=j1+F1,B[z+b1]=G1-X1,B[z+G]=G1+X1,Z+=O,z+=O}while(z<$);e1=(P1=e1)*A[q+0]-K*A[q+1],K=P1*A[q+1]+K*A[q+0]}q+=2}while(O<H)}var F=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(B,R,H,O,z){for(var Z=0;Z<3;Z++){var q=d.BLKSIZE_s/2,$=65535&192*(Z+1),K=d.BLKSIZE_s/8-1;do{var e1,g1,G,n1,b1,U=255&F[K<<2];e1=v[U]*O[H][z+U+$],b1=v[127-U]*O[H][z+U+$+128],g1=e1-b1,e1+=b1,G=v[U+64]*O[H][z+U+$+64],b1=v[63-U]*O[H][z+U+$+192],n1=G-b1,G+=b1,q-=4,R[Z][q+0]=e1+G,R[Z][q+2]=e1-G,R[Z][q+1]=g1+n1,R[Z][q+3]=g1-n1,e1=v[U+1]*O[H][z+U+$+1],b1=v[126-U]*O[H][z+U+$+129],g1=e1-b1,e1+=b1,G=v[U+65]*O[H][z+U+$+65],b1=v[62-U]*O[H][z+U+$+193],n1=G-b1,G+=b1,R[Z][q+d.BLKSIZE_s/2+0]=e1+G,R[Z][q+d.BLKSIZE_s/2+2]=e1-G,R[Z][q+d.BLKSIZE_s/2+1]=g1+n1,R[Z][q+d.BLKSIZE_s/2+3]=g1-n1}while(0<=--K);D(R[Z],q,d.BLKSIZE_s/2)}},this.fft_long=function(B,R,H,O,z){var Z=d.BLKSIZE/8-1,q=d.BLKSIZE/2;do{var $,K,e1,g1,G,n1=255&F[Z];$=S[n1]*O[H][z+n1],G=S[n1+512]*O[H][z+n1+512],K=$-G,$+=G,e1=S[n1+256]*O[H][z+n1+256],G=S[n1+768]*O[H][z+n1+768],g1=e1-G,e1+=G,R[0+(q-=4)]=$+e1,R[q+2]=$-e1,R[q+1]=K+g1,R[q+3]=K-g1,$=S[n1+1]*O[H][z+n1+1],G=S[n1+513]*O[H][z+n1+513],K=$-G,$+=G,e1=S[n1+257]*O[H][z+n1+257],G=S[n1+769]*O[H][z+n1+769],g1=e1-G,e1+=G,R[q+d.BLKSIZE/2+0]=$+e1,R[q+d.BLKSIZE/2+2]=$-e1,R[q+d.BLKSIZE/2+1]=K+g1,R[q+d.BLKSIZE/2+3]=K-g1}while(0<=--Z);D(R,q,d.BLKSIZE/2)},this.init_fft=function(B){for(var R=0;R<d.BLKSIZE;R++)S[R]=.42-.5*Math.cos(2*Math.PI*(R+.5)/d.BLKSIZE)+.08*Math.cos(4*Math.PI*(R+.5)/d.BLKSIZE);for(var R=0;R<d.BLKSIZE_s/2;R++)v[R]=.5*(1-Math.cos(2*Math.PI*(R+.5)/d.BLKSIZE_s))}},W=2.302585092994046,Y=2,N=16,l1=.34,l=1/217621504/(d.BLKSIZE/2),w=.2302585093;function m(S,v,A,D,F,B,R,H,O,z,Z){var q=S.internal_flags;O<2?(I.fft_long(q,D[F],O,z,Z),I.fft_short(q,B[R],O,z,Z)):O==2&&Q(),v[0]=D[F+0][0],v[0]*=v[0];for(var $=d.BLKSIZE/2-1;0<=$;--$){var K=D[F+0][d.BLKSIZE/2-$],e1=D[F+0][d.BLKSIZE/2+$];v[d.BLKSIZE/2-$]=.5*(K*K+e1*e1)}for(var g1=2;0<=g1;--g1){A[g1][0]=B[R+0][g1][0],A[g1][0]*=A[g1][0];for(var $=d.BLKSIZE_s/2-1;0<=$;--$){var K=B[R+0][g1][d.BLKSIZE_s/2-$],e1=B[R+0][g1][d.BLKSIZE_s/2+$];A[g1][d.BLKSIZE_s/2-$]=.5*(K*K+e1*e1)}}for(var G=0,$=11;$<d.HBLKSIZE;$++)G+=v[$];q.tot_ener[O]=G,S.analysis&&Q(),S.athaa_loudapprox==2&&O<2&&(q.loudness_sq[H][O]=q.loudness_sq_save[O],q.loudness_sq_save[O]=function(n1,b1){for(var U=0,P1=0;P1<d.BLKSIZE/2;++P1)U+=n1[P1]*b1.ATH.eql_w[P1];return U*=l}(v,q))}var M,i,y,L=8,J=23,j=15,X=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749],T=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],e=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],t=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276];function r(S,v,A,D,F,B){var R;if(S<v){if(!(v<S*i))return S+v;R=v/S}else{if(v*i<=S)return S+v;R=S/v}if(S+=v,D+3<=6){if(M<=R)return S;var z=0|d1.FAST_LOG10_X(R,16);return S*e[z]}var H,O,z=0|d1.FAST_LOG10_X(R,16);return v=F.ATH.cb_l[A]*F.ATH.adjust,S<y*v?v<S?(H=1,z<=13&&(H=t[z]),O=d1.FAST_LOG10_X(S/v,10/15),S*((T[z]-H)*O+H)):13<z?S:S*t[z]:S*T[z]}function n(S,v,A,D,F){var B,R,H=0,O=0;for(B=R=0;B<d.SBMAX_s;++R,++B){for(var z=S.bo_s[B],Z=S.npart_s,q=z<Z?z:Z;R<q;)H+=v[R],O+=A[R],R++;if(S.en[D].s[B][F]=H,S.thm[D].s[B][F]=O,Z<=R){++B;break}var $=S.PSY.bo_s_weight[B],K=1-$;H=$*v[R],O=$*A[R],S.en[D].s[B][F]+=H,S.thm[D].s[B][F]+=O,H=K*v[R],O=K*A[R]}for(;B<d.SBMAX_s;++B)S.en[D].s[B][F]=0,S.thm[D].s[B][F]=0}function s(S,v,A,D){var F,B,R=0,H=0;for(F=B=0;F<d.SBMAX_l;++B,++F){for(var O=S.bo_l[F],z=S.npart_l,Z=O<z?O:z;B<Z;)R+=v[B],H+=A[B],B++;if(S.en[D].l[F]=R,S.thm[D].l[F]=H,z<=B){++F;break}var q=S.PSY.bo_l_weight[F],$=1-q;R=q*v[B],H=q*A[B],S.en[D].l[F]+=R,S.thm[D].l[F]+=H,R=$*v[B],H=$*A[B]}for(;F<d.SBMAX_l;++F)S.en[D].l[F]=0,S.thm[D].l[F]=0}function a(S,v,A,D,F,B){var R,H,O=S.internal_flags;for(H=R=0;H<O.npart_s;++H){for(var z=0,Z=0,q=O.numlines_s[H],$=0;$<q;++$,++R){var K=v[B][R];z+=K,Z<K&&(Z=K)}A[H]=z}for(R=H=0;H<O.npart_s;H++){var e1=O.s3ind_s[H][0],g1=O.s3_ss[R++]*A[e1];for(++e1;e1<=O.s3ind_s[H][1];)g1+=O.s3_ss[R]*A[e1],++R,++e1;var G=Y*O.nb_s1[F][H];if(D[H]=Math.min(g1,G),O.blocktype_old[1&F]==d.SHORT_TYPE){var G=N*O.nb_s2[F][H],n1=D[H];D[H]=Math.min(G,n1)}O.nb_s2[F][H]=O.nb_s1[F][H],O.nb_s1[F][H]=g1}for(;H<=d.CBANDS;++H)A[H]=0,D[H]=0}function u(S,v,A){return 1<=A?S:A<=0?v:0<v?Math.pow(S/v,A)*v:0}var p=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function f(S,v){for(var A=309.07,D=0;D<d.SBMAX_s-1;D++)for(var F=0;F<3;F++){var B=S.thm.s[D][F];if(0<B){var R=B*v,H=S.en.s[D][F];R<H&&(A+=1e10*R<H?p[D]*(10*W):p[D]*d1.FAST_LOG10(H/R))}}return A}var c=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function o(S,v){for(var A=281.0575,D=0;D<d.SBMAX_l-1;D++){var F=S.thm.l[D];if(0<F){var B=F*v,R=S.en.l[D];B<R&&(A+=1e10*B<R?c[D]*(10*W):c[D]*d1.FAST_LOG10(R/B))}}return A}function _(S,v,A,D,F){var B,R;for(B=R=0;B<S.npart_l;++B){var H,O=0,z=0;for(H=0;H<S.numlines_l[B];++H,++R){var Z=v[R];O+=Z,z<Z&&(z=Z)}A[B]=O,D[B]=z,F[B]=O*S.rnumlines_l[B]}}function b(S,v,A,D){var F=X.length-1,B=0,R=A[B]+A[B+1];if(0<R){var H=v[B];H<v[B+1]&&(H=v[B+1]);var O=0|(R=20*(2*H-R)/(R*(S.numlines_l[B]+S.numlines_l[B+1]-1)));F<O&&(O=F),D[B]=O}else D[B]=0;for(B=1;B<S.npart_l-1;B++)if(0<(R=A[B-1]+A[B]+A[B+1])){var H=v[B-1];H<v[B]&&(H=v[B]),H<v[B+1]&&(H=v[B+1]);var O=0|(R=20*(3*H-R)/(R*(S.numlines_l[B-1]+S.numlines_l[B]+S.numlines_l[B+1]-1)));F<O&&(O=F),D[B]=O}else D[B]=0;if(0<(R=A[B-1]+A[B])){var H=v[B-1];H<v[B]&&(H=v[B]);var O=0|(R=20*(2*H-R)/(R*(S.numlines_l[B-1]+S.numlines_l[B]-1)));F<O&&(O=F),D[B]=O}else D[B]=0}var h=[-1730326e-23,-.01703172,-1349528e-23,.0418072,-673278e-22,-.0876324,-30835e-21,.1863476,-1104424e-22,-.627638];function g(S){return S<0&&(S=0),S*=.001,13*Math.atan(.76*S)+3.5*Math.atan(S*S/56.25)}function k(S,v,A,D,F,B,R,H,O,z,Z,q){var $,K=r1(d.CBANDS+1),e1=H/(15<q?1152:384),g1=o1(d.HBLKSIZE);H/=O;var G=0,n1=0;for($=0;$<d.CBANDS;$++){var b1;for(tt=g(H*G),K[$]=H*G,b1=G;g(H*b1)-tt<l1&&b1<=O/2;b1++);for(S[$]=b1-G,n1=$+1;G<b1;)g1[G++]=$;if(O/2<G){G=O/2,++$;break}}K[$]=H*G;for(var U=0;U<q;U++){var P1,N1,F1,X1,j1;F1=z[U],X1=z[U+1],(P1=0|Math.floor(.5+Z*(F1-.5)))<0&&(P1=0),N1=0|Math.floor(.5+Z*(X1-.5)),O/2<N1&&(N1=O/2),A[U]=(g1[P1]+g1[N1])/2,v[U]=g1[N1];var J1=e1*X1;R[U]=(J1-K[v[U]])/(K[v[U]+1]-K[v[U]]),R[U]<0?R[U]=0:1<R[U]&&(R[U]=1),j1=g(H*z[U]*Z),j1=Math.min(j1,15.5)/15.5,B[U]=Math.pow(10,1.25*(1-Math.cos(Math.PI*j1))-2.5)}for(var G1=G=0;G1<n1;G1++){var tt,U1,st=S[G1];tt=g(H*G),U1=g(H*(G+st-1)),D[G1]=.5*(tt+U1),tt=g(H*(G-.5)),U1=g(H*(G+st-.5)),F[G1]=U1-tt,G+=st}return n1}function x(S,v,A,D,F,B){var R,H,O,z,Z,q,$=y1([d.CBANDS,d.CBANDS]),K=0;if(B)for(var e1=0;e1<v;e1++)for(R=0;R<v;R++){var g1=(H=A[e1]-A[R],q=Z=z=O=void 0,O=H,z=.5<=(O*=0<=O?3:1.5)&&O<=2.5?8*((q=O-.5)*q-2*q):0,((Z=15.811389+7.5*(O+=.474)-17.5*Math.sqrt(1+O*O))<=-60?0:(O=Math.exp((z+Z)*w),O/=.6609193))*D[R]);$[e1][R]=g1*F[e1]}else Q();for(var e1=0;e1<v;e1++){for(R=0;R<v&&!(0<$[e1][R]);R++);for(S[e1][0]=R,R=v-1;0<R&&!(0<$[e1][R]);R--);S[e1][1]=R,K+=S[e1][1]-S[e1][0]+1}for(var G=r1(K),n1=0,e1=0;e1<v;e1++)for(R=S[e1][0];R<=S[e1][1];R++)G[n1++]=$[e1][R];return G}function V(S){var v=g(S);return v=Math.min(v,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*v))-2.5)}function P(S,v){S<-.3&&(S=3410),S/=1e3,S=Math.max(.1,S);var A=3.64*Math.pow(S,-.8)-6.8*Math.exp(-.6*Math.pow(S-3.4,2))+6*Math.exp(-.15*Math.pow(S-8.7,2))+.001*(.6+.04*v)*Math.pow(S,4);return A}this.L3psycho_anal_ns=function(S,v,A,D,F,B,R,H,O,z){var Z,q,$,K,e1,g1,G,n1,b1,U=S.internal_flags,P1=y1([2,d.BLKSIZE]),N1=y1([2,3,d.BLKSIZE_s]),F1=r1(d.CBANDS+1),X1=r1(d.CBANDS+1),j1=r1(d.CBANDS+2),J1=o1(2),G1=o1(2),tt=y1([2,576]),U1=o1(d.CBANDS+2),st=o1(d.CBANDS+2);for(x1.fill(st,0),Z=U.channels_out,S.mode==t1.JOINT_STEREO&&(Z=4),b1=S.VBR==C.vbr_off?U.ResvMax==0?0:U.ResvSize/U.ResvMax*.5:S.VBR==C.vbr_rh||S.VBR==C.vbr_mtrh||S.VBR==C.vbr_mt?.6:1,q=0;q<U.channels_out;q++){var Q1=v[q],nt=A+576-350-21+192;for(K=0;K<576;K++){var ht,pt;for(ht=Q1[nt+K+10],e1=pt=0;e1<9;e1+=2)ht+=h[e1]*(Q1[nt+K+e1]+Q1[nt+K+21-e1]),pt+=h[e1+1]*(Q1[nt+K+e1+1]+Q1[nt+K+21-e1-1]);tt[q][K]=ht+pt}F[D][q].en.assign(U.en[q]),F[D][q].thm.assign(U.thm[q]),2<Z&&Q()}for(q=0;q<Z;q++){var St,et=r1(12),at=[0,0,0,0],bt=r1(12),Mt=1,At=r1(d.CBANDS),yt=r1(d.CBANDS),D1=[0,0,0,0],Tt=r1(d.HBLKSIZE),Bt=y1([3,d.HBLKSIZE_s]);for(K=0;K<3;K++)et[K]=U.nsPsy.last_en_subshort[q][K+6],bt[K]=et[K]/U.nsPsy.last_en_subshort[q][K+4],at[0]+=et[K];q==2&&Q();var kt=tt[1&q],_t=0;for(K=0;K<9;K++){for(var Ct=_t+64,K1=1;_t<Ct;_t++)K1<Math.abs(kt[_t])&&(K1=Math.abs(kt[_t]));U.nsPsy.last_en_subshort[q][K]=et[K+3]=K1,at[1+K/3]+=K1,K1>et[K+3-2]?K1/=et[K+3-2]:K1=et[K+3-2]>10*K1?et[K+3-2]/(10*K1):0,bt[K+3]=K1}for(S.analysis&&Q(),St=q==3?U.nsPsy.attackthre_s:U.nsPsy.attackthre,K=0;K<12;K++)D1[K/3]==0&&bt[K]>St&&(D1[K/3]=K%3+1);for(K=1;K<4;K++)(at[K-1]>at[K]?at[K-1]/at[K]:at[K]/at[K-1])<1.7&&(D1[K]=0,K==1&&(D1[0]=0));for(D1[0]!=0&&U.nsPsy.lastAttacks[q]!=0&&(D1[0]=0),U.nsPsy.lastAttacks[q]!=3&&D1[0]+D1[1]+D1[2]+D1[3]==0||((Mt=0)!=D1[1]&&D1[0]!=0&&(D1[1]=0),D1[2]!=0&&D1[1]!=0&&(D1[2]=0),D1[3]!=0&&D1[2]!=0&&(D1[3]=0)),q<2?G1[q]=Mt:Q(),O[q]=U.tot_ener[q],m(S,Tt,Bt,P1,1&q,N1,1&q,D,q,v,A),_(U,Tt,F1,At,yt),b(U,At,yt,U1),n1=0;n1<3;n1++){var mt,$1;for(a(S,Bt,X1,j1,q,n1),n(U,X1,j1,q,n1),G=0;G<d.SBMAX_s;G++){if($1=U.thm[q].s[G][n1],$1*=.8,2<=D1[n1]||D1[n1+1]==1){var ft=n1!=0?n1-1:2,K1=u(U.thm[q].s[G][ft],$1,.6*b1);$1=Math.min($1,K1)}if(D1[n1]==1){var ft=n1!=0?n1-1:2,K1=u(U.thm[q].s[G][ft],$1,.3*b1);$1=Math.min($1,K1)}else if(n1!=0&&D1[n1-1]==3||n1==0&&U.nsPsy.lastAttacks[q]==3){var ft=n1!=2?n1+1:0,K1=u(U.thm[q].s[G][ft],$1,.3*b1);$1=Math.min($1,K1)}mt=et[3*n1+3]+et[3*n1+4]+et[3*n1+5],6*et[3*n1+5]<mt&&($1*=.5,6*et[3*n1+4]<mt&&($1*=.5)),U.thm[q].s[G][n1]=$1}}for(U.nsPsy.lastAttacks[q]=D1[2],$=g1=0;$<U.npart_l;$++){for(var it=U.s3ind[$][0],vt=F1[it]*X[U1[it]],ot=U.s3_ll[g1++]*vt;++it<=U.s3ind[$][1];)vt=F1[it]*X[U1[it]],ot=r(ot,U.s3_ll[g1++]*vt,it,it-$,U);ot*=.158489319246111,U.blocktype_old[1&q]==d.SHORT_TYPE?j1[$]=ot:j1[$]=u(Math.min(ot,Math.min(2*U.nb_1[q][$],16*U.nb_2[q][$])),ot,b1),U.nb_2[q][$]=U.nb_1[q][$],U.nb_1[q][$]=ot}for(;$<=d.CBANDS;++$)F1[$]=0,j1[$]=0;s(U,F1,j1,q)}for(S.mode!=t1.STEREO&&S.mode!=t1.JOINT_STEREO||Q(),S.mode==t1.JOINT_STEREO&&Q(),function(ct,lt,It,ut){var rt=ct.internal_flags;ct.short_blocks!=_1.short_block_coupled||lt[0]!=0&&lt[1]!=0||(lt[0]=lt[1]=0);for(var W1=0;W1<rt.channels_out;W1++)ut[W1]=d.NORM_TYPE,ct.short_blocks==_1.short_block_dispensed&&(lt[W1]=1),ct.short_blocks==_1.short_block_forced&&(lt[W1]=0),lt[W1]!=0?rt.blocktype_old[W1]==d.SHORT_TYPE&&(ut[W1]=d.STOP_TYPE):(ut[W1]=d.SHORT_TYPE,rt.blocktype_old[W1]==d.NORM_TYPE&&(rt.blocktype_old[W1]=d.START_TYPE),rt.blocktype_old[W1]==d.STOP_TYPE&&(rt.blocktype_old[W1]=d.SHORT_TYPE)),It[W1]=rt.blocktype_old[W1],rt.blocktype_old[W1]=ut[W1]}(S,G1,z,J1),q=0;q<Z;q++){var dt,xt,gt,wt=0;1<q?Q():(dt=R,wt=0,xt=z[q],gt=F[D][q]),dt[wt+q]=xt==d.SHORT_TYPE?f(gt,U.masking_lower):o(gt,U.masking_lower),S.analysis&&(U.pinfo.pe[D][q]=dt[wt+q])}return 0},this.psymodel_init=function(S){var v,A=S.internal_flags,D=!0,F=13,B=0,R=0,H=-8.25,O=-4.5,z=r1(d.CBANDS),Z=r1(d.CBANDS),q=r1(d.CBANDS),$=S.out_samplerate;switch(S.experimentalZ){default:case 0:D=!0;break;case 1:D=S.VBR!=C.vbr_mtrh&&S.VBR!=C.vbr_mt;break;case 2:D=!1;break;case 3:F=8,B=-1.75,R=-.0125,H=-8.25,O=-2.25}for(A.ms_ener_ratio_old=.25,A.blocktype_old[0]=A.blocktype_old[1]=d.NORM_TYPE,v=0;v<4;++v){for(var G=0;G<d.CBANDS;++G)A.nb_1[v][G]=1e20,A.nb_2[v][G]=1e20,A.nb_s1[v][G]=A.nb_s2[v][G]=1;for(var K=0;K<d.SBMAX_l;K++)A.en[v].l[K]=1e20,A.thm[v].l[K]=1e20;for(var G=0;G<3;++G){for(var K=0;K<d.SBMAX_s;K++)A.en[v].s[K][G]=1e20,A.thm[v].s[K][G]=1e20;A.nsPsy.lastAttacks[v]=0}for(var G=0;G<9;G++)A.nsPsy.last_en_subshort[v][G]=10}for(A.loudness_sq_save[0]=A.loudness_sq_save[1]=0,A.npart_l=k(A.numlines_l,A.bo_l,A.bm_l,z,Z,A.mld_l,A.PSY.bo_l_weight,$,d.BLKSIZE,A.scalefac_band.l,d.BLKSIZE/1152,d.SBMAX_l),v=0;v<A.npart_l;v++){var e1=B;z[v]>=F&&(e1=R*(z[v]-F)/(24-F)+B*(24-z[v])/(24-F)),q[v]=Math.pow(10,e1/10),0<A.numlines_l[v]?A.rnumlines_l[v]=1/A.numlines_l[v]:A.rnumlines_l[v]=0}A.s3_ll=x(A.s3ind,A.npart_l,z,Z,q,D);var g1,G=0;for(v=0;v<A.npart_l;v++){U=A1.MAX_VALUE;for(var n1=0;n1<A.numlines_l[v];n1++,G++){var b1=$*G/(1e3*d.BLKSIZE);P1=this.ATHformula(1e3*b1,S)-20,P1=Math.pow(10,.1*P1),(P1*=A.numlines_l[v])<U&&(U=P1)}A.ATH.cb_l[v]=U,6<(U=20*z[v]/10-20)&&(U=100),U<-15&&(U=-15),U-=8,A.minval_l[v]=Math.pow(10,U/10)*A.numlines_l[v]}for(A.npart_s=k(A.numlines_s,A.bo_s,A.bm_s,z,Z,A.mld_s,A.PSY.bo_s_weight,$,d.BLKSIZE_s,A.scalefac_band.s,d.BLKSIZE_s/384,d.SBMAX_s),v=G=0;v<A.npart_s;v++){var U,e1=H;z[v]>=F&&(e1=O*(z[v]-F)/(24-F)+H*(24-z[v])/(24-F)),q[v]=Math.pow(10,e1/10),U=A1.MAX_VALUE;for(var n1=0;n1<A.numlines_s[v];n1++,G++){var P1,b1=$*G/(1e3*d.BLKSIZE_s);P1=this.ATHformula(1e3*b1,S)-20,P1=Math.pow(10,.1*P1),(P1*=A.numlines_s[v])<U&&(U=P1)}A.ATH.cb_s[v]=U,U=7*z[v]/12-7,12<z[v]&&(U*=1+3.1*Math.log(1+U)),z[v]<12&&(U*=1+2.3*Math.log(1-U)),U<-15&&(U=-15),U-=8,A.minval_s[v]=Math.pow(10,U/10)*A.numlines_s[v]}A.s3_ss=x(A.s3ind_s,A.npart_s,z,Z,q,D),M=Math.pow(10,(L+1)/16),i=Math.pow(10,(J+1)/16),y=Math.pow(10,j/10),I.init_fft(A),A.decay=Math.exp(-1*W/(.01*$/192)),g1=3.5,2&S.exp_nspsytune&&(g1=1),0<Math.abs(S.msfix)&&(g1=S.msfix),S.msfix=g1;for(var N1=0;N1<A.npart_l;N1++)A.s3ind[N1][1]>A.npart_l-1&&(A.s3ind[N1][1]=A.npart_l-1);var F1=576*A.mode_gr/$;if(A.ATH.decay=Math.pow(10,-1.2*F1),A.ATH.adjust=.01,-(A.ATH.adjustLimit=1)!=S.ATHtype){var X1=S.out_samplerate/d.BLKSIZE,j1=0;for(v=b1=0;v<d.BLKSIZE/2;++v)b1+=X1,A.ATH.eql_w[v]=1/Math.pow(10,this.ATHformula(b1,S)/10),j1+=A.ATH.eql_w[v];for(j1=1/j1,v=d.BLKSIZE/2;0<=--v;)A.ATH.eql_w[v]*=j1}for(var N1=G=0;N1<A.npart_s;++N1)for(v=0;v<A.numlines_s[N1];++v)++G;for(var N1=G=0;N1<A.npart_l;++N1)for(v=0;v<A.numlines_l[N1];++v)++G;for(v=G=0;v<A.npart_l;v++){var b1=$*(G+A.numlines_l[v]/2)/(1*d.BLKSIZE);A.mld_cb_l[v]=V(b1),G+=A.numlines_l[v]}for(;v<d.CBANDS;++v)A.mld_cb_l[v]=1;for(v=G=0;v<A.npart_s;v++){var b1=$*(G+A.numlines_s[v]/2)/(1*d.BLKSIZE_s);A.mld_cb_s[v]=V(b1),G+=A.numlines_s[v]}for(;v<d.CBANDS;++v)A.mld_cb_s[v]=1;return 0},this.ATHformula=function(S,v){var A;switch(v.ATHtype){case 0:A=P(S,9);break;case 1:A=P(S,-1);break;case 2:A=P(S,0);break;case 3:A=P(S,1)+6;break;case 4:A=P(S,v.ATHcurve);break;default:A=P(S,0)}return A}}function h1(){var I,W,Y,N,l1,l=this;h1.V9=410,h1.V8=420,h1.V7=430,h1.V6=440,h1.V5=450,h1.V4=460,h1.V3=470,h1.V2=480,h1.V1=490,h1.V0=500,h1.R3MIX=1e3,h1.STANDARD=1001,h1.EXTREME=1002,h1.INSANE=1003,h1.STANDARD_FAST=1004,h1.EXTREME_FAST=1005,h1.MEDIUM=1006,h1.MEDIUM_FAST=1007,h1.LAME_MAXMP3BUFFER=147456;var w,m,M=new I1;function i(){this.lowerlimit=0}function y(e,t){this.lowpass=t}this.enc=new d,this.setModules=function(e,t,r,n,s,a,u,p,f){I=e,W=t,Y=r,N=n,l1=s,w=a,m=p,this.enc.setModules(W,M,N,w)};var L=4294479419;function J(e,t){var r=[new y(8,2e3),new y(16,3700),new y(24,3900),new y(32,5500),new y(40,7e3),new y(48,7500),new y(56,1e4),new y(64,11e3),new y(80,13500),new y(96,15100),new y(112,15600),new y(128,17e3),new y(160,17500),new y(192,18600),new y(224,19400),new y(256,19700),new y(320,20500)],n=l.nearestBitrateFullIndex(t);e.lowerlimit=r[n].lowpass}function j(e){var t=d.BLKSIZE+e.framesize-d.FFTOFFSET;return t=Math.max(t,512+e.framesize-32)}function X(){this.n_in=0,this.n_out=0}function T(e,t,r,n,s,a){var u=e.internal_flags;if(u.resample_ratio<.9999||1.0001<u.resample_ratio)Q();else{a.n_out=Math.min(e.framesize,s),a.n_in=a.n_out;for(var p=0;p<a.n_out;++p)t[0][u.mf_size+p]=r[0][n+p],u.channels_out==2&&(t[1][u.mf_size+p]=r[1][n+p])}}this.lame_init=function(){var e,t,r=new function(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=t1.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null},n=((e=r).class_id=L,t=e.internal_flags=new B1,e.mode=t1.NOT_SET,e.original=1,e.in_samplerate=44100,e.num_channels=2,e.num_samples=-1,e.bWriteVbrTag=!0,e.quality=-1,e.short_blocks=null,t.subblock_gain=-1,e.lowpassfreq=0,e.highpassfreq=0,e.lowpasswidth=-1,e.highpasswidth=-1,e.VBR=C.vbr_off,e.VBR_q=4,e.ATHcurve=-1,e.VBR_mean_bitrate_kbps=128,e.VBR_min_bitrate_kbps=0,e.VBR_max_bitrate_kbps=0,e.VBR_hard_min=0,t.VBR_min_bitrate=1,t.VBR_max_bitrate=13,e.quant_comp=-1,e.quant_comp_short=-1,e.msfix=-1,t.resample_ratio=1,t.OldValue[0]=180,t.OldValue[1]=180,t.CurrentStep[0]=4,t.CurrentStep[1]=4,t.masking_lower=1,t.nsPsy.attackthre=-1,t.nsPsy.attackthre_s=-1,e.scale=-1,e.athaa_type=-1,e.ATHtype=-1,e.athaa_loudapprox=-1,e.athaa_sensitivity=0,e.useTemporal=null,e.interChRatio=-1,t.mf_samples_to_encode=d.ENCDELAY+d.POSTDELAY,e.encoder_padding=0,t.mf_size=d.ENCDELAY-d.MDCTDELAY,e.findReplayGain=!1,e.decode_on_the_fly=!1,t.decode_on_the_fly=!1,t.findReplayGain=!1,t.findPeakSample=!1,t.RadioGain=0,t.AudiophileGain=0,t.noclipGainChange=0,t.noclipScale=-1,e.preset=0,e.write_id3tag_automatic=!0,0);return n!=0?null:(r.lame_allocated_gfp=1,r)},this.nearestBitrateFullIndex=function(e){var t=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],r=0,n=0,s=0,a=0;a=t[16],n=t[s=16],r=16;for(var u=0;u<16;u++)if(Math.max(e,t[u+1])!=e){a=t[u+1],s=u+1,n=t[u],r=u;break}return e-n<a-e?r:s},this.lame_init_params=function(e){var t,r,n=e.internal_flags;if(n.Class_ID=0,n.ATH==null&&(n.ATH=new function(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=r1(d.SBMAX_l),this.s=r1(d.SBMAX_s),this.psfb21=r1(d.PSFB21),this.psfb12=r1(d.PSFB12),this.cb_l=r1(d.CBANDS),this.cb_s=r1(d.CBANDS),this.eql_w=r1(d.BLKSIZE/2)}),n.PSY==null&&(n.PSY=new function(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=r1(d.SBMAX_l),this.bo_s_weight=r1(d.SBMAX_s)}),n.rgdata==null&&(n.rgdata=new function(){}),n.channels_in=e.num_channels,n.channels_in==1&&(e.mode=t1.MONO),n.channels_out=e.mode==t1.MONO?1:2,n.mode_ext=d.MPG_MD_MS_LR,e.mode==t1.MONO&&(e.force_ms=!1),e.VBR==C.vbr_off&&e.VBR_mean_bitrate_kbps!=128&&e.brate==0&&(e.brate=e.VBR_mean_bitrate_kbps),e.VBR==C.vbr_off||e.VBR==C.vbr_mtrh||e.VBR==C.vbr_mt||(e.free_format=!1),e.VBR==C.vbr_off&&e.brate==0&&Q(),e.VBR==C.vbr_off&&0<e.compression_ratio&&Q(),e.out_samplerate!=0&&(e.out_samplerate<16e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,64)):e.out_samplerate<32e3?(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,8),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,160)):(e.VBR_mean_bitrate_kbps=Math.max(e.VBR_mean_bitrate_kbps,32),e.VBR_mean_bitrate_kbps=Math.min(e.VBR_mean_bitrate_kbps,320))),e.lowpassfreq==0){var s=16e3;switch(e.VBR){case C.vbr_off:var a=new i;J(a,e.brate),s=a.lowerlimit;break;case C.vbr_abr:var a=new i;J(a,e.VBR_mean_bitrate_kbps),s=a.lowerlimit;break;case C.vbr_rh:Q();default:Q()}e.mode!=t1.MONO||e.VBR!=C.vbr_off&&e.VBR!=C.vbr_abr||(s*=1.5),e.lowpassfreq=0|s}switch(e.out_samplerate==0&&Q(),e.lowpassfreq=Math.min(20500,e.lowpassfreq),e.lowpassfreq=Math.min(e.out_samplerate/2,e.lowpassfreq),e.VBR==C.vbr_off&&(e.compression_ratio=16*e.out_samplerate*n.channels_out/(1e3*e.brate)),e.VBR==C.vbr_abr&&Q(),e.bWriteVbrTag||(e.findReplayGain=!1,e.decode_on_the_fly=!1,n.findPeakSample=!1),n.findReplayGain=e.findReplayGain,n.decode_on_the_fly=e.decode_on_the_fly,n.decode_on_the_fly&&(n.findPeakSample=!0),n.findReplayGain&&Q(),n.decode_on_the_fly&&!e.decode_only&&Q(),n.mode_gr=e.out_samplerate<=24e3?1:2,e.framesize=576*n.mode_gr,e.encoder_delay=d.ENCDELAY,n.resample_ratio=e.in_samplerate/e.out_samplerate,e.VBR){case C.vbr_mt:case C.vbr_rh:case C.vbr_mtrh:e.compression_ratio=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5][e.VBR_q];break;case C.vbr_abr:e.compression_ratio=16*e.out_samplerate*n.channels_out/(1e3*e.VBR_mean_bitrate_kbps);break;default:e.compression_ratio=16*e.out_samplerate*n.channels_out/(1e3*e.brate)}e.mode==t1.NOT_SET&&(e.mode=t1.JOINT_STEREO),0<e.highpassfreq?Q():(n.highpass1=0,n.highpass2=0),0<e.lowpassfreq?(n.lowpass2=2*e.lowpassfreq,0<=e.lowpasswidth?Q():n.lowpass1=2*e.lowpassfreq,n.lowpass1/=e.out_samplerate,n.lowpass2/=e.out_samplerate):Q(),function(b){var h,g=b.internal_flags,k=32;if(0<g.lowpass1){for(var x=999,V=0;V<=31;V++){var P=V/31;P>=g.lowpass2&&(k=Math.min(k,V)),g.lowpass1<P&&P<g.lowpass2&&(x=Math.min(x,V))}g.lowpass1=x==999?(k-.75)/31:(x-.75)/31,g.lowpass2=k/31}0<g.highpass2&&Q(),0<g.highpass2&&Q();for(var V=0;V<32;V++){var S,v,P=V/31;g.highpass2>g.highpass1?Q():S=1,v=g.lowpass2>g.lowpass1?1<(h=(P-g.lowpass1)/(g.lowpass2-g.lowpass1+1e-20))?0:h<=0?1:Math.cos(Math.PI/2*h):1,g.amp_filter[V]=S*v}}(e),n.samplerate_index=function(b,h){switch(b){case 44100:return h.version=1,0;case 48e3:return h.version=1;case 32e3:return h.version=1,2;case 22050:return h.version=0;case 24e3:return h.version=0,1;case 16e3:return h.version=0,2;case 11025:return h.version=0;case 12e3:return h.version=0,1;case 8e3:return h.version=0,2;default:return h.version=0,-1}}(e.out_samplerate,e),n.samplerate_index<0&&Q(),e.VBR==C.vbr_off?e.free_format?n.bitrate_index=0:(e.brate=function(b,h,g){g<16e3&&(h=2);for(var k=E.bitrate_table[h][1],x=2;x<=14;x++)0<E.bitrate_table[h][x]&&Math.abs(E.bitrate_table[h][x]-b)<Math.abs(k-b)&&(k=E.bitrate_table[h][x]);return k}(e.brate,e.version,e.out_samplerate),n.bitrate_index=function(b,h,g){g<16e3&&(h=2);for(var k=0;k<=14;k++)if(0<E.bitrate_table[h][k]&&E.bitrate_table[h][k]==b)return k;return-1}(e.brate,e.version,e.out_samplerate),n.bitrate_index<=0&&Q()):n.bitrate_index=1,e.analysis&&(e.bWriteVbrTag=!1),n.pinfo!=null&&(e.bWriteVbrTag=!1),W.init_bit_stream_w(n);for(var u,p=n.samplerate_index+3*e.version+6*(e.out_samplerate<16e3?1:0),f=0;f<d.SBMAX_l+1;f++)n.scalefac_band.l[f]=N.sfBandIndex[p].l[f];for(var f=0;f<d.PSFB21+1;f++){var c=(n.scalefac_band.l[22]-n.scalefac_band.l[21])/d.PSFB21,o=n.scalefac_band.l[21]+f*c;n.scalefac_band.psfb21[f]=o}n.scalefac_band.psfb21[d.PSFB21]=576;for(var f=0;f<d.SBMAX_s+1;f++)n.scalefac_band.s[f]=N.sfBandIndex[p].s[f];for(var f=0;f<d.PSFB12+1;f++){var c=(n.scalefac_band.s[13]-n.scalefac_band.s[12])/d.PSFB12,o=n.scalefac_band.s[12]+f*c;n.scalefac_band.psfb12[f]=o}for(n.scalefac_band.psfb12[d.PSFB12]=192,e.version==1?n.sideinfo_len=n.channels_out==1?21:36:n.sideinfo_len=n.channels_out==1?13:21,e.error_protection&&(n.sideinfo_len+=2),r=void 0,r=(t=e).internal_flags,t.frameNum=0,t.write_id3tag_automatic&&m.id3tag_write_v2(t),r.bitrate_stereoMode_Hist=R1([16,5]),r.bitrate_blockType_Hist=R1([16,6]),r.PeakSample=0,t.bWriteVbrTag&&w.InitVbrTag(t),n.Class_ID=L,u=0;u<19;u++)n.nsPsy.pefirbuf[u]=700*n.mode_gr*n.channels_out;switch(e.ATHtype==-1&&(e.ATHtype=4),e.VBR){case C.vbr_mt:e.VBR=C.vbr_mtrh;case C.vbr_mtrh:e.useTemporal==null&&(e.useTemporal=!1),Y.apply_preset(e,500-10*e.VBR_q,0),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),e.quality<5&&(e.quality=0),5<e.quality&&(e.quality=5),n.PSY.mask_adjust=e.maskingadjust,n.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?n.sfb21_extra=!1:n.sfb21_extra=44e3<e.out_samplerate,n.iteration_loop=new VBRNewIterationLoop(l1);break;case C.vbr_rh:Y.apply_preset(e,500-10*e.VBR_q,0),n.PSY.mask_adjust=e.maskingadjust,n.PSY.mask_adjust_short=e.maskingadjust_short,e.experimentalY?n.sfb21_extra=!1:n.sfb21_extra=44e3<e.out_samplerate,6<e.quality&&(e.quality=6),e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),n.iteration_loop=new VBROldIterationLoop(l1);break;default:var _;n.sfb21_extra=!1,e.quality<0&&(e.quality=LAME_DEFAULT_QUALITY),(_=e.VBR)==C.vbr_off&&(e.VBR_mean_bitrate_kbps=e.brate),Y.apply_preset(e,e.VBR_mean_bitrate_kbps,0),e.VBR=_,n.PSY.mask_adjust=e.maskingadjust,n.PSY.mask_adjust_short=e.maskingadjust_short,_==C.vbr_off?n.iteration_loop=new function(b){var h=b;this.quantize=h,this.iteration_loop=function(g,k,x,V){var P=g.internal_flags,S=r1(M1.SFBMAX),v=r1(576),A=o1(2),D=0,F=P.l3_side,B=new H1(D);this.quantize.rv.ResvFrameBegin(g,B),D=B.bits;for(var R=0;R<P.mode_gr;R++){this.quantize.qupvt.on_pe(g,k,A,D,R,R),P.mode_ext==d.MPG_MD_MS_LR&&Q();for(var H=0;H<P.channels_out;H++){var O,z,Z=F.tt[R][H];Z.block_type!=d.SHORT_TYPE?(O=0,z=P.PSY.mask_adjust-O):(O=0,z=P.PSY.mask_adjust_short-O),P.masking_lower=Math.pow(10,.1*z),this.quantize.init_outer_loop(P,Z),this.quantize.init_xrpow(P,Z,v)&&(this.quantize.qupvt.calc_xmin(g,V[R][H],Z,S),this.quantize.outer_loop(g,Z,S,v,H,A[H])),this.quantize.iteration_finish_one(P,R,H)}}this.quantize.rv.ResvFrameEnd(P,D)}}(l1):Q()}return e.VBR!=C.vbr_off&&Q(),e.tune&&Q(),function(b){var h=b.internal_flags;switch(b.quality){default:case 9:h.psymodel=0,h.noise_shaping=0,h.noise_shaping_amp=0,h.noise_shaping_stop=0,h.use_best_huffman=0,h.full_outer_loop=0;break;case 8:b.quality=7;case 7:h.psymodel=1,h.noise_shaping=0,h.noise_shaping_amp=0,h.noise_shaping_stop=0,h.use_best_huffman=0,h.full_outer_loop=0;break;case 6:case 5:h.psymodel=1,h.noise_shaping==0&&(h.noise_shaping=1),h.noise_shaping_amp=0,h.noise_shaping_stop=0,h.subblock_gain==-1&&(h.subblock_gain=1),h.use_best_huffman=0,h.full_outer_loop=0;break;case 4:h.psymodel=1,h.noise_shaping==0&&(h.noise_shaping=1),h.noise_shaping_amp=0,h.noise_shaping_stop=0,h.subblock_gain==-1&&(h.subblock_gain=1),h.use_best_huffman=1,h.full_outer_loop=0;break;case 3:h.psymodel=1,h.noise_shaping==0&&(h.noise_shaping=1),h.noise_shaping_amp=1,-(h.noise_shaping_stop=1)==h.subblock_gain&&(h.subblock_gain=1),h.use_best_huffman=1,h.full_outer_loop=0;break;case 2:h.psymodel=1,h.noise_shaping==0&&(h.noise_shaping=1),h.substep_shaping==0&&(h.substep_shaping=2),h.noise_shaping_amp=1,-(h.noise_shaping_stop=1)==h.subblock_gain&&(h.subblock_gain=1),h.use_best_huffman=1,h.full_outer_loop=0;break;case 1:case 0:h.psymodel=1,h.noise_shaping==0&&(h.noise_shaping=1),h.substep_shaping==0&&(h.substep_shaping=2),h.noise_shaping_amp=2,-(h.noise_shaping_stop=1)==h.subblock_gain&&(h.subblock_gain=1),h.use_best_huffman=1,h.full_outer_loop=0}}(e),e.athaa_type<0?n.ATH.useAdjust=3:n.ATH.useAdjust=e.athaa_type,n.ATH.aaSensitivityP=Math.pow(10,e.athaa_sensitivity/-10),e.short_blocks==null&&(e.short_blocks=_1.short_block_allowed),e.short_blocks!=_1.short_block_allowed||e.mode!=t1.JOINT_STEREO&&e.mode!=t1.STEREO||(e.short_blocks=_1.short_block_coupled),e.quant_comp<0&&(e.quant_comp=1),e.quant_comp_short<0&&(e.quant_comp_short=0),e.msfix<0&&(e.msfix=0),e.exp_nspsytune=1|e.exp_nspsytune,e.internal_flags.nsPsy.attackthre<0&&(e.internal_flags.nsPsy.attackthre=I1.NSATTACKTHRE),e.internal_flags.nsPsy.attackthre_s<0&&(e.internal_flags.nsPsy.attackthre_s=I1.NSATTACKTHRE_S),e.scale<0&&(e.scale=1),e.ATHtype<0&&(e.ATHtype=4),e.ATHcurve<0&&(e.ATHcurve=4),e.athaa_loudapprox<0&&(e.athaa_loudapprox=2),e.interChRatio<0&&(e.interChRatio=0),e.useTemporal==null&&(e.useTemporal=!0),n.slot_lag=n.frac_SpF=0,e.VBR==C.vbr_off&&(n.slot_lag=n.frac_SpF=72e3*(e.version+1)*e.brate%e.out_samplerate|0),N.iteration_init(e),M.psymodel_init(e),0},this.lame_encode_flush=function(e,t,r,n){var s,a,u,p,f=e.internal_flags,c=u1([2,1152]),o=0,_=f.mf_samples_to_encode-d.POSTDELAY,b=j(e);if(f.mf_samples_to_encode<1)return 0;for(s=0,e.in_samplerate!=e.out_samplerate&&Q(),(u=e.framesize-_%e.framesize)<576&&(u+=e.framesize),e.encoder_padding=u,p=(_+u)/e.framesize;0<p&&0<=o;){var h=b-f.mf_size,g=e.frameNum;h*=e.in_samplerate,1152<(h/=e.out_samplerate)&&(h=1152),h<1&&(h=1),a=n-s,n==0&&(a=0),o=this.lame_encode_buffer(e,c[0],c[1],h,t,r,a),r+=o,s+=o,p-=g!=e.frameNum?1:0}return f.mf_samples_to_encode=0,o<0?o:(a=n-s,n==0&&(a=0),W.flush_bitstream(e),(o=W.copy_buffer(f,t,r,a,1))<0?o:(r+=o,a=n-(s+=o),n==0&&(a=0),e.write_id3tag_automatic&&Q(),s))},this.lame_encode_buffer=function(e,t,r,n,s,a,u){var p,f,c=e.internal_flags,o=[null,null];if(c.Class_ID!=L)return-3;if(n==0)return 0;f=n,((p=c).in_buffer_0==null||p.in_buffer_nsamples<f)&&(p.in_buffer_0=r1(f),p.in_buffer_1=r1(f),p.in_buffer_nsamples=f),o[0]=c.in_buffer_0,o[1]=c.in_buffer_1;for(var _=0;_<n;_++)o[0][_]=t[_],1<c.channels_in&&(o[1][_]=r[_]);return function(b,h,g,k,x,V,P){var S,v,A,D,F,B=b.internal_flags,R=0,H=[null,null],O=[null,null];if(B.Class_ID!=L)return-3;if(k==0)return 0;if((F=W.copy_buffer(B,x,V,P,0))<0)return F;if(V+=F,R+=F,O[0]=h,O[1]=g,m1.NEQ(b.scale,0)&&m1.NEQ(b.scale,1))for(v=0;v<k;++v)O[0][v]*=b.scale,B.channels_out==2&&(O[1][v]*=b.scale);if(m1.NEQ(b.scale_left,0)&&m1.NEQ(b.scale_left,1))for(v=0;v<k;++v)O[0][v]*=b.scale_left;if(m1.NEQ(b.scale_right,0)&&m1.NEQ(b.scale_right,1))for(v=0;v<k;++v)O[1][v]*=b.scale_right;b.num_channels==2&&B.channels_out==1&&Q(),D=j(b),H[0]=B.mfbuf[0],H[1]=B.mfbuf[1];for(var z,Z,q,$,K,e1,g1,G=0;0<k;){var n1=[null,null],b1=0,U=0;n1[0]=O[0],n1[1]=O[1];var P1=new X;if(T(b,H,n1,G,k,P1),b1=P1.n_in,U=P1.n_out,B.findReplayGain&&!B.decode_on_the_fly&&I.AnalyzeSamples(B.rgdata,H[0],B.mf_size,H[1],B.mf_size,U,B.channels_out)==f1.GAIN_ANALYSIS_ERROR)return-6;if(k-=b1,G+=b1,B.channels_out,B.mf_size+=U,B.mf_samples_to_encode<1&&Q(),B.mf_samples_to_encode+=U,B.mf_size>=D){var N1=P-R;if(P==0&&(N1=0),z=b,Z=H[0],q=H[1],$=x,K=V,e1=N1,g1=l.enc.lame_encode_mp3_frame(z,Z,q,$,K,e1),z.frameNum++,(S=g1)<0)return S;for(V+=S,R+=S,B.mf_size-=b.framesize,B.mf_samples_to_encode-=b.framesize,A=0;A<B.channels_out;A++)for(v=0;v<B.mf_size;v++)H[A][v]=H[A][v+b.framesize]}}return R}(e,o[0],o[1],n,s,a,u)}}M1.SFBMAX=3*d.SBMAX_s,d.ENCDELAY=576,d.POSTDELAY=1152,d.FFTOFFSET=224+(d.MDCTDELAY=48),d.DECDELAY=528,d.SBLIMIT=32,d.CBANDS=64,d.SBPSY_l=21,d.SBPSY_s=12,d.SBMAX_l=22,d.SBMAX_s=13,d.PSFB21=6,d.PSFB12=6,d.HBLKSIZE=(d.BLKSIZE=1024)/2+1,d.HBLKSIZE_s=(d.BLKSIZE_s=256)/2+1,d.NORM_TYPE=0,d.START_TYPE=1,d.SHORT_TYPE=2,d.STOP_TYPE=3,d.MPG_MD_LR_LR=0,d.MPG_MD_LR_I=1,d.MPG_MD_MS_LR=2,d.MPG_MD_MS_I=3,d.fircoef=[-.1039435,-.1892065,5*-.0432472,-.155915,3898045e-23,.0467745*5,.50455,.756825,.187098*5],B1.MFSIZE=3456+d.ENCDELAY-d.MDCTDELAY,B1.MAX_HEADER_BUF=256,B1.MAX_BITS_PER_CHANNEL=4095,B1.MAX_BITS_PER_GRANULE=7680,B1.BPC=320,M1.SFBMAX=3*d.SBMAX_s,k1.Mp3Encoder=function(I,W,Y){I!=1&&Q("fix cc: only supports mono");var N=new h1,l1=new function(){this.setModules=function(a,u){}},l=new f1,w=new m1,m=new function(){function a(o,_,b,h,g,k,x,V,P,S,v,A,D,F){this.quant_comp=_,this.quant_comp_s=b,this.safejoint=h,this.nsmsfix=g,this.st_lrm=k,this.st_s=x,this.nsbass=V,this.scale=P,this.masking_adj=S,this.ath_lower=v,this.ath_curve=A,this.interch=D,this.sfscale=F}var u;function p(o,_,b){Q()}this.setModules=function(o){u=o};var f=[new a(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new a(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new a(24,9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new a(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new a(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new a(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9e-4,1),new a(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8e-4,1),new a(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8e-4,1),new a(80,9,9,0,0,6.6,145,0,.95,0,0,8,7e-4,1),new a(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6e-4,1),new a(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5e-4,1),new a(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2e-4,1),new a(160,9,9,1,1.79,6,135,0,.95,-2,5,3.5,0,1),new a(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new a(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new a(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new a(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];function c(o,_,b){var h=_,g=u.nearestBitrateFullIndex(_);if(o.VBR=C.vbr_abr,o.VBR_mean_bitrate_kbps=h,o.VBR_mean_bitrate_kbps=Math.min(o.VBR_mean_bitrate_kbps,320),o.VBR_mean_bitrate_kbps=Math.max(o.VBR_mean_bitrate_kbps,8),o.brate=o.VBR_mean_bitrate_kbps,320<o.VBR_mean_bitrate_kbps&&(o.disable_reservoir=!0),0<f[g].safejoint&&(o.exp_nspsytune=2|o.exp_nspsytune),0<f[g].sfscale&&(o.internal_flags.noise_shaping=2),0<Math.abs(f[g].nsbass)){var k=int(4*f[g].nsbass);k<0&&(k+=64),o.exp_nspsytune=o.exp_nspsytune|k<<2}return b!=0?o.quant_comp=f[g].quant_comp:0<Math.abs(o.quant_comp- -1)||(o.quant_comp=f[g].quant_comp),b!=0?o.quant_comp_short=f[g].quant_comp_s:0<Math.abs(o.quant_comp_short- -1)||(o.quant_comp_short=f[g].quant_comp_s),b!=0?o.msfix=f[g].nsmsfix:0<Math.abs(o.msfix- -1)||(o.msfix=f[g].nsmsfix),b!=0?o.internal_flags.nsPsy.attackthre=f[g].st_lrm:0<Math.abs(o.internal_flags.nsPsy.attackthre- -1)||(o.internal_flags.nsPsy.attackthre=f[g].st_lrm),b!=0?o.internal_flags.nsPsy.attackthre_s=f[g].st_s:0<Math.abs(o.internal_flags.nsPsy.attackthre_s- -1)||(o.internal_flags.nsPsy.attackthre_s=f[g].st_s),b!=0?o.scale=f[g].scale:0<Math.abs(o.scale- -1)||(o.scale=f[g].scale),b!=0?o.maskingadjust=f[g].masking_adj:0<Math.abs(o.maskingadjust-0)||(o.maskingadjust=f[g].masking_adj),0<f[g].masking_adj?b!=0?o.maskingadjust_short=.9*f[g].masking_adj:0<Math.abs(o.maskingadjust_short-0)||(o.maskingadjust_short=.9*f[g].masking_adj):b!=0?o.maskingadjust_short=1.1*f[g].masking_adj:0<Math.abs(o.maskingadjust_short-0)||(o.maskingadjust_short=1.1*f[g].masking_adj),b!=0?o.ATHlower=-f[g].ath_lower/10:0<Math.abs(10*-o.ATHlower-0)||(o.ATHlower=-f[g].ath_lower/10),b!=0?o.ATHcurve=f[g].ath_curve:0<Math.abs(o.ATHcurve- -1)||(o.ATHcurve=f[g].ath_curve),b!=0?o.interChRatio=f[g].interch:0<Math.abs(o.interChRatio- -1)||(o.interChRatio=f[g].interch),_}this.apply_preset=function(o,_,b){switch(_){case h1.R3MIX:_=h1.V3,o.VBR=C.vbr_mtrh;break;case h1.MEDIUM:_=h1.V4,o.VBR=C.vbr_rh;break;case h1.MEDIUM_FAST:_=h1.V4,o.VBR=C.vbr_mtrh;break;case h1.STANDARD:_=h1.V2,o.VBR=C.vbr_rh;break;case h1.STANDARD_FAST:_=h1.V2,o.VBR=C.vbr_mtrh;break;case h1.EXTREME:_=h1.V0,o.VBR=C.vbr_rh;break;case h1.EXTREME_FAST:_=h1.V0,o.VBR=C.vbr_mtrh;break;case h1.INSANE:return _=320,o.preset=_,c(o,_,b),o.VBR=C.vbr_off,_}switch(o.preset=_){case h1.V9:return p(),_;case h1.V8:return p(),_;case h1.V7:return p(),_;case h1.V6:return p(),_;case h1.V5:return p(),_;case h1.V4:return p(),_;case h1.V3:return p(),_;case h1.V2:return p(),_;case h1.V1:return p(),_;case h1.V0:return p(),_}return 8<=_&&_<=320?c(o,_,b):(o.preset=0,_)}},M=new E1,i=new c1,y=new w1,L=new function(){this.getLameShortVersion=function(){return"3.98.4"}},J=new function(){this.setModules=function(a,u){}},j=new function(){var a;this.setModules=function(u){a=u},this.ResvFrameBegin=function(u,p){var f,c=u.internal_flags,o=c.l3_side,_=a.getframebits(u);p.bits=(_-8*c.sideinfo_len)/c.mode_gr;var b=2048*c.mode_gr-8;320<u.brate?Q():(f=11520,u.strict_ISO&&Q()),c.ResvMax=f-_,c.ResvMax>b&&(c.ResvMax=b),(c.ResvMax<0||u.disable_reservoir)&&(c.ResvMax=0);var h=p.bits*c.mode_gr+Math.min(c.ResvSize,c.ResvMax);return f<h&&(h=f),o.resvDrain_pre=0,c.pinfo!=null&&Q(),h},this.ResvMaxBits=function(u,p,f,c){var o,_=u.internal_flags,b=_.ResvSize,h=_.ResvMax;c!=0&&(b+=p),1&_.substep_shaping&&(h*=.9),f.bits=p,9*h<10*b?(o=b-9*h/10,f.bits+=o,_.substep_shaping|=128):(o=0,_.substep_shaping&=127,u.disable_reservoir||1&_.substep_shaping||(f.bits-=.1*p));var g=b<6*_.ResvMax/10?b:6*_.ResvMax/10;return(g-=o)<0&&(g=0),g},this.ResvAdjust=function(u,p){u.ResvSize-=p.part2_3_length+p.part2_length},this.ResvFrameEnd=function(u,p){var f,c=u.l3_side;u.ResvSize+=p*u.mode_gr;var o=0;c.resvDrain_post=0,(c.resvDrain_pre=0)!=(f=u.ResvSize%8)&&(o+=f),0<(f=u.ResvSize-o-u.ResvMax)&&(o+=f);var _=Math.min(8*c.main_data_begin,o)/8;c.resvDrain_pre+=8*_,o-=8*_,u.ResvSize-=8*_,c.main_data_begin-=_,c.resvDrain_post+=o,u.ResvSize-=o}},X=new i1,T=new function(){this.setModules=function(a,u,p){}},e=new function(){};N.setModules(l,w,m,M,i,y,L,J,e),w.setModules(l,e,L,y),J.setModules(w,L),m.setModules(N),i.setModules(w,j,M,X),M.setModules(X,j,N.enc.psy),j.setModules(w),X.setModules(M),y.setModules(N,w,L),l1.setModules(T,e),T.setModules(L,J,m);var t=N.lame_init();t.num_channels=I,t.in_samplerate=W,t.out_samplerate=W,t.brate=Y,t.mode=t1.STEREO,t.quality=3,t.bWriteVbrTag=!1,t.disable_reservoir=!0,t.write_id3tag_automatic=!1,N.lame_init_params(t);var r=1152,n=0|1.25*r+7200,s=S1(n);this.encodeBuffer=function(a,u){I==1&&(u=a),a.length>r&&(r=a.length,s=S1(n=0|1.25*r+7200));var p=N.lame_encode_buffer(t,a,u,a.length,s,0,n);return new Int8Array(s.subarray(0,p))},this.flush=function(){var a=N.lame_encode_flush(t,s,0,n);return new Int8Array(s.subarray(0,a))}}}k1(),q1.lamejs=k1}((typeof window=="object"&&window.document?window:Object).Recorder)})(Et);var Vt=Et.exports;const Dt=Lt(Vt),Ft=function(z1){let q1,k1,Y1,Q,S1,Z1,o1,r1;const s1=function(R1){q1=Math.round(Math.log(R1)/Math.log(2)),k1=1<<q1,Y1=(k1<<2)*Math.sqrt(2),Q=[],S1=[],Z1=[0],o1=[0],r1=[];let u1,C1,x1,v1;for(u1=0;u1<k1;u1++){for(x1=u1,C1=0,v1=0;C1!=q1;C1++)v1<<=1,v1|=x1&1,x1>>>=1;r1[u1]=v1}let d1,_1=2*Math.PI/k1;for(u1=(k1>>1)-1;u1>0;u1--)d1=u1*_1,o1[u1]=Math.cos(d1),Z1[u1]=Math.sin(d1)},y1=function(R1){let u1,C1,x1,v1,d1=1,_1=q1-1,A1,C,t1,i1;for(u1=0;u1!=k1;u1++)Q[u1]=R1[r1[u1]],S1[u1]=0;for(u1=q1;u1!=0;u1--){for(C1=0;C1!=d1;C1++)for(A1=o1[C1<<_1],C=Z1[C1<<_1],x1=C1;x1<k1;x1+=d1<<1)v1=x1+d1,t1=A1*Q[v1]-C*S1[v1],i1=A1*S1[v1]+C*Q[v1],Q[v1]=Q[x1]-t1,S1[v1]=S1[x1]-i1,Q[x1]+=t1,S1[x1]+=i1;d1<<=1,_1--}C1=k1>>1;const f1=new Float64Array(C1);for(C=Y1,A1=-Y1,u1=C1;u1!=0;u1--)t1=Q[u1],i1=S1[u1],t1>A1&&t1<C&&i1>A1&&i1<C?f1[u1-1]=0:f1[u1-1]=Math.round(t1*t1+i1*i1);return f1};return s1(z1),{transform:y1,bufferSize:k1}},qt=(z1,q1)=>{q1=q1||{type:"mp3",sampleRate:32e3,bitRate:32,duration:6e5,numberOfChannels:1,encodeBitRate:64e3,format:"mp3",frameSize:1};const k1=Rt(!1),Y1=Rt(!1),Q=Ht(),S1=()=>{Q.value=Dt({...q1,async onProcess(y1,R1,u1,C1){var x1;(x1=z1==null?void 0:z1.ondata)==null||x1.call(z1,{pcmData:y1[y1.length-1],powerLevel:R1,sampleRate:C1})}})},Z1=()=>new Promise(async(y1,R1)=>{Q.value||S1(),Q.value.open(()=>{Y1.value=!0,y1()},u1=>{Y1.value=!1,R1("无法录音:"+u1)})}),o1=async()=>{var y1;try{Q.value||S1(),Q.value.start(q1),k1.value=!0,(y1=z1.onstart)==null||y1.call(z1)}catch(R1){return console.log(R1),Promise.reject(R1)}},r1=()=>{var y1;Q.value&&k1.value&&((y1=Q.value)==null||y1.stop((R1,u1)=>{var x1;const C1=window.URL.createObjectURL(R1);return k1.value=!1,(x1=z1.onstop)==null?void 0:x1.call(z1,{tempFilePath:C1,duration:u1,blob:R1})},()=>{k1.value=!1}))},s1=()=>{var y1,R1;(R1=(y1=Q.value)==null?void 0:y1.close)==null||R1.call(y1,()=>{k1.value=!1,Y1.value=!1}),Q.value=null};return Ot(()=>{r1(),s1()}),{isRecording:k1,isOpen:Y1,mediaRecorder:Q,start:o1,authorize:Z1,stop:r1,close:s1}},zt={id:"",width:0,height:0,scale:2,fps:30,fftSize:1024,lineCount:6,widthRatio:.6,spaceWidth:0,minHeight:8,position:0,mirrorEnable:!1,fallDuration:600,linear:[{pos:0,color:"white"},{pos:1,color:"white"}],round:!0,fullFreq:!1},Gt=z1=>{const q1=z1.id;q1||console.error("绘制图形前必须指定`canvasId`");let k1=Object.assign({},zt,z1);(!k1.width||!k1.height)&&console.error("必须指定画布的宽高");const Y1=Ft(k1.fftSize);let Q,S1=0,Z1=0,o1=0,r1=0,s1=[];const y1=_1=>{Q=_1,S1=0,Z1=Date.now(),v1()},R1=(_1,A1,C,t1)=>{const i1=_1.createLinearGradient(0,C,0,t1);for(let f1=0;f1<A1.length;f1++)i1.addColorStop(A1[f1].pos,A1[f1].color);return i1},u1=(_1,A1,C,t1,i1,f1)=>{const[w1,m1,a1,E]=f1;_1.beginPath(),_1.moveTo(A1+w1,C),_1.lineTo(A1+t1-w1,C),_1.arc(A1+t1-m1,C+m1,m1,Math.PI*1.5,Math.PI*2),_1.lineTo(A1+t1,C+i1-a1),_1.arc(A1+t1-a1,C+i1-a1,a1,0,Math.PI*.5),_1.lineTo(A1+E,C+i1),_1.arc(A1+E,C+i1-E,E,Math.PI*.5,Math.PI),_1.lineTo(A1,C+w1),_1.arc(A1+w1,C+w1,w1,Math.PI,Math.PI*1.5),_1.fill()},C1=k1.onDraw?k1.onDraw:(_1,{frequencyData:A1,sampleRate:C,options:t1})=>{const{scale:i1,width:f1,height:w1,lineCount:m1,round:a1,fftSize:E,position:H1,fallDuration:V1,fps:T1,fullFreq:E1,linear:O1,mirrorEnable:M1}=t1,c1=f1*i1,L1=w1*i1,d=Math.abs(H1);let p1=H1==1?0:L1,B1=L1;d<1&&(B1=B1/2,p1=B1,B1=Math.floor(B1*(1+d)),p1=Math.floor(H1>0?p1*(1-d):p1*(1+d)));const I1=s1,h1=Math.ceil(B1/(V1/(1e3/T1))),I=1<<(Math.round(Math.log(E)/Math.log(2)+3)<<1),W=Math.log(I)/Math.log(10),Y=20*Math.log(32767)/Math.log(10),N=E/2.5;let l1=N;E1||(l1=Math.min(N,Math.floor(N*5e3/(C/2))));const l=l1==E,w=l?m1:Math.round(m1*.8),m=l1/w,M=l?0:(N-l1)/(m1-w);let i=0;for(let s=0;s<m1;s++){const a=Math.ceil(i);s<w?i+=m:i+=M;let u=Math.ceil(i);u==a&&u++,u=Math.min(u,N);let p=0;if(A1)for(let o=a;o<u;o++)p=Math.max(p,Math.abs(A1[o]));const f=p>I?Math.floor((Math.log(p)/Math.log(10)-W)*17):0;let c=B1*Math.min(f/Y,1);I1[s]=(I1[s]||0)-h1,c<I1[s]&&(c=I1[s]),c<0&&(c=0),I1[s]=c}_1.clearRect(0,0,c1,L1);const y=R1(_1,O1,p1,p1-B1),L=R1(_1,O1,p1,p1+B1),J=M1?m1*2-1:m1,j=t1.spaceWidth*i1;let X=t1.widthRatio;j!=0&&(X=(c1-j*(J+1))/c1);let T=0,e=0,t=0;for(let s=0;s<2;s++){const a=Math.max(1*i1,c1*X/J);if(T=Math.floor(a),t=a-T,e=(c1-J*a)/(J+1),e>0&&e<1)X=1,e=0;else break}const r=t1.minHeight*i1,n=M1?(c1-T)/2-e:0;for(let s=0;s<2;s++){s&&(_1.save(),_1.scale(-1,1));const a=s?c1:0;for(let u=0,p=n,f=0,c,o,_,b;u<m1;u++){p+=e,c=Math.floor(p)-a,_=T,f+=t,f>=1&&(_++,f--),b=Math.max(s1[u],r);const h=a1?_/2:0;let g=new Array(4).fill(h);p1!=0&&(o=p1-b,_1.fillStyle=y,p1!=L1&&(g=[h,h,0,0]),u1(_1,c,o,_,b,g)),p1!=L1&&(_1.fillStyle=L,p1!=0&&(g=[0,0,h,h]),u1(_1,c,p1,_,b,g)),p+=_}if(s&&_1.restore(),!M1)break}};delete k1.onDraw;const x1=(_1,A1)=>{const C=document.getElementById(q1);if(!C){console.error(`canvasId：${q1}无效`);return}const t1=C.getContext("2d");C1(t1,{frequencyData:_1,sampleRate:A1,options:k1})},v1=()=>{const _1=Math.floor(1e3/k1.fps);o1||(o1=Pt(function(){v1()},_1));const A1=Date.now();if(r1=r1||0,A1-Z1>(k1==null?void 0:k1.fallDuration)*1.5){clearInterval(o1),s1=[],x1(null,Q.sampleRate);return}if(A1-r1<_1)return;r1=A1;const C=Y1.bufferSize,t1=Q.pcmData;let i1=S1;const f1=new Int16Array(C);for(let m1=0;m1<C&&i1<t1.length;m1++,i1++)f1[m1]=t1[i1];S1=i1;const w1=Y1.transform(f1);x1(w1,Q.sampleRate)},d1=()=>{clearInterval(o1)};return Nt(()=>z1,()=>{k1=Object.assign(k1,z1)},{deep:!0}),{render:y1,draw:x1,stopRender:d1}};export{qt as a,Gt as u};
