import{E as N,a as R}from"./C9f7n97H.js";import{a as I,E as U}from"./B7GaOiDz.js";import{bH as j,f as D,e as S,E as A,v as M}from"./CmRxzTqw.js";import{_ as z}from"./eFgaMLiC.js";import{E as G}from"./DNRqakyH.js";/* empty css        */import"./DP2rzg_V.js";import"./DikNcrXK.js";/* empty css        */import{u as H}from"./DymDsCmz.js";import{l as L,j as O,b as T,M as i,N as C,a3 as n,a1 as a,u as t,ag as u,a0 as m,Z as p,O as l,a7 as Z,a5 as x}from"./CUZG7cWw.js";const $={class:"bg-white rounded-[10px] p-main h-[420px]"},q={class:"flex-1"},J={class:"el-upload__text flex items-center justify-center"},K={key:0,class:"mt-[10px]"},P={class:"flex flex-col items-center justify-center"},Q={class:"flex mt-[10px] items-center"},W={class:"scale-90"},X=["src"],y=".wav,.mp3",_e=L({__name:"center-setting",setup(Y){const o=H(),f=O(),c=T(!1),E=r=>{},g=async({raw:r})=>{var e;try{if(r){c.value=!0;const s=await j("audio",{file:r,data:{use_type:2}});o.voiceContent.voice_url=s.uri,o.voiceContent.voice_name=s.name}}catch(s){D.msgError(s)}finally{c.value=!1,(e=f.value)==null||e.clearFiles()}},k=()=>{o.voiceContent.voice_url="",o.voiceContent.voice_name=""};return(r,e)=>{const s=N,V=R,d=U,w=S,v=z,h=G,b=A,B=I,F=M;return i(),C("div",$,[n(B,null,{default:a(()=>[n(d,{label:"播报内容"},{default:a(()=>[n(V,{modelValue:t(o).voiceContent.type,"onUpdate:modelValue":e[0]||(e[0]=_=>t(o).voiceContent.type=_),onChange:E},{default:a(()=>[n(s,{label:1},{default:a(()=>e[2]||(e[2]=[u("文本输入")])),_:1}),n(s,{label:2},{default:a(()=>e[3]||(e[3]=[u("音频输入")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(o).voiceContent.type==1?(i(),m(d,{key:0},{default:a(()=>[n(w,{modelValue:t(o).voiceContent.text,"onUpdate:modelValue":e[1]||(e[1]=_=>t(o).voiceContent.text=_),type:"textarea",rows:15,resize:"none",placeholder:"请输入播报内容..."},null,8,["modelValue"])]),_:1})):p("",!0),t(o).voiceContent.type==2?(i(),m(d,{key:1},{default:a(()=>[l("div",q,[Z((i(),m(h,{ref_key:"uploadRef",ref:f,drag:"","on-change":g,"auto-upload":!1,"show-file-list":!1,accept:y},{default:a(()=>[l("div",J,[n(v,{name:"el-icon-Upload"}),e[4]||(e[4]=u(" 拖拽文件至此，或点击")),e[5]||(e[5]=l("em",null," 选择文件 ",-1))]),l("div",{class:"el-upload__text"}," 音频支持："+x(y)+"格式，时长不超过30分钟， 大小不超过50MB ")]),_:1})),[[F,t(c)]]),t(o).voiceContent.voice_url?(i(),C("div",K,[l("div",P,[l("div",null,x(t(o).voiceContent.voice_name),1),l("div",Q,[l("div",W,[l("audio",{src:t(o).voiceContent.voice_url,controls:""},null,8,X)]),n(b,{link:"",onClick:k},{default:a(()=>[n(v,{name:"el-icon-Delete"})]),_:1})])])])):p("",!0)])]),_:1})):p("",!0)]),_:1})])}}});export{_e as _};
