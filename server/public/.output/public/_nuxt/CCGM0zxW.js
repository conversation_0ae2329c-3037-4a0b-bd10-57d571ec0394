import{E as me}from"./DCzKTodP.js";import{b as X,i as fe,bD as Q,bE as ve,bF as ye,bG as J,j as _e,f as se,E as O,v as ge,m as he,h as we,bz as ke,bH as xe,e as be}from"./CmRxzTqw.js";/* empty css        */import{l as S,az as Ce,M as e,N as i,O as n,Z as d,$ as W,ag as k,a5 as x,u as o,a0 as g,a1 as r,V as F,a6 as Z,a3 as y,_ as B,ao as U,b as E,j as K,r as $e,m as G,a7 as Ee,y as ne,n as Ie,a4 as Ae,w as Be}from"./CUZG7cWw.js";import{_ as Y}from"./DlAUqK2U.js";import{_ as le}from"./CH6wv3Pu.js";import{E as ee}from"./CiabO6Xq.js";import{_ as Pe}from"./BYMcWg3Q.js";import{E as Se}from"./DjHPV-Am.js";import{_ as H}from"./eFgaMLiC.js";/* empty css        */import{u as Ve}from"./DoCT-qbH.js";import{u as ae}from"./DAOx25wS.js";import{E as te}from"./oVx59syQ.js";import{E as oe}from"./ArzC3z2d.js";/* empty css        *//* empty css        */import{c as De}from"./DwFObZc_.js";import{g as Re}from"./DQUFgXGm.js";import{d as Ue}from"./CRNANWso.js";import{Q as je}from"./CHg9aK2B.js";import{i as qe}from"./DecTOTC8.js";import{E as Le}from"./DNRqakyH.js";import"./DikNcrXK.js";const Te=["src"],ze={key:0,class:"h-[20px] mb-[10px] text-tx-secondary text-xs"},Ne={class:"overflow-x-auto"},Me=S({__name:"item",props:{type:{default:"left"},avatar:{default:""},bg:{default:""},color:{default:"black"},time:{default:""},modelName:{default:""}},setup(m){Ce(l=>({75584782:l.color}));const f=X();return(l,p)=>{const v=me;return e(),i("div",{class:"chat-msg-item",style:Z(l.bg?`--item-bg: ${l.bg}`:"")},[n("div",{class:W(`chat-msg-item__${l.type}`)},[l.avatar?(e(),i("img",{key:0,class:"chat-msg-item__avatar",src:l.avatar},null,8,Te)):d("",!0),n("div",{class:W([`chat-msg-item__${l.type}-wrap`,{"has-time":l.time}])},[l.time?(e(),i("div",ze,[k(x(l.time)+" ",1),l.modelName&&o(f).getChatConfig.is_show_model?(e(),g(v,{key:0,class:"ml-2",type:"success",style:{"--el-tag-border-color":"transparent"}},{default:r(()=>[k(x(l.modelName),1)]),_:1})):d("",!0)])):d("",!0),n("div",Ne,[n("div",{class:W(`chat-msg-item__${l.type}-content`)},[F(l.$slots,"default",{},void 0,!0)],2)]),n("div",null,[F(l.$slots,"actions",{},void 0,!0)])],2)],2),F(l.$slots,"outer_actions",{},void 0,!0)],4)}}}),wo=Y(Me,[["__scopeId","data-v-525ad2b6"]]),We="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAgCAYAAABgrToAAAACJElEQVRYR+2YMWsUURSFz3m7s+nskjUIQSutbMRi7WzUVjSadMHCbVLkByjmLygaCVYWRqMEUhkFS9Gg0cJfYCPZjYUQFbPs+I7c2R1Q2ZjZfRNYYS4MAzPv3vnmvDvL3kMA2Hl5/CjLI9ckf4ZwY3Zt15C+gfwIao3So0rt3XsJtPUk9M/cAW6y9ap2DIyfAjgCwANwGeoYiEFtk/5e5CvXeer1D2neATcGgiTZM4+t9RNLEKcBtAFEGeBsiRWzl7EoSXo+8rV9gWc/fDc1B1VSEoEnDpj0KTB33tS26DGaEezvZQZpRxmODyoT5+vwBwS3zeTcT4yjTdZNJEiPSykk1bjZX6HeD/WQJ1zUApgq2w+etcsniBuAVlH9vELOx6Yo1VywgkmTB4X1kEGGhyAtg/Ecq3NNqnknDwVTrNBaactEts88OHs5b8Bw/Tof4M+kr4WrwwhoL9n5uRPWhxWwsxPEl+EGNMacP5I8evCPGgVgqKSFgoWCoQqE5hc9WCgYqkBoftGDeSiYz1/+UJLe+foftvh2A2B1fwQIrapkaFoDcK4PVyH0qVnyU4fjGdW4NQ2WlgDE5hLkMoJmQdh9zW9Dk59K5lhtLjyE01TX/jDILP5MGEbvbFPOJroIXvc5PjvTBbx7GM4vAjjd9WdSc2g/IPaqaTv5Aq58haP1TSb2Au20GGErvgTxIqiTAA7tVSnn+2Z9vAXdCsa4bD6Nsf0C/gYA5PMzcW0AAAAASUVORK5CYII=",Fe={class:"h-[65vh]"},Ge={class:"font-medium text-tx-primary"},Ye={class:"text-muted text-sm whitespace-pre-wrap"},Qe=S({__name:"context-popup",props:{context:{default:()=>[]}},emits:["close"],setup(m,{emit:f}){const l=f;return(p,v)=>{const _=te,h=oe;return e(),g(h,{title:`对话上下文(${p.context.length}条)`,"model-value":!0,width:"700px",onClose:v[0]||(v[0]=w=>l("close"))},{default:r(()=>[n("div",Fe,[y(_,null,{default:r(()=>[(e(!0),i(B,null,U(p.context,(w,b)=>(e(),i("div",{key:b,class:"py-[6px] px-[10px] border border-solid border-br-light rounded mb-[10px]"},[n("div",Ge,x(w.role),1),n("div",Ye,x(w.content),1)]))),128))]),_:1})])]),_:1},8,["title"])}}}),Ze={class:"h-[65vh]"},Oe={key:0,class:"text-muted text-sm whitespace-pre-wrap"},Ke={key:1,class:"text-muted text-sm whitespace-pre-wrap"},He=S({__name:"quote-popup",props:{quotes:{default:()=>[]}},emits:["close"],setup(m,{emit:f}){const l=f;return(p,v)=>{const _=te,h=oe;return e(),g(h,{title:`知识库引用(${p.quotes.length}条)`,"model-value":!0,width:"700px",onClose:v[0]||(v[0]=w=>l("close"))},{default:r(()=>[n("div",Ze,[y(_,null,{default:r(()=>[(e(!0),i(B,null,U(p.quotes,(w,b)=>(e(),i("div",{key:b,class:"py-[6px] px-[10px] border border-solid border-br-light rounded mb-[10px]"},[w.question?(e(),i("div",Oe,x(w.question),1)):d("",!0),w.answer?(e(),i("div",Ke,x(w.answer),1)):d("",!0)]))),128))]),_:1})])]),_:1},8,["title"])}}}),Je=fe({id:"chatStore",state:()=>{const m=Q(ve,"");return m.value||(m.value=ye()),{uuid:m.value||"",token:"",channelId:""}},getters:{isAuth:m=>!!m.token},actions:{init(m){this.channelId=m;const f=Q(J,{});this.token=f.value[this.channelId]||""},clearAuth(){const m=Q(J,{});delete m.value[this.channelId],this.token=""},async auth(m=""){const f=await De({password:m,channel:this.channelId,identity:this.uuid});this.token=f.token;const l=Q(J,{});l.value=Object.assign({[this.channelId]:f.token},l.value)}}}),Xe={key:0,class:"flex mb-[16px] bg-body rounded-[5px] p-[10px] max-w-[300px] items-center"},et={class:"line-clamp-2 flex-1 min-w-0 ml-[6px] text-tx-primary",style:{"word-break":"break-word"}},tt=S({__name:"record-image",props:{url:{},name:{}},setup(m){return(f,l)=>{const p=ee;return f.url?(e(),i("div",Xe,[y(p,{class:"flex-none w-[40px] h-[40px]",src:f.url,"preview-src-list":[f.url],"hide-on-click-modal":!0},null,8,["src","preview-src-list"]),n("div",et,x(f.name),1)])):d("",!0)}}}),ot={class:"line-clamp-2 flex-1 min-w-0 ml-[6px]",style:{"word-break":"break-word"}},st=S({__name:"record-file",props:{url:{},name:{}},setup(m){const f=m,l=async()=>{window.open(f.url,"_blank")};return(p,v)=>p.url?(e(),i("div",{key:0,class:"flex mb-[16px] bg-white rounded-[5px] p-[10px] max-w-[300px] items-center cursor-pointer",onClick:l},[n("div",ot,x(p.name),1)])):d("",!0)}}),nt={class:"h-[70vh]"},lt={class:"poster-bg flex flex-col"},at=["src"],it={class:"px-[20px] pt-[135px]"},rt={class:"bg-white rounded-lg p-[15px] text-tx-primary"},ut={class:"flex justify-end text-[16px] items-baseline"},dt={class:"bg-[#066cff] px-[10px] py-[6px] text-white",style:{"border-radius":"8px 0 8px 8px"}},pt={class:"line-clamp-2"},ct={class:"text-[14px]"},mt={class:"px-[20px] pt-[20px]"},ft={class:"flex items-center justify-between"},vt={class:"flex items-center"},yt=["src"],_t={class:"ml-[10px] text-[16px]"},gt={class:"line-clamp-2"},ht={key:0},wt={class:"w-[120px] flex flex-col justify-center items-end"},kt={class:"flex justify-end mt-[10px]"},xt=S({__name:"dialog-poster",setup(m,{expose:f}){const l=X(),p=E(!1),v=E(!1),_=E({}),h=E(!1),w=_e(),b=K(),P=$e({title:"",content:""}),T=async()=>{var $;h.value=!0;try{const{data:t}=await he({id:12});_.value=(($=JSON.parse(t)[0])==null?void 0:$.content)||{}}finally{h.value=!1}},z=G(()=>`${window.origin}/mobile?user_sn=${w.userInfo.sn}`),{copy:j}=ae(),N=G(()=>`${window.origin}/?user_sn=${w.userInfo.sn}`),I=async()=>{try{v.value=!0,await Ue(b.value)}catch{se.msgError("下载失败，请重试")}finally{v.value=!1}};return f({open:$=>{T(),p.value=!0,P.title=$.title,P.content=qe($.content)?[$.content]:$.content}}),($,t)=>{const a=le,M=te,L=H,V=O,D=oe,R=ge;return e(),g(D,{modelValue:o(p),"onUpdate:modelValue":t[1]||(t[1]=s=>ne(p)?p.value=s:null),title:"生成海报","show-close":"",width:"430px"},{default:r(()=>[n("div",nt,[Ee((e(),g(M,null,{default:r(()=>[o(h)?d("",!0):(e(),i("div",{key:0,ref_key:"posterRef",ref:b,class:"poster overflow-hidden pb-[10px] rounded-lg"},[n("div",lt,[n("img",{class:"w-full",src:o(_).default==2?o(l).getImageUrl(o(_).posterUrl):o(_).poster==1?o(l).getImageUrl(o(_).defaultUrl1):o(l).getImageUrl(o(_).defaultUrl2),style:Z({background:o(_).bgColor}),alt:""},null,12,at),n("div",{class:"flex-1 min-h-0",style:Z({background:o(_).bgColor})},null,4)]),n("div",{class:"w-full h-full poster-contain1 bg-[#BBBBBB]",style:Z({color:o(_).textColor})},[n("div",it,[n("div",rt,[n("div",ut,[n("span",dt,[n("span",pt,x(o(P).title),1)])]),n("span",ct,[(e(!0),i(B,null,U(o(P).content,(s,u)=>(e(),i("div",{key:u,class:W(["mb-[15px] mt-4 p-[10px] bg-[#f0f5fe] text-tx-primary",{"pt-[15px]":u>0}]),style:{"border-radius":"0 8px 8px 8px"}},[y(a,{content:s,"line-numbers":!0,"line-clamp":o(_).showContentType==1?o(_).contentNum:0,theme:"light"},null,8,["content","line-clamp"])],2))),128))])])]),n("div",mt,[n("div",ft,[n("div",vt,[n("img",{src:o(w).userInfo.avatar,alt:"",class:"w-[60px] h-[60px] rounded-full"},null,8,yt),n("div",_t,[n("div",gt,x(o(w).userInfo.nickname),1),o(_).showData==1?(e(),i("div",ht,x(o(_).data),1)):d("",!0)])]),n("div",wt,[y(je,{value:o(z),size:85,margin:1},null,8,["value"]),t[2]||(t[2]=n("div",{class:"text-xs mt-2"}," 长按识别二维码 ",-1))])])])],4)],512))]),_:1})),[[R,o(h)]])]),n("div",kt,[y(V,{round:"",onClick:I,loading:o(v)},{icon:r(()=>[y(L,{name:"el-icon-Download"})]),default:r(()=>[t[3]||(t[3]=k(" 下载 "))]),_:1},8,["loading"]),y(V,{round:"",onClick:t[0]||(t[0]=s=>o(j)(o(N)))},{icon:r(()=>[y(L,{name:"el-icon-DocumentCopy"})]),default:r(()=>[t[4]||(t[4]=k(" 复制链接 "))]),_:1})])]),_:1},8,["modelValue"])}}}),bt=Y(xt,[["__scopeId","data-v-4178ddb0"]]),Ct={class:"chat-content"},$t={class:"chat-text"},Et={key:0},It={key:3,class:"flex flex-wrap mx-[-5px]"},At={key:4,class:"flex flex-wrap mx-[-5px]"},Bt={class:"w-[120px] h-[120px] mx-[5px] mt-[10px]"},Pt={key:5,class:"mt-[15px]"},St={class:"line-clamp-1 mr-[10px] text-tx-primary"},Vt={key:0,class:"mt-[10px]"},Dt=S({__name:"content",props:{type:{default:"text"},recordList:{default:()=>[]},content:{default:""},context:{default:()=>[]},quotes:{default:()=>[]},images:{default:()=>[]},videos:{default:()=>[]},files:{default:()=>[]},filesPlugin:{default:()=>[]},typing:{type:Boolean,default:!1},lineNumbers:{type:Boolean,default:!0},showRewrite:{type:Boolean,default:!1},showCopy:{type:Boolean,default:!1},showContext:{type:Boolean,default:!1},showQuote:{type:Boolean,default:!1},showVoice:{type:Boolean,default:!1},showPoster:{type:Boolean,default:!1},recordId:{default:0},index:{default:0},recordType:{default:1},channel:{default:""},userId:{default:""}},emits:["click-custom-link","rewrite"],setup(m,{emit:f}){const l=f;Je();const p=m,v=E(!1),_=E(!1),h=E(!1),w=async()=>(await Re({records_id:p.recordId,content:p.index,type:p.recordType},{Authorization:p.channel,Identity:p.userId})).file,b=G(()=>p.images.map(({url:t})=>t)),{play:P,audioPlaying:T,pause:z,audioLoading:j}=Ve(),{copy:N}=ae(),I=t=>{l("click-custom-link",t)},q=K(),$=async()=>{const t=p.recordList.filter(a=>a.id==p.recordId);if(t.length!=2){se.msgError("上下文数据不对～");return}h.value=!0,await Ie(),q.value.open({title:t[0].content,content:t[1].content})};return(t,a)=>{var u;const M=le,L=ee,V=Pe,D=Se,R=H,s=O;return e(),i("div",Ct,[n("div",$t,[t.filesPlugin.length?(e(),i("div",Et,[(e(!0),i(B,null,U(t.filesPlugin,(c,C)=>(e(),i(B,{key:C},[c.type=="image"?(e(),g(tt,{key:0,url:c.url,name:c.name},null,8,["url","name"])):c.type=="file"?(e(),g(st,{key:1,url:c.url,name:c.name},null,8,["url","name"])):d("",!0)],64))),128))])):d("",!0),t.type==="html"?(e(),g(M,{key:1,content:t.content,"line-numbers":t.lineNumbers,typing:t.typing,style:{color:"inherit"},onClickCustomLink:a[0]||(a[0]=c=>I(c))},null,8,["content","line-numbers","typing"])):t.type==="text"?(e(),i("div",{key:2,class:W(["break-all text-lg",{"wait-typing":t.typing}]),style:{"word-wrap":"break-word"}},x(t.content),3)):d("",!0),o(b).length?(e(),i("div",It,[(e(!0),i(B,null,U(o(b),(c,C)=>(e(),g(L,{key:C,"preview-src-list":o(b),"preview-teleported":!0,infinite:!1,"initial-index":C,"hide-on-click-modal":!0,class:"w-[120px] h-[120px] mx-[5px] mt-[10px]",src:c,fit:"cover"},null,8,["preview-src-list","initial-index","src"]))),128))])):d("",!0),t.videos.length?(e(),i("div",At,[n("div",Bt,[(e(!0),i(B,null,U(t.videos,(c,C)=>(e(),g(V,{"file-size":"120px",key:C,uri:c.url,type:"video"},null,8,["uri"]))),128))])])):d("",!0),(u=t.files)!=null&&u.length?(e(),i("div",Pt,[(e(!0),i(B,null,U(t.files,(c,C)=>(e(),i("div",{key:C,class:"flex mb-[10px] items-center"},[a[10]||(a[10]=n("img",{class:"w-[18px] h-[14px] mr-2",src:We},null,-1)),n("div",St,x(c.name),1),y(D,{href:c.url,target:"_blank",type:"primary"},{default:r(()=>a[9]||(a[9]=[k(" 下载 ")])),_:2},1032,["href"])]))),128))])):d("",!0)]),!t.typing&&(t.showCopy||t.showVoice||t.showQuote||t.showContext)?(e(),i("div",Vt,[t.showRewrite?(e(),g(s,{key:0,link:"",onClick:a[1]||(a[1]=c=>l("rewrite"))},{icon:r(()=>[y(R,{name:"el-icon-RefreshLeft"})]),default:r(()=>[a[11]||(a[11]=k(" 重新回答 "))]),_:1})):d("",!0),t.showCopy?(e(),g(s,{key:1,link:"",onClick:a[2]||(a[2]=c=>o(N)(t.content))},{icon:r(()=>[y(R,{name:"el-icon-CopyDocument"})]),default:r(()=>[a[12]||(a[12]=k(" 复制 "))]),_:1})):d("",!0),t.showVoice?(e(),i(B,{key:2},[o(T)?(e(),g(s,{key:0,link:"",onClick:o(z)},{icon:r(()=>[y(R,{name:"local-icon-audio_voice"})]),default:r(()=>[a[13]||(a[13]=k(" 停止 "))]),_:1},8,["onClick"])):(e(),g(s,{key:1,link:"",loading:o(j),onClick:a[3]||(a[3]=c=>o(P)(w))},{icon:r(()=>[y(R,{name:"local-icon-audio_voice"})]),default:r(()=>[a[14]||(a[14]=k(" 朗读 "))]),_:1},8,["loading"]))],64)):d("",!0),t.showPoster?(e(),g(s,{key:3,link:"",onClick:$},{icon:r(()=>[y(R,{name:"el-icon-Picture"})]),default:r(()=>[a[15]||(a[15]=k(" 生成海报 "))]),_:1})):d("",!0),t.quotes.length&&t.showQuote?(e(),g(s,{key:4,link:"",type:"primary",onClick:a[4]||(a[4]=c=>_.value=!0)},{default:r(()=>[k(x(t.quotes.length)+"条引用 ",1)]),_:1})):d("",!0),t.context.length&&t.showContext?(e(),g(s,{key:5,link:"",type:"primary",onClick:a[5]||(a[5]=c=>v.value=!0)},{default:r(()=>[k(x(t.context.length)+"条上下文 ",1)]),_:1})):d("",!0)])):d("",!0),o(v)?(e(),g(Qe,{key:1,context:t.context,onClose:a[6]||(a[6]=c=>v.value=!1)},null,8,["context"])):d("",!0),o(_)?(e(),g(He,{key:2,quotes:t.quotes,onClose:a[7]||(a[7]=c=>_.value=!1)},null,8,["quotes"])):d("",!0),o(h)?(e(),g(bt,{key:3,ref_key:"posterRef",ref:q,onClose:a[8]||(a[8]=c=>h.value=!1)},null,512)):d("",!0)])}}}),ko=Y(Dt,[["__scopeId","data-v-2b82ead0"]]),Rt=S({props:{showClose:{type:Boolean,default:!0}},emits:["close"],setup(m,{emit:f}){return{handleClose:()=>{f("close")}}}}),Ut={class:"del-wrap"};function jt(m,f,l,p,v,_){const h=H;return e(),i("div",Ut,[F(m.$slots,"default",{},void 0,!0),m.showClose?(e(),i("div",{key:0,class:"icon-close",onClick:f[0]||(f[0]=Ae((...w)=>m.handleClose&&m.handleClose(...w),["stop"]))},[y(h,{size:12,name:"el-icon-CloseBold"})])):d("",!0)])}const qt=Y(Rt,[["render",jt],["__scopeId","data-v-cad697f2"]]),Lt={class:"chat-action"},Tt={key:0,class:"flex items-center pt-[10px] justify-center"},zt={class:"p-[10px]"},Nt={key:0,class:"flex mb-[10px]"},Mt={class:"flex bg-page p-[10px] rounded-[12px] items-center max-w-[400px]"},Wt={class:"line-clamp-2 ml-[10px] flex-1 min-w-0",style:{"word-break":"break-word"}},Ft={class:"mb-[10px] flex flex-wrap items-center"},Gt={key:0,class:"mr-[10px]"},Yt={class:"py-[3px] mr-[-7px] input-suffix"},Qt=S({__name:"index",props:{placeholder:{default:"请输入问题"},loading:{type:Boolean,default:!1},showManual:{type:Boolean,default:!1},showPause:{type:Boolean,default:!0},showClear:{type:Boolean,default:!0},showContinue:{type:Boolean,default:!1},showFileUpload:{type:Boolean,default:!1},menus:{default:()=>[]},btnColor:{default:"#fff"},filePlugin:{default:()=>({})}},emits:["clear","pause","continue","blur","focus","enter","update:filePlugin"],setup(m,{expose:f,emit:l}){const p=m,v=l,_=X(),h=E(""),w=K(),b=K(),P=E(!1),T=()=>{P.value=!1,v("blur")},z=()=>{P.value=!0,v("focus")},j=E(!1),N=G(()=>`${p.placeholder} ${_.isMobile?"":"（Shift + Enter）= 换行"}`),I=we(p,"filePlugin",v),q=E(!1),$=s=>{I.value.url=s.uri,I.value.name=s.name},t=async s=>{q.value=!0;try{return await xe("image",{file:s.file,name:"file",header:{}})}finally{q.value=!1}},a=s=>{if(!(s.shiftKey&&s.keyCode===13)&&!j.value&&s.keyCode===13)return v("enter",h.value),s.preventDefault()},M=G(()=>_.isMobile?{maxRows:4,minRows:1}:{maxRows:6,minRows:1}),L=E(!1),{height:V}=ke(w);let D=0;return Be(()=>{D===0&&(D=V.value),V.value>D&&(L.value=!0),h.value===""&&V.value>D&&(L.value=!1)}),f({setInputValue:(s="")=>{h.value=s},focus:()=>{var s;return(s=b.value)==null?void 0:s.focus()},blur:()=>{var s;return(s=b.value)==null?void 0:s.blur()}}),(s,u)=>{const c=H,C=O,ie=ee,re=qt,ue=O,de=Le,pe=be;return e(),i("div",Lt,[s.showPause||s.showContinue?(e(),i("div",Tt,[s.loading?(e(),g(C,{key:0,plain:"",onClick:u[0]||(u[0]=A=>v("pause"))},{icon:r(()=>[y(c,{name:"el-icon-VideoPause"})]),default:r(()=>[u[8]||(u[8]=k(" 停止 "))]),_:1})):s.showContinue?(e(),g(C,{key:1,plain:"",onClick:u[1]||(u[1]=A=>v("continue"))},{icon:r(()=>[y(c,{name:"el-icon-VideoPlay"})]),default:r(()=>[u[9]||(u[9]=k(" 继续 "))]),_:1})):d("",!0)])):d("",!0),n("div",zt,[o(I).url?(e(),i("div",Nt,[y(re,{onClose:u[2]||(u[2]=A=>o(I).url="")},{default:r(()=>[n("div",Mt,[y(ie,{src:o(I).url,"preview-src-list":[o(I).url],"hide-on-click-modal":!0,class:"w-[35px] h-[35px] flex-none"},null,8,["src","preview-src-list"]),n("span",Wt,x(o(I).name),1)])]),_:1})])):d("",!0),n("div",Ft,[F(s.$slots,"btn",{},void 0,!0),s.showFileUpload?(e(),i("div",Gt,[y(de,{ref:"uploadRef","show-file-list":!1,accept:".jpg,.png,.jpeg",multiple:!1,"on-success":$,"http-request":t},{trigger:r(()=>[y(ue,{plain:"",class:"!rounded-[8px]",loading:o(q)},{icon:r(()=>[y(c,{name:"el-icon-Upload"})]),default:r(()=>[u[10]||(u[10]=k(" 上传图片 "))]),_:1},8,["loading"])]),_:1},512)])):d("",!0),s.showClear?(e(),g(C,{key:1,disabled:s.loading,class:"!rounded-[8px]",plain:"",onClick:u[3]||(u[3]=A=>v("clear"))},{icon:r(()=>[y(c,{name:"el-icon-Delete"})]),default:r(()=>[u[11]||(u[11]=k(" 清空 "))]),_:1},8,["disabled"])):d("",!0),(e(!0),i(B,null,U(s.menus,(A,ce)=>(e(),g(C,{color:s.btnColor,style:{"--el-button-disabled-text-color":`var(
                            --el-button-text-color
                        )`},disabled:s.loading,onClick:Zt=>v("enter",A.keyword),key:ce},{default:r(()=>[k(x(A.keyword),1)]),_:2},1032,["color","disabled","onClick"]))),128))]),n("div",{ref_key:"textContainerRef",ref:w,class:"chat-input relative text-container flex items-center"},[y(pe,{ref_key:"textareaRef",ref:b,modelValue:o(h),"onUpdate:modelValue":u[4]||(u[4]=A=>ne(h)?h.value=A:null),autosize:o(M),type:"textarea",placeholder:o(N),resize:"none",onCompositionstart:u[5]||(u[5]=A=>j.value=!0),onCompositionend:u[6]||(u[6]=A=>j.value=!1),onKeydown:a,onBlur:T,onFocus:z},null,8,["modelValue","autosize","placeholder"]),n("div",Yt,[y(C,{disabled:!o(h)||s.loading,type:"primary",style:{height:"40px"},onClick:u[7]||(u[7]=A=>v("enter",o(h)))},{default:r(()=>[y(c,{name:"el-icon-Promotion",size:20}),u[12]||(u[12]=k(" 发送 "))]),_:1},8,["disabled"])])],512)])])}}}),xo=Y(Qt,[["__scopeId","data-v-e757fefd"]]);export{wo as _,ko as a,xo as b};
