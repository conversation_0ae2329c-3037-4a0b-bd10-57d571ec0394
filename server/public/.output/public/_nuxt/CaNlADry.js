import{E as v}from"./CmRxzTqw.js";import{E as C}from"./ArzC3z2d.js";/* empty css        */import{l as g,b as k,q as b,n as T,M as c,N as S,O as m,V as p,a3 as $,aq as E,a1 as a,ag as d,a5 as r,a0 as y,Z as B}from"./CUZG7cWw.js";import{_ as V}from"./DlAUqK2U.js";const h=g({props:{title:{type:String,default:""},content:{type:String,default:""},confirmButtonText:{type:[String,Boolean],default:"确定"},cancelButtonText:{type:[String,Boolean],default:"取消"},width:{type:String,default:"400px"},disabled:{type:Boolean,default:!1},async:{type:Boolean,default:!1},clickModalClose:{type:Boolean,default:!1},center:{type:Boolean,default:!1},customClass:{type:String,default:""},interceptClose:{type:Boolean,default:!1},appendToBody:{type:Boolean,default:!0}},emits:["confirm","cancel","close","open","interceptClose"],setup(e,{emit:o}){const l=k(!1),f=t=>{o(t),(!e.async||t==="cancel")&&i()},i=()=>{l.value=!1,T(()=>{o("close")})},u=()=>{e.disabled||(o("open"),l.value=!0)},s=t=>{e.interceptClose?o("interceptClose"):t()};return b("visible",l),{visible:l,handleEvent:f,close:i,open:u,handleClose:s}}}),w={class:"dialog"},N={class:"dialog-footer"};function M(e,o,l,f,i,u){const s=v,t=C;return c(),S("div",w,[m("div",{class:"dialog__trigger",onClick:o[0]||(o[0]=(...n)=>e.open&&e.open(...n))},[p(e.$slots,"trigger",{},void 0,!0)]),$(t,{modelValue:e.visible,"onUpdate:modelValue":o[3]||(o[3]=n=>e.visible=n),"custom-class":e.customClass,center:e.center,"append-to-body":e.appendToBody,width:e.width,"close-on-click-modal":e.clickModalClose,"before-close":e.handleClose,onClosed:e.close},E({footer:a(()=>[p(e.$slots,"footer",{},()=>[m("div",N,[e.cancelButtonText?(c(),y(s,{key:0,onClick:o[1]||(o[1]=n=>e.handleEvent("cancel"))},{default:a(()=>[d(r(e.cancelButtonText),1)]),_:1})):B("",!0),e.confirmButtonText?(c(),y(s,{key:1,type:"primary",onClick:o[2]||(o[2]=n=>e.handleEvent("confirm"))},{default:a(()=>[d(r(e.confirmButtonText),1)]),_:1})):B("",!0)])],!0)]),default:a(()=>[p(e.$slots,"default",{},()=>[d(r(e.content),1)],!0)]),_:2},[e.title?{name:"header",fn:a(()=>[d(r(e.title),1)]),key:"0"}:void 0]),1032,["modelValue","custom-class","center","append-to-body","width","close-on-click-modal","before-close","onClosed"])])}const U=V(h,[["render",M],["__scopeId","data-v-0e1cff40"]]);export{U as P};
