import{E as V}from"./ttFW0yUc.js";import{E as y}from"./B7GaOiDz.js";import{h as w,E as C}from"./CmRxzTqw.js";import{_ as D}from"./eFgaMLiC.js";import{_ as N}from"./CbQsrhNE.js";import{E as B}from"./CiabO6Xq.js";import"./DP2rzg_V.js";/* empty css        */import{u as L}from"./DIUf2-0l.js";import{c as I}from"./C6_W4ts7.js";import{l as S,M as s,N as l,a3 as o,a1 as n,u as a,_ as m,O as p,ao as $,Z as F,ag as c,$ as M,a5 as O}from"./CUZG7cWw.js";const j={class:"pt-[10px]"},z={class:"px-[16px]"},P={class:"flex flex-wrap"},T=["onClick"],U={class:"line-clamp-2 ml-[15px]"},ot=S({__name:"digital-config",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:u}){const t=w(_,"modelValue",u),{optionsData:x,refresh:f}=L({digitalLists:{api:I,params:{page_type:0},transformData(r){return r.lists||[]}}}),g=r=>{if(t.value.digital_id==r){t.value.digital_id="";return}t.value.digital_id=r};return(r,i)=>{const v=V,d=y,b=C,h=D,k=N,E=B;return s(),l("div",j,[o(d,{label:"启用形象",prop:"is_digital"},{default:n(()=>[p("div",null,[o(v,{modelValue:a(t).is_digital,"onUpdate:modelValue":i[0]||(i[0]=e=>a(t).is_digital=e),"inline-prompt":"","active-value":1,"inactive-value":0,"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])])]),_:1}),a(t).is_digital?(s(),l(m,{key:0},[o(d,{label:"选择形象",prop:"digital_id"},{default:n(()=>[o(b,{type:"primary",link:"",onClick:a(f)},{default:n(()=>i[1]||(i[1]=[c("刷新")])),_:1},8,["onClick"])]),_:1}),p("div",z,[p("div",P,[o(k,{to:"/application/digital/edit",target:"_blank",class:"flex items-center justify-center p-[15px] border border-br-light border-solid w-[260px] rounded-[10px] h-[80px] mx-[7.5px] mb-[10px]"},{default:n(()=>[o(h,{name:"el-icon-Plus"}),i[2]||(i[2]=c(" 新增形象 "))]),_:1}),(s(!0),l(m,null,$(a(x).digitalLists,e=>(s(),l("div",{class:M(["flex items-center p-[15px] border border-br-light border-solid w-[260px] rounded-[10px] mx-[7.5px] cursor-pointer mb-[15px] h-[80px]",{"!text-primary border-primary bg-primary-light-9":a(t).digital_id==e.id}]),key:e.id,onClick:A=>g(e.id)},[o(E,{class:"w-[50px] h-[50px] rounded-[50%] overflow-hidden border border-solid border-white flex-none",fit:"cover",src:e.avatar},null,8,["src"]),p("div",U,O(e.name),1)],10,T))),128))])])],64)):F("",!0)])}}});export{ot as _};
