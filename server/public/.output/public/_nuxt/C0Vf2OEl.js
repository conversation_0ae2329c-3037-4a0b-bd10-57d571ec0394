import{h as v,e as w,E}from"./CmRxzTqw.js";import{E as y,a as b}from"./B7GaOiDz.js";import{_ as g}from"./eFgaMLiC.js";import{_ as U}from"./D-n7HwjM.js";import{E as B}from"./oVx59syQ.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */import{l as N,b as F,c as I,M as k,N as q,a3 as o,a1 as t,O as a,u as i,y as z,ag as C}from"./CUZG7cWw.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./DlAUqK2U.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./D8e5izeA.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        */const D={class:"manual-import"},M={class:"py-4"},P={class:"flex-1"},h={class:"flex-1"},O={class:"max-w-[600px]"},me=N({__name:"manual",props:{modelValue:{}},emits:["update:modelValue"],setup(d,{emit:_}){const l=v(d,"modelValue",_),n=F([]);return I(n,p=>{l.value.images=p.map(({url:e})=>e)}),(p,e)=>{const r=w,m=y,f=g,u=U,c=E,x=b,V=B;return k(),q("div",D,[o(V,null,{default:t(()=>[a("div",M,[o(x,{"label-width":"0px"},{default:t(()=>[o(m,null,{default:t(()=>[o(r,{modelValue:i(l).question,"onUpdate:modelValue":e[0]||(e[0]=s=>i(l).question=s),placeholder:"请输入问题"},null,8,["modelValue"])]),_:1}),o(m,null,{default:t(()=>[o(r,{modelValue:i(l).answer,"onUpdate:modelValue":e[1]||(e[1]=s=>i(l).answer=s),placeholder:"请输入问题答案，10000个字以内。",type:"textarea",resize:"none",rows:15,maxlength:"10000"},null,8,["modelValue"])]),_:1}),o(m,null,{default:t(()=>[a("div",P,[a("div",null,[o(u,{files:i(n),"onUpdate:files":e[2]||(e[2]=s=>z(n)?n.value=s:null),type:"image","list-type":"picture-card",limit:9,multiple:"","show-file-list":""},{default:t(()=>[o(f,{name:"el-icon-Plus",size:20})]),_:1},8,["files"])]),e[4]||(e[4]=a("div",{class:"form-tips"},"最多支持上传 9 张图",-1))])]),_:1}),o(m,null,{default:t(()=>[a("div",h,[a("div",O,[o(u,{files:i(l).files,"onUpdate:files":e[3]||(e[3]=s=>i(l).files=s),type:"file","show-file-list":""},{tip:t(()=>e[6]||(e[6]=[a("div",{class:"el-upload__tip"}," 支持上传PDF、docx、excel、等文件格式 ",-1)])),default:t(()=>[o(c,null,{default:t(()=>e[5]||(e[5]=[C("上传附件")])),_:1})]),_:1},8,["files"])])])]),_:1})]),_:1})])]),_:1})])}}});export{me as default};
