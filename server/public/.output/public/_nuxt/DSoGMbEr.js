import{_ as m}from"./eFgaMLiC.js";import{b as p,a as f}from"./CmRxzTqw.js";import{l as _,b as v,m as h,M as t,N as k,O as s,V as b,a0 as w,a7 as B,a8 as C,u as a,a3 as O,as as S}from"./CUZG7cWw.js";import{_ as $}from"./DlAUqK2U.js";const N={class:"chat-container"},V=["src"],g=_({__name:"online",setup(x){const n=p(),r=f(),e=v(!1),i=h(()=>n.getOnlineKf),c=()=>{setTimeout(()=>{r.path=="/"&&(e.value=!0)},5e3)};return(l,o)=>{const d=m;return t(),k("div",null,[s("div",{onClick:o[0]||(o[0]=u=>e.value=!0)},[b(l.$slots,"default",{},void 0,!0)]),(t(),w(S,{to:"body"},[B(s("div",N,[s("div",{class:"close-icon",onClick:o[1]||(o[1]=u=>e.value=!1)},[O(d,{name:"el-icon-Close",size:18})]),s("iframe",{width:"100%",height:"100%",border:"none",src:a(i).link,onLoad:c},null,40,V)],512),[[C,a(e)]])]))])}}}),z=$(g,[["__scopeId","data-v-009bd42f"]]);export{z as default};
