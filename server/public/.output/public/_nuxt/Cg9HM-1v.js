import{E as S}from"./YwtsEmdS.js";import{E as P}from"./oVx59syQ.js";import{j as R,b as $,f as j,E as N}from"./CmRxzTqw.js";import{P as V}from"./CaNlADry.js";/* empty css        *//* empty css        */import{u as q}from"./DAOx25wS.js";import{d as z}from"./CRNANWso.js";import{Q as L}from"./CHg9aK2B.js";import{l as T,j as f,b as x,m as v,M as D,N as Q,a3 as s,a1 as r,O as o,u as t,ag as g,a5 as i}from"./CUZG7cWw.js";import{_ as A}from"./DlAUqK2U.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        */const H={class:"share-pop"},M={class:"flex flex-col"},O={class:"flex-1 min-h-0"},U={class:"flex justify-center bg-[#F2F3F6] rounded-t-[10px] overflow-hidden"},W=["src"],G={class:"px-[16px] mt-[12px]"},J={class:"title text-[16px] font-medium text-[#101010] line-clamp-2"},K={class:"px-[15px]"},X={class:"flex items-center px-[16px] pt-[0px] pb-[30px]"},Y={class:"flex items-center"},Z=["src"],oo={class:"ml-2"},to={class:"mt-[16px] font-medium text-[#101010] text-xl"},eo={class:"mt-[10px] text-primary"},so={class:"ml-auto h-[80px] p-[5px] bg-white rounded-[5px]"},no={class:"flex px-[10px]"},ro=T({__name:"posterPop",setup(ao,{expose:h}){const d=f(),m=f(),a=R(),{getWebsiteConfig:b,config:l}=$(),n=x({}),y=p=>{d.value.open(),n.value=p},k=v(()=>`${l.current_domain}/mobile/packages/pages/robot_square/robot_square?type=2?user_sn=${a.userInfo.sn}`),{copy:w}=q(),E=v(()=>`${l.current_domain}/robot_square?type=2&user_sn=${a.userInfo.sn}`),_=x(!1),B=async()=>{try{_.value=!0,await z(m.value)}catch{j.msgError("下载失败，请重试")}finally{_.value=!1}};return h({open:y}),(p,e)=>{const C=S,F=P,u=N,I=V;return D(),Q("div",H,[s(I,{ref_key:"popRef",ref:d,width:"auto",title:"生成海报","append-to-body":!1,"click-modal-close":!0,confirmButtonText:"",cancelButtonText:""},{footer:r(()=>[o("div",no,[s(u,{class:"flex-1",text:"",bg:"",plain:"",size:"large",onClick:e[0]||(e[0]=c=>t(w)(t(E)))},{default:r(()=>e[1]||(e[1]=[g(" 复制链接 ")])),_:1}),s(u,{class:"flex-1",type:"primary",size:"large",onClick:B},{default:r(()=>e[2]||(e[2]=[g(" 下载海报 ")])),_:1})])]),default:r(()=>[o("div",M,[o("div",O,[s(F,null,{default:r(()=>{var c;return[o("div",{ref_key:"posterRef",ref:m,class:"w-[430px] bg-[#F8F8FB] rounded-[10px]"},[o("div",U,[o("img",{class:"w-full object-contain",src:t(n).thumbnail||t(n).image,alt:""},null,8,W)]),o("div",G,[o("div",J,i(((c=t(n))==null?void 0:c.prompts_cn)||t(n).original_prompts.prompt),1)]),o("div",K,[s(C,{"border-style":"solid"})]),o("div",X,[o("div",null,[o("div",Y,[o("img",{class:"w-[45px] h-[45px] rounded-full",src:t(a).userInfo.avatar},null,8,Z),o("div",oo,i(t(a).userInfo.nickname),1)]),o("div",to,i(t(b).pc_title),1),o("div",eo,i(t(l).current_domain),1)]),o("div",so,[s(L,{value:t(k),size:80,margin:1},null,8,["value"])])])],512)]}),_:1})])])]),_:1},512)])}}}),Eo=A(ro,[["__scopeId","data-v-90c13495"]]);export{Eo as default};
