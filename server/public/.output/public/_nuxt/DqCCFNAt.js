import{l as oe,j as P,r as z,b as L,ai as j,M as i,N as d,a3 as r,a1 as m,O as s,a7 as q,u as n,a0 as E,Z as _,a8 as se,aG as ae,_ as R,ao as ne,$ as A,a5 as b,a4 as re,ag as ie,aH as le}from"./CUZG7cWw.js";import{_ as ce}from"./eFgaMLiC.js";import{E as pe}from"./Zz2DnF66.js";import{E as de}from"./DluKwKHO.js";import{W as me}from"./BZBRZdpQ.js";import{l as ue,j as _e,cF as fe,cZ as ge,H as we,E as ye,bC as N,ct as ve}from"./CmRxzTqw.js";import{E as xe,a as he}from"./CiabO6Xq.js";import{E as ke}from"./C9jirCEY.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{u as W}from"./DNOp0HuO.js";import{a as Ce,d as Pe}from"./CJgd20ip.js";import{a as Ee}from"./DjwCd26w.js";import{e as be}from"./BhXe-NXN.js";import{I as Se}from"./GytdR_nJ.js";import Ve from"./Cg9HM-1v.js";import{_ as Be}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./YwtsEmdS.js";import"./oVx59syQ.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        *//* empty css        *//* empty css        */import"./DAOx25wS.js";import"./CRNANWso.js";import"./CHg9aK2B.js";const Ie={class:"flex-1 min-h-0 mx-[16px] relative"},$e=["onClick"],De={class:"flex-1 min-h-[70vh] overflow-hidden mx-auto"},ze=["infinite-scroll-disabled"],Le={class:"image-payload h-full w-full relative text-sm"},je={class:"image-bg"},qe=["onClick"],Re=["onClick"],Ae={class:"text-center leading-[38px]"},Ne={class:"image-content"},We=["onClick"],Fe={class:"flex justify-between mt-[10px]"},Oe={class:"flex items-center"},Te={class:"text-[#BBBBBB] ml-[6px] w-[80px] truncate"},Me=["onClick"],He={key:1,class:"flex justify-center items-center mt-[50px]"},Ze={class:"flex flex-col justify-center items-center w-full h-[60vh]"},Ge=oe({__name:"draw",props:{keyword:{}},async setup(F){let l,f;const O=F;ue();const g=_e(),S=P(null),V=P(null),c=z({page_no:0,page_size:20,keyword:"",category_id:""}),T={4e3:{rowPerView:7},2e3:{rowPerView:6},1800:{rowPerView:5},1600:{rowPerView:5},1440:{rowPerView:4},1360:{rowPerView:4},1280:{rowPerView:4},1024:{rowPerView:3}},a=z({more:!0,count:0,loading:!1,lists:[]}),B=L(0),w=L([]),{data:I}=([l,f]=j(()=>W(()=>Ee({type:ge.DRAW}),{default(){return[]},transform(t){return[{id:"",name:"全部"}].concat(t)},lazy:!0},"$SQjM9OqPT4")),l=await l,f(),l);[l,f]=j(()=>W(()=>h(),{lazy:!0},"$E2KxCY8C7W")),await l,f();const h=async()=>{if(!a.loading){if(a.more)c.page_no+=1;else return;a.loading=!0;try{const t=await Ce(c),{lists:o,page_no:v,page_size:k,count:x}=t;v*k>x&&(a.more=!1),v==1?a.lists=o:a.lists=[...a.lists,...o]}finally{setTimeout(()=>a.loading=!1,200)}}},y=()=>{c.page_no=0,a.more=!0,h()},M=async t=>{if(!g.isLogin){g.toggleShowLogin(!0);return}await Pe({records_id:t.id,status:t.is_collect?0:1}),c.category_id===0?y():t.is_collect=t.is_collect?0:1},H=t=>{if(!g.isLogin){g.toggleShowLogin(!0);return}V.value.open(t)},Z=P(),G=t=>{Z.value=t,console.log(t)},$=t=>{var o;B.value=t,c.category_id=(o=I.value[t])==null?void 0:o.id,y()};return $(0),fe(()=>O.keyword,t=>{c.keyword=t,y()},{debounce:500}),(t,o)=>{const v=le,k=ae,x=ce,K=pe,Q=de,U=me,Y=we,J=xe,X=ye,ee=he,te=ke;return i(),d("div",Ie,[r(k,{slidesPerView:"auto",spaceBetween:16,class:"category-lists",onSwiper:G,style:{padding:"10px 0"}},{default:m(()=>[(i(!0),d(R,null,ne(n(I),(e,u)=>(i(),E(v,{key:e.id,style:{width:"auto","margin-right":"12px"}},{default:m(()=>[Object.keys(e).includes("name")?(i(),d("div",{key:0,class:A(["category-item bg-white",{"is-active":n(B)===u}]),onClick:C=>$(u)},b(e.name),11,$e)):_("",!0)]),_:2},1024))),128))]),_:1}),s("div",De,[q((i(),d("div",{class:"model-lists mb-[10px] mx-[0px]","infinite-scroll-distance":"50","infinite-scroll-delay":200,"infinite-scroll-disabled":!n(a).more},[n(a).lists.length?(i(),E(U,{key:0,ref_key:"waterFull",ref:S,delay:100,list:n(a).lists,width:305,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:T},{item:m(({item:e})=>{var u,C;return[s("div",Le,[s("div",je,[r(Se,{thumbnail:e.thumbnail,image:(e==null?void 0:e.image)||(e==null?void 0:e.image_url),onRefresh:o[0]||(o[0]=p=>{var D;return(D=n(S))==null?void 0:D.renderer()}),onOnClick:o[1]||(o[1]=p=>w.value=p)},null,8,["thumbnail","image"])]),s("div",{class:"image-praise relative",onClick:p=>M(e)},[s("div",{class:A(["praise-animate",e.is_collect?"praise-entry":"praise-leave"])},null,2)],8,qe),r(K,{effect:"dark",content:"生成海报",placement:"top"},{default:m(()=>[s("div",{class:"image-poster relative",onClick:re(p=>H(e),["stop"])},[s("div",Ae,[r(x,{size:"16px",color:"#ffffff",name:"el-icon-Picture"})])],8,Re)]),_:2},1024),s("div",Ne,[s("p",{class:"text-white line-clamp-2",onClick:p=>("copy"in t?t.copy:n(N))(e.prompt)},b((e==null?void 0:e.prompts_cn)||(e==null?void 0:e.original_prompts.prompt)),9,We),s("div",Fe,[s("div",Oe,[(u=e==null?void 0:e.user_info)!=null&&u.image?(i(),d(R,{key:0},[r(Q,{size:28,src:(C=e==null?void 0:e.user_info)==null?void 0:C.image},null,8,["src"]),s("p",Te,b(e.user_info.name),1)],64)):_("",!0)]),s("div",{class:"flex items-center",onClick:p=>("copy"in t?t.copy:n(N))(e.prompts)},[r(x,{name:"el-icon-Copy"}),o[3]||(o[3]=s("p",{class:"text-white ml-[6px]"}," 复制 ",-1))],8,Me)])])])]}),_:1},8,["list"])):_("",!0),n(a).loading?(i(),d("div",He,[r(Y,{size:"25",class:"is-loading"},{default:m(()=>[r(n(ve))]),_:1}),o[4]||(o[4]=s("span",{class:"mt-[4px] ml-[10px] text-[#999999]"},"加载中...",-1))])):_("",!0),q(s("div",Ze,[r(J,{class:"w-[200px] h-[200px]",src:n(be)},null,8,["src"]),o[6]||(o[6]=s("div",{class:"text-tx-regular mb-4"},"当前选择暂无绘画～",-1)),r(X,{type:"primary",onClick:y},{default:m(()=>o[5]||(o[5]=[ie(" 点击刷新")])),_:1})],512),[[se,!n(a).lists.length&&!n(a).loading]])],8,ze)),[[te,h]]),r(Ve,{ref_key:"posterPopupRef",ref:V},null,512),n(w).length?(i(),E(ee,{key:0,"url-list":n(w),"hide-on-click-modal":!0,onClose:o[2]||(o[2]=e=>w.value=[])},null,8,["url-list"])):_("",!0)])])}}}),Bt=Be(Ge,[["__scopeId","data-v-c481d2de"]]);export{Bt as default};
