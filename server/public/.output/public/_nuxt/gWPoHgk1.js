import{E as c}from"./oVx59syQ.js";import{E as g}from"./CiabO6Xq.js";import{E as x}from"./CUKNHy7a.js";import{b as V,v as w}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{r as y,x as D,y as b,f as m,e as k}from"./-CaxLuW0.js";import{DrawModeEnum as p,DrawTypeEnum as v}from"./tONJIxwY.js";import{_ as E}from"./DZlk7wKD.js";import O from"./BE2FalaX.js";import U from"./BHa1u2_-.js";import B from"./DDAu8SNr.js";import{_ as A}from"./B6SOVFzv.js";import{D as S}from"./CoT3rpTv.js";import z from"./njwEk9xo.js";import{_ as N}from"./DNrEDrFp.js";import C from"./ME-mEWUT.js";import{l as L,E as M,u as o,M as a,N as l,a3 as r,a1 as i,a7 as P,O as s,a0 as $,Z as I}from"./CUZG7cWw.js";import{_ as R}from"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./D5Svi-lq.js";import"./eFgaMLiC.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CXAJ--Vj.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";/* empty css        */import"./8fUKBaFv.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./xixvWuCN.js";import"./DCzKTodP.js";import"./CfDE0MAs.js";import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./DJi8L2lq.js";import"./C-n0m2hZ.js";import"./CXDY_LVT.js";import"./7tQUKVT9.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";/* empty css        *//* empty css        */import"./Dg_4RlNK.js";import"./CRNANWso.js";import"./Cm9Fkrh-.js";import"./DjwCd26w.js";import"./CaNlADry.js";import"./Bj_9-7Jh.js";import"./f66KDjIM.js";import"./TR-GuQrR.js";import"./BWdDF8rn.js";import"./C3s9J3qB.js";import"./DYjlFFbo.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./Bf_xRNbS.js";import"./BOx_5T3X.js";const T={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},j={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},F={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},Z=L({__name:"doubao",setup(q){const n=V();return M(()=>{y({draw_api:p.DOUBAO,draw_model:p.DOUBAO,action:"generate",prompt:"",engine:"high_aes_general_v20_L",negative_prompt:"",size:"512x512",complex_params:{seed:"",ddim_steps:20}}),D.model=p.DOUBAO,b()}),(G,t)=>{const d=c,u=g,_=x,f=w;return o(n).config.switch.doubao_status?(a(),l("div",T,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:i(()=>[s("div",j,[r(E,{modelValue:o(m).draw_type,"onUpdate:modelValue":t[0]||(t[0]=e=>o(m).draw_type=e)},null,8,["modelValue"]),r(O,{modelValue:o(m).prompt,"onUpdate:modelValue":t[1]||(t[1]=e=>o(m).prompt=e),model:o(p).DOUBAO},null,8,["modelValue","model"]),o(m).draw_type===o(v).img2img?(a(),$(U,{key:0,modelValue:o(m).image_mask,"onUpdate:modelValue":t[2]||(t[2]=e=>o(m).image_mask=e),type:"image"},null,8,["modelValue"])):I("",!0),r(z,{modelValue:o(m).size,"onUpdate:modelValue":t[3]||(t[3]=e=>o(m).size=e)},null,8,["modelValue"]),r(N,{modelValue:o(m).engine,"onUpdate:modelValue":t[4]||(t[4]=e=>o(m).engine=e),draw_type:o(m).draw_type},null,8,["modelValue","draw_type"]),r(C,{modelValue:o(m).complex_params,"onUpdate:modelValue":t[5]||(t[5]=e=>o(m).complex_params=e)},null,8,["modelValue"])]),r(A)]),_:1}),P(r(B,{"element-loading-text":"正在加载数据..."},null,512),[[f,o(k)]])])):(a(),l("div",F,[r(_,null,{icon:i(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(S)},null,8,["src"])]),title:i(()=>t[6]||(t[6]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),ft=R(Z,[["__scopeId","data-v-a401c716"]]);export{ft as default};
