import{j as P,b as r,o as w}from"./CUZG7cWw.js";const c=new Set,h=d=>{const{onstart:s,onstop:o,onerror:l}=d||{},e=P(null),n=r(!1),u=r(!1),y=async(a,m=!0)=>{var f;let v;if(typeof a=="string"?v=()=>a:v=a,t(),e.value||g(),m&&((f=e.value)!=null&&f.src)){e.value.play();return}n.value=!0;try{const i=await Promise.resolve(v());e.value.src=i,e.value.play()}catch(i){console.error(i),l==null||l()}finally{n.value=!1}},p=()=>{var a;(a=e.value)==null||a.pause(),u.value=!1},t=()=>{c.forEach(a=>{a.pause(),a.currentTime=0,a.audioPlaying.value=!1})},g=()=>{e.value=new Audio,e.value.audioPlaying=u,c.add(e.value),e.value.onplay=()=>{s==null||s(),u.value=!0},e.value.onended=()=>{o==null||o(),u.value=!1},e.value.onerror=()=>{l==null||l(),u.value=!1}};return w(()=>{var a;(a=e.value)!=null&&a.src&&t(),e.value&&(c.delete(e.value),e.value=null)}),{play:y,audioLoading:n,audioPlaying:u,pause:p,pauseAll:t}};export{h as u};
