import{E as S,a as B}from"./DHUC3PVh.js";import{b as T,a as L}from"./C9f7n97H.js";import{_ as N}from"./Bv29pan0.js";import{E as A}from"./CiabO6Xq.js";import{E as D}from"./llRQJmEG.js";import{E as U}from"./oVx59syQ.js";import{cT as z}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        */import{u as P}from"./DNOp0HuO.js";import{u as R,I as $}from"./DymDsCmz.js";import{e as j}from"./BhXe-NXN.js";import{l as F,r as G,ai as O,m as W,M as e,N as s,O as o,a3 as l,a1 as r,u as a,_,ao as d,a0 as y,ag as q,a5 as H}from"./CUZG7cWw.js";import{_ as J}from"./DlAUqK2U.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./eFgaMLiC.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./DoCT-qbH.js";import"./CiYvFM4x.js";import"./DY7CbrCZ.js";import"./Bfmn7p7A.js";const K={class:"maps-select h-full flex flex-col"},Q={class:"mt-[5px] px-main"},X={class:"flex-1 min-h-0"},Y={class:"p-main pt-0"},Z={key:0,class:"flex flex-wrap mx-[-7px]"},tt=["onClick"],et={class:"bg-[#101010] rounded-md cursor-pointer"},ot={class:"pic-wrap h-0 pt-[100%] relative"},at={class:"absolute inset-0"},st=F({__name:"maps",async setup(lt){let p,u;const b=R(),g=[{type:1,label:"系统贴图"}],n=G({type:1,index:0}),{data:f}=([p,u]=O(()=>P(()=>z(),{lazy:!0},"$Ult9WlnVjn")),p=await p,u(),p),x=W(()=>f.value[n.index].decals||[]),v=i=>{b.addImage(i.url,$.MAPS,i)};return(i,m)=>{const E=B,h=S,k=T,V=L,w=N,C=A,I=D,M=U;return e(),s("div",K,[o("div",Q,[l(h,{modelValue:a(n).type,"onUpdate:modelValue":m[0]||(m[0]=t=>a(n).type=t)},{default:r(()=>[(e(),s(_,null,d(g,t=>l(E,{key:t.type,label:t.label,name:t.type},null,8,["label","name"])),64))]),_:1},8,["modelValue"])]),o("div",X,[l(M,null,{default:r(()=>[o("div",Y,[l(w,{class:"mb-[10px] mt-[-5px]","default-height":42},{default:r(()=>[l(V,{modelValue:a(n).index,"onUpdate:modelValue":m[1]||(m[1]=t=>a(n).index=t),class:"el-radio-group-margin"},{default:r(()=>[(e(!0),s(_,null,d(a(f),(t,c)=>(e(),y(k,{key:c,label:c},{default:r(()=>[q(H(t.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(x).length?(e(),s("div",Z,[(e(!0),s(_,null,d(a(x),t=>(e(),s("div",{key:t.id,class:"w-[33.33%]"},[o("div",{class:"px-[7px] mb-[14px]",onClick:c=>v(t)},[o("div",et,[o("div",null,[o("div",ot,[o("div",at,[l(C,{src:t.url,class:"w-full h-full",fit:"contain",lazy:""},null,8,["src"])])])])])],8,tt)]))),128))])):(e(),y(I,{key:1,image:a(j),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}}),Tt=J(st,[["__scopeId","data-v-4e32725f"]]);export{Tt as default};
