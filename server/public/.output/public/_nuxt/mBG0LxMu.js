import{_ as c}from"./CbQsrhNE.js";import{k as r,c$ as l}from"./CmRxzTqw.js";import{i as m}from"./DecTOTC8.js";import{l as u,m as n,M as _,a0 as f,a1 as x,V as k,W as d,u as a}from"./CUZG7cWw.js";const E=u({__name:"index",props:{to:{}},setup(s){const t=s,e=n(()=>{let o="";return m(t.to)?o=t.to:r(t.to)&&(o=t.to.path),l(o)}),i=n(()=>e.value?{...t,target:"blank",to:r(t.to)?t.to.path:t.to}:t);return(o,h)=>{const p=c;return _(),f(p,d(a(i),{external:a(e)}),{default:x(()=>[k(o.$slots,"default")]),_:3},16,["external"])}}});export{E as _};
