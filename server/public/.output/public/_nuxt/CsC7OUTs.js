import{E as H}from"./D5Svi-lq.js";import{_ as J}from"./D3znQkH1.js";import{P as X,_ as Z,p as G,b as K,a as Q}from"./C9pcNxkS.js";import{E as Y}from"./oVx59syQ.js";import{l as ee,b as te,j as se,dd as oe,de as ae,df as C,E as ne,f as S,B as le}from"./CmRxzTqw.js";/* empty css        */import{u as ie}from"./DNOp0HuO.js";import{u as re}from"./Bj_9-7Jh.js";import{m as de,a as ce}from"./DXdf2lbU.js";import{l as pe,ai as me,b as w,m as j,E as ue,M as a,N as n,a3 as d,a1 as E,u as o,O as e,ag as A,Z as f,a5 as l,y as B,_ as N,ao as U,$ as L,a6 as xe}from"./CUZG7cWw.js";import{_ as _e}from"./DlAUqK2U.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./eFgaMLiC.js";/* empty css        */import"./DqGsTvs3.js";const fe={class:"relative flex flex-col min-h-0 h-full"},ve={class:"grid grid-cols-3 md:grid-cols-3 gap-4 h-[150px] bg-[#EEF2FF] py-[20px] rounded-[12px] flex-none dark:bg-body"},ge={class:"flex flex-col pl-[30px] items-start justify-center"},be={class:"flex flex-col items-center"},ye=["src"],he={class:"mt-[10px] text-sm"},ke={class:"flex flex-col items-center justify-center"},we={class:"font-medium text-[18px] text-[#0256FF]"},Ee={class:"flex flex-col items-end justify-center pr-[30px]"},Fe={class:"flex flex-col items-center"},Ie={class:"font-medium text-[18px] text-[#0256FF]"},Ce={key:0},Se={key:0,class:"h-full bg-[#EEF2FF] mt-[16px] rounded-[12px] dark:bg-body"},Ae={class:"flex pt-4 ml-4 flex-none"},Ve={class:"flex flex-col items-center gap-1 p-2"},ze={class:"text-xl"},De={class:"text-xs text-tx-secondary tabs-active_desc"},Pe={class:"px-[26px] pb-[120px] bg-[#EEF2FF] dark:bg-[#1D2025]"},je={class:"flex flex-col mt-4"},Be={class:"member-lists flex flex-wrap"},Ne=["onClick"],Ue={key:0,class:"absolute top-[-8px] left-[-1px] bg-[#FF7272] px-[12px] py-[2px] text-white rounded-tl-[10px] rounded-br-[10px] text-xs"},Le={class:"flex flex-col items-center"},Me={class:"text-xl font-medium mb-[10px] line-clamp-1"},We={class:"border-t border-solid border-br-light pt-[10px]"},$e={class:"flex justify-between"},Re={class:"flex flex-1"},Oe=["src"],Te={class:"text-tx-primary ml-[10px] line-clamp-1"},qe={class:"text-primary flex-none ml-2"},He={key:0,class:"pb-[8px]"},Je={key:1},Xe={class:"mt-[10px]"},Ze={key:1,class:"w-full h-full bg-[#EEF2FF] mt-[16px] p-[26px] rounded-[12px] flex items-center justify-center"},Ge={key:0,class:"absolute left-0 bottom-0 w-full px-[26px]"},Ke={class:"mt-[40px] flex justify-between rounded-[12px] bg-white py-[15px] px-[20px] dark:bg-page"},Qe=pe({__name:"member",async setup(Ye){let v,V;const M=ee(),F=te(),c=se(),W=le(),{data:g}=([v,V]=me(()=>ie(()=>de(),{default(){return[]},lazy:!0},"$eeO01m0hJa")),v=await v,V(),v);w("");const p=w(X.WECHAT),m=w(-1),r=w(-1),z={1:"天",2:"个月"},b=j(()=>{var i;if(m.value===-1&&((i=g.value)!=null&&i.length)){const t=g.value.findIndex(u=>u.is_recommend);m.value=t!==-1?t:0}return g.value[m.value]}),y=j(()=>{var i,t,u,h;if(r.value===-1&&((t=(i=b.value)==null?void 0:i.price_list)!=null&&t.length)){const x=((u=b.value)==null?void 0:u.price_list.findIndex(I=>I.is_recommend))||0;r.value=x!==-1?x:0}return((h=b.value)==null?void 0:h.price_list)||[]}),{lockFn:$,isLock:R}=re(async()=>{y.value[r.value].id||S.msgError("请选择会员套餐"),p.value||S.msgError("请选择支付方式");const i=await ce({member_price_id:y.value[r.value].id}),t=await G({...i,pay_way:p.value,redirect:`${W.app.baseURL}user/record?type=member`,code:C.getAuthData().code});await K.run({payWay:p.value,orderId:i.order_id,from:i.from,config:t.config}),await c.getUser(),await S.alertSuccess("支付成功"),M.push({path:"/user/record",query:{id:i.order_id,type:"member"}})});return ue(async()=>{oe()==ae.WEIXIN_OA&&C.getAuthData().code==""&&await C.getUrl()}),(i,t)=>{var D;const u=H,h=J,x=Z,I=Q,O=Y,T=ne;return a(),n("div",fe,[d(O,{class:"scrollbar rounded-[12px]"},{default:E(()=>{var P;return[e("div",ve,[e("div",ge,[e("div",be,[e("img",{src:o(c).userInfo.avatar,class:"w-[64px] h-[64px] rounded-full"},null,8,ye),e("div",he," 用户ID: "+l(o(c).userInfo.sn),1)])]),e("div",ke,[e("div",we,l(o(c).userInfo.package_name||"-"),1),t[2]||(t[2]=e("div",{class:"mt-[4px] text-[16px]"},"当前等级",-1))]),e("div",Ee,[e("div",Fe,[e("div",Ie,[A(l(o(c).userInfo.package_time||"-")+" ",1),(P=o(c).userInfo)!=null&&P.package_is_overdue?(a(),n("span",Ce,"(已到期)")):f("",!0)]),t[3]||(t[3]=e("div",{class:"mt-[4px] text-[16px]"},"有效期",-1))])])]),o(F).getIsShowMember?(a(),n("div",Se,[e("div",Ae,[d(h,null,{default:E(()=>[d(u,{modelValue:o(m),"onUpdate:modelValue":t[0]||(t[0]=s=>B(m)?m.value=s:null),block:!1,class:"segmented !p-[8px] h-[70px] !rounded-[12px] !bg-white dark:!bg-page",options:o(g).map((s,_)=>({name:s.name,value:_,desc:s.describe}))},{default:E(({item:s,index:_})=>[e("div",Ve,[e("div",ze,l(s.name),1),e("div",De,l(s.desc),1)])]),_:1},8,["modelValue","options"])]),_:1})]),e("div",Pe,[e("div",je,[t[6]||(t[6]=e("div",{class:"text-2xl font-medium"},"选择套餐",-1)),e("div",Be,[(a(!0),n(N,null,U(o(y),(s,_)=>(a(),n("div",{key:s.id,class:L(["member-item relative",{active:_===o(r)}]),onClick:k=>r.value=_},[s.tags!=""?(a(),n("div",Ue,l(s.tags),1)):f("",!0),e("div",null,[e("div",Le,[e("div",Me,l(z[s.duration_type]?s.duration+z[s.duration_type]:"永久"),1),d(x,{content:s.sell_price,"main-size":"28px","minor-size":"16px"},null,8,["content"]),e("div",{class:L([{"opacity-0":s.lineation_price==="0.00"},"mb-[20px]"])},[d(x,{prefix:"原价",content:s.lineation_price,"main-size":"14px","line-through":"",color:"#999"},null,8,["content"])],2)]),e("div",We,[t[5]||(t[5]=e("div",{class:"font-medium text-xl pt-1"}," 会员权益 ",-1)),(a(!0),n(N,null,U(o(b).benefits_list,(k,q)=>(a(),n("div",{class:"text-base py-[8px]",key:q},[e("div",$e,[e("div",Re,[e("img",{src:k.image,class:"w-[18px] h-[18px]"},null,8,Oe),e("span",Te,l(k.name),1)]),e("div",qe,l(k.describe),1)])]))),128)),s.is_give?(a(),n("div",{key:0,class:"text-base mt-2 p-[8px] bg-[#f7f7f7] rounded-[12px] dark:!bg-[#2d2d2d]",style:xe({background:_===o(r)?"linear-gradient(90deg, rgba(179, 217, 242, 0.5) 0%, rgba(159, 181, 249, 0.5) 100%)":"#f7f7f7"})},[s.give_balance!=0?(a(),n("div",He,[e("span",null,"赠送"+l(o(F).getTokenUnit)+"：",1),e("span",null,l(s.give_balance),1)])):f("",!0),s.give_robot!=0?(a(),n("div",Je,[t[4]||(t[4]=e("span",null,"赠送智能体：",-1)),e("span",null,l(s.give_robot),1)])):f("",!0)],4)):f("",!0)])])],10,Ne))),128))])]),e("div",Xe,[t[7]||(t[7]=e("div",{class:"text-2xl font-medium mb-[5px]"}," 支付方式 ",-1)),d(I,{modelValue:o(p),"onUpdate:modelValue":t[1]||(t[1]=s=>B(p)?p.value=s:null),from:"recharge"},null,8,["modelValue"])])])])):(a(),n("div",Ze,t[8]||(t[8]=[e("div",{class:"text-xl"},"功能未开启!",-1)])))]}),_:1}),o(F).getIsShowMember?(a(),n("div",Ge,[e("div",Ke,[e("div",null,[t[9]||(t[9]=A(" 实付金额： ")),d(x,{content:(D=o(y)[o(r)])==null?void 0:D.sell_price,"main-size":"24px","minor-size":"14px",color:"#FF7021"},null,8,["content"])]),d(T,{type:"primary",size:"large",loading:o(R),onClick:o($),style:{border:"none","border-radius":"6px",padding:"0 54px"}},{default:E(()=>t[10]||(t[10]=[A(" 立即购买 ")])),_:1},8,["loading","onClick"])])])):f("",!0)])}}}),gt=_e(Qe,[["__scopeId","data-v-cd9bd32c"]]);export{gt as default};
