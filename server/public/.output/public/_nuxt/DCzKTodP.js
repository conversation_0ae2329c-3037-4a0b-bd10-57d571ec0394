import{l as v,m as N,M as n,N as T,O as t,V as f,$ as o,u as a,a0 as c,a1 as r,a3 as k,a4 as g,Z as y,a6 as C,a9 as M}from"./CUZG7cWw.js";import{I as w,P as V,Q as $,J as I,R as b,H as h,M as P,N as F}from"./CmRxzTqw.js";const H=w({type:{type:String,values:["primary","success","info","warning","danger"],default:"primary"},closable:Boolean,disableTransitions:<PERSON><PERSON>an,hit:Boolean,color:String,size:{type:String,values:V},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),J={close:l=>l instanceof MouseEvent,click:l=>l instanceof MouseEvent},K=v({name:"ElTag"}),O=v({...K,props:H,emits:J,setup(l,{emit:i}){const S=l,_=$(),s=I("tag"),u=N(()=>{const{type:e,hit:m,effect:B,closable:E,round:z}=S;return[s.b(),s.is("closable",E),s.m(e||"primary"),s.m(_.value),s.m(B),s.is("hit",m),s.is("round",z)]}),p=e=>{i("close",e)},d=e=>{i("click",e)};return(e,m)=>e.disableTransitions?(n(),T("span",{key:0,class:o(a(u)),style:C({backgroundColor:e.color}),onClick:d},[t("span",{class:o(a(s).e("content"))},[f(e.$slots,"default")],2),e.closable?(n(),c(a(h),{key:0,class:o(a(s).e("close")),onClick:g(p,["stop"])},{default:r(()=>[k(a(b))]),_:1},8,["class","onClick"])):y("v-if",!0)],6)):(n(),c(M,{key:1,name:`${a(s).namespace.value}-zoom-in-center`,appear:""},{default:r(()=>[t("span",{class:o(a(u)),style:C({backgroundColor:e.color}),onClick:d},[t("span",{class:o(a(s).e("content"))},[f(e.$slots,"default")],2),e.closable?(n(),c(a(h),{key:0,class:o(a(s).e("close")),onClick:g(p,["stop"])},{default:r(()=>[k(a(b))]),_:1},8,["class","onClick"])):y("v-if",!0)],6)]),_:3},8,["name"]))}});var Q=P(O,[["__file","tag.vue"]]);const j=F(Q);export{j as E,H as t};
