import{E as g}from"./CiabO6Xq.js";import{E as w}from"./CUKNHy7a.js";import{a as v,_ as B}from"./CmRxzTqw.js";/* empty css        */import{u as E}from"./DNOp0HuO.js";import N from"./uUwEDNGk.js";import{_ as C}from"./-NFm30sI.js";import{useSearch as S}from"./CaJo29OT.js";import{e as A}from"./BhXe-NXN.js";import{l as I,ai as R,c as j,M as t,N as r,a3 as m,a1 as i,u as e,_ as V,a0 as c,O as $}from"./CUZG7cWw.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./oVx59syQ.js";/* empty css        */import"./ByaJQqbe.js";import"./eFgaMLiC.js";import"./DlAUqK2U.js";import"./COoKzhde.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./D5Svi-lq.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";import"./DCzKTodP.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./P8Qw-ZvZ.js";import"./DwRn548t.js";import"./BLV0QRdm.js";import"./zsmLP2wC.js";import"./DIux4E1M.js";import"./Cq2NhlyP.js";import"./DecTOTC8.js";import"./DjGGZNxA.js";import"./llRQJmEG.js";import"./C9jirCEY.js";import"./Bj_9-7Jh.js";import"./Dn6zauaO.js";import"./CH6wv3Pu.js";import"./C5MicItF.js";import"./Dx5ik0L8.js";import"./Dbi96Hzd.js";import"./CWhfTipS.js";import"./C-n0m2hZ.js";import"./CXDY_LVT.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";/* empty css        */import"./BImPoEE8.js";import"./PY3-mrub.js";import"./Bf_xRNbS.js";import"./BOx_5T3X.js";import"./BDENRpCP.js";import"./hEcOa1S-.js";import"./BMDjbVzV.js";import"./BDOG4R2G.js";import"./HA5sEeDs.js";/* empty css        */const q={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Pt=I({__name:"index",async setup(z){let p,s;const l=v(),{showSearchResult:a,config:u,getConfig:_,getSearchInfo:f,options:d,result:y}=S();return[p,s]=R(()=>E(()=>_(),"$mZBhG8hzNj")),await p,s(),j(()=>l.query.id,o=>{o?y.value.id<0&&f(o):(d.value.ask="",a.value=!1)},{immediate:!0}),(o,n)=>{const h=g,x=w,k=B;return t(),r("div",null,[m(k,{name:"default"},{default:i(()=>[e(u).status>0?(t(),r(V,{key:0},[e(a)?(t(),c(C,{key:0})):(t(),c(N,{key:1}))],64)):(t(),r("div",q,[m(x,null,{icon:i(()=>[m(h,{class:"w-[150px] dark:opacity-60",src:e(A)},null,8,["src"])]),title:i(()=>n[0]||(n[0]=[$("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Pt as default};
