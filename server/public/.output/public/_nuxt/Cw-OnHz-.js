import{E as M,a as U}from"./CXDY_LVT.js";import{E as w}from"./B7GaOiDz.js";import{E as z}from"./06MVqVCl.js";import{E as B}from"./CXsrG8JM.js";import{b as N,cA as I,f as p,e as L}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import{F as O}from"./Bfmn7p7A.js";import{l as A,M as i,N as V,a3 as l,a1 as s,u as t,_ as P,ao as R,a0 as j,y as r,O as v}from"./CUZG7cWw.js";const q={class:"flex flex-1"},D={class:"flex flex-1"},ee=A({__name:"text-setting",props:{font:{},fontSize:{},fontColor:{},strokeColor:{}},emits:["update:font","update:fontSize","update:fontColor","update:strokeColor"],setup(x,{emit:C}){const E=N(),b=x,k=C,{font:f,fontSize:m,fontColor:n,strokeColor:a}=I(b,k),g=async d=>{p.loading("正在加载字体中，请稍等...");try{await new O(d).load(null,100*1e3),f.value=d}catch(o){console.log(o),p.msgError("字体加载失败，请重试")}finally{p.closeLoading()}};return(d,o)=>{const y=M,S=U,u=w,F=z,c=B,_=L;return i(),V("div",null,[l(u,{label:"设置字体"},{default:s(()=>[l(S,{class:"w-full","model-value":t(f),onChange:g},{default:s(()=>[(i(!0),V(P,null,R(t(E).fontList,e=>(i(),j(y,{key:e.code,label:e.name,value:e.code},null,8,["label","value"]))),128))]),_:1},8,["model-value"])]),_:1}),l(u,{label:"字体大小"},{default:s(()=>[l(F,{modelValue:t(m),"onUpdate:modelValue":o[0]||(o[0]=e=>r(m)?m.value=e:null),min:20,max:160,"controls-position":"right"},null,8,["modelValue"])]),_:1}),l(u,{label:"字体颜色"},{default:s(()=>[v("div",q,[l(c,{modelValue:t(n),"onUpdate:modelValue":o[1]||(o[1]=e=>r(n)?n.value=e:null)},null,8,["modelValue"]),l(_,{class:"flex-1 ml-[10px]",modelValue:t(n),"onUpdate:modelValue":o[2]||(o[2]=e=>r(n)?n.value=e:null),readonly:""},null,8,["modelValue"])])]),_:1}),l(u,{label:"描边颜色"},{default:s(()=>[v("div",D,[l(c,{modelValue:t(a),"onUpdate:modelValue":o[3]||(o[3]=e=>r(a)?a.value=e:null)},null,8,["modelValue"]),l(_,{class:"flex-1 ml-[10px]",modelValue:t(a),"onUpdate:modelValue":o[4]||(o[4]=e=>r(a)?a.value=e:null),readonly:""},null,8,["modelValue"])])]),_:1})])}}});export{ee as _};
