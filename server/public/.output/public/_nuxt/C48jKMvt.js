import{_ as d}from"./BH1TZLrE.js";import{_}from"./DfULzLLs.js";import{a as c,_ as u}from"./CmRxzTqw.js";import{u as f}from"./Ce6KOvmZ.js";import{l as x,m as h,M as C,N as b,a3 as i,a1 as v,O as e,u as o}from"./CUZG7cWw.js";import"./oVx59syQ.js";import"./eFgaMLiC.js";import"./DlAUqK2U.js";/* empty css        */import"./CbQsrhNE.js";import"./CH6wv3Pu.js";import"./CCGM0zxW.js";import"./DCzKTodP.js";/* empty css        */import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./BYMcWg3Q.js";/* empty css        */import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        */import"./DjHPV-Am.js";import"./DoCT-qbH.js";import"./DAOx25wS.js";import"./DwFObZc_.js";import"./DQUFgXGm.js";import"./CRNANWso.js";import"./CHg9aK2B.js";import"./DecTOTC8.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./Do9LV2MU.js";import"./D5RYEqFL.js";import"./DNOp0HuO.js";import"./C-cKpkeq.js";import"./qRM0tN96.js";import"./CaNlADry.js";const I={class:"h-full flex"},S={class:"flex h-full p-[16px]"},V={class:"h-full pr-[16px] py-[16px] flex-1 min-w-0"},k={class:"h-full flex flex-col bg-body rounded-lg"},So=x({__name:"chat",setup(y){const t=f(),m=c(),s=h(()=>m.query.id);return(E,r)=>{const p=d,n=_,l=u;return C(),b("div",null,[i(l,{name:"default"},{default:v(()=>[e("div",I,[e("div",S,[i(p,{modelValue:o(t).sessionId,"onUpdate:modelValue":r[0]||(r[0]=a=>o(t).sessionId=a),data:o(t).sessionLists,onAdd:o(t).sessionAdd,onEdit:o(t).sessionEdit,onDelete:o(t).sessionDelete,onClear:o(t).sessionClear,onClickItem:o(t).setSessionSelect},null,8,["modelValue","data","onAdd","onEdit","onDelete","onClear","onClickItem"])]),e("div",V,[e("div",k,[i(n,{"robot-id":o(s)},null,8,["robot-id"])])])])]),_:1})])}}});export{So as default};
