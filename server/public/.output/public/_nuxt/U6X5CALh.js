import{E as q,a as I}from"./CXDY_LVT.js";import{E as M,a as O}from"./B7GaOiDz.js";import{e as S,E as L}from"./CmRxzTqw.js";import{_ as $}from"./eFgaMLiC.js";import{_ as j}from"./D-n7HwjM.js";import{_ as K}from"./DUp2AN3X.js";import{E as T}from"./oVx59syQ.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{u as A}from"./DIUf2-0l.js";import{r as G}from"./Dl64kDm5.js";import{P as H}from"./CaNlADry.js";import{l as J,j as c,b as v,c as Q,M as u,a0 as k,a1 as s,a3 as l,u as n,N as W,ao as X,_ as Y,O as r,y as Z,ag as h}from"./CUZG7cWw.js";const ee={class:"flex-1"},le={class:"flex-1"},oe={class:"flex-1"},te={class:"max-w-[600px]"},Ve=J({__name:"correct-popup",emits:["confirm"],setup(ae,{expose:x,emit:b}){const y=b,d=c(),m=c(),p=v(""),o=v({name:"",kb_id:"",ask:"",reply:"",images:[],video:[],files:[]});Q(p,t=>{o.value.video=[{url:t,name:""}]});const w={kb_id:[{required:!0,message:"选择知识库"}],ask:[{required:!0,message:"请输入问题"}],reply:[{required:!0,message:"请输入答案"}]},V=t=>{var e;o.value={...o.value,ask:t.ask,reply:t.reply,name:t.name},(e=m.value)==null||e.open()},g=()=>{var t;(t=m.value)==null||t.close()},E=async()=>{var t;await((t=d.value)==null?void 0:t.validate()),y("confirm",o.value)},{optionsData:U}=A({knowledge:{api:G,params:{page_type:0},transformData(t){return t.lists||[]}}});return x({open:V,close:g}),(t,e)=>{const R=q,B=I,i=M,f=S,D=$,_=j,P=K,z=L,C=O,F=T;return u(),k(H,{ref_key:"popupRef",ref:m,center:"",title:"修正问答",async:"",width:"900px",onConfirm:E},{default:s(()=>[l(F,null,{default:s(()=>[l(C,{ref_key:"formRef",ref:d,model:n(o),rules:w,"label-width":"120px"},{default:s(()=>[l(i,{label:"选择知识库",prop:"kb_id"},{default:s(()=>[l(B,{modelValue:n(o).kb_id,"onUpdate:modelValue":e[0]||(e[0]=a=>n(o).kb_id=a),class:"w-[240px]"},{default:s(()=>[(u(!0),W(Y,null,X(n(U).knowledge,(a,N)=>(u(),k(R,{key:N,label:`${a.name}`,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"提问问题",prop:"ask"},{default:s(()=>[l(f,{modelValue:n(o).ask,"onUpdate:modelValue":e[1]||(e[1]=a=>n(o).ask=a),placeholder:"请输入问题",type:"textarea",resize:"none",rows:4,maxlength:"600","show-word-limit":"",clearable:""},null,8,["modelValue"])]),_:1}),l(i,{label:"问题答案",prop:"reply"},{default:s(()=>[l(f,{modelValue:n(o).reply,"onUpdate:modelValue":e[2]||(e[2]=a=>n(o).reply=a),placeholder:"请输入答案",type:"textarea",resize:"none",rows:15,clearable:""},null,8,["modelValue"])]),_:1}),l(i,{label:"上传图片"},{default:s(()=>[r("div",ee,[r("div",null,[l(_,{files:n(o).images,"onUpdate:files":e[3]||(e[3]=a=>n(o).images=a),type:"image","list-type":"picture-card",limit:9,multiple:"","show-file-list":""},{default:s(()=>[l(D,{name:"el-icon-Plus",size:20})]),_:1},8,["files"])]),e[6]||(e[6]=r("div",{class:"form-tips"},"最多支持上传 9 张图",-1))])]),_:1}),l(i,{label:"上传视频"},{default:s(()=>[r("div",le,[r("div",null,[l(P,{modelValue:n(p),"onUpdate:modelValue":e[4]||(e[4]=a=>Z(p)?p.value=a:null),size:"80px"},null,8,["modelValue"])]),e[7]||(e[7]=r("div",{class:"form-tips"},"格式为MP4，大小不能超过20M",-1))])]),_:1}),l(i,{label:"上传附件"},{default:s(()=>[r("div",oe,[r("div",te,[l(_,{files:n(o).files,"onUpdate:files":e[5]||(e[5]=a=>n(o).files=a),type:"file","show-file-list":""},{tip:s(()=>e[9]||(e[9]=[r("div",{class:"el-upload__tip"}," 支持上传PDF、docx、excel、等文件格式 ",-1)])),default:s(()=>[l(z,null,{default:s(()=>e[8]||(e[8]=[h("上传附件")])),_:1})]),_:1},8,["files"])])])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},512)}}});export{Ve as _};
