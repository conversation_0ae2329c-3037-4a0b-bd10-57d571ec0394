import{E as l}from"./oVx59syQ.js";import{b as c,_}from"./CmRxzTqw.js";/* empty css        */import u from"./BCYkNna0.js";import{l as d,M as t,N as r,a3 as p,a1 as m,O as i,_ as f,ao as x,a0 as h,a2 as k,u as e,Z as v}from"./CUZG7cWw.js";import"./B1xxNkJB.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./mBG0LxMu.js";import"./CbQsrhNE.js";import"./DecTOTC8.js";/* empty css        */import"./DlAUqK2U.js";import"./BaQFMpQN.js";import"./D61w_8SR.js";import"./DBz5lpK8.js";import"./FkYCnHE4.js";import"./9CYoqqXX.js";import"./BOx_5T3X.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./Ddo5WWE5.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./aUpreUFZ.js";import"./QHNTKww7.js";import"./D3znQkH1.js";import"./eFgaMLiC.js";import"./DluKwKHO.js";/* empty css        */import"./CwwbfV-C.js";import"./DL3I_s72.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";/* empty css        */import"./DAOx25wS.js";import"./DNOp0HuO.js";import"./BjOWC1fj.js";import"./BildjBiE.js";import"./llRQJmEG.js";import"./DqGsTvs3.js";import"./Mf6yzLCP.js";import"./D0GTlwMd.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";import"./B7GaOiDz.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";/* empty css        */import"./DP2rzg_V.js";import"./Bj_9-7Jh.js";import"./D4TMB8r7.js";import"./Cv1u9LLW.js";import"./D3vSsDRj.js";const N={class:"h-full"},S={class:"index"},Ao=d({__name:"index",setup(g){const a=c();return(y,B)=>{const n=l,s=_;return t(),r("div",null,[p(s,{name:"single-row"},{default:m(()=>[i("div",N,[p(n,null,{default:m(()=>[i("div",S,[(t(!0),r(f,null,x(e(a).pageIndex,o=>(t(),r("div",{key:o.id},[o.isShow?(t(),h(k(e(u)[o.name]),{key:0,prop:o.prop},null,8,["prop"])):v("",!0)]))),128))])]),_:1})])]),_:1})])}}});export{Ao as default};
