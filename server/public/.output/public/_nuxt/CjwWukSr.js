import{E as x,a as g}from"./DHUC3PVh.js";import{a as k}from"./CmRxzTqw.js";import{b as v}from"./Dl64kDm5.js";import y from"./JP19D1Mj.js";import{l as w,r as B,j as U,b as V,M as e,N as i,O as D,a3 as E,a1 as s,u as a,_ as S,ao as h,a0 as n,a2 as C}from"./CUZG7cWw.js";import{_ as N}from"./DlAUqK2U.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./B7GaOiDz.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./eFgaMLiC.js";import"./C7tIPmrK.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";import"./DB7Ysqj9.js";import"./CXDY_LVT.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./D8e5izeA.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./Bf_xRNbS.js";import"./BOx_5T3X.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./DdtGP7XX.js";import"./Dhda0m3Y.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */const R={class:"p-main flex h-full flex-col"},T=w({__name:"setUp",emits:["update"],setup(j,{emit:l}){const c=l,d=k(),r=B({current:"baseSetting",lists:[{type:"baseSetting",name:"基础信息",component:U(y)}]}),m=V({}),p=async()=>{m.value=await v({id:d.query.id})},u=()=>{p(),c("update")};return p(),(q,o)=>{const _=g,f=x;return e(),i("div",R,[o[1]||(o[1]=D("div",{class:"text-xl font-medium"},"知识库设置",-1)),E(f,{class:"flex-1 min-h-0",modelValue:a(r).current,"onUpdate:modelValue":o[0]||(o[0]=t=>a(r).current=t)},{default:s(()=>[(e(!0),i(S,null,h(a(r).lists,(t,b)=>(e(),n(_,{label:t.name,name:t.type,key:b},{default:s(()=>[(e(),n(C(t.component),{data:a(m),onUpdate:u},null,40,["data"]))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])}}}),Dt=N(T,[["__scopeId","data-v-4da5484e"]]);export{Dt as default};
