import{E as j}from"./BCqAdQ5e.js";import{E as z,v as A,f as M}from"./CmRxzTqw.js";import{E as q,a as H}from"./sfCUuwOk.js";import{_ as O}from"./eFgaMLiC.js";import{E as G,a as J,b as K}from"./CwgXbNrK.js";import{_ as Q}from"./DJi8L2lq.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import{u as W}from"./DRe575WM.js";import{_ as X}from"./DKolA9W0.js";import{_ as Y}from"./WjDLTBxx.js";import{_ as Z}from"./BWW-AWEv.js";import{w as ee,x as ae,A as te,y as oe,z as ne}from"./qRM0tN96.js";import{l as se,j as y,C as le,r as ie,c as re,M as h,N as me,a3 as a,O as c,a1 as o,a7 as pe,u as d,a0 as de,y as _e,_ as fe,ag as r}from"./CUZG7cWw.js";const ce={class:"mt-4"},ue={class:"mt-4"},ge={class:"flex items-center"},ye={class:"flex justify-end mt-4"},Le=se({__name:"oa",props:{appId:{}},emits:["back"],setup(R,{emit:E}){const w=R,x=E,u=y(),_=y(),k=y(),{appId:S}=le(w),f=ie({robot_id:S,type:3}),{pager:m,getLists:s}=W({fetchFun:ee,params:f});s();const $=async t=>{await M.confirm("确定删除？"),await ne({id:t,type:f.type}),s()},B=(t,e)=>{switch(t){case"delete":$(e.id);break;case"edit":v(e);break;case"usage":V(e)}},v=t=>{var n;let e=null;t&&(e={id:t.id,name:t.name,password:t.secret}),(n=u.value)==null||n.open(e)},I=async(t,e)=>{var n;await(e=="add"?ae({...t,...f}):te({id:t.id,name:t.name,password:t.password})),(n=u.value)==null||n.close(),s()},V=t=>{var e,n;(e=_.value)==null||e.open(),(n=_.value)==null||n.setFormData(t)},D=async t=>{var e;await oe({...t,...f}),(e=_.value)==null||e.close(),s()};return re(()=>w.appId,()=>{s()}),(t,e)=>{const n=j,l=z,p=H,N=O,g=G,P=J,T=K,F=q,L=Q,U=A;return h(),me(fe,null,[a(n,{content:"发布微信公众号",onBack:e[0]||(e[0]=i=>x("back"))}),c("div",ce,[a(l,{type:"primary",onClick:e[1]||(e[1]=i=>v())},{default:o(()=>e[3]||(e[3]=[r(" 创建链接 ")])),_:1})]),c("div",ue,[pe((h(),de(F,{data:d(m).lists,size:"large"},{default:o(()=>[a(p,{label:"apikey",prop:"apikey","min-width":"200"}),a(p,{label:"分享名称",prop:"name","min-width":"180","show-tooltip-when-overflow":""}),a(p,{label:"访问密码",prop:"secret","min-width":"120"}),a(p,{label:"最后使用时间",prop:"use_time","min-width":"180"}),a(p,{label:"操作","min-width":"150",fixed:"right"},{default:o(({row:i})=>[c("div",ge,[a(l,{type:"primary",link:"",onClick:C=>{var b;return(b=d(k))==null?void 0:b.open(i.apikey)}},{default:o(()=>e[4]||(e[4]=[r(" 公众号配置 ")])),_:2},1032,["onClick"]),a(T,{class:"ml-[10px]",onCommand:C=>B(C,i)},{dropdown:o(()=>[a(P,null,{default:o(()=>[a(g,{command:"edit"},{default:o(()=>[a(l,{type:"primary",link:""},{default:o(()=>e[6]||(e[6]=[r(" 编辑 ")])),_:1})]),_:1}),a(g,{command:"usage"},{default:o(()=>[a(l,{type:"primary",link:""},{default:o(()=>e[7]||(e[7]=[r(" 用量设置 ")])),_:1})]),_:1}),a(g,{command:"delete"},{default:o(()=>[a(l,{type:"danger",link:""},{default:o(()=>e[8]||(e[8]=[r(" 删除 ")])),_:1})]),_:1})]),_:1})]),default:o(()=>[a(l,{type:"primary",link:""},{default:o(()=>[e[5]||(e[5]=r(" 更多 ")),a(N,{name:"el-icon-ArrowDown"})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[U,d(m).loading]]),c("div",ye,[a(L,{modelValue:d(m),"onUpdate:modelValue":e[2]||(e[2]=i=>_e(m)?m.value=i:null),onChange:d(s)},null,8,["modelValue","onChange"])])]),a(X,{ref_key:"createShareRef",ref:u,isShowChatType:!1,onConfirm:I},null,512),a(Y,{ref_key:"usageSettingsRef",ref:_,onConfirm:D},null,512),a(Z,{ref_key:"oaConfigRef",ref:k},null,512)],64)}}});export{Le as _};
