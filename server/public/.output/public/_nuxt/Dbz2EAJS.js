import{E as V}from"./oVx59syQ.js";import{_ as B}from"./BvSuqySp.js";import{_ as D}from"./eFgaMLiC.js";import{E as L}from"./C7tIPmrK.js";import{_ as T}from"./Dhx2k-ev.js";import{_ as N}from"./D3znQkH1.js";import{a as M,l as O,b as P}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        */import{u as U}from"./DNOp0HuO.js";import z from"./F9mNas_D.js";import{_ as F}from"./B9lr2Hvj.js";import{_ as J}from"./FK7_IsO-.js";import{g as Z}from"./qRM0tN96.js";import{u as j}from"./Ce6KOvmZ.js";import{l as G,b as d,ai as H,c as K,M as a,N as f,a3 as m,a1 as s,u as o,y as w,O as e,a0 as h,Z as y,_ as Q,ao as W,a5 as X}from"./CUZG7cWw.js";import{_ as Y}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";import"./u6CVc_ZE.js";import"./9CYoqqXX.js";import"./BOx_5T3X.js";import"./Ddo5WWE5.js";/* empty css        */import"./DHUC3PVh.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./B7GaOiDz.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";/* empty css        */import"./BjmMA-ez.js";import"./Dhda0m3Y.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./D8e5izeA.js";import"./CXDY_LVT.js";import"./DCzKTodP.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./C9f7n97H.js";import"./DYjlFFbo.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./DIUf2-0l.js";import"./Dl64kDm5.js";import"./BXBQD0li.js";import"./DB7Ysqj9.js";import"./Bf_xRNbS.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./DdtGP7XX.js";import"./zeSNwnEI.js";import"./CqEm55x_.js";import"./sfCUuwOk.js";import"./HA5sEeDs.js";/* empty css        *//* empty css        */import"./CdCxqKUj.js";import"./D-n7HwjM.js";import"./ng09i8Gy.js";import"./ttFW0yUc.js";import"./CbQsrhNE.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./BscXL5XZ.js";/* empty css        */import"./C6_W4ts7.js";import"./BX9LuVNS.js";import"./BCqAdQ5e.js";import"./YwtsEmdS.js";import"./CwgXbNrK.js";import"./DJi8L2lq.js";import"./C-n0m2hZ.js";/* empty css        */import"./DAOx25wS.js";import"./DRe575WM.js";import"./DKolA9W0.js";import"./WjDLTBxx.js";import"./BEkrvZTm.js";import"./CRNANWso.js";import"./Bj_9-7Jh.js";import"./BbGVBgM5.js";import"./DlorTuIx.js";import"./CMG3-MzP.js";import"./-o5CZtyp.js";import"./CH6wv3Pu.js";import"./Cu1bEeyC.js";import"./gIlbrd_1.js";import"./nO7O23Ti.js";import"./BWW-AWEv.js";import"./DjHPV-Am.js";import"./xFN416HV.js";import"./BNIxm8Lt.js";import"./Ce0U9aAs.js";import"./CAVlIcVy.js";import"./U6X5CALh.js";import"./DUp2AN3X.js";import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";const tt={class:"h-full flex"},ot=["onClick"],rt=["src"],et={class:"ml-[8px] line-clamp-1"},it={class:"flex items-center cursor-pointer"},pt={class:"text-xl flex-1 min-w-0"},mt={class:"sm:h-full py-[16px] pr-[16px] flex flex-col sm:flex-row flex-1 min-w-0"},nt={class:"sm:h-full flex-1 min-w-0 min-h-0 bg-body rounded-[12px]"},at=G({__name:"setting",async setup(st){let c,x;const i=M(),u=O();P();const v=j();v.getRobot();const _=d(!1),l=d(i.query.id),{data:b,refresh:k}=([c,x]=H(()=>U(()=>Z({id:l.value}),{transform(t){return(t==null?void 0:t.category_id)===0&&(t.category_id=""),t},default(){return{}},lazy:!0},"$3nIwi7TB6J")),c=await c,x(),c),p=d("edit"),S=[{name:"智能体设置",icon:"el-icon-Setting",key:"edit"},{name:"发布智能体",key:"release",icon:"el-icon-Position"},{name:"对话数据",key:"dialogue",icon:"el-icon-ChatDotRound"},{name:"立即对话",key:"chat",icon:"el-icon-ChatLineRound"}],q=t=>{switch(t){case"chat":u.push({path:"/application/chat",query:{id:l.value}});break;default:u.replace({path:i.path,query:{...i.query,currentTab:t}})}},C=async t=>{_.value=!1,t!=i.query.id&&(l.value=t,await k(),u.replace({path:i.path,query:{...i.query,id:t}}))};return K(()=>i.query,t=>{p.value=t.currentTab||"edit"},{immediate:!0}),(t,n)=>{const g=V,R=B,A=D,E=L,$=T,I=N;return a(),f("div",tt,[m($,{modelValue:o(p),"onUpdate:modelValue":[n[1]||(n[1]=r=>w(p)?p.value=r:null),q],"menu-list":S,"back-path":"/application/layout/robot"},{title:s(()=>[e("div",null,[m(E,{placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:o(_),"onUpdate:visible":n[0]||(n[0]=r=>w(_)?_.value=r:null)},{reference:s(()=>[e("div",it,[e("div",pt,[m(R,{content:o(b).name,teleported:!0,effect:"light"},null,8,["content"])]),m(A,{name:"el-icon-ArrowDown"})])]),default:s(()=>[e("div",null,[m(g,{style:{height:"250px"}},{default:s(()=>[(a(!0),f(Q,null,W(o(v).robotLists,r=>(a(),f("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-primary-light-9 px-[10px] my-[5px] rounded-[12px] hover:text-primary",key:r.id,onClick:lt=>C(r.id)},[e("img",{class:"rounded-[50%] w-[28px] h-[28px] flex-none",src:r.image,alt:""},null,8,rt),e("div",et,X(r.name),1)],8,ot))),128))]),_:1})])]),_:1},8,["visible"])])]),_:1},8,["modelValue"]),e("div",mt,[e("div",nt,[o(p)==="edit"?(a(),h(z,{key:0,"model-value":o(b),onSuccess:n[2]||(n[2]=r=>o(u).push("/application/layout/robot"))},null,8,["model-value"])):y("",!0),m(I,null,{default:s(()=>[o(p)==="release"?(a(),h(g,{key:0},{default:s(()=>[m(F,{"app-id":o(l)},null,8,["app-id"])]),_:1})):y("",!0)]),_:1}),o(p)==="dialogue"?(a(),h(J,{key:1,"app-id":o(l)},null,8,["app-id"])):y("",!0)])])])}}}),hr=Y(at,[["__scopeId","data-v-21d9b61a"]]);export{hr as default};
