import{cA as x,e as v}from"./CmRxzTqw.js";import{_ as h}from"./eFgaMLiC.js";import{_ as w}from"./DwRn548t.js";import{_ as V}from"./DIux4E1M.js";import{l as k,M as C,N as I,O as n,a3 as s,u as m,y as l,a1 as M,a5 as $}from"./CUZG7cWw.js";import{_ as g}from"./DlAUqK2U.js";import"./C7tIPmrK.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";/* empty css        *//* empty css        */import"./BLV0QRdm.js";import"./CaJo29OT.js";import"./Cq2NhlyP.js";import"./DecTOTC8.js";const B={class:"bg-page rounded-[15px] overflow-hidden p-[10px] search-input"},D={class:"flex items-center"},E={class:"mr-auto"},N={class:"flex items-center px-[8px]"},R={class:"px-[6px]"},z=k({__name:"search-input",props:{mode:{},model:{},type:{},input:{}},emits:["update:type","update:input","search"],setup(d,{emit:u}){const c=d,p=u,{type:a,input:i}=x(c,p),_=e=>{if(!(e.shiftKey&&e.keyCode===13)&&e.keyCode===13)return p("search"),e.preventDefault()};return(e,t)=>{const f=v,r=h;return C(),I("div",B,[n("div",null,[s(f,{modelValue:m(i),"onUpdate:modelValue":t[0]||(t[0]=o=>l(i)?i.value=o:null),autosize:{minRows:2,maxRows:4},type:"textarea",placeholder:"输入你想搜索的问题",resize:"none",onKeydown:_},null,8,["modelValue"])]),n("div",D,[n("div",E,[s(w,{type:m(a),"onUpdate:type":t[1]||(t[1]=o=>l(a)?a.value=o:null),model:e.model},{item:M(({icon:o,label:y})=>[n("div",N,[s(r,{name:o},null,8,["name"]),n("span",R,$(y),1),s(r,{name:"el-icon-ArrowDown"})])]),_:1},8,["type","model"])]),n("div",null,[s(V,{onClick:t[2]||(t[2]=o=>p("search"))})])])])}}}),W=g(z,[["__scopeId","data-v-68cad505"]]);export{W as default};
