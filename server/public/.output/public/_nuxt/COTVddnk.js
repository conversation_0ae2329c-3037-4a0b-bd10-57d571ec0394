import{E as h}from"./DCzKTodP.js";import{E as V,a as D}from"./B7GaOiDz.js";import{E as C}from"./CiabO6Xq.js";import{E as N}from"./ArzC3z2d.js";import"./CmRxzTqw.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import{e as P}from"./CH-eeB8d.js";import{l as q,b as f,M as _,a0 as m,a1 as a,u as t,y as B,a3 as r,ag as l,a5 as o,Z as n}from"./CUZG7cWw.js";const H=q({__name:"detail",emits:["closePop"],setup(F,{expose:d,emit:y}){const b=y,u=f(!1),e=f({}),k=async c=>{u.value=!0,e.value=await P({id:c.id})},g=()=>{u.value=!1,b("closePop")};return d({open:k}),(c,p)=>{const i=h,s=V,w=C,E=D,v=N;return _(),m(v,{modelValue:t(u),"onUpdate:modelValue":p[0]||(p[0]=x=>B(u)?u.value=x:null),width:"500px",title:"提现详情","close-on-click-modal":!1,onClose:g},{default:a(()=>[r(E,{"label-width":"120px"},{default:a(()=>[r(s,{label:"提现状态"},{default:a(()=>[t(e).status==1?(_(),m(i,{key:0},{default:a(()=>[l(o(t(e).status_desc||"-"),1)]),_:1})):n("",!0),t(e).status==3?(_(),m(i,{key:1,type:"success"},{default:a(()=>[l(o(t(e).status_desc||"-"),1)]),_:1})):n("",!0),t(e).status==2?(_(),m(i,{key:2,type:"warning"},{default:a(()=>[l(o(t(e).status_desc||"-"),1)]),_:1})):n("",!0),t(e).status==4?(_(),m(i,{key:3,type:"danger"},{default:a(()=>[l(o(t(e).status_desc||"-"),1)]),_:1})):n("",!0)]),_:1}),r(s,{label:"用户信息"},{default:a(()=>[l(o(t(e).nickname||"-"),1)]),_:1}),r(s,{label:"提现金额"},{default:a(()=>[l(o(t(e).money||"-"),1)]),_:1}),r(s,{label:"手续费"},{default:a(()=>[l(o(t(e).handling_fee||"-"),1)]),_:1}),r(s,{label:"到账金额"},{default:a(()=>[l(o(t(e).left_money||"-"),1)]),_:1}),r(s,{label:"提现方式"},{default:a(()=>[l(o(t(e).type_desc||"-"),1)]),_:1}),r(s,{label:"真实姓名"},{default:a(()=>[l(o(t(e).real_name||"-"),1)]),_:1}),t(e).type!=2?(_(),m(s,{key:0,label:`${t(e).type==3?"微信":"支付宝"}账号`},{default:a(()=>[l(o(t(e).account),1)]),_:1},8,["label"])):n("",!0),t(e).type==3||t(e).type==4?(_(),m(s,{key:1,label:"收款码"},{default:a(()=>[r(w,{src:t(e).money_qr_code,class:"w-[60px] h-[60px]","preview-src-list":[t(e).money_qr_code]},null,8,["src","preview-src-list"])]),_:1})):n("",!0),r(s,{label:"申请时间"},{default:a(()=>[l(o(t(e).create_time||"-"),1)]),_:1}),r(s,{label:"审核时间"},{default:a(()=>[l(o(t(e).verify_time||"-"),1)]),_:1}),t(e).status==4?(_(),m(s,{key:2,label:"失败时间"},{default:a(()=>[l(o(t(e).finish_time||"-"),1)]),_:1})):n("",!0),t(e).verify_remark?(_(),m(s,{key:3,label:"审核说明"},{default:a(()=>[l(o(t(e).verify_remark||"-"),1)]),_:1})):n("",!0),t(e).transfer_remark?(_(),m(s,{key:4,label:"转账说明"},{default:a(()=>[l(o(t(e).transfer_remark||"-"),1)]),_:1})):n("",!0)]),_:1})]),_:1},8,["modelValue"])}}});export{H as _};
