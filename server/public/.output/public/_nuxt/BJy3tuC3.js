import{b as y,l as v,a as b,_ as k}from"./CmRxzTqw.js";import w from"./DaNo3OlT.js";import{l as g,m,b as C,c as u,M as s,N as n,a3 as N,a1 as B,x as R,O as o,u as r,_ as q,ao as L,$ as V,a5 as D,a0 as I,a2 as S,Z as $}from"./CUZG7cWw.js";import{_ as j}from"./DlAUqK2U.js";import"./C9pcNxkS.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./eFgaMLiC.js";/* empty css        */import"./DqGsTvs3.js";import"./Bj_9-7Jh.js";import"./5SHXd8at.js";const z={class:"p-main"},A={key:0,class:"flex h-full flex-col pt-[10px] max-w-[1200px] mx-auto"},E={class:"tab-lists"},F={class:"flex mx-[-10px]"},M=["onClick"],O={class:"w-full"},T={class:"flex-1 min-h-0"},Z={key:1,class:"w-full h-full bg-white rounded-[12px] flex items-center justify-center"},G=g({__name:"index",setup(H){y();const _=v(),p=b(),c=m(()=>[{name:"充值中心",type:"recharge",show:!0,component:R(w)}].filter(t=>!!t.show)),d=p.query.type,a=C(d),f=e=>{a.value=e,_.replace({path:"",query:{type:e}})},i=m(()=>c.value.find(e=>e.type===a.value));return u(c,e=>{if(!i.value&&e.length){const[t]=e;a.value=t.type}},{immediate:!0}),u(()=>p.query.type,e=>{a.value=e}),(e,t)=>{const x=k;return s(),n("div",null,[N(x,{name:"default"},{default:B(()=>[o("div",z,[r(c).length?(s(),n("div",A,[o("div",E,[o("div",F,[(s(!0),n(q,null,L(r(c),(l,h)=>(s(),n("div",{class:V(["tab-item",{"is-active":r(a)==l.type}]),key:h,onClick:J=>f(l.type)},[o("span",O,D(l.name),1)],10,M))),128))])]),o("div",T,[r(i)?(s(),I(S(r(i).component),{key:0})):$("",!0)])])):(s(),n("div",Z,t[0]||(t[0]=[o("div",{class:"text-xl"},"功能未开启!",-1)])))])]),_:1})])}}}),ce=j(G,[["__scopeId","data-v-db46a5c1"]]);export{ce as default};
