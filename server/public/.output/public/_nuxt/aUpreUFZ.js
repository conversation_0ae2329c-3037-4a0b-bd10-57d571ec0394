import{E as d}from"./9CYoqqXX.js";import{_}from"./mBG0LxMu.js";import{b as f,d6 as h}from"./CmRxzTqw.js";import{l as y,m as I,M as s,N as n,a3 as i,a1 as r,u as t,O as S,a5 as k,Z as N}from"./CUZG7cWw.js";import{_ as g}from"./DlAUqK2U.js";import"./BOx_5T3X.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./Ddo5WWE5.js";import"./CbQsrhNE.js";import"./DecTOTC8.js";const B=["src"],b=["src"],q=y({__name:"menu-item",props:{item:{},path:{},isShowIcon:{type:[Number,Boolean]},isActive:{type:<PERSON>olean}},setup(c){const p=c,{getImageUrl:a}=f(),m=I(()=>{const e=p.item.link.query;try{const o=JSON.parse(e);return h(o)}catch{return e}});return(e,o)=>{const u=d,l=_;return s(),n("div",null,[i(l,{to:`${e.path}${t(m)?`?${t(m)}`:""}`},{default:r(()=>[i(u,{index:e.path},{title:r(()=>[S("span",null,k(e.item.name),1)]),default:r(()=>[e.isActive&&e.item.selected&&e.isShowIcon?(s(),n("img",{key:0,class:"menu-item-icon",src:t(a)(e.item.selected)},null,8,B)):e.item.unselected&&e.isShowIcon?(s(),n("img",{key:1,class:"menu-item-icon",src:t(a)(e.item.unselected)},null,8,b)):N("",!0)]),_:1},8,["index"])]),_:1},8,["to"])])}}}),J=g(q,[["__scopeId","data-v-eaa55001"]]);export{J as default};
