import{A as l,r as u}from"./CUZG7cWw.js";function h(o){const{page:c=1,size:i=15,fetchFun:g,params:t={},firstLoading:p=!1,afterFetch:f=()=>{}}=o,r=Object.assign({},l(t)),a=u({page:c,size:i,loading:p,count:0,lists:[],extend:{}}),s=()=>(a.loading=!0,g({page_no:a.page,page_size:a.size,...t}).then(e=>(Reflect.ownKeys(e).map(n=>{a[n]=e[n]}),f(),Promise.resolve(e))).catch(e=>Promise.reject(e)).finally(()=>{a.loading=!1}));return{pager:a,getLists:s,resetParams:()=>{Object.keys(r).forEach(e=>{t[e]=r[e]}),s()},resetPage:()=>{a.page=1,s()}}}export{h as u};
