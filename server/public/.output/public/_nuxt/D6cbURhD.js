import{E as w,a as I}from"./CXDY_LVT.js";import{j as k,dl as B,dm as F,dn as g,e as M}from"./CmRxzTqw.js";import{E as O,a as P}from"./B7GaOiDz.js";import{_ as j}from"./FAfxnQR5.js";import{P as N}from"./CaNlADry.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{l as U,j as m,r as q,M as D,a0 as $,a1 as l,u as n,O as u,a3 as o}from"./CUZG7cWw.js";const h={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},oe=U({__name:"bindmobilePop",emits:["close"],setup(z,{expose:f,emit:G}){const a=m(),_=k(),t=q({mobile:"",code:""}),i=m(),p=m(),b=()=>{a.value.open()},E={mobile:[{required:!0,message:"请输入手机号"}],code:[{required:!0,message:"请输入验证码"}]},v=async()=>{var s,e;await((s=p.value)==null?void 0:s.validateField(["mobile"])),await F({scene:g.BIND_MOBILE,mobile:t.mobile}),(e=i.value)==null||e.start()},x=async()=>{await B({type:"bind",...t}),a.value.close()};return f({open:b}),(s,e)=>{const y=w,V=I,d=M,c=O,C=j,R=P,S=N;return D(),$(S,{ref_key:"popRef",ref:a,title:n(_).userInfo.mobile?"点击更改":"立即绑定",async:"","confirm-button-text":"确认更改",onConfirm:x,onClose:e[2]||(e[2]=r=>s.$emit("close"))},{default:l(()=>[u("div",null,[o(R,{ref_key:"formRef",ref:p,size:"large",model:n(t),rules:E},{default:l(()=>[o(c,{prop:"mobile"},{default:l(()=>[o(d,{modelValue:n(t).mobile,"onUpdate:modelValue":e[0]||(e[0]=r=>n(t).mobile=r),placeholder:"请输入手机号"},{prepend:l(()=>[o(V,{"model-value":"+86",style:{width:"80px"}},{default:l(()=>[o(y,{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(c,{prop:"code"},{default:l(()=>[o(d,{modelValue:n(t).code,"onUpdate:modelValue":e[1]||(e[1]=r=>n(t).code=r),placeholder:"请输入验证码"},{suffix:l(()=>[u("div",h,[o(C,{ref_key:"verificationCodeRef",ref:i,onClickGet:v},null,512)])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["title"])}}});export{oe as _};
