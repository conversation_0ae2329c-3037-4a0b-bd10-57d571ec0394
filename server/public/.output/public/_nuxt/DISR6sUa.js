import{j as N,b as R,dE as O,d1 as v,dp as T,dn as P,dF as U,e as j,E as M}from"./CmRxzTqw.js";import{a as q,E as D}from"./B7GaOiDz.js";import{_ as G}from"./FAfxnQR5.js";import"./DP2rzg_V.js";/* empty css        */import{u as $}from"./Bj_9-7Jh.js";import{l as A,j as C,r as z,m as x,M as i,N as W,O as p,a3 as a,a1 as l,u as o,a0 as u,Z as f,ag as c}from"./CUZG7cWw.js";const X={class:"pt-[10px]"},Z={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},H={class:"flex justify-end"},J={class:"flex-1 flex"},le=A({__name:"mailbox-login",setup(K){const r=N(),L=R(),_=C(),V={email:[{required:!0,message:"请输入邮箱账号"},{type:"email",message:"请输入正确的邮箱账号"}],password:[{required:!0,message:"请输入密码"}],code:[{required:!0,message:"请输入验证码"}]},t=z({code:"",email:"",password:"",scene:4,terminal:O}),k=x(()=>t.scene===4),y=x(()=>t.scene===2),w=s=>{t.scene=s},E=C(),B=async()=>{var s,e;await((s=_.value)==null?void 0:s.validateField(["email"])),await T({scene:P.LOGIN,email:t.email}),(e=E.value)==null||e.start()},{lockFn:F,isLock:I}=$(async()=>{var e;await((e=_.value)==null?void 0:e.validate());const s=await U(t);!s.mobile&&L.getLoginConfig.coerce_mobile?(r.temToken=s.token,r.setLoginPopupType(v.BIND_MOBILE)):(r.login(s.token),r.setUser(s),r.toggleShowLogin(!1),location.reload())});return(s,e)=>{const g=j,d=D,S=G,m=M,b=q;return i(),W("div",X,[p("div",null,[a(b,{ref_key:"formRef",ref:_,size:"large",model:o(t),rules:V},{default:l(()=>[a(d,{prop:"email"},{default:l(()=>[a(g,{modelValue:o(t).email,"onUpdate:modelValue":e[0]||(e[0]=n=>o(t).email=n),placeholder:"请输入邮箱账号"},null,8,["modelValue"])]),_:1}),o(y)?(i(),u(d,{key:0,prop:"password"},{default:l(()=>[a(g,{modelValue:o(t).password,"onUpdate:modelValue":e[1]||(e[1]=n=>o(t).password=n),type:"password","show-password":"",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):f("",!0),o(k)?(i(),u(d,{key:1,prop:"code"},{default:l(()=>[a(g,{modelValue:o(t).code,"onUpdate:modelValue":e[2]||(e[2]=n=>o(t).code=n),placeholder:"请输入验证码"},{suffix:l(()=>[p("div",Z,[a(S,{ref_key:"verificationCodeRef",ref:E,onClickGet:B},null,512)])]),_:1},8,["modelValue"])]),_:1})):f("",!0),p("div",H,[p("div",J,[o(y)?(i(),u(m,{key:0,type:"primary",link:"",onClick:e[3]||(e[3]=n=>w(4))},{default:l(()=>e[6]||(e[6]=[c(" 邮箱验证码登录 ")])),_:1})):f("",!0),o(k)?(i(),u(m,{key:1,type:"primary",link:"",onClick:e[4]||(e[4]=n=>w(2))},{default:l(()=>e[7]||(e[7]=[c(" 邮箱密码登录 ")])),_:1})):f("",!0)]),a(m,{link:"",onClick:e[5]||(e[5]=n=>o(r).setLoginPopupType(o(v).FORGOT_PWD_MAILBOX))},{default:l(()=>e[8]||(e[8]=[c(" 忘记密码？ ")])),_:1})]),a(d,{class:"my-[30px]"},{default:l(()=>[a(m,{class:"w-full",type:"primary",loading:o(I),onClick:o(F)},{default:l(()=>e[9]||(e[9]=[c(" 登录 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])])])}}});export{le as _};
