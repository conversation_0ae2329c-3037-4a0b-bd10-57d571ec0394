import{E as h,c as g,d as y}from"./BCtbxh46.js";import{b as S,j as w,d0 as k,by as $,cD as b,dJ as v}from"./CmRxzTqw.js";import{_ as x}from"./KpaauuTh.js";import{_ as E}from"./BC0yJvgx.js";import M from"./DEn9YCt-.js";import{_ as C}from"./CWF3-709.js";import{l as B,m as D,E as N,ap as V,M as e,a0 as i,a1 as t,a6 as z,u as m,a3 as o,V as F,Z as n}from"./CUZG7cWw.js";import{_ as I}from"./DlAUqK2U.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        *//* empty css        */import"./BVTdJXKU.js";import"./DHUC3PVh.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./CbQsrhNE.js";import"./B4XIt-XN.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./B7GaOiDz.js";import"./Bh-PoUNP.js";import"./FAfxnQR5.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./Bj_9-7Jh.js";import"./DISR6sUa.js";import"./BEuS_AA8.js";import"./eFgaMLiC.js";import"./DqGsTvs3.js";import"./DqKCLwOu.js";import"./ezienwJz.js";import"./DnaAw8MZ.js";import"./D8CLlV3f.js";import"./P8Qw-ZvZ.js";import"./D9b7mKi3.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";/* empty css        */import"./DSoGMbEr.js";const P=B({__name:"single-row",setup(R){const l=S(),c=w(),u=k(),_=$();D(()=>l.isMobile?{"--header-height":"50px","--main-padding":"12px"}:{"--main-padding":"15px"});const{height:p}=b(),a=()=>{_.value=!1,u.setTheme(!1)};return N(()=>{a()}),V(()=>{a()}),(r,T)=>{const d=g,f=y,s=h;return e(),i(s,{class:"layout-bg h-full layout-default",style:z([{height:`${m(p)=="Infinity"?"100vh":m(p)+"px"}`}])},{default:t(()=>[o(s,{class:"min-h-0"},{default:t(()=>[o(d,{style:{padding:"0"}},{default:t(()=>[F(r.$slots,"default",{},void 0,!0)]),_:3}),(r._.provides[v]||r.$route).meta.hiddenFooter?n("",!0):(e(),i(f,{key:0,height:"auto"},{default:t(()=>[o(x)]),_:1}))]),_:3}),m(c).showLogin?(e(),i(E,{key:0})):n("",!0),o(M),o(C)]),_:3},8,["style"])}}}),Lo=I(P,[["__scopeId","data-v-71269ca2"]]);export{Lo as default};
