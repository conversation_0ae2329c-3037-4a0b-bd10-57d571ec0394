import{E as A}from"./CiabO6Xq.js";import{_ as M}from"./eFgaMLiC.js";import{E as R}from"./oVx59syQ.js";import{h as F,E as j,v as O}from"./CmRxzTqw.js";import{E as U}from"./ArzC3z2d.js";/* empty css        *//* empty css        *//* empty css        */import{u as Z}from"./Bj_9-7Jh.js";import{c as q}from"./R2n930gq.js";import{l as G,r as H,b as J,m as K,c as C,M as r,a0 as E,a1 as v,u as t,y as Q,a7 as W,N as i,O as e,a3 as c,_,ao as f,$ as V,a5 as X,a6 as Y,Z as ee,ag as le}from"./CUZG7cWw.js";const oe={class:"flex text-tx-primary"},te={class:"flex-[3] h-full mr-[26px]"},se={class:"flex-[1.8] flex flex-col"},re={class:"flex text-xs"},ae={class:"flex flex-wrap"},ne=["onClick"],ie={class:"flex text-xs"},ce={class:"flex flex-wrap mx-[-6px]"},pe=["onClick"],de={class:"flex-1 min-h-0"},me={class:"overflow-hidden"},ue={class:"flex flex-wrap mx-[-6px] py-[12px]"},ve=["onClick"],xe={class:"px-[6px]"},_e={class:"flex ml-[10px]"},Ie=G({__name:"select-template",props:{visible:{type:Boolean},coverId:{},prompt:{default:""}},emits:["update:visible","update:coverId","confirm"],setup(I,{emit:B}){const a=I,x=B,p=F(a,"visible",x),d=F(a,"coverId",x),P=["科技","商务","小清新","极简","中国风","可爱卡通"],S=[{label:"红色",value:"#D7000F"},{label:"橙色",value:"#FF7A00"},{label:"黄色",value:"#FFC700"},{label:"绿色",value:"#39D819"},{label:"青色",value:"#3f9097"},{label:"蓝色",value:"#0385FF"},{label:"紫色",value:"#C73AFF"},{label:"粉色",value:"#FF3AA0"}],n=H({color:"红色",style:"科技"});let b="";const m=J([]),g=()=>{const[s]=m.value;s&&s.cover_id&&(d.value=s.cover_id)},{lockFn:y,isLock:T}=Z(async()=>{b=a.prompt,m.value=await q({prompt:a.prompt,...n}),g()}),$=K(()=>m.value.find(s=>s.cover_id===a.coverId)||{});return C(n,()=>{y()},{deep:!0}),C(p,s=>{if(s){if(b===a.prompt){!d.value&&g();return}y()}}),(s,o)=>{const k=A,h=M,D=R,L=j,N=U,z=O;return r(),E(N,{modelValue:t(p),"onUpdate:modelValue":o[1]||(o[1]=l=>Q(p)?p.value=l:null),width:"1100"},{default:v(()=>[W((r(),i("div",oe,[e("div",te,[c(k,{class:"w-full rounded-[10px] h-[350px]",src:t($).cover_image},{error:v(()=>o[2]||(o[2]=[e("div",{class:"el-image__error"},"选中右侧模板以预览",-1)])),_:1},8,["src"])]),e("div",se,[o[6]||(o[6]=e("div",{class:"font-bold mb-[15px]"},"选择PPT模板",-1)),e("div",re,[o[3]||(o[3]=e("div",{class:"flex-none mt-[5px] text-tx-regular mr-[8px]"}," 模板风格: ",-1)),e("div",ae,[(r(),i(_,null,f(P,(l,u)=>e("div",{key:u,class:V(["mx-[1px] px-[8px] py-[5px] rounded cursor-pointer hover:bg-page mb-[8px]",{"!bg-page":t(n).style===l}]),onClick:w=>t(n).style=l},X(l),11,ne)),64))])]),e("div",ie,[o[4]||(o[4]=e("div",{class:"flex-none text-tx-regular mr-[8px]"}," 主题颜色: ",-1)),e("div",ce,[(r(),i(_,null,f(S,(l,u)=>e("div",{key:u,class:"w-[18px] h-[18px] text-white mx-[6px] mb-[6px] rounded-[50%] flex items-center justify-center cursor-pointer",style:Y({background:l.value}),onClick:w=>t(n).color=l.label},[t(n).color===l.label?(r(),E(h,{key:0,name:"el-icon-Select",size:12})):ee("",!0)],12,pe)),64))])]),e("div",de,[c(D,null,{default:v(()=>[e("div",me,[e("div",ue,[(r(!0),i(_,null,f(t(m),(l,u)=>(r(),i("div",{class:"w-[50%]",key:l.cover_id,onClick:w=>d.value=l.cover_id},[e("div",xe,[c(k,{class:V(["w-full h-[100px] rounded border-2 cursor-pointer border-solid border-[transparent]",{"border-primary":t(d)===l.cover_id}]),src:l.cover_image},null,8,["class","src"])])],8,ve))),128))])])]),_:1})]),e("div",null,[c(L,{class:"w-full",type:"primary",size:"large",onClick:o[0]||(o[0]=l=>x("confirm"))},{default:v(()=>[o[5]||(o[5]=le(" 生成PPT ")),e("span",_e,[c(h,{name:"el-icon-Right"})])]),_:1})])])])),[[z,t(T)]])]),_:1},8,["modelValue"])}}});export{Ie as _};
