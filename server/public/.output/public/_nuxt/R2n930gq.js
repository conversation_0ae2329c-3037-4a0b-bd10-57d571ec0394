function e(){return $request.get({url:"/ppt/config"})}function r(){return $request.get({url:"/ppt/example"})}function u(t){return $request.get({url:"/ppt/lists",params:t})}function n(t){return $request.get({url:"/ppt/detail",params:t})}function s(t){return $request.post({url:"/ppt/structure",params:t})}function p(t){return $request.post({url:"/ppt/cover",params:t})}function o(t){return $request.post({url:"/ppt/submit",params:t})}function l(t){return $request.post({url:"/ppt/del",params:t})}function i(t){return $request.post({url:"/ppt/download",params:t})}export{u as a,l as b,p as c,i as d,e,o as f,n as g,s as h,r as i};
