import{E as le}from"./DluKwKHO.js";import{_ as ae}from"./BvSuqySp.js";import{E as ne}from"./DCzKTodP.js";import{E as ie,a as re}from"./sfCUuwOk.js";import{_ as me}from"./eFgaMLiC.js";import{E as de}from"./C7tIPmrK.js";import{l as ue,j as ce,E as C,f as pe}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{l as ve,b as v,m as S,r as $,j as fe,E as be,M as n,N as k,O as o,a5 as U,a0 as m,a1 as l,u,Z as g,a3 as a,a4 as T,ag as x}from"./CUZG7cWw.js";import{a as _e,d as ke,b as ge,t as xe}from"./DwFObZc_.js";import ye from"./CIM5GUyf.js";import A from"./DgYzYYsq.js";import{E as L}from"./HA5sEeDs.js";import{E as he}from"./ArzC3z2d.js";import{_ as we}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./oVx59syQ.js";import"./DSuLZIN6.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./Ddo5WWE5.js";import"./u6CVc_ZE.js";import"./DRe575WM.js";import"./Bj_9-7Jh.js";import"./YwtsEmdS.js";import"./CDwN27aR.js";import"./C3XldtMC.js";const Ce={class:"flex justify-between items-center py-2"},Te={class:"flex items-center"},Ee={class:"flex items-center"},Ve={key:0},Pe=["onClick"],Ie=["onClick"],Me=["onClick"],Se=["onClick"],$e={key:3,class:"text-base"},Ue={class:"flex items-center"},Ae={key:0},Ne={class:"cursor-pointer"},Oe=["onClick"],De={class:"text-base mr-1"},ze={key:2,class:"text-base"},Be={class:"flex justify-end"},Re=ve({__name:"team-data",props:{id:{type:Number,default:0}},setup(Z){const q={1:"可管理",2:"可编辑",3:"可查看"},F=Z,N=ue(),E=v(F.id),y=v([]),i=S(()=>y.value.find(s=>s.sn===ce().userInfo.sn)||{}),h=v(null),f=v(!1),c=v("all"),V=v(!1),O=$({sn:-1}),D=fe(),b=async()=>{y.value=await _e({kb_id:E.value})},G=()=>{d.visible=!1,p.visible=!1},H=()=>{var s;(s=D.value)==null||s.show()},d=$({visible:!1,memberId:""}),z=s=>{d.visible=!d.visible,d.memberId=s.id.toString()},B=s=>d.visible&&d.memberId===s.id.toString(),R=async s=>{try{await pe.confirm("确定删除该成员？","删除成员"),await ke({id:s.id}),i.value.sn===s.sn&&setTimeout(()=>{N.back()},1e3),b()}finally{d.visible=!1}},p=$({visible:!1,memberId:""}),J=s=>{p.visible=!0,p.memberId=s.id.toString(),h.value=s.power},K=s=>p.visible&&p.memberId===s.id.toString(),P=async(s,t)=>{h.value=t,await ge({id:s.id,power:t}),p.visible=!1,b()},Q=s=>{O.sn=s.sn,f.value=!0},W=async()=>{V.value=!0,await xe({sn:O.sn,kb_id:E.value,type:c.value}),V.value=!1,c.value==="kb"?setTimeout(()=>{N.back()},1e3):(f.value=!1,b())},j=s=>q[s]||"未知权限",w=S(()=>i.value.power===1),X=S(()=>i.value.owned===1),Y=s=>s.sn!==i.value.sn&&w.value;return be(()=>{b()}),(s,t)=>{const ee=le,te=ae,se=ne,_=re,I=me,M=de,oe=ie;return n(),k("div",{class:"flex flex-col min-h-0 h-full p-5",onClick:T(G,["stop"])},[t[12]||(t[12]=o("div",{class:"text-xl font-bold"},"我的团队",-1)),o("div",Ce,[o("div",null,"团队成员(共"+U(y.value.length)+"人)",1),i.value.owned===1?(n(),m(u(C),{key:0,type:"primary",plain:"",onClick:H},{default:l(()=>t[4]||(t[4]=[x(" + 添加成员 ")])),_:1})):g("",!0)]),a(oe,{class:"mt-4 cursor-pointer flex-1 min-h-0",data:y.value,size:"large",height:"100%","row-class-name":"h-[70px]"},{default:l(()=>[a(_,{label:"成员信息",prop:"name","min-width":"160"},{default:l(({row:e})=>[o("div",Te,[a(ee,{src:e.avatar,size:"26",class:"flex-none"},null,8,["src"]),a(te,{class:"ml-2",content:e.nickname,teleported:!0,effect:"light"},null,8,["content"]),e.sn===i.value.sn?(n(),m(se,{key:0,type:"primary",size:"small",class:"ml-2"},{default:l(()=>t[5]||(t[5]=[x(" 我 ")])),_:1})):g("",!0)])]),_:1}),a(_,{label:"成员角色",prop:"role","min-width":"160"},{default:l(({row:e})=>[o("div",Ee,[e.owned===1?(n(),k("div",Ve,"所有者")):w.value&&i.value.sn!==e.sn?(n(),m(M,{key:1,placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:B(e)},{reference:l(()=>[o("div",{class:"flex items-center cursor-pointer",onClick:T(r=>z(e),["stop"])},[t[6]||(t[6]=o("span",{class:"text-base mr-1"},"成员",-1)),a(I,{name:"el-icon-ArrowDown"})],8,Ie)]),default:l(()=>[o("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-page px-2 rounded text-error",onClick:r=>R(e)}," 移出团队 ",8,Pe)]),_:2},1032,["visible"])):i.value.power>=1&&i.value.sn===e.sn?(n(),m(M,{key:2,placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:B(e)},{reference:l(()=>[o("div",{class:"flex items-center cursor-pointer",onClick:T(r=>z(e),["stop"])},[t[7]||(t[7]=o("span",{class:"text-base mr-1"},"成员",-1)),a(I,{name:"el-icon-ArrowDown"})],8,Se)]),default:l(()=>[o("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-page px-2 rounded text-error",onClick:r=>R(e)}," 退出团队 ",8,Me)]),_:2},1032,["visible"])):(n(),k("div",$e,"成员"))])]),_:1}),a(_,{label:"默认权限",prop:"permissions","min-width":"160"},{default:l(({row:e})=>[o("div",Ue,[e.owned===1?(n(),k("div",Ae,"全部")):w.value?(n(),m(M,{key:1,placement:"bottom-end",width:390,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:K(e)},{reference:l(()=>[o("div",{class:"flex items-center cursor-pointer",onClick:T(r=>J(e),["stop"])},[o("span",De,U(j(e.power)),1),a(I,{name:"el-icon-ArrowDown"})],8,Oe)]),default:l(()=>[o("div",Ne,[a(A,{label:"可编辑",description:"只能操作数据学习，增删改查自己的数据，不能修改他人",value:2,"model-value":h.value,onChange:r=>P(e,2)},null,8,["model-value","onChange"]),a(A,{label:"可查看",description:"查看知识库所有数据",value:3,"model-value":h.value,onChange:r=>P(e,3)},null,8,["model-value","onChange"]),w.value?(n(),m(A,{key:0,label:"可管理",description:"管理整个知识库数据和信息",value:1,"model-value":e.power,onChange:r=>P(e,1)},null,8,["model-value","onChange"])):g("",!0)])]),_:2},1032,["visible"])):(n(),k("div",ze,U(j(e.power)),1))])]),_:1}),a(_,{label:"加入时间",prop:"create_time","min-width":"150"}),X.value?(n(),m(_,{key:0,label:"操作","min-width":"200",fixed:"right"},{default:l(({row:e})=>[Y(e)?(n(),m(u(C),{key:0,type:"primary",link:"",onClick:r=>Q(e)},{default:l(()=>t[8]||(t[8]=[x(" 转移所有权 ")])),_:2},1032,["onClick"])):g("",!0)]),_:1})):g("",!0)]),_:1},8,["data"]),a(ye,{ref_key:"addUserModal",ref:D,id:E.value,onSuccess:b},null,8,["id"]),a(u(he),{modelValue:f.value,"onUpdate:modelValue":t[3]||(t[3]=e=>f.value=e),width:"450px",class:"!rounded-[12px]",center:"",draggable:"","destroy-on-close":"","close-on-click-modal":"false"},{header:l(()=>t[9]||(t[9]=[o("div",{class:"w-full text-left"},[o("div",{class:"text-lg font-medium"},"成员处理方式")],-1)])),footer:l(()=>[o("div",Be,[a(u(C),{onClick:t[2]||(t[2]=e=>f.value=!1)},{default:l(()=>t[10]||(t[10]=[x("取消")])),_:1}),a(u(C),{type:"primary",loading:V.value,onClick:W},{default:l(()=>t[11]||(t[11]=[x("确认 ")])),_:1},8,["loading"])])]),default:l(()=>[o("div",null,[a(u(L),{modelValue:c.value,"onUpdate:modelValue":t[0]||(t[0]=e=>c.value=e),"true-value":"all",label:"同步转移所有者的所有成员。",size:"large"},null,8,["modelValue"]),a(u(L),{modelValue:c.value,"onUpdate:modelValue":t[1]||(t[1]=e=>c.value=e),"true-value":"kb",label:"不同步转移所有者的所有成员，只转移知识库。",size:"large"},null,8,["modelValue"])])]),_:1},8,["modelValue"])])}}}),wt=we(Re,[["__scopeId","data-v-c7524e81"]]);export{wt as default};
