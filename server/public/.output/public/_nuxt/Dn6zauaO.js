import{I as h,J as y,K as b,M as g,N as S,O as $}from"./CmRxzTqw.js";import{b as E,E as I,c as B,l as p,M as n,N as u,a0 as w,u as s,Z as N,$ as m,t as P,_ as c,ao as k,V as v,a3 as T,W as _,aj as C}from"./CUZG7cWw.js";const M=(r,o=0)=>{if(o===0)return r;const a=E(!1);let t=0;const l=()=>{t&&clearTimeout(t),t=window.setTimeout(()=>{a.value=r.value},o)};return I(l),B(()=>r.value,e=>{e?l():a.value=e}),a},V=h({animated:{type:Boolean,default:!1},count:{type:Number,default:1},rows:{type:Number,default:3},loading:{type:Boolean,default:!0},throttle:{type:Number}}),z=h({variant:{type:String,values:["circle","rect","h1","h3","text","caption","p","image","button"],default:"text"}}),L=p({name:"ElSkeletonItem"}),R=p({...L,props:z,setup(r){const o=y("skeleton");return(a,t)=>(n(),u("div",{class:m([s(o).e("item"),s(o).e(a.variant)])},[a.variant==="image"?(n(),w(s(b),{key:0})):N("v-if",!0)],2))}});var i=g(R,[["__file","skeleton-item.vue"]]);const j=p({name:"ElSkeleton"}),F=p({...j,props:V,setup(r,{expose:o}){const a=r,t=y("skeleton"),l=M(P(a,"loading"),a.throttle);return o({uiLoading:l}),(e,J)=>s(l)?(n(),u("div",_({key:0,class:[s(t).b(),s(t).is("animated",e.animated)]},e.$attrs),[(n(!0),u(c,null,k(e.count,d=>(n(),u(c,{key:d},[e.loading?v(e.$slots,"template",{key:d},()=>[T(i,{class:m(s(t).is("first")),variant:"p"},null,8,["class"]),(n(!0),u(c,null,k(e.rows,f=>(n(),w(i,{key:f,class:m([s(t).e("paragraph"),s(t).is("last",f===e.rows&&e.rows>1)]),variant:"p"},null,8,["class"]))),128))]):N("v-if",!0)],64))),128))],16)):v(e.$slots,"default",C(_({key:1},e.$attrs)))}});var H=g(F,[["__file","skeleton.vue"]]);const W=S(H,{SkeletonItem:i});$(i);export{W as E};
