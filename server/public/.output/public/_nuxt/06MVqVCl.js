import{l as q,b as ie,r as ce,m as v,c as de,E as me,ar as pe,M as b,N as D,a7 as W,u as a,$ as x,af as E,V as J,a3 as K,a1 as Q,a0 as S,Z as Y,a4 as z,H as be}from"./CUZG7cWw.js";import{I as fe,aJ as ve,X as p,W as Ne,aK as ee,aQ as P,aG as y,ar as h,a5 as Ve,J as he,aM as ye,aR as _,Q as Ie,aL as ge,am as we,aS as Ee,aT as Se,H as X,aU as _e,aV as Pe,e as Ae,M as Fe,S as Ce,aP as Z,N as ke}from"./CmRxzTqw.js";import{v as j}from"./CtvQKSRC.js";const Me=fe({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:Number,readonly:<PERSON>olean,disabled:Boolean,size:ve,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:l=>l===null||p(l)||["min","max"].includes(l),default:null},name:String,label:String,placeholder:String,precision:{type:Number,validator:l=>l>=0&&l===Number.parseInt(`${l}`,10)},validateEvent:{type:Boolean,default:!0},...Ne(["ariaLabel"])}),Te={[ee]:(l,A)=>A!==l,blur:l=>l instanceof FocusEvent,focus:l=>l instanceof FocusEvent,[P]:l=>p(l)||y(l),[h]:l=>p(l)||y(l)},Be=["aria-label","onKeydown"],De=["aria-label","onKeydown"],xe=q({name:"ElInputNumber"}),Ke=q({...xe,props:Me,emits:Te,setup(l,{expose:A,emit:c}){const r=l,{t:L}=Ve(),d=he("input-number"),N=ie(),u=ce({currentValue:r.modelValue,userInput:null}),{formItem:f}=ye(),O=v(()=>p(r.modelValue)&&r.modelValue<=r.min),U=v(()=>p(r.modelValue)&&r.modelValue>=r.max),ne=v(()=>{const e=G(r.step);return _(r.precision)?Math.max(G(r.modelValue),e):(e>r.precision,r.precision)}),F=v(()=>r.controls&&r.controlsPosition==="right"),$=Ie(),V=ge(),C=v(()=>{if(u.userInput!==null)return u.userInput;let e=u.currentValue;if(y(e))return"";if(p(e)){if(Number.isNaN(e))return"";_(r.precision)||(e=e.toFixed(r.precision))}return e}),k=(e,n)=>{if(_(n)&&(n=ne.value),n===0)return Math.round(e);let t=String(e);const o=t.indexOf(".");if(o===-1||!t.replace(".","").split("")[o+n])return e;const g=t.length;return t.charAt(g-1)==="5"&&(t=`${t.slice(0,Math.max(0,g-1))}6`),Number.parseFloat(Number(t).toFixed(n))},G=e=>{if(y(e))return 0;const n=e.toString(),t=n.indexOf(".");let o=0;return t!==-1&&(o=n.length-t-1),o},H=(e,n=1)=>p(e)?k(e+r.step*n):u.currentValue,M=()=>{if(r.readonly||V.value||U.value)return;const e=Number(C.value)||0,n=H(e);I(n),c(P,u.currentValue),B()},T=()=>{if(r.readonly||V.value||O.value)return;const e=Number(C.value)||0,n=H(e,-1);I(n),c(P,u.currentValue),B()},R=(e,n)=>{const{max:t,min:o,step:s,precision:m,stepStrictly:g,valueOnClear:w}=r;t<o&&Ce("InputNumber","min should not be greater than max.");let i=Number(e);if(y(e)||Number.isNaN(i))return null;if(e===""){if(w===null)return null;i=be(w)?{min:o,max:t}[w]:w}return g&&(i=k(Math.round(i/s)*s,m)),_(m)||(i=k(i,m)),(i>t||i<o)&&(i=i>t?t:o,n&&c(h,i)),i},I=(e,n=!0)=>{var t;const o=u.currentValue,s=R(e);if(!n){c(h,s);return}o===s&&e||(u.userInput=null,c(h,s),o!==s&&c(ee,s,o),r.validateEvent&&((t=f==null?void 0:f.validate)==null||t.call(f,"change").catch(m=>Z())),u.currentValue=s)},ae=e=>{u.userInput=e;const n=e===""?null:Number(e);c(P,n),I(n,!1)},te=e=>{const n=e!==""?Number(e):"";(p(n)&&!Number.isNaN(n)||e==="")&&I(n),B(),u.userInput=null},re=()=>{var e,n;(n=(e=N.value)==null?void 0:e.focus)==null||n.call(e)},le=()=>{var e,n;(n=(e=N.value)==null?void 0:e.blur)==null||n.call(e)},ue=e=>{c("focus",e)},se=e=>{var n;u.userInput=null,c("blur",e),r.validateEvent&&((n=f==null?void 0:f.validate)==null||n.call(f,"blur").catch(t=>Z()))},B=()=>{u.currentValue!==r.modelValue&&(u.currentValue=r.modelValue)},oe=e=>{document.activeElement===e.target&&e.preventDefault()};return de(()=>r.modelValue,(e,n)=>{const t=R(e,!0);u.userInput===null&&t!==n&&(u.currentValue=t)},{immediate:!0}),me(()=>{var e;const{min:n,max:t,modelValue:o}=r,s=(e=N.value)==null?void 0:e.input;if(s.setAttribute("role","spinbutton"),Number.isFinite(t)?s.setAttribute("aria-valuemax",String(t)):s.removeAttribute("aria-valuemax"),Number.isFinite(n)?s.setAttribute("aria-valuemin",String(n)):s.removeAttribute("aria-valuemin"),s.setAttribute("aria-valuenow",u.currentValue||u.currentValue===0?String(u.currentValue):""),s.setAttribute("aria-disabled",String(V.value)),!p(o)&&o!=null){let m=Number(o);Number.isNaN(m)&&(m=null),c(h,m)}s.addEventListener("wheel",oe,{passive:!1})}),pe(()=>{var e,n;const t=(e=N.value)==null?void 0:e.input;t==null||t.setAttribute("aria-valuenow",`${(n=u.currentValue)!=null?n:""}`)}),we({from:"label",replacement:"aria-label",version:"2.8.0",scope:"el-input-number",ref:"https://element-plus.org/en-US/component/input-number.html"},v(()=>!!r.label)),A({focus:re,blur:le}),(e,n)=>(b(),D("div",{class:x([a(d).b(),a(d).m(a($)),a(d).is("disabled",a(V)),a(d).is("without-controls",!e.controls),a(d).is("controls-right",a(F))]),onDragstart:n[0]||(n[0]=z(()=>{},["prevent"]))},[e.controls?W((b(),D("span",{key:0,role:"button","aria-label":a(L)("el.inputNumber.decrease"),class:x([a(d).e("decrease"),a(d).is("disabled",a(O))]),onKeydown:E(T,["enter"])},[J(e.$slots,"decrease-icon",{},()=>[K(a(X),null,{default:Q(()=>[a(F)?(b(),S(a(Ee),{key:0})):(b(),S(a(Se),{key:1}))]),_:1})])],42,Be)),[[a(j),T]]):Y("v-if",!0),e.controls?W((b(),D("span",{key:1,role:"button","aria-label":a(L)("el.inputNumber.increase"),class:x([a(d).e("increase"),a(d).is("disabled",a(U))]),onKeydown:E(M,["enter"])},[J(e.$slots,"increase-icon",{},()=>[K(a(X),null,{default:Q(()=>[a(F)?(b(),S(a(_e),{key:0})):(b(),S(a(Pe),{key:1}))]),_:1})])],42,De)),[[a(j),M]]):Y("v-if",!0),K(a(Ae),{id:e.id,ref_key:"input",ref:N,type:"number",step:e.step,"model-value":a(C),placeholder:e.placeholder,readonly:e.readonly,disabled:a(V),size:a($),max:e.max,min:e.min,name:e.name,"aria-label":e.label||e.ariaLabel,"validate-event":!1,onKeydown:[E(z(M,["prevent"]),["up"]),E(z(T,["prevent"]),["down"])],onBlur:se,onFocus:ue,onInput:ae,onChange:te},null,8,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","aria-label","onKeydown"])],34))}});var ze=Fe(Ke,[["__file","input-number.vue"]]);const $e=ke(ze);export{$e as E};
