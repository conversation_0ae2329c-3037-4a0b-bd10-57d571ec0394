import{_ as v}from"./eFgaMLiC.js";import{_ as y}from"./BvSuqySp.js";import{E as V,a as g}from"./9CYoqqXX.js";import{E}from"./oVx59syQ.js";import{l as w,h as B}from"./CmRxzTqw.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{l as S,M as o,N as p,O as t,a3 as n,V as C,a0 as l,Z as _,a1 as r,u as M,_ as N,ao as I,a5 as L}from"./CUZG7cWw.js";import{_ as P}from"./DlAUqK2U.js";const $={class:"setting-aside p-4"},O={class:"flex flex-col h-full bg-body w-[180px] rounded-[12px]"},T={class:"px-[15px] pt-[15px]"},z={class:"flex items-center cursor-pointer"},D={class:"text-xl flex-1 min-w-0 ml-[10px]"},F={class:"mb-[10px]"},R=S({__name:"index",props:{modelValue:{default:""},menuList:{default:()=>[]},backPath:{},title:{default:""}},emits:["update:modelValue"],setup(u,{emit:Z}){const a=u,c=w(),i=B(a,"modelValue"),f=()=>{a.backPath?c.replace({path:a.backPath}):c.back()};return(s,m)=>{const d=v,x=y,b=V,h=g,k=E;return o(),p("div",$,[t("div",O,[t("div",T,[t("div",z,[t("div",{class:"flex bg-body p-[5px] text-bold rounded-[50%] text-primary shadow-light",onClick:f},[n(d,{name:"el-icon-Back",size:18})]),t("div",D,[C(s.$slots,"title",{},()=>[s.title?(o(),l(x,{key:0,content:s.title,teleported:!0,effect:"light"},null,8,["content"])):_("",!0)],!0)])])]),n(k,{class:"tab-lists w-full"},{default:r(()=>[t("div",F,[n(h,{"default-active":M(i),style:{border:"none"},router:!1,onSelect:m[0]||(m[0]=e=>i.value=e)},{default:r(()=>[(o(!0),p(N,null,I(s.menuList,e=>(o(),l(b,{key:e.key,index:e.key},{default:r(()=>[e.icon?(o(),l(d,{key:0,name:e.icon},null,8,["name"])):_("",!0),t("span",null,L(e.name),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"])])]),_:1})])])}}}),Y=P(R,[["__scopeId","data-v-80cd954f"]]);export{Y as _};
