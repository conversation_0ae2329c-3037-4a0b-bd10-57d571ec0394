import{a as he,_ as _e,b as ve}from"./CCGM0zxW.js";import{_ as ye}from"./eFgaMLiC.js";import{by as we,a as be,l as xe,b as ke,j as Ce,bz as Le,bA as Ee,cF as Re,_ as Se,f as N,E as Te}from"./CmRxzTqw.js";import{_ as $e}from"./DB7Ysqj9.js";import{E as Ue}from"./Do9LV2MU.js";import{u as Ne}from"./DAOx25wS.js";import{l as Me,j as ee,b as k,ai as j,r as Ae,E as De,k as ze,M as u,N as v,a3 as p,a1 as d,O as g,u as e,y as te,Z as M,_ as H,ao as J,a0 as Z,ag as Fe,a4 as Be,a5 as Ve,$ as qe,n as oe}from"./CUZG7cWw.js";import{u as G}from"./DNOp0HuO.js";import{u as Ie}from"./Bj_9-7Jh.js";import{b as Oe,e as ne,c as Pe}from"./DQUFgXGm.js";import je from"./CtdHkUcX.js";import{E as He}from"./oVx59syQ.js";import{_ as Je}from"./DlAUqK2U.js";import"./DCzKTodP.js";/* empty css        */import"./CH6wv3Pu.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./BYMcWg3Q.js";/* empty css        */import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        */import"./DjHPV-Am.js";import"./DoCT-qbH.js";/* empty css        */import"./DwFObZc_.js";import"./CRNANWso.js";import"./CHg9aK2B.js";import"./DecTOTC8.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./7tQUKVT9.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./Bf_xRNbS.js";import"./BOx_5T3X.js";/* empty css        *//* empty css        */import"./CaNlADry.js";import"./DdtGP7XX.js";import"./CbQsrhNE.js";function Ze(A){return $request.get({url:"/chat.skill/lists",params:A})}function Ge(A){return $request.get({url:"/chat.skill/detail",params:A})}const We={class:"h-full flex"},Ye={class:"p-[16px]"},Ke={class:"flex-1 min-w-0 pr-4 py-4"},Qe={class:"h-full flex flex-col bg-body rounded-[12px]"},Xe={class:"flex-1 min-h-0"},et={key:0,class:"px-[40px] pt-[40px]"},tt={class:"my-[5px]"},ot={key:0,class:"flex flex-col",style:{"margin-left":"52px"}},nt=["onClick"],rt={class:"mr-2 text-tx-primary"},at={class:"mb-[10px] px-[30px]"},st={class:"mr-[10px]"},it=Me({__name:"role",async setup(A){let c,C;const re=we(),{copy:ae}=Ne(),se=be(),W=xe(),x=ke(),y=Ce(),D=ee(),q=ee(),Y=k(),z=k(""),L=k(""),w=k(Number(se.query.id)),{data:F,refresh:K}=([c,C]=j(()=>G(()=>Ze({keyword:L.value}),{default(){return[]},lazy:!0,immediate:!1},"$sJo73HUy0r")),c=await c,C(),c),{data:E,refresh:Q}=([c,C]=j(()=>G(()=>Ge({id:w.value}),{lazy:!0,immediate:!1},"$FeZRwM0ASG")),c=await c,C(),c),{data:a,refresh:I}=([c,C]=j(()=>G(()=>Oe({type:3,other_id:w.value,page_type:0}),{transform(o){return o.lists||[]},default(){return[]},lazy:!0},"$xhlnmU1xdY")),c=await c,C(),c),h=Ae({show:!1,data:{url:"",name:"",type:"image"}}),ie=o=>{h.show=!!o.support_image,h.show||(h.data.url="")},O=({id:o,image:t})=>{W.push({path:"/dialogue/role",query:{id:o}}),w.value=Number(o),oe(async()=>{await I(),E.value={image:t},await Q(),V()})},le=async()=>{if(!y.isLogin)return y.toggleShowLogin();await N.confirm("确定清空记录？"),await ne({other_id:w.value,type:3}),I()},B=k(-1),{lockFn:ce}=Ie(async()=>{const o=a.value[a.value.length-1],t=a.value.find(({id:i})=>i===o.id);t&&(B.value=o.id,a.value.splice(a.value.length-2,2),R(t.content))}),ue=()=>{var o;if(!y.isLogin)return(o=D.value)==null||o.blur(),y.toggleShowLogin();V()};let n=null;const b=k(!1),P=k([]),R=async(o,t="input")=>{var S;if(!y.isLogin)return y.toggleShowLogin();if(!o)return N.msgError("请输入问题");if(b.value)return;const i=Date.now();b.value=!0,a.value.push({type:1,content:o,files_plugin:[{...h.data}]}),a.value.push({type:2,typing:!0,content:[""],key:i}),(S=D.value)==null||S.setInputValue();const s=a.value.find(l=>l.key===i);n=Pe({type:3,other_id:w.value,question:o,model:z.value,file:h.data.url}),n.addEventListener("chat",({data:l})=>{const{data:_,index:m}=l;s.content[m]||(s.content[m]=""),s.content[m]+=_}),n.addEventListener("question",({data:l})=>{P.value=JSON.parse(l.data)}),n.addEventListener("finish",({data:l})=>{const{data:_,index:m}=l;_&&(s.content[m]+=_),h.data.url=""}),n.addEventListener("close",async()=>{B.value!==-1&&s.content[0].length&&(await ne({type:1,id:B.value}),B.value=-1),await y.getUser(),setTimeout(async()=>{await I(),b.value=!1,s.typing=!1,await oe(),V()},600)}),n.addEventListener("error",async l=>{var _,m;if(t==="input"&&((_=D.value)==null||_.setInputValue(o)),((m=l.data)==null?void 0:m.code)===1100){x.getIsShowRecharge?(await N.confirm(`${x.getTokenUnit}数量已用完，请前往充值`),W.push("/user/recharge")):N.msgError(`${x.getTokenUnit}数量已用完。请联系客服增加`);return}l.errorType==="connectError"&&N.msgError("请求失败，请重试"),["connectError","responseError"].includes(l.errorType)&&a.value.splice(a.value.length-2,2),s.typing=!1,setTimeout(()=>{b.value=!1},200)})},V=async()=>{var t,i,s;const o=(i=(t=q.value)==null?void 0:t.wrapRef)==null?void 0:i.scrollHeight;(s=q.value)==null||s.setScrollTop(o)},{height:pe}=Le(Y);Ee(pe,()=>{b.value&&V()},{immediate:!0});const de=()=>{n==null||n.removeEventListener("chat"),n==null||n.removeEventListener("close"),n==null||n.removeEventListener("error"),n==null||n.removeEventListener("finish"),n==null||n.abort(),b.value=!1},me=()=>{if(!w.value)O(F.value[0].skill[0]);else for(let o=0;o<F.value.length;o++){const t=F.value[o];for(let i=0;i<t.skill.length;i++){const s=t.skill[i];if(s.id===w.value){O(s);return}}}};return Re(L,o=>{K()},{debounce:500}),De(async()=>{await K(),me(),await Q()}),ze(()=>{de()}),(o,t)=>{const i=he,s=_e,S=ye,l=Te,_=$e,m=ve,fe=Ue,ge=Se;return u(),v("div",null,[p(ge,{name:"default"},{default:d(()=>[g("div",We,[g("div",Ye,[p(je,{keyword:e(L),"onUpdate:keyword":t[0]||(t[0]=f=>te(L)?L.value=f:null),sidebarList:e(F),currentId:e(w),onOntoggle:O},null,8,["keyword","sidebarList","currentId"])]),g("div",Ke,[p(fe,{class:"h-full",content:e(x).getChatConfig.watermark,font:{color:e(re)?"rgba(256,256,256,0.08)":"rgba(0,0,0,0.06)",fontSize:12}},{default:d(()=>[g("div",Qe,[g("div",Xe,[p(e(He),{ref_key:"scrollbarRef",ref:q},{default:d(()=>{var f;return[g("div",null,[!e(a).length&&((f=e(E))!=null&&f.tips)?(u(),v("div",et,[p(s,{type:"left",avatar:e(E).image,bg:"var(--el-bg-color-page)"},{default:d(()=>{var r;return[p(i,{content:(r=e(E))==null?void 0:r.tips,type:"html",typing:!1,"line-numbers":!e(x).isMobile,"show-collect-btn":!1,"show-copy-btn":!1,"show-poster":!1,"show-voice":!1,class:"mb-[15px] last-of-type:mb-0",onClickCustomLink:t[1]||(t[1]=T=>R(T,"link"))},null,8,["content","line-numbers"])]}),_:1},8,["avatar"])])):M("",!0),e(a).length?(u(),v("div",{key:1,ref_key:"innerRef",ref:Y,class:"px-8"},[(u(!0),v(H,null,J(e(a),(r,T)=>{var X;return u(),v("div",{key:r.id+""+T,class:"mt-4 sm:pb-[20px]"},[r.type==1?(u(),Z(s,{key:0,type:"right",avatar:e(y).userInfo.avatar,color:"white"},{actions:d(()=>[g("div",tt,[p(l,{link:"",type:"info",onClick:$=>e(ae)(r.content)},{icon:d(()=>[p(S,{name:"el-icon-CopyDocument"})]),default:d(()=>[t[6]||(t[6]=Fe(" 复制 "))]),_:2},1032,["onClick"])])]),default:d(()=>[p(i,{content:r.content,"files-plugin":r.files_plugin},null,8,["content","files-plugin"])]),_:2},1032,["avatar"])):M("",!0),r.type==2?(u(),Z(s,{key:1,type:"left",avatar:(X=e(E))==null?void 0:X.image,time:r.create_time,bg:"var(--el-bg-color-page)",modelName:r.model},{outer_actions:d(()=>[T===e(a).length-1&&!e(b)?(u(),v("div",ot,[(u(!0),v(H,null,J(e(P).length?e(P):r.correlation,($,U)=>(u(),v("div",{key:U,class:"inline-flex items-center rounded-[12px] bg-page cursor-pointer mt-[10px] hover:bg-primary-light-9",style:{padding:"8px 12px",width:"fit-content"},onClick:Be(lt=>R($,"input"),["stop"])},[g("span",rt,Ve($),1),p(S,{name:"el-icon-Right",color:"#999",size:"20"})],8,nt))),128))])):M("",!0)]),default:d(()=>[(u(!0),v(H,null,J(r.content,($,U)=>(u(),Z(i,{key:U,content:$,type:"html",typing:r.typing,"line-numbers":!e(x).isMobile,"show-rewrite":T===e(a).length-1,"show-copy":"","show-voice":e(x).getIsVoiceOpen,class:qe(["mb-[15px] last-of-type:mb-0",{"pt-[15px] border-t border-solid border-br-light":U>0}]),index:U,"record-id":r.id,"show-poster":"","record-list":e(a),onRewrite:e(ce)},null,8,["content","typing","line-numbers","show-rewrite","show-voice","class","index","record-id","record-list","onRewrite"]))),128))]),_:2},1032,["avatar","time","modelName"])):M("",!0)])}),128))],512)):M("",!0)])]}),_:1},512)]),g("div",at,[p(m,{ref_key:"chatActionRef",ref:D,loading:e(b),"show-continue":e(a).length,"show-file-upload":e(h).show,"file-plugin":e(h).data,"onUpdate:filePlugin":t[3]||(t[3]=f=>e(h).data=f),onEnter:R,onClear:le,onPause:t[4]||(t[4]=f=>{var r;return(r=e(n))==null?void 0:r.abort()}),onFocus:ue,onContinue:t[5]||(t[5]=f=>R("继续","btn"))},{btn:d(()=>[g("div",st,[p(_,{class:"min-w-[280px] select-class",sub_id:e(z),"onUpdate:sub_id":t[2]||(t[2]=f=>te(z)?z.value=f:null),"onUpdate:modelConfig":ie},null,8,["sub_id","onUpdate:modelConfig"])])]),_:1},8,["loading","show-continue","show-file-upload","file-plugin"])])])]),_:1},8,["content","font"])])])]),_:1})])}}}),po=Je(it,[["__scopeId","data-v-6c59986f"]]);export{po as default};
