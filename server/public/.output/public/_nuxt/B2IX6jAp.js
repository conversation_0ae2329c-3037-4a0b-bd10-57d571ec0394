import{E as V}from"./CmRxzTqw.js";import{a as F}from"./B7GaOiDz.js";import{E as j}from"./oVx59syQ.js";import{E as C,a as O}from"./DHUC3PVh.js";import{E as U}from"./llRQJmEG.js";/* empty css        *//* empty css        */import{u as A,T as E}from"./DymDsCmz.js";import{_ as B}from"./Cw-OnHz-.js";import N from"./7h9ARPvN.js";import{e as w}from"./BhXe-NXN.js";import{c as u}from"./Bs9Zhtqd.js";import{l as I,r as x,m as P,c as k,M as y,N as T,O as p,a3 as r,a1 as a,u as e,ag as X}from"./CUZG7cWw.js";import{_ as D}from"./DlAUqK2U.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./DoCT-qbH.js";import"./CiYvFM4x.js";import"./DY7CbrCZ.js";import"./Bfmn7p7A.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./DCzKTodP.js";import"./D8e5izeA.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./CXsrG8JM.js";import"./BscXL5XZ.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./C9f7n97H.js";import"./Bv29pan0.js";import"./eFgaMLiC.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";/* empty css        *//* empty css        */import"./DdtGP7XX.js";const H={class:"avatar-select h-full flex flex-col"},K={class:"px-main pt-main"},L={key:0,class:"mt-[5px] flex-1 min-h-0"},M={class:"h-full"},W={class:"p-main"},$={class:"h-full"},q={class:"p-main"},G={key:1,class:"p-main"},J=I({__name:"text",setup(Q){const n=A(),_=x({currentTab:"setting"}),b={fontSize:64,fontFamily:"Alibaba PuHuiTi",fill:"#ffffff",stroke:"",effect:{name:"",server_key:"",type:"",url:""}},o=x(u(b)),v=P(()=>{var s;return((s=n.activeObject)==null?void 0:s.customType)===E.TEXT}),z=()=>{_.currentTab="setting";const s=u(b);n.addText("这里是文字",E.TEXT,s)};k(()=>n.activeObject,s=>{var t;if(v.value)for(const l in o)o[l]=(t=s==null?void 0:s.data)==null?void 0:t[l];else Object.assign(o,u(b))},{immediate:!0});const S=["fontSize","fontFamily","fill","stroke"];return k(()=>o,s=>{var l,d,f;if(!v.value)return;for(const m in o)if(S.includes(m)){let c=s[m];m==="fontSize"&&(c=n.calcFontSize(c)),(l=n.activeObject)==null||l.set(m,c)}const t=u(o);(d=n.activeObject)==null||d.set("data",t),(f=n.canvas)==null||f.renderAll()},{deep:!0}),(s,t)=>{const l=V,d=F,f=j,m=O,c=C,g=U;return y(),T("div",H,[p("div",K,[r(l,{type:"primary",class:"w-full",size:"large",onClick:z},{default:a(()=>t[6]||(t[6]=[X(" 添加文字 ")])),_:1})]),e(v)?(y(),T("div",L,[r(c,{modelValue:e(_).currentTab,"onUpdate:modelValue":t[5]||(t[5]=i=>e(_).currentTab=i),stretch:""},{default:a(()=>[r(m,{name:"setting"},{label:a(()=>t[7]||(t[7]=[p("span",{class:"el-tab__label-text"},"文字设置",-1)])),default:a(()=>[p("div",M,[r(f,null,{default:a(()=>[p("div",W,[r(d,null,{default:a(()=>[r(B,{font:e(o).fontFamily,"onUpdate:font":t[0]||(t[0]=i=>e(o).fontFamily=i),"font-size":e(o).fontSize,"onUpdate:fontSize":t[1]||(t[1]=i=>e(o).fontSize=i),"font-color":e(o).fill,"onUpdate:fontColor":t[2]||(t[2]=i=>e(o).fill=i),"stroke-color":e(o).stroke,"onUpdate:strokeColor":t[3]||(t[3]=i=>e(o).stroke=i)},null,8,["font","font-size","font-color","stroke-color"])]),_:1})])]),_:1})])]),_:1}),r(m,{name:"special"},{label:a(()=>t[8]||(t[8]=[p("span",{class:"el-tab__label-text"},"文字特效",-1)])),default:a(()=>[p("div",$,[r(f,null,{default:a(()=>[p("div",q,[r(N,{modelValue:e(o).effect,"onUpdate:modelValue":t[4]||(t[4]=i=>e(o).effect=i)},null,8,["modelValue"])])]),_:1})])]),_:1})]),_:1},8,["modelValue"])])):(y(),T("div",G,[r(g,{image:e(w),description:"请添加文字，或在右侧选中文字～"},null,8,["image"])]))])}}}),Mt=D(J,[["__scopeId","data-v-002da2fb"]]);export{Mt as default};
