import{_ as O}from"./eFgaMLiC.js";import{E as P}from"./DNRqakyH.js";import{E as Q}from"./Zz2DnF66.js";import{h as Z,f as G,e as H,E as J,v as K}from"./CmRxzTqw.js";import{E as W}from"./oVx59syQ.js";import"./DikNcrXK.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{isSameFile as X}from"./BiHhwkbt.js";import{b as I,c as Y,d as ee}from"./CyoNPmdv.js";import{s as te}from"./C4qLKnCc.js";import{_ as oe}from"./CoWsWLh1.js";import{l as ae,b as f,j as se,M as d,N as p,a7 as le,u as s,a3 as l,a1 as x,O as o,_ as D,ao as L,y as ne,a5 as k,Z as ie,ag as $,$ as ce}from"./CUZG7cWw.js";import{_ as re}from"./DlAUqK2U.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./DCTLXrZ8.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";const de={class:"h-full flex flex-col min-h-0"},pe={class:"py-[16px]"},me={class:"el-upload__text"},ue={class:"el-upload__text"},_e={key:0,class:"grid grid-cols-2 gap-4 flex-1 min-h-[500px]"},fe={style:{"border-right":"1px solid #eeeeee"}},ve={class:"mt-4 max-w-[500px]"},he=["onClick"],xe={class:"ml-2"},ge={class:"closeIcon ml-auto opacity-0 transition duration-300 flex items-center"},we={class:"mt-4"},ke={class:"flex"},be={class:"mt-2"},ye={class:"flex flex-col"},Ce={class:"text-lg"},Ee={class:"flex-auto mt-2 h-[100px]"},Ve=ae({__name:"doc",props:{modelValue:{}},emits:["update:modelValue"],setup(N,{expose:S,emit:U}){const n=Z(N,"modelValue",U),b=[".txt","doc",".docx",".pdf",".md"],g=b.join(", "),c=f([]),y=se(),w=f(!1),m=f(-1);f("");const v=f(512),B=async({raw:e})=>{var t,a;try{if(e){const i="."+((t=e.name.split(".").pop())==null?void 0:t.toLowerCase());if(!b.includes(i))throw`不支持的文件类型，请上传 ${g} 格式的文件`;w.value=!0,await X(e,c.value);const h=await R(e);if(!h)throw"解析结果为空，已自动忽略";n.value.push({name:e.name,path:"",data:[]}),e.data=h,c.value.push(e),E(c.value.length-1),C()}}catch(i){G.msgError(i)}finally{w.value=!1,(a=y.value)==null||a.clearFiles()}},C=()=>{n.value.forEach(e=>{e.data=[];const t=c.value.findIndex(i=>i.name==e.name);te({text:c.value[t].data,chunkLen:v.value}).forEach(i=>{e.data.push({q:i,a:""})})})},R=async e=>{const t=e.name.substring(e.name.lastIndexOf(".")+1);let a="";switch(t){case"md":case"txt":a=await I(e);break;case"pdf":a=await ee(e);break;case"doc":case"docx":a=await Y(e);break;default:a=await I(e);break}return a},T=async e=>{n.value[m.value].data.splice(e,1)},q=e=>{n.value.splice(e,1),c.value.splice(e,1)},E=e=>{m.value=e};return S({clearFiles:()=>{c.value=[]}}),(e,t)=>{var V;const a=O,i=P,h=Q,j=H,z=J,A=W,M=K;return d(),p("div",de,[le((d(),p("div",pe,[l(i,{ref_key:"uploadRef",ref:y,drag:"","on-change":B,"auto-upload":!1,"show-file-list":!1,accept:s(g),multiple:!0,limit:50},{default:x(()=>[o("div",me,[l(a,{name:"el-icon-Upload"}),t[1]||(t[1]=$(" 拖拽文件至此，或点击")),t[2]||(t[2]=o("em",null," 选择文件 ",-1))]),o("div",ue,"支持 "+k(s(g))+" 文件",1)]),_:1},8,["accept"])])),[[M,s(w)]]),s(n).length>0?(d(),p("div",_e,[o("div",fe,[o("div",ve,[(d(!0),p(D,null,L(s(n),(u,r)=>(d(),p("div",{key:r,class:ce(["fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer hover:bg-page transition duration-300",{"bg-page":s(m)==r}]),onClick:_=>E(r)},[l(a,{name:"el-icon-Folder",size:16,color:"#ffc94d"}),o("div",xe,k(u.name),1),o("div",ge,[l(a,{name:"el-icon-DeleteFilled",onClick:_=>q(r)},null,8,["onClick"])])],10,he))),128))]),o("div",we,[o("div",ke,[t[3]||(t[3]=o("div",null,"分段长度",-1)),l(h,{content:"按结束符号进行分段。我们建议您的文档应合理的使用标点符号，以确保每个完整的句子长度不要超过该值中文文档建议400~1000英文文档建议600~1200",placement:"top"},{default:x(()=>[o("span",null,[l(a,{name:"el-icon-QuestionFilled"})])]),_:1})]),l(j,{class:"mt-2 !w-[300px]",modelValue:s(v),"onUpdate:modelValue":t[0]||(t[0]=u=>ne(v)?v.value=u:null)},null,8,["modelValue"]),o("div",be,[l(z,{type:"primary",onClick:C},{default:x(()=>t[4]||(t[4]=[$("重新预览")])),_:1})])])]),o("div",ye,[o("div",Ce," 分段预览（"+k((V=s(n)[s(m)])==null?void 0:V.data.length)+"组） ",1),o("div",Ee,[l(A,{height:"100%"},{default:x(()=>{var u;return[(d(!0),p(D,null,L((u=s(n)[s(m)])==null?void 0:u.data,(r,_)=>(d(),p("div",{class:"bg-page rounded p-[10px] mt-2",key:_},[l(oe,{index:_,name:s(n)[s(m)].name,data:r.q,"onUpdate:data":F=>r.q=F,onDelete:F=>T(_)},null,8,["index","name","data","onUpdate:data","onDelete"])]))),128))]}),_:1})])])])):ie("",!0)])}}}),We=re(Ve,[["__scopeId","data-v-b76b91ef"]]);export{We as default};
