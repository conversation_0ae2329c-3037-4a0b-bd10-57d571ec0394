import{_ as x}from"./eFgaMLiC.js";import{l as g,a as v,b,j as k,h as y}from"./CmRxzTqw.js";import{l as S,b as p,c as V,M as c,N as l,O as o,_ as z,ao as B,u as m,a7 as C,a8 as I,$,a3 as M,a5 as N}from"./CUZG7cWw.js";import{_ as R}from"./DlAUqK2U.js";const j={class:"h-full relative"},D={class:"w-[200px] bg-body rounded-[12px] h-full px-[16px] py-[20px]"},P={class:"mt-[10px]"},A=["onClick"],E={class:"ml-[10px]"},F=S({__name:"sidePop",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(h,{emit:d}){const _=g(),f=v(),e=b();k();const u=p(0);y(h,"modelValue",d);const a=p([{name:"充值中心",icon:"chongzhi",path:"/user/recharge",show:(e==null?void 0:e.getIsShowRecharge)||!1},{name:"会员中心",icon:"open_vip",path:"/user/member",show:(e==null?void 0:e.getIsShowMember)||!1},{name:"分销推广",icon:"distribution",path:"/user/promotion/distribution",show:!0},{name:"任务奖励",icon:"task_reward",path:"/user/task_reward",show:!0},{name:"购买记录",icon:"goumaijilu",path:"/user/record",show:!0},{name:"我的作品",icon:"my_works",path:"/user/works",show:!0},{name:"余额明细",icon:"yuemingxi",path:"/user/balance",show:!0},{name:"消息通知",icon:"notice",path:"/user/notification",show:!0},{name:"个人信息",icon:"gerenzhongxin",path:"/user/center",show:!0}]),w=s=>{_.push(a.value[s].path)};return V(()=>f.path,s=>{console.log(s);const t=a.value.findIndex(n=>s.includes(n.path));u.value=t},{immediate:!0}),(s,t)=>{const n=x;return c(),l("div",j,[o("div",D,[t[0]||(t[0]=o("div",{class:"text-base"},"个人中心",-1)),o("div",P,[(c(!0),l(z,null,B(m(a),(r,i)=>C((c(),l("div",{class:"py-[10px] cursor-pointer",key:i,onClick:U=>w(i)},[o("div",{class:$([{isSelect:m(u)==i},"flex items-center h-[40px] leading-[40px] w-full pl-[15px]"])},[M(n,{name:`local-icon-${r.icon}`,size:"16px"},null,8,["name"]),o("span",E,N(r.name),1)],2)],8,A)),[[I,r.show]])),128))])])])}}}),K=R(F,[["__scopeId","data-v-4b6b8600"]]);export{K as default};
