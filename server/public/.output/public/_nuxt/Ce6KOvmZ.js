import{i as e,f as o}from"./CmRxzTqw.js";import{e as a,f as n,h as r,p as d,i as b,j as c,k as h,l as S,m as g}from"./qRM0tN96.js";const R=e({id:"robot",state:()=>({robotId:"",robotLists:[],sessionId:"",sessionLists:[]}),getters:{getCurrentSession:s=>s.sessionLists.find(t=>String(t.id)===s.sessionId)||{},getCurrentApp:s=>s.robotLists.find(t=>String(t.id)===s.robotId)||{}},actions:{setRobotId(s=""){this.robotId=String(s)},async getRobot(s){s=s||{page_type:0};const t=await a(s);if(this.robotLists=t.lists,this.robotLists.length>0){const[i]=this.robotLists;this.setRobotId(i.id)}return t},async delRobot(s){await o.confirm("确定删除？"),await n({id:s})},async cancelShareRobot(s){await o.confirm("确定取消分享吗？"),await r({id:s})},async addRobot(){const{id:s}=await d();return this.setRobotId(s),s},setSessionId(s=""){this.sessionId=String(s)},setSessionSelect(s){s||([s]=this.sessionLists),this.setSessionId((s==null?void 0:s.id)||"")},async getSessionLists(){const s=await b({robot_id:this.robotId});return this.sessionLists=s,this.sessionLists},async sessionAdd(){await c({robot_id:this.robotId}),await this.getSessionLists(),this.setSessionSelect()},async sessionEdit(s){await h({...s,robot_id:this.robotId}),await this.getSessionLists()},async sessionClear(){await S({robot_id:this.robotId}),await this.getSessionLists(),this.setSessionSelect()},async sessionDelete(s){await g({id:s,robot_id:this.robotId}),await this.getSessionLists(),this.setSessionSelect()}}});export{R as u};
