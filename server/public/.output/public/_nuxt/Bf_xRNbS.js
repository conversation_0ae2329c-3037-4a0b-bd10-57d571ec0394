import{I as x,ar as H,aK as S,X as P,V as A,a2 as j,J as w,M as T,aE as W,a8 as q,H as z,N as G,O as J}from"./CmRxzTqw.js";import{H as L,I as U,b as y,c as X,q as Q,m as r,l as _,M as B,N as D,V as K,$ as m,u as e,i as R,O as E,a3 as N,a1 as g,af as Y,a4 as Z,ag as ee,a5 as se,a7 as ae,a8 as te}from"./CUZG7cWw.js";import{c as $}from"./D6yUe_Nr.js";import{_ as le}from"./BOx_5T3X.js";const V=s=>P(s)||L(s)||U(s),oe=x({accordion:Boolean,modelValue:{type:A([Array,String,Number]),default:()=>j([])}}),ne={[H]:V,[S]:V},M=Symbol("collapseContextKey"),ie=(s,n)=>{const t=y($(s.modelValue)),i=l=>{t.value=l;const o=s.accordion?t.value[0]:t.value;n(H,o),n(S,o)},a=l=>{if(s.accordion)i([t.value[0]===l?"":l]);else{const o=[...t.value],c=o.indexOf(l);c>-1?o.splice(c,1):o.push(l),i(o)}};return X(()=>s.modelValue,()=>t.value=$(s.modelValue),{deep:!0}),Q(M,{activeNames:t,handleItemClick:a}),{activeNames:t,setActiveNames:i}},ce=()=>{const s=w("collapse");return{rootKls:r(()=>s.b())}},re=_({name:"ElCollapse"}),de=_({...re,props:oe,emits:ne,setup(s,{expose:n,emit:t}){const i=s,{activeNames:a,setActiveNames:l}=ie(i,t),{rootKls:o}=ce();return n({activeNames:a,setActiveNames:l}),(c,u)=>(B(),D("div",{class:m(e(o))},[K(c.$slots,"default")],2))}});var ue=T(de,[["__file","collapse.vue"]]);const pe=x({title:{type:String,default:""},name:{type:A([String,Number]),default:void 0},disabled:Boolean}),me=s=>{const n=R(M),{namespace:t}=w("collapse"),i=y(!1),a=y(!1),l=W(),o=r(()=>l.current++),c=r(()=>{var C;return(C=s.name)!=null?C:`${t.value}-id-${l.prefix}-${e(o)}`}),u=r(()=>n==null?void 0:n.activeNames.value.includes(e(c)));return{focusing:i,id:o,isActive:u,handleFocus:()=>{setTimeout(()=>{a.value?a.value=!1:i.value=!0},50)},handleHeaderClick:()=>{s.disabled||(n==null||n.handleItemClick(e(c)),i.value=!1,a.value=!0)},handleEnterClick:()=>{n==null||n.handleItemClick(e(c))}}},ve=(s,{focusing:n,isActive:t,id:i})=>{const a=w("collapse"),l=r(()=>[a.b("item"),a.is("active",e(t)),a.is("disabled",s.disabled)]),o=r(()=>[a.be("item","header"),a.is("active",e(t)),{focusing:e(n)&&!s.disabled}]),c=r(()=>[a.be("item","arrow"),a.is("active",e(t))]),u=r(()=>a.be("item","wrap")),v=r(()=>a.be("item","content")),f=r(()=>a.b(`content-${e(i)}`)),b=r(()=>a.b(`head-${e(i)}`));return{arrowKls:c,headKls:o,rootKls:l,itemWrapperKls:u,itemContentKls:v,scopedContentId:f,scopedHeadId:b}},fe=["id","aria-expanded","aria-controls","aria-describedby","tabindex"],be=["id","aria-hidden","aria-labelledby"],Ce=_({name:"ElCollapseItem"}),he=_({...Ce,props:pe,setup(s,{expose:n}){const t=s,{focusing:i,id:a,isActive:l,handleFocus:o,handleHeaderClick:c,handleEnterClick:u}=me(t),{arrowKls:v,headKls:f,rootKls:b,itemWrapperKls:C,itemContentKls:F,scopedContentId:I,scopedHeadId:k}=ve(t,{focusing:i,isActive:l,id:a});return n({isActive:l}),(h,d)=>(B(),D("div",{class:m(e(b))},[E("button",{id:e(k),class:m(e(f)),"aria-expanded":e(l),"aria-controls":e(I),"aria-describedby":e(I),tabindex:h.disabled?-1:0,type:"button",onClick:d[0]||(d[0]=(...p)=>e(c)&&e(c)(...p)),onKeydown:d[1]||(d[1]=Y(Z((...p)=>e(u)&&e(u)(...p),["stop","prevent"]),["space","enter"])),onFocus:d[2]||(d[2]=(...p)=>e(o)&&e(o)(...p)),onBlur:d[3]||(d[3]=p=>i.value=!1)},[K(h.$slots,"title",{},()=>[ee(se(h.title),1)]),N(e(z),{class:m(e(v))},{default:g(()=>[N(e(q))]),_:1},8,["class"])],42,fe),N(e(le),null,{default:g(()=>[ae(E("div",{id:e(I),role:"region",class:m(e(C)),"aria-hidden":!e(l),"aria-labelledby":e(k)},[E("div",{class:m(e(F))},[K(h.$slots,"default")],2)],10,be),[[te,e(l)]])]),_:3})],2))}});var O=T(he,[["__file","collapse-item.vue"]]);const ye=G(ue,{CollapseItem:O}),Ke=J(O);export{Ke as E,ye as a};
