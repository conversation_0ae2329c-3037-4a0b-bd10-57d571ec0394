import{a as d}from"./9CYoqqXX.js";import{b as f,a as v}from"./CmRxzTqw.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import k from"./aUpreUFZ.js";import{l as w,m as r,M as n,N as c,a3 as g,a1 as M,u as a,_ as S,ao as x,a0 as B}from"./CUZG7cWw.js";import{_ as C}from"./DlAUqK2U.js";import"./BOx_5T3X.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./Ddo5WWE5.js";import"./mBG0LxMu.js";import"./CbQsrhNE.js";import"./DecTOTC8.js";const H={class:"menu"},I=w({__name:"menu",props:{isHome:{type:Boolean}},setup(m){const u=m,s=f(),l=r(()=>{var e;return((e=s.getHeaderConfig.nav)==null?void 0:e.filter(p=>p.isShow))||[]}),h=r(()=>s.getHeaderConfig.isShowIcon&&!u.isHome),t=v(),i=r(()=>{const e=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.parentPath||t.meta.activePath||e});return(e,p)=>{const _=d;return n(),c("div",H,[g(_,{"default-active":a(i),mode:"horizontal"},{default:M(()=>[(n(!0),c(S,null,x(a(l),o=>(n(),B(k,{key:o.id,item:o,"is-show-icon":a(h),path:o.link.path,"is-active":a(i)===o.link.path},null,8,["item","is-show-icon","path","is-active"]))),128))]),_:1},8,["default-active"])])}}}),G=C(I,[["__scopeId","data-v-10dc3ac7"]]);export{G as default};
