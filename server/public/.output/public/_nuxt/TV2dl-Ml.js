import{E as B,a as C}from"./CXDY_LVT.js";import{j as w,E}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{g as F,s as V}from"./DjwCd26w.js";import{P as L}from"./CaNlADry.js";import{u as S}from"./Bj_9-7Jh.js";import{l as N,j as R,b as T,r as j,M as i,N as d,a3 as l,a1 as r,u as t,O as u,ag as U,_ as A,ao as D,a0 as O}from"./CUZG7cWw.js";import{_ as P}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */const z={class:"share-popup"},I={class:"h-[100px]"},M={class:"dialog-footer flex justify-center pb-2"},q=N({__name:"robot-share",emits:["success","close"],setup(G,{expose:f,emit:_}){const g=w(),c=_,n=R(),p=T([]),s=j({cate_id:"",id:""}),h=async()=>{try{const e=await F();e.unshift({name:"全部",id:""}),p.value=e}catch(e){console.log("获取视频分类失败=>",e)}},{lockFn:m,isLock:x}=S(async()=>{var e;await V(s),await g.getUser(),(e=n.value)==null||e.close(),c("success",s.id)}),y=()=>{c("close")};return f({open:e=>{var o;h(),(o=n.value)==null||o.open(),s.id=e}}),(e,o)=>{const k=B,v=C,b=E;return i(),d("div",z,[l(L,{ref_key:"popupRef",ref:n,title:"分享至广场",async:!0,width:"400px",center:!0,cancelButtonText:"",confirmButtonText:"",appendToBody:!1,onConfirm:t(m),onClose:y},{footer:r(()=>[u("div",M,[l(b,{type:"primary",loading:t(x),class:"!rounded-md",onClick:t(m)},{default:r(()=>o[1]||(o[1]=[U(" 分享至广场 ")])),_:1},8,["loading","onClick"])])]),default:r(()=>[u("div",I,[l(v,{size:"large",class:"w-[360px]",modelValue:t(s).cate_id,"onUpdate:modelValue":o[0]||(o[0]=a=>t(s).cate_id=a),placeholder:"全部",style:{"--el-fill-color-blank":"#F7F7FB"}},{default:r(()=>[(i(!0),d(A,null,D(t(p),a=>(i(),O(k,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["onConfirm"])])}}}),xe=P(q,[["__scopeId","data-v-9e735ed7"]]);export{xe as default};
