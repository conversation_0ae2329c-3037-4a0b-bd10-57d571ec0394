import{I as W,V as m,J as z,bs as A,bK as O,b4 as H,bL as R,R as U,H as J,M as K,N as Z}from"./CmRxzTqw.js";import{l as T,m as r,U as j,H as P,M as l,N as c,$ as i,u as t,O as y,a6 as f,V as B,a5 as I,Z as C,a0 as D,a1 as q,a2 as G}from"./CUZG7cWw.js";const Q=W({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:u=>u>=0&&u<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:{type:Boolean,default:!1},duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:m(String),default:"round"},textInside:{type:<PERSON>olean,default:!1},width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:m([String,Array,Function]),default:""},striped:Boolean,stripedFlow:Boolean,format:{type:m(Function),default:u=>`${u}%`}}),X=["aria-valuenow"],Y={viewBox:"0 0 100 100"},ee=["d","stroke","stroke-linecap","stroke-width"],te=["d","stroke","opacity","stroke-linecap","stroke-width"],se={key:0},ae=T({name:"ElProgress"}),re=T({...ae,props:Q,setup(u){const s=u,b={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},a=z("progress"),_=r(()=>({width:`${s.percentage}%`,animationDuration:`${s.duration}s`,background:N(s.percentage)})),g=r(()=>(s.strokeWidth/s.width*100).toFixed(1)),v=r(()=>["circle","dashboard"].includes(s.type)?Number.parseInt(`${50-Number.parseFloat(g.value)/2}`,10):0),$=r(()=>{const e=v.value,o=s.type==="dashboard";return`
          M 50 50
          m 0 ${o?"":"-"}${e}
          a ${e} ${e} 0 1 1 0 ${o?"-":""}${e*2}
          a ${e} ${e} 0 1 1 0 ${o?"":"-"}${e*2}
          `}),h=r(()=>2*Math.PI*v.value),k=r(()=>s.type==="dashboard"?.75:1),w=r(()=>`${-1*h.value*(1-k.value)/2}px`),F=r(()=>({strokeDasharray:`${h.value*k.value}px, ${h.value}px`,strokeDashoffset:w.value})),x=r(()=>({strokeDasharray:`${h.value*k.value*(s.percentage/100)}px, ${h.value}px`,strokeDashoffset:w.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"})),E=r(()=>{let e;return s.color?e=N(s.percentage):e=b[s.status]||b.default,e}),L=r(()=>s.status==="warning"?A:s.type==="line"?s.status==="success"?O:H:s.status==="success"?R:U),M=r(()=>s.type==="line"?12+s.strokeWidth*.4:s.width*.111111+2),S=r(()=>s.format(s.percentage));function V(e){const o=100/e.length;return e.map((n,p)=>P(n)?{color:n,percentage:(p+1)*o}:n).sort((n,p)=>n.percentage-p.percentage)}const N=e=>{var o;const{color:d}=s;if(j(d))return d(e);if(P(d))return d;{const n=V(d);for(const p of n)if(p.percentage>e)return p.color;return(o=n[n.length-1])==null?void 0:o.color}};return(e,o)=>(l(),c("div",{class:i([t(a).b(),t(a).m(e.type),t(a).is(e.status),{[t(a).m("without-text")]:!e.showText,[t(a).m("text-inside")]:e.textInside}]),role:"progressbar","aria-valuenow":e.percentage,"aria-valuemin":"0","aria-valuemax":"100"},[e.type==="line"?(l(),c("div",{key:0,class:i(t(a).b("bar"))},[y("div",{class:i(t(a).be("bar","outer")),style:f({height:`${e.strokeWidth}px`})},[y("div",{class:i([t(a).be("bar","inner"),{[t(a).bem("bar","inner","indeterminate")]:e.indeterminate},{[t(a).bem("bar","inner","striped")]:e.striped},{[t(a).bem("bar","inner","striped-flow")]:e.stripedFlow}]),style:f(t(_))},[(e.showText||e.$slots.default)&&e.textInside?(l(),c("div",{key:0,class:i(t(a).be("bar","innerText"))},[B(e.$slots,"default",{percentage:e.percentage},()=>[y("span",null,I(t(S)),1)])],2)):C("v-if",!0)],6)],6)],2)):(l(),c("div",{key:1,class:i(t(a).b("circle")),style:f({height:`${e.width}px`,width:`${e.width}px`})},[(l(),c("svg",Y,[y("path",{class:i(t(a).be("circle","track")),d:t($),stroke:`var(${t(a).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-linecap":e.strokeLinecap,"stroke-width":t(g),fill:"none",style:f(t(F))},null,14,ee),y("path",{class:i(t(a).be("circle","path")),d:t($),stroke:t(E),fill:"none",opacity:e.percentage?1:0,"stroke-linecap":e.strokeLinecap,"stroke-width":t(g),style:f(t(x))},null,14,te)]))],6)),(e.showText||e.$slots.default)&&!e.textInside?(l(),c("div",{key:2,class:i(t(a).e("text")),style:f({fontSize:`${t(M)}px`})},[B(e.$slots,"default",{percentage:e.percentage},()=>[e.status?(l(),D(t(J),{key:1},{default:q(()=>[(l(),D(G(t(L))))]),_:1})):(l(),c("span",se,I(t(S)),1))])],6)):C("v-if",!0)],10,X))}});var oe=K(re,[["__file","progress.vue"]]);const ie=Z(oe);export{ie as E};
