import{e as B}from"./CmRxzTqw.js";import{E as g,a as F}from"./B7GaOiDz.js";import{E as T,a as U}from"./C9f7n97H.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */import{P as q}from"./CaNlADry.js";import{c as D}from"./Bs9Zhtqd.js";import{l as I,j as _,b as c,s as P,M as w,N as S,a3 as t,a1 as s,u as o,a0 as $,O as v,ag as y,Z as j}from"./CUZG7cWw.js";const Q=I({__name:"create-share",props:{isShowChatType:{type:[Boolean,Number]}},emits:["confirm"],setup(G,{expose:V,emit:b}){const R=b,n=_(),m=_(),a=c({name:"",password:"",chat_type:1}),p=c("add"),C=P({name:[{required:!0,message:"请输入分享名称"}]}),E=l=>{var e;l?(p.value="edit",a.value=D(l)):(p.value="add",a.value={name:"",password:"",chat_type:1}),(e=m.value)==null||e.open()},h=()=>{var l;(l=m.value)==null||l.close()},k=async()=>{var l;await((l=n.value)==null?void 0:l.validate()),R("confirm",a.value,p.value)};return V({open:E,close:h}),(l,e)=>{const u=B,d=g,i=T,x=U,N=F;return w(),S("div",null,[t(q,{ref_key:"popupRef",ref:m,title:`${o(p)=="add"?"创建":"编辑"}链接`,async:!0,width:"550px",onConfirm:k,onClose:e[3]||(e[3]=r=>{var f;return(f=o(n))==null?void 0:f.resetFields()})},{default:s(()=>[t(N,{ref_key:"formRef",ref:n,model:o(a),rules:o(C),"label-width":"84px"},{default:s(()=>[t(d,{label:"名称",prop:"name"},{default:s(()=>[t(u,{modelValue:o(a).name,"onUpdate:modelValue":e[0]||(e[0]=r=>o(a).name=r),placeholder:"记录名称，仅用于展示",clearable:""},null,8,["modelValue"])]),_:1}),t(d,{label:"密码",prop:"password"},{default:s(()=>[t(u,{modelValue:o(a).password,"onUpdate:modelValue":e[1]||(e[1]=r=>o(a).password=r),type:"password",placeholder:"不设置密码，可直接访问","show-password":""},null,8,["modelValue"])]),_:1}),l.isShowChatType&&o(p)==="add"?(w(),$(d,{key:0,label:"对话模式",prop:"chat_type",required:""},{default:s(()=>[v("div",null,[t(x,{modelValue:o(a).chat_type,"onUpdate:modelValue":e[2]||(e[2]=r=>o(a).chat_type=r)},{default:s(()=>[t(i,{label:1},{default:s(()=>e[4]||(e[4]=[y("文本对话")])),_:1}),t(i,{label:2},{default:s(()=>e[5]||(e[5]=[y("形象对话")])),_:1})]),_:1},8,["modelValue"]),e[6]||(e[6]=v("div",{class:"form-tips"}," 若关闭或没有配置形象选择后，默认展示文本 ",-1))])]),_:1})):j("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title"])])}}});export{Q as _};
