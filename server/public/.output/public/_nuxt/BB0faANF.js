import{cA as p}from"./CmRxzTqw.js";import{_ as u}from"./CXAJ--Vj.js";import{l as d,M as a,N as o,a3 as m,O as s,_,ao as v,$ as f,u as y,a5 as x}from"./CUZG7cWw.js";import{_ as V}from"./DlAUqK2U.js";import"./eFgaMLiC.js";import"./C7tIPmrK.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";/* empty css        *//* empty css        */const h={class:"mt-[15px]"},k={class:"mt-[10px]"},q=["onClick"],C=d({__name:"dalle-picture-quality",props:{modelValue:{default:{version:"",style:"standard"}}},emits:["update:modelValue"],setup(l,{emit:r}){const i=r,n=l,{modelValue:t}=p(n,i),c=[{value:"standard",label:"标准"},{value:"hd",label:"HD-高清"}];return t.value="standard",(b,B)=>(a(),o("div",h,[m(u,{title:"图片质量",tips:"",required:""}),s("div",k,[(a(),o(_,null,v(c,e=>s("div",{key:e.value,class:f(["picture-quality-option rounded-[12px]",{"picture-quality-option__active":e.value===y(t)}]),onClick:D=>t.value=e.value},x(e.label),11,q)),64))])]))}}),O=V(C,[["__scopeId","data-v-cf6c91f6"]]);export{O as default};
