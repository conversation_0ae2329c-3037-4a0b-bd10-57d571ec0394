import{_ as dt}from"./CbQsrhNE.js";import{_ as ut}from"./BH1TZLrE.js";import{a as mt,_ as ft,b as _t}from"./CCGM0zxW.js";import{_ as gt}from"./eFgaMLiC.js";import{i as ht,b as yt,j as vt,l as wt,by as xt,bz as Ct,bA as St,_ as bt,f as A,E as kt}from"./CmRxzTqw.js";import{_ as Lt}from"./D3znQkH1.js";import{E as Et}from"./oVx59syQ.js";import{_ as It}from"./DB7Ysqj9.js";import{E as Rt}from"./Do9LV2MU.js";/* empty css        */import{l as At,j as W,b as T,ai as Z,r as Tt,c as $t,E as Dt,n as G,k as Ut,M as u,N as v,a3 as c,a1 as p,O as m,u as t,_ as B,ao as F,a0 as j,ag as Vt,Z as O,a4 as Nt,a5 as Mt,$ as zt,y as Bt}from"./CUZG7cWw.js";import{u as Ft}from"./DAOx25wS.js";import{u as J}from"./DNOp0HuO.js";import{u as jt}from"./Bj_9-7Jh.js";import Ot from"./77CHl07W.js";import{h as Pt,i as qt,j as Ht,k as Wt,l as Zt,b as Gt,e as K,c as Jt}from"./DQUFgXGm.js";import{_ as Kt}from"./DlAUqK2U.js";import"./DCzKTodP.js";/* empty css        */import"./CH6wv3Pu.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./BYMcWg3Q.js";/* empty css        */import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        */import"./DjHPV-Am.js";import"./DoCT-qbH.js";import"./DwFObZc_.js";import"./CRNANWso.js";import"./CHg9aK2B.js";import"./DecTOTC8.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./7tQUKVT9.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./Bf_xRNbS.js";import"./BOx_5T3X.js";/* empty css        *//* empty css        */import"./CaNlADry.js";import"./DdtGP7XX.js";import"./DHUC3PVh.js";import"./Dbi96Hzd.js";const Qt=ht({id:"dialogueSate",state:()=>({sessionId:"",sessionLists:[]}),getters:{getCurrentSession:a=>a.sessionLists.find(w=>String(w.id)===String(a.sessionId))||{}},actions:{setSessionId(a=""){this.sessionId=String(a)},setSessionSelect(a){a||([a]=this.sessionLists),this.setSessionId((a==null?void 0:a.id)||"")},async getSessionLists(){const a=await Pt({page_type:0});return this.sessionLists=a.lists||[],this.setSessionSelect(),this.sessionLists},async sessionAdd(){await qt({}),await this.getSessionLists(),this.setSessionSelect()},async sessionEdit(a){await Ht({...a}),await this.getSessionLists(),this.setSessionSelect(a)},async sessionClear(){await Wt(),await this.getSessionLists()},async sessionDelete(a){await Zt({id:a}),await this.getSessionLists()}}}),Xt={class:"h-full flex"},Yt={class:"p-[16px]"},te={class:"flex items-center justify-around text-xl font-medium px-[16px] pt-[16px] cursor-pointer"},ee={class:"flex-1 min-w-0 pr-4 py-4"},oe={class:"h-full flex flex-col bg-body rounded-[12px]"},se={class:"flex-1 min-h-0"},ne={class:"my-[5px]"},ie={key:0,class:"flex flex-col",style:{"margin-left":"52px"}},ae=["onClick"],re={class:"mr-2 text-tx-primary"},le={key:1,class:"max-w-[1200px] mx-auto"},ce={class:"mb-[10px] px-[30px]"},pe={class:"mr-[10px]"},de=At({__name:"chat",async setup(a){var H;let w,$;const _=yt(),x=vt(),D=W(),n=Qt(),Q=wt(),U=T(""),{copy:X}=Ft();[w,$]=Z(()=>J(()=>n.getSessionLists(),{lazy:!0},"$SUo5FcMjp8")),await w,$();const Y=xt(),{data:r,refresh:N}=([w,$]=Z(()=>J(()=>Gt({type:1,category_id:n.sessionId,page_type:0}),{transform(i){return i.lists||[]},default(){return[]},lazy:!0},"$5oItIRu1UV")),w=await w,$(),w);(H=_.getChatConfig)!=null&&H.is_reopen&&(n.sessionAdd(),_.getChatConfig.is_reopen=0);const tt=async()=>{if(!x.isLogin)return x.toggleShowLogin();n.sessionId&&(await A.confirm("确定清空记录？"),await K({category_id:n.sessionId,type:1}),N())},V=T(-1),{lockFn:et}=jt(async()=>{const i=r.value[r.value.length-1],e=r.value.find(({id:h})=>h===i.id);e&&(V.value=i.id,r.value.splice(r.value.length-2,2),k(e.content))}),g=Tt({show:!1,data:{url:"",name:"",type:"image"}}),ot=i=>{g.show=!!i.support_image,g.show||(g.data.url="")},st=()=>{var i;if(!x.isLogin)return(i=D.value)==null||i.blur(),x.toggleShowLogin();L()};let o=null;const C=T(!1);let M=!1;const b=T([]),k=async(i,e="input")=>{var E;if(!x.isLogin)return x.toggleShowLogin();if(!i)return A.msgError("请输入问题");if(C.value)return;const h=Date.now();b.value=[],C.value=!0,r.value.push({type:1,content:i,files_plugin:[{...g.data}]}),r.value.push({type:2,typing:!0,content:[""],key:h}),(E=D.value)==null||E.setInputValue();const d=r.value.find(l=>l.key===h);n.sessionId||(M=!0,await n.sessionAdd(),M=!1),o=Jt({type:1,other_id:n.sessionId,question:i,model:U.value,file:g.data.url}),o.addEventListener("chat",({data:l})=>{console.log(l);const{data:y,index:f}=l;d.content[f]||(d.content[f]=""),d.content[f]+=y}),o.addEventListener("finish",({data:l})=>{const{data:y,index:f}=l;y&&(d.content[f]+=y),g.data.url=""}),o.addEventListener("question",({data:l})=>{b.value=JSON.parse(l.data)}),o.addEventListener("close",async()=>{V.value!==-1&&d.content[0].length&&(await K({type:1,id:V.value}),V.value=-1),await x.getUser(),n.getCurrentSession.name==="新的会话"&&await n.sessionEdit({id:n.sessionId,name:i}),setTimeout(async()=>{await N(),C.value=!1,d.typing=!1,await G(),L()},600)}),o.addEventListener("error",async l=>{var y,f;if(e==="input"&&((y=D.value)==null||y.setInputValue(i)),((f=l.data)==null?void 0:f.code)===1100){_.getIsShowRecharge?(await A.confirm(`${_.getTokenUnit}数量已用完，请前往充值`),Q.push("/user/recharge")):A.msgError(`${_.getTokenUnit}数量已用完。请联系客服增加`);return}l.errorType==="connectError"&&A.msgError("请求失败，请重试"),["connectError","responseError"].includes(l.errorType)&&r.value.splice(r.value.length-2,2),d.typing=!1,setTimeout(()=>{C.value=!1},200)})},z=W(),P=T(),L=async()=>{var e,h,d;const i=(h=(e=z.value)==null?void 0:e.wrapRef)==null?void 0:h.scrollHeight;(d=z.value)==null||d.setScrollTop(i)},{height:nt}=Ct(P);St(nt,()=>{C.value&&L()},{immediate:!0});const q=()=>{o==null||o.removeEventListener("chat"),o==null||o.removeEventListener("close"),o==null||o.removeEventListener("error"),o==null||o.removeEventListener("finish"),o==null||o.abort(),C.value=!1,b.value=[]};return $t(()=>n.sessionId,async(i,e)=>{!M&&i!=e&&(q(),await N(),L())}),Dt(async()=>{await G(),r.value.length&&L()}),Ut(()=>{q()}),(i,e)=>{const h=dt,d=ut,E=mt,l=gt,y=kt,f=ft,it=Lt,at=Et,rt=It,lt=_t,ct=Rt,pt=bt;return u(),v("div",null,[c(pt,{name:"default"},{default:p(()=>[m("div",Xt,[m("div",Yt,[c(d,{modelValue:t(n).sessionId,"onUpdate:modelValue":e[0]||(e[0]=s=>t(n).sessionId=s),data:t(n).sessionLists,onAdd:t(n).sessionAdd,onEdit:t(n).sessionEdit,onDelete:t(n).sessionDelete,onClear:t(n).sessionClear,onClickItem:t(n).setSessionSelect},{top:p(()=>[m("div",te,[e[7]||(e[7]=m("div",{class:"pb-[6px] text-primary border-solid border-b-[2px] border-primary"}," 问答助手 ",-1)),c(h,{to:"/dialogue/role"},{default:p(()=>e[6]||(e[6]=[m("div",{class:"pb-[8px]"},"角色助手",-1)])),_:1})])]),_:1},8,["modelValue","data","onAdd","onEdit","onDelete","onClear","onClickItem"])]),m("div",ee,[c(ct,{class:"h-full",content:t(_).getChatConfig.watermark,font:{color:t(Y)?"rgba(256,256,256,0.08)":"rgba(0,0,0,0.06)",fontSize:12}},{default:p(()=>[m("div",oe,[m("div",se,[c(at,{ref_key:"scrollbarRef",ref:z},{default:p(()=>[c(it,null,{default:p(()=>[t(r).length?(u(),v("div",{key:0,ref_key:"innerRef",ref:P,class:"px-8"},[(u(!0),v(B,null,F(t(r),(s,S)=>(u(),v("div",{key:s.id+""+S,class:"mt-4 sm:pb-[20px]"},[s.type==1?(u(),j(f,{key:0,type:"right",avatar:t(x).userInfo.avatar,color:"white"},{actions:p(()=>[m("div",ne,[c(y,{link:"",type:"info",onClick:I=>t(X)(s.content)},{icon:p(()=>[c(l,{name:"el-icon-CopyDocument"})]),default:p(()=>[e[8]||(e[8]=Vt(" 复制 "))]),_:2},1032,["onClick"])])]),default:p(()=>[c(E,{content:s.content,"files-plugin":s.files_plugin},null,8,["content","files-plugin"])]),_:2},1032,["avatar"])):O("",!0),s.type==2?(u(),j(f,{key:1,type:"left",avatar:t(_).getChatConfig.chat_logo,time:s.create_time,bg:"var(--el-bg-color-page)",modelName:s.model},{outer_actions:p(()=>[S===t(r).length-1&&!t(C)?(u(),v("div",ie,[(u(!0),v(B,null,F(t(b).length?t(b):s.correlation,(I,R)=>(u(),v("div",{key:R,class:"inline-flex items-center rounded-[12px] bg-page cursor-pointer mt-[10px] hover:bg-primary-light-9",style:{padding:"8px 12px",width:"fit-content"},onClick:Nt(ue=>k(I,"input"),["stop"])},[m("span",re,Mt(I),1),c(l,{name:"el-icon-Right",color:"#999",size:"20"})],8,ae))),128))])):O("",!0)]),default:p(()=>[(u(!0),v(B,null,F(s.content,(I,R)=>(u(),j(E,{key:R,content:I,type:"html",typing:s.typing,"line-numbers":!t(_).isMobile,"show-rewrite":S===t(r).length-1,"show-copy":"","show-voice":t(_).getIsVoiceOpen,class:zt(["mb-[15px] last-of-type:mb-0",{"pt-[15px] border-t border-solid border-br-light":R>0}]),"show-poster":"","record-list":t(r),index:R,"record-id":s.id,onRewrite:t(et)},null,8,["content","typing","line-numbers","show-rewrite","show-voice","class","record-list","index","record-id","onRewrite"]))),128))]),_:2},1032,["avatar","time","modelName"])):O("",!0)]))),128))],512)):(u(),v("div",le,[c(Ot,{onClickItem:e[1]||(e[1]=s=>k(s,"sample"))})]))]),_:1})]),_:1},512)]),m("div",ce,[c(lt,{ref_key:"chatActionRef",ref:D,loading:t(C),"file-plugin":t(g).data,"onUpdate:filePlugin":e[3]||(e[3]=s=>t(g).data=s),onEnter:k,onClear:tt,onPause:e[4]||(e[4]=s=>{var S;return(S=t(o))==null?void 0:S.abort()}),onFocus:st,"show-continue":t(r).length,"show-file-upload":t(g).show,onContinue:e[5]||(e[5]=s=>k("继续","btn"))},{btn:p(()=>[m("div",pe,[c(rt,{class:"min-w-[280px] select-class",sub_id:t(U),"onUpdate:sub_id":e[2]||(e[2]=s=>Bt(U)?U.value=s:null),"onUpdate:modelConfig":ot},null,8,["sub_id","onUpdate:modelConfig"])])]),_:1},8,["loading","file-plugin","show-continue","show-file-upload"])])])]),_:1},8,["content","font"])])])]),_:1})])}}}),wo=Kt(de,[["__scopeId","data-v-4e36a7c5"]]);export{wo as default};
