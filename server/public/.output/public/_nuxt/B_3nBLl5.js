import{a as _}from"./9CYoqqXX.js";import{b as h,a as d}from"./CmRxzTqw.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import f from"./cdDY9IAx.js";import{l as v,m as r,M as s,N as c,a3 as k,a1 as w,u as a,_ as M,ao as x,a0 as B}from"./CUZG7cWw.js";import{_ as I}from"./DlAUqK2U.js";import"./BOx_5T3X.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./Ddo5WWE5.js";import"./mBG0LxMu.js";import"./CbQsrhNE.js";import"./DecTOTC8.js";const N={class:"menu"},b=v({__name:"menu",props:{isHome:{type:Boolean}},setup(g){const n=h(),m=r(()=>{var e;return((e=n.pageAside.menu)==null?void 0:e.filter(i=>Number(i.is_show)===1))||[]}),u=r(()=>n.pageAside.showNavIcon),t=d(),p=r(()=>{const e=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.parentPath||t.meta.activePath||e});return(e,i)=>{const l=_;return s(),c("div",N,[k(l,{"default-active":a(p)},{default:w(()=>[(s(!0),c(M,null,x(a(m),o=>(s(),B(f,{key:o.id,item:o,"is-show-icon":a(u),path:o.link.path,"is-active":a(p)===o.link.path},null,8,["item","is-show-icon","path","is-active"]))),128))]),_:1},8,["default-active"])])}}}),D=I(b,[["__scopeId","data-v-1b361228"]]);export{D as default};
