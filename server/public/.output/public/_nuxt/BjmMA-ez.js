import{_ as S}from"./Dhda0m3Y.js";import{E as B}from"./B7GaOiDz.js";import{h as q,e as D,E as I}from"./CmRxzTqw.js";import{E as O,a as z}from"./CXDY_LVT.js";import{E as F,a as K}from"./C9f7n97H.js";import{E as L}from"./DYjlFFbo.js";import{E as M}from"./06MVqVCl.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import{u as P}from"./DIUf2-0l.js";import{r as T}from"./Dl64kDm5.js";import{_ as $}from"./BXBQD0li.js";import{t as j}from"./qRM0tN96.js";import{l as G,j as Z,b as A,M as r,N as x,a3 as l,a1 as n,u as o,a0 as k,Z as H,O as t,_ as J,ao as Q,ag as m,n as W}from"./CUZG7cWw.js";const X={class:"pt-[10px]"},Y={class:"w-80"},h={class:"w-80"},ee={class:"flex"},le={class:"w-80"},oe={class:"ml-2 flex items-center"},te={class:"flex-1 min-w-0"},se={class:"flex"},ae={class:"w-80"},ne={class:"flex w-[400px]"},Ne=G({__name:"base-config",props:{modelValue:{}},emits:["update:modelValue"],setup(g,{emit:E}){const u=Z(),_=A(!1),a=q(g,"modelValue",E),{optionsData:y,refresh:f}=P({knowledge:{api:T,params:{page_type:0},transformData(V){return V.lists||[]}},robotCategory:{api:j}}),U=async()=>{_.value=!0,await W(),u.value.open()};return(V,e)=>{const b=S,d=B,p=D,R=O,C=z,v=I,i=F,w=K,c=L,N=M;return r(),x("div",X,[l(d,{label:"智能体图标",prop:"image"},{default:n(()=>[t("div",null,[t("div",null,[l(b,{modelValue:o(a).image,"onUpdate:modelValue":e[0]||(e[0]=s=>o(a).image=s)},null,8,["modelValue"])]),e[10]||(e[10]=t("div",{class:"form-tips"},"建议尺寸：240*240px",-1))])]),_:1}),l(d,{label:"智能体名称",prop:"name"},{default:n(()=>[t("div",Y,[l(p,{modelValue:o(a).name,"onUpdate:modelValue":e[1]||(e[1]=s=>o(a).name=s),placeholder:"请输入智能体名称",clearable:""},null,8,["modelValue"])])]),_:1}),l(d,{label:"简介",prop:"intro"},{default:n(()=>[t("div",h,[l(p,{modelValue:o(a).intro,"onUpdate:modelValue":e[2]||(e[2]=s=>o(a).intro=s),placeholder:"请简单描述下给你的智能体",type:"textarea",autosize:{minRows:3,maxRows:6},maxlength:200,"show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1}),l(d,{label:"关联知识库",prop:"kb_ids"},{default:n(()=>[t("div",null,[t("div",ee,[t("div",le,[l(C,{modelValue:o(a).kb_ids,"onUpdate:modelValue":e[3]||(e[3]=s=>o(a).kb_ids=s),placeholder:"请选择关联知识库",clearable:"",multiple:""},{default:n(()=>[(r(!0),x(J,null,Q(o(y).knowledge,s=>(r(),k(R,{key:s.id,label:`${s.name}`,value:String(s.id)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),t("div",oe,[l(v,{type:"primary",link:"",onClick:U},{default:n(()=>e[11]||(e[11]=[m(" 新增知识库 ")])),_:1}),e[13]||(e[13]=t("span",{class:"px-1"},"|",-1)),l(v,{type:"primary",link:"",onClick:o(f)},{default:n(()=>e[12]||(e[12]=[m(" 刷新 ")])),_:1},8,["onClick"])])]),e[14]||(e[14]=t("div",{class:"form-tips"},"需选择同一种训练模型的知识库",-1))])]),_:1}),l(d,{label:"角色设定",prop:"roles_prompt"},{default:n(()=>[t("div",te,[t("div",se,[t("div",ae,[l(p,{modelValue:o(a).roles_prompt,"onUpdate:modelValue":e[4]||(e[4]=s=>o(a).roles_prompt=s),placeholder:"请输入角色设定",type:"textarea",autosize:{minRows:4,maxRows:6},clearable:""},null,8,["modelValue"])])]),e[15]||(e[15]=t("div",{class:"form-tips"}," 引导应用的聊天方向，该内容会被固定在上下文的开头。 ",-1))])]),_:1}),l(d,{label:"对话图标"},{default:n(()=>[t("div",null,[l(b,{modelValue:o(a).icons,"onUpdate:modelValue":e[5]||(e[5]=s=>o(a).icons=s),"exclude-domain":!1,"can-close":!0},null,8,["modelValue"]),e[16]||(e[16]=t("div",{class:"form-tips"}," 不设置的话，对话图标默认拿智能体封面 ",-1))])]),_:1}),l(d,{label:"对话上下文",prop:"is_show_context"},{default:n(()=>[t("div",null,[l(w,{modelValue:o(a).is_show_context,"onUpdate:modelValue":e[6]||(e[6]=s=>o(a).is_show_context=s)},{default:n(()=>[l(i,{label:1},{default:n(()=>e[17]||(e[17]=[m(" 显示 ")])),_:1}),l(i,{label:0},{default:n(()=>e[18]||(e[18]=[m(" 关闭 ")])),_:1})]),_:1},8,["modelValue"]),e[19]||(e[19]=t("div",{class:"form-tips"},"在前台显示对话上下文，默认显示",-1))])]),_:1}),l(d,{label:"引用内容",prop:"is_show_quote"},{default:n(()=>[t("div",null,[l(w,{modelValue:o(a).is_show_quote,"onUpdate:modelValue":e[7]||(e[7]=s=>o(a).is_show_quote=s)},{default:n(()=>[l(i,{label:1},{default:n(()=>e[20]||(e[20]=[m(" 显示 ")])),_:1}),l(i,{label:0},{default:n(()=>e[21]||(e[21]=[m(" 关闭 ")])),_:1})]),_:1},8,["modelValue"]),e[22]||(e[22]=t("div",{class:"form-tips"},"在前台显示引用内容，默认显示",-1))])]),_:1}),l(d,{label:"问答相似问题推荐",prop:"related_issues_num",required:""},{default:n(()=>[t("div",null,[t("div",ne,[l(c,{modelValue:o(a).related_issues_num,"onUpdate:modelValue":e[8]||(e[8]=s=>o(a).related_issues_num=s),min:0,max:10},null,8,["modelValue"]),l(N,{modelValue:o(a).related_issues_num,"onUpdate:modelValue":e[9]||(e[9]=s=>o(a).related_issues_num=s),min:0,max:10,class:"ml-4 w-[180px]"},null,8,["modelValue"])]),e[23]||(e[23]=t("div",{class:"form-tips"},"作用于智能体对话问题推荐，填0对话问题推荐不生效",-1))])]),_:1}),o(_)?(r(),k($,{key:0,ref_key:"addkbPopRef",ref:u,onSuccess:o(f)},null,8,["onSuccess"])):H("",!0)])}}});export{Ne as _};
