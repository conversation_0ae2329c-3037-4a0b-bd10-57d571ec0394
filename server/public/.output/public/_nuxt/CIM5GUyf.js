import{E as K}from"./oVx59syQ.js";import{_ as Q}from"./eFgaMLiC.js";import{E as A,e as R,s as W}from"./CmRxzTqw.js";/* empty css        */import{u as X}from"./DRe575WM.js";import{u as Y}from"./Bj_9-7Jh.js";import{l as ee,b as _,r as se,M as r,N as c,a3 as i,a1 as n,u as o,O as t,ag as U,Z as E,_ as I,ao as N,$ as te,a4 as m,a5 as g}from"./CUZG7cWw.js";import{g as le,p as oe}from"./DwFObZc_.js";import{E as h}from"./HA5sEeDs.js";import{E as B}from"./DluKwKHO.js";import{E as ie}from"./YwtsEmdS.js";import{E as ae}from"./C7tIPmrK.js";import{E as ne}from"./ArzC3z2d.js";import{_ as de}from"./DlAUqK2U.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";import"./CDwN27aR.js";import"./C3XldtMC.js";const re={class:"locality-draw-popup"},ce={class:"w-1/2"},ue={class:"px-4"},pe={class:"flex flex-col"},ve={key:0,style:{height:"500px"},class:"flex justify-center items-center text-tx-placeholder"},xe={class:"mt-4"},fe={class:"my-2 px-4"},_e=["onClick"],me={class:"ml-2 text-xs"},he={key:0,class:"text-tx-placeholder"},ke={class:"flex flex-col px-4"},ge={class:"text-base"},ye={class:"flex items-center"},Ce={class:"ml-2 text-tx-regular text-xs"},we={class:"flex items-center"},Ee={class:"cursor-pointer"},Ve=["onClick"],Pe=["onClick"],$e=["onClick"],Se=["onClick"],ze={class:"text-xs"},Me=["onClick"],Ae={class:"flex justify-end"},Ue=ee({__name:"add-user",props:{id:{type:Number,default:0}},emits:["success"],setup(L,{expose:T,emit:D}){const F={1:"可管理",2:"可编辑",3:"可查看"},b=D,V=L,x=_(!1),y=_(!1),C=_(!1),P=_(-1),w=se({keyword:"",kb_id:V.id,page_type:0}),a=_([]),{pager:u,getLists:$,resetPage:S,resetParams:j}=X({fetchFun:le,params:w}),q=()=>{S(),$()},z=(l,e)=>{if(l.is_added)return;const f=a.value.findIndex(k=>k.id===l.id);f!==-1?(l.permission=0,a.value.splice(f,1)):l.is_added||(l.permission=3,a.value.push(l)),e==="box"?l.isSelected=l.isSelected?0:1:l.isSelected=l.isSelected?1:0},O=l=>{u.lists.forEach(e=>{e.is_added||(e.isSelected=l?1:0,e.permission=l?3:0)}),a.value=l?u.lists.filter(e=>!e.is_added):[]},Z=l=>{a.value=a.value.filter(e=>e.id!==l.id),u.lists.find(e=>e.id===l.id).isSelected=!1},G=l=>{P.value=l,C.value=!0},M=()=>{C.value=!1},p=(l,e)=>{l.permission=e,M()},{lockFn:H,isLock:J}=Y(async()=>{const l={};a.value.forEach(e=>{l[e.sn]=e.permission}),await oe({kb_id:V.id,users:l}),b("success"),x.value=!1});return T({show:()=>{x.value=!0,j(),S(),a.value=[],y.value=!1,$()}}),(l,e)=>{const f=K,k=Q;return r(),c("div",re,[i(o(ne),{modelValue:x.value,"onUpdate:modelValue":e[3]||(e[3]=s=>x.value=s),width:"980px",class:"!rounded-[12px]",center:!0,draggable:!0,"destroy-on-close":!0,"close-on-click-modal":!1},{header:n(()=>e[4]||(e[4]=[t("div",{class:"w-full text-left"},[t("div",{class:"text-lg font-medium"},"添加成员")],-1)])),footer:n(()=>[t("div",Ae,[i(o(A),{onClick:e[2]||(e[2]=s=>x.value=!1)},{default:n(()=>e[8]||(e[8]=[U("取消")])),_:1}),i(o(A),{type:"primary",loading:o(J),onClick:o(H)},{default:n(()=>e[9]||(e[9]=[U(" 确认 ")])),_:1},8,["loading","onClick"])])]),default:n(()=>[t("div",{class:"flex",onClick:M},[t("div",ce,[t("div",ue,[i(o(R),{modelValue:w.keyword,"onUpdate:modelValue":e[0]||(e[0]=s=>w.keyword=s),style:{width:"100%"},size:"large",placeholder:"搜索成员","prefix-icon":o(W),onInput:q},null,8,["modelValue","prefix-icon"])]),i(f,{height:"500px"},{default:n(()=>[t("div",pe,[o(u).lists.length===0?(r(),c("div",ve," 请搜索成员添加 ")):E("",!0),t("div",xe,[t("div",fe,[i(o(h),{modelValue:y.value,"onUpdate:modelValue":e[1]||(e[1]=s=>y.value=s),"true-value":1,"false-value":0,label:"全选",size:"large",onChange:O},null,8,["modelValue"])]),o(u).lists.length!==0?(r(!0),c(I,{key:0},N(o(u).lists,s=>(r(),c("div",{class:te(["my-4 mr-4 py-2 px-4 flex items-center cursor-pointer hover:bg-primary-light-9 rounded-[12px]",{"!cursor-not-allowed":s.is_added}]),key:s.id,onClick:v=>z(s,"box")},[i(o(h),{modelValue:s.isSelected,"onUpdate:modelValue":v=>s.isSelected=v,"true-value":1,"false-value":0,label:"",size:"large",disabled:s.is_added,onClick:m(v=>z(s,"checkbox"),["stop"])},null,8,["modelValue","onUpdate:modelValue","disabled","onClick"]),i(o(B),{src:s.avatar,size:"26",class:"flex-none ml-2"},null,8,["src"]),t("div",me,[t("span",null,g(s.nickname),1),s.is_added?(r(),c("div",he," 已添加 ")):E("",!0)])],10,_e))),128)):E("",!0)])])]),_:1})]),i(o(ie),{direction:"vertical",style:{height:"500px"}}),i(f,{height:"500px",class:"w-1/2"},{default:n(()=>[t("div",ke,[t("div",ge," 已选择："+g(a.value.length)+" 个 ",1),(r(!0),c(I,null,N(a.value,(s,v)=>(r(),c("div",{class:"mt-4 py-2 px-4 flex items-center justify-between cursor-pointer hover:bg-primary-light-9 rounded-[12px]",key:s.id},[t("div",ye,[i(o(B),{src:s.avatar,size:"26",class:"flex-none ml-2"},null,8,["src"]),t("div",Ce,g(s.nickname),1)]),t("div",we,[i(o(ae),{placement:"bottom-end",width:380,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:C.value&&P.value===v},{reference:n(()=>[t("div",{class:"flex items-center cursor-pointer",onClick:m(d=>G(v),["stop"])},[t("span",ze,g(F[s.permission]),1),i(k,{name:"el-icon-ArrowDown"})],8,Se)]),default:n(()=>[t("div",Ee,[t("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(s,2)},[e[5]||(e[5]=t("div",{style:{width:"320px"}},[t("div",{class:"text-base text-tx-primary"}," 可编辑 "),t("div",{class:"text-xs text-tx-placeholder mt-2"}," 只能操作数据学习，增删改查自己的数据，不能修改他人 ")],-1)),i(o(h),{"model-value":s.permission,"true-value":2,label:"",size:"large",onClick:m(d=>p(s,2),["stop"])},null,8,["model-value","true-value","onClick"])],8,Ve),t("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(s,3)},[e[6]||(e[6]=t("div",{style:{width:"320px"}},[t("div",{class:"text-base text-tx-primary"}," 可查看 "),t("div",{class:"text-xs text-tx-placeholder mt-2"}," 查看知识库所有数据 ")],-1)),i(o(h),{"model-value":s.permission,"true-value":3,label:"",size:"large",onClick:m(d=>p(s,3),["stop"])},null,8,["model-value","true-value","onClick"])],8,Pe),t("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:d=>p(s,1)},[e[7]||(e[7]=t("div",{style:{width:"320px"}},[t("div",{class:"text-base text-tx-primary"}," 可管理 "),t("div",{class:"text-xs text-tx-placeholder mt-2"}," 管理整个知识库数据和信息 ")],-1)),i(o(h),{"model-value":s.permission,"true-value":1,label:"",size:"large",onClick:m(d=>p(s,1),["stop"])},null,8,["model-value","true-value","onClick"])],8,$e)])]),_:2},1032,["visible"]),t("div",{class:"flex items-center ml-6",onClick:d=>Z(s)},[i(k,{name:"el-icon-CloseBold"})],8,Me)])]))),128))])]),_:1})])]),_:1},8,["modelValue"])])}}}),es=de(Ue,[["__scopeId","data-v-8e35f1ae"]]);export{es as default};
