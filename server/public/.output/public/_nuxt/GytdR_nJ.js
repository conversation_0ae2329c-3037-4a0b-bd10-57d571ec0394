import{l as u,b as a,M as p,N as g,$ as h,u as o,a6 as f}from"./CUZG7cWw.js";import{_ as v}from"./DlAUqK2U.js";const k=u({__name:"index",props:{thumbnail:{default:""},image:{default:""}},emits:["refresh","on-click"],setup(m,{emit:c}){const n=c,t=m,r=a(350),s=a("400px"),i=a(!0),d=a("");return(()=>{const e=new Image;e.onload=()=>{n("refresh"),i.value=!1,r.value=e.width,s.value=e.height+"px"},e.onerror=()=>{d.value="加载失败"},e.src=t.thumbnail||t.image})(),(e,l)=>(p(),g("div",{class:h(["rounded-[12px] relative image-cover",{ld:!o(i)}]),style:f({"--image-width":o(r),"--image-height":o(s),background:`url(${e.thumbnail||e.image}) center center / cover no-repeat`}),onClick:l[0]||(l[0]=b=>n("on-click",[e.image]))},null,6))}}),C=v(k,[["__scopeId","data-v-d58c4008"]]);export{C as I};
