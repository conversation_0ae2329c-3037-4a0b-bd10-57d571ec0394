import{_ as R}from"./eFgaMLiC.js";import{by as y,E as D}from"./CmRxzTqw.js";import{b as L,E as j,a as M}from"./CwgXbNrK.js";/* empty css        *//* empty css        */import{d as B}from"./CRNANWso.js";import{T as N,e as T}from"./BMDjbVzV.js";import{l as C,j as f,c as H,E as I,M as u,N as _,O as i,a3 as t,a1 as n,ag as r}from"./CUZG7cWw.js";const U={class:"h-full relative"},O={class:"w-full h-full flex flex-col"},P={class:"flex justify-end"},K=C({__name:"mind-map-preview",setup(V,{expose:w}){const g=new N,m=f(),c=f(),v=y();let a=null;const k=o=>{const{root:e}=g.transform(o);a==null||a.setData(e),a==null||a.fit()},h=o=>{switch(o){case"html":x();break;case"png":p("png");break;case"jpg":p("jpeg");break}};H(v,o=>{o?document.documentElement.classList.add("markmap-dark"):document.documentElement.classList.remove("markmap-dark")},{immediate:!0});const x=()=>{const o=`<style>*{margin: 0;padding:0} .markmap{width: 100vw;height:100vh}</style>
${c.value.innerHTML}`,e=new Blob([o],{type:"text/html"}),s=URL.createObjectURL(e),l=document.createElement("a");l.href=s,l.download="markmap.html",l.click(),URL.revokeObjectURL(s)},p=o=>{B(c.value,{type:o,name:"markmap"},{backgroundColor:"#fff",scale:window.devicePixelRatio*1.5})};return I(async()=>{m.value&&(a=T.create(m.value))}),w({renderMarkMap:k}),(o,e)=>{const s=R,l=D,d=j,b=M,E=L;return u(),_("div",U,[i("div",O,[i("div",P,[t(E,{onCommand:h},{dropdown:n(()=>[t(b,null,{default:n(()=>[t(d,{command:"html"},{default:n(()=>e[1]||(e[1]=[r(" 导出HTML ")])),_:1}),t(d,{command:"png"},{default:n(()=>e[2]||(e[2]=[r(" 导出PNG ")])),_:1}),t(d,{command:"jpg"},{default:n(()=>e[3]||(e[3]=[r(" 导出JPG ")])),_:1})]),_:1})]),default:n(()=>[t(l,{text:"",bg:""},{icon:n(()=>[t(s,{name:"el-icon-Download"})]),default:n(()=>[e[0]||(e[0]=r(" 导出文件 ")),t(s,{name:"el-icon-ArrowDown",class:"el-icon--right"})]),_:1})]),_:1})]),i("div",{class:"flex-1 min-h-0",ref_key:"svgWrapRef",ref:c},[(u(),_("svg",{ref_key:"svgRef",ref:m,class:"w-full h-full"},null,512))],512)])])}}});export{K as _};
