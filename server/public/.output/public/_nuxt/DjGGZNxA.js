import{_ as F}from"./eFgaMLiC.js";import{j as M,l as T,E as A,v as O}from"./CmRxzTqw.js";import{E as P}from"./Zz2DnF66.js";import{E as U}from"./oVx59syQ.js";import{E as V}from"./llRQJmEG.js";import{E as $}from"./C7tIPmrK.js";import{E as j}from"./C9jirCEY.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{u as q}from"./Bj_9-7Jh.js";import{c as H}from"./Cq2NhlyP.js";import{ModelEnums as p}from"./BLV0QRdm.js";import{l as Y,b as k,r as G,aw as J,M as e,N as c,a3 as n,a1 as r,u as t,a7 as _,a0 as m,O as s,_ as K,ao as Q,a5 as E}from"./CUZG7cWw.js";const W={class:"h-full flex flex-col"},X={class:"flex items-center"},Z={class:"flex-1 min-h-0"},oo=["onClick"],eo={class:"line-clamp-2"},to={class:"flex items-center mt-1 text-tx-secondary"},so={class:"ml-1 text-xs"},yo=Y({__name:"search-history",setup(io){const x=M(),y=T(),u=k(),f=k(),S={[p.BASE]:"local-icon-search_base",[p.ENHANCE]:"local-icon-search_copilot",[p.STUDY]:"local-icon-search_research"},d=()=>{var i;(i=t(f))==null||i.hide()},o=G({pageNo:1,count:1,pageSize:15,lists:[]}),g=async()=>{if(!x.isLogin)return;const i=await H({page_no:o.pageNo,page_size:o.pageSize});o.count=i.count,o.pageNo===1&&(o.lists=[]),o.lists.push(...i.lists)},N=async()=>{o.count>=o.pageNo*o.pageSize&&(o.pageNo++,await g())},{lockFn:b,isLock:w}=q(async()=>{o.pageNo=1,await g()});return(i,v)=>{const l=F,h=A,z=P,C=U,L=V,B=$,R=J("click-outside"),D=j,I=O;return e(),c("div",null,[n(z,{effect:"light",content:"历史搜索",placement:"bottom"},{default:r(()=>[_((e(),m(h,{link:"",ref_key:"buttonRef",ref:u},{icon:r(()=>[n(l,{name:"local-icon-clock",size:18})]),_:1})),[[R,d]])]),_:1}),n(B,{ref_key:"popoverRef",ref:f,"virtual-ref":t(u),trigger:"click",width:"300px","virtual-triggering":"","popper-style":{bottom:"20px"},onShow:t(b)},{default:r(()=>[s("div",W,[s("div",X,[v[0]||(v[0]=s("span",{class:"mr-auto"}," 历史搜索 ",-1)),s("div",null,[n(h,{link:"",onClick:d},{icon:r(()=>[n(l,{name:"el-icon-Close",size:18})]),_:1})])]),_((e(),c("div",Z,[t(o).count>0?(e(),m(C,{key:0,class:"h-full"},{default:r(()=>[_((e(),c("div",null,[(e(!0),c(K,null,Q(t(o).lists,a=>(e(),c("div",{key:a.id,class:"cursor-pointer p-[12px] hover:bg-page",onClick:no=>t(y).push({query:{id:a.id}})},[s("div",eo,E(a.ask),1),s("div",to,[n(l,{name:S[a.model],size:14},null,8,["name"]),s("span",so,E(a.create_time),1)])],8,oo))),128))])),[[D,N]])]),_:1})):(e(),m(L,{key:1,"image-size":150}))])),[[I,t(w)]])])]),_:1},8,["virtual-ref","onShow"])])}}});export{yo as _};
