import{E as j,a as F}from"./sfCUuwOk.js";import{j as B,b as N,ds as L,E as R}from"./CmRxzTqw.js";import{_ as U}from"./DJi8L2lq.js";/* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{u as D}from"./DRe575WM.js";import{_ as I}from"./DcOhod1K.js";import{l as M,b as v,m as b,j as z,E as A,M as m,N as u,O as t,a5 as n,u as o,_ as O,ao as P,a3 as s,a1 as l,y as Z,a0 as q,Z as G,$ as k,ag as H,n as C}from"./CUZG7cWw.js";import{_ as J}from"./DlAUqK2U.js";import"./oVx59syQ.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./DSuLZIN6.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./HA5sEeDs.js";import"./Ddo5WWE5.js";import"./C-n0m2hZ.js";import"./CXDY_LVT.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D6yUe_Nr.js";/* empty css        */import"./B7GaOiDz.js";import"./Bh-PoUNP.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        */const K={class:"p-[20px] flex bg-body rounded-[12px] flex-col h-full"},Q={class:"grid grid-cols-2 md:grid-cols-2 gap-4 bg-page py-[20px] rounded-lg flex-none"},W={class:"flex flex-col items-center justify-center"},X={class:"font-medium text-[25px] text-[#0256FF]"},Y={class:"mt-2"},tt={class:"flex flex-col items-center justify-center"},et={class:"font-medium text-[25px] text-[#0256FF]"},ot={class:"flex mt-4 flex-none"},st={class:"p-[8px] flex justify-around bg-page rounded-[10px] font-medium"},at=["onClick"],nt={class:"mt-4 flex-1 min-h-0 flex flex-col"},it={class:"flex-1 min-h-0"},lt={class:"flex justify-end mt-4"},pt=M({__name:"balance",setup(rt){const{userInfo:f}=B(),x=N(),c=v({type:1}),h=b(()=>"数量"),d=v(!1),y=z(),w=b(()=>[{name:`${x.getTokenUnit}明细`,type:1},{name:"智能体明细",type:2}]),$=async r=>{c.value.type=r,await C(),_()},{pager:p,getLists:_}=D({fetchFun:L,params:c.value}),E=async r=>{d.value=!0,await C(),y.value.open(r)};return A(()=>{_()}),(r,a)=>{const i=F,S=R,T=j,V=U;return m(),u("div",K,[t("div",Q,[t("div",W,[t("div",X,n(o(f).balance),1),t("div",Y,n(o(x).getTokenUnit)+"数量",1)]),t("div",tt,[t("div",et,n(o(f).robot_num),1),a[2]||(a[2]=t("div",{class:"mt-2"},"智能体",-1))])]),t("div",ot,[t("div",st,[(m(!0),u(O,null,P(o(w),(e,g)=>(m(),u("div",{class:k([{isSelect:o(c).type==e.type},"px-[15px] md:px-[30px] py-[10px] cursor-pointer"]),key:g,onClick:mt=>$(e.type)},[t("span",null,n(e.name),1)],10,at))),128))])]),t("div",nt,[t("div",it,[s(T,{data:o(p).lists,height:"100%"},{default:l(()=>[s(i,{label:"订单编号",prop:"sn","min-width":"150"}),s(i,{label:"变动类型",prop:"change_type","min-width":"150"}),s(i,{label:"智能体/应用名",prop:"robot_name","min-width":"150"},{default:l(({row:e})=>[t("div",null,n(e.robot_name||"-"),1)]),_:1}),s(i,{label:"操作时间",prop:"create_time","min-width":"150"}),s(i,{label:`变动${o(h)}`,prop:"change_amount","min-width":"100"},{default:l(({row:e})=>[t("div",{class:k({"text-danger":e.action==2})},[t("span",null,n(e.action==1?"+":"-"),1),t("span",null,n(e.change_amount),1)],2)]),_:1},8,["label"]),s(i,{label:"操作","min-width":"80"},{default:l(({row:e})=>[s(S,{onClick:g=>E(e.id),link:"",type:"primary"},{default:l(()=>a[3]||(a[3]=[H(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),t("div",lt,[s(V,{modelValue:o(p),"onUpdate:modelValue":a[0]||(a[0]=e=>Z(p)?p.value=e:null),onChange:o(_)},null,8,["modelValue","onChange"])])]),o(d)?(m(),q(I,{key:0,type:o(h),ref_key:"popRef",ref:y,onClose:a[1]||(a[1]=e=>d.value=!1)},null,8,["type"])):G("",!0)])}}}),Jt=J(pt,[["__scopeId","data-v-b0e2ee91"]]);export{Jt as default};
