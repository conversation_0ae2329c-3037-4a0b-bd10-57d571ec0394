import{_ as k}from"./CfDE0MAs.js";import{_ as x}from"./eFgaMLiC.js";import{E as b}from"./llRQJmEG.js";import{E as C}from"./oVx59syQ.js";import{cA as w}from"./CmRxzTqw.js";/* empty css        */import{m as o}from"./-CaxLuW0.js";import{_ as E}from"./CXAJ--Vj.js";import{l as V,c as $,M as l,N as s,a3 as r,a1 as B,u as c,_ as L,ao as z,O as n,$ as N,a5 as A,a0 as F}from"./CUZG7cWw.js";const M={key:0,class:"grid grid-cols-2 gap-4"},S=["onClick"],j={class:"relative rounded-[12px] overflow-hidden cursor-pointer"},D={class:"text-hidden-2 text-center"},T=V({__name:"sd-lora",props:{modelValue:{type:Array,default:[]}},emits:["update:modelValue"],setup(_,{emit:d}){const p=d,u=_;$(()=>o,()=>{i("clear")});const{modelValue:e}=w(u,p),i=a=>{a==="clear"?e.value=[]:e.value.includes(o.value[a].model_name)?e.value=e.value.filter(m=>m!==o.value[a].model_name):e.value.push(o.value[a].model_name)};return(a,m)=>{const f=k,v=x,h=b,g=C;return l(),s("div",null,[r(E,{title:"微调模型",tips:"在基础模型上叠加微调模型，让画面更细腻更可控"}),r(g,{"max-height":"360px"},{default:B(()=>[c(o).length>0?(l(),s("div",M,[(l(!0),s(L,null,z(c(o),(t,y)=>(l(),s("div",{key:t.id,class:"flex flex-col gap-2",onClick:I=>i(y)},[n("div",j,[r(f,{class:"rounded-[12px] overflow-hidden w-auto h-full bg-[var(--el-bg-color-page)]",src:t.cover,fit:"cover",ratio:[144,100]},null,8,["src"]),n("div",{class:N(["absolute top-0 left-0 bg-[rgba(0,0,0,0.4)] w-full h-full flex justify-center items-center transition-opacity opacity-0",{"opacity-100":c(e).includes(t.model_name)}])},[r(v,{name:"el-icon-CircleCheckFilled",size:20,color:"#fff"})],2)]),n("div",D,A(t.title||t.model_name),1)],8,S))),128))])):(l(),F(h,{key:1,description:"暂无关联的微调模型","image-size":50}))]),_:1})])}}});export{T as _};
