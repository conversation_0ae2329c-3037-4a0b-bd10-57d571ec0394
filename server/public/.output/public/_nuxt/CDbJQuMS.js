import{a as X,E as Y}from"./CXDY_LVT.js";import{j as ee,l as oe,b as te,e as se,E as re,f as S,v as ne}from"./CmRxzTqw.js";import{_ as ie}from"./eFgaMLiC.js";import{E as ae}from"./DCzKTodP.js";import{E as le}from"./C7tIPmrK.js";import{_ as pe}from"./BvSuqySp.js";import{_ as ce}from"./CbQsrhNE.js";import{E as de}from"./oVx59syQ.js";import{E as me}from"./C9jirCEY.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import{l as ue,b as B,j as xe,r as A,ai as fe,M as a,N as c,O as o,a3 as t,a1 as l,u as n,a4 as m,a0 as C,Z as b,ag as E,a7 as D,_ as _e,ao as ge,a5 as be,n as ve}from"./CUZG7cWw.js";import{u as he}from"./Bj_9-7Jh.js";import{u as ye}from"./DNOp0HuO.js";import{_ as ke}from"./CgHpiIC_.js";import{u as we}from"./Ce6KOvmZ.js";import Se from"./TV2dl-Ml.js";import{_ as Ce}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./u6CVc_ZE.js";import"./l0sNRNKZ.js";import"./BscXL5XZ.js";import"./CpufhUzm.js";import"./CiabO6Xq.js";/* empty css        */import"./qRM0tN96.js";import"./DjwCd26w.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */const Ee={class:"h-full flex flex-col"},Re={class:"flex px-[20px] py-[16px]"},Le={class:"flex items-center flex-none ml-auto"},Ne={class:"flex-1 min-h-0"},Ve={class:"px-[20px]","infinite-scroll-distance":"50"},$e={key:0},Ie={class:"flex flex-wrap items-stretch mx-[-10px]"},Pe={class:"w-1/4 2xl:w-1/5 sm:mb-[20px] mb-[10px] app-item"},qe={class:"flex px-[15px] py-[12px]"},ze={class:"flex-1 text-tx-secondary"},je=["onClick"],Be=["onClick"],Ae=["onClick"],De=["onClick"],Te=["onClick"],Ue={class:"px-[15px] flex flex-col items-center text-center flex-1"},Fe=["src"],Me={class:"text-2xl mt-[6px] mb-[12px]"},Oe={class:"text-tx-secondary leading-5 h-[60px] line-clamp-3"},Ze={class:"flex mt-4 items-center border-t border-solid border-br-light"},Ge={class:"flex-1 text-tx-regular border-r border-solid border-br-light flex items-center justify-center h-[50px] cursor-pointer hover:text-primary"},He={key:1},Je=ue({__name:"robot",async setup(Ke){let R,L;const d=ee(),v=oe(),h=te(),u=we(),y=B(!1),N=xe(null),V=B([]),T=async(r,e)=>{(V.value.includes(r)||e)&&await S.confirm("该智能体已分享过，是否确认重复分享？"),y.value=!0,await ve(),N.value.open(r)},U=r=>{V.value.push(r),u.getRobot(),_()},{isLock:F,lockFn:$}=he(async()=>{if(!d.isLogin)return d.toggleShowLogin();if(d.userInfo.robot_num<=0)return h.getIsShowRecharge?(await S.confirm("智能体数量已用完，请前往充值"),v.push({path:"/user/recharge"})):S.msgError("智能体数量已用完。请联系客服增加"),Promise.reject();const r=await u.addRobot();d.getUser(),v.push({path:"/application/robot/setting",query:{id:r}})}),x=A({type:" ",keyword:""}),i=A({pageNo:1,count:0,pageSize:15,lists:[]}),k=async()=>{const r=await u.getRobot({...x,page_no:i.pageNo,page_size:i.pageSize});i.count=r.count,i.pageNo===1&&(i.lists=[]),i.lists.push(...r.lists)},M=()=>{d.isLogin&&i.count>=i.pageNo*i.pageSize&&(i.pageNo++,k())},_=()=>{i.pageNo=1,k()};[R,L]=fe(()=>ye(()=>k(),{lazy:!0},"$Iyd8pwcMuC")),await R,L();const f=async(r,e)=>{switch(r){case"delete":await u.delRobot(e.id),d.getUser(),_();break;case"release":case"dialogue":v.push({path:"/application/robot/setting",query:{id:e.id,currentTab:r}});case"share":T(e.id,e.is_share);break;case"cancelPublic":await u.cancelShareRobot(e.id),_()}};return(r,e)=>{const w=Y,O=X,Z=se,I=re,p=ie,P=ae,G=le,H=pe,q=ce,J=de,K=ne,Q=me;return a(),c("div",Ee,[o("div",Re,[e[6]||(e[6]=o("div",{class:"font-medium text-xl"},"我的智能体",-1)),o("div",Le,[e[5]||(e[5]=o("div",{class:"flex-none mr-[10px]"},"筛选",-1)),t(O,{class:"!w-[100px] flex-none",modelValue:n(x).type,"onUpdate:modelValue":e[0]||(e[0]=s=>n(x).type=s)},{default:l(()=>[t(w,{label:"全部",value:" "}),t(w,{label:"公开",value:1}),t(w,{label:"私有",value:0})]),_:1},8,["modelValue"]),t(Z,{class:"mx-[10px]",modelValue:n(x).keyword,"onUpdate:modelValue":e[1]||(e[1]=s=>n(x).keyword=s),placeholder:"请输入"},null,8,["modelValue"]),o("div",null,[t(I,{onClick:m(_,["stop"]),type:"primary"},{default:l(()=>e[4]||(e[4]=[E("搜索")])),_:1})])])]),o("div",Ne,[t(J,null,{default:l(()=>[D((a(),c("div",Ve,[n(d).isLogin?(a(),c("div",$e,[o("div",Ie,[o("div",Pe,[D((a(),c("div",{class:"mx-[10px] bg-body h-full rounded-[12px] p-[20px] overflow-hidden flex flex-col justify-center items-center cursor-pointer min-h-[200px]",onClick:e[2]||(e[2]=(...s)=>n($)&&n($)(...s))},[t(p,{name:"el-icon-Plus",size:24}),e[7]||(e[7]=o("div",{class:"mt-[10px]"},"新增智能体",-1))])),[[K,n(F)]])]),(a(!0),c(_e,null,ge(n(i).lists,(s,W)=>(a(),c("div",{key:W,class:"w-1/4 2xl:w-1/5 sm:mb-[20px] mb-[10px] app-item"},[t(q,{to:{path:"/application/robot/setting",query:{id:s.id}},class:"mx-[10px] bg-body h-full rounded-[12px] overflow-hidden relative flex flex-col"},{default:l(()=>[o("div",qe,[o("div",ze,[s.is_public?(a(),C(P,{key:0,type:"warning"},{default:l(()=>e[8]||(e[8]=[E("公开")])),_:1})):(a(),C(P,{key:1,type:"primary"},{default:l(()=>e[9]||(e[9]=[E("私有")])),_:1}))]),t(G,{placement:"bottom",trigger:"hover",offset:"12",teleported:!0,"show-arrow":!1,transition:"custom-popover","popper-class":"cursor-pointer",width:190},{reference:l(()=>[t(I,{link:"",class:"el-dropdown-link"},{default:l(()=>[t(p,{name:"el-icon-MoreFilled"})]),_:1})]),default:l(()=>{var z,j;return[o("div",{class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(g=>f("release",s),["stop"])},[t(p,{name:"el-icon-Position"}),e[10]||(e[10]=o("span",{class:"ml-2"},"发布智能体",-1))],8,je),o("div",{class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(g=>f("dialogue",s),["stop"])},[t(p,{name:"el-icon-ChatDotRound"}),e[11]||(e[11]=o("span",{class:"ml-2"},"对话数据",-1))],8,Be),s.is_public&&((z=n(h).getSquareConfig.robot_award)!=null&&z.is_open)?(a(),c("div",{key:0,class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(g=>f("cancelPublic",s),["stop"])},[t(p,{name:"el-icon-Share"}),e[12]||(e[12]=o("span",{class:"ml-2"},"取消发布至广场",-1))],8,Ae)):(j=n(h).getSquareConfig.robot_award)!=null&&j.is_open?(a(),c("div",{key:1,class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(g=>f("share",s),["stop"])},[t(p,{name:"el-icon-Share"}),e[13]||(e[13]=o("span",{class:"ml-2"},"发布至广场",-1))],8,De)):b("",!0),o("div",{class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(g=>f("delete",s),["stop"])},[t(p,{name:"el-icon-Delete"}),e[14]||(e[14]=o("span",{class:"ml-2"},"删除",-1))],8,Te)]}),_:2},1024)]),o("div",Ue,[o("img",{class:"flex-none w-[74px] h-[74px] rounded-full",src:s.image,alt:""},null,8,Fe),o("div",Me,[t(H,{content:s.name},null,8,["content"])]),o("div",Oe,be(s.intro||"这个智能体还没介绍呢～"),1)]),o("div",Ze,[o("div",Ge,[t(p,{name:"el-icon-Position",size:18}),e[15]||(e[15]=o("div",{class:"ml-[8px]"}," 设置智能体 ",-1))]),t(q,{to:{path:"/application/chat",query:{id:s.id}},class:"flex-1 text-tx-regular flex items-center justify-center h-[50px] cursor-pointer hover:text-primary"},{default:l(()=>[t(p,{name:"el-icon-ChatLineSquare",size:18}),e[16]||(e[16]=o("div",{class:"ml-[8px]"},"开始对话",-1))]),_:2},1032,["to"])])]),_:2},1032,["to"])]))),128))])])):b("",!0),n(d).isLogin?b("",!0):(a(),c("div",He,[t(ke)]))])),[[Q,M]])]),_:1})]),n(y)?(a(),C(Se,{key:0,ref_key:"shareRef",ref:N,onClose:e[3]||(e[3]=s=>y.value=!1),onSuccess:U},null,512)):b("",!0)])}}}),To=Ce(Je,[["__scopeId","data-v-bbcfd3ef"]]);export{To as default};
