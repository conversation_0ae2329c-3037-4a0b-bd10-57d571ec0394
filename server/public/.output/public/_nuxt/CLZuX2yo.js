import{_ as Be}from"./eFgaMLiC.js";import{b as He,a as Me,_ as Pe}from"./CCGM0zxW.js";import{l as $e,j as Ne,a as Oe,b as Fe,bz as Ue,bA as We,cD as Ge,f as q,E as Je}from"./CmRxzTqw.js";import{E as Ze}from"./llRQJmEG.js";import{l as Ke,j as S,m as P,b as f,ai as Qe,r as me,k as Xe,E as Ye,n as he,M as p,N as b,a7 as _e,a8 as ye,u as t,O as i,a6 as ge,a3 as k,a5 as xe,$ as et,Z as D,a0 as L,a1 as B,_ as we,ao as tt,ag as be}from"./CUZG7cWw.js";import{u as at}from"./DNOp0HuO.js";import{u as ot,a as it}from"./C-cKpkeq.js";import{u as st}from"./DoCT-qbH.js";import{_ as rt}from"./GcP5Frf5.js";import{g as nt,b as lt,r as ct,a as ut,v as dt,d as pt}from"./qRM0tN96.js";import{c as ft}from"./BluXXrgj.js";import{E as vt}from"./oVx59syQ.js";import{_ as mt}from"./DlAUqK2U.js";import"./DCzKTodP.js";/* empty css        */import"./CH6wv3Pu.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./BYMcWg3Q.js";/* empty css        */import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";/* empty css        */import"./DjHPV-Am.js";import"./DAOx25wS.js";/* empty css        */import"./DwFObZc_.js";import"./DQUFgXGm.js";import"./CRNANWso.js";import"./CHg9aK2B.js";import"./DecTOTC8.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";const ht={class:"h-screen w-screen flex justify-center items-center"},_t=["width","height"],yt={class:"p-[20px] flex h-full relative z-10"},gt={class:"flex-1 h-full flex flex-col"},xt={class:"flex-1 min-h-0"},wt={class:"flex items-center cursor-pointer"},bt={class:"text-xl flex-1 min-w-0 ml-[10px] text-white"},kt={class:"h-full flex"},Rt={class:"h-full flex flex-col items-center w-[160px] justify-end"},Et=["width","height","id"],St={class:"text-xs text-white bg-[rgba(51,51,51,0.3)] py-[5px] px-[10px] rounded my-[10px]"},Tt={class:"w-[400px] h-full flex flex-col mr-[20px] pt-[100px]"},Ct={class:"flex-1 min-h-0 bg-[rgba(0,0,0,0.5)] rounded-[20px] overflow-hidden flex flex-col"},Lt={class:"flex-1 min-h-0"},zt={class:"py-4 px-[20px]"},jt={key:1,class:"h-full flex justify-center text-tx-secondary items-center"},At={key:0,class:"flex justify-center items-center py-[10px]"},It={class:"flex flex-col justify-center items-center"},Vt={key:1,class:"h-screen w-screen flex flex-col items-center justify-center"},qt=Ke({__name:"chat",async setup(Dt){let te,ae;S();const $=$e(),R=Ne(),N=Oe(),z=P(()=>N.query.id),H=P(()=>N.query.squareId),O=P(()=>N.query.cateId),F=Fe(),s=f({}),ke=async()=>{s.value=await nt({id:z.value})},m=f([]);let U=0;const W=async()=>{const a=await ut({square_id:H.value,category_id:O.value,robot_id:z.value,page_size:25e3});if(m.value=a.lists||[],v.value==3){const e=m.value[m.value.length-1];e&&e.id!==U&&(U=e.id,Ae(U))}};[te,ae]=Qe(()=>at(()=>W(),{lazy:!0},"$Mtlbt2SHcf")),await te,ae();const oe=()=>{H.value?$.replace({path:"/robot_square"}):$.replace({path:"/application/layout/robot"})},v=f(0),Re=me({0:"正在初始化对话...",1:"点击开始说话",2:"我在听，您请说...",3:"稍等，让我想一想",4:"正在回复中..."}),G=S();P(()=>v.value==4?s.value.digital.wide_talk_video:s.value.digital.wide_stay_video);const Ee=async()=>{if(!R.isLogin)return R.toggleShowLogin();y.value||(await q.confirm("确定清空记录？"),await lt({square_id:H.value,category_id:O.value,robot_id:z.value}),W())},j=S(),Se=()=>{var a;if(!R.isLogin)return(a=j.value)==null||a.blur(),R.toggleShowLogin();A()};let n=null;const y=f(!1),ie=async(a,e="input")=>{var u;if(!R.isLogin)return R.toggleShowLogin();if(!a)return q.msgError("请输入问题");if(y.value||!z.value)return;I(),h(3);const r=Date.now();y.value=!0,m.value.push({type:1,content:a}),m.value.push({type:2,typing:!0,content:"",key:r}),(u=j.value)==null||u.setInputValue();const o=m.value.find(l=>l.key===r);n=ct({square_id:H.value,cate_id:O.value,robot_id:z.value,question:a,stream:!0}),n.addEventListener("chat",({data:l})=>{const{data:c,index:x}=l;o.content||(o.content=""),o.content+=c}),n.addEventListener("file",({data:l})=>{try{const c=JSON.parse(l.data);o.files=c}catch(c){console.error(c)}}),n.addEventListener("image",({data:l})=>{try{const c=JSON.parse(l.data);o.images=c}catch(c){console.error(c)}}),n.addEventListener("close",async()=>{setTimeout(async()=>{await W(),o.typing=!1,y.value=!1,A()},500)}),n.addEventListener("error",async l=>{var c,x,w;if(h(1),((c=l.data)==null?void 0:c.code)===1100){try{F.getIsShowRecharge?(await q.confirm(`${F.getTokenUnit}数量已用完，请前往充值`),$.push("/user/recharge")):q.msgError(`${F.getTokenUnit}数量已用完。请联系客服增加`)}finally{e==="input"&&((x=j.value)==null||x.setInputValue(a))}return}l.errorType==="connectError"&&q.msgError("请求失败，请重试"),["connectError","responseError"].includes(l.errorType)&&(m.value.splice(m.value.length-2,2),e==="input"&&((w=j.value)==null||w.setInputValue(a))),o.typing=!1,setTimeout(()=>{y.value=!1},200)})},J=S(),se=f(),A=async()=>{var e,r,o;const a=(r=(e=J.value)==null?void 0:e.wrapRef)==null?void 0:r.scrollHeight;(o=J.value)==null||o.setScrollTop(a)},{height:Te}=Ue(se);We(Te,()=>{y.value&&A()},{throttle:500,immediate:!0});const re=f(),T=f(!0),h=a=>{v.value=a},Z=f(!1),K=f(0),E=f(!1),ne=f(0),g=me({id:"audio-canvas",width:80,height:40,minHeight:5,scale:2}),{render:Ce,stopRender:Le,draw:Bt}=ot(g),{start:ze,stop:I,isRecording:V,authorize:je,close:Ht,isOpen:Mt}=it({onstart(){h(2),clearTimeout(re.value),E.value=!1,K.value=Date.now()},async onstop(a){if(Le(),E.value=!1,!Z.value){h(1);return}Z.value=!1,h(3);try{const e=await dt({file:a.blob});if(!e.text){T.value&&C();return}ie(e.text,"voice")}catch{T.value&&C()}},ondata(a){var r;const e=Date.now();E.value&&Ce(a),a.powerLevel>=10&&(clearTimeout(ne.value),v.value=2,E.value=!0,K.value=e,ne.value=setTimeout(()=>{Z.value=!0,clearTimeout(re.value),Q(),I()},2e3)),e-K.value>=((r=s.value.digital)==null?void 0:r.idle_time)*1e3&&(E.value||(Ve(),I()))}}),{play:le,pause:Q,audioPlaying:ce}=st({onstart(){v.value=4,X.value&&(X.value=!1)},onstop(){h(2),T.value?C():h(1)},onerror(){h(1)}}),Ae=async a=>{le(async()=>await ue({type:2,record_id:a}),!1)},C=async()=>{V.value||ze()},Ie=async()=>{if(v.value==4){Q(),C();return}v.value!=3&&(V.value?(T.value=!1,I(),h(1)):(T.value=!0,C()))},ue=async a=>{try{const{url:e}=await pt(a);return e}catch{return h(1),Promise.reject()}},M=f(""),X=f(!1),Ve=async()=>{if(!s.value.is_digital||!s.value.digital_id||s.value.digital.is_disable||(M.value||(M.value=await ue({type:3,record_id:s.value.id})),!M.value))return Promise.reject();X.value=!0;const a=Date.now();m.value.push({type:2,typing:!1,content:s.value.digital.idle_reply,key:a}),await he(),A(),le(M.value,!1)},qe=()=>{n==null||n.removeEventListener("close"),n==null||n.removeEventListener("chat"),n==null||n.removeEventListener("error"),n==null||n.abort(),y.value=!1};Xe(()=>{qe(),Q(),I()});const De=S(),{width:de,height:pe}=Ge(),Y=S(),ee=S(),fe=async a=>new Promise((e,r)=>{const o=document.createElement("video");o.src=a,o.preload="auto",o.loop=!0,o.muted=!0,o.addEventListener("loadedmetadata",u=>{o.width=o.videoWidth,o.height=o.videoHeight,e(o)}),o.addEventListener("error",u=>{r(u)}),o.addEventListener("play",u=>{ve()})}),ve=()=>{if(!G.value)return;const a=de.value*2,e=pe.value*2,r=G.value.getContext("2d");if(!r)return;const o=v.value===4?ee.value:Y.value;if(!o)return;r.clearRect(0,0,a,e);const{videoHeight:u,videoWidth:l}=o;let c=0,x=0,w=l,d=u;if(l/u>=a/e){const _=a*u/e;c=(l-_)/2,w=_}else{const _=e*l/a;x=(u-_)/2,d=_}r.drawImage(o,c,x,w,d,0,0,a,e),requestAnimationFrame(ve)};return Ye(async()=>{if(await ke(),!(!s.value.digital_id||s.value.digital.is_disable)){T.value=!0,Y.value=await fe(s.value.digital.wide_stay_video),ee.value=await fe(s.value.digital.wide_talk_video),Y.value.play(),ee.value.play();try{await je(),C()}catch{h(1)}await he(),A()}}),(a,e)=>{var w;const r=Be,o=Me,u=Pe,l=Je,c=He,x=Ze;return p(),b(we,null,[_e(i("div",ht,e[1]||(e[1]=[i("img",{class:"w-[400px]",src:rt,alt:""},null,-1)]),512),[[ye,t(v)===0]]),_e(i("div",null,[t(s).digital_id&&!((w=t(s).digital)!=null&&w.is_disable)?(p(),b("div",{key:0,ref_key:"containRef",ref:De,class:"h-screen w-screen relative overflow-hidden",style:ge({background:t(s).digital_bg})},[i("canvas",{ref_key:"canvasRef",ref:G,id:"digital-canvas",width:t(de)*2,height:t(pe)*2},null,8,_t),i("div",yt,[i("div",gt,[i("div",xt,[i("div",wt,[i("div",{class:"flex bg-white p-[5px] text-bold rounded-[50%] text-primary shadow-light",onClick:oe},[k(r,{name:"el-icon-Back",size:18})]),i("div",bt,xe(t(s).name),1)])]),e[2]||(e[2]=i("div",{class:"flex justify-center"},null,-1))]),i("div",kt,[i("div",Rt,[i("div",{class:et(["recorder gradient-button",{"recorder--stop":!t(V)&&!t(ce)}]),onClick:Ie},[t(E)?(p(),b("canvas",{key:0,style:ge({width:`${t(g).width}px`,height:`${t(g).height}px`}),width:t(g).width*t(g).scale,height:t(g).height*t(g).scale,id:t(g).id},null,12,Et)):D("",!0),t(V)&&!t(E)?(p(),L(r,{key:1,name:"el-icon-Microphone",size:40})):t(ce)?(p(),L(r,{key:2,name:"local-icon-pause",size:40})):t(V)?D("",!0):(p(),L(r,{key:3,name:"el-icon-Mute",size:40}))],2),i("div",St,[i("div",null,xe(t(Re)[t(v)]),1)])]),i("div",Tt,[i("div",Ct,[i("div",Lt,[t(m).length?(p(),L(t(vt),{key:0,ref_key:"scrollbarRef",ref:J},{default:B(()=>[i("div",zt,[i("div",{ref_key:"innerRef",ref:se},[(p(!0),b(we,null,tt(t(m),(d,_)=>(p(),b("div",{key:d.id+""+_,class:"mt-4 sm:pb-[20px]"},[d.type==1?(p(),L(u,{key:0,type:"right",avatar:t(R).userInfo.avatar,color:"white"},{default:B(()=>[k(o,{content:String(d.content)},null,8,["content"])]),_:2},1032,["avatar"])):D("",!0),d.type==2?(p(),L(u,{key:1,type:"left",time:d.create_time,avatar:t(s).icons?t(s).icons:t(s).image,bg:"#fff"},{default:B(()=>[k(o,{content:String(d.content),type:"html",typing:d.typing,images:d.images,files:d.files,"record-id":d.id,"record-type":2},null,8,["content","typing","images","files","record-id"])]),_:2},1032,["time","avatar"])):D("",!0)]))),128))],512)])]),_:1},512)):(p(),b("div",jt," 暂无聊天记录 "))]),t(y)?(p(),b("div",At,[k(l,{color:"#fff",round:"",onClick:e[0]||(e[0]=d=>{var _;return(_=t(n))==null?void 0:_.abort()})},{default:B(()=>e[3]||(e[3]=[be(" 停止 ")])),_:1})])):D("",!0)]),i("div",null,[k(c,{ref_key:"chatActionRef",ref:j,loading:[3,4].includes(t(v)),menus:t(s).menus,"show-pause":!1,"show-clear":!1,onEnter:ie,onFocus:Se},null,8,["loading","menus"])])]),i("div",It,[i("div",{class:"gradient-button",onClick:Ee},[k(r,{name:"local-icon-clear",size:24})])])])])],4)):(p(),b("div",Vt,[k(x,{description:"该智能体暂未配置形象或形象已被禁用",image:t(ft)},null,8,["image"]),k(l,{type:"primary",round:"",onClick:oe},{default:B(()=>e[4]||(e[4]=[be(" 返回智能体 ")])),_:1})]))],512),[[ye,t(v)!==0]])],64)}}}),za=mt(qt,[["__scopeId","data-v-3e566265"]]);export{za as default};
