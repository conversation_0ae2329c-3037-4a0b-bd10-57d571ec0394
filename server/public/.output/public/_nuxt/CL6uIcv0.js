import{E as C,a as V}from"./CXDY_LVT.js";import{j as E,cZ as S,E as b}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{a as w,h as F}from"./DjwCd26w.js";import{P as L}from"./CaNlADry.js";import{u as N}from"./Bj_9-7Jh.js";import{l as T,j,b as D,r as O,M as i,N as d,a3 as l,a1 as r,u as t,O as u,ag as R,_ as U,ao as q,a0 as I}from"./CUZG7cWw.js";import{_ as P}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */const z={class:"share-popup"},M={class:"h-[100px]"},Z={class:"dialog-footer flex justify-center pb-2"},A=T({__name:"video-share",emits:["success","close"],setup(G,{expose:_,emit:f}){const h=E(),c=f,n=j(),p=D([]),a=O({category_id:"",records_id:""}),y=async()=>{try{const e=await w({type:S.VIDEO,share:1});e.unshift({name:"全部",id:""}),p.value=e}catch(e){console.log("获取视频分类失败=>",e)}},{lockFn:m,isLock:g}=N(async()=>{var e;await F(a),await h.getUser(),(e=n.value)==null||e.close(),c("success",a.records_id)}),x=()=>{c("close")};return _({open:e=>{var o;y(),(o=n.value)==null||o.open(),a.records_id=e}}),(e,o)=>{const k=C,v=V,B=b;return i(),d("div",z,[l(L,{ref_key:"popupRef",ref:n,title:"分享至广场",async:!0,width:"400px",center:!0,cancelButtonText:"",confirmButtonText:"",appendToBody:!1,onConfirm:t(m),onClose:x},{footer:r(()=>[u("div",Z,[l(B,{type:"primary",loading:t(g),class:"!rounded-md",onClick:t(m)},{default:r(()=>o[1]||(o[1]=[R(" 分享至广场 ")])),_:1},8,["loading","onClick"])])]),default:r(()=>[u("div",M,[l(v,{size:"large",class:"w-[360px]",modelValue:t(a).category_id,"onUpdate:modelValue":o[0]||(o[0]=s=>t(a).category_id=s),placeholder:"全部",style:{"--el-fill-color-blank":"#F7F7FB"}},{default:r(()=>[(i(!0),d(U,null,q(t(p),s=>(i(),I(k,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["onConfirm"])])}}}),xe=P(A,[["__scopeId","data-v-0874c328"]]);export{xe as default};
