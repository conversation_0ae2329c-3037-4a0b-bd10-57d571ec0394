import{E as h}from"./CiabO6Xq.js";import{_ as y}from"./eFgaMLiC.js";import{E as g}from"./B7GaOiDz.js";import{cA as w}from"./CmRxzTqw.js";/* empty css        */import"./DP2rzg_V.js";import{l as k,M as a,a0 as V,a1 as c,O as e,N as i,ao as b,a3 as r,u as d,Z as C,a5 as E,_ as I}from"./CUZG7cWw.js";const B={class:"flex-1 overflow-hidden"},N={class:"flex flex-wrap mx-[-6px]"},S=["onClick"],F={class:"pt-[100%] relative h-0 rounded-[12px] overflow-hidden"},L={class:"absolute inset-0 left-0 top-0"},M={key:0,class:"absolute bg-[var(--el-overlay-color-lighter)] inset-0 left-0 top-0 flex items-center justify-center text-white"},$={class:"text-center text-xs"},H=k({__name:"video-style",props:{modelValue:{default:()=>[]},styleList:{}},emits:["update:modelValue"],setup(m,{emit:u}){const p=u,_=m,{modelValue:t}=w(_,p),f=l=>{const o=t.value.findIndex(n=>n===l);o!==-1?t.value.splice(o,1):t.value.push(l)};return(l,o)=>{const n=h,x=y,v=g;return a(),V(v,null,{label:c(()=>o[0]||(o[0]=[e("div",{class:"w-full flex items-center"},[e("span",{class:"font-bold text-tx-primary flex-1"}," 选择风格 ")],-1)])),default:c(()=>[e("div",B,[e("div",N,[(a(!0),i(I,null,b(l.styleList,s=>(a(),i("div",{class:"w-[25%] px-[6px]",key:s.id},[e("div",{class:"h-full cursor-pointer",onClick:j=>f(s.id)},[e("div",F,[e("div",L,[r(n,{src:s.image,class:"h-full w-full"},null,8,["src"])]),d(t).includes(s.id)||d(t).includes(String(s.id))?(a(),i("div",M,[r(x,{name:"el-icon-SuccessFilled",size:20})])):C("",!0)]),e("div",$,E(s.name),1)],8,S)]))),128))])])]),_:1})}}});export{H as _};
