import{a as s,E as n}from"./Bf_xRNbS.js";import"./CmRxzTqw.js";import{_ as r}from"./DlAUqK2U.js";import{a0 as c,M as m,a1 as e,a3 as _,V as o}from"./CUZG7cWw.js";import"./D6yUe_Nr.js";import"./BOx_5T3X.js";const p={};function d(t,i){const a=n,l=s;return m(),c(l,{"model-value":"name"},{default:e(()=>[_(a,{name:"name"},{title:e(()=>[o(t.$slots,"title",{},void 0,!0)]),default:e(()=>[o(t.$slots,"default",{},void 0,!0)]),_:3})]),_:3})}const v=r(p,[["render",d],["__scopeId","data-v-7291479b"]]);export{v as default};
