import{_ as V}from"./DlAUqK2U.js";import{b as n,c as w,M as i,N as g,O as s,a6 as x,a5 as h,ag as y,Z as B,V as p,$ as N}from"./CUZG7cWw.js";const b=["data-error"],k=["placeholder","rows","value","maxlength"],F={key:0,class:"l-textarea-length-maxlength"},O={__name:"index",props:{placeholder:String,maxlength:String,error:String,rows:String,customStyle:Object,contentStyle:Object,showWordLimit:Boolean,modelValue:String},emits:["update:modelValue","focus"],setup(e,{emit:v}){var m;const l=e,r=v,u=n(l.modelValue),o=n(!1),c=n(((m=l.modelValue)==null?void 0:m.length)||0),f=a=>{const t=a||window.event,d=t.srcElement||t.taget;r("update:modelValue",d.value)},S=()=>{o.value=!0,r("focus")};return w(()=>l.modelValue,a=>{u.value=a,c.value=a.length}),(a,t)=>(i(),g("div",{class:N(["l-textarea",{error:e.error,focus:o.value}]),"data-error":e.error,style:x(e.customStyle)},[s("textarea",{class:"l-textarea__inner",placeholder:e.placeholder,rows:e.rows,value:u.value,maxlength:e.maxlength,style:x(e.contentStyle),onFocus:S,onBlur:t[0]||(t[0]=d=>o.value=!1),onInput:f},null,44,k),e.showWordLimit?(i(),g("div",F,[s("span",null,h(c.value),1),t[1]||(t[1]=y(" / ")),s("span",null,h(e.maxlength),1)])):B("",!0),p(a.$slots,"length-suffix",{},void 0,!0)],14,b))}},j=V(O,[["__scopeId","data-v-2e3864ec"]]);export{j as _};
