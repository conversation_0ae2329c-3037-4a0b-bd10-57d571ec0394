import{_ as l}from"./eFgaMLiC.js";import{b as n}from"./CmRxzTqw.js";import{l as a,m as c,M as p,N as r,a3 as m,u as i}from"./CUZG7cWw.js";import"./DlAUqK2U.js";const h=a({__name:"fold",setup(_){const e=n(),o=c(()=>e.isCollapsed),s=()=>{e.toggleCollapsed()};return(f,d)=>{const t=l;return p(),r("div",{class:"fold h-full cursor-pointer flex items-center pr-2 text-white",onClick:s},[m(t,{name:`local-icon-${i(o)?"close":"open"}`,size:24},null,8,["name"])])}}});export{h as default};
