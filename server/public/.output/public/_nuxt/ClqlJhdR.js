import{E as c}from"./oVx59syQ.js";import{E as g}from"./CiabO6Xq.js";import{E as x}from"./CUKNHy7a.js";import{b as y,v as D}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{r as E,x as V,y as w,f as e,e as v}from"./-CaxLuW0.js";import L from"./BE2FalaX.js";import b from"./DDAu8SNr.js";import{_ as k}from"./B6SOVFzv.js";import S from"./B6w0CGjW.js";import A from"./DmYkPQGN.js";import P from"./BB0faANF.js";import{D as z}from"./CoT3rpTv.js";import{DrawModeEnum as i}from"./tONJIxwY.js";import{l as U,E as B,u as o,M as l,N as a,a3 as r,a1 as p,a7 as M,O as s}from"./CUZG7cWw.js";import{_ as N}from"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./eFgaMLiC.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CXAJ--Vj.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";/* empty css        */import"./8fUKBaFv.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./D5Svi-lq.js";import"./DCzKTodP.js";import"./CfDE0MAs.js";import"./Cs0_Uid5.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./DJi8L2lq.js";import"./C-n0m2hZ.js";import"./CXDY_LVT.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";/* empty css        *//* empty css        */import"./Dg_4RlNK.js";import"./CRNANWso.js";import"./Cm9Fkrh-.js";import"./DjwCd26w.js";import"./CaNlADry.js";import"./Bj_9-7Jh.js";import"./f66KDjIM.js";import"./TR-GuQrR.js";import"./BWdDF8rn.js";import"./C3s9J3qB.js";const q={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},C={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},I={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},O=U({__name:"dalle",setup(R){const n=y();return B(()=>{E({draw_api:i.DALLE3,draw_model:i.DALLE3,action:"generate",prompt:"",negative_prompt:"",size:"1024x1024"}),V.model=i.DALLE3,w()}),(j,t)=>{const d=c,u=g,f=x,_=D;return o(n).config.switch.dalle3_status?(l(),a("div",q,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:p(()=>[s("div",C,[r(L,{modelValue:o(e).prompt,"onUpdate:modelValue":t[0]||(t[0]=m=>o(e).prompt=m),model:o(i).DALLE3},null,8,["modelValue","model"]),r(S,{modelValue:o(e).size,"onUpdate:modelValue":t[1]||(t[1]=m=>o(e).size=m)},null,8,["modelValue"]),r(A,{modelValue:o(e).style,"onUpdate:modelValue":t[2]||(t[2]=m=>o(e).style=m)},null,8,["modelValue"]),r(P,{modelValue:o(e).quality,"onUpdate:modelValue":t[3]||(t[3]=m=>o(e).quality=m)},null,8,["modelValue"])]),r(k)]),_:1}),M(r(b,{"element-loading-text":"正在加载数据..."},null,512),[[_,o(v)]])])):(l(),a("div",I,[r(f,null,{icon:p(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(z)},null,8,["src"])]),title:p(()=>t[4]||(t[4]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),Xo=N(O,[["__scopeId","data-v-9879be3f"]]);export{Xo as default};
