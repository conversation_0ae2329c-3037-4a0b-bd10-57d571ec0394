import{l as ne,j as z,r as j,b as ie,ai as M,M as i,N as l,a3 as n,a1 as d,O as r,a7 as $,u as a,a0 as P,Z as _,a8 as le,aG as ce,_ as de,ao as ue,$ as S,a5 as h,ag as D,a4 as R,aH as _e}from"./CUZG7cWw.js";import{E as pe}from"./CiabO6Xq.js";import{_ as me}from"./eFgaMLiC.js";import{_ as fe}from"./CbQsrhNE.js";import{E as ge}from"./DluKwKHO.js";import{E as ye}from"./Zz2DnF66.js";import{W as xe}from"./BZBRZdpQ.js";import{l as we,j as he,cF as ve,cZ as be,H as ke,E as Ce,ct as Ee,f as Pe}from"./CmRxzTqw.js";import{u as Se,_ as Be}from"./DrxPZuc-.js";import{E as Ve}from"./C9jirCEY.js";/* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{u as F}from"./DNOp0HuO.js";import{b as qe}from"./CRNANWso.js";import{b as Ie,m as Le}from"./CJgd20ip.js";import{a as ze}from"./DjwCd26w.js";import{e as je}from"./BhXe-NXN.js";import{_ as Me}from"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./DCTLXrZ8.js";import"./DYjlFFbo.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./Bu_nKEGp.js";const $e={class:"flex-1 min-h-0 mx-[16px] relative"},De=["onClick"],Re={class:"flex-1 min-h-[70vh] overflow-hidden mx-auto",style:{"padding-bottom":"100px"}},Fe=["infinite-scroll-disabled"],Te=["id","onClick"],Ne={class:"w-[100px] h-[100px] flex items-center justify-center flex-none relative"},Ue={key:1,class:"text-tx-secondary"},Ae={key:2,class:"absolute inset-0 flex items-center justify-center text-white"},Oe={key:3,class:"absolute inset-0 flex items-center justify-center text-white"},We={class:"flex-1 ml-[20px]"},He={key:0,class:"mt-[12px] text-tx-secondary"},Ye={class:"flex justify-between mt-[12px]"},Ze={key:0,class:"flex items-center"},Ge={class:"text-[#BBBBBB] ml-[6px] w-[100px] truncate"},Je={class:"flex items-center mt-[4px] text-tx-secondary"},Ke={class:"flex items-center"},Qe=["onClick"],Xe=["onClick"],et={key:1,class:"flex justify-center items-center mt-[50px]"},tt={class:"flex flex-col justify-center items-center w-full h-[60vh]"},st={class:"fixed pb-[32px] bottom-0 left-[112px] right-[34px] bg-page"},ot=ne({__name:"music",props:{keyword:{}},async setup(T){let p,v;const N=T,U=we(),b=he(),A=z(null),{playing:B,currentId:y,setCurrentId:O,togglePlay:W,setMusic:H}=Se(),m=j({page_no:0,page_size:20,keyword:"",category_id:""}),Y={4e3:{rowPerView:4},2e3:{rowPerView:3},1800:{rowPerView:3},1600:{rowPerView:3},1440:{rowPerView:2},1360:{rowPerView:2},1280:{rowPerView:2},1024:{rowPerView:2}},s=j({first:!0,more:!0,count:0,loading:!1,lists:[]}),V=ie(0),{data:q}=([p,v]=M(()=>F(()=>ze({type:be.MUSIC}),{default(){return[]},transform(t){return[{id:"",name:"全部"}].concat(t)},lazy:!0},"$ydYDo0Bg16")),p=await p,v(),p);[p,v]=M(()=>F(()=>C(),{lazy:!0},"$Fywk2foRYg")),await p,v();const C=async()=>{if(!s.loading){if(s.more)m.page_no+=1;else return;s.loading=!0;try{const t=await Ie(m),{lists:o,page_no:c,page_size:x,count:f}=t;if(c*x>f&&(s.more=!1),c==1?s.lists=o:s.lists=[...s.lists,...o],o.length){const g=s.lists.map(u=>(u.square_id=u.id,u.id=u.records_id,u));H(g),y.value=s.lists[0].records_id}}finally{setTimeout(()=>s.loading=!1,200)}}},k=()=>{m.page_no=0,s.more=!0,C()},Z=t=>{U.push({path:"/music/player",query:{id:t.square_id}})},G=t=>{if(t.records_id==y.value){W();return}O(t.records_id)},J=async t=>{if(!b.isLogin){b.toggleShowLogin(!0);return}await Le({records_id:t.square_id,status:t.is_collect?0:1}),m.category_id===0?k():t.is_collect=t.is_collect?0:1},K=async(t,o)=>{if(!b.isLogin){b.toggleShowLogin(!0);return}try{const c=await $request.get({url:t,responseType:"blob",baseURL:""},{isReturnDefaultResponse:!0,apiPrefix:""});console.log(c);const x=new Blob([c._data],{type:c.headers.get("Content-Type")}),f=window.URL.createObjectURL(x);qe(f,o)}catch{Pe.msgError("文件下载失败")}},Q=z(),X=t=>{Q.value=t,console.log(t)},I=t=>{var o;V.value=t,m.category_id=(o=q.value[t])==null?void 0:o.id,k()};return I(0),ve(()=>N.keyword,t=>{m.keyword=t,k()},{debounce:500}),(t,o)=>{const c=_e,x=ce,f=pe,g=me,u=fe,ee=ge,L=ye,te=xe,se=ke,oe=Ce,re=Be,ae=Ve;return i(),l("div",$e,[n(x,{slidesPerView:"auto",spaceBetween:16,class:"category-lists",onSwiper:X,style:{padding:"10px 0"}},{default:d(()=>[(i(!0),l(de,null,ue(a(q),(e,w)=>(i(),P(c,{key:e.id,style:{width:"auto","margin-right":"12px"}},{default:d(()=>[Object.keys(e).includes("name")?(i(),l("div",{key:0,class:S(["category-item bg-white",{"is-active":a(V)===w}]),onClick:E=>I(w)},h(e.name),11,De)):_("",!0)]),_:2},1024))),128))]),_:1}),r("div",Re,[$((i(),l("div",{class:"model-lists mb-[10px] mx-[0px]","infinite-scroll-distance":"50","infinite-scroll-delay":200,"infinite-scroll-disabled":!a(s).more},[a(s).lists.length?(i(),P(te,{key:0,ref_key:"waterFull",ref:A,delay:100,list:a(s).lists,width:315,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:Y},{item:d(({item:e})=>{var w;return[r("div",{class:"flex bg-body p-[20px] rounded-[12px] hover:bg-[#EEF2FF]",id:`music-item-${e.id}`,onClick:E=>G(e)},[r("div",Ne,[e.image_url?(i(),P(f,{key:0,src:e.image_url,class:"w-full h-full rounded-[12px]"},null,8,["src"])):(i(),l("div",Ue,[n(g,{name:"local-icon-music1",size:45})])),a(y)==e.records_id&&a(B)?(i(),l("div",Ae,[n(g,{name:"local-icon-pause1",size:20})])):_("",!0),a(y)==e.records_id&&!a(B)?(i(),l("div",Oe,[n(g,{name:"local-icon-play",size:20})])):_("",!0)]),r("div",We,[n(u,{class:S(["text-[16px] font-bold",{"!text-primary":a(y)===e.records_id}]),to:{path:"/music/player",query:{id:e.square_id}}},{default:d(()=>[D(h(e.title),1)]),_:2},1032,["class","to"]),e.tags?(i(),l("div",He,h(e.tags),1)):_("",!0),r("div",Ye,[e.user_info?(i(),l("div",Ze,[n(ee,{size:28,src:(w=e==null?void 0:e.user_info)==null?void 0:w.image},null,8,["src"]),r("p",Ge,h(e.user_info.name),1)])):_("",!0),r("div",Je,h(e.duration),1),r("div",Ke,[n(L,{effect:"dark",content:"收藏 / 取消收藏",placement:"bottom"},{default:d(()=>[r("div",{class:"image-praise relative dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",onClick:R(E=>J(e),["stop"])},[r("div",{class:S(["praise-animate",e.is_collect?"praise-entry":"praise-leave"])},null,2)],8,Qe)]),_:2},1024),n(L,{effect:"dark",content:"下载音乐",placement:"bottom"},{default:d(()=>[r("div",{onClick:R(E=>K(e.audio_url,e.title),["stop"])},[n(g,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Download",size:"24",color:"#556477"})],8,Xe)]),_:2},1024)])])])],8,Te)]}),_:1},8,["list"])):_("",!0),a(s).loading?(i(),l("div",et,[n(se,{size:"25",class:"is-loading"},{default:d(()=>[n(a(Ee))]),_:1}),o[0]||(o[0]=r("span",{class:"mt-[4px] ml-[10px] text-[#999999]"},"加载中...",-1))])):_("",!0),$(r("div",tt,[n(f,{class:"w-[200px] h-[200px]",src:a(je)},null,8,["src"]),o[2]||(o[2]=r("div",{class:"text-tx-regular mb-4"},"当前选择暂无音乐～",-1)),n(oe,{type:"primary",onClick:k},{default:d(()=>o[1]||(o[1]=[D(" 点击刷新 ")])),_:1})],512),[[le,!a(s).lists.length&&!a(s).loading]])],8,Fe)),[[ae,C]])]),r("div",st,[n(re,{ref:"musicPlayerRef",class:"bg-body rounded-[12px]",onTitle:Z},null,512)])])}}}),Lt=Me(ot,[["__scopeId","data-v-6410fed0"]]);export{Lt as default};
