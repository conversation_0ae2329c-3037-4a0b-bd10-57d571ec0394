import{E as R,a as U}from"./DHUC3PVh.js";import{b as j,a as G}from"./C9f7n97H.js";import{_ as M}from"./Bv29pan0.js";import{E as J}from"./CiabO6Xq.js";import{E as K}from"./llRQJmEG.js";import{E as F}from"./oVx59syQ.js";import{cM as P,cN as Z,f as q}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        */import{u as E}from"./DNOp0HuO.js";import{u as H,I as C,c as Q}from"./DymDsCmz.js";import{e as W}from"./BhXe-NXN.js";import{l as X,r as Y,ai as B,m as w,M as l,N as p,O as s,a3 as d,a1 as m,u as a,_ as g,ao as b,a0 as V,ag as ee,a5 as te,$ as N,Z as ae}from"./CUZG7cWw.js";import{_ as oe}from"./DlAUqK2U.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./eFgaMLiC.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./DoCT-qbH.js";import"./CiYvFM4x.js";import"./DY7CbrCZ.js";import"./Bfmn7p7A.js";const se={class:"avatar-select h-full flex flex-col"},ne={class:"px-main"},re={class:"pt-[5px]"},le={class:"flex-1 min-h-0"},ie={class:"p-main"},ce={key:0,class:"flex flex-wrap mx-[-7px]"},pe=["onClick"],de={class:"absolute inset-0"},me=X({__name:"background",async setup(ue){let n,u;const i=H(),v=[{type:1,label:"竖版背景"},{type:2,label:"横版背景"}],r=Y({type:1,category_id:0}),{data:z}=([n,u]=B(()=>E(()=>P(),{lazy:!0},"$kOs1tBC3ke")),n=await n,u(),n),{data:f,refresh:x,pending:T}=([n,u]=B(()=>E(()=>Z(r),{lazy:!0},"$92jT89Nd84")),n=await n,u(),n),I=async o=>{var t,c,_;if(((t=h.value)==null?void 0:t.type)!==i.defaultSize.resolution){const y=Q[(c=h.value)==null?void 0:c.type];(_=i.getCanvasJson())!=null&&_.objects.length&&await q.confirm("是否确认更改画布尺寸？当前画面所有设置将被重置且无法恢复"),i.changeSize(y),i.initObject()}i.addImage(o.url,C.BACKGROUND,o)},h=w(()=>v.find(o=>o.type===r.type)),S=w(()=>{var t;const o=(t=i.canvasJson.objects)==null?void 0:t.find(c=>c.customType===C.BACKGROUND);return o!=null&&o.data?o.data:null});return(o,t)=>{const c=U,_=R,y=j,D=G,$=M,L=J,O=K,A=F;return l(),p("div",se,[s("div",ne,[s("div",re,[d(_,{modelValue:a(r).type,"onUpdate:modelValue":t[0]||(t[0]=e=>a(r).type=e),onTabChange:t[1]||(t[1]=e=>{f.value=[],a(x)()})},{default:m(()=>[(l(),p(g,null,b(v,e=>d(c,{key:e.type,label:e.label,name:e.type},null,8,["label","name"])),64))]),_:1},8,["modelValue"])]),d($,{"default-height":42,class:"my-[-5px]"},{default:m(()=>[d(D,{modelValue:a(r).category_id,"onUpdate:modelValue":t[2]||(t[2]=e=>a(r).category_id=e),class:"el-radio-group-margin",onChange:t[3]||(t[3]=e=>a(x)())},{default:m(()=>[(l(!0),p(g,null,b(a(z),e=>(l(),V(y,{key:e.id,label:e.id},{default:m(()=>[ee(te(e.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("div",le,[d(A,null,{default:m(()=>[s("div",ie,[a(f).length?(l(),p("div",ce,[(l(!0),p(g,null,b(a(f),e=>{var k;return l(),p("div",{key:e.id,class:"w-[50%]"},[s("div",{class:"px-[7px] mb-[14px]",onClick:_e=>I(e)},[s("div",{class:N(["border border-solid border-br-light rounded-md p-[10px] cursor-pointer",{"!border-primary":((k=a(S))==null?void 0:k.id)==e.id}])},[s("div",null,[s("div",{class:N(["pic-wrap h-0 relative",{"pt-[177%]":a(r).type==1,"pt-[56.3%]":a(r).type==2}])},[s("div",de,[d(L,{src:e.url,class:"w-full h-full",fit:"contain",lazy:""},null,8,["src"])])],2)])],2)],8,pe)])}),128))])):a(T)?ae("",!0):(l(),V(O,{key:1,image:a(W),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}}),je=oe(me,[["__scopeId","data-v-c2d2a601"]]);export{je as default};
