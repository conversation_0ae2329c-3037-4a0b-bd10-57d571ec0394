import{I as ue,R as k,l as _,b as de,m as J,M as h,a0 as U,a1 as C,N as F,_ as ke,ao as Ee,$ as y,u as t,af as ce,V as R,Z as b,O as N,a4 as D,a3 as S,a5 as Y,a6 as $e,aA as Re,i as Ce,j as W,aB as x,U as Pe,c as ee,o as we,q as Le,t as Se,aq as te,W as se}from"./CUZG7cWw.js";import{S as Q,aG as pe,I as M,V as m,a2 as G,a5 as Fe,J as q,aL as A,H as O,bJ as Ue,bK as _e,bL as Te,R as Oe,aa as De,bM as Be,M as K,bN as je,h as Ne,aP as qe,N as Me}from"./CmRxzTqw.js";import{E as Ae}from"./DikNcrXK.js";import{c as ae}from"./Bs9Zhtqd.js";import{i as Ke}from"./D8e5izeA.js";const fe=Symbol("uploadContextKey"),He="ElUpload";class Ie extends Error{constructor(l,e,p,u){super(l),this.name="UploadAjaxError",this.status=e,this.method=p,this.url=u}}function oe(o,l,e){let p;return e.response?p=`${e.response.error||e.response}`:e.responseText?p=`${e.responseText}`:p=`fail to ${l.method} ${o} ${e.status}`,new Ie(p,e.status,l.method,o)}function ze(o){const l=o.responseText||o.response;if(!l)return l;try{return JSON.parse(l)}catch{return l}}const Ve=o=>{typeof XMLHttpRequest>"u"&&Q(He,"XMLHttpRequest is undefined");const l=new XMLHttpRequest,e=o.action;l.upload&&l.upload.addEventListener("progress",d=>{const v=d;v.percent=d.total>0?d.loaded/d.total*100:0,o.onProgress(v)});const p=new FormData;if(o.data)for(const[d,v]of Object.entries(o.data))ue(v)&&v.length?p.append(d,...v):p.append(d,v);p.append(o.filename,o.file,o.file.name),l.addEventListener("error",()=>{o.onError(oe(e,o,l))}),l.addEventListener("load",()=>{if(l.status<200||l.status>=300)return o.onError(oe(e,o,l));o.onSuccess(ze(l))}),l.open(o.method,e,!0),o.withCredentials&&"withCredentials"in l&&(l.withCredentials=!0);const u=o.headers||{};if(u instanceof Headers)u.forEach((d,v)=>l.setRequestHeader(v,d));else for(const[d,v]of Object.entries(u))pe(v)||l.setRequestHeader(d,String(v));return l.send(p),l},ve=["text","picture","picture-card"];let Xe=1;const Z=()=>Date.now()+Xe++,me=M({action:{type:String,default:"#"},headers:{type:m(Object)},method:{type:String,default:"post"},data:{type:m([Object,Function,Promise]),default:()=>G({})},multiple:{type:Boolean,default:!1},name:{type:String,default:"file"},drag:{type:Boolean,default:!1},withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:{type:String,default:""},fileList:{type:m(Array),default:()=>G([])},autoUpload:{type:Boolean,default:!0},listType:{type:String,values:ve,default:"text"},httpRequest:{type:m(Function),default:Ve},disabled:Boolean,limit:Number}),Je=M({...me,beforeUpload:{type:m(Function),default:k},beforeRemove:{type:m(Function)},onRemove:{type:m(Function),default:k},onChange:{type:m(Function),default:k},onPreview:{type:m(Function),default:k},onSuccess:{type:m(Function),default:k},onProgress:{type:m(Function),default:k},onError:{type:m(Function),default:k},onExceed:{type:m(Function),default:k},crossorigin:{type:m(String)}}),We=M({files:{type:m(Array),default:()=>G([])},disabled:{type:Boolean,default:!1},handlePreview:{type:m(Function),default:k},listType:{type:String,values:ve,default:"text"},crossorigin:{type:m(String)}}),Ge={remove:o=>!!o},Ze=["onKeydown"],Qe=["src","crossorigin"],Ye=["onClick"],xe=["title"],et=["onClick"],tt=["onClick"],st=_({name:"ElUploadList"}),at=_({...st,props:We,emits:Ge,setup(o,{emit:l}){const e=o,{t:p}=Fe(),u=q("upload"),d=q("icon"),v=q("list"),E=A(),g=de(!1),$=J(()=>[u.b("list"),u.bm("list",e.listType),u.is("disabled",e.disabled)]),w=f=>{l("remove",f)};return(f,n)=>(h(),U(Re,{tag:"ul",class:y(t($)),name:t(v).b()},{default:C(()=>[(h(!0),F(ke,null,Ee(f.files,s=>(h(),F("li",{key:s.uid||s.name,class:y([t(u).be("list","item"),t(u).is(s.status),{focusing:g.value}]),tabindex:"0",onKeydown:ce(i=>!t(E)&&w(s),["delete"]),onFocus:n[0]||(n[0]=i=>g.value=!0),onBlur:n[1]||(n[1]=i=>g.value=!1),onClick:n[2]||(n[2]=i=>g.value=!1)},[R(f.$slots,"default",{file:s},()=>[f.listType==="picture"||s.status!=="uploading"&&f.listType==="picture-card"?(h(),F("img",{key:0,class:y(t(u).be("list","item-thumbnail")),src:s.url,crossorigin:f.crossorigin,alt:""},null,10,Qe)):b("v-if",!0),s.status==="uploading"||f.listType!=="picture-card"?(h(),F("div",{key:1,class:y(t(u).be("list","item-info"))},[N("a",{class:y(t(u).be("list","item-name")),onClick:D(i=>f.handlePreview(s),["prevent"])},[S(t(O),{class:y(t(d).m("document"))},{default:C(()=>[S(t(Ue))]),_:1},8,["class"]),N("span",{class:y(t(u).be("list","item-file-name")),title:s.name},Y(s.name),11,xe)],10,Ye),s.status==="uploading"?(h(),U(t(Ae),{key:0,type:f.listType==="picture-card"?"circle":"line","stroke-width":f.listType==="picture-card"?6:2,percentage:Number(s.percentage),style:$e(f.listType==="picture-card"?"":"margin-top: 0.5rem")},null,8,["type","stroke-width","percentage","style"])):b("v-if",!0)],2)):b("v-if",!0),N("label",{class:y(t(u).be("list","item-status-label"))},[f.listType==="text"?(h(),U(t(O),{key:0,class:y([t(d).m("upload-success"),t(d).m("circle-check")])},{default:C(()=>[S(t(_e))]),_:1},8,["class"])):["picture-card","picture"].includes(f.listType)?(h(),U(t(O),{key:1,class:y([t(d).m("upload-success"),t(d).m("check")])},{default:C(()=>[S(t(Te))]),_:1},8,["class"])):b("v-if",!0)],2),t(E)?b("v-if",!0):(h(),U(t(O),{key:2,class:y(t(d).m("close")),onClick:i=>w(s)},{default:C(()=>[S(t(Oe))]),_:2},1032,["class","onClick"])),b(" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn"),b(" This is a bug which needs to be fixed "),b(" TODO: Fix the incorrect navigation interaction "),t(E)?b("v-if",!0):(h(),F("i",{key:3,class:y(t(d).m("close-tip"))},Y(t(p)("el.upload.deleteTip")),3)),f.listType==="picture-card"?(h(),F("span",{key:4,class:y(t(u).be("list","item-actions"))},[N("span",{class:y(t(u).be("list","item-preview")),onClick:i=>f.handlePreview(s)},[S(t(O),{class:y(t(d).m("zoom-in"))},{default:C(()=>[S(t(De))]),_:1},8,["class"])],10,et),t(E)?b("v-if",!0):(h(),F("span",{key:0,class:y(t(u).be("list","item-delete")),onClick:i=>w(s)},[S(t(O),{class:y(t(d).m("delete"))},{default:C(()=>[S(t(Be))]),_:1},8,["class"])],10,tt))],2)):b("v-if",!0)])],42,Ze))),128)),R(f.$slots,"append")]),_:3},8,["class","name"]))}});var ne=K(at,[["__file","upload-list.vue"]]);const ot=M({disabled:{type:Boolean,default:!1}}),nt={file:o=>ue(o)},lt=["onDrop","onDragover"],ye="ElUploadDrag",rt=_({name:ye}),it=_({...rt,props:ot,emits:nt,setup(o,{emit:l}){Ce(fe)||Q(ye,"usage: <el-upload><el-upload-dragger /></el-upload>");const p=q("upload"),u=de(!1),d=A(),v=g=>{if(d.value)return;u.value=!1,g.stopPropagation();const $=Array.from(g.dataTransfer.files);l("file",$)},E=()=>{d.value||(u.value=!0)};return(g,$)=>(h(),F("div",{class:y([t(p).b("dragger"),t(p).is("dragover",u.value)]),onDrop:D(v,["prevent"]),onDragover:D(E,["prevent"]),onDragleave:$[0]||($[0]=D(w=>u.value=!1,["prevent"]))},[R(g.$slots,"default")],42,lt))}});var ut=K(it,[["__file","upload-dragger.vue"]]);const dt=M({...me,beforeUpload:{type:m(Function),default:k},onRemove:{type:m(Function),default:k},onStart:{type:m(Function),default:k},onSuccess:{type:m(Function),default:k},onProgress:{type:m(Function),default:k},onError:{type:m(Function),default:k},onExceed:{type:m(Function),default:k}}),ct=["onKeydown"],pt=["name","multiple","accept"],ft=_({name:"ElUploadContent",inheritAttrs:!1}),vt=_({...ft,props:dt,setup(o,{expose:l}){const e=o,p=q("upload"),u=A(),d=W({}),v=W(),E=a=>{if(a.length===0)return;const{autoUpload:c,limit:r,fileList:P,multiple:L,onStart:B,onExceed:H}=e;if(r&&P.length+a.length>r){H(a,P);return}L||(a=a.slice(0,1));for(const I of a){const j=I;j.uid=Z(),B(j),c&&g(j)}},g=async a=>{if(v.value.value="",!e.beforeUpload)return w(a);let c,r={};try{const L=e.data,B=e.beforeUpload(a);r=x(e.data)?ae(e.data):e.data,c=await B,x(e.data)&&Ke(L,r)&&(r=ae(e.data))}catch{c=!1}if(c===!1){e.onRemove(a);return}let P=a;c instanceof Blob&&(c instanceof File?P=c:P=new File([c],a.name,{type:a.type})),w(Object.assign(P,{uid:a.uid}),r)},$=async(a,c)=>Pe(a)?a(c):a,w=async(a,c)=>{const{headers:r,data:P,method:L,withCredentials:B,name:H,action:I,onProgress:j,onSuccess:ge,onError:he,httpRequest:be}=e;try{c=await $(c??P,a)}catch{e.onRemove(a);return}const{uid:z}=a,V={headers:r||{},withCredentials:B,file:a,data:c,method:L,filename:H,action:I,onProgress:T=>{j(T,a)},onSuccess:T=>{ge(T,a),delete d.value[z]},onError:T=>{he(T,a),delete d.value[z]}},X=be(V);d.value[z]=X,X instanceof Promise&&X.then(V.onSuccess,V.onError)},f=a=>{const c=a.target.files;c&&E(Array.from(c))},n=()=>{u.value||(v.value.value="",v.value.click())},s=()=>{n()};return l({abort:a=>{je(d.value).filter(a?([r])=>String(a.uid)===r:()=>!0).forEach(([r,P])=>{P instanceof XMLHttpRequest&&P.abort(),delete d.value[r]})},upload:g}),(a,c)=>(h(),F("div",{class:y([t(p).b(),t(p).m(a.listType),t(p).is("drag",a.drag)]),tabindex:"0",onClick:n,onKeydown:ce(D(s,["self"]),["enter","space"])},[a.drag?(h(),U(ut,{key:0,disabled:t(u),onFile:E},{default:C(()=>[R(a.$slots,"default")]),_:3},8,["disabled"])):R(a.$slots,"default",{key:1}),N("input",{ref_key:"inputRef",ref:v,class:y(t(p).e("input")),name:a.name,multiple:a.multiple,accept:a.accept,type:"file",onChange:f,onClick:c[0]||(c[0]=D(()=>{},["stop"]))},null,42,pt)],42,ct))}});var le=K(vt,[["__file","upload-content.vue"]]);const re="ElUpload",ie=o=>{var l;(l=o.url)!=null&&l.startsWith("blob:")&&URL.revokeObjectURL(o.url)},mt=(o,l)=>{const e=Ne(o,"fileList",void 0,{passive:!0}),p=n=>e.value.find(s=>s.uid===n.uid);function u(n){var s;(s=l.value)==null||s.abort(n)}function d(n=["ready","uploading","success","fail"]){e.value=e.value.filter(s=>!n.includes(s.status))}const v=(n,s)=>{const i=p(s);i&&(console.error(n),i.status="fail",e.value.splice(e.value.indexOf(i),1),o.onError(n,i,e.value),o.onChange(i,e.value))},E=(n,s)=>{const i=p(s);i&&(o.onProgress(n,i,e.value),i.status="uploading",i.percentage=Math.round(n.percent))},g=(n,s)=>{const i=p(s);i&&(i.status="success",i.response=n,o.onSuccess(n,i,e.value),o.onChange(i,e.value))},$=n=>{pe(n.uid)&&(n.uid=Z());const s={name:n.name,percentage:0,status:"ready",size:n.size,raw:n,uid:n.uid};if(o.listType==="picture-card"||o.listType==="picture")try{s.url=URL.createObjectURL(n)}catch(i){qe(re,i.message),o.onError(i,s,e.value)}e.value=[...e.value,s],o.onChange(s,e.value)},w=async n=>{const s=n instanceof File?p(n):n;s||Q(re,"file to be removed not found");const i=a=>{u(a);const c=e.value;c.splice(c.indexOf(a),1),o.onRemove(a,c),ie(a)};o.beforeRemove?await o.beforeRemove(s,e.value)!==!1&&i(s):i(s)};function f(){e.value.filter(({status:n})=>n==="ready").forEach(({raw:n})=>{var s;return n&&((s=l.value)==null?void 0:s.upload(n))})}return ee(()=>o.listType,n=>{n!=="picture-card"&&n!=="picture"||(e.value=e.value.map(s=>{const{raw:i,url:a}=s;if(!a&&i)try{s.url=URL.createObjectURL(i)}catch(c){o.onError(c,s,e.value)}return s}))}),ee(e,n=>{for(const s of n)s.uid||(s.uid=Z()),s.status||(s.status="success")},{immediate:!0,deep:!0}),{uploadFiles:e,abort:u,clearFiles:d,handleError:v,handleProgress:E,handleStart:$,handleSuccess:g,handleRemove:w,submit:f,revokeFileObjectURL:ie}},yt=_({name:"ElUpload"}),gt=_({...yt,props:Je,setup(o,{expose:l}){const e=o,p=A(),u=W(),{abort:d,submit:v,clearFiles:E,uploadFiles:g,handleStart:$,handleError:w,handleRemove:f,handleSuccess:n,handleProgress:s,revokeFileObjectURL:i}=mt(e,u),a=J(()=>e.listType==="picture-card"),c=J(()=>({...e,fileList:g.value,onStart:$,onProgress:s,onSuccess:n,onError:w,onRemove:f}));return we(()=>{g.value.forEach(i)}),Le(fe,{accept:Se(e,"accept")}),l({abort:d,submit:v,clearFiles:E,handleStart:$,handleRemove:f}),(r,P)=>(h(),F("div",null,[t(a)&&r.showFileList?(h(),U(ne,{key:0,disabled:t(p),"list-type":r.listType,files:t(g),crossorigin:r.crossorigin,"handle-preview":r.onPreview,onRemove:t(f)},te({append:C(()=>[S(le,se({ref_key:"uploadRef",ref:u},t(c)),{default:C(()=>[r.$slots.trigger?R(r.$slots,"trigger",{key:0}):b("v-if",!0),!r.$slots.trigger&&r.$slots.default?R(r.$slots,"default",{key:1}):b("v-if",!0)]),_:3},16)]),_:2},[r.$slots.file?{name:"default",fn:C(({file:L})=>[R(r.$slots,"file",{file:L})])}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):b("v-if",!0),!t(a)||t(a)&&!r.showFileList?(h(),U(le,se({key:1,ref_key:"uploadRef",ref:u},t(c)),{default:C(()=>[r.$slots.trigger?R(r.$slots,"trigger",{key:0}):b("v-if",!0),!r.$slots.trigger&&r.$slots.default?R(r.$slots,"default",{key:1}):b("v-if",!0)]),_:3},16)):b("v-if",!0),r.$slots.trigger?R(r.$slots,"default",{key:2}):b("v-if",!0),R(r.$slots,"tip"),!t(a)&&r.showFileList?(h(),U(ne,{key:3,disabled:t(p),"list-type":r.listType,files:t(g),crossorigin:r.crossorigin,"handle-preview":r.onPreview,onRemove:t(f)},te({_:2},[r.$slots.file?{name:"default",fn:C(({file:L})=>[R(r.$slots,"file",{file:L})])}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):b("v-if",!0)]))}});var ht=K(gt,[["__file","upload.vue"]]);const Ct=Me(ht);export{Ct as E};
