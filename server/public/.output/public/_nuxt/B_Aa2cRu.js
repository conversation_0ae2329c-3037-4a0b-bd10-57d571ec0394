import{_ as F}from"./eFgaMLiC.js";import{E as U}from"./C7tIPmrK.js";import{E as y}from"./DYjlFFbo.js";import{E as k,a as z}from"./CXDY_LVT.js";import{cA as Q,e as j}from"./CmRxzTqw.js";import{a as q,E as B}from"./Bf_xRNbS.js";/* empty css        *//* empty css        */import"./06MVqVCl.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{d as C,c as I}from"./-CaxLuW0.js";import{l as N,b as O,M as d,N as _,a3 as o,a1 as i,u as l,y as M,O as e,a5 as c,_ as S,ao as L,a0 as $}from"./CUZG7cWw.js";import{_ as A}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";import"./_i9izYtZ.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./BOx_5T3X.js";import"./CtvQKSRC.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./tONJIxwY.js";const D={class:"flex flex-col gap-2"},P={class:"flex items-center gap-2"},R={class:"flex items-center cursor-pointer text-[#999999]"},G={class:"flex gap-4 items-center pl-3"},H={class:"flex items-center gap-2"},J={class:"flex items-center cursor-pointer text-[#999999]"},K={class:"flex gap-4 items-center mt-2"},T={class:"flex items-center gap-2 mb-2"},W={class:"flex items-center cursor-pointer text-[#999999]"},X={class:"flex gap-4 items-center pl-3"},Y={class:"flex items-center gap-2 mb-2"},Z={class:"flex items-center cursor-pointer text-[#999999]"},ee={class:"flex gap-4 items-center pl-3"},te={class:"flex items-center gap-2 mb-2"},oe={class:"flex items-center cursor-pointer text-[#999999]"},le={class:"flex gap-4 items-center"},se=N({__name:"mj-options",props:{modelValue:{type:Object,default:{seed:"",iw:1,q:1,s:100,c:0}}},emits:["update:modelValue"],setup(f,{emit:v}){const g=v,x=f,{modelValue:n}=Q(x,g),m=O("1");return(ne,t)=>{const r=F,a=U,p=y,V=k,w=z,h=j,E=B,b=q;return d(),_("div",null,[o(b,{modelValue:l(m),"onUpdate:modelValue":t[6]||(t[6]=s=>M(m)?m.value=s:null),class:"complex_params"},{default:i(()=>[o(E,{title:"高级参数",name:"1"},{title:i(()=>t[7]||(t[7]=[e("div",{class:"flex items-center gap-2"},[e("span",null,"高级参数")],-1)])),default:i(()=>[e("div",D,[e("div",null,[e("div",P,[t[8]||(t[8]=e("span",null,"参考图权重",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"设置生成图片时垫图的权重，值越大越像垫图，取值范围0.5-2， 默认值1"},{reference:i(()=>[e("div",R,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",G,[o(p,{modelValue:l(n).iw,"onUpdate:modelValue":t[0]||(t[0]=s=>l(n).iw=s),step:.1,min:.5,max:2},null,8,["modelValue"]),e("span",null,c(l(n).iw),1)])]),e("div",null,[e("div",H,[t[9]||(t[9]=e("span",null,"图片质量",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"设置图片的质量，越大质量越高，取值范围0.25 - 1，默认值1"},{reference:i(()=>[e("div",J,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",K,[o(w,{modelValue:l(n).q,"onUpdate:modelValue":t[1]||(t[1]=s=>l(n).q=s),placeholder:"请选择图片质量"},{default:i(()=>[(d(!0),_(S,null,L(l(C).mj_quality,(s,u)=>(d(),$(V,{key:u,label:s,value:u},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),e("div",null,[e("div",T,[t[10]||(t[10]=e("span",null,"风格化值",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"设置生成图片时的风格化程度，值越大，风格化的程度越高，取值范围0-1000， 默认值100"},{reference:i(()=>[e("div",W,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",X,[o(p,{modelValue:l(n).s,"onUpdate:modelValue":t[2]||(t[2]=s=>l(n).s=s),step:1,max:1e3,min:0},null,8,["modelValue"]),e("span",null,c(l(n).s),1)])]),e("div",null,[e("div",Y,[t[11]||(t[11]=e("span",null,"混乱值",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"本参数会控制生成4张图的差别， 值越大，生成4张图的区别越大，值越小,生成的4张图越接近，取值范围0-100， 默认值0"},{reference:i(()=>[e("div",Z,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",ee,[o(p,{modelValue:l(n).c,"onUpdate:modelValue":t[3]||(t[3]=s=>l(n).c=s),step:1,max:100,min:0},null,8,["modelValue"]),e("span",null,c(l(n).c),1)])]),e("div",null,[e("div",te,[t[12]||(t[12]=e("span",null,"随机种子",-1)),o(a,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"种子用于指定生成效果，可以用于生成套图，保障生成的一系列图片保持同一种风格"},{reference:i(()=>[e("div",oe,[o(r,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),e("div",le,[o(h,{modelValue:l(n).seed,"onUpdate:modelValue":t[4]||(t[4]=s=>l(n).seed=s),type:"number",min:-1,maxlength:18,onFocus:t[5]||(t[5]=s=>l(I)()),placeholder:"请输入seed种子编号"},null,8,["modelValue"])])])])]),_:1})]),_:1},8,["modelValue"])])}}}),Le=A(se,[["__scopeId","data-v-b052342e"]]);export{Le as default};
