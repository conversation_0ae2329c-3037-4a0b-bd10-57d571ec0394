import{E as A,a as T}from"./DHUC3PVh.js";import{E as k}from"./CiabO6Xq.js";import{E as w}from"./llRQJmEG.js";import{E as C}from"./oVx59syQ.js";import{cL as V}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{u as I}from"./DNOp0HuO.js";import{u as B,I as f}from"./DymDsCmz.js";import{e as L}from"./BhXe-NXN.js";import{l as S,m as z,r as D,ai as N,M as r,N as l,O as a,a3 as c,a1 as v,u as e,_ as $,ao as F,$ as R,a5 as J,a0 as M}from"./CUZG7cWw.js";const O={class:"avatar-select h-full flex flex-col"},P={class:"px-main"},U={class:"mt-[5px]"},j={class:"flex-1 min-h-0"},q={class:"px-main pb-main"},G={key:0,class:"flex flex-wrap mx-[-7px]"},H=["onClick"],K={class:"px-[7px] mb-[14px]"},Q={class:"pic-wrap h-0 pt-[110%] relative"},W={class:"absolute inset-0 bg-[#f4f6ff] rounded-lg"},X={class:"mt-[10px] line-clamp-1 text-center"},ma=S({__name:"avatar",async setup(Y){let n,d;const m=B(),x=t=>{m.addImage(t.cover_url,f.AVATAR,t)},b=z(()=>{var o;const t=(o=m.canvasJson.objects)==null?void 0:o.find(i=>i.customType===f.AVATAR);if(t!=null&&t.data)return t.data}),_=D({keyword:"",currentTab:"2d"}),{data:p}=([n,d]=N(()=>I(()=>V(),{lazy:!0},"$ax15cTFL79")),n=await n,d(),n);return(t,o)=>{const i=T,h=A,y=k,g=w,E=C;return r(),l("div",O,[a("div",P,[a("div",U,[c(h,{modelValue:e(_).currentTab,"onUpdate:modelValue":o[0]||(o[0]=s=>e(_).currentTab=s)},{default:v(()=>[c(i,{label:"2D形象",name:"2d"})]),_:1},8,["modelValue"])])]),a("div",j,[c(E,null,{default:v(()=>[a("div",q,[e(p).length?(r(),l("div",G,[(r(!0),l($,null,F(e(p),s=>{var u;return r(),l("div",{key:s.type,class:"w-[50%]",onClick:Z=>x(s)},[a("div",K,[a("div",{class:R(["border border-solid border-br-light rounded-md p-[10px] cursor-pointer",{"!border-primary":((u=e(b))==null?void 0:u.avatar_id)==s.avatar_id}])},[a("div",null,[a("div",Q,[a("div",W,[c(y,{src:s.cover_url,class:"w-full h-full",fit:"contain",lazy:""},null,8,["src"])])]),a("div",X,J(s.name),1)])],2)])],8,H)}),128))])):(r(),M(g,{key:1,image:e(L),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}});export{ma as _};
