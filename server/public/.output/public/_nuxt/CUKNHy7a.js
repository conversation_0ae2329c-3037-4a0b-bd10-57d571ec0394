import{br as g,bs as y,bt as k,bu as $,I as C,J as w,M as I,N}from"./CmRxzTqw.js";import{l as m,m as S,M as l,N as a,O as f,V as i,u as e,a0 as B,$ as t,a2 as E,Z as r,a5 as p}from"./CUZG7cWw.js";const o={success:"icon-success",warning:"icon-warning",error:"icon-error",info:"icon-info"},d={[o.success]:g,[o.warning]:y,[o.error]:k,[o.info]:$},M=C({title:{type:String,default:""},subTitle:{type:String,default:""},icon:{type:String,values:["success","warning","info","error"],default:"info"}}),h=m({name:"ElResult"}),R=m({...h,props:M,setup(v){const b=v,n=w("result"),c=S(()=>{const s=b.icon,u=s&&o[s]?o[s]:"icon-info",_=d[u]||d["icon-info"];return{class:u,component:_}});return(s,u)=>(l(),a("div",{class:t(e(n).b())},[f("div",{class:t(e(n).e("icon"))},[i(s.$slots,"icon",{},()=>[e(c).component?(l(),B(E(e(c).component),{key:0,class:t(e(c).class)},null,8,["class"])):r("v-if",!0)])],2),s.title||s.$slots.title?(l(),a("div",{key:0,class:t(e(n).e("title"))},[i(s.$slots,"title",{},()=>[f("p",null,p(s.title),1)])],2)):r("v-if",!0),s.subTitle||s.$slots["sub-title"]?(l(),a("div",{key:1,class:t(e(n).e("subtitle"))},[i(s.$slots,"sub-title",{},()=>[f("p",null,p(s.subTitle),1)])],2)):r("v-if",!0),s.$slots.extra?(l(),a("div",{key:2,class:t(e(n).e("extra"))},[i(s.$slots,"extra")],2)):r("v-if",!0)],2))}});var T=I(R,[["__file","result.vue"]]);const P=N(T);export{P as E};
