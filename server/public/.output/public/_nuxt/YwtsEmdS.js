import{I as c,V as p,J as u,M as f,N as m}from"./CmRxzTqw.js";import{l as a,m as v,M as s,N as o,$ as i,u as r,V as y,Z as S,a6 as _}from"./CUZG7cWw.js";const b=c({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:p(String),default:"solid"}}),h=a({name:"ElDivider"}),P=a({...h,props:b,setup(n){const l=n,e=u("divider"),d=v(()=>e.cssVar({"border-style":l.borderStyle}));return(t,z)=>(s(),o("div",{class:i([r(e).b(),r(e).m(t.direction)]),style:_(r(d)),role:"separator"},[t.$slots.default&&t.direction!=="vertical"?(s(),o("div",{key:0,class:i([r(e).e("text"),r(e).is(t.contentPosition)])},[y(t.$slots,"default")],2)):S("v-if",!0)],6))}});var g=f(P,[["__file","divider.vue"]]);const V=m(g);export{V as E};
