import{l as R,e as b}from"./CmRxzTqw.js";import{E as y,a as k}from"./B7GaOiDz.js";import{_ as q}from"./Dhda0m3Y.js";import{P as E}from"./CaNlADry.js";import"./DP2rzg_V.js";/* empty css        */import{a as U}from"./C6_W4ts7.js";import{l as B,j as f,b as C,s as I,M as P,a0 as D,a1 as s,V as F,a3 as t,O as l,u as o}from"./CUZG7cWw.js";const N={class:"w-[420px]"},H=B({__name:"add",emits:["success"],setup(S,{emit:_}){const c=_,v=R(),i=f(),a=C({name:"",avatar:"",image:""}),u=f(),g=I({name:[{required:!0,message:"请输入形象名称"}],avatar:[{required:!0,type:"string",message:"请选择形象头像"}],image:[{required:!0,type:"string",message:"请选择形象封面"}]}),V=async()=>{var e,m;await((e=u.value)==null?void 0:e.validate());const{id:n}=await U(a.value);(m=i.value)==null||m.close(),v.push({path:"/application/digital/edit",query:{id:n}}),c("success")};return(n,e)=>{const m=b,p=y,d=q,x=k,w=E;return P(),D(w,{ref_key:"popRef",ref:i,title:"创建形象",width:"500px",async:"",onConfirm:V},{trigger:s(()=>[F(n.$slots,"default")]),default:s(()=>[t(x,{class:"p-4",ref_key:"formRef",ref:u,model:o(a),"label-width":"100px",rules:o(g)},{default:s(()=>[t(p,{label:"形象名称",prop:"name"},{default:s(()=>[l("div",N,[t(m,{modelValue:o(a).name,"onUpdate:modelValue":e[0]||(e[0]=r=>o(a).name=r),placeholder:"请输入形象名称",clearable:""},null,8,["modelValue"])])]),_:1}),t(p,{label:"形象头像",prop:"avatar"},{default:s(()=>[l("div",null,[l("div",null,[t(d,{modelValue:o(a).avatar,"onUpdate:modelValue":e[1]||(e[1]=r=>o(a).avatar=r)},null,8,["modelValue"])]),e[3]||(e[3]=l("div",{class:"form-tips"},"建议尺寸：50*50px",-1))])]),_:1}),t(p,{label:"形象封面",prop:"image"},{default:s(()=>[l("div",null,[l("div",null,[t(d,{modelValue:o(a).image,"onUpdate:modelValue":e[2]||(e[2]=r=>o(a).image=r)},null,8,["modelValue"])]),e[4]||(e[4]=l("div",{class:"form-tips"},"建议尺寸：280px×187px",-1))])]),_:1})]),_:1},8,["model","rules"])]),_:3},512)}}});export{H as _};
