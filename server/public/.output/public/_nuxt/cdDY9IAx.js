import{E as h}from"./9CYoqqXX.js";import{_ as y}from"./mBG0LxMu.js";import{b as f,d6 as k}from"./CmRxzTqw.js";import{l as N,m as b,M as t,N as o,a3 as c,a1 as m,u as s,a5 as w,Z as r}from"./CUZG7cWw.js";import{_ as I}from"./DlAUqK2U.js";import"./BOx_5T3X.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./Ddo5WWE5.js";import"./CbQsrhNE.js";import"./DecTOTC8.js";const S={key:0,class:"mb-[6px]"},g=["src"],B=["src"],q={key:0,class:"text-sm"},v=N({__name:"menu-item",props:{item:{},path:{},showName:{type:Boolean},isShowIcon:{type:[Number,Boolean]},isActive:{type:Boolean}},setup(u){const l=u,{getImageUrl:a}=f(),i=b(()=>{const e=l.item.link.query;try{const n=JSON.parse(e);return k(n)}catch{return e}});return(e,n)=>{const d=h,_=y;return t(),o("div",null,[c(_,{to:`${e.path}${s(i)?`?${s(i)}`:""}`},{default:m(()=>[c(d,{index:e.path},{title:m(()=>{var p;return[(p=e.item)!=null&&p.showName||e.showName?(t(),o("span",q,w(e.item.name),1)):r("",!0)]}),default:m(()=>[e.isShowIcon?(t(),o("span",S,[e.isActive&&e.item.selected?(t(),o("img",{key:0,class:"menu-item-icon",src:s(a)(e.item.selected)},null,8,g)):e.item.unselected?(t(),o("img",{key:1,class:"menu-item-icon",src:s(a)(e.item.unselected)},null,8,B)):r("",!0)])):r("",!0)]),_:1},8,["index"])]),_:1},8,["to"])])}}}),T=I(v,[["__scopeId","data-v-236bb885"]]);export{T as default};
