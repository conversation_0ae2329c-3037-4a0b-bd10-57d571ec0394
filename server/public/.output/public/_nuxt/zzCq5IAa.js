import{bz as h,bA as n,v as m}from"./CmRxzTqw.js";import{l as _,j as w,b as d,E as g,n as S,k as z,a7 as f,u as c,M as y,N as k,O as u,a8 as x,a6 as b}from"./CUZG7cWw.js";import{u as C}from"./DymDsCmz.js";import{_ as B}from"./DlAUqK2U.js";import"./DoCT-qbH.js";import"./CiYvFM4x.js";import"./DY7CbrCZ.js";import"./Bfmn7p7A.js";const E={class:"canvas-bg"},v="design-canvas",R=_({__name:"canvas-display",setup(T){const a=C(),t=w(null),{width:o}=h(t),s=d(a.defaultSize.height),i=d(!1);n(()=>a.defaultSize,()=>{l()});const l=()=>{o.value>=a.defaultSize.width?s.value=a.defaultSize.height:s.value=o.value*a.defaultSize.height/a.defaultSize.width};return n(o,e=>{l()}),n(s,e=>{if(e){const r=e/a.defaultSize.height;a.setZoom(r)}}),g(async()=>{i.value=!0,await S();try{await a.initCanvas(v,t.value)}finally{i.value=!1}}),z(()=>{var e;(e=a.canvas)==null||e.dispose(),a.$dispose()}),(e,r)=>{const p=m;return f((y(),k("div",{ref_key:"workspaceRef",ref:t,class:"canvas-display overflow-hidden",style:b({height:`${c(s)}px`})},[f(u("div",E,[u("canvas",{id:v})],512),[[x,c(a).canvas]])],4)),[[p,c(i)]])}}}),L=B(R,[["__scopeId","data-v-089e911c"]]);export{L as default};
