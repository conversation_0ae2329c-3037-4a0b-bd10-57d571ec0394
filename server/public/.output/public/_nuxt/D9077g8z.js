import{E as b,a as k,b as w,c as E,d as C}from"./BCtbxh46.js";import{b as M,j as D,d0 as z,by as A,cD as B,dJ as c}from"./CmRxzTqw.js";import H from"./BuqBF2Ub.js";import L from"./BpiVvYM5.js";import{_ as N}from"./KpaauuTh.js";import{_ as V}from"./BC0yJvgx.js";import F from"./DEn9YCt-.js";import{_ as I}from"./CWF3-709.js";import{l as P,m as R,E as T,ap as j,M as m,a0 as p,a1 as r,a6 as q,u as a,a3 as t,V as s,Z as n,aq as J,$ as U}from"./CUZG7cWw.js";import{_ as W}from"./DlAUqK2U.js";import"./DBz5lpK8.js";import"./CbQsrhNE.js";import"./QHNTKww7.js";import"./D3znQkH1.js";import"./eFgaMLiC.js";import"./DluKwKHO.js";/* empty css        */import"./CwwbfV-C.js";import"./DL3I_s72.js";import"./C7tIPmrK.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";/* empty css        *//* empty css        */import"./DAOx25wS.js";import"./DNOp0HuO.js";import"./BjOWC1fj.js";import"./BildjBiE.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";/* empty css        */import"./llRQJmEG.js";import"./DqGsTvs3.js";import"./Mf6yzLCP.js";import"./D0GTlwMd.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";import"./B7GaOiDz.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";/* empty css        */import"./DP2rzg_V.js";import"./Bj_9-7Jh.js";import"./oVx59syQ.js";/* empty css        */import"./DGy9z-W2.js";import"./B_3nBLl5.js";import"./9CYoqqXX.js";import"./BOx_5T3X.js";import"./Ddo5WWE5.js";/* empty css        */import"./l0sNRNKZ.js";import"./cdDY9IAx.js";import"./mBG0LxMu.js";import"./DecTOTC8.js";import"./CXtYBCvR.js";import"./BVTdJXKU.js";import"./DHUC3PVh.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./B4XIt-XN.js";import"./CXDY_LVT.js";import"./DCzKTodP.js";import"./D8e5izeA.js";import"./DSuLZIN6.js";import"./FAfxnQR5.js";/* empty css        *//* empty css        *//* empty css        */import"./DISR6sUa.js";import"./BEuS_AA8.js";import"./DqKCLwOu.js";import"./ezienwJz.js";import"./DnaAw8MZ.js";import"./D8CLlV3f.js";import"./P8Qw-ZvZ.js";import"./D9b7mKi3.js";import"./DSoGMbEr.js";const Z=P({__name:"default",setup(G){const f=M(),h=D(),d=z(),g=A();R(()=>f.isMobile?{"--header-height":"50px","--main-padding":"12px"}:{"--main-padding":"15px"});const{height:l}=B(),u=()=>{g.value=d.isDark,d.setTheme()};return T(()=>{u()}),j(()=>{u()}),(o,K)=>{const y=k,v=w,$=E,S=C,i=b;return m(),p(i,{class:"bg-body h-full layout-default",style:q([{height:`${a(l)=="Infinity"?"100vh":a(l)+"px"}`}])},{default:r(()=>[t(y,{height:"var(--header-height)",style:{padding:"0"}},{default:r(()=>[t(H,null,{default:r(()=>{var e;return[(e=o.$slots)!=null&&e.header?s(o.$slots,"header",{key:0},void 0,!0):n("",!0)]}),_:3})]),_:3}),t(i,{class:"min-h-0"},{default:r(()=>{var e;return[t(v,{width:"auto",class:"!overflow-visible"},{default:r(()=>{var _;return[t(L,null,J({_:2},[(_=o.$slots)!=null&&_.aside?{name:"aside",fn:r(()=>[s(o.$slots,"aside",{},void 0,!0)]),key:"0"}:void 0]),1024)]}),_:3}),t(i,{class:U(["overflow-hidden layout-bg rounded-[12px]",{"":(e=o.$slots)==null?void 0:e.aside,"!rounded-none ":(o._.provides[c]||o.$route).meta.hiddenRounded}])},{default:r(()=>[t($,{class:"scrollbar",style:{padding:"0"}},{default:r(()=>[s(o.$slots,"default",{},void 0,!0)]),_:3}),(o._.provides[c]||o.$route).meta.hiddenFooter?n("",!0):(m(),p(S,{key:0,height:"auto"},{default:r(()=>[t(N)]),_:1}))]),_:3},8,["class"])]}),_:3}),a(h).showLogin?(m(),p(V,{key:0})):n("",!0),t(F),t(I)]),_:3},8,["style"])}}}),Et=W(Z,[["__scopeId","data-v-1a51c74d"]]);export{Et as default};
