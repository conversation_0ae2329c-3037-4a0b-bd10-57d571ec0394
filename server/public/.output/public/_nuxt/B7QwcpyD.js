import{_ as h}from"./eFgaMLiC.js";import{E as x,a as v}from"./9CYoqqXX.js";import{a as E,_ as b,cz as g}from"./CmRxzTqw.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{l as u,m as y,M as s,N as l,a3 as e,a1 as o,_ as L,ao as N,a0 as d,O as i,a5 as $,u as k}from"./CUZG7cWw.js";import{_ as B}from"./DlAUqK2U.js";import{E as P}from"./oVx59syQ.js";/* empty css        */import"./BOx_5T3X.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./Ddo5WWE5.js";const S={class:"tab-list"},w={class:"menu-icon"},C={class:"mt-[10px] text-sm"},I=u({__name:"index",props:{navList:{}},setup(f){const t=E(),m=y(()=>{const a=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.activePath||a});return(a,p)=>{const _=h,c=x,r=v;return s(),l("div",S,[e(r,{"default-active":k(m),router:"",style:{border:"none"}},{default:o(()=>[(s(!0),l(L,null,N(a.navList,n=>(s(),d(c,{key:n.path,index:n.path},{default:o(()=>[i("span",w,[e(_,{size:20,name:n.icon},null,8,["name"])]),i("span",C,$(n.name),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"])])}}}),M=B(I,[["__scopeId","data-v-317921ce"]]),V={class:"bg-white flex flex-col h-full w-[150px] text-tx-primary tab-list"},U=u({__name:"aside",setup(f){const t=[{name:"视频合成",icon:"el-icon-VideoCamera",path:"/digital_human/aside/video_compositing"}];return(m,a)=>{const p=M,_=P,c=g,r=b;return s(),d(r,{name:"default"},{panel:o(()=>[i("div",V,[e(_,null,{default:o(()=>[e(p,{"nav-list":t})]),_:1})])]),default:o(()=>[e(c)]),_:1})}}});export{U as default};
