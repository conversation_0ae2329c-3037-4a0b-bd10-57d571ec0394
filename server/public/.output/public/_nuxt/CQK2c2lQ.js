import{_ as g}from"./eFgaMLiC.js";import{b as h,E as S,a as b}from"./CwgXbNrK.js";import{f as z}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{c as v,u as D}from"./DymDsCmz.js";import{l as k,b as C,M as n,N as p,O as r,a3 as s,a1 as a,_ as y,ao as E,a0 as B,ag as N,a5 as c,u as m}from"./CUZG7cWw.js";import"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./oVx59syQ.js";import"./u6CVc_ZE.js";import"./D6yUe_Nr.js";import"./C3XldtMC.js";import"./DoCT-qbH.js";import"./CiYvFM4x.js";import"./DY7CbrCZ.js";import"./Bfmn7p7A.js";const V={class:"flex items-center justify-center"},j={class:"bg-white min-w-[90px] flex item-center py-[6px] px-[10px] text-tx-primary rounded-md shadow-[0_2px_6px_#ebefff]"},I={class:"flex-1 mr-[4px]"},ee=k({__name:"select-size",setup(M){const l=C(v),e=D(),_=async t=>{var o;if(t.id!==e.defaultSize.resolution){if(!((o=e.getCanvasJson())!=null&&o.objects.length)){e.changeSize(t);return}await z.confirm("是否确认更改画布尺寸？当前画面所有设置将被重置且无法恢复"),e.changeSize(t)}};return(t,o)=>{const d=g,f=S,u=b,x=h;return n(),p("div",V,[o[0]||(o[0]=r("span",{class:"mr-[10px]"},"画布尺寸",-1)),s(x,null,{dropdown:a(()=>[s(u,null,{default:a(()=>[(n(!0),p(y,null,E(m(l),(i,w)=>(n(),B(f,{key:w,onClick:$=>_(i)},{default:a(()=>[N(c(i.label),1)]),_:2},1032,["onClick"]))),128))]),_:1})]),default:a(()=>[r("div",j,[r("span",I,c(m(e).defaultSize.label),1),s(d,{name:"el-icon-ArrowDown"})])]),_:1})])}}});export{ee as default};
