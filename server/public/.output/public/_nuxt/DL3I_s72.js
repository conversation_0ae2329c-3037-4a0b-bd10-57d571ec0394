import{E as j}from"./DluKwKHO.js";import{_ as L}from"./eFgaMLiC.js";import{j as M,b as U,m as H,f as J,d2 as O,E as S}from"./CmRxzTqw.js";import{_ as T}from"./CbQsrhNE.js";import{E as q}from"./C7tIPmrK.js";/* empty css        *//* empty css        *//* empty css        */import{u as F}from"./DAOx25wS.js";import{u as P}from"./DNOp0HuO.js";import{l as Z,b as G,ai as K,M as i,N as _,a3 as r,a1 as c,V as Q,O as e,u as t,a5 as o,ag as u,a0 as N,Z as R,_ as W,ao as X}from"./CUZG7cWw.js";import{_ as Y}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";const ee={class:"user-info"},te={class:"p-[8px]"},se={class:"flex items-center"},oe={class:"flex-1 flex items-center"},ne={class:"ml-[10px]"},ae={class:"text-lg line-clamp-1"},re={class:"bg-[#eff2fe] mt-[20px] py-[20px] px-[5px] rounded-[10px] dark:bg-[#333]"},ce={class:"flex justify-between px-[20px]"},ie={class:"text-xl font-medium text-tx-primary"},le={class:"text-tx-secondary text-sm mt-[10px]"},de={class:"flex items-center"},_e={key:0},pe={key:1},me={class:"flex mt-[20px]"},ue={class:"flex-1 flex flex-col items-center px-[5px]"},fe={class:"text-md text-primary text-center font-bold"},xe={class:"text-xs mt-[5px]"},he={class:"flex-1 flex flex-col items-center px-[5px]"},ve={class:"text-md text-primary text-center font-bold"},ge={class:"py-[20px] flex"},be={class:"flex flex-col items-center w-full"},ke={class:"text-tx-regular"},ye={class:"mt-2"},we={class:"border-t border-solid border-br-light pt-[20px]"},Ie={class:"flex justify-end"},Ee=Z({__name:"user-info",async setup(Ce){let l,f;const s=M(),{copy:B}=F(),x=U(),z=G([{icon:"local-icon-user_works",name:"我的作品",path:"/user/works"},{icon:"local-icon-head_goumai",name:"购买记录",path:"/user/record"},{icon:"local-icon-head_shiyong",name:"余额明细",path:"/user/balance"}]),{data:d}=([l,f]=K(()=>P(()=>H({id:2}),{default(){return[]},transform(p){return JSON.parse(p.data)[1]},lazy:!0},"$EqHsJzg0HM")),l=await l,f(),l),A=async()=>{await J.confirm("确定退出登录吗？"),await O(),s.logout(),window.location.reload()};return(p,n)=>{const D=j,h=L,v=S,m=T,V=S,$=q;return i(),_("div",ee,[r($,{placement:"bottom",trigger:"hover",teleported:!1,"show-arrow":!1,transition:"custom-popover",width:390},{reference:c(()=>[Q(p.$slots,"default",{},void 0,!0)]),default:c(()=>[e("div",te,[e("div",se,[e("div",oe,[r(D,{class:"flex-none",size:50,src:t(s).userInfo.avatar},null,8,["src"]),e("div",ne,[e("div",ae,o(t(s).userInfo.nickname),1),e("div",{class:"text-xs text-tx-secondary mt-1 cursor-pointer flex items-center",onClick:n[0]||(n[0]=a=>t(B)(t(s).userInfo.sn))},[u(" ID："+o(t(s).userInfo.sn)+" ",1),r(h,{class:"ml-1",size:"12",name:"el-icon-CopyDocument"})])])]),e("div",null,[r(m,{to:"/user/center"},{default:c(()=>[r(v,{text:"",bg:"",round:""},{default:c(()=>n[1]||(n[1]=[u("个人中心")])),_:1})]),_:1})])]),e("div",re,[t(x).getIsShowMember?(i(),N(m,{key:0,to:"/user/member"},{default:c(()=>{var a,g,b,k;return[e("div",ce,[e("div",null,[e("div",ie,o(t(s).userInfo.package_name||((g=(a=t(d))==null?void 0:a.content)==null?void 0:g.title)),1),e("div",le,o(t(s).userInfo.package_time?`有效期至：${t(s).userInfo.package_time}`:(k=(b=t(d))==null?void 0:b.content)==null?void 0:k.sub_title),1)]),e("div",de,[r(v,{text:"",bg:"",round:"",class:"dark:!bg-[#1b1c1d] !bg-white"},{default:c(()=>{var y,w,I,E,C;return[(y=t(s).userInfo)!=null&&y.package_is_overdue?(i(),_("span",_e,o(t(s).userInfo.package_name?"立即开通":(I=(w=t(d))==null?void 0:w.content)==null?void 0:I.btn),1)):(i(),_("span",pe,o(t(s).userInfo.package_name?"立即续费":(C=(E=t(d))==null?void 0:E.content)==null?void 0:C.btn),1))]}),_:1})])])]}),_:1})):R("",!0),e("div",me,[e("div",ue,[e("span",fe,o(t(s).userInfo.balance),1),e("span",xe,o(t(x).getTokenUnit)+"数量",1)]),e("div",he,[e("span",ve,o(t(s).userInfo.robot_num),1),n[2]||(n[2]=e("span",{class:"text-xs mt-[5px]"},"智能体",-1))])])]),e("div",ge,[(i(!0),_(W,null,X(t(z),a=>(i(),N(m,{class:"w-[33.3%] flex",key:a.path,to:a.path},{default:c(()=>[e("div",be,[e("div",ke,[r(h,{name:a.icon,size:20},null,8,["name"])]),e("div",ye,o(a.name),1)])]),_:2},1032,["to"]))),128))]),e("div",we,[e("div",Ie,[r(V,{link:"",onClick:A},{default:c(()=>n[3]||(n[3]=[u(" 退出登录 ")])),_:1})])])])]),_:3})])}}}),Te=Y(Ee,[["__scopeId","data-v-17233f26"]]);export{Te as default};
