import{l as O,j as b,r as k,b as J,ai as E,M as i,N as c,a3 as l,a1 as d,O as s,a7 as S,u as n,a0 as C,Z as x,a8 as M,aG as Q,_ as U,ao as Z,$ as K,a5 as p,ag as X,aH as Y}from"./CUZG7cWw.js";import{E as ee}from"./DluKwKHO.js";import{W as te}from"./BZBRZdpQ.js";import{l as oe,j as se,cF as ae,H as ne,E as ie,ct as re}from"./CmRxzTqw.js";import{E as le}from"./CiabO6Xq.js";import{E as ce}from"./C9jirCEY.js";/* empty css        *//* empty css        */import{u as V}from"./DNOp0HuO.js";import{t as de,D as me,E as pe}from"./qRM0tN96.js";import{e as ue}from"./BhXe-NXN.js";import{_ as _e}from"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";const fe={class:"flex-1 min-h-0 mx-[16px] relative"},ge=["onClick"],we={class:"flex-1 min-h-[70vh] overflow-hidden mx-auto"},xe=["onClick"],ye={class:"flex items-center"},he={class:"flex-1 min-w-0 ml-[15px]"},ve={class:"line-clamp-1 text-xl font-medium"},be={class:"line-clamp-1 text-tx-secondary text-xs mt-[5px]"},ke={class:"mt-[13px] text-tx-secondary line-clamp-2 h-[40px] text-sm"},Ee={key:1,class:"flex justify-center items-center mt-[50px]"},Se={class:"flex flex-col justify-center items-center w-full h-[60vh]"},Ce=O({__name:"robot",props:{keyword:{}},async setup(N){let r,m;const P=N,R=oe(),u=se(),z=b(),_=k({keyword:"",cid:0}),I={4e3:{rowPerView:7},2e3:{rowPerView:6},1800:{rowPerView:5},1600:{rowPerView:5},1440:{rowPerView:4},1360:{rowPerView:4},1280:{rowPerView:4},1024:{rowPerView:3}},e=k({pageNo:1,count:0,loading:!0,pageSize:15,lists:[]}),y=J(0),{data:h}=([r,m]=E(()=>V(()=>de(),{default(){return[]},transform(t){return[{id:0,name:"全部"}].concat(t)},lazy:!0},"$LRegJQe7Nd")),r=await r,m(),r);[r,m]=E(()=>V(()=>f(),{lazy:!0},"$elq9G93T6A")),await r,m();const f=async()=>{e.loading=!0;try{const t=await me({..._,page_no:e.pageNo,page_size:e.pageSize});e.pageNo===1&&(e.lists=[]),e.count=t.count,e.lists.push(...t.lists)}finally{setTimeout(()=>e.loading=!1,200)}},B=()=>{u.isLogin&&e.count>=e.pageNo*e.pageSize&&(e.pageNo++,f())},g=()=>{e.pageNo=1,f()},D=b(),L=t=>{D.value=t,console.log(t)},v=t=>{var o;y.value=t,_.cid=(o=h.value[t])==null?void 0:o.id,g()};v(0);const j=async t=>{if(!u.isLogin){u.toggleShowLogin();return}const{id:o}=await pe({id:t.id});R.push({path:"/robot_square/chat",query:{id:o}})};return ae(()=>P.keyword,t=>{_.keyword=t,g()},{debounce:500}),(t,o)=>{const q=Y,A=Q,$=ee,F=te,T=ne,W=le,G=ie,H=ce;return i(),c("div",fe,[l(A,{slidesPerView:"auto",spaceBetween:16,class:"category-lists",onSwiper:L,style:{padding:"10px 0"}},{default:d(()=>[(i(!0),c(U,null,Z(n(h),(a,w)=>(i(),C(q,{key:a.id,style:{width:"auto","margin-right":"12px"}},{default:d(()=>[Object.keys(a).includes("name")?(i(),c("div",{key:0,class:K(["category-item bg-white",{"is-active":n(y)===w}]),onClick:Ve=>v(w)},p(a.name),11,ge)):x("",!0)]),_:2},1024))),128))]),_:1}),s("div",we,[S((i(),c("div",{class:"model-lists mb-[10px] mx-[0px]",ref_key:"robotRef",ref:z,"infinite-scroll-distance":"50"},[n(e).lists.length?(i(),C(F,{key:0,ref:"waterFull",delay:100,list:n(e).lists,width:315,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:I},{item:d(({item:a})=>[s("div",{class:"card-item cursor-pointer bg-white dark:bg-[#1d2025]",onClick:w=>j(a)},[s("div",ye,[l($,{class:"flex-none",src:a.image,size:64},null,8,["src"]),s("div",he,[s("div",ve,p(a.name),1),s("div",be,p(a.author),1)])]),s("div",ke,p(a.intro),1),o[0]||(o[0]=s("div",{class:"mt-[30px] entry-btn"},"开始对话",-1))],8,xe)]),_:1},8,["list"])):x("",!0),n(e).loading?(i(),c("div",Ee,[l(T,{size:"25",class:"is-loading"},{default:d(()=>[l(n(re))]),_:1}),o[1]||(o[1]=s("span",{class:"mt-[4px] ml-[10px] text-[#999999]"},"加载中...",-1))])):x("",!0),S(s("div",Se,[l(W,{class:"w-[200px] h-[200px]",src:n(ue)},null,8,["src"]),o[3]||(o[3]=s("div",{class:"text-tx-regular mb-4"},"暂无机器人",-1)),l(G,{type:"primary",onClick:g},{default:d(()=>o[2]||(o[2]=[X(" 点击刷新")])),_:1})],512),[[M,!n(e).lists.length&&!n(e).loading]])])),[[H,B]])])])}}}),Ge=_e(Ce,[["__scopeId","data-v-a7660ba3"]]);export{Ge as default};
