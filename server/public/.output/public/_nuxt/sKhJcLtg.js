import{_ as m}from"./CbQsrhNE.js";import{a as x,_ as h,cz as f}from"./CmRxzTqw.js";import{l,m as v,M as e,N as c,O as n,_ as y,ao as b,a0 as L,a1 as i,$ as N,u as $,a5 as g,a3 as _}from"./CUZG7cWw.js";import{_ as u}from"./DlAUqK2U.js";const k={class:"flex"},B={class:"flex bg-body p-[8px] rounded-[10px]"},P=l({__name:"index",props:{navList:{}},setup(d){const t=x(),p=v(()=>{const o=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.activePath||o});return(o,r)=>{const s=m;return e(),c("div",k,[n("div",B,[(e(!0),c(y,null,b(o.navList,a=>(e(),L(s,{key:a.path,to:a.path},{default:i(()=>[n("div",{class:N(["text-xl px-[17.5px] py-[5px] rounded-[7px] min-w-[85px] text-center font-bold",{"text-white bg-primary":a.path==$(p)}])},g(a.name),3)]),_:2},1032,["to"]))),128))])])}}}),w=u(P,[["__scopeId","data-v-585af5cb"]]),C={class:"h-full flex flex-col"},z={class:"flex-1 min-h-0"},I=l({__name:"layout",setup(d){const t=[{name:"智能体",path:"/application/layout/robot"},{name:"智能体形象",path:"/application/layout/digital"},{name:"知识库",path:"/application/layout/kb"}];return(p,o)=>{const r=w,s=f,a=h;return e(),c("div",null,[_(a,{name:"default"},{default:i(()=>[n("div",C,[_(r,{class:"px-[20px] pt-[16px]","nav-list":t}),n("div",z,[_(s)])])]),_:1})])}}}),M=u(I,[["__scopeId","data-v-8125b6d3"]]);export{M as default};
