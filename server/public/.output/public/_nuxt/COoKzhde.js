import{_ as D}from"./eFgaMLiC.js";import{E as L}from"./Zz2DnF66.js";import{E as R}from"./D5Svi-lq.js";import{E as H}from"./C7tIPmrK.js";import{E as j}from"./DCzKTodP.js";import{cA as F}from"./CmRxzTqw.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{u as I}from"./P8Qw-ZvZ.js";import{_ as P}from"./DwRn548t.js";import{ModelEnums as v,TypeEnums as f}from"./BLV0QRdm.js";import{l as W,b as E,m as M,c as Y,M as d,N as y,a3 as s,a1 as a,u as o,a0 as q,y as b,a6 as G,O as t,_ as T,$ as k,a5 as h,ao as J,ag as K}from"./CUZG7cWw.js";const Q={class:"flex items-center justify-center"},X={class:"ml-1"},Z={class:"py-[10px]"},ee={key:1,class:"flex items-center"},oe={class:"flex items-center cursor-pointer"},te={class:"flex text-primary"},le={class:"px-[6px]"},se={class:"py-[10px]"},ae=["onMouseover"],ne={class:"py-[6px]"},re={class:"border-l border-solid border-br-light ml-[8px] pl-[8px]"},Me=W({__name:"search-model",props:{type:{},mode:{default:"segmented"},model:{}},emits:["update:model","update:type"],setup(V,{emit:C}){const B=V,N=C,[S,x]=I(),{type:c,model:n}=F(B,N),i=E(!1),m=E(""),p=[{label:"基础",value:v.BASE,icon:"local-icon-search_base"},{label:"增强",value:v.ENHANCE,icon:"local-icon-search_copilot",desc:"检索更多网页，提供更全面个性化答案"},{label:"研究",value:v.STUDY,icon:"local-icon-search_research",desc:"结构更细致，内容更深入。自动总结大纲和图谱，答案更清晰"}],$=[{label:"全网",value:f.ALL},{label:"文档",value:f.DOC},{label:"学术",value:f.SCHOLAR}],w=M(()=>$.find(l=>l.value==c.value)||{}),g=M(()=>p.find(l=>l.value==n.value)||{});return Y(i,()=>{m.value=""}),(u,l)=>{const _=D,A=L,O=R,U=H,z=j;return d(),y(T,null,[s(o(S),null,{default:a(({item:e,select:r})=>[t("div",Q,[t("span",{class:k({"text-primary":!r})},[s(_,{size:"15",name:e.icon},null,8,["name"])],2),t("div",X,h(e.label),1)])]),_:1}),u.mode=="segmented"?(d(),q(O,{key:0,modelValue:o(n),"onUpdate:modelValue":l[0]||(l[0]=e=>b(n)?n.value=e:null),options:p,style:G({width:`${p.length*90}px`,"--el-border-radius-base":"10px","--el-segmented-color":"var(--el-text-color-primary)"})},{default:a(({item:e})=>[s(A,{effect:"dark",content:e.desc,disabled:!e.desc,placement:"top"},{default:a(()=>[t("div",Z,[s(o(x),{item:e,select:e.value==o(n)},null,8,["item","select"])])]),_:2},1032,["content","disabled"])]),_:1},8,["modelValue","style"])):(d(),y("div",ee,[s(U,{placement:"bottom",trigger:"click",width:120,"popper-style":{minWidth:"120px",padding:0},visible:o(i),"onUpdate:visible":l[2]||(l[2]=e=>b(i)?i.value=e:null)},{reference:a(()=>[t("div",oe,[t("span",te,[s(_,{name:o(g).icon},null,8,["name"])]),t("span",le,h(o(g).label),1),s(_,{name:"el-icon-CaretBottom"})])]),default:a(()=>[t("div",se,[(d(),y(T,null,J(p,e=>t("div",{key:e.value,class:k({"bg-primary-light-9":o(m)==e.value}),onMouseover:r=>m.value=e.value},[s(P,{model:e.value,type:o(c),"onUpdate:type":[l[1]||(l[1]=r=>b(c)?c.value=r:null),r=>n.value=e.value],trigger:"hover",placement:"right"},{item:a(({icon:r,label:ce})=>[t("div",ne,[s(o(x),{class:"px-[10px]",item:e,select:!1},null,8,["item"])])]),_:2},1032,["model","type","onUpdate:type"])],42,ae)),64))])]),_:1},8,["visible"]),t("div",re,[s(z,{type:"primary",size:"small"},{default:a(()=>[K(h(o(w).label),1)]),_:1})])]))],64)}}});export{Me as _};
