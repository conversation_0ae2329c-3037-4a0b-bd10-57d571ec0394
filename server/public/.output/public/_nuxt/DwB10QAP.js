import{E as g}from"./CiabO6Xq.js";import{_ as w}from"./eFgaMLiC.js";import{E as C}from"./CmRxzTqw.js";/* empty css        */import{l as E,b as B,M as a,N as n,O as l,a3 as r,u as s,$ as c,a4 as u,Z as V,a5 as N,a1 as $,a6 as z,ag as M}from"./CUZG7cWw.js";import{u as P}from"./DoCT-qbH.js";import{_ as h}from"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";const D={class:"relative flex-none flex"},S={key:0,class:"absolute inset-0 flex items-center justify-center bg-[rgba(0,0,0,0.5)] rounded-full"},j={class:"flex-1 line-clamp-2 min-w-0 ml-[10px]"},A=E({__name:"dub-item",props:{activeId:{},itemId:{},name:{},pic:{},url:{},disabled:{type:Boolean,default:!1}},emits:["click"],setup(f,{emit:v}){const b=f,y=v,i=B(!1),{play:k,audioPlaying:d,pause:m}=P(),x=()=>{b.disabled||y("click")};return(t,e)=>{const I=g,p=w,_=C;return a(),n("div",{class:c(["dub-item flex items-center p-main cursor-pointer",{"is-hover":s(i),"is-active":t.activeId==t.itemId,"is-disable":t.disabled}]),onMouseenter:e[2]||(e[2]=o=>i.value=!0),onMouseleave:e[3]||(e[3]=o=>i.value=!1),onClick:x},[l("div",D,[r(I,{src:t.pic,class:"w-[40px] h-[40px] rounded-full overflow-hidden"},null,8,["src"]),s(i)||s(d)?(a(),n("div",S,[l("div",{class:c(["audio-btn cursor-pointer",{"audio-btn--animation":s(d)}])},[s(d)?(a(),n("span",{key:0,class:"text-white flex",onClick:e[0]||(e[0]=u((...o)=>s(m)&&s(m)(...o),["stop"]))},[r(p,{size:24,name:"el-icon-VideoPause"})])):(a(),n("span",{key:1,class:"text-white flex",onClick:e[1]||(e[1]=u(o=>s(k)(t.url),["stop"]))},[r(p,{size:24,name:"el-icon-VideoPlay"})]))],2)])):V("",!0)]),l("div",j,N(t.name),1),l("div",{class:"flex-none",style:z({visibility:s(i)?"visible":"hidden"})},[r(_,{type:"primary",disabled:t.disabled},{default:$(()=>e[4]||(e[4]=[M("使用")])),_:1},8,["disabled"])],4)],34)}}}),Q=h(A,[["__scopeId","data-v-56d9fccd"]]);export{Q as default};
