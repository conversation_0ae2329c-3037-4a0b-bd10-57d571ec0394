const z="-----CUSTOM_SPLIT_SIGN-----",B=o=>o.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),G=o=>{if(!o.includes("|"))return!1;const c=o.split(`
`);if(c.length<2)return!1;const n=c[0].trim();if(!n.startsWith("|")||!n.endsWith("|"))return!1;const p=c[1].trim();if(!/^(\|[\s:]*-+[\s:]*)+\|$/.test(p))return!1;for(let h=2;h<c.length;h++){const f=c[h].trim();if(f&&(!f.startsWith("|")||!f.endsWith("|")))return!1}return!0},H=o=>{const{text:c="",chunkLen:n}=o,p=c.split(`
`),g=p[0],h=g.split("|").length-2,f=`| ${new Array(h>0?h:1).fill(0).map(()=>"---").join(" | ")} |`,L=[];let l=`${g}
${f}
`;for(let i=2;i<p.length;i++)l.length+p[i].length>n*1.2&&(L.push(l),l=`${g}
${f}
`),l+=`${p[i]}
`;return l&&L.push(l),{chunks:L,chars:L.reduce((i,R)=>i+R.length,0)}},K=o=>{let{text:c="",chunkLen:n,overlapRatio:p=.2,customReg:g=[]}=o;const h="SPLIT_HERE_SPLIT_HERE",f="CODE_BLOCK_LINE_MARKER",L=Math.round(n*p);c=c.replace(/(```[\s\S]*?```|~~~[\s\S]*?~~~)/g,function(e){return e.replace(/\n/g,f)}),c=c.replace(/(\r?\n|\r){3,}/g,`


`);const l=[...g.map(e=>({reg:new RegExp(`(${B(e)})`,"g"),maxLen:n*1.4})),{reg:/^(#\s[^\n]+)\n/gm,maxLen:n*1.2},{reg:/^(##\s[^\n]+)\n/gm,maxLen:n*1.2},{reg:/^(###\s[^\n]+)\n/gm,maxLen:n*1.2},{reg:/^(####\s[^\n]+)\n/gm,maxLen:n*1.2},{reg:/([\n]([`~]))/g,maxLen:n*4},{reg:/([\n](?!\s*[\*\-|>0-9]))/g,maxLen:n*2},{reg:/([\n])/g,maxLen:n*1.2},{reg:/([。]|([a-zA-Z])\.\s)/g,maxLen:n*1.2},{reg:/([！]|!\s)/g,maxLen:n*1.2},{reg:/([？]|\?\s)/g,maxLen:n*1.4},{reg:/([；]|;\s)/g,maxLen:n*1.6},{reg:/([，]|,\s)/g,maxLen:n*2}],i=g.length,R=e=>e<i,b=e=>e>=i&&e<=3+i,M=e=>e>=i&&e<=4+i,P=e=>e<=6+i,O=({text:e,step:r})=>{if(r>=l.length)return[{text:e,title:""}];const t=R(r),u=b(r),$=M(r),{reg:x}=l[r];return e.replace(x,t?h:$?`${h}$1`:`$1${h}`).split(`${h}`).filter(a=>a.trim()).map(a=>{var S;const m=u&&((S=a.match(x))==null?void 0:S[0])||"";return{text:u?a.replace(m,""):a,title:m}}).filter(a=>a.text.trim())},T=({text:e,step:r})=>{const t=P(r),u=n*.4;if(t||L===0||r>=l.length)return"";const $=O({text:e,step:r});let x="";for(let d=$.length-1;d>=0;d--){const m=$[d].text+x,S=m.length;if(S>L)return S>u?T({text:m,step:r+1})||x:m;x=m}return x},v=({text:e="",step:r,lastText:t,mdTitle:u=""})=>{const $=M(r),x=R(r);if(r>=l.length){if(e.length<n*3)return[e];const k=[];for(let C=0;C<e.length;C+=n-L)k.push(`${u}${e.slice(C,C+n)}`);return k}const d=O({text:e,step:r}),a=d.length>1?l[r].maxLen:n,m=n*.7,S=30,s=[];for(let k=0;k<d.length;k++){const C=d[k],I=`${u}${C.title}`,W=C.text,N=W.length,y=t.length,A=t+W,_=y+N;if(_>a){if(y>m){s.push(`${I}${t}`),t=T({text:t,step:r}),k--;continue}const w=v({text:A,step:r+1,lastText:"",mdTitle:I}),E=w[w.length-1];!$&&E.length<m?(s.push(...w.slice(0,-1)),t=E):(s.push(...w),t=T({text:E,step:r}));continue}t=A,(x||$&&_>S||_>=n)&&(s.push(`${I}${t}`),t=T({text:t,step:r}))}return t&&s[s.length-1]&&!s[s.length-1].endsWith(t)?t.length<n*.4?s[s.length-1]=s[s.length-1]+t:s.push(`${u}${t}`):t&&s.length===0&&s.push(t),s};try{const e=v({text:c,step:0,lastText:"",mdTitle:""}).map(t=>(t==null?void 0:t.replaceAll(f,`
`))||""),r=e.reduce((t,u)=>t+u.length,0);return{chunks:e,chars:r}}catch(e){throw new Error(e)}},U=o=>{const{text:c=""}=o;return c.split(z).map(g=>G(g)?H(o):K(o)).map(g=>g.chunks).flat()};export{U as s};
