import{E as f,a as h}from"./sfCUuwOk.js";import{b as x,v as g}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{b}from"./DXdf2lbU.js";import{l as v,b as m,E as y,M as i,N as o,O as t,a7 as k,u as d,a0 as E,a1 as s,a3 as l,a5 as n,Z as N,ag as B}from"./CUZG7cWw.js";const C={class:"mt-4 flex-1 flex flex-col min-h-0"},V={class:"flex-1 min-h-0"},w={class:"flex items-center"},L={class:"flex items-center"},T={class:"flex items-center"},D={class:"flex items-center"},M={class:"flex items-center"},S={class:"flex items-center"},z={key:0},A={key:1},O={class:"flex flex-col"},P={key:0,class:"text-warning"},X=v({__name:"member",setup(R){x();const c=m([]),_=m(!1),p=async()=>{_.value=!0;try{c.value=await b()}finally{_.value=!1}};return y(()=>{p()}),(Z,j)=>{const a=h,r=f,u=g;return i(),o("div",C,[t("div",V,[k((i(),E(r,{height:"100%",size:"large",data:d(c)},{default:s(()=>[l(a,{label:"订单编号",prop:"order_sn",width:"170"},{default:s(({row:e})=>[t("div",w,[t("span",null,n(e.order_sn||"-"),1)])]),_:1}),l(a,{label:"会员等级",prop:"name","min-width":"130"},{default:s(({row:e})=>[t("div",L,[t("span",null,n(e.package_name||e.name||"-"),1)])]),_:1}),l(a,{label:"会员时长",prop:"original_package_long_time","min-width":"100"},{default:s(({row:e})=>[t("div",T,[t("span",null,n(e.original_package_long_time||"-"),1)])]),_:1}),l(a,{label:"有效期至",prop:"package_long_time","min-width":"160"},{default:s(({row:e})=>[t("div",D,[t("span",null,n(e.package_long_time||"-"),1)])]),_:1}),l(a,{label:"购买方式",prop:"channel_text","min-width":"100"}),l(a,{label:"支付方式",prop:"pay_way_text","min-width":"100"},{default:s(({row:e})=>[t("div",M,[t("span",null,n(e.pay_way_text||"-"),1)])]),_:1}),l(a,{label:"实付金额","min-width":"100"},{default:s(({row:e})=>[t("div",S,[e.order_amount?(i(),o("span",z,"¥"+n(e.order_amount),1)):(i(),o("span",A,"-"))])]),_:1}),l(a,{label:"支付状态",prop:"pay_status_text","min-width":"100"},{default:s(({row:e})=>[t("div",O,[t("span",null,n(e.pay_status_text||"-"),1),e.refund_status==1?(i(),o("span",P,"已退款")):N("",!0)])]),_:1}),l(a,{label:"支付/操作时间",prop:"pay_time_text","min-width":"160"},{default:s(({row:e})=>[B(n((e==null?void 0:e.pay_time_text)||(e==null?void 0:e.create_time)),1)]),_:1})]),_:1},8,["data"])),[[u,d(_)]])])])}}});export{X as _};
