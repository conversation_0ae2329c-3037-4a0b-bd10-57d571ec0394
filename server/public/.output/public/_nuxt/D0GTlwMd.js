import{l as D,b as I,j as z,bC as L,d8 as T,d9 as $,f as A,E as w,e as M}from"./CmRxzTqw.js";import{E as O}from"./ArzC3z2d.js";import{E as P}from"./B7GaOiDz.js";/* empty css        */import"./DP2rzg_V.js";import{u as E}from"./Bj_9-7Jh.js";import{l as Q,b as _,M as p,N as y,u as t,a3 as s,a1 as o,y as g,Z as v,_ as Z,O as l,a5 as c,ag as i,a0 as G}from"./CUZG7cWw.js";import{_ as H}from"./DlAUqK2U.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";const J={key:0,class:"redeem-code-query-pop"},K={key:0,class:"flex justify-between pt-4 text-base"},W={class:"flex pt-[40px]"},X={class:"flex-1"},Y={class:"flex justify-end w-[110px]"},ee={key:1,class:"redeem-code-check-pop"},te={class:"h-full"},oe={class:"flex-1 flex justify-center items-center bg-white pt-[20px]"},se=Q({__name:"redeem-code-pop",emits:["close"],setup(le,{emit:R}){const x=R,U=D(),k=I(),B=z(),a=_(""),m=_(!0),r=_(!1),d=_({content:"",failure_time:"",id:"",sn:"",type:"",type_desc:"",valid_time:""}),{isLock:S,lockFn:h}=E(async()=>{try{const u=await T({sn:a.value});r.value=!0,d.value=u}catch(u){a.value="",console.log("查询卡密失败=>",u)}}),{isLock:F,lockFn:N}=E(async()=>{try{await $({sn:a.value}),A.msgSuccess("兑换成功"),r.value=!1,m.value=!1,a.value="",await B.getUser(),await U.push({path:"/user/record",query:{time:new Date().getTime()}}),x("close")}catch(u){console.log("兑换卡密失败=>",u)}});return(u,e)=>{const j=w,q=M,C=w,b=O,f=P;return p(),y(Z,null,[t(m)?(p(),y("div",J,[s(b,{modelValue:t(m),"onUpdate:modelValue":e[2]||(e[2]=n=>g(m)?m.value=n:null),title:"卡密兑换",width:"600",onClose:e[3]||(e[3]=n=>x("close"))},{default:o(()=>{var n;return[(n=t(k).getRedeemCode)!=null&&n.is_show?(p(),y("div",K,[l("div",null,[e[5]||(e[5]=l("span",{class:"mr-2"},"购买链接:",-1)),l("span",null,c(t(k).getRedeemCode.buy_site),1)]),s(j,{type:"primary",link:!0,onClick:e[0]||(e[0]=V=>t(L)(t(k).getRedeemCode.buy_site))},{default:o(()=>e[6]||(e[6]=[i("复制")])),_:1})])):v("",!0),l("div",W,[l("div",X,[s(q,{modelValue:t(a),"onUpdate:modelValue":e[1]||(e[1]=V=>g(a)?a.value=V:null),placeholder:"请输入卡密编号",size:"large"},null,8,["modelValue"])]),l("div",Y,[s(C,{type:"primary",size:"large",loading:t(S),onClick:t(h)},{default:o(()=>e[7]||(e[7]=[i(" 查询 ")])),_:1},8,["loading","onClick"])])])]}),_:1},8,["modelValue"])])):v("",!0),t(r)?(p(),y("div",ee,[s(b,{modelValue:t(r),"onUpdate:modelValue":e[4]||(e[4]=n=>g(r)?r.value=n:null),width:"400"},{header:o(()=>e[8]||(e[8]=[l("div",{class:"text-lg text-center font-medium"},"查询结果",-1)])),default:o(()=>[l("div",te,[s(f,{label:"卡密类型："},{default:o(()=>[i(c(t(d).type_desc),1)]),_:1}),s(f,{label:"卡密面额："},{default:o(()=>[i(c(t(d).content),1)]),_:1}),s(f,{label:"兑换时间："},{default:o(()=>[i(c(t(d).failure_time),1)]),_:1}),t(d).valid_time?(p(),G(f,{key:0,label:"有效期至："},{default:o(()=>[i(c(t(d).valid_time),1)]),_:1})):v("",!0)]),l("div",oe,[s(C,{class:"w-full",type:"primary",size:"large",loading:t(F),onClick:t(N)},{default:o(()=>e[9]||(e[9]=[i(" 立即兑换 ")])),_:1},8,["loading","onClick"])])]),_:1},8,["modelValue"])])):v("",!0)],64)}}}),ge=H(se,[["__scopeId","data-v-c4e52e63"]]);export{ge as default};
