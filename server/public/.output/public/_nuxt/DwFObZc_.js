function t(e){return $request.post({url:"/KnowDialogue/shareAuth",params:e},{withToken:!1})}function r(e){return $request.get({url:"/kb.know/teamUsers",params:e})}function n(e){return $request.get({url:"/kb.know/teamLists",params:e})}function s(e){return $request.post({url:"/kb.know/teamAdd",params:e})}function u(e){return $request.post({url:"/kb.know/teamEdit",params:e})}function a(e){return $request.post({url:"/kb.know/teamDel",params:e})}function o(e){return $request.post({url:"/kb.know/transfer",params:e})}export{n as a,u as b,t as c,a as d,r as g,s as p,o as t};
