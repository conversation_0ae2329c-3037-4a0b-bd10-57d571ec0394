import{a as j,E as k}from"./CXDY_LVT.js";import{cA as w}from"./CmRxzTqw.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{d as t,f as n}from"./-CaxLuW0.js";import{_ as x}from"./CXAJ--Vj.js";import{l as y,c as B,u as l,M as p,N as c,a3 as d,O as E,a1 as N,y as b,Z as g,_ as h,ao as C,a0 as M}from"./CUZG7cWw.js";import{_ as O}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./7tQUKVT9.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./tONJIxwY.js";import"./eFgaMLiC.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";/* empty css        */const z={key:0,class:"mj-version"},A=y({__name:"mj-version",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(_,{emit:f}){const v=f,V=_,{modelValue:e}=w(V,v);return B(()=>{var o;return[n.value.draw_model,(o=t.value)==null?void 0:o.mj_version]},([o,s])=>{var r,a;(r=t.value)!=null&&r.mj_version&&e.value!==o&&(e.value=(a=t.value)==null?void 0:a.mj_version[n.value.draw_model][0])}),(o,s)=>{var u;const r=k,a=j;return(u=l(t))!=null&&u.mj_version?(p(),c("div",z,[d(x,{title:"版本选择",tips:"指定midjourney的渲染版本"}),E("div",null,[d(a,{modelValue:l(e),"onUpdate:modelValue":s[0]||(s[0]=m=>b(e)?e.value=m:null),placeholder:"请选择版本",class:"w-full mt-[8px]",size:"large"},{default:N(()=>{var m;return[(p(!0),c(h,null,C((m=l(t))==null?void 0:m.mj_version[l(n).draw_model],(i,D)=>(p(),M(r,{key:i,label:i,value:i},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])])])):g("",!0)}}}),ne=O(A,[["__scopeId","data-v-4b0030cc"]]);export{ne as default};
