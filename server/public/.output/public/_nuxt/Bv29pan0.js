import{_ as H}from"./eFgaMLiC.js";import{l as b,b as i,j as v,m as z,c as $,n as E,w as I,M as h,N as w,O as u,V as R,u as a,a6 as d,a3 as B,Z as M,a4 as N}from"./CUZG7cWw.js";import{bz as S,cO as V,T as O}from"./CmRxzTqw.js";import{_ as T}from"./DlAUqK2U.js";const X=b({__name:"index",props:{defaultHeight:{default:32},bg:{default:"white"},iconColor:{default:"inherit"},zIndex:{default:999}},setup(g,{expose:_}){const n=g,o=i(!1),s=i(!1),c=v(),l=i("auto"),f=v(),y=z(()=>{const e=o.value?l.value:n.defaultHeight;return{height:typeof e=="number"?`${e}px`:e,backgroundColor:n.bg}});$(o,async e=>{e?(await E(),l.value=r.value?r.value:"auto"):l.value=n.defaultHeight});const{height:r}=S(c),{x:p,y:m,height:k,width:x}=V(f);return I(()=>{r.value>n.defaultHeight?s.value=!0:s.value=!1}),O(window,"click",e=>{e.clientX>p.value&&e.clientX<p.value+x.value&&e.clientY>m.value&&e.clientY<m.value+k.value||(o.value=!1)},{capture:!0}),_({hidden(){o.value=!1},show(){o.value=!0}}),(e,t)=>{const C=H;return h(),w("div",{class:"dropdown-more",style:d({"--dropdown-more-default-height":`${e.defaultHeight}px`,"--dropdown-more-z-index":e.zIndex}),onClick:t[1]||(t[1]=N(()=>{},["stop"]))},[t[2]||(t[2]=u("div",{class:"dropdown-placeholder"},null,-1)),u("div",{ref_key:"contentRef",ref:f,class:"dropdown-content",style:d(a(y))},[u("div",{ref_key:"slotRef",ref:c,class:"dropdown-slot"},[R(e.$slots,"default",{},void 0,!0)],512),a(s)?(h(),w("div",{key:0,style:d({transform:`rotateZ(${a(o)?"180deg":"0"})`}),class:"dropdown-icon cursor-pointer",onClick:t[0]||(t[0]=Y=>o.value=!a(o))},[B(C,{color:e.iconColor,name:"el-icon-ArrowDown"},null,8,["color"])],4)):M("",!0)],4)],4)}}}),L=T(X,[["__scopeId","data-v-a93115e1"]]);export{L as _};
