import{_ as k}from"./Dhx2k-ev.js";import{_ as w}from"./D3znQkH1.js";import{a as D,l as b}from"./CmRxzTqw.js";import{_ as h}from"./03QmEwbQ.js";import g from"./C7PKBChU.js";import q from"./CjwWukSr.js";import S from"./Ct4jzbJa.js";import{b as U}from"./Dl64kDm5.js";import{l as V,b as c,r as B,c as C,E as M,q as N,M as u,N as I,a3 as d,u as r,y as O,O as n,a1 as R,a0 as $,a2 as E}from"./CUZG7cWw.js";import{_ as T}from"./DlAUqK2U.js";import"./eFgaMLiC.js";import"./BvSuqySp.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./9CYoqqXX.js";import"./BOx_5T3X.js";import"./Ddo5WWE5.js";import"./oVx59syQ.js";/* empty css        *//* empty css        */import"./Cc32Zcz_.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./DCzKTodP.js";import"./sfCUuwOk.js";import"./DSuLZIN6.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./HA5sEeDs.js";import"./DJi8L2lq.js";import"./C-n0m2hZ.js";import"./CXDY_LVT.js";import"./7tQUKVT9.js";import"./D6yUe_Nr.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DRe575WM.js";import"./DEOaFiHq.js";import"./B7GaOiDz.js";import"./Bh-PoUNP.js";import"./CaNlADry.js";import"./ArzC3z2d.js";import"./CDwN27aR.js";import"./C3XldtMC.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./CcXEqQU4.js";import"./Bj_9-7Jh.js";import"./BiHhwkbt.js";import"./Dp4_NTQz.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./CyoNPmdv.js";import"./DY7CbrCZ.js";import"./Cpg3PDWZ.js";import"./BVy5bzwO.js";import"./ntKK5cQU.js";import"./C4qLKnCc.js";import"./CoWsWLh1.js";import"./C4lJb2GF.js";import"./Dtu61t44.js";import"./THcMaKcC.js";import"./C9f7n97H.js";/* empty css        */import"./D_o6zaHw.js";import"./D-n7HwjM.js";import"./DUp2AN3X.js";import"./Cs0_Uid5.js";import"./DqGsTvs3.js";import"./DHUC3PVh.js";import"./Dbi96Hzd.js";import"./JP19D1Mj.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";import"./DB7Ysqj9.js";import"./Bf_xRNbS.js";import"./DdtGP7XX.js";import"./Dhda0m3Y.js";/* empty css        */import"./DluKwKHO.js";/* empty css        */import"./DwFObZc_.js";import"./CIM5GUyf.js";import"./YwtsEmdS.js";import"./DgYzYYsq.js";const j={class:"h-full flex"},K={class:"flex-1 min-w-0 overflow-auto pr-[16px] py-[16px]"},L={class:"bg-body h-full rounded-2xl"},z={class:"import-data h-full"},A=V({__name:"index",setup(F){const p=D(),_=b(),s=c(1),e=p.query.id,f={dataStudy:h,testData:g,teamData:S,setUp:q},i=B({type:"",name:"",image:"",intro:"",owned:1,power:1,qa_length:""}),o=c("dataStudy"),y=[{name:"数据学习",icon:"el-icon-Document",key:"dataStudy"},{name:"搜索测试",key:"testData",icon:"el-icon-Search"},{name:"团队成员",key:"teamData",icon:"el-icon-User"},{name:"知识库设置",key:"setUp",icon:"el-icon-Setting"}],l=async()=>{const m=await U({id:e});Object.keys(i).map(t=>{i[t]=m[t]}),p.query.type&&(o.value=p.query.type)};return C(()=>o.value,m=>{_.replace({path:"",query:{id:e,type:m}})}),M(()=>{l()}),N("knowDetail",i),(m,t)=>{const v=k,x=w;return u(),I("div",j,[d(v,{modelValue:r(o),"onUpdate:modelValue":t[0]||(t[0]=a=>O(o)?o.value=a:null),"menu-list":y,title:r(i).name,"back-path":"/application/layout/kb"},null,8,["modelValue","title"]),n("div",K,[d(x,null,{default:R(()=>[n("div",L,[n("div",z,[(u(),$(E(f[r(o)]),{id:r(e),type:r(i).type,onToImport:t[1]||(t[1]=a=>s.value=2),onSuccess:t[2]||(t[2]=a=>s.value=1),onUpdate:l},null,40,["id","type"]))])])]),_:1})])])}}}),No=T(A,[["__scopeId","data-v-875d1dc1"]]);export{No as default};
