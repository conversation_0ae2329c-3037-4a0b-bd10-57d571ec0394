import{_ as x}from"./eFgaMLiC.js";import{E as h,a as y}from"./DHUC3PVh.js";import"./CmRxzTqw.js";import{u as $,a as w}from"./DymDsCmz.js";import{l as T,m as k,b as B,c as D,M as s,N as p,O as m,a3 as l,a1 as c,u as e,Z as M,_ as S,ao as E,a0 as f,a5 as N,a7 as z,a8 as L,a2 as V}from"./CUZG7cWw.js";import{_ as j}from"./Btsr68UI.js";import{_ as A}from"./BLeEUk17.js";import{_ as I}from"./_IsPce8C.js";import O from"./Bxy8Ff7l.js";import P from"./B2IX6jAp.js";import{_ as F}from"./CpBm7YaS.js";import R from"./DqxK3r1f.js";import{_ as Z}from"./Dn5Z8lNl.js";import{_ as q}from"./DlAUqK2U.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./DoCT-qbH.js";import"./CiYvFM4x.js";import"./DY7CbrCZ.js";import"./Bfmn7p7A.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./llRQJmEG.js";import"./oVx59syQ.js";/* empty css        *//* empty css        */import"./DNOp0HuO.js";import"./BhXe-NXN.js";import"./C9f7n97H.js";import"./Bv29pan0.js";/* empty css        */import"./DwB10QAP.js";import"./B7GaOiDz.js";import"./D6yUe_Nr.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";/* empty css        */import"./Cw-OnHz-.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./DCzKTodP.js";import"./D8e5izeA.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./06MVqVCl.js";import"./CtvQKSRC.js";import"./CXsrG8JM.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./7h9ARPvN.js";import"./DdtGP7XX.js";import"./Bs9Zhtqd.js";import"./ttFW0yUc.js";const G={class:"h-full relative z-20 bg-white"},H={class:"flex flex-col items-center justify-center"},J={class:"text-lg mt-[6px]"},K={class:"w-[360px] h-full"},Q=T({__name:"index",setup(U){const u={Avatar:j,Dub:A,Music:I,Background:O,Text:P,Captions:F,Maps:R,Prospect:Z},d=$(),{tabsState:a,initTabs:v,changeTabs:b}=w(),r=k({get(){return a.value.isCollapsed},set(i){a.value.isCollapsed=i}}),n=B(!1);return v(),D(()=>a.value.current,async i=>{d.setActiveObjectByType(i)}),(i,o)=>{const _=x,C=y,g=h;return s(),p("div",{class:"control-panel h-full",onMouseenter:o[2]||(o[2]=t=>n.value=!0),onMouseleave:o[3]||(o[3]=t=>n.value=!1)},[m("div",G,[l(g,{"tab-position":"left",class:"h-full",type:"card","model-value":e(a).current,onTabChange:o[0]||(o[0]=t=>e(b)(t))},{default:c(()=>[(s(!0),p(S,null,E(e(a).tabs,t=>(s(),f(C,{key:t.id,name:t.id,lazy:""},{label:c(()=>[m("div",H,[l(_,{name:t.icon,size:22},null,8,["name"]),m("span",J,N(t.label),1)])]),default:c(()=>[z(m("div",K,[(s(),f(V(u[t.component])))],512),[[L,!e(r)]])]),_:2},1032,["name"]))),128))]),_:1},8,["model-value"])]),e(r)||e(n)?(s(),p("div",{key:0,class:"panel-left-arrow",onClick:o[1]||(o[1]=t=>r.value=!e(r))},[l(_,{class:"mr-1",name:`el-icon-${e(r)?"CaretRight":"CaretLeft"}`},null,8,["name"])])):M("",!0)],32)}}}),oo=q(Q,[["__scopeId","data-v-8d50fa1f"]]);export{oo as default};
