import{_ as g}from"./eFgaMLiC.js";import{E as V}from"./C7tIPmrK.js";import{E}from"./DYjlFFbo.js";import{cA as w,e as b}from"./CmRxzTqw.js";import{a as h,E as y}from"./Bf_xRNbS.js";/* empty css        *//* empty css        */import"./06MVqVCl.js";import"./l0sNRNKZ.js";import{c as C}from"./-CaxLuW0.js";import{l as I,b as N,M as U,N as k,a3 as t,a1 as i,u as s,y as B,O as o,a5 as F}from"./CUZG7cWw.js";import{_ as O}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";import"./_i9izYtZ.js";import"./D6yUe_Nr.js";import"./BOx_5T3X.js";import"./CtvQKSRC.js";import"./DRe575WM.js";import"./DqGsTvs3.js";import"./tONJIxwY.js";const z={class:"flex flex-col gap-2"},D={class:"flex items-center gap-2"},M={class:"flex items-center cursor-pointer text-[#999999]"},Q={class:"flex gap-4 items-center pl-3"},S={class:"flex items-center gap-2 mb-2"},$={class:"flex items-center cursor-pointer text-[#999999]"},j={class:"flex gap-4 items-center"},A=I({__name:"doubao-options",props:{modelValue:{type:Object,default:{seed:"",ddim_steps:20}}},emits:["update:modelValue"],setup(p,{emit:d}){const c=d,u=p,{modelValue:n}=w(u,c),a=N("1");return(L,e)=>{const m=g,r=V,_=E,f=b,v=y,x=h;return U(),k("div",null,[t(x,{modelValue:s(a),"onUpdate:modelValue":e[3]||(e[3]=l=>B(a)?a.value=l:null),class:"complex_params"},{default:i(()=>[t(v,{title:"高级参数",name:"1"},{title:i(()=>e[4]||(e[4]=[o("div",{class:"flex items-center gap-2"},[o("span",null,"高级参数")],-1)])),default:i(()=>[o("div",z,[o("div",null,[o("div",D,[e[5]||(e[5]=o("span",null,"绘画质量",-1)),t(r,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"越低：细节简练；越高：细节丰富, 默认值20"},{reference:i(()=>[o("div",M,[t(m,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),o("div",Q,[t(_,{modelValue:s(n).ddim_steps,"onUpdate:modelValue":e[0]||(e[0]=l=>s(n).ddim_steps=l),step:1,max:50,min:1},null,8,["modelValue"]),o("span",null,F(s(n).ddim_steps),1)])]),o("div",null,[o("div",S,[e[6]||(e[6]=o("span",null,"随机种子",-1)),t(r,{placement:"right",width:200,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"种子用于指定生成效果，可以用于生成套图，保障生成的一系列图片保持同一种风格"},{reference:i(()=>[o("div",$,[t(m,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),o("div",j,[t(f,{modelValue:s(n).seed,"onUpdate:modelValue":e[1]||(e[1]=l=>s(n).seed=l),type:"number",min:-1,maxlength:18,onFocus:e[2]||(e[2]=l=>s(C)()),placeholder:"请输入seed种子编号"},null,8,["modelValue"])])])])]),_:1})]),_:1},8,["modelValue"])])}}}),pe=O(A,[["__scopeId","data-v-ca887ef4"]]);export{pe as default};
