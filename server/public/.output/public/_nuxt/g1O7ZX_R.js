import{E as t}from"./oVx59syQ.js";import"./CmRxzTqw.js";/* empty css        */import e from"./zzCq5IAa.js";import{_ as l}from"./BbnoZrPC.js";import{_ as c}from"./BejSeAE6.js";import{l as m,M as r,N as _,O as a,a3 as s,a1 as n}from"./CUZG7cWw.js";const i={class:"h-full flex flex-col"},p={class:"flex-1 min-h-0 max-w-[800px] w-full mx-auto"},f={class:"p-main"},b=m({__name:"index",setup(x){return(d,u)=>{const o=t;return r(),_("div",i,[a("div",p,[s(o,null,{default:n(()=>[a("div",f,[s(l),s(e,{class:"mt-[16px]"}),s(c,{class:"mt-[16px]"})])]),_:1})])])}}});export{b as _};
