import{E as h}from"./CiabO6Xq.js";import{_ as w}from"./eFgaMLiC.js";import{E as B}from"./DNRqakyH.js";import{E as M}from"./B7GaOiDz.js";import{cA as C,d as N,bH as I}from"./CmRxzTqw.js";/* empty css        */import"./DikNcrXK.js";import"./DP2rzg_V.js";import{l as P,b as U,m as $,M as i,a0 as j,a1 as p,O as o,a3 as m,u as l,N as d,a4 as q,ag as z,a5 as F,y as G}from"./CUZG7cWw.js";import{_ as A}from"./xixvWuCN.js";import{_ as D}from"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./D6yUe_Nr.js";const J={class:"flex-1 leading-snug"},L={key:0,class:"flex justify-center items-center h-[150px] relative"},R={key:1,class:"uploader-container"},S={class:"el-upload__tip text-[#798696]"},n=10,H=P({__name:"uploader-picture",props:{modelValue:{default:""},type:{default:"image"},files:{}},emits:["update:modelValue"],setup(f,{emit:_}){const u=_,r=f,{modelValue:s}=C(r,u),a=U([]),g=t=>t.size>n*1024*1024?(N.error(`文件大小不能超过${n}MB`),!1):!0,v=t=>{u("update:modelValue",t.uri),s.value=t.uri,a.value=[{name:t.name,url:t.uri}]},x=t=>I(r.type,{file:t.file,name:"file",header:{}}),b=$(()=>{switch(r.type){case"image":return".jpg,.png,.jpeg";case"video":return".wmv,.avi,.mpg,.mpeg,.3gp,.mov,.mp4,.flv,.rmvb,.mkv";case"audio":return;default:return"*"}});return(t,e)=>{const y=h,E=w,k=B,V=M;return i(),j(V,{prop:"image",required:""},{label:p(()=>e[2]||(e[2]=[o("span",{class:"font-bold text-tx-primary"}," 上传参考图 ",-1)])),default:p(()=>[o("div",J,[m(k,{"file-list":l(a),"onUpdate:fileList":e[1]||(e[1]=c=>G(a)?a.value=c:null),class:"uploader",drag:"",multiple:!1,"show-file-list":!1,"on-success":v,"http-request":x,"before-upload":g,accept:l(b)},{default:p(()=>[o("div",null,[l(s)?(i(),d("div",L,[m(y,{class:"!block h-[100%]",src:l(s),fit:"contain"},null,8,["src"]),m(E,{class:"!absolute right-0 top-0 z-10 drop-shadow",name:"el-icon-CircleCloseFilled",color:"#ffffff",onClick:e[0]||(e[0]=q(c=>s.value="",["stop"]))})])):(i(),d("div",R,[e[3]||(e[3]=o("img",{src:A,alt:"文件上传",class:"w-8 mx-auto mb-2"},null,-1)),e[4]||(e[4]=o("div",{class:"el-upload__text text-[#798696] !text-[13px]"},[z(" 拖拽文件到此处或者"),o("em",null,"点击上传")],-1)),o("div",S,F(`支持图片格式：JPG/JPEG/PNG，低于${n}MB`),1)]))])]),_:1},8,["file-list","accept"])])]),_:1})}}}),ue=D(H,[["__scopeId","data-v-cd547139"]]);export{ue as default};
