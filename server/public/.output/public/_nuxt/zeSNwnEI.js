import{_ as V}from"./DB7Ysqj9.js";import{E as b}from"./B7GaOiDz.js";import{E as w}from"./DYjlFFbo.js";import{E as y,a as v}from"./C9f7n97H.js";import{h as E,b as h,e as U}from"./CmRxzTqw.js";import"./DP2rzg_V.js";import"./06MVqVCl.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{l as k,M as n,N,a3 as s,a1 as i,u as l,a0 as g,Z as B,O as a,ag as p}from"./CUZG7cWw.js";const I={class:"pt-[10px]"},R={class:"w-80"},A={class:"flex-1 min-w-0"},C={class:"w-full flex"},M={class:"flex-1 max-w-[320px]"},q={class:"flex-1 min-w-0"},S={class:"w-full flex"},z={class:"flex-1 max-w-[320px]"},D={class:"w-80"},Y=k({__name:"search-config",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:u}){const o=E(_,"modelValue",u);return h(),(O,e)=>{const f=V,d=b,m=w,r=y,c=v,x=U;return n(),N("div",I,[s(d,{label:"AI模型",prop:"model_id"},{default:i(()=>[a("div",R,[s(f,{class:"flex-1",id:l(o).model_id,"onUpdate:id":e[0]||(e[0]=t=>l(o).model_id=t),sub_id:l(o).model_sub_id,"onUpdate:sub_id":e[1]||(e[1]=t=>l(o).model_sub_id=t),"set-default":!1,disabled:""},null,8,["id","sub_id"])])]),_:1}),s(d,{label:"相似度",required:"",prop:"search_similarity"},{default:i(()=>[a("div",A,[a("div",C,[a("div",M,[s(m,{min:0,max:1,step:.001,modelValue:l(o).search_similarity,"onUpdate:modelValue":e[2]||(e[2]=t=>l(o).search_similarity=t)},null,8,["modelValue"])])]),e[6]||(e[6]=a("div",{class:"form-tips"}," 输入0-1之间的数值，支持3位小数点；高相似度推荐设置0.8以上 ",-1))])]),_:1}),s(d,{label:"单次搜索数量",required:"",prop:"search_limits"},{default:i(()=>[a("div",q,[a("div",S,[a("div",z,[s(m,{min:0,max:20,modelValue:l(o).search_limits,"onUpdate:modelValue":e[3]||(e[3]=t=>l(o).search_limits=t)},null,8,["modelValue"])])]),e[7]||(e[7]=a("div",{class:"form-tips"},"默认设置为5，请输入0-20之间的整数数值",-1))])]),_:1}),s(d,{label:"空搜索回复"},{default:i(()=>[s(c,{modelValue:l(o).search_empty_type,"onUpdate:modelValue":e[4]||(e[4]=t=>l(o).search_empty_type=t)},{default:i(()=>[s(r,{label:1},{default:i(()=>e[8]||(e[8]=[p(" AI回复")])),_:1}),s(r,{label:2},{default:i(()=>e[9]||(e[9]=[p(" 自定义回复")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(o).search_empty_type===2?(n(),g(d,{key:0},{default:i(()=>[a("div",D,[s(x,{modelValue:l(o).search_empty_text,"onUpdate:modelValue":e[5]||(e[5]=t=>l(o).search_empty_text=t),placeholder:"请输入回复内容，当搜索匹配不上内容时，直接回复填写的内容",type:"textarea",autosize:{minRows:6,maxRows:6},maxlength:1e3,"show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1})):B("",!0)])}}});export{Y as _};
