import{I as g,aq as $,cC as h,a5 as y,J as C,H as E,M as H,N}from"./CmRxzTqw.js";import{E as P}from"./YwtsEmdS.js";import{l as f,Y as S,m as V,M as o,N as l,$ as t,u as a,V as r,Z as i,O as n,a0 as u,a1 as B,a2 as D,ag as m,a5 as p,a3 as I}from"./CUZG7cWw.js";const w=g({icon:{type:$,default:()=>h},title:String,content:{type:String,default:""}}),M={back:()=>!0},T=["aria-label"],_=f({name:"ElPageHeader"}),q=f({..._,props:w,emits:M,setup(J,{emit:b}){const c=S(),{t:d}=y(),s=C("page-header"),v=V(()=>[s.b(),{[s.m("has-breadcrumb")]:!!c.breadcrumb,[s.m("has-extra")]:!!c.extra,[s.is("contentful")]:!!c.default}]);function k(){b("back")}return(e,L)=>(o(),l("div",{class:t(a(v))},[e.$slots.breadcrumb?(o(),l("div",{key:0,class:t(a(s).e("breadcrumb"))},[r(e.$slots,"breadcrumb")],2)):i("v-if",!0),n("div",{class:t(a(s).e("header"))},[n("div",{class:t(a(s).e("left"))},[n("div",{class:t(a(s).e("back")),role:"button",tabindex:"0",onClick:k},[e.icon||e.$slots.icon?(o(),l("div",{key:0,"aria-label":e.title||a(d)("el.pageHeader.title"),class:t(a(s).e("icon"))},[r(e.$slots,"icon",{},()=>[e.icon?(o(),u(a(E),{key:0},{default:B(()=>[(o(),u(D(e.icon)))]),_:1})):i("v-if",!0)])],10,T)):i("v-if",!0),n("div",{class:t(a(s).e("title"))},[r(e.$slots,"title",{},()=>[m(p(e.title||a(d)("el.pageHeader.title")),1)])],2)],2),I(a(P),{direction:"vertical"}),n("div",{class:t(a(s).e("content"))},[r(e.$slots,"content",{},()=>[m(p(e.content),1)])],2)],2),e.$slots.extra?(o(),l("div",{key:0,class:t(a(s).e("extra"))},[r(e.$slots,"extra")],2)):i("v-if",!0)],2),e.$slots.default?(o(),l("div",{key:1,class:t(a(s).e("main"))},[r(e.$slots,"default")],2)):i("v-if",!0)],2))}});var z=H(q,[["__file","page-header.vue"]]);const j=N(z);export{j as E};
