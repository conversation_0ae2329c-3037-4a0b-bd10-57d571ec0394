import{E as l}from"./CiabO6Xq.js";import{j as p,E as i}from"./CmRxzTqw.js";/* empty css        */import{l as c,M as m,N as u,a3 as e,u as _,O as g,a1 as f,ag as x}from"./CUZG7cWw.js";const d=""+new URL("noAuth.iRqApgVd.png",import.meta.url).href,h={class:"flex flex-col justify-center items-center h-[60vh]"},V=c({__name:"tologin",setup(E){const{toggleShowLogin:o,setLoginPopupType:s}=p(),n=()=>{s(),o()};return(B,t)=>{const r=l,a=i;return m(),u("div",h,[e(r,{class:"w-[200px] h-[200px]",src:_(d)},null,8,["src"]),t[1]||(t[1]=g("div",{class:"text-tx-regular mb-4"},"暂无查看权限，请登录账号后查看",-1)),e(a,{type:"primary",onClick:n},{default:f(()=>t[0]||(t[0]=[x(" 点击登录 ")])),_:1})])}}});export{V as _};
