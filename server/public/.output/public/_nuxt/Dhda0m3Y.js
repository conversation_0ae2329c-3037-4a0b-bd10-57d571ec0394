import{_ as b}from"./eFgaMLiC.js";import{E as V}from"./DNRqakyH.js";import{b as k,c0 as w,v as z}from"./CmRxzTqw.js";import"./DikNcrXK.js";import{l as B,b as v,m as E,j as S,E as D,u as s,M as n,N as i,a7 as I,a3 as p,a1 as N,V as j,O as f,Z as u,a6 as F,a4 as M}from"./CUZG7cWw.js";import{_ as R}from"./DlAUqK2U.js";const U={key:0},$={"element-loading-text":"上传中..."},A={key:0,class:"el-upload flex-col bg-fill-lighter"},L={key:1,class:"imgContiner relative"},O=["src"],P=B({__name:"index",props:{modelValue:{type:String,default:""},excludeDomain:{type:Boolean,default:!1},canClose:{type:Boolean,default:!0},size:{type:String,default:"100px"}},emits:["change","update:modelValue"],setup(r,{emit:h}){const g=h,{getImageUrl:x}=k(),d=r,c=v(!1),_=v(!1),a=E({get(){return d.excludeDomain?x(d.modelValue):d.modelValue},set(o){g("update:modelValue",o)}}),m=S(),y=async({raw:o})=>{var e,l;try{c.value=!0;const t=await w({file:o});c.value=!1,a.value=d.excludeDomain?t.url:t.uri,g("change",t.uri),(e=m.value)==null||e.clearFiles()}catch{c.value=!1,(l=m.value)==null||l.clearFiles()}};return D(()=>{_.value=!0}),(o,e)=>{const l=b,t=V,C=z;return s(_)?(n(),i("div",U,[I((n(),i("div",$,[p(t,{ref_key:"uploadRef",ref:m,class:"avatar-uploader","show-file-list":!1,limit:1,"on-change":y,"auto-upload":!1,accept:".jpg,.png,.gif,.jpeg"},{default:N(()=>[j(o.$slots,"default",{},()=>[s(a)?u("",!0):(n(),i("div",A,[p(l,{name:"el-icon-Plus",size:20}),e[1]||(e[1]=f("div",{class:"text-tx-secondary mt-[2px]"},"添加图片",-1))])),s(a)?(n(),i("div",L,[f("div",{class:"border border-solid border-br-light rounded-[6px] relative cursor-pointer",style:F({width:r.size,height:r.size})},[f("img",{class:"rounded-lg w-full h-full",src:s(a)},null,8,O)],4),r.canClose?(n(),i("div",{key:0,class:"icon absolute top-[-10px] right-[-10px] text-tx-secondary",onClick:e[0]||(e[0]=M(Z=>a.value="",["stop"]))},[p(l,{size:"20",name:"el-icon-CircleCloseFilled"})])):u("",!0)])):u("",!0)],!0)]),_:3},512)])),[[C,s(c)]])])):u("",!0)}}}),T=R(P,[["__scopeId","data-v-ba6ae763"]]);export{T as _};
