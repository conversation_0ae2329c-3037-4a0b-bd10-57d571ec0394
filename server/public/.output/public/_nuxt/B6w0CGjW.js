import{E as m}from"./C7tIPmrK.js";import{cA as f}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{_ as v}from"./CXAJ--Vj.js";import{l as h,r as V,M as t,N as o,a3 as w,O as s,_ as z,ao as k,u as l,a0 as b,a1 as C,$ as c,a5 as r}from"./CUZG7cWw.js";import{_ as L}from"./DlAUqK2U.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./u6CVc_ZE.js";import"./eFgaMLiC.js";const g={class:"mt-[15px]"},y={class:"flex flex-wrap"},B=["onClick"],$={class:"flex justify-center items-center mt-[10px] h-[20px]"},E={class:"text-base text-[#101010] dark:text-white mt-[4px] size-scale"},N={class:"text-xs text-[#798696] dark:text-white mt-[4px] size-name"},S=h({__name:"dalle-picture-size",props:{modelValue:{default:"512x512"}},emits:["update:modelValue"],setup(i,{emit:n}){const p=n,u=i,{modelValue:a}=f(u,p),x=V({lists:[{name:"头像图",scaleLabel:"1:1",scaleValue:"1024x1024",class:"w-[20px] h-[20px]"},{name:"媒体配图",scaleLabel:"3:4",scaleValue:"1024x1792",class:"w-[15px] h-[20px]"},{name:"文章配图",scaleLabel:"4:3",scaleValue:"1792x1024",class:"w-[20px] h-[15px]"}]});return a.value="1024x1024",(D,M)=>{const d=m;return t(),o("div",g,[w(v,{title:"图片尺寸",tips:"",required:""}),s("div",y,[(t(!0),o(z,null,k(l(x).lists,(e,_)=>(t(),b(d,{key:_,placement:"bottom",width:150,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:`分辨率：${e.scaleValue}px`},{reference:C(()=>[s("div",{class:c(["picture-size cursor-pointer text-center hover:text-primary",{"picture-size-active":l(a)==(e==null?void 0:e.scaleValue),"picture-size-disable":!(e!=null&&e.scaleValue)}]),onClick:P=>a.value=e.scaleValue},[s("div",$,[s("div",{class:c(["rect",e.class])},null,2)]),s("div",E,r(e.scaleLabel),1),s("div",N,r(e.name),1)],10,B)]),_:2},1032,["content"]))),128))])])}}}),R=L(S,[["__scopeId","data-v-055ff91f"]]);export{R as default};
