import{b as C,a as D}from"./C9f7n97H.js";import{_ as N}from"./Bv29pan0.js";import{E as B}from"./llRQJmEG.js";import{E as L}from"./oVx59syQ.js";import{bx as S,cP as $}from"./CmRxzTqw.js";/* empty css        *//* empty css        */import{u as I}from"./DNOp0HuO.js";import{u as R}from"./DymDsCmz.js";import U from"./DwB10QAP.js";import{e as A}from"./BhXe-NXN.js";import{l as F,r as G,ai as M,m as O,M as o,N as r,O as l,a3 as d,a1 as i,u as t,_ as x,ao as y,a0 as u,ag as P,a5 as T}from"./CUZG7cWw.js";const z=""+new URL("dub.hl03NZow.png",import.meta.url).href,Z={class:"h-full flex flex-col"},j={class:"pt-[15px] px-main"},q={class:"flex-1 min-h-0"},H={class:"p-main"},J={key:0},ce=F({__name:"dub",async setup(K){let s,p;const c=R(),n=G({keyword:"",index:0}),{data:_,refresh:h,pending:Q}=([s,p]=M(()=>I(()=>$(n),{lazy:!0},"$O0PtTSN0G7")),s=await s,p(),s),f=O(()=>{var a;return((a=_.value[n.index])==null?void 0:a.list)||[]});S(()=>{h()},1e3);const g=a=>{c.dub=a};return(a,b)=>{const v=C,k=D,E=N,V=B,w=L;return o(),r("div",Z,[l("div",null,[l("div",j,[d(E,{class:"my-[-5px]","default-height":42},{default:i(()=>[d(k,{modelValue:t(n).index,"onUpdate:modelValue":b[0]||(b[0]=e=>t(n).index=e),class:"el-radio-group-margin"},{default:i(()=>[(o(!0),r(x,null,y(t(_),(e,m)=>(o(),u(v,{key:m,label:m},{default:i(()=>[P(T(e.type),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),l("div",q,[d(w,null,{default:i(()=>[l("div",H,[t(f).length?(o(),r("div",J,[(o(!0),r(x,null,y(t(f),e=>(o(),u(U,{"active-id":t(c).dub.Voice,"item-id":e.Voice,key:e.Voice,class:"mb-[15px]",name:`${e.Name}-${e.Desc}`,pic:t(z),url:e.VoiceUrl,disabled:t(c).voiceContent.type===2,onClick:m=>g(e)},null,8,["active-id","item-id","name","pic","url","disabled","onClick"]))),128))])):(o(),u(V,{key:1,image:t(A),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}});export{ce as _};
