import{E}from"./06MVqVCl.js";import{E as g,a as w}from"./B7GaOiDz.js";import{e as R}from"./CmRxzTqw.js";import"./DP2rzg_V.js";/* empty css        */import{P as k}from"./CaNlADry.js";import{l as C,j as p,r as F,s as N,M as q,N as B,a3 as l,a1 as i,u as a,O as m}from"./CUZG7cWw.js";const D={class:"flex"},I={class:"flex"},U={class:"flex-1 min-w-0"},K=C({__name:"usage-settings",emits:["confirm"],setup(j,{expose:u,emit:c}){const f=c,n=p(),r=p(),o=F({id:"",limit_exceed:"",limit_today_chat:0,limit_total_chat:1}),x=N({limit_exceed:[{required:!0,message:"请输入超出将默认回复"}],limit_today_chat:[{required:!0,message:"请输入限制每个用户总对话数"}],limit_total_chat:[{required:!0,message:"请输入限制每个用户每天总对话数"}]}),h=()=>{var t,e;(t=n.value)==null||t.clearValidate(),(e=r.value)==null||e.open()},V=()=>{var t;(t=r.value)==null||t.close()},v=async()=>{var t;await((t=n.value)==null?void 0:t.validate()),f("confirm",o)};return u({open:h,close:V,setFormData:async t=>{Object.keys(o).forEach(e=>{o[e]=t[e]})}}),(t,e)=>{const d=E,_=g,y=R,b=w;return q(),B("div",null,[l(k,{ref_key:"popupRef",ref:r,title:"用量设置",async:!0,width:"550px",onConfirm:v},{default:i(()=>[l(b,{ref_key:"formRef",ref:n,model:a(o),rules:a(x),"label-width":"auto"},{default:i(()=>[l(_,{label:"限制每个用户总对话数",prop:"limit_today_chat"},{default:i(()=>[m("div",D,[l(d,{modelValue:a(o).limit_today_chat,"onUpdate:modelValue":e[0]||(e[0]=s=>a(o).limit_today_chat=s),"controls-position":"right",min:0,clearable:""},null,8,["modelValue"]),e[3]||(e[3]=m("span",{class:"ml-[10px]"},"条",-1))])]),_:1}),l(_,{label:"限制每个用户每天总对话数",prop:"limit_total_chat"},{default:i(()=>[m("div",I,[l(d,{modelValue:a(o).limit_total_chat,"onUpdate:modelValue":e[1]||(e[1]=s=>a(o).limit_total_chat=s),"controls-position":"right",min:0,clearable:""},null,8,["modelValue"]),e[4]||(e[4]=m("span",{class:"ml-[10px]"},"条",-1))])]),_:1}),l(_,{label:"超出将默认回复",prop:"limit_exceed"},{default:i(()=>[m("div",U,[l(y,{modelValue:a(o).limit_exceed,"onUpdate:modelValue":e[2]||(e[2]=s=>a(o).limit_exceed=s)},null,8,["modelValue"])])]),_:1})]),_:1},8,["model","rules"])]),_:1},512)])}}});export{K as _};
