import{a as S,E as $}from"./CiabO6Xq.js";import{_ as C}from"./eFgaMLiC.js";import"./CmRxzTqw.js";/* empty css        */import{_ as b}from"./Cs0_Uid5.js";import{E}from"./ArzC3z2d.js";/* empty css        */import{l as v,j as z,m as B,b as _,c as N,n as y,M as o,N as a,u as f,a0 as g,Z as s,a3 as d,a1 as R,y as I,ae as P,O as j,V as U,a6 as D}from"./CUZG7cWw.js";import{_ as L}from"./DlAUqK2U.js";const M={key:0},O={key:0},T={key:1},Z=v({__name:"preview",props:{modelValue:{type:Boolean,default:!1},url:{type:String,default:""},type:{type:String,default:"image"}},emits:["update:modelValue"],setup(e,{emit:l}){const r=e,c=l,m=z(),p=B({get(){return r.modelValue},set(n){c("update:modelValue",n)}}),u=()=>{c("update:modelValue",!1)},i=_([]);return N(()=>r.modelValue,n=>{n?y(()=>{var t;i.value=[r.url],(t=m.value)==null||t.play()}):y(()=>{var t;i.value=[],(t=m.value)==null||t.pause()})}),(n,t)=>{const h=S,w=b,V=E;return e.modelValue?(o(),a("div",M,[e.type=="image"?(o(),a("div",O,[f(i).length?(o(),g(h,{key:0,"url-list":f(i),"hide-on-click-modal":"",onClose:u},null,8,["url-list"])):s("",!0)])):s("",!0),e.type=="video"||e.type=="audio"?(o(),a("div",T,[d(V,{modelValue:f(p),"onUpdate:modelValue":t[0]||(t[0]=k=>I(p)?p.value=k:null),width:"740px",title:`${e.type=="video"?"视频预览":"音频预览"}`,"before-close":u},{default:R(()=>[d(w,{ref_key:"playerRef",ref:m,src:e.url,width:"100%",height:"450px"},null,8,["src"])]),_:1},8,["modelValue","title"])])):s("",!0)])):s("",!0)}}}),q=v({components:{Preview:Z},props:{uri:{type:String},fileSize:{type:String,default:"100px"},type:{type:String,default:"image"},fileName:{type:String,default:""}},emits:["close"],setup(e,l){return{show:_(!1)}}}),x=["src"],A={key:3,class:"audio"};function F(e,l,r,c,m,p){const u=$,i=C,n=P("Preview");return o(),a("div",null,[j("div",{class:"file-item relative",style:D({height:e.fileSize,width:e.fileSize})},[e.type=="image"?(o(),g(u,{key:0,class:"image",fit:"contain",src:e.uri},null,8,["src"])):e.type=="video"?(o(),a("video",{key:1,class:"video",src:e.uri},null,8,x)):s("",!0),e.type=="video"?(o(),a("div",{key:2,class:"absolute left-1/2 top-1/2 translate-x-[-50%] translate-y-[-50%] rounded-full w-5 h-5 flex justify-center items-center bg-[rgba(0,0,0,0.3)] cursor-pointer",onClick:l[0]||(l[0]=t=>e.show=!0)},[d(i,{name:"el-icon-CaretRight",size:18,color:"#fff"})])):s("",!0),e.type=="audio"?(o(),a("div",A,[d(u,{class:"w-full h-full",src:"/src/assets/images/musicIcon.png"})])):s("",!0),U(e.$slots,"default",{},void 0,!0)],4),d(n,{url:e.uri,modelValue:e.show,"onUpdate:modelValue":l[1]||(l[1]=t=>e.show=t),type:e.type},null,8,["url","modelValue","type"])])}const te=L(q,[["render",F],["__scopeId","data-v-fa6709ca"]]);export{te as _,Z as a};
