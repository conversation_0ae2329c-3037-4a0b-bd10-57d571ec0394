import{h as R,n as S,f as j,E as D,e as F}from"./CmRxzTqw.js";import{_ as M}from"./D-n7HwjM.js";import{E as $}from"./ArzC3z2d.js";/* empty css        */import{d as z}from"./CRNANWso.js";import{u as L}from"./Bj_9-7Jh.js";import{B as P}from"./qRM0tN96.js";import H from"./BbGVBgM5.js";import{l as O,b as f,j as Q,m as T,c as Z,M as k,a0 as q,a1 as n,u as o,y as _,O as t,N as A,Z as G,a3 as s,a5 as w,ag as x}from"./CUZG7cWw.js";import{_ as J}from"./DlAUqK2U.js";import"./DNRqakyH.js";import"./DikNcrXK.js";import"./Bs9Zhtqd.js";import"./Bh-PoUNP.js";import"./gj6kus5n.js";import"./D8e5izeA.js";import"./CDwN27aR.js";import"./DCTLXrZ8.js";import"./C3XldtMC.js";const K=["src"],W={class:"absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center"},X={class:"text-center text-white mt-[15px] text-[18px] font-bold"},Y={class:"flex py-3 ml-[-5px] items-center"},ee={class:"flex-1 ml-3"},te={class:""},oe={class:"flex items-center"},le={class:"flex items-center"},se=O({__name:"poster",props:{show:{type:Boolean},url:{},apikey:{},shareId:{}},emits:["update:show","update"],setup(V,{emit:h}){const i=V,v=h,m=R(i,"show",v),p=`${S}/resource/image/other/ai_share_bg.png`,l=f(p),d=f("快来扫码"),u=f("和我的智能体对话吧"),B=a=>{l.value=a.uri},g=Q(),b=T(()=>`${location.origin}/chat/${i.apikey}`),{lockFn:E,isLock:C}=L(async()=>{try{await P({id:i.shareId,url:l.value}),v("update"),await z(g.value)}catch{return j.msgError("下载失败，请重试"),Promise.reject()}}),U=()=>{l.value=p};return Z(()=>i.url,a=>{a?l.value=a:l.value=p}),(a,e)=>{const c=D,I=M,y=F,N=$;return k(),q(N,{modelValue:o(m),"onUpdate:modelValue":e[2]||(e[2]=r=>_(m)?m.value=r:null),title:"生成海报",width:"400"},{default:n(()=>[t("div",{class:"poster relative",ref_key:"posterRef",ref:g},[o(l)?(k(),A("img",{key:0,class:"object-cover w-full h-full",src:o(l)},null,8,K)):G("",!0),t("div",W,[s(H,{text:o(b),size:"200",class:"rounded-[10px]",dotScale:"1",margin:"12"},null,8,["text"]),t("div",X,[t("div",null,w(o(d)),1),t("div",null,w(o(u)),1)])])],512),t("div",Y,[s(I,{limit:1,onSuccess:B},{default:n(()=>[s(c,{type:"primary",link:""},{default:n(()=>e[3]||(e[3]=[x("自定义背景图")])),_:1})]),_:1}),t("div",ee,[s(c,{type:"primary",link:"",onClick:U},{default:n(()=>e[4]||(e[4]=[x("使用默认图")])),_:1})]),e[5]||(e[5]=t("div",{class:"text-tx-regular"},"背景图尺寸：430*670",-1))]),t("div",te,[t("div",oe,[e[6]||(e[6]=t("div",{class:"text-tx-regular flex-none mr-2"},"标题",-1)),s(y,{modelValue:o(d),"onUpdate:modelValue":e[0]||(e[0]=r=>_(d)?d.value=r:null),placeholder:"请输入背景图地址"},null,8,["modelValue"])]),t("div",le,[e[7]||(e[7]=t("div",{class:"text-tx-regular flex-none mr-2"},"描述",-1)),s(y,{modelValue:o(u),"onUpdate:modelValue":e[1]||(e[1]=r=>_(u)?u.value=r:null),placeholder:"请输入背景图地址",class:"py-3"},null,8,["modelValue"])])]),t("div",null,[s(c,{type:"primary",size:"large",class:"w-full",loading:o(C),onClick:o(E)},{default:n(()=>e[8]||(e[8]=[x(" 保存 ")])),_:1},8,["loading","onClick"])])]),_:1},8,["modelValue"])}}}),Be=J(se,[["__scopeId","data-v-4f9d7aed"]]);export{Be as default};
