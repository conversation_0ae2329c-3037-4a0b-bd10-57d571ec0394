import{e as v}from"./CmRxzTqw.js";import{E as C,a as E}from"./B7GaOiDz.js";import{P}from"./CaNlADry.js";import"./DP2rzg_V.js";/* empty css        */import{m as R}from"./Dl64kDm5.js";import{l as V,j as b,b as k,M as w,a0 as x,a1 as n,a3 as a,u as l}from"./CUZG7cWw.js";const N=V({__name:"renamePop",emits:["success","close"],setup(y,{expose:p,emit:r}){const t=b(),i=r,e=k({name:"",fd_id:-1}),u=async()=>{await R({...e.value}),i("success"),t.value.close()};return p({open:s=>{t.value.open(),e.value.fd_id=s}}),(s,o)=>{const c=v,f=C,_=E,d=P;return w(),x(d,{title:"重命名",ref_key:"popRef",ref:t,async:"",onConfirm:u,onClose:o[1]||(o[1]=m=>s.$emit("close"))},{default:n(()=>[a(_,null,{default:n(()=>[a(f,{label:"文件名称",class:"is-required"},{default:n(()=>[a(c,{placeholder:"请输入文件名称",modelValue:l(e).name,"onUpdate:modelValue":o[0]||(o[0]=m=>l(e).name=m)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},512)}}});export{N as _};
