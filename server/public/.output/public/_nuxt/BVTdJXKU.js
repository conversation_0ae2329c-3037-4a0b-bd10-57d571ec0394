import{E as N,a as V}from"./DHUC3PVh.js";import{_ as $}from"./CbQsrhNE.js";import{b as w,j as B,dc as x,d1 as P,E as R}from"./CmRxzTqw.js";import{_ as W}from"./B4XIt-XN.js";import{_ as A}from"./DISR6sUa.js";import{_ as I}from"./BEuS_AA8.js";import{l as F,b as O,m as p,M as r,N as _,O as c,a3 as u,a1 as m,u as t,ag as g,Z as n,a0 as l,_ as j,ao as G}from"./CUZG7cWw.js";import{_ as M}from"./DlAUqK2U.js";import"./7tQUKVT9.js";import"./Dbi96Hzd.js";import"./CXDY_LVT.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./oVx59syQ.js";import"./DCzKTodP.js";import"./D8e5izeA.js";import"./gj6kus5n.js";import"./_i9izYtZ.js";import"./DSuLZIN6.js";import"./Ddo5WWE5.js";import"./D6yUe_Nr.js";import"./B7GaOiDz.js";import"./Bh-PoUNP.js";import"./FAfxnQR5.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./Bj_9-7Jh.js";import"./eFgaMLiC.js";import"./DqGsTvs3.js";import"./CiabO6Xq.js";import"./CpufhUzm.js";import"./BscXL5XZ.js";const U={class:"flex flex-col h-full"},Y={class:"flex-1 pt-[20px] px-[30px] min-h-0"},Z={key:0,class:"bg-[#f4f4f4] px-[20px] py-[15px] flex dark:bg-[#333]"},q={key:0,class:"flex-1 text-tx-secondary"},z=["href"],D=["href"],H=F({__name:"index",setup(J){const s=w(),b=B(),k=[{name:"微信登录",type:"3"},{name:"手机号登录",type:"1"},{name:"邮箱登录",type:"2"}],i=O(1),C=p(()=>k.filter(e=>L.value.includes(e.type))),L=p(()=>{var e;return((e=s.getLoginConfig)==null?void 0:e.login_way)||[]}),f=p(()=>{var e;return((e=s.getLoginConfig)==null?void 0:e.register_way)||[]}),y=p(()=>s.getLoginConfig.is_agreement===1),E=p(()=>(i.value=s.getLoginConfig.default_login_way.toString(),s.getLoginConfig.default_login_way.toString())),v=e=>{i.value=e};return(e,a)=>{const h=V,S=N,d=$;return r(),_("div",U,[c("div",Y,[u(S,{"model-value":t(E),onTabChange:v},{default:m(()=>[(r(!0),_(j,null,G(t(C),(o,T)=>(r(),l(h,{key:T,label:o.name,name:o.type},{default:m(()=>[o.type==="3"&&t(i)==="3"?(r(),l(I,{key:0})):n("",!0),o.type==="1"?(r(),l(W,{key:1})):n("",!0),o.type==="2"?(r(),l(A,{key:2})):n("",!0)]),_:2},1032,["label","name"]))),128))]),_:1},8,["model-value"])]),t(f).length&&t(i)!="3"||t(y)?(r(),_("div",Z,[t(y)?(r(),_("div",q,[a[1]||(a[1]=g(" 您登录即同意 ")),u(d,{to:`/policy/${t(x).SERVICE}`,custom:""},{default:m(({href:o})=>[c("a",{class:"text-tx-primary",href:o,target:"_blank"}," 用户协议 ",8,z)]),_:1},8,["to"]),a[2]||(a[2]=g(" 和 ")),u(d,{class:"text-tx-primary",to:`/policy/${t(x).PRIVACY}`,custom:""},{default:m(({href:o})=>[c("a",{class:"text-tx-primary",href:o,target:"_blank"}," 隐私政策 ",8,D)]),_:1},8,["to"])])):n("",!0),t(f).length&&t(i)!="3"?(r(),l(t(R),{key:1,type:"primary",link:"",onClick:a[0]||(a[0]=o=>t(b).setLoginPopupType(t(P).REGISTER))},{default:m(()=>a[3]||(a[3]=[g(" 注册新账号 ")])),_:1})):n("",!0)])):n("",!0)])}}}),Pt=M(H,[["__scopeId","data-v-b05048cb"]]);export{Pt as default};
