function s(o,n){var c,d;const t=o.split(","),a=(d=(c=t[0])==null?void 0:c.match(/:(.*?);/u))==null?void 0:d[1];if(!a)throw"dataUrl 解析错误";const l=atob(t[1]);let e=l.length;const r=new Uint8Array(e);for(;e--;)r[e]=l.charCodeAt(e);const i=new Blob([r],{type:a});return new File([i],n,{type:a,lastModified:Date.now()})}function w(o,n){const t=document.createElement("a");document.body.appendChild(t),t.href=o,t.download=n,t.click(),t.remove()}export{w as a,s as d};
