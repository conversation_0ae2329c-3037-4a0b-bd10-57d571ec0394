import{I as oe,V as le,J as ne,M as re,N as ie,b as ce,cF as de,m as pe,_ as ue,s as me,e as fe,E as _e}from"./CmRxzTqw.js";import{_ as xe}from"./eFgaMLiC.js";import{l as C,M as r,N as d,$ as f,u as t,V as v,ag as k,a5 as u,Z as m,O as a,a6 as T,b as S,r as ge,ai as A,m as he,j as ye,a3 as c,a1 as p,y as we,aG as ve,_ as Se,ao as be,a0 as b,aH as ke,a7 as M,a4 as Ce,a8 as Ne}from"./CUZG7cWw.js";import{_ as Ve}from"./CbQsrhNE.js";import{W as Ie}from"./BZBRZdpQ.js";import{E as $e}from"./CiabO6Xq.js";import{E as ze}from"./C9jirCEY.js";/* empty css        */import{u as O}from"./DNOp0HuO.js";import{g as Pe,c as Ee,a as Be}from"./BdjxQtGL.js";import{e as De}from"./BhXe-NXN.js";import{_ as Le}from"./DlAUqK2U.js";import"./CpufhUzm.js";import"./_i9izYtZ.js";import"./BscXL5XZ.js";const Fe=oe({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:le([String,Object,Array]),default:""},bodyClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),je=C({name:"ElCard"}),Ae=C({...je,props:Fe,setup(W){const n=ne("card");return(o,N)=>(r(),d("div",{class:f([t(n).b(),t(n).is(`${o.shadow}-shadow`)])},[o.$slots.header||o.header?(r(),d("div",{key:0,class:f(t(n).e("header"))},[v(o.$slots,"header",{},()=>[k(u(o.header),1)])],2)):m("v-if",!0),a("div",{class:f([t(n).e("body"),o.bodyClass]),style:T(o.bodyStyle)},[v(o.$slots,"default")],6),o.$slots.footer||o.footer?(r(),d("div",{key:1,class:f(t(n).e("footer"))},[v(o.$slots,"footer",{},()=>[k(u(o.footer),1)])],2)):m("v-if",!0)],2))}});var Me=re(Ae,[["__file","card.vue"]]);const Oe=ie(Me),Te={class:"h-full flex flex-col create 4xl:w-[2000px] mx-auto"},We={class:"2xl:max-w-[880px] xl:max-w-[780px] lg:max-w-[680px] max-w-[680px] search w-full mt-4"},Ge={class:"flex-1 min-h-0 mx-[16px]"},Je={class:"h-full flex flex-col"},Re=["onClick"],Ue={class:"flex-1 min-h-0"},qe={key:0,"infinite-scroll-distance":"50","infinite-scroll-immediate":"false"},He={class:"flex flex-col min-h-0 h-full"},Ke={class:"flex items-center"},Ze=["src"],Qe={class:"text-lg font-medium line-clamp-1"},Xe={class:"h-[36px]"},Ye={key:0,class:"text-xs text-tx-secondary mt-[10px] line-clamp-2 flex-1"},et={class:"flex items-center mt-[16px]"},tt={class:"text-tx-secondary mr-[30px] flex items-center text-sm"},at={class:"ml-1"},st=["onClick"],ot={class:"flex flex-col justify-center items-center w-full h-[60vh]"},lt=C({__name:"index",async setup(W){let n,o;const N=ce(),V=S(0),_=S(""),x=S(0),e=ge({pageNo:1,count:0,loading:!0,pageSize:10,lists:[]}),G={4e3:{rowPerView:7},2e3:{rowPerView:6},1800:{rowPerView:5},1600:{rowPerView:5},1440:{rowPerView:4},1360:{rowPerView:4},1280:{rowPerView:4},1024:{rowPerView:3}},J=async()=>{const s=await Pe();return $(0),s},{data:I,refresh:nt}=([n,o]=A(()=>O(J,{default(){return[]},lazy:!0},"$gS1lmh76Gl")),n=await n,o(),n),{data:g}=([n,o]=A(()=>O(()=>pe({id:5}),{transform:s=>JSON.parse(s.data),default(){return[]},lazy:!0},"$Dd0tiMcnfk")),n=await n,o(),n),R=he(()=>s=>{switch(s){case 1:return"text-black";case 2:return"text-white";case 3:return"text-primary"}}),U=ye(),q=s=>{U.value=s,console.log(s)},$=s=>{var i;V.value=s,x.value=(i=I.value[s])==null?void 0:i.id,P()},h=async()=>{e.loading=!0;try{const s=await Be({category_id:x.value,keyword:_.value,page_no:e.pageNo,page_size:e.pageSize});e.pageNo===1&&(e.lists=[]),e.count=s.count,e.lists.push(...s.lists)}finally{setTimeout(()=>e.loading=!1,200)}},H=()=>{e.count>=e.pageNo*e.pageSize&&(e.pageNo++,h())},z=async()=>{e.pageSize=e.pageNo*e.pageSize,e.pageNo=1,await h()},P=async()=>{e.loading=!0,e.pageSize=15,e.pageNo=1,await h()},K=async s=>{await Ee({id:s}),z()};return de(_,s=>{P()},{debounce:500}),(s,i)=>{const Z=fe,y=xe,Q=Oe,X=Ve,Y=Ie,ee=$e,te=_e,ae=ue,se=ze;return r(),d("div",null,[c(ae,{name:"default"},{default:p(()=>{var E,B,D,L,F,j;return[a("div",Te,[a("header",{class:"creation-header flex flex-col justify-center items-center px-[16px] m-[16px] rounded-[12px] overflow-hidden",style:T({"background-image":`url(${t(N).getImageUrl((B=(E=t(g)[0])==null?void 0:E.prop)==null?void 0:B.banner_bg)})`})},[a("div",{class:f(["font-medium 2xl:text-[50px] xl:text-[40px] lg:text-[36px] text-[36px]",t(R)((L=(D=t(g)[0])==null?void 0:D.prop)==null?void 0:L.title_color)])},u((j=(F=t(g)[0])==null?void 0:F.prop)==null?void 0:j.title),3),a("div",We,[c(Z,{size:"large",class:"2xl:h-[54px] xl:h-[48px] lg:h-[44px] rounded-[7px]",style:{"--el-border-color":"transparent"},modelValue:t(_),"onUpdate:modelValue":i[0]||(i[0]=l=>we(_)?_.value=l:null),"prefix-icon":t(me),placeholder:"请输入关键词搜索"},null,8,["modelValue","prefix-icon"])])],4),a("div",Ge,[a("div",Je,[c(t(ve),{slidesPerView:"auto",spaceBetween:16,class:"category-lists w-full",onSwiper:q,style:{padding:"10px 0","margin-left":"0"}},{default:p(()=>[(r(!0),d(Se,null,be(t(I),(l,w)=>(r(),b(t(ke),{key:l.id,style:{width:"auto","margin-right":"12px"}},{default:p(()=>[Object.keys(l).includes("name")?(r(),d("div",{key:0,class:f(["category-item bg-white",{"is-active":t(V)===w}]),onClick:rt=>$(w)},u(l.name),11,Re)):m("",!0)]),_:2},1024))),128))]),_:1}),a("div",Ue,[t(e).lists.length?M((r(),d("div",qe,[c(Y,{ref:"waterFull",delay:100,list:t(e).lists,width:305,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:G},{item:p(({item:l})=>[c(X,{to:{path:"/creation/produce",query:{cateId:t(x),modelId:l.id}},class:"h-full"},{default:p(()=>[c(Q,{class:"!border-none h-full rounded-[12px] relative cardItem shadow-light",shadow:"never",style:{"border-radius":"12px"},"body-style":"padding: 20px;height: 100%"},{default:p(()=>[a("div",He,[a("div",Ke,[a("img",{class:"w-[34px] h-[34px] mr-[10px]",src:l.image,alt:""},null,8,Ze),a("div",Qe,u(l.name),1)]),a("div",Xe,[l.tips?(r(),d("div",Ye,u(l.tips),1)):m("",!0)]),a("div",et,[a("div",tt,[c(y,{name:"local-icon-yonghu"}),a("div",at,u(l.use_num)+"人使用过 ",1)]),a("div",{class:"flex collection absolute top-[10px] right-[10px]",onClick:Ce(w=>K(l.id),["prevent"])},[l.is_collect?m("",!0):(r(),b(y,{key:0,size:20,name:"el-icon-Star",color:"#999"})),l.is_collect?(r(),b(y,{key:1,style:{transform:`scale(
                                                                        1.2
                                                                    )`},size:20,name:"el-icon-StarFilled",color:"#FFB529"})):m("",!0)],8,st)])])]),_:2},1024)]),_:2},1032,["to"])]),_:1},8,["list"])])),[[se,H]]):m("",!0),M(a("div",ot,[c(ee,{class:"w-[200px] h-[200px]",src:t(De)},null,8,["src"]),i[2]||(i[2]=a("div",{class:"text-tx-regular mb-4"}," 当前选择暂无创作～ ",-1)),c(te,{type:"primary",onClick:z},{default:p(()=>i[1]||(i[1]=[k(" 点击刷新 ")])),_:1})],512),[[Ne,!t(e).lists.length&&!t(e).loading]])])])])])]}),_:1})])}}}),bt=Le(lt,[["__scopeId","data-v-031e5857"]]);export{bt as default};
