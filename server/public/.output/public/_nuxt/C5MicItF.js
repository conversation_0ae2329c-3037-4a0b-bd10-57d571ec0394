import{e as s}from"./CmRxzTqw.js";import{l as i,M as l,N as a,O as d,a3 as r,u as e,af as f}from"./CUZG7cWw.js";import{_ as u}from"./COoKzhde.js";import{_}from"./DIux4E1M.js";import{useSearch as c}from"./CaJo29OT.js";import{_ as x}from"./DlAUqK2U.js";import"./eFgaMLiC.js";import"./Zz2DnF66.js";import"./DCTLXrZ8.js";import"./D5Svi-lq.js";import"./C7tIPmrK.js";import"./u6CVc_ZE.js";import"./DCzKTodP.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./P8Qw-ZvZ.js";import"./DwRn548t.js";import"./BLV0QRdm.js";import"./Cq2NhlyP.js";import"./DecTOTC8.js";const y={class:"bg-page overflow-hidden flex items-center input-select"},v={class:"flex-none flex px-[8px]"},k=i({__name:"input-select",setup(V){const{options:p,launchSearch:m}=c();return(w,o)=>{const n=s;return l(),a("div",y,[d("div",v,[r(u,{mode:"dropdown",model:e(p).model,"onUpdate:model":o[0]||(o[0]=t=>e(p).model=t),type:e(p).type,"onUpdate:type":o[1]||(o[1]=t=>e(p).type=t)},null,8,["model","type"])]),r(n,{modelValue:e(p).ask,"onUpdate:modelValue":o[2]||(o[2]=t=>e(p).ask=t),placeholder:"输入你想搜索的问题",onKeydown:o[3]||(o[3]=f(t=>e(m)(),["enter"]))},null,8,["modelValue"]),r(_,{onClick:o[4]||(o[4]=t=>e(m)())})])}}}),L=x(k,[["__scopeId","data-v-f9d3468d"]]);export{L as default};
