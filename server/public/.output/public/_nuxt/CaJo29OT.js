import{cG as h,j,l as x,f as d,$ as C}from"./CmRxzTqw.js";import{g as L,p as U,a as q,b as I}from"./Cq2NhlyP.js";import{ModelEnums as b,TypeEnums as _,StatusEnums as g}from"./BLV0QRdm.js";import{i as R}from"./DecTOTC8.js";import{b as E,c as Y}from"./CUZG7cWw.js";const n=E({model:b.BASE,type:_.ALL,ask:"",probe:0});Y(()=>n.value.model,e=>{e!==b.STUDY&&(n.value.type=_.ALL)},{flush:"post",immediate:!0});let c;const H=()=>{const e=h(()=>({id:-1,query:"",data:[],status:-1,search:[],outline:{},outline_json:{},suggestion:{}}),"$sd7cibyGCt"),i=h(()=>!1,"$843Kh9xI0P"),v=h(()=>!1,"$T48Tb6Ybal"),f=j(),m=x(),y=h(()=>({status:0,price:0,isVipFree:!1}),"$JUXG1OFsCM"),k=async()=>{y.value=await L()},p=()=>{e.value.id=-1,e.value.query=n.value.ask,e.value.data=[],e.value.status=g.ANALYSIS,e.value.search=[],e.value.outline={},e.value.outline_json={},e.value.suggestion={}},w=(o,r,s)=>{const a=e.value.data.find(t=>t.type=="markdown"&&t.target=="update");a?(R(a.content)&&(a.content+=s),C(a.content)&&a.content.push(s),a.target=r):e.value.data.push({type:o,target:r,content:s})},S=o=>{var s;const r=o.findLast(a=>a.type==="search_result");return((s=r==null?void 0:r.content)==null?void 0:s.map((a,t)=>({...a,index:t+1})))||[]};return{config:y,getConfig:k,showSearchResult:v,options:n,result:e,launchSearch:async(o="")=>{if(o&&(n.value.ask=o),!f.isLogin)return f.toggleShowLogin();if(!n.value.ask)return d.msgError("请输入你想搜索的问题");if(i.value)return d.msgWarning("正在搜索中...");i.value=!0,p(),v.value=!0,c=U({...n.value,stream:!0}),c.onmessage=({data:r})=>{const{card_type:s,target:a,data:t}=r;switch(s){case"error":d.msgError(t),i.value=!1,v.value=!1;break;case"action":{e.value.status=g.SEARCH;break}case"markdown":e.value.status=g.SUMMARY;case"expand_query":case"search_result":{w(s,a,t);break}case"suggestion":{e.value.suggestion={type:s,content:t};break}case"outline_json":{e.value.outline_json=t;break}case"outline":{e.value.outline=t;break}case"done":{e.value.status=g.SUCCESS+1,e.value.search=S(e.value.data),f.getUser(),k();break}case"finish":{e.value.id=t.id,m.push({query:{id:t.id}});break}}},c.onerror=()=>{i.value=!1,v.value=!1},c.onclose=()=>{i.value=!1}},abortSearch:()=>{c==null||c.abort()},initResult:p,getSearchInfo:async o=>{var r,s;try{p(),e.value.status=g.ANALYSIS,v.value=!0;const{ask:a,model:t,type:A,results:u}=await q({id:o});if(!u){d.msgError("数据不存在"),m.back();return}n.value.ask=a,n.value.model=t,n.value.type=A,e.value.query=a,e.value.status=g.SUCCESS+1,Array.isArray(u)?(e.value.data=u.filter(l=>["markdown","expand_query","search_result"].includes(l.type)),e.value.search=S(u),e.value.suggestion=u.find(l=>l.type=="suggestion")||[],e.value.outline_json=((r=u.find(l=>l.type=="outline_json"))==null?void 0:r.content)||{},e.value.outline=((s=u.find(l=>l.type=="outline"))==null?void 0:s.content)||{}):(e.value.data=[{content:u.markdown,type:"markdown"},{type:"search_result",content:u.search_result}],e.value.search=S(e.value.data),e.value.suggestion={type:"suggestion",content:u.suggestion},e.value.outline=u.outline||{},e.value.outline_json=u.outline_json||{})}catch(a){console.error(a),v.value=!1}}}},J=()=>{const e=E([]);return{searchEx:e,getSearchEx:async()=>{e.value=await I()}}};export{H as useSearch,J as useSearchEx};
