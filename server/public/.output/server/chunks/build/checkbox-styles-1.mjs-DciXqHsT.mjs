const checkbox_vue_vue_type_style_index_0_scoped_71662013_lang = ".el-checkbox-group[data-v-71662013] .el-checkbox-button{height:36px;margin-bottom:10px;margin-right:13px}.el-checkbox-group[data-v-71662013] .el-checkbox-button.is-checked .el-checkbox-button__inner{background-color:var(--el-color-primary-light-9);color:var(--el-color-primary)}.el-checkbox-group[data-v-71662013] .el-checkbox-button .el-checkbox-button__inner{background-color:var(--el-bg-color-page);border:1px solid transparent;border-radius:8px;box-shadow:none;color:var(--el-text-color-primary);height:36px;padding:10px 16px}";

export { checkbox_vue_vue_type_style_index_0_scoped_71662013_lang as c };
//# sourceMappingURL=checkbox-styles-1.mjs-DciXqHsT.mjs.map
