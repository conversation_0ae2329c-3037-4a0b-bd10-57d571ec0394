const dialogPoster_vue_vue_type_style_index_0_scoped_4178ddb0_lang = ".poster[data-v-4178ddb0]{background:transparent;overflow:hidden;position:relative;width:100%}.poster-bg[data-v-4178ddb0]{height:100%;position:absolute;width:100%}.poster-contain1[data-v-4178ddb0]{background-color:transparent;position:relative;z-index:1}";

export { dialogPoster_vue_vue_type_style_index_0_scoped_4178ddb0_lang as d };
//# sourceMappingURL=dialog-poster-styles-1.mjs-1R2mDmDu.mjs.map
