const posterPop_vue_vue_type_style_index_0_scoped_90c13495_lang = ".share-pop[data-v-90c13495]  .el-dialog__header{font-weight:500;padding-bottom:0}.share-pop[data-v-90c13495]  .el-dialog{border-radius:15px}.share-pop[data-v-90c13495]  .el-dialog__body{padding:15px 10px!important}";

export { posterPop_vue_vue_type_style_index_0_scoped_90c13495_lang as p };
//# sourceMappingURL=posterPop-styles-1.mjs-BWSV2V6j.mjs.map
