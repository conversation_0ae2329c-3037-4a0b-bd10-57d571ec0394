import { a as buildAssetsURL } from '../routes/renderer.mjs';

const singleRow_vue_vue_type_style_index_0_scoped_71269ca2_lang = ".layout-default[data-v-71269ca2]{min-width:1200px}.layout-bg[data-v-71269ca2]{background:url(" + buildAssetsURL("layout_bg.CQiN3ao1.png") + ") no-repeat;background-position:50%;background-size:cover}";

export { singleRow_vue_vue_type_style_index_0_scoped_71269ca2_lang as s };
//# sourceMappingURL=single-row-styles-1.mjs-DjrKN-tw.mjs.map
