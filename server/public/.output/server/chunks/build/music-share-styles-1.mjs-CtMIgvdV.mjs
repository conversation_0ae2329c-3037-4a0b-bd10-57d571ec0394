const musicShare_vue_vue_type_style_index_0_scoped_0138da76_lang = ".share-popup[data-v-0138da76]  .el-dialog{border-radius:20px}.share-popup[data-v-0138da76]  .el-select__wrapper{box-shadow:none}.share-popup[data-v-0138da76]  .el-select__wrapper:hover{box-shadow:0 0 0 1px var(--el-border-color) inset}";

export { musicShare_vue_vue_type_style_index_0_scoped_0138da76_lang as m };
//# sourceMappingURL=music-share-styles-1.mjs-CtMIgvdV.mjs.map
