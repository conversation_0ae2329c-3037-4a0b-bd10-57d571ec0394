import { p as permissionOption_vue_vue_type_style_index_0_scoped_bfb962ae_lang } from './permission-option-styles-1.mjs-CXudqAJO.mjs';

const permissionOptionStyles_BxdzPhLN = [permissionOption_vue_vue_type_style_index_0_scoped_bfb962ae_lang, permissionOption_vue_vue_type_style_index_0_scoped_bfb962ae_lang];

export { permissionOptionStyles_BxdzPhLN as default };
//# sourceMappingURL=permission-option-styles.BxdzPhLN.mjs.map
