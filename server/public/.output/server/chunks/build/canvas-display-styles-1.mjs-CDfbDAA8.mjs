import { a as buildAssetsURL } from '../routes/renderer.mjs';

const canvasDisplay_vue_vue_type_style_index_0_scoped_089e911c_lang = ".canvas-display[data-v-089e911c]{align-items:center;background-color:#ebebeb;border-radius:10px;box-shadow:0 2px 6px #ebefff;display:flex;justify-content:center;width:100%}.canvas-display .canvas-bg[data-v-089e911c]{background:url(" + buildAssetsURL("16_9.BPjMnVnh.png") + ") no-repeat;background-position:50%;background-size:cover}";

export { canvasDisplay_vue_vue_type_style_index_0_scoped_089e911c_lang as c };
//# sourceMappingURL=canvas-display-styles-1.mjs-CDfbDAA8.mjs.map
