import { c as canvasDisplay_vue_vue_type_style_index_0_scoped_089e911c_lang } from './canvas-display-styles-1.mjs-CDfbDAA8.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import 'devalue';
import 'vue/server-renderer';
import '@unhead/ssr';
import 'unhead';
import 'vue';
import '@unhead/shared';

const canvasDisplayStyles_CGp2yZ9t = [canvasDisplay_vue_vue_type_style_index_0_scoped_089e911c_lang, canvasDisplay_vue_vue_type_style_index_0_scoped_089e911c_lang];

export { canvasDisplayStyles_CGp2yZ9t as default };
//# sourceMappingURL=canvas-display-styles.CGp2yZ9t.mjs.map
