import { m as music_vue_vue_type_style_index_0_scoped_6410fed0_lang } from './music-styles-1.mjs-DqjWzfxv.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import 'devalue';
import 'vue/server-renderer';
import '@unhead/ssr';
import 'unhead';
import 'vue';
import '@unhead/shared';

const musicStyles_XtlfD0Sl = [music_vue_vue_type_style_index_0_scoped_6410fed0_lang];

export { musicStyles_XtlfD0Sl as default };
//# sourceMappingURL=music-styles.XtlfD0Sl.mjs.map
