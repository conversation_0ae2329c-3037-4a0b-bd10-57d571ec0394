{"version": 3, "file": "role-sidebar-F3uWRt8C.mjs", "sources": ["../../../../.nuxt/dist/server/_nuxt/role-sidebar-F3uWRt8C.js"], "sourcesContent": null, "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,MAAM,8BAA8C,eAAA,CAAA;AAAA,EAClD,MAAQ,EAAA,OAAA;AAAA,EACR,iBAAmB,EAAA,IAAA;AAAA,EACnB,KAAO,EAAA;AAAA,IACL,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,OAAS,EAAA;AAAA,KACX;AAAA,IACA,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,MAAA;AAAA,MACN,OAAS,EAAA;AAAA;AACX,GACF;AAAA,EACA,MAAM,OAAS,EAAA;AACb,IAAM,MAAA,WAAA,GAAc,IAAI,IAAI,CAAA;AAC5B,IAAA,OAAO,CAAC,IAAA,EAAM,KAAO,EAAA,OAAA,EAAS,MAAW,KAAA;AACvC,MAAA,MAAM,eAAkB,GAAA,WAAA;AACxB,MAAM,KAAA,CAAA,CAAA,IAAA,EAAO,eAAe,MAAM,CAAC,mJAAmJ,cAAe,CAAA,OAAA,CAAQ,KAAK,CAAC,CAAqB,kBAAA,EAAA,cAAA,CAAe,CAAC,EAAE,YAAA,EAAc,CAAC,KAAM,CAAA,WAAW,GAAK,EAAA,8BAA8B,CAAC,CAAC,CAAoB,kBAAA,CAAA,CAAA;AACnV,MAAM,KAAA,CAAA,kBAAA,CAAmB,iBAAiB,EAAE,IAAA,EAAM,mBAAqB,EAAA,IAAA,EAAM,OAAO,CAAC,CAAA;AACrF,MAAA,KAAA,CAAM,CAAc,YAAA,CAAA,CAAA;AACpB,MAAI,IAAA,KAAA,CAAM,WAAW,CAAG,EAAA;AACtB,QAAM,KAAA,CAAA,CAAA,YAAA,EAAe,cAAe,CAAA,EAAE,YAAc,EAAA,OAAA,CAAQ,SAAS,GAAM,GAAA,IAAA,EAAM,CAAC,CAAyD,uDAAA,CAAA,CAAA;AAC3I,QAAA,aAAA,CAAc,KAAK,MAAQ,EAAA,MAAA,EAAQ,EAAI,EAAA,IAAA,EAAM,OAAO,OAAO,CAAA;AAC3D,QAAA,KAAA,CAAM,CAAQ,MAAA,CAAA,CAAA;AAAA,OACT,MAAA;AACL,QAAA,KAAA,CAAM,CAAS,OAAA,CAAA,CAAA;AAAA;AAEjB,MAAA,KAAA,CAAM,CAAQ,MAAA,CAAA,CAAA;AAAA,KAChB;AAAA;AAEJ,CAAC,CAAA;AACD,MAAM,eAAe,WAAY,CAAA,KAAA;AACjC,WAAY,CAAA,KAAA,GAAQ,CAAC,KAAA,EAAO,GAAQ,KAAA;AAClC,EAAA,MAAM,aAAa,aAAc,EAAA;AACjC,EAAC,CAAA,UAAA,CAAW,YAAY,UAAW,CAAA,OAAA,uBAA8B,GAAI,EAAA,CAAA,EAAI,IAAI,gCAAgC,CAAA;AAC7G,EAAA,OAAO,YAAe,GAAA,YAAA,CAAa,KAAO,EAAA,GAAG,CAAI,GAAA,KAAA,CAAA;AACnD,CAAA;AACA,MAAM,kBAAA,+BAAiD,WAAa,EAAA,CAAC,CAAC,WAAa,EAAA,iBAAiB,CAAC,CAAC,CAAA;AACtG,MAAM,4BAA4C,eAAA,CAAA;AAAA,EAChD,MAAQ,EAAA,cAAA;AAAA,EACR,iBAAmB,EAAA,IAAA;AAAA,EACnB,KAAO,EAAA;AAAA,IACL,WAAa,EAAA,EAAE,OAAS,EAAA,MAAM,EAAG,EAAA;AAAA,IACjC,SAAW,EAAA,EAAE,OAAS,EAAA,MAAM,CAAE,EAAA;AAAA,IAC9B,OAAA,EAAS,EAAE,OAAA,EAAS,EAAG;AAAA,GACzB;AAAA,EACA,KAAA,EAAO,CAAC,UAAA,EAAY,gBAAgB,CAAA;AAAA,EACpC,KAAM,CAAA,OAAA,EAAS,EAAE,IAAA,EAAM,QAAU,EAAA;AAC/B,IAAA,MAAM,IAAO,GAAA,MAAA;AACb,IAAA,MAAM,KAAQ,GAAA,OAAA;AACd,IAAA,MAAM,aAAgB,GAAA,SAAA,CAAU,KAAO,EAAA,SAAA,EAAW,IAAI,CAAA;AACtD,IAAA,MAAM,aAAa,GAAI,EAAA;AACvB,IAAA,OAAO,CAAC,IAAA,EAAM,KAAO,EAAA,OAAA,EAAS,MAAW,KAAA;AACvC,MAAA,MAAM,mBAAsB,GAAA,kBAAA;AAC5B,MAAA,MAAM,mBAAsB,GAAA,OAAA;AAC5B,MAAA,MAAM,mBAAsB,GAAA,kBAAA;AAC5B,MAAA,MAAM,kBAAqB,GAAA,OAAA;AAC3B,MAAM,KAAA,CAAA,CAAA,IAAA,EAAO,eAAe,UAAW,CAAA;AAAA,QACrC,KAAO,EAAA,6EAAA;AAAA,QACP,KAAA,EAAO,EAAE,OAAA,EAAS,0BAA2B;AAAA,OAC5C,EAAA,MAAM,CAAC,CAAC,CAAwI,sIAAA,CAAA,CAAA;AACnJ,MAAA,KAAA,CAAM,kBAAmB,CAAA,mBAAA,EAAqB,EAAE,EAAA,EAAI,kBAAoB,EAAA;AAAA,QACtE,SAAS,OAAQ,CAAA,CAAC,CAAG,EAAA,MAAA,EAAQ,UAAU,QAAa,KAAA;AAClD,UAAA,IAAI,MAAQ,EAAA;AACV,YAAO,MAAA,CAAA,CAAA,qCAAA,EAAwC,QAAQ,CAAa,+BAAA,CAAA,CAAA;AAAA,WAC/D,MAAA;AACL,YAAO,OAAA;AAAA,cACL,YAAY,KAAO,EAAA,EAAE,KAAO,EAAA,UAAA,IAAc,0BAAM;AAAA,aAClD;AAAA;AACF,SACD,CAAA;AAAA,QACD,CAAG,EAAA;AAAA,OACL,EAAG,OAAO,CAAC,CAAA;AACX,MAAA,KAAA,CAAM,CAA4K,8LAAA,CAAA,CAAA;AAClL,MAAA,KAAA,CAAM,mBAAmB,mBAAqB,EAAA;AAAA,QAC5C,KAAO,EAAA,mCAAA;AAAA,QACP,UAAA,EAAY,MAAM,aAAa,CAAA;AAAA,QAC/B,qBAAA,EAAuB,CAAC,MAAW,KAAA,KAAA,CAAM,aAAa,CAAI,GAAA,aAAA,CAAc,QAAQ,MAAS,GAAA,IAAA;AAAA,QACzF,aAAA,EAAe,MAAM,cAAc,CAAA;AAAA,QACnC,WAAa,EAAA;AAAA,OACf,EAAG,IAAM,EAAA,OAAO,CAAC,CAAA;AACjB,MAAA,KAAA,CAAM,CAAoD,kDAAA,CAAA,CAAA;AAC1D,MAAM,KAAA,CAAA,kBAAA,CAAmB,KAAM,CAAA,WAAW,CAAG,EAAA;AAAA,QAC3C,KAAO,EAAA,EAAA;AAAA,QACP,OAAS,EAAA,YAAA;AAAA,QACT,GAAK,EAAA;AAAA,OACJ,EAAA;AAAA,QACD,SAAS,OAAQ,CAAA,CAAC,CAAG,EAAA,MAAA,EAAQ,UAAU,QAAa,KAAA;AAClD,UAAA,IAAI,MAAQ,EAAA;AACV,YAAO,MAAA,CAAA,CAAA,gDAAA,EAAmD,QAAQ,CAAW,SAAA,CAAA,CAAA;AAC7E,YAAA,aAAA,CAAc,IAAK,CAAA,WAAA,EAAa,CAAC,IAAA,EAAM,KAAU,KAAA;AAC/C,cAAA,MAAA,CAAO,mBAAmB,mBAAqB,EAAA;AAAA,gBAC7C,OAAO,IAAK,CAAA,IAAA;AAAA,gBACZ,MAAA,EAAQ,KAAK,KAAM,CAAA,MAAA;AAAA,gBACnB,GAAK,EAAA;AAAA,eACJ,EAAA;AAAA,gBACD,MAAM,OAAQ,CAAA,CAAC,EAAI,EAAA,MAAA,EAAQ,UAAU,SAAc,KAAA;AACjD,kBAAA,IAAI,MAAQ,EAAA;AACV,oBAAA,MAAA,CAAO,CAAU,QAAA,CAAA,CAAA;AACjB,oBAAA,aAAA,CAAc,IAAK,CAAA,KAAA,EAAO,CAAC,KAAA,EAAO,MAAW,KAAA;AAC3C,sBAAO,MAAA,CAAA,CAAA,YAAA,EAAe,eAAe,CAAC;AAAA,wBACpC,aAAA,EAAe,IAAK,CAAA,SAAA,IAAa,KAAM,CAAA;AAAA,yBACtC,oEAAoE,CAAC,CAAC,CAAA,iBAAA,EAAoB,SAAS,CAAG,CAAA,CAAA,CAAA;AACzG,sBAAA,MAAA,CAAO,mBAAmB,kBAAoB,EAAA;AAAA,wBAC5C,KAAK,KAAM,CAAA,KAAA;AAAA,wBACX,KAAO,EAAA;AAAA,uBACN,EAAA,IAAA,EAAM,QAAU,EAAA,SAAS,CAAC,CAAA;AAC7B,sBAAA,MAAA,CAAO,2CAA2C,SAAS,CAAA,2DAAA,EAA8D,SAAS,CAAA,CAAA,EAAI,eAAe,KAAM,CAAA,IAAI,CAAC,CAAA,qFAAA,EAAwF,SAAS,CAAI,CAAA,EAAA,cAAA,CAAe,KAAM,CAAA,QAAQ,CAAC,CAAoB,kBAAA,CAAA,CAAA;AAAA,qBACxT,CAAA;AACD,oBAAA,MAAA,CAAO,CAAU,QAAA,CAAA,CAAA;AAAA,mBACZ,MAAA;AACL,oBAAO,OAAA;AAAA,uBACJ,SAAA,CAAU,IAAI,CAAA,EAAG,WAAY,CAAA,QAAA,EAAU,IAAM,EAAA,UAAA,CAAW,IAAK,CAAA,KAAA,EAAO,CAAC,KAAA,EAAO,MAAW,KAAA;AACtF,wBAAO,OAAA,SAAA,EAAa,EAAA,WAAA,CAAY,KAAO,EAAA;AAAA,0BACrC,KAAA,EAAO,CAAC,oEAAsE,EAAA;AAAA,4BAC5E,aAAA,EAAe,IAAK,CAAA,SAAA,IAAa,KAAM,CAAA;AAAA,2BACxC,CAAA;AAAA,0BACD,GAAK,EAAA,MAAA;AAAA,0BACL,OAAS,EAAA,CAAC,MAAW,KAAA,IAAA,CAAK,YAAY,KAAK;AAAA,yBAC1C,EAAA;AAAA,0BACD,YAAY,kBAAoB,EAAA;AAAA,4BAC9B,KAAK,KAAM,CAAA,KAAA;AAAA,4BACX,KAAO,EAAA;AAAA,2BACN,EAAA,IAAA,EAAM,CAAG,EAAA,CAAC,KAAK,CAAC,CAAA;AAAA,0BACnB,WAAY,CAAA,KAAA,EAAO,EAAE,KAAA,EAAO,eAAiB,EAAA;AAAA,4BAC3C,WAAA,CAAY,KAAO,EAAA,EAAE,KAAO,EAAA,+BAAA,IAAmC,eAAgB,CAAA,KAAA,CAAM,IAAI,CAAA,EAAG,CAAC,CAAA;AAAA,4BAC7F,WAAA,CAAY,KAAO,EAAA,EAAE,KAAO,EAAA,oDAAA,IAAwD,eAAgB,CAAA,KAAA,CAAM,QAAQ,CAAA,EAAG,CAAC;AAAA,2BACvH;AAAA,yBACA,EAAA,EAAA,EAAI,CAAC,SAAS,CAAC,CAAA;AAAA,uBACnB,GAAG,GAAG,CAAA;AAAA,qBACT;AAAA;AACF,iBACD,CAAA;AAAA,gBACD,CAAG,EAAA;AAAA,eACL,EAAG,QAAU,EAAA,QAAQ,CAAC,CAAA;AAAA,aACvB,CAAA;AACD,YAAA,MAAA,CAAO,CAAgB,cAAA,CAAA,CAAA;AAAA,WAClB,MAAA;AACL,YAAO,OAAA;AAAA,cACL,WAAY,CAAA,KAAA,EAAO,EAAE,KAAA,EAAO,uBAAyB,EAAA;AAAA,iBAClD,SAAA,CAAU,IAAI,CAAA,EAAG,WAAY,CAAA,QAAA,EAAU,IAAM,EAAA,UAAA,CAAW,IAAK,CAAA,WAAA,EAAa,CAAC,IAAA,EAAM,KAAU,KAAA;AAC1F,kBAAO,OAAA,SAAA,EAAa,EAAA,WAAA,CAAY,mBAAqB,EAAA;AAAA,oBACnD,OAAO,IAAK,CAAA,IAAA;AAAA,oBACZ,MAAA,EAAQ,KAAK,KAAM,CAAA,MAAA;AAAA,oBACnB,GAAK,EAAA;AAAA,mBACJ,EAAA;AAAA,oBACD,IAAA,EAAM,QAAQ,MAAM;AAAA,uBACjB,SAAA,CAAU,IAAI,CAAA,EAAG,WAAY,CAAA,QAAA,EAAU,IAAM,EAAA,UAAA,CAAW,IAAK,CAAA,KAAA,EAAO,CAAC,KAAA,EAAO,MAAW,KAAA;AACtF,wBAAO,OAAA,SAAA,EAAa,EAAA,WAAA,CAAY,KAAO,EAAA;AAAA,0BACrC,KAAA,EAAO,CAAC,oEAAsE,EAAA;AAAA,4BAC5E,aAAA,EAAe,IAAK,CAAA,SAAA,IAAa,KAAM,CAAA;AAAA,2BACxC,CAAA;AAAA,0BACD,GAAK,EAAA,MAAA;AAAA,0BACL,OAAS,EAAA,CAAC,MAAW,KAAA,IAAA,CAAK,YAAY,KAAK;AAAA,yBAC1C,EAAA;AAAA,0BACD,YAAY,kBAAoB,EAAA;AAAA,4BAC9B,KAAK,KAAM,CAAA,KAAA;AAAA,4BACX,KAAO,EAAA;AAAA,2BACN,EAAA,IAAA,EAAM,CAAG,EAAA,CAAC,KAAK,CAAC,CAAA;AAAA,0BACnB,WAAY,CAAA,KAAA,EAAO,EAAE,KAAA,EAAO,eAAiB,EAAA;AAAA,4BAC3C,WAAA,CAAY,KAAO,EAAA,EAAE,KAAO,EAAA,+BAAA,IAAmC,eAAgB,CAAA,KAAA,CAAM,IAAI,CAAA,EAAG,CAAC,CAAA;AAAA,4BAC7F,WAAA,CAAY,KAAO,EAAA,EAAE,KAAO,EAAA,oDAAA,IAAwD,eAAgB,CAAA,KAAA,CAAM,QAAQ,CAAA,EAAG,CAAC;AAAA,2BACvH;AAAA,yBACA,EAAA,EAAA,EAAI,CAAC,SAAS,CAAC,CAAA;AAAA,uBACnB,GAAG,GAAG,CAAA;AAAA,qBACR,CAAA;AAAA,oBACD,CAAG,EAAA;AAAA,mBACF,EAAA,IAAA,EAAM,CAAC,OAAA,EAAS,QAAQ,CAAC,CAAA;AAAA,iBAC7B,GAAG,GAAG,CAAA;AAAA,eACR;AAAA,aACH;AAAA;AACF,SACD,CAAA;AAAA,QACD,CAAG,EAAA;AAAA,OACL,EAAG,OAAO,CAAC,CAAA;AACX,MAAA,KAAA,CAAM,CAAc,YAAA,CAAA,CAAA;AAAA,KACtB;AAAA;AAEJ,CAAC,CAAA;AACD,MAAM,aAAa,SAAU,CAAA,KAAA;AAC7B,SAAU,CAAA,KAAA,GAAQ,CAAC,KAAA,EAAO,GAAQ,KAAA;AAChC,EAAA,MAAM,aAAa,aAAc,EAAA;AACjC,EAAC,CAAA,UAAA,CAAW,YAAY,UAAW,CAAA,OAAA,uBAA8B,GAAI,EAAA,CAAA,EAAI,IAAI,6CAA6C,CAAA;AAC1H,EAAA,OAAO,UAAa,GAAA,UAAA,CAAW,KAAO,EAAA,GAAG,CAAI,GAAA,KAAA,CAAA;AAC/C,CAAA;AACM,MAAA,WAAA,+BAA0C,SAAW,EAAA,CAAC,CAAC,WAAa,EAAA,iBAAiB,CAAC,CAAC;;;;"}