import { E as ElSelect, a as ElOption } from './index-CUhOTuS-.mjs';
import { useSSRContext, defineComponent, watch, unref, mergeProps, isRef, withCtx, openBlock, createBlock, Fragment, renderList } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderList } from 'vue/server-renderer';
import { useVModels } from '@vueuse/core';
import { f as formData, a as config } from './useDrawEffect-B2jxDCVi.mjs';
import _sfc_main$1 from './sidbar-item-title-aTPs1IEb.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './server.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';
import 'lodash-es';
import 'weixin-js-sdk';
import '@vue/shared';
import 'lodash-unified';
import '@ctrl/tinycolor';
import '@vue/reactivity';
import 'jsonc-parser';
import '@tanstack/vue-query';
import 'css-color-function';
import './index-L-VTEUEA.mjs';
import '@popperjs/core';
import './index-0xCxAaTZ.mjs';
import './index-D7S5lb8a.mjs';
import './strings-D1uxkXhq.mjs';
import './index-5Ia44xzE.mjs';
import './usePaging-DU8sXki3.mjs';
import './usePolling-DOP50YcO.mjs';
import './DrawEnum-CqAPEJOR.mjs';
import './index-CJqYHNUB.mjs';
import './dropdown-C6fgV-Vy.mjs';
import './index-BoqjHllR.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "mj-version",
  __ssrInlineRender: true,
  props: {
    modelValue: { default: "" }
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const { modelValue: currentVersion } = useVModels(props, emit);
    watch(() => {
      var _a;
      return [formData.value.draw_model, (_a = config.value) == null ? void 0 : _a.mj_version];
    }, ([v1, v2]) => {
      var _a, _b;
      if (!((_a = config.value) == null ? void 0 : _a.mj_version)) return;
      if (currentVersion.value !== v1) {
        currentVersion.value = (_b = config.value) == null ? void 0 : _b.mj_version[formData.value.draw_model][0];
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      const _component_el_select = ElSelect;
      const _component_el_option = ElOption;
      if ((_a = unref(config)) == null ? void 0 : _a.mj_version) {
        _push(`<div${ssrRenderAttrs(mergeProps({ class: "mj-version" }, _attrs))} data-v-4b0030cc>`);
        _push(ssrRenderComponent(_sfc_main$1, {
          title: "\u7248\u672C\u9009\u62E9",
          tips: "\u6307\u5B9Amidjourney\u7684\u6E32\u67D3\u7248\u672C"
        }, null, _parent));
        _push(`<div data-v-4b0030cc>`);
        _push(ssrRenderComponent(_component_el_select, {
          modelValue: unref(currentVersion),
          "onUpdate:modelValue": ($event) => isRef(currentVersion) ? currentVersion.value = $event : null,
          placeholder: "\u8BF7\u9009\u62E9\u7248\u672C",
          class: "w-full mt-[8px]",
          size: "large"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            var _a2, _b;
            if (_push2) {
              _push2(`<!--[-->`);
              ssrRenderList((_a2 = unref(config)) == null ? void 0 : _a2.mj_version[unref(formData).draw_model], (item, key) => {
                _push2(ssrRenderComponent(_component_el_option, {
                  key: item,
                  label: item,
                  value: item
                }, null, _parent2, _scopeId));
              });
              _push2(`<!--]-->`);
            } else {
              return [
                (openBlock(true), createBlock(Fragment, null, renderList((_b = unref(config)) == null ? void 0 : _b.mj_version[unref(formData).draw_model], (item, key) => {
                  return openBlock(), createBlock(_component_el_option, {
                    key: item,
                    label: item,
                    value: item
                  }, null, 8, ["label", "value"]);
                }), 128))
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/draw/components/mj/mj-version.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const MjVersion = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-4b0030cc"]]);

export { MjVersion as default };
//# sourceMappingURL=mj-version-CBEMCIGd.mjs.map
