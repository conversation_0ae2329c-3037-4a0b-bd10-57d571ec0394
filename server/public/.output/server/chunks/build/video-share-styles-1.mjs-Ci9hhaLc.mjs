const videoShare_vue_vue_type_style_index_0_scoped_0874c328_lang = ".share-popup[data-v-0874c328]  .el-dialog{border-radius:20px}.share-popup[data-v-0874c328]  .el-select__wrapper{box-shadow:none}.share-popup[data-v-0874c328]  .el-select__wrapper:hover{box-shadow:0 0 0 1px var(--el-border-color) inset}";

export { videoShare_vue_vue_type_style_index_0_scoped_0874c328_lang as v };
//# sourceMappingURL=video-share-styles-1.mjs-Ci9hhaLc.mjs.map
