import { v as video_vue_vue_type_style_index_0_scoped_26cc000b_lang } from './video-styles-1.mjs-Cqx41VvK.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import 'devalue';
import 'vue/server-renderer';
import '@unhead/ssr';
import 'unhead';
import 'vue';
import '@unhead/shared';

const videoStyles_BDY8Kq4M = [video_vue_vue_type_style_index_0_scoped_26cc000b_lang, video_vue_vue_type_style_index_0_scoped_26cc000b_lang];

export { videoStyles_BDY8Kq4M as default };
//# sourceMappingURL=video-styles.BDY8Kq4M.mjs.map
