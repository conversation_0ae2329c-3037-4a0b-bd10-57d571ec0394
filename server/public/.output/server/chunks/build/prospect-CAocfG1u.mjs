import { E as ElTabs, a as ElTabPane } from './el-tab-pane-C7DQ8faq.mjs';
import { E as ElScrollbar } from './index-0xCxAaTZ.mjs';
import { E as ElImage } from './index-C2yEelJa.mjs';
import { E as ElEmpty } from './el-empty-xbPr04pX.mjs';
import { bk as getProspectsList } from './server.mjs';
import { defineComponent, reactive, withAsyncContext, mergeProps, unref, withCtx, openBlock, createBlock, Fragment, renderList, createVNode, useSSRContext } from 'vue';
import { u as useAsyncData } from './asyncData-BagoRZi2.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderList } from 'vue/server-renderer';
import { u as useCanvasStore, I as ImageTypes } from './canvas-DJ4hjlD7.mjs';
import { e as emptyImg } from './empty_con-BDdV71_z.mjs';
import '@vueuse/core';
import './strings-D1uxkXhq.mjs';
import '@vue/shared';
import './index-C5I0EtSx.mjs';
import 'lodash-unified';
import './position-DVxxNIGX.mjs';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';
import 'lodash-es';
import 'weixin-js-sdk';
import '@ctrl/tinycolor';
import '@vue/reactivity';
import 'jsonc-parser';
import '@tanstack/vue-query';
import 'css-color-function';
import './useAudioPlay-C6V9947w.mjs';
import './file-RP6bCPT_.mjs';
import 'jsdom';
import 'jsdom/lib/jsdom/living/generated/utils';
import 'jsdom/lib/jsdom/utils';
import 'fontfaceobserver';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "prospect",
  __ssrInlineRender: true,
  async setup(__props) {
    let __temp, __restore;
    const canvasStore = useCanvasStore();
    const tabList = [
      {
        type: 1,
        label: "\u7CFB\u7EDF\u524D\u666F"
      }
    ];
    const state = reactive({
      type: 1
    });
    const { data: lists } = ([__temp, __restore] = withAsyncContext(() => useAsyncData(() => getProspectsList(), {
      lazy: true
    }, "$MMRLoo0MmU")), __temp = await __temp, __restore(), __temp);
    const insertMaps = (item) => {
      canvasStore.addImage(item.url, ImageTypes.PROSPECT, item);
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_el_tabs = ElTabs;
      const _component_el_tab_pane = ElTabPane;
      const _component_ElScrollbar = ElScrollbar;
      const _component_ElImage = ElImage;
      const _component_ElEmpty = ElEmpty;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "h-full flex flex-col" }, _attrs))}><div class="mt-[5px] px-main">`);
      _push(ssrRenderComponent(_component_el_tabs, {
        modelValue: unref(state).type,
        "onUpdate:modelValue": ($event) => unref(state).type = $event
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<!--[-->`);
            ssrRenderList(tabList, (item) => {
              _push2(ssrRenderComponent(_component_el_tab_pane, {
                key: item.type,
                label: item.label,
                name: item.type
              }, null, _parent2, _scopeId));
            });
            _push2(`<!--]-->`);
          } else {
            return [
              (openBlock(), createBlock(Fragment, null, renderList(tabList, (item) => {
                return createVNode(_component_el_tab_pane, {
                  key: item.type,
                  label: item.label,
                  name: item.type
                }, null, 8, ["label", "name"]);
              }), 64))
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="flex-1 min-h-0">`);
      _push(ssrRenderComponent(_component_ElScrollbar, null, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="p-main pt-0"${_scopeId}>`);
            if (unref(lists).length) {
              _push2(`<div class="flex flex-wrap mx-[-7px]"${_scopeId}><!--[-->`);
              ssrRenderList(unref(lists), (item) => {
                _push2(`<div class="w-[50%]"${_scopeId}><div class="px-[7px] mb-[14px]"${_scopeId}><div class="border border-solid border-br-light rounded-md cursor-pointer p-[10px]"${_scopeId}><div${_scopeId}><div class="pic-wrap h-0 pt-[100%] relative"${_scopeId}><div class="absolute inset-0"${_scopeId}>`);
                _push2(ssrRenderComponent(_component_ElImage, {
                  src: item.url,
                  class: "w-full h-full",
                  fit: "contain",
                  lazy: ""
                }, null, _parent2, _scopeId));
                _push2(`</div></div></div></div></div></div>`);
              });
              _push2(`<!--]--></div>`);
            } else {
              _push2(ssrRenderComponent(_component_ElEmpty, {
                image: unref(emptyImg),
                description: "\u6682\u65E0\u6570\u636E\uFF5E"
              }, null, _parent2, _scopeId));
            }
            _push2(`</div>`);
          } else {
            return [
              createVNode("div", { class: "p-main pt-0" }, [
                unref(lists).length ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "flex flex-wrap mx-[-7px]"
                }, [
                  (openBlock(true), createBlock(Fragment, null, renderList(unref(lists), (item) => {
                    return openBlock(), createBlock("div", {
                      key: item.id,
                      class: "w-[50%]"
                    }, [
                      createVNode("div", {
                        class: "px-[7px] mb-[14px]",
                        onClick: ($event) => insertMaps(item)
                      }, [
                        createVNode("div", { class: "border border-solid border-br-light rounded-md cursor-pointer p-[10px]" }, [
                          createVNode("div", null, [
                            createVNode("div", { class: "pic-wrap h-0 pt-[100%] relative" }, [
                              createVNode("div", { class: "absolute inset-0" }, [
                                createVNode(_component_ElImage, {
                                  src: item.url,
                                  class: "w-full h-full",
                                  fit: "contain",
                                  lazy: ""
                                }, null, 8, ["src"])
                              ])
                            ])
                          ])
                        ])
                      ], 8, ["onClick"])
                    ]);
                  }), 128))
                ])) : (openBlock(), createBlock(_component_ElEmpty, {
                  key: 1,
                  image: unref(emptyImg),
                  description: "\u6682\u65E0\u6570\u636E\uFF5E"
                }, null, 8, ["image"]))
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/digital_human/_components/design-left/prospect.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=prospect-CAocfG1u.mjs.map
