var ModelEnums = /* @__PURE__ */ ((ModelEnums2) => {
  ModelEnums2["BASE"] = "search";
  ModelEnums2["ENHANCE"] = "copilot";
  ModelEnums2["STUDY"] = "research";
  return ModelEnums2;
})(ModelEnums || {});
var TypeEnums = /* @__PURE__ */ ((TypeEnums2) => {
  TypeEnums2["ALL"] = "all";
  TypeEnums2["DOC"] = "doc";
  TypeEnums2["SCHOLAR"] = "scholar";
  return TypeEnums2;
})(TypeEnums || {});
var StatusEnums = /* @__PURE__ */ ((StatusEnums2) => {
  StatusEnums2[StatusEnums2["ANALYSIS"] = 0] = "ANALYSIS";
  StatusEnums2[StatusEnums2["SEARCH"] = 1] = "SEARCH";
  StatusEnums2[StatusEnums2["SUMMARY"] = 2] = "SUMMARY";
  StatusEnums2[StatusEnums2["SUCCESS"] = 3] = "SUCCESS";
  return StatusEnums2;
})(StatusEnums || {});

export { ModelEnums, StatusEnums, TypeEnums };
//# sourceMappingURL=searchEnums-Dgcx5RT8.mjs.map
