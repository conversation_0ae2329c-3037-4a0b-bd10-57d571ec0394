{"version": 3, "file": "useDictOptions-DmOxg3R0.mjs", "sources": ["../../../../.nuxt/dist/server/_nuxt/useDictOptions-DmOxg3R0.js"], "sourcesContent": null, "names": [], "mappings": ";;AACA,SAAS,eAAe,OAAS,EAAA;AAC/B,EAAM,MAAA,WAAA,GAAc,QAAS,CAAA,EAAE,CAAA;AAC/B,EAAM,MAAA,UAAA,GAAa,MAAO,CAAA,IAAA,CAAK,OAAO,CAAA;AACtC,EAAA,MAAM,QAAW,GAAA,UAAA,CAAW,GAAI,CAAA,CAAC,GAAQ,KAAA;AACvC,IAAM,MAAA,KAAA,GAAQ,QAAQ,GAAG,CAAA;AACzB,IAAY,WAAA,CAAA,GAAG,IAAI,EAAC;AACpB,IAAO,OAAA,MAAM,MAAM,GAAI,CAAA,KAAA,CAAM,MAAM,MAAM,CAAA,IAAK,EAAE,CAAA;AAAA,GACjD,CAAA;AACD,EAAA,MAAM,UAAU,YAAY;AAC1B,IAAM,MAAA,GAAA,GAAM,MAAM,OAAQ,CAAA,UAAA;AAAA,MACxB,QAAS,CAAA,GAAA,CAAI,CAAC,GAAA,KAAQ,KAAK;AAAA,KAC7B;AACA,IAAI,GAAA,CAAA,OAAA,CAAQ,CAAC,IAAA,EAAM,KAAU,KAAA;AAC3B,MAAM,MAAA,GAAA,GAAM,WAAW,KAAK,CAAA;AAC5B,MAAI,IAAA,IAAA,CAAK,UAAU,WAAa,EAAA;AAC9B,QAAA,MAAM,EAAE,aAAA,EAAkB,GAAA,OAAA,CAAQ,GAAG,CAAA;AACrC,QAAA,MAAM,OAAO,aAAgB,GAAA,aAAA,CAAc,IAAK,CAAA,KAAK,IAAI,IAAK,CAAA,KAAA;AAC9D,QAAA,WAAA,CAAY,GAAG,CAAI,GAAA,IAAA;AAAA;AACrB,KACD,CAAA;AAAA,GACH;AACA,EAAQ,OAAA,EAAA;AACR,EAAO,OAAA;AAAA,IACL,WAAA;AAAA,IACA;AAAA,GACF;AACF;;;;"}