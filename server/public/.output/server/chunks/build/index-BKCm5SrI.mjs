import { _ as __vite_glob_0_0 } from './entrance-D67OpvrX.mjs';
import { _ as __vite_glob_0_1 } from './guide-CT2_xTNK.mjs';
import { _ as __vite_glob_0_2 } from './header-BSZGZ8i5.mjs';
import { _ as __vite_glob_0_3 } from './intro-BJNU7tT2.mjs';
import { _ as __vite_glob_0_4 } from './title-5oqj6kGf.mjs';
import './index-DLVgZG5d.mjs';
import './nuxt-link-l5zPv3vf.mjs';
import 'vue';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import './server.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import 'vue/server-renderer';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';
import 'lodash-es';
import '@vueuse/core';
import 'weixin-js-sdk';
import '@vue/shared';
import 'lodash-unified';
import '@ctrl/tinycolor';
import '@vue/reactivity';
import 'jsonc-parser';
import '@tanstack/vue-query';
import 'css-color-function';
import './index-C2yEelJa.mjs';
import './position-DVxxNIGX.mjs';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import './title-logo-BNM0flCB.mjs';
import './menu-C91s3odr.mjs';
import './el-menu-item-DBjUF0xW.mjs';
import './index-DadLUs6d.mjs';
import './index-L-VTEUEA.mjs';
import '@popperjs/core';
import './index-5Ia44xzE.mjs';
import './menu-item-DyOqt2KJ.mjs';
import './user-SBxKtT5H.mjs';
import './index-BoqjHllR.mjs';
import './member-btn-MuRMgKHK.mjs';
import './index_arrow-right02-CtbdAQ0b.mjs';

const widgets = /* @__PURE__ */ Object.assign({
  "./entrance.vue": __vite_glob_0_0,
  "./guide.vue": __vite_glob_0_1,
  "./header.vue": __vite_glob_0_2,
  "./intro.vue": __vite_glob_0_3,
  "./title.vue": __vite_glob_0_4
});
const exportWidgets = {};
Object.keys(widgets).forEach((key) => {
  var _a;
  const widgetName = key.replace(/^\.\/([\w-]+).*/gi, "$1");
  exportWidgets[widgetName] = (_a = widgets[key]) == null ? void 0 : _a.default;
});

export { exportWidgets as default };
//# sourceMappingURL=index-BKCm5SrI.mjs.map
