const drawShare_vue_vue_type_style_index_0_scoped_33ac0b85_lang = ".share-popup[data-v-33ac0b85]  .el-dialog{border-radius:20px}.share-popup[data-v-33ac0b85]  .el-select__wrapper{box-shadow:none}.share-popup[data-v-33ac0b85]  .el-select__wrapper:hover{box-shadow:0 0 0 1px var(--el-border-color) inset}";

export { drawShare_vue_vue_type_style_index_0_scoped_33ac0b85_lang as d };
//# sourceMappingURL=draw-share-styles-1.mjs-JRdjrj9h.mjs.map
