const user_vue_vue_type_style_index_0_lang = "::view-transition-new(root),::view-transition-old(root){animation:none;mix-blend-mode:normal}.dark::view-transition-old(root){z-index:1}.dark::view-transition-new(root){z-index:999}::view-transition-old(root){z-index:999}::view-transition-new(root){z-index:1}";

export { user_vue_vue_type_style_index_0_lang as u };
//# sourceMappingURL=user-styles-1.mjs-BMcRwItk.mjs.map
