import { P as Popup } from './index-BKj4TrcW.mjs';
import { E as ElForm, a as ElFormItem } from './index-DLL0sEcv.mjs';
import { z as useUserStore, bC as smsSend, bD as SMSEnum, bF as sendEmailCode, bG as forgotPassword, E as ElInput } from './server.mjs';
import { E as ElSelect, a as ElOption } from './index-CUhOTuS-.mjs';
import { _ as __nuxt_component_0 } from './index-6v4EX2UV.mjs';
import { defineComponent, shallowRef, reactive, watch, mergeProps, unref, withCtx, createVNode, openBlock, createBlock, createCommentVNode, useSSRContext } from 'vue';
import { u as useLockFn } from './useLockFn-BWbjkhBs.mjs';
import { ssrRenderComponent } from 'vue/server-renderer';
import './index-CzJm6kkT.mjs';
import './use-dialog-DHq_GjFf.mjs';
import '@vueuse/core';
import 'lodash-unified';
import './refs-CJvnaIJj.mjs';
import '@vue/shared';
import './_plugin-vue_export-helper-1tPrXgE0.mjs';
import 'async-validator';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';
import 'lodash-es';
import 'weixin-js-sdk';
import '@ctrl/tinycolor';
import '@vue/reactivity';
import 'jsonc-parser';
import '@tanstack/vue-query';
import 'css-color-function';
import './index-L-VTEUEA.mjs';
import '@popperjs/core';
import './index-0xCxAaTZ.mjs';
import './index-D7S5lb8a.mjs';
import './strings-D1uxkXhq.mjs';
import './index-5Ia44xzE.mjs';
import '@chenfengyuan/vue-countdown';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "changePwdPop",
  __ssrInlineRender: true,
  props: {
    mobile: {},
    email: {}
  },
  emits: ["close"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const popRef = shallowRef();
    useUserStore();
    useUserStore();
    const formRef = shallowRef();
    const verificationCodeRef = shallowRef();
    const formRules = {
      mobile: [
        {
          required: true,
          message: "\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801"
        }
      ],
      email: [
        {
          required: true,
          message: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u8D26\u53F7"
        },
        { type: "email", message: "\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u8D26\u53F7" }
      ],
      code: [
        {
          required: true,
          message: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801",
          trigger: ["change", "blur"]
        }
      ],
      password: [
        {
          required: true,
          message: "\u8BF7\u8F93\u51656-20\u4F4D\u6570\u5B57+\u5B57\u6BCD\u6216\u7B26\u53F7\u7EC4\u5408",
          trigger: ["change", "blur"]
        },
        {
          min: 6,
          max: 20,
          message: "\u5BC6\u7801\u957F\u5EA6\u5E94\u4E3A6-20",
          trigger: ["change", "blur"]
        }
      ],
      password_confirm: [
        {
          validator(rule, value, callback) {
            if (value === "") {
              callback(new Error("\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801"));
            } else if (value !== formData.password) {
              callback(new Error("\u4E24\u6B21\u8F93\u5165\u7684\u5BC6\u7801\u4E0D\u4E00\u81F4"));
            } else {
              callback();
            }
          },
          trigger: ["change", "blur"]
        }
      ]
    };
    const formData = reactive({
      email: "",
      mobile: "",
      password: "",
      code: "",
      password_confirm: ""
    });
    watch(() => props.mobile, (val) => {
      if (val) {
        formData.mobile = val;
      }
    }, { immediate: true });
    watch(() => props.email, (val) => {
      if (val) {
        formData.email = val;
      }
    }, { immediate: true });
    const sendCode = async () => {
      var _a;
      formData.mobile ? await sendSms() : await sendEmail();
      (_a = verificationCodeRef.value) == null ? void 0 : _a.start();
    };
    const sendSms = async () => {
      var _a;
      await ((_a = formRef.value) == null ? void 0 : _a.validateField(["mobile"]));
      await smsSend({
        scene: SMSEnum.FIND_PASSWORD,
        mobile: formData.mobile
      });
    };
    const sendEmail = async () => {
      var _a;
      await ((_a = formRef.value) == null ? void 0 : _a.validateField(["email"]));
      await sendEmailCode({
        scene: SMSEnum.FIND_PASSWORD,
        email: formData.email
      });
    };
    const handleConfirm = async () => {
      var _a;
      await ((_a = formRef.value) == null ? void 0 : _a.validate());
      await forgotPassword({
        ...formData,
        scene: formData.mobile ? 1 : 2
      });
      popRef.value.close();
    };
    const { lockFn: handleConfirmLock, isLock } = useLockFn(handleConfirm);
    const open = () => {
      popRef.value.open();
    };
    __expose({ open });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_Popup = Popup;
      const _component_ElForm = ElForm;
      const _component_ElFormItem = ElFormItem;
      const _component_ElInput = ElInput;
      const _component_ElSelect = ElSelect;
      const _component_ElOption = ElOption;
      const _component_VerificationCode = __nuxt_component_0;
      _push(ssrRenderComponent(_component_Popup, mergeProps({
        ref_key: "popRef",
        ref: popRef,
        title: "\u8BBE\u7F6E\u767B\u5F55\u5BC6\u7801",
        async: true,
        "confirm-button-text": "\u786E\u8BA4",
        onConfirm: unref(handleConfirmLock),
        onClose: ($event) => _ctx.$emit("close")
      }, _attrs), {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div${_scopeId}>`);
            _push2(ssrRenderComponent(_component_ElForm, {
              ref_key: "formRef",
              ref: formRef,
              size: "large",
              model: unref(formData),
              rules: formRules
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  if (unref(formData).mobile) {
                    _push3(ssrRenderComponent(_component_ElFormItem, { prop: "mobile" }, {
                      default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                        if (_push4) {
                          _push4(ssrRenderComponent(_component_ElInput, {
                            modelValue: unref(formData).mobile,
                            "onUpdate:modelValue": ($event) => unref(formData).mobile = $event,
                            placeholder: "\u8BF7\u8F93\u5165\u624B\u673A\u53F7",
                            disabled: ""
                          }, {
                            prepend: withCtx((_4, _push5, _parent5, _scopeId4) => {
                              if (_push5) {
                                _push5(ssrRenderComponent(_component_ElSelect, {
                                  "model-value": "+86",
                                  style: { "width": "80px" },
                                  disabled: ""
                                }, {
                                  default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                                    if (_push6) {
                                      _push6(ssrRenderComponent(_component_ElOption, {
                                        label: "+86",
                                        value: "+86"
                                      }, null, _parent6, _scopeId5));
                                    } else {
                                      return [
                                        createVNode(_component_ElOption, {
                                          label: "+86",
                                          value: "+86"
                                        })
                                      ];
                                    }
                                  }),
                                  _: 1
                                }, _parent5, _scopeId4));
                              } else {
                                return [
                                  createVNode(_component_ElSelect, {
                                    "model-value": "+86",
                                    style: { "width": "80px" },
                                    disabled: ""
                                  }, {
                                    default: withCtx(() => [
                                      createVNode(_component_ElOption, {
                                        label: "+86",
                                        value: "+86"
                                      })
                                    ]),
                                    _: 1
                                  })
                                ];
                              }
                            }),
                            _: 1
                          }, _parent4, _scopeId3));
                        } else {
                          return [
                            createVNode(_component_ElInput, {
                              modelValue: unref(formData).mobile,
                              "onUpdate:modelValue": ($event) => unref(formData).mobile = $event,
                              placeholder: "\u8BF7\u8F93\u5165\u624B\u673A\u53F7",
                              disabled: ""
                            }, {
                              prepend: withCtx(() => [
                                createVNode(_component_ElSelect, {
                                  "model-value": "+86",
                                  style: { "width": "80px" },
                                  disabled: ""
                                }, {
                                  default: withCtx(() => [
                                    createVNode(_component_ElOption, {
                                      label: "+86",
                                      value: "+86"
                                    })
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }, 8, ["modelValue", "onUpdate:modelValue"])
                          ];
                        }
                      }),
                      _: 1
                    }, _parent3, _scopeId2));
                  } else {
                    _push3(`<!---->`);
                  }
                  if (unref(formData).email) {
                    _push3(ssrRenderComponent(_component_ElFormItem, {
                      prop: "email",
                      disabled: ""
                    }, {
                      default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                        if (_push4) {
                          _push4(ssrRenderComponent(_component_ElInput, {
                            modelValue: unref(formData).email,
                            "onUpdate:modelValue": ($event) => unref(formData).email = $event,
                            placeholder: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u8D26\u53F7"
                          }, null, _parent4, _scopeId3));
                        } else {
                          return [
                            createVNode(_component_ElInput, {
                              modelValue: unref(formData).email,
                              "onUpdate:modelValue": ($event) => unref(formData).email = $event,
                              placeholder: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u8D26\u53F7"
                            }, null, 8, ["modelValue", "onUpdate:modelValue"])
                          ];
                        }
                      }),
                      _: 1
                    }, _parent3, _scopeId2));
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(ssrRenderComponent(_component_ElFormItem, { prop: "code" }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent(_component_ElInput, {
                          modelValue: unref(formData).code,
                          "onUpdate:modelValue": ($event) => unref(formData).code = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801"
                        }, {
                          suffix: withCtx((_4, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(`<div class="flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"${_scopeId4}>`);
                              _push5(ssrRenderComponent(_component_VerificationCode, {
                                ref_key: "verificationCodeRef",
                                ref: verificationCodeRef,
                                onClickGet: sendCode
                              }, null, _parent5, _scopeId4));
                              _push5(`</div>`);
                            } else {
                              return [
                                createVNode("div", { class: "flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br" }, [
                                  createVNode(_component_VerificationCode, {
                                    ref_key: "verificationCodeRef",
                                    ref: verificationCodeRef,
                                    onClickGet: sendCode
                                  }, null, 512)
                                ])
                              ];
                            }
                          }),
                          _: 1
                        }, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode(_component_ElInput, {
                            modelValue: unref(formData).code,
                            "onUpdate:modelValue": ($event) => unref(formData).code = $event,
                            placeholder: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801"
                          }, {
                            suffix: withCtx(() => [
                              createVNode("div", { class: "flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br" }, [
                                createVNode(_component_VerificationCode, {
                                  ref_key: "verificationCodeRef",
                                  ref: verificationCodeRef,
                                  onClickGet: sendCode
                                }, null, 512)
                              ])
                            ]),
                            _: 1
                          }, 8, ["modelValue", "onUpdate:modelValue"])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent(_component_ElFormItem, { prop: "password" }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent(_component_ElInput, {
                          modelValue: unref(formData).password,
                          "onUpdate:modelValue": ($event) => unref(formData).password = $event,
                          placeholder: "\u8BF7\u8F93\u51656-20\u4F4D\u6570\u5B57+\u5B57\u6BCD\u6216\u7B26\u53F7\u7EC4\u5408",
                          type: "password",
                          "show-password": ""
                        }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode(_component_ElInput, {
                            modelValue: unref(formData).password,
                            "onUpdate:modelValue": ($event) => unref(formData).password = $event,
                            placeholder: "\u8BF7\u8F93\u51656-20\u4F4D\u6570\u5B57+\u5B57\u6BCD\u6216\u7B26\u53F7\u7EC4\u5408",
                            type: "password",
                            "show-password": ""
                          }, null, 8, ["modelValue", "onUpdate:modelValue"])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent(_component_ElFormItem, { prop: "password_confirm" }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent(_component_ElInput, {
                          modelValue: unref(formData).password_confirm,
                          "onUpdate:modelValue": ($event) => unref(formData).password_confirm = $event,
                          placeholder: "\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801",
                          type: "password",
                          "show-password": ""
                        }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode(_component_ElInput, {
                            modelValue: unref(formData).password_confirm,
                            "onUpdate:modelValue": ($event) => unref(formData).password_confirm = $event,
                            placeholder: "\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801",
                            type: "password",
                            "show-password": ""
                          }, null, 8, ["modelValue", "onUpdate:modelValue"])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                } else {
                  return [
                    unref(formData).mobile ? (openBlock(), createBlock(_component_ElFormItem, {
                      key: 0,
                      prop: "mobile"
                    }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).mobile,
                          "onUpdate:modelValue": ($event) => unref(formData).mobile = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u624B\u673A\u53F7",
                          disabled: ""
                        }, {
                          prepend: withCtx(() => [
                            createVNode(_component_ElSelect, {
                              "model-value": "+86",
                              style: { "width": "80px" },
                              disabled: ""
                            }, {
                              default: withCtx(() => [
                                createVNode(_component_ElOption, {
                                  label: "+86",
                                  value: "+86"
                                })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        }, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    })) : createCommentVNode("", true),
                    unref(formData).email ? (openBlock(), createBlock(_component_ElFormItem, {
                      key: 1,
                      prop: "email",
                      disabled: ""
                    }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).email,
                          "onUpdate:modelValue": ($event) => unref(formData).email = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u8D26\u53F7"
                        }, null, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    })) : createCommentVNode("", true),
                    createVNode(_component_ElFormItem, { prop: "code" }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).code,
                          "onUpdate:modelValue": ($event) => unref(formData).code = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801"
                        }, {
                          suffix: withCtx(() => [
                            createVNode("div", { class: "flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br" }, [
                              createVNode(_component_VerificationCode, {
                                ref_key: "verificationCodeRef",
                                ref: verificationCodeRef,
                                onClickGet: sendCode
                              }, null, 512)
                            ])
                          ]),
                          _: 1
                        }, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    }),
                    createVNode(_component_ElFormItem, { prop: "password" }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).password,
                          "onUpdate:modelValue": ($event) => unref(formData).password = $event,
                          placeholder: "\u8BF7\u8F93\u51656-20\u4F4D\u6570\u5B57+\u5B57\u6BCD\u6216\u7B26\u53F7\u7EC4\u5408",
                          type: "password",
                          "show-password": ""
                        }, null, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    }),
                    createVNode(_component_ElFormItem, { prop: "password_confirm" }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).password_confirm,
                          "onUpdate:modelValue": ($event) => unref(formData).password_confirm = $event,
                          placeholder: "\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801",
                          type: "password",
                          "show-password": ""
                        }, null, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    })
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            return [
              createVNode("div", null, [
                createVNode(_component_ElForm, {
                  ref_key: "formRef",
                  ref: formRef,
                  size: "large",
                  model: unref(formData),
                  rules: formRules
                }, {
                  default: withCtx(() => [
                    unref(formData).mobile ? (openBlock(), createBlock(_component_ElFormItem, {
                      key: 0,
                      prop: "mobile"
                    }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).mobile,
                          "onUpdate:modelValue": ($event) => unref(formData).mobile = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u624B\u673A\u53F7",
                          disabled: ""
                        }, {
                          prepend: withCtx(() => [
                            createVNode(_component_ElSelect, {
                              "model-value": "+86",
                              style: { "width": "80px" },
                              disabled: ""
                            }, {
                              default: withCtx(() => [
                                createVNode(_component_ElOption, {
                                  label: "+86",
                                  value: "+86"
                                })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        }, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    })) : createCommentVNode("", true),
                    unref(formData).email ? (openBlock(), createBlock(_component_ElFormItem, {
                      key: 1,
                      prop: "email",
                      disabled: ""
                    }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).email,
                          "onUpdate:modelValue": ($event) => unref(formData).email = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u8D26\u53F7"
                        }, null, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    })) : createCommentVNode("", true),
                    createVNode(_component_ElFormItem, { prop: "code" }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).code,
                          "onUpdate:modelValue": ($event) => unref(formData).code = $event,
                          placeholder: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801"
                        }, {
                          suffix: withCtx(() => [
                            createVNode("div", { class: "flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br" }, [
                              createVNode(_component_VerificationCode, {
                                ref_key: "verificationCodeRef",
                                ref: verificationCodeRef,
                                onClickGet: sendCode
                              }, null, 512)
                            ])
                          ]),
                          _: 1
                        }, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    }),
                    createVNode(_component_ElFormItem, { prop: "password" }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).password,
                          "onUpdate:modelValue": ($event) => unref(formData).password = $event,
                          placeholder: "\u8BF7\u8F93\u51656-20\u4F4D\u6570\u5B57+\u5B57\u6BCD\u6216\u7B26\u53F7\u7EC4\u5408",
                          type: "password",
                          "show-password": ""
                        }, null, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    }),
                    createVNode(_component_ElFormItem, { prop: "password_confirm" }, {
                      default: withCtx(() => [
                        createVNode(_component_ElInput, {
                          modelValue: unref(formData).password_confirm,
                          "onUpdate:modelValue": ($event) => unref(formData).password_confirm = $event,
                          placeholder: "\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801",
                          type: "password",
                          "show-password": ""
                        }, null, 8, ["modelValue", "onUpdate:modelValue"])
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }, 8, ["model"])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/user/index/_components/changePwdPop.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=changePwdPop-CA-FyOAp.mjs.map
