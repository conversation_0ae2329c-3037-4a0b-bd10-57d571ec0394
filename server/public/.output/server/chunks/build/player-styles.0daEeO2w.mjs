import { p as player_vue_vue_type_style_index_0_scoped_2b59b763_lang } from './player-styles-1.mjs-CQMAImer.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../nitro/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:fs';
import 'node:path';
import 'node:url';
import 'devalue';
import 'vue/server-renderer';
import '@unhead/ssr';
import 'unhead';
import 'vue';
import '@unhead/shared';

const playerStyles_0daEeO2w = [player_vue_vue_type_style_index_0_scoped_2b59b763_lang, player_vue_vue_type_style_index_0_scoped_2b59b763_lang];

export { playerStyles_0daEeO2w as default };
//# sourceMappingURL=player-styles.0daEeO2w.mjs.map
