{"version": 3, "file": "index-DVLwoLV9.mjs", "sources": ["../../../../node_modules/.pnpm/@videojs-player+vue@1.0.0_@types+video.js@7.3.58_video.js@7.21.6_vue@3.5.13_typescript@4.9.3_/node_modules/@videojs-player/vue/dist/videojs-player.esm.js", "../../../../.nuxt/dist/server/_nuxt/index-DVLwoLV9.js"], "sourcesContent": null, "names": ["e", "n", "t", "r", "o", "a", "i", "u", "c", "l", "s", "videojs", "VideoPlayer"], "mappings": "", "x_google_ignoreList": [0]}