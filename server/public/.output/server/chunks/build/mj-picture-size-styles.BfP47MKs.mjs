import { m as mjPictureSize_vue_vue_type_style_index_0_scoped_7588a305_lang } from './mj-picture-size-styles-1.mjs-ftl_OJ31.mjs';

const mjPictureSizeStyles_BfP47MKs = [mjPictureSize_vue_vue_type_style_index_0_scoped_7588a305_lang, mjPictureSize_vue_vue_type_style_index_0_scoped_7588a305_lang];

export { mjPictureSizeStyles_BfP47MKs as default };
//# sourceMappingURL=mj-picture-size-styles.BfP47MKs.mjs.map
