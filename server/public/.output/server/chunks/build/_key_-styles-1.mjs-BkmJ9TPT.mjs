import { a as buildAssetsURL } from '../routes/renderer.mjs';

const _key__vue_vue_type_style_index_0_scoped_b030a425_lang = ".layout-bg[data-v-b030a425]{background:url(" + buildAssetsURL("layout_bg.CQiN3ao1.png") + ") no-repeat;background-position:50%;background-size:cover}#digital-canvas[data-v-b030a425]{height:100%;-o-object-fit:cover;object-fit:cover;position:absolute;width:100%;z-index:8}.gradient-button[data-v-b030a425]{align-items:center;background:linear-gradient(90deg,#70c3ec,#4a92ff);border-radius:50%;box-shadow:0 3.11px 7.78px 0 rgba(0,0,0,.2);color:var(--color-white);cursor:pointer;display:flex;height:48px;justify-content:center;opacity:1;width:48px}.gradient-button--small[data-v-b030a425]{bottom:200px;height:40px;position:absolute;right:10px;width:40px}.recorder[data-v-b030a425]{height:90px;width:90px}.recorder--small[data-v-b030a425]{bottom:100px;height:60px;position:absolute;right:10px;width:60px}.recorder--stop[data-v-b030a425]{background:#b7b7b7;border:1px solid #fff}.feedback-pop[data-v-b030a425] .el-dialog__body{padding:0!important}";

export { _key__vue_vue_type_style_index_0_scoped_b030a425_lang as _ };
//# sourceMappingURL=_key_-styles-1.mjs-BkmJ9TPT.mjs.map
