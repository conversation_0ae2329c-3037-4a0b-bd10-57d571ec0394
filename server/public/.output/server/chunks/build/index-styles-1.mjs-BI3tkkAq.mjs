const index_vue_vue_type_style_index_0_lang = ':root{--code-bg-color:#35373f;--code-hl-bg-color:rgba(0,0,0,.66);--code-ln-color:#6e6e7f;--code-ln-wrapper-width:3.5rem}@keyframes blink{0%,to{opacity:0}50%{opacity:1}}.markdown-it-container{position:relative}.markdown-it-container .markdown-body{background-color:transparent;font-size:15px}.markdown-it-container .markdown-body .markdown-custom-link{color:var(--el-color-primary);cursor:pointer;margin-bottom:5px}.markdown-it-container .markdown-doc-link-quote{align-items:center;background-color:#edeff1!important;border-radius:50%;color:#485568!important;display:inline-flex;font-size:10px;height:16px!important;justify-content:center;line-height:14px;margin-left:2px;transition:0s;vertical-align:text-bottom;width:16px!important}.markdown-it-container .markdown-doc-link-quote:hover{background-color:var(--el-color-primary)!important;color:#fff!important}.markdown-it-container .markdown-typing{animation:blink .6s infinite;background-color:currentColor;color:#1a202c;content:"";display:inline-block;height:14px;position:absolute;transform:translate(4px,2px) scaleY(1.3);width:5px}.markdown-it-container ul{list-style:disc}.markdown-it-container ol{list-style:decimal}.markdown-it-container code{border-radius:3px;font-size:.85em;margin:0;padding:.25rem}.markdown-it-container code[class*=language-],.markdown-it-container pre[class*=language-]{background:none;color:#ccc;font-size:1em;text-align:left;white-space:pre;word-break:normal;word-spacing:normal;word-wrap:normal;-webkit-hyphens:none;hyphens:none;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4}.markdown-it-container pre[class*=language-]{margin:0;overflow:auto;padding:20px 0}.markdown-it-container :not(pre)>code[class*=language-],.markdown-it-container pre[class*=language-]{background:#35373f;font-size:.85em}.markdown-it-container :not(pre)>code[class*=language-]{border-radius:.3em;white-space:normal}.markdown-it-container pre,.markdown-it-container pre[class*=language-]{border-radius:6px;display:inline-block;line-height:1.375;overflow:visible;padding:20px}.markdown-it-container pre code,.markdown-it-container pre[class*=language-] code{background-color:transparent!important;border-radius:0;color:#fff;overflow-wrap:unset;padding:0;-webkit-font-smoothing:auto;-moz-osx-font-smoothing:auto}.markdown-it-container div[class*=language-]{background-color:#35373f;background-color:var(--code-bg-color);border-radius:6px;margin:.85rem 0;overflow:hidden;padding-top:32px;position:relative}.markdown-it-container div[class*=language-] .code-copy-line{background-color:#595b63;box-sizing:border-box;color:#e0e0e0;font-size:12px;height:32px;left:0;line-height:32px;padding:0 12px;position:absolute;top:0;width:100%}.markdown-it-container div[class*=language-] .code-copy-line:before{color:#e0e0e0;content:attr(data-ext);font-size:.75rem;position:absolute;top:0;z-index:3}.markdown-it-container div[class*=language-] .code-copy-line .code-copy-btn{color:#e0e0e0;cursor:pointer;font-size:.75rem;position:absolute;right:1rem;top:0;z-index:3}.markdown-it-container div[class*=language-] pre,.markdown-it-container div[class*=language-] pre[class*=language-]{background:transparent!important;position:relative;z-index:1}.markdown-it-container div[class*=language-] .pre-code-scroll{overflow:auto}.markdown-it-container div[class*=language-]:not(.line-numbers-mode) .line-numbers{display:none}.markdown-it-container div[class*=language-].line-numbers-mode{padding-left:3.5rem;padding-left:var(--code-ln-wrapper-width)}.markdown-it-container div[class*=language-].line-numbers-mode pre{padding:20px 20px 20px 0;vertical-align:middle}.markdown-it-container div[class*=language-].line-numbers-mode .line-numbers{color:#6e6e7f;color:var(--code-ln-color);counter-reset:line-number;left:0;line-height:1.375;padding-top:52px;position:absolute;text-align:center;top:0;width:3.5rem;width:var(--code-ln-wrapper-width)}.markdown-it-container div[class*=language-].line-numbers-mode .line-numbers .line-number{height:1.175em;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;z-index:3}.markdown-it-container div[class*=language-].line-numbers-mode .line-numbers .line-number:before{content:counter(line-number);counter-increment:line-number;display:block;font-size:.8em;height:100%}.markdown-it-container{display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden}';

export { index_vue_vue_type_style_index_0_lang as i };
//# sourceMappingURL=index-styles-1.mjs-BI3tkkAq.mjs.map
