var E="top",R="bottom",W="right",P="left",me="auto",G=[E,R,W,P],U="start",J="end",Xe="clippingParents",je="viewport",K="popper",Ye="reference",De=G.reduce(function(t,e){return t.concat([e+"-"+U,e+"-"+J])},[]),Ee=[].concat(G,[me]).reduce(function(t,e){return t.concat([e,e+"-"+U,e+"-"+J])},[]),Ge="beforeRead",Je="read",Ke="afterRead",Qe="beforeMain",Ze="main",et="afterMain",tt="beforeWrite",nt="write",rt="afterWrite",ot=[Ge,Je,Ke,Qe,Ze,et,tt,nt,rt];function C(t){return t?(t.nodeName||"").toLowerCase():null}function H(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function Q(t){var e=H(t).Element;return t instanceof e||t instanceof Element}function B(t){var e=H(t).HTMLElement;return t instanceof e||t instanceof HTMLElement}function Pe(t){if(typeof ShadowRoot=="undefined")return!1;var e=H(t).ShadowRoot;return t instanceof e||t instanceof ShadowRoot}function Mt(t){var e=t.state;Object.keys(e.elements).forEach(function(n){var r=e.styles[n]||{},o=e.attributes[n]||{},i=e.elements[n];!B(i)||!C(i)||(Object.assign(i.style,r),Object.keys(o).forEach(function(a){var s=o[a];s===!1?i.removeAttribute(a):i.setAttribute(a,s===!0?"":s)}))})}function Rt(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach(function(r){var o=e.elements[r],i=e.attributes[r]||{},a=Object.keys(e.styles.hasOwnProperty(r)?e.styles[r]:n[r]),s=a.reduce(function(f,c){return f[c]="",f},{});!B(o)||!C(o)||(Object.assign(o.style,s),Object.keys(i).forEach(function(f){o.removeAttribute(f)}))})}}var Ae={name:"applyStyles",enabled:!0,phase:"write",fn:Mt,effect:Rt,requires:["computeStyles"]};function q(t){return t.split("-")[0]}var X=Math.max,ve=Math.min,Z=Math.round;function ee(t,e){e===void 0&&(e=!1);var n=t.getBoundingClientRect(),r=1,o=1;if(B(t)&&e){var i=t.offsetHeight,a=t.offsetWidth;a>0&&(r=Z(n.width)/a||1),i>0&&(o=Z(n.height)/i||1)}return{width:n.width/r,height:n.height/o,top:n.top/o,right:n.right/r,bottom:n.bottom/o,left:n.left/r,x:n.left/r,y:n.top/o}}function ke(t){var e=ee(t),n=t.offsetWidth,r=t.offsetHeight;return Math.abs(e.width-n)<=1&&(n=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:r}}function it(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&Pe(n)){var r=e;do{if(r&&t.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function N(t){return H(t).getComputedStyle(t)}function Wt(t){return["table","td","th"].indexOf(C(t))>=0}function I(t){return((Q(t)?t.ownerDocument:t.document)||window.document).documentElement}function ge(t){return C(t)==="html"?t:t.assignedSlot||t.parentNode||(Pe(t)?t.host:null)||I(t)}function at(t){return!B(t)||N(t).position==="fixed"?null:t.offsetParent}function Bt(t){var e=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,n=navigator.userAgent.indexOf("Trident")!==-1;if(n&&B(t)){var r=N(t);if(r.position==="fixed")return null}var o=ge(t);for(Pe(o)&&(o=o.host);B(o)&&["html","body"].indexOf(C(o))<0;){var i=N(o);if(i.transform!=="none"||i.perspective!=="none"||i.contain==="paint"||["transform","perspective"].indexOf(i.willChange)!==-1||e&&i.willChange==="filter"||e&&i.filter&&i.filter!=="none")return o;o=o.parentNode}return null}function se(t){for(var e=H(t),n=at(t);n&&Wt(n)&&N(n).position==="static";)n=at(n);return n&&(C(n)==="html"||C(n)==="body"&&N(n).position==="static")?e:n||Bt(t)||e}function Le(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function fe(t,e,n){return X(t,ve(e,n))}function St(t,e,n){var r=fe(t,e,n);return r>n?n:r}function st(){return{top:0,right:0,bottom:0,left:0}}function ft(t){return Object.assign({},st(),t)}function ct(t,e){return e.reduce(function(n,r){return n[r]=t,n},{})}var Tt=function(t,e){return t=typeof t=="function"?t(Object.assign({},e.rects,{placement:e.placement})):t,ft(typeof t!="number"?t:ct(t,G))};function Ht(t){var e,n=t.state,r=t.name,o=t.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=q(n.placement),f=Le(s),c=[P,W].indexOf(s)>=0,u=c?"height":"width";if(!(!i||!a)){var m=Tt(o.padding,n),v=ke(i),l=f==="y"?E:P,h=f==="y"?R:W,p=n.rects.reference[u]+n.rects.reference[f]-a[f]-n.rects.popper[u],g=a[f]-n.rects.reference[f],x=se(i),y=x?f==="y"?x.clientHeight||0:x.clientWidth||0:0,$=p/2-g/2,d=m[l],b=y-v[u]-m[h],w=y/2-v[u]/2+$,O=fe(d,w,b),j=f;n.modifiersData[r]=(e={},e[j]=O,e.centerOffset=O-w,e)}}function Ct(t){var e=t.state,n=t.options,r=n.element,o=r===void 0?"[data-popper-arrow]":r;o!=null&&(typeof o=="string"&&(o=e.elements.popper.querySelector(o),!o)||!it(e.elements.popper,o)||(e.elements.arrow=o))}var pt={name:"arrow",enabled:!0,phase:"main",fn:Ht,effect:Ct,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function te(t){return t.split("-")[1]}var qt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Vt(t){var e=t.x,n=t.y,r=window,o=r.devicePixelRatio||1;return{x:Z(e*o)/o||0,y:Z(n*o)/o||0}}function ut(t){var e,n=t.popper,r=t.popperRect,o=t.placement,i=t.variation,a=t.offsets,s=t.position,f=t.gpuAcceleration,c=t.adaptive,u=t.roundOffsets,m=t.isFixed,v=a.x,l=v===void 0?0:v,h=a.y,p=h===void 0?0:h,g=typeof u=="function"?u({x:l,y:p}):{x:l,y:p};l=g.x,p=g.y;var x=a.hasOwnProperty("x"),y=a.hasOwnProperty("y"),$=P,d=E,b=window;if(c){var w=se(n),O="clientHeight",j="clientWidth";if(w===H(n)&&(w=I(n),N(w).position!=="static"&&s==="absolute"&&(O="scrollHeight",j="scrollWidth")),w=w,o===E||(o===P||o===W)&&i===J){d=R;var A=m&&w===b&&b.visualViewport?b.visualViewport.height:w[O];p-=A-r.height,p*=f?1:-1}if(o===P||(o===E||o===R)&&i===J){$=W;var k=m&&w===b&&b.visualViewport?b.visualViewport.width:w[j];l-=k-r.width,l*=f?1:-1}}var D=Object.assign({position:s},c&&qt),S=u===!0?Vt({x:l,y:p}):{x:l,y:p};if(l=S.x,p=S.y,f){var L;return Object.assign({},D,(L={},L[d]=y?"0":"",L[$]=x?"0":"",L.transform=(b.devicePixelRatio||1)<=1?"translate("+l+"px, "+p+"px)":"translate3d("+l+"px, "+p+"px, 0)",L))}return Object.assign({},D,(e={},e[d]=y?p+"px":"",e[$]=x?l+"px":"",e.transform="",e))}function Nt(t){var e=t.state,n=t.options,r=n.gpuAcceleration,o=r===void 0?!0:r,i=n.adaptive,a=i===void 0?!0:i,s=n.roundOffsets,f=s===void 0?!0:s,c={placement:q(e.placement),variation:te(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:o,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,ut(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:a,roundOffsets:f})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,ut(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}var Me={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Nt,data:{}},ye={passive:!0};function It(t){var e=t.state,n=t.instance,r=t.options,o=r.scroll,i=o===void 0?!0:o,a=r.resize,s=a===void 0?!0:a,f=H(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return i&&c.forEach(function(u){u.addEventListener("scroll",n.update,ye)}),s&&f.addEventListener("resize",n.update,ye),function(){i&&c.forEach(function(u){u.removeEventListener("scroll",n.update,ye)}),s&&f.removeEventListener("resize",n.update,ye)}}var Re={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:It,data:{}},_t={left:"right",right:"left",bottom:"top",top:"bottom"};function be(t){return t.replace(/left|right|bottom|top/g,function(e){return _t[e]})}var zt={start:"end",end:"start"};function lt(t){return t.replace(/start|end/g,function(e){return zt[e]})}function We(t){var e=H(t),n=e.pageXOffset,r=e.pageYOffset;return{scrollLeft:n,scrollTop:r}}function Be(t){return ee(I(t)).left+We(t).scrollLeft}function Ft(t){var e=H(t),n=I(t),r=e.visualViewport,o=n.clientWidth,i=n.clientHeight,a=0,s=0;return r&&(o=r.width,i=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(a=r.offsetLeft,s=r.offsetTop)),{width:o,height:i,x:a+Be(t),y:s}}function Ut(t){var e,n=I(t),r=We(t),o=(e=t.ownerDocument)==null?void 0:e.body,i=X(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),a=X(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),s=-r.scrollLeft+Be(t),f=-r.scrollTop;return N(o||n).direction==="rtl"&&(s+=X(n.clientWidth,o?o.clientWidth:0)-i),{width:i,height:a,x:s,y:f}}function Se(t){var e=N(t),n=e.overflow,r=e.overflowX,o=e.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function dt(t){return["html","body","#document"].indexOf(C(t))>=0?t.ownerDocument.body:B(t)&&Se(t)?t:dt(ge(t))}function ce(t,e){var n;e===void 0&&(e=[]);var r=dt(t),o=r===((n=t.ownerDocument)==null?void 0:n.body),i=H(r),a=o?[i].concat(i.visualViewport||[],Se(r)?r:[]):r,s=e.concat(a);return o?s:s.concat(ce(ge(a)))}function Te(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function Xt(t){var e=ee(t);return e.top=e.top+t.clientTop,e.left=e.left+t.clientLeft,e.bottom=e.top+t.clientHeight,e.right=e.left+t.clientWidth,e.width=t.clientWidth,e.height=t.clientHeight,e.x=e.left,e.y=e.top,e}function ht(t,e){return e===je?Te(Ft(t)):Q(e)?Xt(e):Te(Ut(I(t)))}function Yt(t){var e=ce(ge(t)),n=["absolute","fixed"].indexOf(N(t).position)>=0,r=n&&B(t)?se(t):t;return Q(r)?e.filter(function(o){return Q(o)&&it(o,r)&&C(o)!=="body"}):[]}function Gt(t,e,n){var r=e==="clippingParents"?Yt(t):[].concat(e),o=[].concat(r,[n]),i=o[0],a=o.reduce(function(s,f){var c=ht(t,f);return s.top=X(c.top,s.top),s.right=ve(c.right,s.right),s.bottom=ve(c.bottom,s.bottom),s.left=X(c.left,s.left),s},ht(t,i));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function mt(t){var e=t.reference,n=t.element,r=t.placement,o=r?q(r):null,i=r?te(r):null,a=e.x+e.width/2-n.width/2,s=e.y+e.height/2-n.height/2,f;switch(o){case E:f={x:a,y:e.y-n.height};break;case R:f={x:a,y:e.y+e.height};break;case W:f={x:e.x+e.width,y:s};break;case P:f={x:e.x-n.width,y:s};break;default:f={x:e.x,y:e.y}}var c=o?Le(o):null;if(c!=null){var u=c==="y"?"height":"width";switch(i){case U:f[c]=f[c]-(e[u]/2-n[u]/2);break;case J:f[c]=f[c]+(e[u]/2-n[u]/2);break}}return f}function ne(t,e){e===void 0&&(e={});var n=e,r=n.placement,o=r===void 0?t.placement:r,i=n.boundary,a=i===void 0?Xe:i,s=n.rootBoundary,f=s===void 0?je:s,c=n.elementContext,u=c===void 0?K:c,m=n.altBoundary,v=m===void 0?!1:m,l=n.padding,h=l===void 0?0:l,p=ft(typeof h!="number"?h:ct(h,G)),g=u===K?Ye:K,x=t.rects.popper,y=t.elements[v?g:u],$=Gt(Q(y)?y:y.contextElement||I(t.elements.popper),a,f),d=ee(t.elements.reference),b=mt({reference:d,element:x,strategy:"absolute",placement:o}),w=Te(Object.assign({},x,b)),O=u===K?w:d,j={top:$.top-O.top+p.top,bottom:O.bottom-$.bottom+p.bottom,left:$.left-O.left+p.left,right:O.right-$.right+p.right},A=t.modifiersData.offset;if(u===K&&A){var k=A[o];Object.keys(j).forEach(function(D){var S=[W,R].indexOf(D)>=0?1:-1,L=[E,R].indexOf(D)>=0?"y":"x";j[D]+=k[L]*S})}return j}function Jt(t,e){e===void 0&&(e={});var n=e,r=n.placement,o=n.boundary,i=n.rootBoundary,a=n.padding,s=n.flipVariations,f=n.allowedAutoPlacements,c=f===void 0?Ee:f,u=te(r),m=u?s?De:De.filter(function(h){return te(h)===u}):G,v=m.filter(function(h){return c.indexOf(h)>=0});v.length===0&&(v=m);var l=v.reduce(function(h,p){return h[p]=ne(t,{placement:p,boundary:o,rootBoundary:i,padding:a})[q(p)],h},{});return Object.keys(l).sort(function(h,p){return l[h]-l[p]})}function Kt(t){if(q(t)===me)return[];var e=be(t);return[lt(t),e,lt(e)]}function Qt(t){var e=t.state,n=t.options,r=t.name;if(!e.modifiersData[r]._skip){for(var o=n.mainAxis,i=o===void 0?!0:o,a=n.altAxis,s=a===void 0?!0:a,f=n.fallbackPlacements,c=n.padding,u=n.boundary,m=n.rootBoundary,v=n.altBoundary,l=n.flipVariations,h=l===void 0?!0:l,p=n.allowedAutoPlacements,g=e.options.placement,x=q(g),y=x===g,$=f||(y||!h?[be(g)]:Kt(g)),d=[g].concat($).reduce(function(z,V){return z.concat(q(V)===me?Jt(e,{placement:V,boundary:u,rootBoundary:m,padding:c,flipVariations:h,allowedAutoPlacements:p}):V)},[]),b=e.rects.reference,w=e.rects.popper,O=new Map,j=!0,A=d[0],k=0;k<d.length;k++){var D=d[k],S=q(D),L=te(D)===U,re=[E,R].indexOf(S)>=0,oe=re?"width":"height",M=ne(e,{placement:D,boundary:u,rootBoundary:m,altBoundary:v,padding:c}),T=re?L?W:P:L?R:E;b[oe]>w[oe]&&(T=be(T));var pe=be(T),_=[];if(i&&_.push(M[S]<=0),s&&_.push(M[T]<=0,M[pe]<=0),_.every(function(z){return z})){A=D,j=!1;break}O.set(D,_)}if(j)for(var ue=h?3:1,xe=function(z){var V=d.find(function(de){var ae=O.get(de);if(ae)return ae.slice(0,z).every(function(Y){return Y})});if(V)return A=V,"break"},ie=ue;ie>0;ie--){var le=xe(ie);if(le==="break")break}e.placement!==A&&(e.modifiersData[r]._skip=!0,e.placement=A,e.reset=!0)}}var vt={name:"flip",enabled:!0,phase:"main",fn:Qt,requiresIfExists:["offset"],data:{_skip:!1}};function gt(t,e,n){return n===void 0&&(n={x:0,y:0}),{top:t.top-e.height-n.y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function yt(t){return[E,W,R,P].some(function(e){return t[e]>=0})}function Zt(t){var e=t.state,n=t.name,r=e.rects.reference,o=e.rects.popper,i=e.modifiersData.preventOverflow,a=ne(e,{elementContext:"reference"}),s=ne(e,{altBoundary:!0}),f=gt(a,r),c=gt(s,o,i),u=yt(f),m=yt(c);e.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:m},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":m})}var bt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Zt};function en(t,e,n){var r=q(t),o=[P,E].indexOf(r)>=0?-1:1,i=typeof n=="function"?n(Object.assign({},e,{placement:t})):n,a=i[0],s=i[1];return a=a||0,s=(s||0)*o,[P,W].indexOf(r)>=0?{x:s,y:a}:{x:a,y:s}}function tn(t){var e=t.state,n=t.options,r=t.name,o=n.offset,i=o===void 0?[0,0]:o,a=Ee.reduce(function(u,m){return u[m]=en(m,e.rects,i),u},{}),s=a[e.placement],f=s.x,c=s.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=f,e.modifiersData.popperOffsets.y+=c),e.modifiersData[r]=a}var wt={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:tn};function nn(t){var e=t.state,n=t.name;e.modifiersData[n]=mt({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}var He={name:"popperOffsets",enabled:!0,phase:"read",fn:nn,data:{}};function rn(t){return t==="x"?"y":"x"}function on(t){var e=t.state,n=t.options,r=t.name,o=n.mainAxis,i=o===void 0?!0:o,a=n.altAxis,s=a===void 0?!1:a,f=n.boundary,c=n.rootBoundary,u=n.altBoundary,m=n.padding,v=n.tether,l=v===void 0?!0:v,h=n.tetherOffset,p=h===void 0?0:h,g=ne(e,{boundary:f,rootBoundary:c,padding:m,altBoundary:u}),x=q(e.placement),y=te(e.placement),$=!y,d=Le(x),b=rn(d),w=e.modifiersData.popperOffsets,O=e.rects.reference,j=e.rects.popper,A=typeof p=="function"?p(Object.assign({},e.rects,{placement:e.placement})):p,k=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),D=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,S={x:0,y:0};if(w){if(i){var L,re=d==="y"?E:P,oe=d==="y"?R:W,M=d==="y"?"height":"width",T=w[d],pe=T+g[re],_=T-g[oe],ue=l?-j[M]/2:0,xe=y===U?O[M]:j[M],ie=y===U?-j[M]:-O[M],le=e.elements.arrow,z=l&&le?ke(le):{width:0,height:0},V=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:st(),de=V[re],ae=V[oe],Y=fe(0,O[M],z[M]),jt=$?O[M]/2-ue-Y-de-k.mainAxis:xe-Y-de-k.mainAxis,Dt=$?-O[M]/2+ue+Y+ae+k.mainAxis:ie+Y+ae+k.mainAxis,Oe=e.elements.arrow&&se(e.elements.arrow),Et=Oe?d==="y"?Oe.clientTop||0:Oe.clientLeft||0:0,Ce=(L=D==null?void 0:D[d])!=null?L:0,Pt=T+jt-Ce-Et,At=T+Dt-Ce,qe=fe(l?ve(pe,Pt):pe,T,l?X(_,At):_);w[d]=qe,S[d]=qe-T}if(s){var Ve,kt=d==="x"?E:P,Lt=d==="x"?R:W,F=w[b],he=b==="y"?"height":"width",Ne=F+g[kt],Ie=F-g[Lt],$e=[E,P].indexOf(x)!==-1,_e=(Ve=D==null?void 0:D[b])!=null?Ve:0,ze=$e?Ne:F-O[he]-j[he]-_e+k.altAxis,Fe=$e?F+O[he]+j[he]-_e-k.altAxis:Ie,Ue=l&&$e?St(ze,F,Fe):fe(l?ze:Ne,F,l?Fe:Ie);w[b]=Ue,S[b]=Ue-F}e.modifiersData[r]=S}}var xt={name:"preventOverflow",enabled:!0,phase:"main",fn:on,requiresIfExists:["offset"]};function an(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function sn(t){return t===H(t)||!B(t)?We(t):an(t)}function fn(t){var e=t.getBoundingClientRect(),n=Z(e.width)/t.offsetWidth||1,r=Z(e.height)/t.offsetHeight||1;return n!==1||r!==1}function cn(t,e,n){n===void 0&&(n=!1);var r=B(e),o=B(e)&&fn(e),i=I(e),a=ee(t,o),s={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(r||!r&&!n)&&((C(e)!=="body"||Se(i))&&(s=sn(e)),B(e)?(f=ee(e,!0),f.x+=e.clientLeft,f.y+=e.clientTop):i&&(f.x=Be(i))),{x:a.left+s.scrollLeft-f.x,y:a.top+s.scrollTop-f.y,width:a.width,height:a.height}}function pn(t){var e=new Map,n=new Set,r=[];t.forEach(function(i){e.set(i.name,i)});function o(i){n.add(i.name);var a=[].concat(i.requires||[],i.requiresIfExists||[]);a.forEach(function(s){if(!n.has(s)){var f=e.get(s);f&&o(f)}}),r.push(i)}return t.forEach(function(i){n.has(i.name)||o(i)}),r}function un(t){var e=pn(t);return ot.reduce(function(n,r){return n.concat(e.filter(function(o){return o.phase===r}))},[])}function ln(t){var e;return function(){return e||(e=new Promise(function(n){Promise.resolve().then(function(){e=void 0,n(t())})})),e}}function dn(t){var e=t.reduce(function(n,r){var o=n[r.name];return n[r.name]=o?Object.assign({},o,r,{options:Object.assign({},o.options,r.options),data:Object.assign({},o.data,r.data)}):r,n},{});return Object.keys(e).map(function(n){return e[n]})}var Ot={placement:"bottom",modifiers:[],strategy:"absolute"};function $t(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function we(t){t===void 0&&(t={});var e=t,n=e.defaultModifiers,r=n===void 0?[]:n,o=e.defaultOptions,i=o===void 0?Ot:o;return function(a,s,f){f===void 0&&(f=i);var c={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ot,i),modifiersData:{},elements:{reference:a,popper:s},attributes:{},styles:{}},u=[],m=!1,v={state:c,setOptions:function(p){var g=typeof p=="function"?p(c.options):p;h(),c.options=Object.assign({},i,c.options,g),c.scrollParents={reference:Q(a)?ce(a):a.contextElement?ce(a.contextElement):[],popper:ce(s)};var x=un(dn([].concat(r,c.options.modifiers)));return c.orderedModifiers=x.filter(function(y){return y.enabled}),l(),v.update()},forceUpdate:function(){if(!m){var p=c.elements,g=p.reference,x=p.popper;if($t(g,x)){c.rects={reference:cn(g,se(x),c.options.strategy==="fixed"),popper:ke(x)},c.reset=!1,c.placement=c.options.placement,c.orderedModifiers.forEach(function(j){return c.modifiersData[j.name]=Object.assign({},j.data)});for(var y=0;y<c.orderedModifiers.length;y++){if(c.reset===!0){c.reset=!1,y=-1;continue}var $=c.orderedModifiers[y],d=$.fn,b=$.options,w=b===void 0?{}:b,O=$.name;typeof d=="function"&&(c=d({state:c,options:w,name:O,instance:v})||c)}}}},update:ln(function(){return new Promise(function(p){v.forceUpdate(),p(c)})}),destroy:function(){h(),m=!0}};if(!$t(a,s))return v;v.setOptions(f).then(function(p){!m&&f.onFirstUpdate&&f.onFirstUpdate(p)});function l(){c.orderedModifiers.forEach(function(p){var g=p.name,x=p.options,y=x===void 0?{}:x,$=p.effect;if(typeof $=="function"){var d=$({state:c,name:g,instance:v,options:y}),b=function(){};u.push(d||b)}})}function h(){u.forEach(function(p){return p()}),u=[]}return v}}var hn=we(),mn=[Re,He,Me,Ae],vn=we({defaultModifiers:mn}),gn=[Re,He,Me,Ae,wt,vt,xt,pt,bt],yn=we({defaultModifiers:gn});export{et as afterMain,Ke as afterRead,rt as afterWrite,Ae as applyStyles,pt as arrow,me as auto,G as basePlacements,Qe as beforeMain,Ge as beforeRead,tt as beforeWrite,R as bottom,Xe as clippingParents,Me as computeStyles,yn as createPopper,hn as createPopperBase,vn as createPopperLite,ne as detectOverflow,J as end,Re as eventListeners,vt as flip,bt as hide,P as left,Ze as main,ot as modifierPhases,wt as offset,Ee as placements,K as popper,we as popperGenerator,He as popperOffsets,xt as preventOverflow,Je as read,Ye as reference,W as right,U as start,E as top,De as variationPlacements,je as viewport,nt as write};
