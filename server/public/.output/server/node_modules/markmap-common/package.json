{"name": "markmap-common", "version": "0.17.1", "description": "", "author": "", "license": "MIT", "scripts": {"clean": "del-cli dist", "build:types": "tsc", "build:js": "vite build", "build": "run-s clean build:*", "prepublishOnly": "run-s build"}, "main": "dist/index.js", "module": "dist/index.mjs", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "homepage": "https://github.com/markmap/markmap/packages/markmap-common#readme", "repository": {"type": "git", "url": "git+https://github.com/markmap/markmap.git"}, "bugs": {"url": "https://github.com/markmap/markmap/issues"}, "typings": "dist/index.d.ts", "dependencies": {"@babel/runtime": "^7.22.6", "@gera2ld/jsx-dom": "^2.2.2", "npm2url": "^0.2.4"}, "gitHead": "628f06a60d0c06f4d2f70b6df786ecb8b4124241"}