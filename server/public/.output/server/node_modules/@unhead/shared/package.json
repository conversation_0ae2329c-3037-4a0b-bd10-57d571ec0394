{"name": "@unhead/shared", "type": "module", "version": "1.11.14", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/harlan-zw", "homepage": "https://unhead.unjs.io", "repository": {"type": "git", "url": "git+https://github.com/unjs/unhead.git", "directory": "packages/schema"}, "bugs": {"url": "https://github.com/unjs/unhead/issues"}, "keywords": ["head", "meta tags", "types"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "dependencies": {"@unhead/schema": "1.11.14"}, "devDependencies": {"packrup": "^0.1.2"}, "scripts": {"build": "unbuild .", "stub": "unbuild . --stub", "export:sizes": "npx export-size . -r"}}