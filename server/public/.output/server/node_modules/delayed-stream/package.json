{"author": "<PERSON> <<EMAIL>> (http://debuggable.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "license": "MIT", "version": "1.0.0", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}}