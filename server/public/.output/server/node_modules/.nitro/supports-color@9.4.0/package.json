{"name": "supports-color", "version": "9.4.0", "description": "Detect whether a terminal supports color", "license": "MIT", "repository": "chalk/supports-color", "funding": "https://github.com/chalk/supports-color?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"node": "./index.js", "default": "./browser.js"}, "engines": {"node": ">=12"}, "scripts": {"//test": "xo && ava && tsd", "test": "tsd"}, "files": ["index.js", "index.d.ts", "browser.js", "browser.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "devDependencies": {"@types/node": "^20.3.2", "ava": "^5.3.1", "import-fresh": "^3.3.0", "tsd": "^0.18.0", "xo": "^0.54.2"}}