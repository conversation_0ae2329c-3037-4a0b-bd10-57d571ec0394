{"name": "entities", "version": "3.0.1", "description": "Encode & decode XML and HTML entities with ease", "author": "<PERSON> <<EMAIL>>", "funding": "https://github.com/fb55/entities?sponsor=1", "sideEffects": false, "keywords": ["entity", "decoding", "encoding", "html", "xml", "html entities"], "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/**/*"], "engines": {"node": ">=0.12"}, "devDependencies": {"@types/jest": "^26.0.24", "@types/node": "^16.4.13", "@typescript-eslint/eslint-plugin": "^4.29.0", "@typescript-eslint/parser": "^4.29.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^27.0.3", "prettier": "^2.0.5", "ts-jest": "^27.0.4", "typescript": "^4.0.2"}, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc && cp -r src/maps lib", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/entities.git"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8"}, "prettier": {"tabWidth": 4, "proseWrap": "always"}}