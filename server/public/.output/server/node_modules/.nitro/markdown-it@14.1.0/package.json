{"name": "markdown-it", "version": "14.1.0", "description": "Markdown-it - modern pluggable markdown parser.", "keywords": ["markdown", "parser", "commonmark", "markdown-it", "markdown-it-plugin"], "repository": "markdown-it/markdown-it", "license": "MIT", "main": "dist/index.cjs.js", "module": "index.mjs", "exports": {".": {"import": "./index.mjs", "require": "./dist/index.cjs.js"}, "./*": {"require": "./*", "import": "./*"}}, "bin": {"markdown-it": "bin/markdown-it.mjs"}, "scripts": {"lint": "eslint .", "test": "npm run lint && CJS_ONLY=1 npm run build && c8 --exclude dist --exclude test -r text -r html -r lcov mocha && node support/specsplit.mjs", "doc": "node support/build_doc.mjs", "gh-doc": "npm run doc && gh-pages -d apidoc -f", "demo": "npm run lint && node support/build_demo.mjs", "gh-demo": "npm run demo && gh-pages -d demo -f -b master -r **************:markdown-it/markdown-it.github.io.git", "build": "rollup -c support/rollup.config.mjs", "benchmark-deps": "npm install --prefix benchmark/extra/ -g marked@0.3.6 commonmark@0.26.0 markdown-it/markdown-it.git#2.2.1", "specsplit": "support/specsplit.mjs good -o test/fixtures/commonmark/good.txt && support/specsplit.mjs bad -o test/fixtures/commonmark/bad.txt && support/specsplit.mjs", "todo": "grep 'TODO' -n -r ./lib 2>/dev/null", "prepublishOnly": "npm test && npm run build && npm run gh-demo && npm run gh-doc"}, "files": ["index.mjs", "lib/", "dist/"], "dependencies": {"argparse": "^2.0.1", "entities": "^4.4.0", "linkify-it": "^5.0.0", "mdurl": "^2.0.0", "punycode.js": "^2.3.1", "uc.micro": "^2.1.0"}, "devDependencies": {"@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "ansi": "^0.3.0", "benchmark": "~2.1.0", "c8": "^8.0.1", "chai": "^4.2.0", "eslint": "^8.4.1", "eslint-config-standard": "^17.1.0", "express": "^4.14.0", "gh-pages": "^6.1.0", "highlight.js": "^11.9.0", "jest-worker": "^29.7.0", "markdown-it-abbr": "^2.0.0", "markdown-it-container": "^4.0.0", "markdown-it-deflist": "^3.0.0", "markdown-it-emoji": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-for-inline": "^2.0.1", "markdown-it-ins": "^4.0.0", "markdown-it-mark": "^4.0.0", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markdown-it-testgen": "^0.1.3", "mocha": "^10.2.0", "ndoc": "^6.0.0", "needle": "^3.0.0", "rollup": "^4.5.0", "shelljs": "^0.8.4", "supertest": "^6.0.1"}, "mocha": {"inline-diffs": true, "timeout": 60000}}