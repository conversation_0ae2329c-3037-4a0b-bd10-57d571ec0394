{"name": "uc.micro", "version": "2.1.0", "description": "Micro subset of unicode data files for markdown-it projects.", "repository": "markdown-it/uc.micro", "license": "MIT", "main": "build/index.cjs.js", "module": "index.mjs", "exports": {".": {"require": "./build/index.cjs.js", "import": "./index.mjs"}, "./*": {"require": "./*", "import": "./*"}}, "files": ["index.mjs", "categories/", "properties/", "build/"], "scripts": {"test": "npm run build && mocha", "build": "rollup -c", "update": "node update.mjs && npm test", "prepublishOnly": "npm run build"}, "devDependencies": {"@unicode/unicode-15.1.0": "^1.5.2", "mocha": "^10.2.0", "rollup": "^4.6.1", "shelljs": "^0.8.5"}}