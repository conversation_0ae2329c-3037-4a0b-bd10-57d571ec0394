{"name": "@chenfengyuan/vue-countdown", "version": "2.1.1", "description": "Countdown component for Vue 3.", "main": "dist/vue-countdown.js", "module": "dist/vue-countdown.esm.js", "types": "dist/vue-countdown.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c --environment BUILD:production", "build:docs": "webpack --env production", "build:types": "move-file dist/index.d.ts dist/vue-countdown.d.ts", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "clean": "del-cli dist", "lint": "npm run lint:js && npm run lint:css", "lint:css": "stylelint **/*.{css,scss,vue} --fix", "lint:js": "eslint . --ext .js,.ts,.vue --fix", "prepare": "husky install", "release": "npm run clean && npm run lint && npm run build && npm run build:types && npm run build:docs && npm test && npm run changelog", "serve": "webpack serve --hot --open", "start": "npm run serve", "test": "jest", "test:coverage": "cat coverage/lcov.info | codecov"}, "repository": {"type": "git", "url": "https://github.com/fengyuanchen/vue-countdown.git"}, "keywords": ["countdown", "vue", "vue3", "vue-component", "front-end", "web"], "author": "<PERSON> (https://chenfengyuan.com/)", "license": "MIT", "bugs": "https://github.com/fengyuanchen/vue-countdown/issues", "homepage": "https://fengyuanchen.github.io/vue-countdown", "devDependencies": {"@babel/core": "^7.21.3", "@babel/preset-env": "^7.20.2", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@types/jest": "^28.1.8", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "@vue/compiler-sfc": "^3.2.47", "@vue/test-utils": "^2.3.1", "@vue/vue3-jest": "^28.1.0", "babel-jest": "^28.1.3", "babel-loader": "^8.3.0", "change-case": "^4.1.2", "codecov": "^3.8.3", "conventional-changelog-cli": "^2.2.2", "create-banner": "^2.0.0", "css-loader": "^6.7.3", "del-cli": "^5.0.0", "eslint": "^8.36.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-vue": "^9.9.0", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.3", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "lint-staged": "^13.2.0", "markdown-to-vue-loader": "^3.1.4", "mini-css-extract-plugin": "^2.7.5", "move-file-cli": "^3.0.0", "postcss": "^8.4.21", "rollup": "^2.79.1", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.34.1", "rollup-plugin-vue": "^6.0.0", "sass": "^1.59.3", "sass-loader": "^13.2.0", "style-loader": "^3.3.2", "stylelint": "^14.16.1", "stylelint-config-recommended-scss": "^7.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-order": "^5.0.0", "ts-jest": "^28.0.8", "ts-loader": "^9.4.2", "tslib": "^2.5.0", "typescript": "^4.9.5", "vue": "^3.2.47", "vue-loader": "^17.0.1", "webpack": "^5.76.2", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.13.0"}, "peerDependencies": {"vue": "^3.0.0"}, "publishConfig": {"access": "public"}}