{"name": "d3-dsv", "version": "3.0.1", "description": "A parser and formatter for delimiter-separated values, such as CSV and TSV", "homepage": "https://d3js.org/d3-dsv/", "repository": {"type": "git", "url": "https://github.com/d3/d3-dsv.git"}, "keywords": ["d3", "d3-module", "dsv", "csv", "tsv"], "license": "ISC", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "type": "module", "files": ["bin/*.js", "dist/**/*.js", "src/**/*.js"], "module": "src/index.js", "main": "src/index.js", "jsdelivr": "dist/d3-dsv.min.js", "unpkg": "dist/d3-dsv.min.js", "exports": {"umd": "./dist/d3-dsv.min.js", "default": "./src/index.js"}, "sideEffects": false, "dependencies": {"commander": "7", "iconv-lite": "0.6", "rw": "1"}, "devDependencies": {"csv-spectrum": "1", "eslint": "7", "mocha": "8", "rollup": "2", "rollup-plugin-terser": "7"}, "scripts": {"test": "TZ=America/Los_Angeles mocha 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && yarn test && rollup -c", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd -"}, "engines": {"node": ">=12"}, "bin": {"csv2json": "bin/dsv2json.js", "csv2tsv": "bin/dsv2dsv.js", "dsv2dsv": "bin/dsv2dsv.js", "dsv2json": "bin/dsv2json.js", "json2csv": "bin/json2dsv.js", "json2dsv": "bin/json2dsv.js", "json2tsv": "bin/json2dsv.js", "tsv2csv": "bin/dsv2dsv.js", "tsv2json": "bin/dsv2json.js"}}