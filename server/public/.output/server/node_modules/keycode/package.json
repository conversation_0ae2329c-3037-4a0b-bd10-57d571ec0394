{"name": "keycode", "description": "Convert between keyboard keycodes and keynames and vice versa.", "version": "2.2.1", "main": "index.js", "directories": {"example": "examples", "test": "test"}, "scripts": {"test": "mocha test/keycode.js"}, "repository": {"type": "git", "url": "git://github.com/timoxley/keycode.git"}, "license": "MIT", "bugs": {"url": "https://github.com/timoxley/keycode/issues"}, "devDependencies": {"mocha": "^3.0.2"}, "homepage": "https://github.com/timoxley/keycode", "dependencies": {}, "keywords": ["keyboard", "keycode", "keyboardevent", "ascii", "keydown", "keyup", "metakey", "keyname", "keypress"], "author": "<PERSON> <<EMAIL>>", "typings": "./index.d.ts"}