{"name": "mpd-parser", "version": "0.22.1", "description": "mpd parser", "main": "dist/mpd-parser.cjs.js", "module": "dist/mpd-parser.es.js", "repository": {"type": "git", "url": "**************:videojs/mpd-parser.git"}, "bin": {"mpd-to-m3u8-json": "bin/parse.js"}, "scripts": {"prenetlify": "npm run build", "netlify": "node scripts/netlify.js", "build-test": "cross-env-shell TEST_BUNDLE_ONLY=1 'npm run build'", "build-prod": "cross-env-shell NO_TEST_BUNDLE=1 'npm run build'", "build": "npm-run-all -s clean -p build:*", "build:js": "rollup -c scripts/rollup.config.js", "clean": "shx rm -rf ./dist ./test/dist && shx mkdir -p ./dist ./test/dist", "lint": "vjsstandard", "prepublishOnly": "npm-run-all build-prod && vjsverify --verbose", "start": "npm-run-all -p server watch", "server": "karma start scripts/karma.conf.js --singleRun=false --auto-watch", "test": "npm-run-all lint build-test && npm-run-all test:*", "test:browser": "karma start scripts/karma.conf.js", "test:node": "qunit test/dist/bundle-node.js", "posttest": "shx cat test/dist/coverage/text.txt", "version": "is-prerelease || npm run update-changelog && git add CHANGELOG.md", "update-changelog": "conventional-changelog -p videojs -i CHANGELOG.md -s", "watch": "npm-run-all -p watch:*", "watch:js": "npm run build:js -- -w"}, "keywords": ["videojs", "videojs-plugin"], "author": "Brightcove, Inc", "license": "Apache-2.0", "vjsstandard": {"ignore": ["dist", "docs", "test/dist"]}, "files": ["CONTRIBUTING.md", "dist/", "docs/", "index.html", "scripts/", "src/", "test/"], "dependencies": {"@babel/runtime": "^7.12.5", "@videojs/vhs-utils": "^3.0.5", "@xmldom/xmldom": "^0.8.3", "global": "^4.4.0"}, "devDependencies": {"@rollup/plugin-replace": "^2.3.4", "@videojs/generator-helpers": "~2.0.1", "jsdom": "^16.4.0", "karma": "^5.2.3", "rollup": "^2.36.1", "rollup-plugin-string": "^3.0.0", "sinon": "^9.2.3", "videojs-generate-karma-config": "~7.0.0", "videojs-generate-rollup-config": "^6.2.2", "videojs-generator-verify": "~3.0.2", "videojs-standard": "^9.0.1"}, "generator-videojs-plugin": {"version": "7.7.3"}, "browserslist": ["defaults", "ie 11"], "lint-staged": {"*.js": "vjsstandard --fix", "README.md": "doctoc --notitle"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}