{"name": "jsonc-parser", "version": "3.2.0", "description": "Scanner and parser for JSON with comments.", "main": "./lib/umd/main.js", "typings": "./lib/umd/main.d.ts", "module": "./lib/esm/main.js", "author": "Microsoft Corporation", "repository": {"type": "git", "url": "https://github.com/microsoft/node-jsonc-parser"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/node-jsonc-parser/issues"}, "devDependencies": {"mocha": "^10.0.0", "typescript": "^4.8.2", "@types/node": "^16.x", "@types/mocha": "^9.1.1", "@typescript-eslint/eslint-plugin": "^5.36.0", "@typescript-eslint/parser": "^5.36.0", "eslint": "^8.23.0", "rimraf": "^3.0.2"}, "scripts": {"prepublishOnly": "npm run clean && npm run compile-esm && npm run test && npm run remove-sourcemap-refs", "postpublish": "node ./build/post-publish.js", "compile": "tsc -p ./src && npm run lint", "compile-esm": "tsc -p ./src/tsconfig.esm.json", "remove-sourcemap-refs": "node ./build/remove-sourcemap-refs.js", "clean": "<PERSON><PERSON><PERSON> lib", "watch": "tsc -w -p ./src", "test": "npm run compile && mocha ./lib/umd/test", "lint": "eslint src/**/*.ts", "preversion": "npm test", "postversion": "git push && git push --tags"}}