{"name": "joplin-turndown-plugin-gfm", "description": "Turndown plugin to add GitHub Flavored Markdown extensions.", "version": "1.0.12", "author": "<PERSON>", "main": "lib/turndown-plugin-gfm.cjs.js", "module": "lib/turndown-plugin-gfm.es.js", "jsnext:main": "lib/turndown-plugin-gfm.es.js", "devDependencies": {"browserify": "^14.5.0", "rollup": "^0.50.0", "standard": "^10.0.3", "turndown": "4.0.1", "turndown-attendant": "0.0.2"}, "files": ["lib", "dist"], "keywords": ["turndown", "turndown-plugin", "html-to-markdown", "html", "markdown", "github-flavored-markdown", "gfm"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/laurent22/joplin-turndown-plugin-gfm.git"}, "scripts": {"build": "npm run build-cjs && npm run build-es && npm run build-iife", "build-cjs": "rollup -c config/rollup.config.cjs.js && rollup -c config/rollup.config.browser.cjs.js", "build-es": "rollup -c config/rollup.config.es.js && rollup -c config/rollup.config.browser.es.js", "build-iife": "rollup -c config/rollup.config.iife.js", "build-test": "browserify test/turndown-plugin-gfm-test.js --outfile test/turndown-plugin-gfm-test.browser.js", "prepublish": "npm run build", "test": "npm run build && standard ./src/**/*.js && node test/turndown-plugin-gfm-test.js"}}