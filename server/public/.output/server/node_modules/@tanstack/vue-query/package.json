{"name": "@tanstack/vue-query", "version": "4.32.0", "description": "Hooks for managing, caching and syncing asynchronous and remote data in Vue", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/vue-query"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "types": "build/lib/index.d.ts", "main": "build/lib/index.js", "module": "build/lib/index.esm.js", "exports": {".": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.mjs", "default": "./build/lib/index.js"}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build/lib/*", "build/umd/*"], "devDependencies": {"@vue/composition-api": "1.7.1", "vue": "^3.2.40", "vue2": "npm:vue@2"}, "dependencies": {"@vue/devtools-api": "^6.4.2", "@tanstack/match-sorter-utils": "^8.1.1", "vue-demi": "^0.13.11", "@tanstack/query-core": "4.32.0"}, "peerDependencies": {"@vue/composition-api": "^1.1.2", "vue": "^2.5.0 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "scripts": {"clean": "rimraf ./build", "test:eslint": "eslint --ext .ts ./src", "test:types": "tsc", "test:lib": "jest --config ./jest.config.ts", "test:lib:dev": "pnpm run test:lib --watch", "build:types": "tsc --build"}}