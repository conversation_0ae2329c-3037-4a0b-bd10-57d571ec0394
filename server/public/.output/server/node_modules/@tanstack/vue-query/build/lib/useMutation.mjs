import { computed, reactive, watch, onScopeDispose, toRefs, readonly, unref } from 'vue-demi';
import { MutationObserver } from '@tanstack/query-core';
import { updateState, isMutationKey, cloneDeepUnref } from './utils.mjs';
import { useQueryClient } from './useQueryClient.mjs';

function useMutation(arg1, arg2, arg3) {
  var _options$value$queryC;

  const options = computed(() => {
    return parseMutationArgs(arg1, arg2, arg3);
  });
  const queryClient = (_options$value$queryC = options.value.queryClient) != null ? _options$value$queryC : useQueryClient(options.value.queryClientKey);
  const observer = new MutationObserver(queryClient, queryClient.defaultMutationOptions(options.value));
  const state = reactive(observer.getCurrentResult());
  const unsubscribe = observer.subscribe(result => {
    updateState(state, result);
  });

  const mutate = (variables, mutateOptions) => {
    observer.mutate(variables, mutateOptions).catch(() => {// This is intentional
    });
  };

  watch(options, () => {
    observer.setOptions(queryClient.defaultMutationOptions(options.value));
  }, {
    deep: true
  });
  onScopeDispose(() => {
    unsubscribe();
  });
  const resultRefs = toRefs(readonly(state));
  return { ...resultRefs,
    mutate,
    mutateAsync: state.mutate,
    reset: state.reset
  };
}
function parseMutationArgs(arg1, arg2, arg3) {
  const plainArg1 = unref(arg1);
  const plainArg2 = unref(arg2);
  let options = plainArg1;

  if (isMutationKey(plainArg1)) {
    if (typeof plainArg2 === 'function') {
      const plainArg3 = unref(arg3);
      options = { ...plainArg3,
        mutationKey: plainArg1,
        mutationFn: plainArg2
      };
    } else {
      options = { ...plainArg2,
        mutationKey: plainArg1
      };
    }
  }

  if (typeof plainArg1 === 'function') {
    options = { ...plainArg2,
      mutationFn: plainArg1
    };
  }

  return cloneDeepUnref(options);
}

export { parseMutationArgs, useMutation };
//# sourceMappingURL=useMutation.mjs.map
