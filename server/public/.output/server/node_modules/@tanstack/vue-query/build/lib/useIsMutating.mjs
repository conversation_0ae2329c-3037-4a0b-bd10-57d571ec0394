import { computed, ref, watch, onScopeDispose, unref } from 'vue-demi';
import { useQueryClient } from './useQueryClient.mjs';
import { isQueryKey, cloneDeepUnref } from './utils.mjs';

function useIsMutating(arg1, arg2) {
  var _filters$value$queryC;

  const filters = computed(() => parseFilterArgs(arg1, arg2));
  const queryClient = (_filters$value$queryC = filters.value.queryClient) != null ? _filters$value$queryC : useQueryClient(filters.value.queryClientKey);
  const isMutating = ref(queryClient.isMutating(filters));
  const unsubscribe = queryClient.getMutationCache().subscribe(() => {
    isMutating.value = queryClient.isMutating(filters);
  });
  watch(filters, () => {
    isMutating.value = queryClient.isMutating(filters);
  }, {
    deep: true
  });
  onScopeDispose(() => {
    unsubscribe();
  });
  return isMutating;
}
function parseFilterArgs(arg1, arg2 = {}) {
  const plainArg1 = unref(arg1);
  const plainArg2 = unref(arg2);
  let options = plainArg1;

  if (isQueryKey(plainArg1)) {
    options = { ...plainArg2,
      mutationKey: plainArg1
    };
  } else {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    options = plainArg1 || {};
  }

  return cloneDeepUnref(options);
}

export { parseFilterArgs, useIsMutating };
//# sourceMappingURL=useIsMutating.mjs.map
