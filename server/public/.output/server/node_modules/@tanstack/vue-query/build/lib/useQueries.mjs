import { QueriesObserver } from '@tanstack/query-core';
import { computed, reactive, watch, onScopeDispose, readonly } from 'vue-demi';
import { useQueryClient } from './useQueryClient.mjs';
import { cloneDeepUnref } from './utils.mjs';

/* eslint-disable @typescript-eslint/no-explicit-any */
function useQueries({
  queries,
  queryClient: queryClientInjected
}) {
  var _unreffedQueries$valu, _unreffedQueries$valu2, _ref;

  const unreffedQueries = computed(() => cloneDeepUnref(queries));
  const queryClientKey = (_unreffedQueries$valu = unreffedQueries.value[0]) == null ? void 0 : _unreffedQueries$valu.queryClientKey;
  const optionsQueryClient = (_unreffedQueries$valu2 = unreffedQueries.value[0]) == null ? void 0 : _unreffedQueries$valu2.queryClient;
  const queryClient = (_ref = queryClientInjected != null ? queryClientInjected : optionsQueryClient) != null ? _ref : useQueryClient(queryClientKey);

  if (process.env.NODE_ENV !== 'production' && (queryClientKey || optionsQueryClient)) {
    queryClient.getLogger().error("Providing queryClient to individual queries in useQueries has been deprecated and will be removed in the next major version. You can still pass queryClient as an option directly to useQueries hook.");
  }

  const defaultedQueries = computed(() => unreffedQueries.value.map(options => {
    const defaulted = queryClient.defaultQueryOptions(options);
    defaulted._optimisticResults = queryClient.isRestoring.value ? 'isRestoring' : 'optimistic';
    return defaulted;
  }));
  const observer = new QueriesObserver(queryClient, defaultedQueries.value);
  const state = reactive(observer.getCurrentResult());

  let unsubscribe = () => {// noop
  };

  watch(queryClient.isRestoring, isRestoring => {
    if (!isRestoring) {
      unsubscribe();
      unsubscribe = observer.subscribe(result => {
        state.splice(0, result.length, ...result);
      }); // Subscription would not fire for persisted results

      state.splice(0, state.length, ...observer.getOptimisticResult(defaultedQueries.value));
    }
  }, {
    immediate: true
  });
  watch(unreffedQueries, () => {
    observer.setQueries(defaultedQueries.value);
    state.splice(0, state.length, ...observer.getCurrentResult());
  }, {
    deep: true
  });
  onScopeDispose(() => {
    unsubscribe();
  });
  return readonly(state);
}

export { useQueries };
//# sourceMappingURL=useQueries.mjs.map
