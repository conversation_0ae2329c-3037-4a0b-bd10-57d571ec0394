import { ref } from 'vue-demi';
import { QueryClient as QueryClient$1 } from '@tanstack/query-core';
import { cloneDeepUnref, isQueryKey } from './utils.mjs';
import { QueryCache } from './queryCache.mjs';
import { MutationCache } from './mutationCache.mjs';

class QueryClient extends QueryClient$1 {
  constructor(config = {}) {
    const unreffedConfig = cloneDeepUnref(config);
    const vueQueryConfig = {
      logger: cloneDeepUnref(unreffedConfig.logger),
      defaultOptions: cloneDeepUnref(unreffedConfig.defaultOptions),
      queryCache: unreffedConfig.queryCache || new QueryCache(),
      mutationCache: unreffedConfig.mutationCache || new MutationCache()
    };
    super(vueQueryConfig);
    this.isRestoring = ref(false);
  }

  isFetching(arg1, arg2) {
    const arg1Unreffed = cloneDeepUnref(arg1);
    const arg2Unreffed = cloneDeepUnref(arg2);

    if (isQueryKey(arg1Unreffed)) {
      return super.isFetching(arg1Unreffed, arg2Unreffed);
    }

    return super.isFetching(arg1Unreffed);
  }

  isMutating(filters) {
    return super.isMutating(cloneDeepUnref(filters));
  }

  getQueryData(queryKey, filters) {
    return super.getQueryData(cloneDeepUnref(queryKey), cloneDeepUnref(filters));
  }

  getQueriesData(queryKeyOrFilters) {
    const unreffed = cloneDeepUnref(queryKeyOrFilters);

    if (isQueryKey(unreffed)) {
      return super.getQueriesData(unreffed);
    }

    return super.getQueriesData(unreffed);
  }

  setQueryData(queryKey, updater, options) {
    return super.setQueryData(cloneDeepUnref(queryKey), updater, cloneDeepUnref(options));
  }

  setQueriesData(queryKeyOrFilters, updater, options) {
    const arg1Unreffed = cloneDeepUnref(queryKeyOrFilters);
    const arg3Unreffed = cloneDeepUnref(options);

    if (isQueryKey(arg1Unreffed)) {
      return super.setQueriesData(arg1Unreffed, updater, arg3Unreffed);
    }

    return super.setQueriesData(arg1Unreffed, updater, arg3Unreffed);
  }

  getQueryState(queryKey, filters) {
    return super.getQueryState(cloneDeepUnref(queryKey), cloneDeepUnref(filters));
  }

  removeQueries(arg1, arg2) {
    const arg1Unreffed = cloneDeepUnref(arg1);

    if (isQueryKey(arg1Unreffed)) {
      return super.removeQueries(arg1Unreffed, cloneDeepUnref(arg2));
    }

    return super.removeQueries(arg1Unreffed);
  }

  resetQueries(arg1, arg2, arg3) {
    const arg1Unreffed = cloneDeepUnref(arg1);
    const arg2Unreffed = cloneDeepUnref(arg2);

    if (isQueryKey(arg1Unreffed)) {
      return super.resetQueries(arg1Unreffed, arg2Unreffed, cloneDeepUnref(arg3));
    }

    return super.resetQueries(arg1Unreffed, arg2Unreffed);
  }

  cancelQueries(arg1, arg2, arg3) {
    const arg1Unreffed = cloneDeepUnref(arg1);
    const arg2Unreffed = cloneDeepUnref(arg2);

    if (isQueryKey(arg1Unreffed)) {
      return super.cancelQueries(arg1Unreffed, arg2Unreffed, cloneDeepUnref(arg3));
    }

    return super.cancelQueries(arg1Unreffed, arg2Unreffed);
  }

  invalidateQueries(arg1, arg2, arg3) {
    const arg1Unreffed = cloneDeepUnref(arg1);
    const arg2Unreffed = cloneDeepUnref(arg2);

    if (isQueryKey(arg1Unreffed)) {
      return super.invalidateQueries(arg1Unreffed, arg2Unreffed, cloneDeepUnref(arg3));
    }

    return super.invalidateQueries(arg1Unreffed, arg2Unreffed);
  }

  refetchQueries(arg1, arg2, arg3) {
    const arg1Unreffed = cloneDeepUnref(arg1);
    const arg2Unreffed = cloneDeepUnref(arg2);

    if (isQueryKey(arg1Unreffed)) {
      return super.refetchQueries(arg1Unreffed, arg2Unreffed, cloneDeepUnref(arg3));
    }

    return super.refetchQueries(arg1Unreffed, arg2Unreffed);
  }

  fetchQuery(arg1, arg2, arg3) {
    const arg1Unreffed = cloneDeepUnref(arg1);
    const arg2Unreffed = cloneDeepUnref(arg2);

    if (isQueryKey(arg1Unreffed)) {
      return super.fetchQuery(arg1Unreffed, arg2Unreffed, cloneDeepUnref(arg3));
    }

    return super.fetchQuery(arg1Unreffed);
  }

  prefetchQuery(arg1, arg2, arg3) {
    return super.prefetchQuery(cloneDeepUnref(arg1), cloneDeepUnref(arg2), cloneDeepUnref(arg3));
  }

  fetchInfiniteQuery(arg1, arg2, arg3) {
    const arg1Unreffed = cloneDeepUnref(arg1);
    const arg2Unreffed = cloneDeepUnref(arg2);

    if (isQueryKey(arg1Unreffed)) {
      return super.fetchInfiniteQuery(arg1Unreffed, arg2Unreffed, cloneDeepUnref(arg3));
    }

    return super.fetchInfiniteQuery(arg1Unreffed);
  }

  prefetchInfiniteQuery(arg1, arg2, arg3) {
    return super.prefetchInfiniteQuery(cloneDeepUnref(arg1), cloneDeepUnref(arg2), cloneDeepUnref(arg3));
  }

  setDefaultOptions(options) {
    super.setDefaultOptions(cloneDeepUnref(options));
  }

  setQueryDefaults(queryKey, options) {
    super.setQueryDefaults(cloneDeepUnref(queryKey), cloneDeepUnref(options));
  }

  getQueryDefaults(queryKey) {
    return super.getQueryDefaults(cloneDeepUnref(queryKey));
  }

  setMutationDefaults(mutationKey, options) {
    super.setMutationDefaults(cloneDeepUnref(mutationKey), cloneDeepUnref(options));
  }

  getMutationDefaults(mutationKey) {
    return super.getMutationDefaults(cloneDeepUnref(mutationKey));
  }

}

export { QueryClient };
//# sourceMappingURL=queryClient.mjs.map
