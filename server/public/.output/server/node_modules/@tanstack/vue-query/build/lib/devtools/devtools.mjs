import { setupDevtoolsPlugin } from '@vue/devtools-api';
import { rankItem } from '@tanstack/match-sorter-utils';
import { sortFns, getQueryStateLabel, getQueryStatusFg, getQueryStatusBg } from './utils.mjs';

/* istanbul ignore file */
const pluginId = 'vue-query';
const pluginName = 'Vue Query';
function setupDevtools(app, queryClient) {
  setupDevtoolsPlugin({
    id: pluginId,
    label: pluginName,
    packageName: 'vue-query',
    homepage: 'https://tanstack.com/query/v4',
    logo: 'https://vue-query.vercel.app/vue-query.svg',
    app,
    settings: {
      baseSort: {
        type: 'choice',
        component: 'button-group',
        label: 'Sort Cache Entries',
        options: [{
          label: 'ASC',
          value: 1
        }, {
          label: 'DESC',
          value: -1
        }],
        defaultValue: 1
      },
      sortFn: {
        type: 'choice',
        label: 'Sort Function',
        options: Object.keys(sortFns).map(key => ({
          label: key,
          value: key
        })),
        defaultValue: Object.keys(sortFns)[0]
      }
    }
  }, api => {
    const queryCache = queryClient.getQueryCache();
    api.addInspector({
      id: pluginId,
      label: pluginName,
      icon: 'api',
      nodeActions: [{
        icon: 'cloud_download',
        tooltip: 'Refetch',
        action: queryHash => {
          var _queryCache$get;

          (_queryCache$get = queryCache.get(queryHash)) == null ? void 0 : _queryCache$get.fetch();
        }
      }, {
        icon: 'alarm',
        tooltip: 'Invalidate',
        action: queryHash => {
          const query = queryCache.get(queryHash);
          queryClient.invalidateQueries(query.queryKey);
        }
      }, {
        icon: 'settings_backup_restore',
        tooltip: 'Reset',
        action: queryHash => {
          var _queryCache$get2;

          (_queryCache$get2 = queryCache.get(queryHash)) == null ? void 0 : _queryCache$get2.reset();
        }
      }, {
        icon: 'delete',
        tooltip: 'Remove',
        action: queryHash => {
          const query = queryCache.get(queryHash);
          queryCache.remove(query);
        }
      }]
    });
    api.addTimelineLayer({
      id: pluginId,
      label: pluginName,
      color: 0xffd94c
    });
    queryCache.subscribe(event => {
      api.sendInspectorTree(pluginId);
      api.sendInspectorState(pluginId);
      const queryEvents = ['added', 'removed', 'updated'];

      if ( // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      event && queryEvents.includes(event.type)) {
        api.addTimelineEvent({
          layerId: pluginId,
          event: {
            title: event.type,
            subtitle: event.query.queryHash,
            time: api.now(),
            data: {
              queryHash: event.query.queryHash,
              ...event
            }
          }
        });
      }
    });
    api.on.getInspectorTree(payload => {
      if (payload.inspectorId === pluginId) {
        const queries = queryCache.getAll();
        const settings = api.getSettings();
        const filtered = payload.filter ? queries.filter(item => rankItem(item.queryHash, payload.filter).passed) : [...queries];
        const sorted = filtered.sort((a, b) => sortFns[settings.sortFn](a, b) * settings.baseSort);
        const nodes = sorted.map(query => {
          const stateLabel = getQueryStateLabel(query);
          return {
            id: query.queryHash,
            label: query.queryHash,
            tags: [{
              label: stateLabel + " [" + query.getObserversCount() + "]",
              textColor: getQueryStatusFg(query),
              backgroundColor: getQueryStatusBg(query)
            }]
          };
        });
        payload.rootNodes = nodes;
      }
    });
    api.on.getInspectorState(payload => {
      if (payload.inspectorId === pluginId) {
        const query = queryCache.get(payload.nodeId);

        if (!query) {
          return;
        }

        payload.state = {
          ' Query Details': [{
            key: 'Query key',
            value: query.queryHash
          }, {
            key: 'Query status',
            value: getQueryStateLabel(query)
          }, {
            key: 'Observers',
            value: query.getObserversCount()
          }, {
            key: 'Last Updated',
            value: new Date(query.state.dataUpdatedAt).toLocaleTimeString()
          }],
          'Data Explorer': [{
            key: 'Data',
            value: query.state.data
          }],
          'Query Explorer': [{
            key: 'Query',
            value: query
          }]
        };
      }
    });
  });
}

export { setupDevtools };
//# sourceMappingURL=devtools.mjs.map
