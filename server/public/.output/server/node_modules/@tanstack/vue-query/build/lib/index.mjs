export * from '@tanstack/query-core';
export { useQueryClient } from './useQueryClient.mjs';
export { VueQueryPlugin } from './vueQueryPlugin.mjs';
export { QueryClient } from './queryClient.mjs';
export { QueryCache } from './queryCache.mjs';
export { MutationCache } from './mutationCache.mjs';
export { useQuery } from './useQuery.mjs';
export { useQueries } from './useQueries.mjs';
export { useInfiniteQuery } from './useInfiniteQuery.mjs';
export { useMutation } from './useMutation.mjs';
export { useIsFetching } from './useIsFetching.mjs';
export { useIsMutating } from './useIsMutating.mjs';
export { VUE_QUERY_CLIENT } from './utils.mjs';
//# sourceMappingURL=index.mjs.map
