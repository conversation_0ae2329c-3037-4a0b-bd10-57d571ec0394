import { computed, ref, watch, onScopeDispose, unref } from 'vue-demi';
import { useQueryClient } from './useQueryClient.mjs';
import { isQueryKey, cloneDeepUnref } from './utils.mjs';

function useIsFetching(arg1, arg2) {
  var _filters$value$queryC;

  const filters = computed(() => parseFilterArgs(arg1, arg2));
  const queryClient = (_filters$value$queryC = filters.value.queryClient) != null ? _filters$value$queryC : useQueryClient(filters.value.queryClientKey);
  const isFetching = ref(queryClient.isFetching(filters));
  const unsubscribe = queryClient.getQueryCache().subscribe(() => {
    isFetching.value = queryClient.isFetching(filters);
  });
  watch(filters, () => {
    isFetching.value = queryClient.isFetching(filters);
  }, {
    deep: true
  });
  onScopeDispose(() => {
    unsubscribe();
  });
  return isFetching;
}
function parseFilterArgs(arg1, arg2 = {}) {
  const plainArg1 = unref(arg1);
  const plainArg2 = unref(arg2);
  let options = plainArg1;

  if (isQueryKey(plainArg1)) {
    options = { ...plainArg2,
      queryKey: plainArg1
    };
  } else {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    options = plainArg1 || {};
  }

  return cloneDeepUnref(options);
}

export { parseFilterArgs, useIsFetching };
//# sourceMappingURL=useIsFetching.mjs.map
