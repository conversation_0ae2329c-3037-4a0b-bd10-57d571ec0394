import { isRef, unref } from 'vue-demi';

/* eslint-disable @typescript-eslint/no-explicit-any */
const VUE_QUERY_CLIENT = 'VUE_QUERY_CLIENT';
function getClientKey(key) {
  const suffix = key ? ":" + key : '';
  return "" + VUE_QUERY_CLIENT + suffix;
}
function isQueryKey(value) {
  return Array.isArray(value);
}
function isMutationKey(value) {
  return Array.isArray(value);
}
function updateState(state, update) {
  Object.keys(state).forEach(key => {
    state[key] = update[key];
  });
}
function cloneDeep(value, customizer) {
  if (customizer) {
    const result = customizer(value);

    if (result !== undefined || isRef(value)) {
      return result;
    }
  }

  if (Array.isArray(value)) {
    return value.map(val => cloneDeep(val, customizer));
  }

  if (typeof value === 'object' && isPlainObject(value)) {
    const entries = Object.entries(value).map(([key, val]) => [key, cloneDeep(val, customizer)]);
    return Object.fromEntries(entries);
  }

  return value;
}
function cloneDeepUnref(obj) {
  return cloneDeep(obj, val => {
    if (isRef(val)) {
      return cloneDeepUnref(unref(val));
    }
  });
}

function isPlainObject(value) {
  if (Object.prototype.toString.call(value) !== '[object Object]') {
    return false;
  }

  const prototype = Object.getPrototypeOf(value);
  return prototype === null || prototype === Object.prototype;
}

export { VUE_QUERY_CLIENT, cloneDeep, cloneDeepUnref, getClientKey, isMutationKey, isQueryKey, updateState };
//# sourceMappingURL=utils.mjs.map
