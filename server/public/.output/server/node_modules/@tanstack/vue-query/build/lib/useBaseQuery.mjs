import { computed, reactive, watch, onScopeDispose, toRefs, readonly, unref } from 'vue-demi';
import { useQueryClient } from './useQueryClient.mjs';
import { updateState, isQueryKey, cloneDeepUnref } from './utils.mjs';

function useBaseQuery(Observer, arg1, arg2 = {}, arg3 = {}) {
  var _options$value$queryC;

  const options = computed(() => parseQueryArgs(arg1, arg2, arg3));
  const queryClient = (_options$value$queryC = options.value.queryClient) != null ? _options$value$queryC : useQueryClient(options.value.queryClientKey);
  const defaultedOptions = computed(() => {
    const defaulted = queryClient.defaultQueryOptions(options.value);
    defaulted._optimisticResults = queryClient.isRestoring.value ? 'isRestoring' : 'optimistic';
    return defaulted;
  });
  const observer = new Observer(queryClient, defaultedOptions.value);
  const state = reactive(observer.getCurrentResult());

  let unsubscribe = () => {// noop
  };

  watch(queryClient.isRestoring, isRestoring => {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!isRestoring) {
      unsubscribe();
      unsubscribe = observer.subscribe(result => {
        updateState(state, result);
      });
    }
  }, {
    immediate: true
  });
  watch(defaultedOptions, () => {
    observer.setOptions(defaultedOptions.value);
    updateState(state, observer.getCurrentResult());
  }, {
    deep: true
  });
  onScopeDispose(() => {
    unsubscribe();
  });

  const suspense = () => {
    return new Promise(resolve => {
      let stopWatch = () => {//noop
      };

      const run = () => {
        if (defaultedOptions.value.enabled !== false) {
          const optimisticResult = observer.getOptimisticResult(defaultedOptions.value);

          if (optimisticResult.isStale) {
            stopWatch();
            resolve(observer.fetchOptimistic(defaultedOptions.value));
          } else {
            stopWatch();
            resolve(optimisticResult);
          }
        }
      };

      run();
      stopWatch = watch(defaultedOptions, run, {
        deep: true
      });
    });
  };

  return { ...toRefs(readonly(state)),
    suspense
  };
}
function parseQueryArgs(arg1, arg2 = {}, arg3 = {}) {
  const plainArg1 = unref(arg1);
  const plainArg2 = unref(arg2);
  const plainArg3 = unref(arg3);
  let options = plainArg1;

  if (!isQueryKey(plainArg1)) {
    options = plainArg1;
  } else if (typeof plainArg2 === 'function') {
    options = { ...plainArg3,
      queryKey: plainArg1,
      queryFn: plainArg2
    };
  } else {
    options = { ...plainArg2,
      queryKey: plainArg1
    };
  }

  return cloneDeepUnref(options);
}

export { parseQueryArgs, useBaseQuery };
//# sourceMappingURL=useBaseQuery.mjs.map
