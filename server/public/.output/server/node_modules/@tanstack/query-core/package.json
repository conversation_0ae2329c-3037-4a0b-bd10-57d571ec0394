{"name": "@tanstack/query-core", "version": "4.32.0", "description": "The framework agnostic core that powers TanStack Query", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": "tanstack/query", "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "types": "build/lib/index.d.ts", "main": "build/lib/index.js", "module": "build/lib/index.esm.js", "exports": {".": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.mjs", "default": "./build/lib/index.js"}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build/lib/*", "build/umd/*", "src"], "scripts": {"clean": "rimraf ./build", "test:eslint": "eslint --ext .ts,.tsx ./src", "test:types": "tsc", "test:lib": "jest --config ./jest.config.ts", "test:lib:dev": "pnpm run test:lib --watch", "build:types": "tsc --build"}}