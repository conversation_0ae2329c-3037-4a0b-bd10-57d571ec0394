{"name": "color", "version": "0.11.4", "description": "Color conversion and manipulation with CSS string support", "keywords": ["color", "colour", "css"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON>", "<PERSON>"], "license": "MIT", "repository": "Qix-/color", "xo": {"rules": {"no-cond-assign": 0, "new-cap": 0}}, "files": ["CHANGELOG.md", "LICENSE", "index.js"], "scripts": {"pretest": "xo", "test": "mocha"}, "dependencies": {"clone": "^1.0.2", "color-convert": "^1.3.0", "color-string": "^0.3.0"}, "devDependencies": {"mocha": "^2.2.5", "xo": "^0.12.1"}}