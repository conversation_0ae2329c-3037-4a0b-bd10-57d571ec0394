{"name": "markmap-html-parser", "version": "0.17.1", "description": "Parse HTML into markmap data structure", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"clean": "del-cli dist", "build:types": "tsc", "build:js": "vite build && TARGET=browserJs vite build", "build": "run-s clean build:*", "prepublishOnly": "run-s build", "test": "jest test"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "main": "dist/index.js", "module": "dist/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist"], "keywords": ["markdown", "markmap", "mindmap"], "homepage": "https://github.com/markmap/markmap/packages/markmap-html-parser#readme", "repository": {"type": "git", "url": "git+https://github.com/markmap/markmap.git"}, "bugs": {"url": "https://github.com/markmap/markmap/issues"}, "devDependencies": {"markmap-common": "0.17.1"}, "dependencies": {"@babel/runtime": "^7.22.6", "cheerio": "1.0.0-rc.12"}, "peerDependencies": {"markmap-common": "*"}, "jest": {"transform": {"\\.ts$": ["babel-jest", {"rootMode": "upward"}]}}, "gitHead": "628f06a60d0c06f4d2f70b6df786ecb8b4124241"}