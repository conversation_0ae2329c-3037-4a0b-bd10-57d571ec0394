import { load } from "cheerio";
import { walkTree } from "markmap-common";
var Levels = /* @__PURE__ */ ((Levels2) => {
  Levels2[Levels2["None"] = 0] = "None";
  Levels2[Levels2["H1"] = 1] = "H1";
  Levels2[Levels2["H2"] = 2] = "H2";
  Levels2[Levels2["H3"] = 3] = "H3";
  Levels2[Levels2["H4"] = 4] = "H4";
  Levels2[Levels2["H5"] = 5] = "H5";
  Levels2[Levels2["H6"] = 6] = "H6";
  Levels2[Levels2["Block"] = 7] = "Block";
  Levels2[Levels2["List"] = 8] = "List";
  Levels2[Levels2["ListItem"] = 9] = "ListItem";
  return Levels2;
})(Levels || {});
const defaultSelectorRules = {
  "div,p": ({ $node }) => ({
    queue: $node.children()
  }),
  "h1,h2,h3,h4,h5,h6": ({ $node, getContent }) => ({
    ...getContent($node.contents())
  }),
  "ul,ol": ({ $node }) => ({
    queue: $node.children(),
    nesting: true
  }),
  li: ({ $node, getContent }) => {
    const queue = $node.children().filter("ul,ol");
    let content;
    if ($node.contents().first().is("div,p")) {
      content = getContent($node.children().first());
    } else {
      let $contents = $node.contents();
      const i = $contents.index(queue);
      if (i >= 0)
        $contents = $contents.slice(0, i);
      content = getContent($contents);
    }
    return {
      queue,
      nesting: true,
      ...content
    };
  },
  "table,pre,p>img:only-child": ({ $node, getContent }) => ({
    ...getContent($node)
  })
};
const defaultOptions = {
  selector: "h1,h2,h3,h4,h5,h6,ul,ol,li,table,pre,p>img:only-child",
  selectorRules: defaultSelectorRules
};
const MARKMAP_COMMENT_PREFIX = "markmap: ";
const SELECTOR_HEADING = /^h[1-6]$/;
const SELECTOR_LIST = /^[uo]l$/;
const SELECTOR_LIST_ITEM = /^li$/;
function getLevel(tagName) {
  if (SELECTOR_HEADING.test(tagName))
    return +tagName[1];
  if (SELECTOR_LIST.test(tagName))
    return 8;
  if (SELECTOR_LIST_ITEM.test(tagName))
    return 9;
  return 7;
}
function parseHtml(html, opts) {
  const options = {
    ...defaultOptions,
    ...opts
  };
  const $ = load(html);
  const $root = $("body");
  let id = 0;
  const rootNode = {
    id,
    tag: "",
    html: "",
    level: 0,
    parent: 0,
    childrenLevel: 0,
    children: []
  };
  const headingStack = [];
  let skippingHeading = 0;
  checkNodes($root.children());
  return rootNode;
  function addChild(props) {
    var _a;
    const { parent } = props;
    const node = {
      id: ++id,
      tag: props.tagName,
      level: props.level,
      html: props.html,
      childrenLevel: 0,
      children: props.nesting ? [] : void 0,
      parent: parent.id
    };
    if ((_a = props.comments) == null ? void 0 : _a.length) {
      node.comments = props.comments;
    }
    if (Object.keys(props.data || {}).length) {
      node.data = props.data;
    }
    if (parent.children) {
      if (parent.childrenLevel === 0 || parent.childrenLevel > node.level) {
        parent.children = [];
        parent.childrenLevel = node.level;
      }
      if (parent.childrenLevel === node.level) {
        parent.children.push(node);
      }
    }
    return node;
  }
  function getCurrentHeading(level) {
    let heading;
    while ((heading = headingStack.at(-1)) && heading.level >= level) {
      headingStack.pop();
    }
    return heading || rootNode;
  }
  function getContent($node) {
    var _a;
    const result = extractMagicComments($node);
    const html2 = (_a = $.html(result.$node)) == null ? void 0 : _a.trimEnd();
    return { comments: result.comments, html: html2 };
  }
  function extractMagicComments($node) {
    const comments = [];
    $node = $node.filter((_, child) => {
      if (child.type === "comment") {
        const data = child.data.trim();
        if (data.startsWith(MARKMAP_COMMENT_PREFIX)) {
          comments.push(data.slice(MARKMAP_COMMENT_PREFIX.length).trim());
          return false;
        }
      }
      return true;
    });
    return { $node, comments };
  }
  function checkNodes($els, node) {
    $els.each((_, child) => {
      var _a;
      const $child = $(child);
      const rule = (_a = Object.entries(options.selectorRules).find(
        ([selector]) => $child.is(selector)
      )) == null ? void 0 : _a[1];
      const result = rule == null ? void 0 : rule({ $node: $child, $, getContent });
      if ((result == null ? void 0 : result.queue) && !result.nesting) {
        checkNodes(result.queue, node);
        return;
      }
      const level = getLevel(child.tagName);
      if (!result) {
        if (level <= 6) {
          skippingHeading = level;
        }
        return;
      }
      if (skippingHeading > 0 && level > skippingHeading)
        return;
      if (!$child.is(options.selector))
        return;
      skippingHeading = 0;
      const isHeading = level <= 6;
      let data = $child.data();
      if ($child.children("code:only-child").length) {
        data = {
          ...data,
          ...$child.children().data()
        };
      }
      const childNode = addChild({
        parent: node || getCurrentHeading(level),
        nesting: !!result.queue || isHeading,
        tagName: child.tagName,
        level,
        html: result.html || "",
        comments: result.comments,
        data
      });
      if (isHeading)
        headingStack.push(childNode);
      if (result.queue)
        checkNodes(result.queue, childNode);
    });
  }
}
function convertNode(htmlRoot) {
  return walkTree(htmlRoot, (htmlNode, next) => {
    const node = {
      content: htmlNode.html,
      children: next() || []
    };
    if (htmlNode.data) {
      node.payload = {
        ...htmlNode.data
      };
    }
    if (htmlNode.comments) {
      if (htmlNode.comments.includes("foldAll")) {
        node.payload = { ...node.payload, fold: 2 };
      } else if (htmlNode.comments.includes("fold")) {
        node.payload = { ...node.payload, fold: 1 };
      }
    }
    return node;
  });
}
function buildTree(html, opts) {
  const htmlRoot = parseHtml(html, opts);
  return convertNode(htmlRoot);
}
export {
  Levels,
  buildTree,
  convertNode,
  defaultOptions,
  parseHtml
};
