{"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "3.0.2", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "MIT", "repository": "jsdom/data-urls", "main": "lib/parser.js", "files": ["lib/"], "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"abab": "^2.0.6", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^11.0.0"}, "devDependencies": {"@domenic/eslint-config": "^2.0.0", "eslint": "^8.14.0", "jest": "^27.5.1", "minipass-fetch": "^2.1.0"}, "engines": {"node": ">=12"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/(?!(abab/lib/atob.js))"]}}