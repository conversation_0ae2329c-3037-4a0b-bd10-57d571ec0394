{"name": "@vue/reactivity", "version": "3.5.13", "description": "@vue/reactivity", "main": "index.js", "module": "dist/reactivity.esm-bundler.js", "types": "dist/reactivity.d.ts", "unpkg": "dist/reactivity.global.js", "jsdelivr": "dist/reactivity.global.js", "files": ["index.js", "dist"], "exports": {".": {"types": "./dist/reactivity.d.ts", "node": {"production": "./dist/reactivity.cjs.prod.js", "development": "./dist/reactivity.cjs.js", "default": "./dist/reactivity.cjs.prod.js"}, "module": "./dist/reactivity.esm-bundler.js", "import": "./dist/reactivity.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/reactivity"}, "buildOptions": {"name": "VueReactivity", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/reactivity#readme", "dependencies": {"@vue/shared": "3.5.13"}}