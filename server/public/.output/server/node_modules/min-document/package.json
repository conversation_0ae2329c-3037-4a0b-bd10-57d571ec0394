{"name": "min-document", "version": "2.19.0", "description": "A minimal DOM implementation", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/min-document.git", "main": "index", "homepage": "https://github.com/Raynos/min-document", "contributors": [{"name": "<PERSON><PERSON>"}], "bugs": {"url": "https://github.com/Raynos/min-document/issues", "email": "<EMAIL>"}, "dependencies": {"dom-walk": "^0.1.0"}, "devDependencies": {"run-browser": "git://github.com/Raynos/run-browser", "tap-dot": "^0.2.1", "tap-spec": "^0.1.8", "tape": "^2.12.3"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/min-document/raw/master/LICENSE"}], "scripts": {"test": "node ./test/index.js | tap-spec", "dot": "node ./test/index.js | tap-dot", "cover": "istanbul cover --report none --print detail ./test/index.js", "view-cover": "istanbul report html && google-chrome ./coverage/index.html", "browser": "run-browser test/index.js", "phantom": "run-browser test/index.js -b | tap-spec"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}