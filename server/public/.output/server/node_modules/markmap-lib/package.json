{"name": "markmap-lib", "version": "0.17.2", "description": "Visualize your Markdown as mindmaps with Mark<PERSON><PERSON>", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"clean": "del-cli dist", "build:types": "tsc", "build:js": "vite build && TARGET=nodeLight vite build && TARGET=browserEs vite build && TARGET=browserJs vite build", "build": "run-s clean build:*", "prepublishOnly": "run-s build", "test": "jest test"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/index.mjs", "unpkg": "dist/browser/index.iife.js", "jsdelivr": "dist/browser/index.iife.js", "exports": {".": {"types": "./dist/index.d.ts", "browser": "./dist/browser/index.mjs", "require": "./dist/index.js", "default": "./dist/index.mjs"}, "./plugins": {"types": "./dist/plugins/index.d.ts", "require": "./dist/plugins.js", "default": "./dist/plugins.mjs"}, "./no-plugins": {"types": "./dist/index.d.ts", "require": "./dist/index.no-plugins.js", "default": "./dist/index.no-plugins.mjs"}, "./package.json": "./package.json"}, "types": "dist/index.d.ts", "files": ["dist"], "keywords": ["markdown", "markmap", "mindmap"], "homepage": "https://github.com/markmap/markmap/packages/markmap-lib#readme", "repository": {"type": "git", "url": "git+https://github.com/markmap/markmap.git"}, "bugs": {"url": "https://github.com/markmap/markmap/issues"}, "devDependencies": {"@highlightjs/cdn-assets": "^11.8.0", "@types/katex": "^0.16.7", "@types/markdown-it": "^14.0.0", "markmap-common": "0.17.1", "webfontloader": "^1.6.28"}, "dependencies": {"@babel/runtime": "^7.22.6", "@iktakahiro/markdown-it-katex": "^4.0.1", "highlight.js": "^11.8.0", "katex": "^0.16.8", "markdown-it": "^14.1.0", "markdown-it-ins": "^4.0.0", "markdown-it-mark": "^4.0.0", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markmap-html-parser": "0.17.1", "markmap-view": "0.17.2", "prismjs": "^1.29.0", "yaml": "^2.5.1"}, "peerDependencies": {"markmap-common": "*"}, "jest": {"transform": {"\\.ts$": ["babel-jest", {"rootMode": "upward"}], "\\.svg$": "<rootDir>/scripts/file-transform.js"}, "moduleNameMapper": {"^(.*\\.svg)\\?raw$": "$1"}}, "gitHead": "bbeec32d0efeea4e36755b1909a5993187264487"}