<!-- Generated by SpecStory -->

# Untitled (2025-07-03 06:24Z)

_**User**_

在豆包接口联网模式对话时，点击停止，有时候会出现这个报错，请修复CBeYYh-l.js:2   GET http://cs.zhikufeng.com/api/user/center net::ERR_CONNECTION_TIMED_OUT
(匿名) @ CBeYYh-l.js:2
o @ CBeYYh-l.js:2
(匿名) @ CBeYYh-l.js:38
request @ CBeYYh-l.js:38
get @ CBeYYh-l.js:38
Gd @ CBeYYh-l.js:17
getUser @ CBeYYh-l.js:17
Z @ CBeYYh-l.js:12
(匿名) @ Defc3Xrj.js:1
(匿名) @ CBeYYh-l.js:38
dispatchEvent @ CBeYYh-l.js:38
triggerEvent @ CBeYYh-l.js:38
abort @ CBeYYh-l.js:38
u.onPause.t.<computed>.t.<computed> @ Defc3Xrj.js:1
Qt @ uahP8ofS.js:13
He @ uahP8ofS.js:13
jf @ uahP8ofS.js:14
s.showPause.s.showContinue.s.loading.h.onClick.u.<computed>.u.<computed> @ CNyvtBtb.js:1
Qt @ uahP8ofS.js:13
He @ uahP8ofS.js:13
jf @ uahP8ofS.js:14
handleClick @ CBeYYh-l.js:36
Qt @ uahP8ofS.js:13
He @ uahP8ofS.js:13
n @ uahP8ofS.js:18
Defc3Xrj.js:1  Uncaught (in promise) FetchError: [GET] "/api/user/center": <no response> Failed to fetchCaused by: TypeError: Failed to fetch
    at CBeYYh-l.js:2:11907
    at s.o [as raw] (CBeYYh-l.js:2:10667)
    at CBeYYh-l.js:38:12241
    at new Promise (<anonymous>)
    at mV.request (CBeYYh-l.js:38:12203)
    at mV.get (CBeYYh-l.js:38:10933)
    at Gd (CBeYYh-l.js:17:2979)
    at Proxy.getUser (CBeYYh-l.js:17:22079)
    at Proxy.Z (CBeYYh-l.js:12:2203)
    at Defc3Xrj.js:1:5498
    at CBeYYh-l.js:38:8130
    at Array.forEach (<anonymous>)
    at fV.dispatchEvent (CBeYYh-l.js:38:8118)
    at fV.triggerEvent (CBeYYh-l.js:38:9790)
    at fV.abort (CBeYYh-l.js:38:10643)
    at u.onPause.t.<computed>.t.<computed> (Defc3Xrj.js:1:10194)
await
(匿名) @ CBeYYh-l.js:38
request @ CBeYYh-l.js:38
get @ CBeYYh-l.js:38
Gd @ CBeYYh-l.js:17
getUser @ CBeYYh-l.js:17
Z @ CBeYYh-l.js:12
(匿名) @ Defc3Xrj.js:1
(匿名) @ CBeYYh-l.js:38
dispatchEvent @ CBeYYh-l.js:38
triggerEvent @ CBeYYh-l.js:38
abort @ CBeYYh-l.js:38
u.onPause.t.<computed>.t.<computed> @ Defc3Xrj.js:1
Qt @ uahP8ofS.js:13
He @ uahP8ofS.js:13
jf @ uahP8ofS.js:14
s.showPause.s.showContinue.s.loading.h.onClick.u.<computed>.u.<computed> @ CNyvtBtb.js:1
Qt @ uahP8ofS.js:13
He @ uahP8ofS.js:13
jf @ uahP8ofS.js:14
handleClick @ CBeYYh-l.js:36
Qt @ uahP8ofS.js:13
He @ uahP8ofS.js:13
n @ uahP8ofS.js:18

---

