<!-- Generated by SpecStory -->

# H5端智能体重排模型保存问题 (2025-07-22 13:26Z)

_**User**_

h5端，智能体设置页面，重排模型保存后不生效，重新打开后，还是需要重新选择重排模型

---

_**Assistant**_

我来帮您解决H5端智能体设置页面重排模型保存后不生效的问题。首先让我了解一下当前的项目结构和相关代码。

---

Tool call timed out after 10s (codebase search)

---

Tool call timed out after 5s (file search)

---

Tool call timed out after 5s (list files)

---

<details>
            <summary>Grep search for "智能体设置" • **4** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/pc/src/pages/application/robot/setting.vue` | L116 | `title: '智能体设置'` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/pc/src/pages/application/robot/setting.vue` | L120 | `name: '智能体设置',` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/uniapp/src/packages/pages/robot_info/robot_info.vue` | L45 | `name: '智能体设置',` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/yuanshi/pc/src/pages/application/robot/setting.vue` | L116 | `title: '智能体设置'` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/yuanshi/pc/src/pages/application/robot/setting.vue` | L120 | `name: '智能体设置',` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/yuanshi/uniapp/src/packages/pages/robot_info/robot_info.vue` | L45 | `name: '智能体设置',` |

</details>

---

<details>
            <summary>Grep search for "重排模型" • **10** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/new/pc/src/pages/application/robot/_components/app-edit/search-config.vue` | L112 | `<el-form-item label="重排模型" prop="vl_models" v-if="formData.ranking_status === 1">` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/new/admin/src/views/ai_setting/ai_model/index.vue` | L117 | `name: '重排模型配置',` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/backup/20250722_210925_ranking_model_fix/model-picker/index.vue` | L2 | `<!--    向量模型/重排模型    -->` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/backup/20250722_210925_ranking_model_fix/model-picker/index.vue` | L268 | `// 对于向量模型和重排模型，直接查找主模型` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/backup/20250722_210925_ranking_model_fix/model-picker/index.vue` | L326 | `// 向量模型和重排模型直接设置主模型` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/backup/20250721_150825_pc_modelpicker_fix/model-picker/index.vue` | L2 | `<!--    向量模型/重排模型    -->` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/pc/src/pages/application/robot/_components/app-edit/search-config.vue` | L112 | `<el-form-item label="重排模型" prop="vl_models" v-if="formData.ranking_status === 1">` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/admin/src/views/ai_setting/ai_model/index.vue` | L117 | `name: '重排模型配置',` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/uniapp/src/components/model-picker/model-picker.vue` | L170 | `<!-- 向量模型和重排模型：直接显示主模型 -->` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/uniapp/src/components/model-picker/model-picker.vue` | L299 | `// 向量模型和重排模型直接查找主模型` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/uniapp/src/components/model-picker/model-picker.vue` | L349 | `// 向量模型和重排模型直接设置主模型` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/uniapp/src/components/model-picker/model-picker.vue` | L362 | `// 向量模型和重排模型不使用subModel` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/uniapp/src/components/model-picker/model-picker.vue` | L389 | `// 向量模型和重排模型直接检查主模型` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/uniapp/src/packages/pages/robot_info/component/robot-setting/search-config.vue` | L162 | `<u-form-item v-if="formData.ranking_status === 1" label="重排模型" prop="ranking_model" required>` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/uniapp/src/packages/pages/robot_info/component/robot-setting/search-config.vue` | L174 | `<text class="text-[#888]" v-else>请选择重排模型</text>` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/yuanshi/admin/src/views/ai_setting/ai_model/index.vue` | L117 | `name: '重排模型配置',` |
| `vscode-remote://ssh-remote%2B182.92.119.42/www/wwwroot/ai/yuanshi/pc/src/pages/application/robot/_components/app-edit/search-config.vue` | L112 | `<el-form-item label="重排模型" prop="vl_models" v-if="formData.ranking_status === 1">` |

</details>

---

现在我找到了相关文件，让我查看H5端（uniapp）的智能体设置页面代码：

---

Read file: uniapp/src/packages/pages/robot_info/component/robot-setting/search-config.vue

---



---

