<!-- Generated by SpecStory -->

# Debugging 404 errors in API requests (2025-07-18 03:21Z)

_**User**_

index.25c46c99.js:1 Object
/favicon.ico:1   Failed to load resource: the server responded with a status of 404 (Not Found)
/adminapi/setting/notice/list?page=1&limit=10:1   Failed to load resource: the server responded with a status of 404 (Not Found)
/adminapi/setting.setting.bulletin/detail:1   Failed to load resource: the server responded with a status of 404 (Not Found)
index.031a31e4.js:1  获取公告列表失败: N
C @ index.031a31e4.js:1
index.031a31e4.js:1  获取公告配置失败: N
v @ index.031a31e4.js:1
index.25c46c99.js:1  Uncaught (in promise) N
index.25c46c99.js:1  Uncaught (in promise) N
axios.c3b81d20.js:2   GET http://cs.zhikufeng.com/adminapi/setting.setting.bulletin/detail 404 (Not Found)
(匿名) @ axios.c3b81d20.js:2
Y @ axios.c3b81d20.js:2
tt @ axios.c3b81d20.js:2
Promise.then
_.request @ axios.c3b81d20.js:2
(匿名) @ axios.c3b81d20.js:1
(匿名) @ index.25c46c99.js:1
request @ index.25c46c99.js:1
get @ index.25c46c99.js:1
n @ notice.300c63f9.js:1
v @ index.031a31e4.js:1
H @ index.031a31e4.js:1
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
Hl @ @vue.a11433a6.js:16
g @ element-plus.e8677eae.js:9
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
n @ @vue.a11433a6.js:28
index.031a31e4.js:1  获取公告配置失败: N {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
v @ index.031a31e4.js:1
await in v
H @ index.031a31e4.js:1
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
Hl @ @vue.a11433a6.js:16
g @ element-plus.e8677eae.js:9
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
n @ @vue.a11433a6.js:28
index.25c46c99.js:1  Uncaught (in promise) N {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
responseInterceptorsCatchHook @ index.25c46c99.js:1
(匿名) @ index.25c46c99.js:1
Promise.then
_.request @ axios.c3b81d20.js:2
(匿名) @ axios.c3b81d20.js:1
(匿名) @ index.25c46c99.js:1
request @ index.25c46c99.js:1
get @ index.25c46c99.js:1
n @ notice.300c63f9.js:1
v @ index.031a31e4.js:1
H @ index.031a31e4.js:1
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
Hl @ @vue.a11433a6.js:16
g @ element-plus.e8677eae.js:9
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
n @ @vue.a11433a6.js:28
axios.c3b81d20.js:2   POST http://cs.zhikufeng.com/adminapi/setting.setting.bulletin/save 404 (Not Found)
(匿名) @ axios.c3b81d20.js:2
Y @ axios.c3b81d20.js:2
tt @ axios.c3b81d20.js:2
Promise.then
_.request @ axios.c3b81d20.js:2
(匿名) @ axios.c3b81d20.js:1
(匿名) @ index.25c46c99.js:1
request @ index.25c46c99.js:1
post @ index.25c46c99.js:1
i @ notice.300c63f9.js:1
S @ index.031a31e4.js:1
await in S
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
Hl @ @vue.a11433a6.js:16
g @ element-plus.e8677eae.js:9
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
n @ @vue.a11433a6.js:28
index.031a31e4.js:1  保存公告配置失败: N {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
S @ index.031a31e4.js:1
await in S
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
Hl @ @vue.a11433a6.js:16
g @ element-plus.e8677eae.js:9
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
n @ @vue.a11433a6.js:28
index.25c46c99.js:1  Uncaught (in promise) N {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
responseInterceptorsCatchHook @ index.25c46c99.js:1
(匿名) @ index.25c46c99.js:1
Promise.then
_.request @ axios.c3b81d20.js:2
(匿名) @ axios.c3b81d20.js:1
(匿名) @ index.25c46c99.js:1
request @ index.25c46c99.js:1
post @ index.25c46c99.js:1
i @ notice.300c63f9.js:1
S @ index.031a31e4.js:1
await in S
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
Hl @ @vue.a11433a6.js:16
g @ element-plus.e8677eae.js:9
Ue @ @vue.a11433a6.js:16
we @ @vue.a11433a6.js:16
n @ @vue.a11433a6.js:28


---

_**Assistant**_

我来帮您分析并解决这些404错误问题。首先让我查看项目的结构和相关文件，了解问题的根源。


---

Read file: README.md

---

现在让我查看具体的错误信息，这些404错误主要涉及公告相关的API接口。让我检查相关的路由和控制器文件：


---

<details>
            <summary>Grep search for "setting\.setting\.bulletin" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/chatmoney%20%281%29.sql` | L3914 | `(1530, 1000, 'C', '公告设置', '', 60, 'setting.setting.bulletin/detail', 'notice', 'setting/notice', '', '', 0, 1, 0, 1704857547, 1706166437),` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/chatmoney%20%281%29.sql` | L3915 | `(1531, 1530, 'A', '公共设置保存', '', 0, 'setting.setting.bulletin/save', '', '', '', '', 0, 1, 0, 1704857560, 1705548142),` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L246 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/chatmoney%20%281%29.sql` | L3914 | `(1530, 1000, 'C', '公告设置', '', 60, 'setting.setting.bulletin/detail', 'notice', 'setting/notice', '', '', 0, 1, 0, 1704857547, 1706166437),` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L247 | `⟪ 234 characters skipped ⟫7), (1531, 1530, 'A', '公共设置保存', '', 0, 'setting.setting.bulletin/save', '', '', '', '', 0, 1, 0, 1704857560, 1705548142), (2000, 0, 'M', 'AI设置',` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L248 | `⟪ 230 characters skipped ⟫7), (1531, 1530, 'A', '公共设置保存', '', 0, 'setting.setting.bulletin/save', '', '', '', '', 0, 1, 0, 1704857560, 1705548142), (2000, 0, 'M', 'AI设置',` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L249 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/chatmoneyceshi.sql` | L8887 | `(1530, 1000, 'C', '公告设置', '', 60, 'setting.setting.bulletin/detail', 'notice', 'setting/notice', '', '', 0, 1, 0, 1704857547, 1706166437),` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L258 | `(1530, 1000, 'C', '公告设置', '', 60, 'setting.setting.bulletin/detail', 'notice', 'setting/notice', '', '', 0, 1, 0, 1704857547, 1706166437)` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L408 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/chatmoney%20%281%29.sql` | L3914 | `(1530, 1000, 'C', '公告设置', '', 60, 'setting.setting.bulletin/detail', 'notice', 'setting/notice', '', '', 0, 1, 0, 1704857547, 1706166437),` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L409 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/chatmoney%20%281%29.sql` | L3915 | `(1531, 1530, 'A', '公共设置保存', '', 0, 'setting.setting.bulletin/save', '', '', '', '', 0, 1, 0, 1704857560, 1705548142),` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L410 | `⟪ 123 characters skipped ⟫acters skipped ⟫, 'C', '公告设置', '', 60, 'setting.setting.bulletin/detail', 'notice', 'setting/notice', '', '', 0, 1, 0, 1704857547, 1706166437), (1531, 1530, 'A', '公共设置保存', '', 0, 'setting.setting.bulletin/save', '', '', '', '', 0, 1, 0, 1704857560, 1705548142), (2000, 0, 'M', 'AI设置', 'el-icon-CopyDocument', 800, '', 'ai_setting', '', '', '', 0, 1, 0, 1704270497, 1704959303), (2` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L413 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L414 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L415 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L416 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L417 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L418 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L419 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L420 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L421 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L422 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L423 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L424 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L425 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L426 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L427 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L428 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L429 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L430 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L431 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L432 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L433 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L434 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L435 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L436 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L437 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L438 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L439 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L440 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L441 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L442 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L443 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L444 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L445 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L446 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L447 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L448 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L449 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L450 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L451 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L452 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L453 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L454 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L455 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L456 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L457 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L458 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L459 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L460 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L461 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L462 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L463 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L464 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L465 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L466 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L467 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L468 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L469 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L470 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L471 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L472 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L473 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L474 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L475 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L476 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L477 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L478 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L479 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L480 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L481 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L482 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L483 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L484 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L485 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L486 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L487 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L488 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L489 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L490 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L491 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L492 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L493 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L494 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L495 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L496 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L497 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L498 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L499 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L500 | `⟪ 104 characters skipped ⟫acters skipped ⟫\"sort\":60,\"perms\":\"setting.setting.bulletin\\/detail\",\"paths\":\"notice\",\"component\":\"setting\\/notice\",\"selected\":\"\",\"params\":\"\",\"is_cache\":0,\"is_show\":1,\"is_disable\":0,\"create_time\":\"2024-01-10` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L513 | `- `setting.setting.bulletin/detail`（查看公告详情接口）` |

</details>

---

现在让我查看具体的路由配置和控制器文件：


---

<details>
            <summary>Searched codebase "setting.setting.bulletin controller" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `server/app/adminapi/controller/setting/BulletinController.php` | L1-50 |
| `server/app/adminapi/logic/setting/BulletinLogic.php` | L1-54 |
| `server/app/adminapi/controller/ppt/SettingController.php` | L1-66 |
| `server/app/adminapi/controller/search/SettingController.php` | L1-60 |
| `admin/src/views/setting/notice/index.vue` | L158-234 |
| `server/app/adminapi/controller/notice/NoticeController.php` | L27-67 |
| `admin/src/api/setting/notice.ts` | L6-11 |
| `server/app/adminapi/controller/channel/PcSettingController.php` | L1-48 |
| `.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L561-613 |
| `.specstory/history/2025-07-15_00-29Z-frontend-error-missing-component-notice.md` | L1448-1513 |
| `admin/src/views/setting/notice/index.vue` | L34-74 |
| `admin/src/api/setting/notice.ts` | L1-6 |
| `.specstory/history/2025-07-15_00-29Z-frontend-error-missing-component-notice.md` | L1513-1551 |
| `.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L522-561 |
| `server/app/adminapi/controller/setting/WebsiteController.php` | L26-104 |
| `server/app/adminapi/controller/setting/VoiceController.php` | L1-47 |
| `server/app/adminapi/controller/channel/AppSettingController.php` | L1-50 |
| `server/app/adminapi/controller/setting/WebsiteController.php` | L1-26 |
| `server/public/admin/assets/notice.300c63f9.js` | L1-1 |
| `server/app/adminapi/controller/setting/ShareSettingController.php` | L1-58 |
| `server/app/adminapi/controller/channel/WebPageSettingController.php` | L1-51 |
| `server/app/adminapi/controller/setting/CustomerController.php` | L1-50 |
| `.specstory/history/2025-06-18_02-45Z-debugging-gift-management-save-errors.md` | L2591-2601 |
| `server/app/adminapi/controller/setting/UserController.php` | L25-75 |
| `server/app/adminapi/controller/channel/OfficialAccountSettingController.php` | L1-51 |

</details>

---

现在让我查看路由配置文件，了解API路由的配置：


---

Tool call timed out after 5s (file search)

---

<details>
            <summary>Listed directory server/route • **1** results</summary>
        
| Name |
|-------|
| 📄 `app.php` |

</details>

---

Read file: server/route/app.php

---

现在让我查看ThinkPHP的路由配置，通常在config目录下：


---

<details>
            <summary>Listed directory server/config • **17** results</summary>
        
| Name |
|-------|
| 📄 `ai.php` |
| 📄 `project.php` |
| 📄 `console.php` |
| 📄 `cache.php` |
| 📄 `database.php` |
| 📄 `cookie.php` |
| 📄 `filesystem.php` |
| 📄 `install.lock` |
| 📄 `lang.php` |
| 📄 `log.php` |
| 📄 `middleware.php` |
| 📄 `queue.php` |
| 📄 `route.php` |
| 📄 `session.php` |
| 📄 `trace.php` |
| 📄 `view.php` |
| 📄 `app.php` |

</details>

---

Read file: server/config/route.php

---

现在让我查看具体的API路由配置，通常在adminapi目录下：


---

<details>
            <summary>Listed directory server/app/adminapi • **9** results</summary>
        
| Name |
|-------|
| 📁 `controller` |
| 📁 `validate` |
| 📁 `service` |
| 📁 `logic` |
| 📁 `lists` |
| 📄 `event.php` |
| 📁 `http` |
| 📁 `listener` |
| 📁 `config` |

</details>

---

Read file: server/app/adminapi/event.php

---

现在让我查看ThinkPHP的自动路由机制，通常是通过控制器和方法名自动生成路由。让我检查一下BulletinController是否存在：


---

Read file: server/app/adminapi/controller/setting/BulletinController.php

---

现在让我检查前端API调用的具体实现：


---

Read file: admin/src/api/setting/notice.ts

---

现在我明白问题了！前端API调用的是 `/setting.setting.bulletin/detail` 和 `/setting.setting.bulletin/save`，但是根据ThinkPHP的自动路由机制，这个路径应该对应的是 `setting/setting/bulletin` 控制器，而不是 `setting/bulletin` 控制器。

让我检查是否存在 `setting/setting/bulletin` 控制器：


---

<details>
            <summary>Listed directory server/app/adminapi/controller/setting • **17** results</summary>
        
| Name |
|-------|
| 📄 `SensitiveWordController.php` |
| 📄 `ShareSettingController.php` |
| 📄 `SpaceController.php` |
| 📄 `StorageController.php` |
| 📄 `UserController.php` |
| 📄 `VoiceController.php` |
| 📄 `WebsiteController.php` |
| 📁 `pay` |
| 📁 `system` |
| 📄 `BulletinController.php` |
| 📄 `ContentCensorController.php` |
| 📄 `CustomerController.php` |
| 📄 `KeyPoolController.php` |
| 📄 `KeyRuleController.php` |
| 📄 `MindmapController.php` |
| 📁 `ai` |
| 📁 `dict` |

</details>

---

确实没有 `setting/setting` 目录。问题在于前端API调用的路径不正确。根据ThinkPHP的自动路由机制，`/setting.setting.bulletin/detail` 应该对应 `setting/setting/bulletin` 控制器，但实际上应该是 `setting/bulletin` 控制器。

让我修复前端API调用路径：


---



---

现在让我检查前端的公告设置页面，确保它正确调用了这些API：


---

Read file: admin/src/views/setting/notice/index.vue

---

Read file: admin/src/views/setting/notice/index.vue

---

现在让我检查一下是否有富文本编辑器组件：


---

<details>
            <summary>Searched codebase "editor" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Listed directory admin/src/components • **25** results</summary>
        
| Name |
|-------|
| 📁 `app-link` |
| 📁 `aspect-ratio` |
| 📁 `color-picker` |
| 📁 `creative-picker` |
| 📁 `date-picker` |
| 📁 `daterange-picker` |
| 📁 `del-wrap` |
| 📁 `dict-value` |
| 📁 `editor` |
| 📁 `export-data` |
| 📁 `footer-btns` |
| 📁 `form-designer` |
| 📁 `icon` |
| 📁 `image-contain` |
| 📁 `link` |
| 📁 `markdown` |
| 📁 `material` |
| 📁 `overflow-tooltip` |
| 📁 `pagination` |
| 📁 `popover-input` |
| 📁 `popup` |
| 📁 `progress` |
| 📁 `upload` |
| 📁 `user-picker` |
| 📁 `video-player` |

</details>

---

<details>
            <summary>Listed directory admin/src/components/editor • **1** results</summary>
        
| Name |
|-------|
| 📄 `index.vue` |

</details>

---

很好！编辑器组件存在。现在让我测试一下修复后的API是否能正常工作。首先让我检查一下服务器是否正在运行：


---

```bash
cd /www/wwwroot/ai && docker ps
```

```
[root@iZ2ze776l6a0t8ydszrzbbZ ai]# cd /www/wwwroot/ai && docker ps
CONTAINER ID   IMAGE                                                           COMMAND                  CREA
TED       STATUS        PORTS                                                    NAMES
d0669f21dc85   registry.cn-guangzhou.aliyuncs.com/likeadmin/nginx:1.23.1       "/docker-entrypoint.…"   5 we
eks ago   Up 3 hours    0.0.0.0:180->80/tcp, :::180->80/tcp                      chatmoney-nginx
9e9197fa1f46   registry.cn-guangzhou.aliyuncs.com/likeadmin/pgvector:v0.4.4    "docker-entrypoint.s…"   5 we
eks ago   Up 40 hours   0.0.0.0:15432->5432/tcp, :::15432->5432/tcp              chatmoney-postgres
5a6738feb629   registry.cn-guangzhou.aliyuncs.com/likeadmin/mysql:5.7.29       "docker-entrypoint.s…"   5 we
eks ago   Up 40 hours   33060/tcp, 0.0.0.0:13306->3306/tcp, :::13306->3306/tcp   chatmoney-mysql
dca4a834c126   registry.cn-guangzhou.aliyuncs.com/likeadmin/php:8.0.30.3-fpm   "docker-php-entrypoi…"   5 we
eks ago   Up 3 hours    0.0.0.0:7314->7314/tcp, :::7314->7314/tcp, 9000/tcp      chatmoney-php
210784faab05   registry.cn-guangzhou.aliyuncs.com/likeadmin/redis:7.4.0        "docker-entrypoint.s…"   5 we
eks ago   Up 40 hours   6379/tcp                                                 chatmoney-redis
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---

很好！Docker容器都在运行。现在让我测试一下修复后的API接口：


---

```bash
curl -X GET "http://localhost:180/adminapi/setting.bulletin/detail" -H "Content-Type: application/json"
```

```
        .echo pre > pre {
            padding: 0;
            margin: 0;
        }
        /* Exception Info */
        .exception {
            margin-top: 20px;
        }
        .exception .message{
            padding: 12px;
            border: 1px solid #ddd;
            border-bottom: 0 none;
            line-height: 18px;
            font-size:16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .code{
            float: left;
            text-align: center;
            color: #fff;
            margin-right: 12px;
            padding: 16px;
            border-radius: 4px;
            background: #999;
        }
        .exception .source-code{
            padding: 6px;
            border: 1px solid #ddd;

            background: #f9f9f9;
            overflow-x: auto;

        }
        .exception .source-code pre{
            margin: 0;
        }
        .exception .source-code pre ol{
            margin: 0;
            color: #4288ce;
            display: inline-block;
            min-width: 100%;
            box-sizing: border-box;
            font-size:14px;
            font-family: "Century Gothic",Consolas,"Liberation Mono",Courier,Verdana,serif;
            padding-left: 40px;
        }
        .exception .source-code pre li{
            border-left: 1px solid #ddd;
            height: 18px;
            line-height: 18px;
        }
        .exception .source-code pre code{
            color: #333;
            height: 100%;
            display: inline-block;
            border-left: 1px solid #fff;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace{
            padding: 6px;
            border: 1px solid #ddd;
            border-top: 0 none;
            line-height: 16px;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace h2:hover {
            text-decoration: underline;
            cursor: pointer;
        }
        .exception .trace ol{
            margin: 12px;
        }
        .exception .trace ol li{
            padding: 2px 4px;
        }
        .exception div:last-child{
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        /* Exception Variables */
        .exception-var table{
            width: 100%;
            margin: 12px 0;
            box-sizing: border-box;
            table-layout:fixed;
            word-wrap:break-word;
        }
        .exception-var table caption{
            text-align: left;
            font-size: 16px;
            font-weight: bold;
            padding: 6px 0;
        }
        .exception-var table caption small{
            font-weight: 300;
            display: inline-block;
            margin-left: 10px;
            color: #ccc;
        }
        .exception-var table tbody{
            font-size: 13px;
            font-family: Consolas, "Liberation Mono", Courier, "微软雅黑",serif;
        }
        .exception-var table td{
            padding: 0 6px;
            vertical-align: top;
            word-break: break-all;
        }
        .exception-var table td:first-child{
            width: 28%;
            font-weight: bold;
            white-space: nowrap;
        }
        .exception-var table td pre{
            margin: 0;
        }

        /* Copyright Info */
        .copyright{
            margin-top: 24px;
            padding: 12px 0;
            border-top: 1px solid #eee;
        }

        /* SPAN elements with the classes below are added by prettyprint. */
        pre.prettyprint .pln { color: #000 }  /* plain text */
        pre.prettyprint .str { color: #080 }  /* string content */
        pre.prettyprint .kwd { color: #008 }  /* a keyword */
        pre.prettyprint .com { color: #800 }  /* a comment */
        pre.prettyprint .typ { color: #606 }  /* a type name */
        pre.prettyprint .lit { color: #066 }  /* a literal value */
        /* punctuation, lisp open bracket, lisp close bracket */
        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }
        pre.prettyprint .tag { color: #008 }  /* a markup tag name */
        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */
        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */
        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */
        pre.prettyprint .fun { color: red }  /* a function name */
    </style>
</head>
<body>
                <div class="exception">
        <div class="message">
            <div class="info">
                <div>
                    <h2>#0 [0]<abbr title="think\exception\HttpException">HttpException</abbr> in <a class="
toggle" title="/server/vendor/topthink/framework/src/helper.php line 53">helper.php line 53</a></h2>
                </div>
                <div><h1>页面不存在</h1></div>
            </div>
        </div>
                            <div class="source-code">
                    <pre class="prettyprint lang-php"><ol start="44"><li class="line-0-44"><code>     * @par
am integer|Response $code    状态码 或者 Response对象实例
</code></li><li class="line-0-45"><code>     * @param string           $message 错误信息
</code></li><li class="line-0-46"><code>     * @param array            $header  参数
</code></li><li class="line-0-47"><code>     */
</code></li><li class="line-0-48"><code>    function abort($code, string $message = '', array $header = [])
</code></li><li class="line-0-49"><code>    {
</code></li><li class="line-0-50"><code>        if ($code instanceof Response) {
</code></li><li class="line-0-51"><code>            throw new HttpResponseException($code);
</code></li><li class="line-0-52"><code>        } else {
</code></li><li class="line-0-53 line-error"><code>            throw new HttpException($code, $message, null
, $header);
</code></li><li class="line-0-54"><code>        }
</code></li><li class="line-0-55"><code>    }
</code></li><li class="line-0-56"><code>}
</code></li><li class="line-0-57"><code>
</code></li><li class="line-0-58"><code>if (!function_exists('app')) {
</code></li><li class="line-0-59"><code>    /**
</code></li><li class="line-0-60"><code>     * 快速获取容器中的实例 支持依赖注入
</code></li><li class="line-0-61"><code>     * @template T
</code></li><li class="line-0-62"><code>     * @param string|class-string&lt;T&gt; $name        类名或标识 
默认获取当前应用实例
</code></li></ol></pre>
                </div>
                    <div class="trace">
            <h2 data-expand="1">Call Stack</h2>
            <ol>
                <li>in <a class="toggle" title="/server/vendor/topthink/framework/src/helper.php line 53">he
lper.php line 53</a></li>
                                    <li>
                        at abort(404, '页面不存在') in <a class="toggle" title="/server/app/adminapi/http/mi
ddleware/AdminIpMiddleware.php line 55">AdminIpMiddleware.php line 55</a>                    </li>
                                    <li>
                        at <abbr title="app\adminapi\http\middleware\AdminIpMiddleware">AdminIpMiddleware</a
bbr>->handle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure
">Closure</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\adminapi\http\middleware\AdminIp
Middleware">AdminIpMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>),
 <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/
framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Route.php l
ine 794">Route.php line 794</a>                    </li>
                                    <li>
                        at <abbr title="think\Route">Route</abbr>->dispatch(<em>object</em>(<abbr title="app
\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title
="/server/vendor/topthink/framework/src/think/Http.php line 216">Http.php line 216</a>                    </
li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->dispatchToRoute(<em>object</em>(<abbr title
="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http
.php line 206">Http.php line 206</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->think\{closure}(<em>object</em>(<abbr title
="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Pipe
line.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-multi-app/
src/MultiApp.php line 51">MultiApp.php line 51</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->think\app\{closure}(<em>object<
/em>(<abbr title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framewor
k/src/think/Pipeline.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-multi-app/src/MultiApp.ph
p line 52">MultiApp.php line 52</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->handle(<em>object</em>(<abbr ti
tle="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>))                   
 </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="think\app\MultiApp">MultiApp</abbr>)
, 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure
">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Middleware.php li
ne 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/app/common/http/middleware/BaseM
iddleware.php line 29">BaseMiddleware.php line 29</a>                    </li>
                                    <li>
                        at <abbr title="app\common\http\middleware\BaseMiddleware">BaseMiddleware</abbr>->ha
ndle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closur
e</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\common\http\middleware\BaseMiddl
eware">BaseMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>obj
ect</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framewor
k/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/app/common/http/middleware/LikeA
dminAllowMiddleware.php line 47">LikeAdminAllowMiddleware.php line 47</a>                    </li>
                                    <li>
                        at <abbr title="app\common\http\middleware\LikeAdminAllowMiddleware">LikeAdminAllowM
iddleware</abbr>->handle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr ti
tle="Closure">Closure</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\common\http\middleware\LikeAdmin
AllowMiddleware">LikeAdminAllowMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Requ
est</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vend
or/topthink/framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li
>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-trace/src/
TraceDebug.php line 71">TraceDebug.php line 71</a>                    </li>
                                    <li>
                        at <abbr title="think\trace\TraceDebug">TraceDebug</abbr>->handle(<em>object</em>(<a
bbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>))             
       </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="think\trace\TraceDebug">TraceDebug</
abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Middleware.
php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http.php li
ne 207">Http.php line 207</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->runWithRequest(<em>object</em>(<abbr title=
"app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http.
php line 170">Http.php line 170</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->run() in <a class="toggle" title="/server/p
ublic/index.php line 29">index.php line 29</a>                    </li>
                            </ol>
        </div>
    </div>
                
    
        <div class="exception-var">
        <h2>Environment Variables</h2>
                <table>
                        <caption>GET Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>POST Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Files<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Cookies<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Session<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Server/Request Data</caption>
            <tbody>
                                <tr>
                    <td>HOSTNAME</td>
                    <td>dca4a834c126</td>
                </tr>
                                <tr>
                    <td>PHP_VERSION</td>
                    <td>8.0.30</td>
                </tr>
                                <tr>
                    <td>PHP_INI_DIR</td>
                    <td>/usr/local/etc/php</td>
                </tr>
                                <tr>
                    <td>GPG_KEYS</td>
                    <td>1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F 2C
16C765DBE54A088130F1BC4B9B5F600B55F3B4 39B641343D8C104B2B146DC3F9C39DC0B9698544</td>
                </tr>
                                <tr>
                    <td>PHP_LDFLAGS</td>
                    <td>-Wl,-O1 -pie</td>
                </tr>
                                <tr>
                    <td>PWD</td>
                    <td>/server</td>
                </tr>
                                <tr>
                    <td>TZ</td>
                    <td>Asia/Shanghai</td>
                </tr>
                                <tr>
                    <td>HOME</td>
                    <td>/</td>
                </tr>
                                <tr>
                    <td>PHP_SHA256</td>
                    <td>216ab305737a5d392107112d618a755dc5df42058226f1670e9db90e77d777d9</td>
                </tr>
                                <tr>
                    <td>PHPIZE_DEPS</td>
                    <td>autoconf                dpkg-dev                file            g++             gcc 
libc-dev                make            pkg-config              re2c</td>
                </tr>
                                <tr>
                    <td>PHP_URL</td>
                    <td>https://www.php.net/distributions/php-8.0.30.tar.xz</td>
                </tr>
                                <tr>
                    <td>SHLVL</td>
                    <td>0</td>
                </tr>
                                <tr>
                    <td>PHP_CFLAGS</td>
                    <td>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<
/td>
                </tr>
                                <tr>
                    <td>PATH</td>
                    <td>/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin</td>
                </tr>
                                <tr>
                    <td>PHP_ASC_URL</td>
                    <td>https://www.php.net/distributions/php-8.0.30.tar.xz.asc</td>
                </tr>
                                <tr>
                    <td>PHP_CPPFLAGS</td>
                    <td>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<
/td>
                </tr>
                                <tr>
                    <td>_</td>
                    <td>/usr/local/sbin/php-fpm</td>
                </tr>
                                <tr>
                    <td>HTTP_CONTENT_TYPE</td>
                    <td>application/json</td>
                </tr>
                                <tr>
                    <td>HTTP_ACCEPT</td>
                    <td>*/*</td>
                </tr>
                                <tr>
                    <td>HTTP_USER_AGENT</td>
                    <td>curl/7.61.1</td>
                </tr>
                                <tr>
                    <td>HTTP_HOST</td>
                    <td>localhost:180</td>
                </tr>
                                <tr>
                    <td>REDIRECT_STATUS</td>
                    <td>200</td>
                </tr>
                                <tr>
                    <td>SERVER_NAME</td>
                    <td>www.chatmoney.localhost</td>
                </tr>
                                <tr>
                    <td>SERVER_PORT</td>
                    <td>80</td>
                </tr>
                                <tr>
                    <td>SERVER_ADDR</td>
                    <td>**********</td>
                </tr>
                                <tr>
                    <td>REMOTE_PORT</td>
                    <td>58030</td>
                </tr>
                                <tr>
                    <td>REMOTE_ADDR</td>
                    <td>**********</td>
                </tr>
                                <tr>
                    <td>SERVER_SOFTWARE</td>
                    <td>nginx/1.23.1</td>
                </tr>
                                <tr>
                    <td>GATEWAY_INTERFACE</td>
                    <td>CGI/1.1</td>
                </tr>
                                <tr>
                    <td>REQUEST_SCHEME</td>
                    <td>http</td>
                </tr>
                                <tr>
                    <td>SERVER_PROTOCOL</td>
                    <td>HTTP/1.1</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_ROOT</td>
                    <td>/etc/nginx/html</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_URI</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_URI</td>
                    <td>/adminapi/setting.bulletin/detail</td>
                </tr>
                                <tr>
                    <td>SCRIPT_NAME</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>CONTENT_LENGTH</td>
                    <td></td>
                </tr>
                                <tr>
                    <td>CONTENT_TYPE</td>
                    <td>application/json</td>
                </tr>
                                <tr>
                    <td>REQUEST_METHOD</td>
                    <td>GET</td>
                </tr>
                                <tr>
                    <td>QUERY_STRING</td>
                    <td>s=adminapi/setting.bulletin/detail</td>
                </tr>
                                <tr>
                    <td>SCRIPT_FILENAME</td>
                    <td>/server/public/index.php</td>
                </tr>
                                <tr>
                    <td>FCGI_ROLE</td>
                    <td>RESPONDER</td>
                </tr>
                                <tr>
                    <td>PHP_SELF</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME_FLOAT</td>
                    <td>1752814171.7459</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME</td>
                    <td>1752814171</td>
                </tr>
                                <tr>
                    <td>argv</td>
                    <td>[
    &quot;s=adminapi\/setting.bulletin\/detail&quot;
]</td>
                </tr>
                                <tr>
                    <td>argc</td>
                    <td>1</td>
                </tr>
                            </tbody>
                    </table>
            </div>
    
    <div class="copyright">
        <a title="官方网站" href="http://www.thinkphp.cn">ThinkPHP</a> 
        <span>V6.1.4</span> 
        <span>{ 十年磨一剑-为API开发设计的高性能框架 }</span>
        <span>- <a title="官方手册" href="https://www.kancloud.cn/manual/thinkphp6_0/content">官方手册</a></
span>
    </div>
        <script>
        function $(selector, node){
            var elements;

            node = node || document;
            if(document.querySelectorAll){
                elements = node.querySelectorAll(selector);
            } else {
                switch(selector.substr(0, 1)){
                    case '#':
                        elements = [node.getElementById(selector.substr(1))];
                        break;
                    case '.':
                        if(document.getElementsByClassName){
                            elements = node.getElementsByClassName(selector.substr(1));
                        } else {
                            elements = get_elements_by_class(selector.substr(1), node);
                        }
                        break;
                    default:
                        elements = node.getElementsByTagName();
                }
            }
            return elements;

            function get_elements_by_class(search_class, node, tag) {
                var elements = [], eles, 
                    pattern  = new RegExp('(^|\\s)' + search_class + '(\\s|$)');

                node = node || document;
                tag  = tag  || '*';

                eles = node.getElementsByTagName(tag);
                for(var i = 0; i < eles.length; i++) {
                    if(pattern.test(eles[i].className)) {
                        elements.push(eles[i])
                    }
                }

                return elements;
            }
        }

        $.getScript = function(src, func){
            var script = document.createElement('script');
            
            script.async  = 'async';
            script.src    = src;
            script.onload = func || function(){};
            
            $('head')[0].appendChild(script);
        }

        ;(function(){
            var files = $('.toggle');
            var ol    = $('ol', $('.prettyprint')[0]);
            var li    = $('li', ol[0]);   

            // 短路径和长路径变换
            for(var i = 0; i < files.length; i++){
                files[i].ondblclick = function(){
                    var title = this.title;

                    this.title = this.innerHTML;
                    this.innerHTML = title;
                }
            }

            (function () {
                var expand = function (dom, expand) {
                    var ol = $('ol', dom.parentNode)[0];
                    expand = undefined === expand ? dom.attributes['data-expand'].value === '0' : undefined;
                    if (expand) {
                        dom.attributes['data-expand'].value = '1';
                        ol.style.display = 'none';
                        dom.innerText = 'Call Stack (展开)';
                    } else {
                        dom.attributes['data-expand'].value = '0';
                        ol.style.display = 'block';
                        dom.innerText = 'Call Stack (折叠)';
                    }
                };
                var traces = $('.trace');
                for (var i = 0; i < traces.length; i ++) {
                    var h2 = $('h2', traces[i])[0];
                    expand(h2);
                    h2.onclick = function () {
                        expand(this);
                    };
                }
            })();

            $.getScript('//cdn.bootcdn.net/ajax/libs/prettify/r298/prettify.min.js', function(){
                prettyPrint();
            });
        })();
    </script>
    <div id="think_page_trace" style="position: fixed;bottom:0;right:0;font-size:14px;width:100%;z-index: 99
9999;color: #000;text-align:left;font-family:'微软雅黑';">
    <div id="think_page_trace_tab" style="display: none;background:white;margin:0;height: 250px;">
        <div id="think_page_trace_tab_tit" style="height:30px;padding: 6px 12px 0;border-bottom:1px solid #e
cecec;border-top:1px solid #ececec;font-size:16px">
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">基本</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">文件</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">流程</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">错误</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">SQL</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">调试</span>
                    </div>
        <div id="think_page_trace_tab_cont" style="overflow:auto;height:212px;padding:0;line-height: 24px">
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                    <li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">请求信息 : 2025-0
7-18 12:49:31 HTTP/1.1 GET : http://localhost:180/adminapi/setting.bulletin/detail</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">运行时间 : 0.057692s [ 吞吐率：17.33req/s ] 内存消耗：2,61
5.48kb 文件加载：145</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">查询信息 : 0
 queries</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">缓存信息 : 0 reads,0 wri
tes</li>                </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                    <li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/public/in
dex.php ( 1.10 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendo
r/autoload.php ( 0.75 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/composer/autoload_real.php ( 1.63 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;p
adding:0 12px">/server/vendor/composer/platform_check.php ( 0.90 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/composer/ClassLoader.php ( 15.99 KB )</li><li style="bor
der-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/composer/autoload_static.php ( 38.34
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/depr
ecation-contracts/function.php ( 0.99 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;paddin
g:0 12px">/server/vendor/ralouphie/getallheaders/src/getallheaders.php ( 1.60 KB )</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzlehttp/psr7/src/functions_include.php (
 0.15 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzleh
ttp/psr7/src/functions.php ( 13.09 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0
 12px">/server/vendor/guzzlehttp/guzzle/src/functions_include.php ( 0.16 KB )</li><li style="border-bottom:1
px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzlehttp/guzzle/src/functions.php ( 5.55 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-m
bstring/bootstrap.php ( 7.33 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px"
>/server/vendor/symfony/polyfill-mbstring/bootstrap80.php ( 8.84 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/src/helper.php ( 7.35 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php80/bo
otstrap.php ( 1.50 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/v
endor/adbario/php-dot-notation/src/helpers.php ( 0.57 KB )</li><li style="border-bottom:1px solid #EEE;font-
size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php72/bootstrap.php ( 1.89 KB )</li><li style="bor
der-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-ctype/bootstrap.php
 ( 1.56 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfo
ny/polyfill-ctype/bootstrap80.php ( 1.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pad
ding:0 12px">/server/vendor/symfony/polyfill-intl-normalizer/bootstrap.php ( 0.71 KB )</li><li style="border
-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-intl-normalizer/bootst
rap80.php ( 0.70 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/think-orm/stubs/load_stubs.php ( 0.16 KB )</li><li style="border-bottom:1px solid #EEE;font-siz
e:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Exception.php ( 1.69 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Faca
de.php ( 2.71 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor
/symfony/polyfill-intl-idn/bootstrap.php ( 4.52 KB )</li><li style="border-bottom:1px solid #EEE;font-size:1
4px;padding:0 12px">/server/vendor/symfony/polyfill-php81/bootstrap.php ( 0.72 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php73/bootstrap.php ( 0.9
9 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/tra
nslation/Resources/functions.php ( 0.55 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/vendor/ezyang/htmlpurifier/library/HTMLPurifier.composer.php ( 0.10 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/mtdowling/jmespath.php/src/JmesPa
th.php ( 0.36 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor
/overtrue/socialite/src/Contracts/FactoryInterface.php ( 0.47 KB )</li><li style="border-bottom:1px solid #E
EE;font-size:14px;padding:0 12px">/server/vendor/overtrue/socialite/src/Contracts/UserInterface.php ( 1.04 K
B )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/overtrue/socia
lite/src/Contracts/ProviderInterface.php ( 2.89 KB )</li><li style="border-bottom:1px solid #EEE;font-size:1
4px;padding:0 12px">/server/vendor/alibabacloud/client/src/Functions.php ( 5.33 KB )</li><li style="border-b
ottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/qcloud/cos-sdk-v5/src/Common.php ( 2.40 K
B )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/qiniu/php-sdk/
src/Qiniu/functions.php ( 8.20 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/qiniu/php-sdk/src/Qiniu/Config.php ( 3.56 KB )</li><li style="border-bottom:1px solid #EEE
;font-size:14px;padding:0 12px">/server/vendor/ramsey/uuid/src/functions.php ( 5.03 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/rmccue/requests/library/Deprecated.ph
p ( 0.53 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/rmcc
ue/requests/src/Autoload.php ( 9.12 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/vendor/symfony/var-dumper/Resources/functions/dump.php ( 0.79 KB )</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-queue/src/common.php ( 1.01 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/fram
ework/src/think/App.php ( 14.17 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12
px">/server/vendor/topthink/framework/src/think/Container.php ( 15.38 KB )</li><li style="border-bottom:1px 
solid #EEE;font-size:14px;padding:0 12px">/server/vendor/psr/container/src/ContainerInterface.php ( 1.02 KB 
)</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/provider.php ( 0.19
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/fra
mework/src/think/Http.php ( 6.12 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 1
2px">/server/vendor/topthink/think-helper/src/helper/Str.php ( 7.28 KB )</li><li style="border-bottom:1px so
lid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Env.php ( 4.64 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/common.php ( 15.43 KB )</
li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/
src/helper.php ( 18.44 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serv
er/vendor/topthink/framework/src/think/Config.php ( 5.03 KB )</li><li style="border-bottom:1px solid #EEE;fo
nt-size:14px;padding:0 12px">/server/config/ai.php ( 37.72 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/config/app.php ( 1.02 KB )</li><li style="border-bottom:1px solid #EE
E;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/facade/Env.php ( 1.67 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/cache.php ( 1.25 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/console.php ( 1.76
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/cookie.php (
 0.56 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/databas
e.php ( 3.21 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/
filesystem.php ( 0.63 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/config/lang.php ( 0.81 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/config/log.php ( 1.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/
server/config/middleware.php ( 0.19 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/config/project.php ( 8.82 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/framework/src/think/facade/Request.php ( 8.92 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/Request.php ( 0.14 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Reque
st.php ( 54.04 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/c
ommon/enum/VoiceEnum.php ( 6.26 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12
px">/server/config/queue.php ( 1.23 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/config/route.php ( 1.54 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/config/session.php ( 0.57 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14p
x;padding:0 12px">/server/config/trace.php ( 0.34 KB )</li><li style="border-bottom:1px solid #EEE;font-size
:14px;padding:0 12px">/server/config/view.php ( 0.82 KB )</li><li style="border-bottom:1px solid #EEE;font-s
ize:14px;padding:0 12px">/server/app/event.php ( 0.35 KB )</li><li style="border-bottom:1px solid #EEE;font-
size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Event.php ( 6.96 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/service.php ( 0.13 KB )</li><li style
="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/AppService.php ( 1.00 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/th
ink/Service.php ( 1.67 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serv
er/vendor/topthink/framework/src/think/Lang.php ( 7.60 KB )</li><li style="border-bottom:1px solid #EEE;font
-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/lang/zh-cn.php ( 12.88 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/init
ializer/Error.php ( 3.27 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/vendor/topthink/framework/src/think/initializer/RegisterService.php ( 1.33 KB )</li><li style="border-b
ottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/services.php ( 0.20 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/servi
ce/PaginatorService.php ( 1.52 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/topthink/framework/src/think/service/ValidateService.php ( 0.99 KB )</li><li style="border
-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/service/Mo
delService.php ( 1.76 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/topthink/think-multi-app/src/Service.php ( 1.08 KB )</li><li style="border-bottom:1px solid #EEE;fo
nt-size:14px;padding:0 12px">/server/vendor/topthink/think-queue/src/queue/Service.php ( 1.24 KB )</li><li s
tyle="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-trace/src/Se
rvice.php ( 0.77 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/framework/src/think/Middleware.php ( 6.78 KB )</li><li style="border-bottom:1px solid #EEE;font
-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/initializer/BootService.php ( 0.77 KB
 )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-
orm/src/Paginator.php ( 11.80 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/framework/src/think/Validate.php ( 46.10 KB )</li><li style="border-bottom:1px sol
id #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/Model.php ( 25.28 KB )</li><li 
style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/src/
contract/Arrayable.php ( 0.09 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/think-helper/src/contract/Jsonable.php ( 0.13 KB )</li><li style="border-bottom:1p
x solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/Attribute.ph
p ( 17.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/top
think/think-orm/src/model/concern/RelationShip.php ( 26.12 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/ModelEvent.php ( 2.27 KB 
)</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-o
rm/src/model/concern/TimeStamp.php ( 5.70 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/Conversion.php ( 10.41 KB )</li><li style=
"border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Db.
php ( 2.87 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/to
pthink/think-orm/src/DbManager.php ( 8.49 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/framework/src/think/Log.php ( 8.50 KB )</li><li style="border-bottom:1
px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Manager.php ( 3.98 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/psr/log/Psr/L
og/LoggerInterface.php ( 3.04 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/framework/src/think/Cache.php ( 4.79 KB )</li><li style="border-bottom:1px solid #
EEE;font-size:14px;padding:0 12px">/server/vendor/psr/simple-cache/src/CacheInterface.php ( 4.50 KB )</li><l
i style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/sr
c/helper/Arr.php ( 15.54 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/vendor/topthink/framework/src/think/cache/driver/Redis.php ( 6.72 KB )</li><li style="border-bottom:1px
 solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/cache/Driver.php ( 8.
06 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/f
ramework/src/think/contract/CacheHandlerInterface.php ( 2.25 KB )</li><li style="border-bottom:1px solid #EE
E;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Console.php ( 22.65 KB )</li><l
i style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/middleware.php ( 0.21 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework
/src/think/Pipeline.php ( 2.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/topthink/think-trace/src/TraceDebug.php ( 2.94 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/app/common/http/middleware/LikeAdminAllowMiddleware.php ( 1.96 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/common/http/midd
leware/BaseMiddleware.php ( 1.08 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 1
2px">/server/vendor/topthink/think-multi-app/src/MultiApp.php ( 7.07 KB )</li><li style="border-bottom:1px s
olid #EEE;font-size:14px;padding:0 12px">/server/app/adminapi/config/route.php ( 1.35 KB )</li><li style="bo
rder-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/adminapi/event.php ( 0.95 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/th
ink/Route.php ( 23.96 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/topthink/framework/src/think/route/RuleName.php ( 5.33 KB )</li><li style="border-bottom:1px solid 
#EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/Domain.php ( 5.55 KB )
</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framewor
k/src/think/route/RuleGroup.php ( 13.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/vendor/topthink/framework/src/think/route/Rule.php ( 22.85 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/RuleIte
m.php ( 8.81 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/
topthink/framework/src/think/route/dispatch/Url.php ( 3.42 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/dispatch/Controller.php ( 6
.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/
framework/src/think/route/Dispatch.php ( 6.49 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14p
x;padding:0 12px">/server/app/adminapi/http/middleware/AdminIpMiddleware.php ( 5.26 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/facade/C
onfig.php ( 1.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/framework/src/think/exception/HttpException.php ( 1.20 KB )</li><li style="border-bottom:1px so
lid #EEE;font-size:14px;padding:0 12px">/server/app/ExceptionHandle.php ( 2.11 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/exception/Han
dle.php ( 10.02 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vend
or/topthink/framework/src/think/Response.php ( 8.60 KB )</li><li style="border-bottom:1px solid #EEE;font-si
ze:14px;padding:0 12px">/server/vendor/topthink/framework/src/tpl/think_exception.tpl ( 17.03 KB )</li><li s
tyle="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/thin
k/facade/App.php ( 2.73 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/ser
ver/vendor/topthink/framework/src/think/response/Html.php ( 0.98 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Cookie.php ( 6.28 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-trace/s
rc/Html.php ( 4.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/v
endor/topthink/framework/src/think/log/driver/File.php ( 6.17 KB )</li><li style="border-bottom:1px solid #E
EE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/contract/LogHandlerInterface.p
hp ( 0.86 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/top
think/framework/src/think/log/Channel.php ( 6.54 KB )</li>                </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                    </div>
    </div>
    <div id="think_page_trace_close" style="display:none;text-align:right;height:15px;position:absolute;top:
10px;right:12px;cursor:pointer;"><img style="vertical-align:top;" src="data:image/gif;base64,R0lGODlhDwAPAJE
AAAAAAAMDA////wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4
gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCA
yMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmR
mLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzE
uMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmN
vbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1
NOkluc3RhbmNlSUQ9InhtcC5paWQ6MUQxMjc1MUJCQUJDMTFFMTk0OUVGRjc3QzU4RURFNkEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ
6MUQxMjc1MUNCQUJDMTFFMTk0OUVGRjc3QzU4RURFNkEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo
xRDEyNzUxOUJBQkMxMUUxOTQ5RUZGNzdDNThFREU2QSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxRDEyNzUxQUJBQkMxMUUxOTQ5RUZ
GNzdDNThFREU2QSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgH//v3
8+/r5+Pf29fTz8vHw7+7t7Ovq6ejn5uXk4+Lh4N/e3dzb2tnY19bV1NPS0dDPzs3My8rJyMfGxcTDwsHAv769vLu6ubi3trW0s7KxsK+uray
rqqmop6alpKOioaCfnp2cm5qZmJeWlZSTkpGQj46NjIuKiYiHhoWEg4KBgH9+fXx7enl4d3Z1dHNycXBvbm1sa2ppaGdmZWRjYmFgX15dXFt
aWVhXVlVUU1JRUE9OTUxLSklIR0ZFRENCQUA/Pj08Ozo5ODc2NTQzMjEwLy4tLCsqKSgnJiUkIyIhIB8eHRwbGhkYFxYVFBMSERAPDg0MCwo
JCAcGBQQDAgEAACH5BAAAAAAALAAAAAAPAA8AAAIdjI6JZqotoJPR1fnsgRR3C2jZl3Ai9aWZZooV+RQAOw==" /></div>
</div>
<div id="think_page_trace_open" style="height:30px;float:right;text-align:right;overflow:hidden;position:fix
ed;bottom:0;right:0;color:#000;line-height:30px;cursor:pointer;">
    <div style="background:#232323;color:#FFF;padding:0 6px;float:right;line-height:30px;font-size:14px">0.0
60573s </div>
    <img width="30" style="" title="ShowPageTrace" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAA
AwCAYAAABXAvmHAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eH
BhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bW
V0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPH
JkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbi
ByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2
JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIH
htcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjVERDVENkZGQj
kyNDExRTE5REY3RDQ5RTQ2RTRDQUJCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjVERDVENzAwQjkyNDExRTE5REY3RDQ5RTQ2RTRDQU
JCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NURENUQ2RkRCOTI0MTFFMTlERjdENDlFNDZFNENBQk
IiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NURENUQ2RkVCOTI0MTFFMTlERjdENDlFNDZFNENBQkIiLz4gPC9yZGY6RGVzY3JpcHRpb2
4+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5fx6IRAAAMCElEQVR42sxae3BU1Rk/9+69+8xuNtkHJAFCSI
AkhMgjCCJQUi0GtEIVbP8Qq9LH2No6TmfaztjO2OnUdvqHFMfOVFTqIK0vUEEeqUBARCsEeYQkEPJoEvIiELLvvc9z+p27u2F3s5tsBB1OZi
ebu5dzf7/v/L7f952zMM8cWIwY+Mk2ulCp92Fnq3XvnzArr2NZnYNldDp0Gw+/OEQ4+obQn5D+4Ubb22+YOGsWi/Todh8AHglKEGkEsnHBQ1
62511GZFgW6ZCBM9/W4H3iNSQqIe09O196dLKX7d1O39OViP/wthtkND62if/wj/DbMpph8BY/m9xy8BoBmQk+mHqZQGNy4JYRwCoRbwa8l4
JXw6M+orJxpU0U6ToKy/5bQsAiTeokGKkTx46RRxxEUgrwGgF4MWNNEJCGgYTvpgnY1IJWg5RzfqLgvcIgktX0i8dmMlFA8qCQ5L0Z/WObPL
UxT1i4lWSYDISoEfBYGvM+LlMQQdkLHoWRRZ8zYQI62Thswe5WTORGwNXDcGjqeOA9AF7B8rhzsxMBEoJ8oJKaqPu4hblHMCMPwl9XeNWyb8
xkB/DDGYKfMAE6aFL7xesZ389JlgG3XHEMI6UPDOP6JHHu67T2pwNPI69mCP4rEaBDUAJaKc/AOuXiwH07VCS3w5+UQMAuF/WqGI+yFIwVNB
wemBD4r0wgQiKoFZa00sEYTwss32lA1tPwVxtc8jQ5/gWCwmGCyUD8vRT0sHBFW4GJDvZmrJFWRY1EkrGA6ZB8/10fOZSSj0E6F+BSP7xidi
IzhBmKB09lEwHPkG+UQIyEN44EBiT5vrv2uJXyPQqSqO930fxvcvwbR/+JAkD9EfASgI9EHlp6YiHO4W+cAB20SnrFqxBbNljiXf1Pl1K2S0
HCWfiog3YlAD5RGwwxK6oUjTweuVigLjyB0mX410mAFnMoVK1lvvUvgt8fUJH0JVyjuvcmg4dE5mUiFtD24AZ4qBVELxXKS+pMxN43kSdzNw
udJ+bQbLlmnxvPOQoCugSap1GnSRoG8KOiKbH+rIA0lEeSAg3y6eeQ6XI2nrYnrPM89bUTgI0Pdqvl50vlNbtZxDUBcLBK0kPd5jPziyLdoj
JIN0pq5/mdzwL4UVvVInV5ncQEPNOUxa9d0TU+CW5l+FoI0GSDKHVVSOs+0KOsZoxwOzSZNFGv0mQ9avyLCh2Hpm+70Y0YJoJVgmQv822wnD
C8Miq6VjJ5IFed0QD1YiAbT+nQE8v/RMZfmgmcCRHIIu7Bmcp39oM9fqEychcA747KxQ/AEyqQonl7hATtJmnhO2XYtgcia01aSbVMenAXrI
omPcLgEBA4liGBzFZAT8zBYqW6brI67wg8sFVhxBhwLwBP2+tqBQqqK7VJKGh/BRrfTr6nWL7nYBaZdBJHqrX3kPEPap56xwE/GvjJTRMADe
MCdcGpGXL1Xh4ZL8BDOlWkUpegfi0CeDzeA5YITzEnddv+IXL+UYCmqIvqC9UlUC/ki9FipwVjunL3yX7dOTLeXmVMAhbsGporPfyOBTm/BJ
23gTVehsvXRnSewagUfpBXF3p5pygKS7OceqTjb7h2vjr/XKm0ZofKSI2Q/J102wHzatZkJPYQ5JoKsuK+EoHJakVzubzuLQDepCKllTZi9A
G0DYg9ZLxhFaZsOu7bvlmVI5oPXJMQJcHxHClSln1apFTvAimeg48u0RWFeZW4lVcjbQWZuIQK1KozZfIDO6CSQmQQXdpBaiKZyEWThVK1uE
c6v7V7uK0ysduExPZx4vysDR+4SelhBYm0R6LBuR4PXts8MYMcJPsINo4YZCDLj0sgB0/vLpPXvA2Tn42Cv5rsLulGubzW0sEd3d4W/mJt2K
ck+DzDMijfPLOjyrDhXSh852B+OvflqAkoyXO1cYfujtc/i3jJSAwhgfFlp20laMLOku/bC7prgqW7lCn4auE5NhcXPd3M7x70+IceSgZvNl
jCd9k3fLjYsPElqLR14PXQZqD2ZNkkrAB79UeJUebFQmXpf8ZcAQt2XrMQdyNUVBqZoUzAFyp3V3xi/MubUA/mCT4Fhf038PC8XplhWnCmnK
/ZzyC2BSTRSqKVOuY2kB8Jia0lvvRIVoP+vVWJbYarf6p655E2/nANBMCWkgD49DA0VAMyI1OLFMYCXiU9bmzi9/y5i/vsaTpHPHidTofzLb
M65vMPva9HlovgXp0AvjtaqYMfDD0/4mAsYE92pxa+9k1QgCnRVObCpojpzsKTPvayPetTEgBdwnssjuc0kOBFX+q3HwRQxdrOLAqeYRjkMk
/trTSu2Z9Lik7CfF0AvjtqAhS4NHobGXUnB5DQs8hG8p/wMX1r4+8xkmyvQ50JVq72TVeXbz3HvpWaQJi57hJYTw4kGbtS+C2TigQUtZUX+X
27QQq2ePBZBru/0lxTm8fOOQ5yaZOZMAV+he4FqIMB+LQB0UgMSajANX29j+vbmly8ipRvHeSQoQOkM5iFXcPQCVwDMs5RBCQmaPOyvbNd6u
wvQJ183BZQG3Zc+Eiv7vQOKu8YeDmMcJlt2ckyftVeMIGLBCmdMHl/tFILYwGPjXWO3zOfSq/+om+oa7Mlh2fpSsRGLp7RAW3FUVjNHgiMhy
E6zBFjM2BdkdJGO7nP1kJXWAtBuBpPIAu7f+hhu7bFXIuC5xWrf0X2xreykOsUyKkF2gwadbrXDcXrfKxR43zGcSj4t/cCgr+a1iy6EjE5GY
ktUCl9fwfMeylyooGF48bN2IGLTw8x7StS7sj8TF9FmPGWQhm3rRR+o9lhvjJvSYAdfDUevI1M6bnX/OwWaDMOQ8RPgKRo0eulBTdT8AW2kl
8e9L7UHghHwMfLiZPNoSpx0yugpQZaFqKWqxVSM3a2pN1SAhC2jf94I7ybBI7EL5A2Wvu5ht3xsoEt4+Ay/abXgCQAxyOeDsDlTCQzy75ohc
Ggv9Tra9uiymRUYTLrswOLlCdfAQf7HPDQQ4ErAH5EDXB9cMxWYpjtXApRncojS0sbV/cCgHTHwGNBJy+1PQE2x56FpaVR7wfQGZ37V+V+19
EiHNvR6q1fRUjqvbjbMq1/qfHxbTrE10ePY2gPFk48D2CVMTf1AF4PXvyYR9dV6Wf7H413m3xTWQvYGhQ7mfYwA5mAX+18Vue05v/8jG/fZX
/IW5MKPKtjSYlt0ellxh+/BOCPAwYaeVr0QofZFxJWVWC8znG70au6llVmktsF0bfHF6k8fvZ5esZJbwHwwnjg59tXz6sL/P0NUZDuSNu1mn
J8Vab17+cy005A9wtOpp3i0bZdpJLUil00semAwN45LgEViZYe3amNye0B6A9chviSlzXVsFtyN5/1H3gaNmMpn8Fz0GpYFp6Zw615H/LpUu
RQQDMCL82n5DpBSawkvzIdN2ypiT8nSLth8Pk9jnjwdFzH3W4XW6KMBfwB569NdcGX93mC16tTflcArcYUc/mFuYbV+8zY0SAjAVoNErNgWj
twumJ3wbn/HlBFYdxHvSkJJEc+Ngal9opSwyo9YlITX2C/P/+gf8sxURSLR+mcZUmeqaS9wrh6vxW5zxFCOqFi90RbDWq/YwZmnu1+a6Ovdp
vRqkNxxe44lyl4OobEnpKA6Uox5EfH9xzPs/HRKrTPWdIQrK1VZDU7ETiD3Obpl+8wPPCRBbkbwNtpW9AbBe5L1SMlj3tdTxk/9W47JUmqS5
HU+JzYymUKXjtWVmT9RenIhgXc+nroWLyxXJhmL112OdB8GCsk4f8oZJucnvmmtR85mBn10GZ0EKSCMUSAR3ukcXd5s7LvLD3me61WkuTCpJ
zYAyRurMB44EdEJzTfU271lUJC03YjXJXzYOGZwN4D8eB5jlfLrdWfzGRW7icMPfiSO6Oe7s20bmhdgLX4Z23B+s3JgQESzUDiMboSzDMHFp
NMwccGePauhfwjzwnI2wu9zKGgEFg80jcZ7MHllk07s1H+5yojtUQTlH4nFdLKTGwDmPbIklOb1L1zO4T6N8NCuDLFLS/C63c0eNRimZ++s5
BMBHxU11jHchI9oFVUxRh/eMDzHEzGYu0Lg8gJ7oS/tFCwoic44fyUtix0n/46vP4bf+//BRgAYwDDar4ncHIAAAAASUVORK5CYII=">
</div>

<script type="text/javascript">
    (function(){
        var tab_tit  = document.getElementById('think_page_trace_tab_tit').getElementsByTagName('span');
        var tab_cont = document.getElementById('think_page_trace_tab_cont').getElementsByTagName('div');
        var open     = document.getElementById('think_page_trace_open');
        var close    = document.getElementById('think_page_trace_close').children[0];
        var trace    = document.getElementById('think_page_trace_tab');
        var cookie   = document.cookie.match(/thinkphp_show_page_trace=(\d\|\d)/);
        var history  = (cookie && typeof cookie[1] != 'undefined' && cookie[1].split('|')) || [0,0];
        open.onclick = function(){
            trace.style.display = 'block';
            this.style.display = 'none';
            close.parentNode.style.display = 'block';
            history[0] = 1;
            document.cookie = 'thinkphp_show_page_trace='+history.join('|')
        }
        close.onclick = function(){
            trace.style.display = 'none';
            this.parentNode.style.display = 'none';
            open.style.display = 'block';
            history[0] = 0;
            document.cookie = 'thinkphp_show_page_trace='+history.join('|')
        }
        for(var i = 0; i < tab_tit.length; i++){
            tab_tit[i].onclick = (function(i){
                return function(){
                    for(var j = 0; j < tab_cont.length; j++){
                        tab_cont[j].style.display = 'none';
                        tab_tit[j].style.color = '#999';
                    }
                    tab_cont[i].style.display = 'block';
                    tab_tit[i].style.color = '#000';
                    history[1] = i;
                    document.cookie = 'thinkphp_show_page_trace='+history.join('|')
                }
            })(i)
        }
        parseInt(history[0]) && open.click();
        tab_tit[history[1]].click();
    })();
</script>
</body>
</html>
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---

看起来有错误。让我检查一下具体的错误信息，并尝试不同的方法：


---

```bash
cd /www/wwwroot/ai && curl -X GET "http://localhost:180/adminapi/setting.bulletin/detail" -H "Content-Type: application/json" -v
```

```
        }
        .echo table {
            width: 100%;
        }
        .echo pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f7f7f7;
            border: 0;
            border-radius: 3px;
            font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
        }
        .echo pre > pre {
            padding: 0;
            margin: 0;
        }
        /* Exception Info */
        .exception {
            margin-top: 20px;
        }
        .exception .message{
            padding: 12px;
            border: 1px solid #ddd;
            border-bottom: 0 none;
            line-height: 18px;
            font-size:16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .code{
            float: left;
            text-align: center;
            color: #fff;
            margin-right: 12px;
            padding: 16px;
            border-radius: 4px;
            background: #999;
        }
        .exception .source-code{
            padding: 6px;
            border: 1px solid #ddd;

            background: #f9f9f9;
            overflow-x: auto;

        }
        .exception .source-code pre{
            margin: 0;
        }
        .exception .source-code pre ol{
            margin: 0;
            color: #4288ce;
            display: inline-block;
            min-width: 100%;
            box-sizing: border-box;
            font-size:14px;
            font-family: "Century Gothic",Consolas,"Liberation Mono",Courier,Verdana,serif;
            padding-left: 40px;
        }
        .exception .source-code pre li{
            border-left: 1px solid #ddd;
            height: 18px;
            line-height: 18px;
        }
        .exception .source-code pre code{
            color: #333;
            height: 100%;
            display: inline-block;
            border-left: 1px solid #fff;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace{
            padding: 6px;
            border: 1px solid #ddd;
            border-top: 0 none;
            line-height: 16px;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace h2:hover {
            text-decoration: underline;
            cursor: pointer;
        }
        .exception .trace ol{
            margin: 12px;
        }
        .exception .trace ol li{
            padding: 2px 4px;
        }
        .exception div:last-child{
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        /* Exception Variables */
        .exception-var table{
            width: 100%;
            margin: 12px 0;
            box-sizing: border-box;
            table-layout:fixed;
            word-wrap:break-word;
        }
        .exception-var table caption{
            text-align: left;
            font-size: 16px;
            font-weight: bold;
            padding: 6px 0;
        }
        .exception-var table caption small{
            font-weight: 300;
            display: inline-block;
            margin-left: 10px;
            color: #ccc;
        }
        .exception-var table tbody{
            font-size: 13px;
            font-family: Consolas, "Liberation Mono", Courier, "微软雅黑",serif;
        }
        .exception-var table td{
            padding: 0 6px;
            vertical-align: top;
            word-break: break-all;
        }
        .exception-var table td:first-child{
            width: 28%;
            font-weight: bold;
            white-space: nowrap;
        }
        .exception-var table td pre{
            margin: 0;
        }

        /* Copyright Info */
        .copyright{
            margin-top: 24px;
            padding: 12px 0;
            border-top: 1px solid #eee;
        }

        /* SPAN elements with the classes below are added by prettyprint. */
        pre.prettyprint .pln { color: #000 }  /* plain text */
        pre.prettyprint .str { color: #080 }  /* string content */
        pre.prettyprint .kwd { color: #008 }  /* a keyword */
        pre.prettyprint .com { color: #800 }  /* a comment */
        pre.prettyprint .typ { color: #606 }  /* a type name */
        pre.prettyprint .lit { color: #066 }  /* a literal value */
        /* punctuation, lisp open bracket, lisp close bracket */
        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }
        pre.prettyprint .tag { color: #008 }  /* a markup tag name */
        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */
        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */
        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */
        pre.prettyprint .fun { color: red }  /* a function name */
    </style>
</head>
<body>
                <div class="exception">
        <div class="message">
            <div class="info">
                <div>
                    <h2>#0 [0]<abbr title="think\exception\HttpException">HttpException</abbr> in <a class="
toggle" title="/server/vendor/topthink/framework/src/helper.php line 53">helper.php line 53</a></h2>
                </div>
                <div><h1>页面不存在</h1></div>
            </div>
        </div>
                            <div class="source-code">
                    <pre class="prettyprint lang-php"><ol start="44"><li class="line-0-44"><code>     * @par
am integer|Response $code    状态码 或者 Response对象实例
</code></li><li class="line-0-45"><code>     * @param string           $message 错误信息
</code></li><li class="line-0-46"><code>     * @param array            $header  参数
</code></li><li class="line-0-47"><code>     */
</code></li><li class="line-0-48"><code>    function abort($code, string $message = '', array $header = [])
</code></li><li class="line-0-49"><code>    {
</code></li><li class="line-0-50"><code>        if ($code instanceof Response) {
</code></li><li class="line-0-51"><code>            throw new HttpResponseException($code);
</code></li><li class="line-0-52"><code>        } else {
</code></li><li class="line-0-53 line-error"><code>            throw new HttpException($code, $message, null
, $header);
</code></li><li class="line-0-54"><code>        }
</code></li><li class="line-0-55"><code>    }
</code></li><li class="line-0-56"><code>}
</code></li><li class="line-0-57"><code>
</code></li><li class="line-0-58"><code>if (!function_exists('app')) {
</code></li><li class="line-0-59"><code>    /**
</code></li><li class="line-0-60"><code>     * 快速获取容器中的实例 支持依赖注入
</code></li><li class="line-0-61"><code>     * @template T
</code></li><li class="line-0-62"><code>     * @param string|class-string&lt;T&gt; $name        类名或标识 
默认获取当前应用实例
</code></li></ol></pre>
                </div>
                    <div class="trace">
            <h2 data-expand="1">Call Stack</h2>
            <ol>
                <li>in <a class="toggle" title="/server/vendor/topthink/framework/src/helper.php line 53">he
lper.php line 53</a></li>
                                    <li>
                        at abort(404, '页面不存在') in <a class="toggle" title="/server/app/adminapi/http/mi
ddleware/AdminIpMiddleware.php line 55">AdminIpMiddleware.php line 55</a>                    </li>
                                    <li>
                        at <abbr title="app\adminapi\http\middleware\AdminIpMiddleware">AdminIpMiddleware</a
bbr>->handle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure
">Closure</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\adminapi\http\middleware\AdminIp
Middleware">AdminIpMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>),
 <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/
framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Route.php l
ine 794">Route.php line 794</a>                    </li>
                                    <li>
                        at <abbr title="think\Route">Route</abbr>->dispatch(<em>object</em>(<abbr title="app
\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title
="/server/vendor/topthink/framework/src/think/Http.php line 216">Http.php line 216</a>                    </
li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->dispatchToRoute(<em>object</em>(<abbr title
="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http
.php line 206">Http.php line 206</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->think\{closure}(<em>object</em>(<abbr title
="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Pipe
line.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-multi-app/
src/MultiApp.php line 51">MultiApp.php line 51</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->think\app\{closure}(<em>object<
/em>(<abbr title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framewor
k/src/think/Pipeline.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-multi-app/src/MultiApp.ph
p line 52">MultiApp.php line 52</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->handle(<em>object</em>(<abbr ti
tle="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>))                   
 </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="think\app\MultiApp">MultiApp</abbr>)
, 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure
">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Middleware.php li
ne 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/app/common/http/middleware/BaseM
iddleware.php line 29">BaseMiddleware.php line 29</a>                    </li>
                                    <li>
                        at <abbr title="app\common\http\middleware\BaseMiddleware">BaseMiddleware</abbr>->ha
ndle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closur
e</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\common\http\middleware\BaseMiddl
eware">BaseMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>obj
ect</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framewor
k/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/app/common/http/middleware/LikeA
dminAllowMiddleware.php line 47">LikeAdminAllowMiddleware.php line 47</a>                    </li>
                                    <li>
                        at <abbr title="app\common\http\middleware\LikeAdminAllowMiddleware">LikeAdminAllowM
iddleware</abbr>->handle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr ti
tle="Closure">Closure</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\common\http\middleware\LikeAdmin
AllowMiddleware">LikeAdminAllowMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Requ
est</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vend
or/topthink/framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li
>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-trace/src/
TraceDebug.php line 71">TraceDebug.php line 71</a>                    </li>
                                    <li>
                        at <abbr title="think\trace\TraceDebug">TraceDebug</abbr>->handle(<em>object</em>(<a
bbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>))             
       </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="think\trace\TraceDebug">TraceDebug</
abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Middleware.
php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http.php li
ne 207">Http.php line 207</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->runWithRequest(<em>object</em>(<abbr title=
"app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http.
php line 170">Http.php line 170</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->run() in <a class="toggle" title="/server/p
ublic/index.php line 29">index.php line 29</a>                    </li>
                            </ol>
        </div>
    </div>
                
    
        <div class="exception-var">
        <h2>Environment Variables</h2>
                <table>
                        <caption>GET Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>POST Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Files<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Cookies<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Session<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Server/Request Data</caption>
            <tbody>
                                <tr>
                    <td>HOSTNAME</td>
                    <td>dca4a834c126</td>
                </tr>
                                <tr>
                    <td>PHP_VERSION</td>
                    <td>8.0.30</td>
                </tr>
                                <tr>
                    <td>PHP_INI_DIR</td>
                    <td>/usr/local/etc/php</td>
                </tr>
                                <tr>
                    <td>GPG_KEYS</td>
                    <td>1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F 2C
16C765DBE54A088130F1BC4B9B5F600B55F3B4 39B641343D8C104B2B146DC3F9C39DC0B9698544</td>
                </tr>
                                <tr>
                    <td>PHP_LDFLAGS</td>
                    <td>-Wl,-O1 -pie</td>
                </tr>
                                <tr>
                    <td>PWD</td>
                    <td>/server</td>
                </tr>
                                <tr>
                    <td>TZ</td>
                    <td>Asia/Shanghai</td>
                </tr>
                                <tr>
                    <td>HOME</td>
                    <td>/</td>
                </tr>
                                <tr>
                    <td>PHP_SHA256</td>
                    <td>216ab305737a5d392107112d618a755dc5df42058226f1670e9db90e77d777d9</td>
                </tr>
                                <tr>
                    <td>PHPIZE_DEPS</td>
                    <td>autoconf                dpkg-dev                file            g++             gcc 
libc-dev                make            pkg-config              re2c</td>
                </tr>
                                <tr>
                    <td>PHP_URL</td>
                    <td>https://www.php.net/distributions/php-8.0.30.tar.xz</td>
                </tr>
                                <tr>
                    <td>SHLVL</td>
                    <td>0</td>
                </tr>
                                <tr>
                    <td>PHP_CFLAGS</td>
                    <td>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<
/td>
                </tr>
                                <tr>
                    <td>PATH</td>
                    <td>/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin</td>
                </tr>
                                <tr>
                    <td>PHP_ASC_URL</td>
                    <td>https://www.php.net/distributions/php-8.0.30.tar.xz.asc</td>
                </tr>
                                <tr>
                    <td>PHP_CPPFLAGS</td>
                    <td>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<
/td>
                </tr>
                                <tr>
                    <td>_</td>
                    <td>/usr/local/sbin/php-fpm</td>
                </tr>
                                <tr>
                    <td>HTTP_CONTENT_TYPE</td>
                    <td>application/json</td>
                </tr>
                                <tr>
                    <td>HTTP_ACCEPT</td>
                    <td>*/*</td>
                </tr>
                                <tr>
                    <td>HTTP_USER_AGENT</td>
                    <td>curl/7.61.1</td>
                </tr>
                                <tr>
                    <td>HTTP_HOST</td>
                    <td>localhost:180</td>
                </tr>
                                <tr>
                    <td>REDIRECT_STATUS</td>
                    <td>200</td>
                </tr>
                                <tr>
                    <td>SERVER_NAME</td>
                    <td>www.chatmoney.localhost</td>
                </tr>
                                <tr>
                    <td>SERVER_PORT</td>
                    <td>80</td>
                </tr>
                                <tr>
                    <td>SERVER_ADDR</td>
                    <td>**********</td>
                </tr>
                                <tr>
                    <td>REMOTE_PORT</td>
                    <td>54800</td>
                </tr>
                                <tr>
                    <td>REMOTE_ADDR</td>
                    <td>**********</td>
                </tr>
                                <tr>
                    <td>SERVER_SOFTWARE</td>
                    <td>nginx/1.23.1</td>
                </tr>
                                <tr>
                    <td>GATEWAY_INTERFACE</td>
                    <td>CGI/1.1</td>
                </tr>
                                <tr>
                    <td>REQUEST_SCHEME</td>
                    <td>http</td>
                </tr>
                                <tr>
                    <td>SERVER_PROTOCOL</td>
                    <td>HTTP/1.1</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_ROOT</td>
                    <td>/etc/nginx/html</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_URI</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_URI</td>
                    <td>/adminapi/setting.bulletin/detail</td>
                </tr>
                                <tr>
                    <td>SCRIPT_NAME</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>CONTENT_LENGTH</td>
                    <td></td>
                </tr>
                                <tr>
                    <td>CONTENT_TYPE</td>
                    <td>application/json</td>
                </tr>
                                <tr>
                    <td>REQUEST_METHOD</td>
                    <td>GET</td>
                </tr>
                                <tr>
                    <td>QUERY_STRING</td>
                    <td>s=adminapi/setting.bulletin/detail</td>
                </tr>
                                <tr>
                    <td>SCRIPT_FILENAME</td>
                    <td>/server/public/index.php</td>
                </tr>
                                <tr>
                    <td>FCGI_ROLE</td>
                    <td>RESPONDER</td>
                </tr>
                                <tr>
                    <td>PHP_SELF</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME_FLOAT</td>
                    <td>1752814344.1657</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME</td>
                    <td>1752814344</td>
                </tr>
                                <tr>
                    <td>argv</td>
                    <td>[
    &quot;s=adminapi\/setting.bulletin\/detail&quot;
]</td>
                </tr>
                                <tr>
                    <td>argc</td>
                    <td>1</td>
                </tr>
                            </tbody>
                    </table>
            </div>
    
    <div class="copyright">
        <a title="官方网站" href="http://www.thinkphp.cn">ThinkPHP</a> 
        <span>V6.1.4</span> 
        <span>{ 十年磨一剑-为API开发设计的高性能框架 }</span>
        <span>- <a title="官方手册" href="https://www.kancloud.cn/manual/thinkphp6_0/content">官方手册</a></
span>
    </div>
        <script>
        function $(selector, node){
            var elements;

            node = node || document;
            if(document.querySelectorAll){
                elements = node.querySelectorAll(selector);
            } else {
                switch(selector.substr(0, 1)){
                    case '#':
                        elements = [node.getElementById(selector.substr(1))];
                        break;
                    case '.':
                        if(document.getElementsByClassName){
                            elements = node.getElementsByClassName(selector.substr(1));
                        } else {
                            elements = get_elements_by_class(selector.substr(1), node);
                        }
                        break;
                    default:
                        elements = node.getElementsByTagName();
                }
            }
            return elements;

            function get_elements_by_class(search_class, node, tag) {
                var elements = [], eles, 
                    pattern  = new RegExp('(^|\\s)' + search_class + '(\\s|$)');

                node = node || document;
                tag  = tag  || '*';

                eles = node.getElementsByTagName(tag);
                for(var i = 0; i < eles.length; i++) {
                    if(pattern.test(eles[i].className)) {
                        elements.push(eles[i])
                    }
                }

                return elements;
            }
        }

        $.getScript = function(src, func){
            var script = document.createElement('script');
            
            script.async  = 'async';
            script.src    = src;
            script.onload = func || function(){};
            
            $('head')[0].appendChild(script);
        }

        ;(function(){
            var files = $('.toggle');
            var ol    = $('ol', $('.prettyprint')[0]);
            var li    = $('li', ol[0]);   

            // 短路径和长路径变换
            for(var i = 0; i < files.length; i++){
                files[i].ondblclick = function(){
                    var title = this.title;

                    this.title = this.innerHTML;
                    this.innerHTML = title;
                }
            }

            (function () {
                var expand = function (dom, expand) {
                    var ol = $('ol', dom.parentNode)[0];
                    expand = undefined === expand ? dom.attributes['data-expand'].value === '0' : undefined;
                    if (expand) {
                        dom.attributes['data-expand'].value = '1';
                        ol.style.display = 'none';
                        dom.innerText = 'Call Stack (展开)';
                    } else {
                        dom.attributes['data-expand'].value = '0';
                        ol.style.display = 'block';
                        dom.innerText = 'Call Stack (折叠)';
                    }
                };
                var traces = $('.trace');
                for (var i = 0; i < traces.length; i ++) {
                    var h2 = $('h2', traces[i])[0];
                    expand(h2);
                    h2.onclick = function () {
                        expand(this);
                    };
                }
            })();

            $.getScript('//cdn.bootcdn.net/ajax/libs/prettify/r298/prettify.min.js', function(){
                prettyPrint();
            });
        })();
    </script>
    <div id="think_page_trace" style="position: fixed;bottom:0;right:0;font-size:14px;width:100%;z-index: 99
9999;color: #000;text-align:left;font-family:'微软雅黑';">
    <div id="think_page_trace_tab" style="display: none;background:white;margin:0;height: 250px;">
        <div id="think_page_trace_tab_tit" style="height:30px;padding: 6px 12px 0;border-bottom:1px solid #e
cecec;border-top:1px solid #ececec;font-size:16px">
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">基本</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">文件</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">流程</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">错误</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">SQL</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">调试</span>
                    </div>
        <div id="think_page_trace_tab_cont" style="overflow:auto;height:212px;padding:0;line-height: 24px">
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                    <li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">请求信息 : 2025-0
7-18 12:52:24 HTTP/1.1 GET : http://localhost:180/adminapi/setting.bulletin/detail</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">运行时间 : 0.047961s [ 吞吐率：20.85req/s ] 内存消耗：2,61
5.48kb 文件加载：145</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">查询信息 : 0
 queries</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">缓存信息 : 0 reads,0 wri
tes</li>                </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                    <li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/public/in
dex.php ( 1.10 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendo
r/autoload.php ( 0.75 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/composer/autoload_real.php ( 1.63 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;p
adding:0 12px">/server/vendor/composer/platform_check.php ( 0.90 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/composer/ClassLoader.php ( 15.99 KB )</li><li style="bor
der-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/composer/autoload_static.php ( 38.34
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/depr
ecation-contracts/function.php ( 0.99 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;paddin
g:0 12px">/server/vendor/ralouphie/getallheaders/src/getallheaders.php ( 1.60 KB )</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzlehttp/psr7/src/functions_include.php (
 0.15 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzleh
ttp/psr7/src/functions.php ( 13.09 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0
 12px">/server/vendor/guzzlehttp/guzzle/src/functions_include.php ( 0.16 KB )</li><li style="border-bottom:1
px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzlehttp/guzzle/src/functions.php ( 5.55 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-m
bstring/bootstrap.php ( 7.33 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px"
>/server/vendor/symfony/polyfill-mbstring/bootstrap80.php ( 8.84 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/src/helper.php ( 7.35 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php80/bo
otstrap.php ( 1.50 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/v
endor/adbario/php-dot-notation/src/helpers.php ( 0.57 KB )</li><li style="border-bottom:1px solid #EEE;font-
size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php72/bootstrap.php ( 1.89 KB )</li><li style="bor
der-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-ctype/bootstrap.php
 ( 1.56 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfo
ny/polyfill-ctype/bootstrap80.php ( 1.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pad
ding:0 12px">/server/vendor/symfony/polyfill-intl-normalizer/bootstrap.php ( 0.71 KB )</li><li style="border
-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-intl-normalizer/bootst
rap80.php ( 0.70 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/think-orm/stubs/load_stubs.php ( 0.16 KB )</li><li style="border-bottom:1px solid #EEE;font-siz
e:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Exception.php ( 1.69 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Faca
de.php ( 2.71 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor
/symfony/polyfill-intl-idn/bootstrap.php ( 4.52 KB )</li><li style="border-bottom:1px solid #EEE;font-size:1
4px;padding:0 12px">/server/vendor/symfony/polyfill-php81/bootstrap.php ( 0.72 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php73/bootstrap.php ( 0.9
9 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/tra
nslation/Resources/functions.php ( 0.55 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/vendor/ezyang/htmlpurifier/library/HTMLPurifier.composer.php ( 0.10 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/mtdowling/jmespath.php/src/JmesPa
th.php ( 0.36 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor
/overtrue/socialite/src/Contracts/FactoryInterface.php ( 0.47 KB )</li><li style="border-bottom:1px solid #E
EE;font-size:14px;padding:0 12px">/server/vendor/overtrue/socialite/src/Contracts/UserInterface.php ( 1.04 K
B )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/overtrue/socia
lite/src/Contracts/ProviderInterface.php ( 2.89 KB )</li><li style="border-bottom:1px solid #EEE;font-size:1
4px;padding:0 12px">/server/vendor/alibabacloud/client/src/Functions.php ( 5.33 KB )</li><li style="border-b
ottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/qcloud/cos-sdk-v5/src/Common.php ( 2.40 K
B )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/qiniu/php-sdk/
src/Qiniu/functions.php ( 8.20 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/qiniu/php-sdk/src/Qiniu/Config.php ( 3.56 KB )</li><li style="border-bottom:1px solid #EEE
;font-size:14px;padding:0 12px">/server/vendor/ramsey/uuid/src/functions.php ( 5.03 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/rmccue/requests/library/Deprecated.ph
p ( 0.53 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/rmcc
ue/requests/src/Autoload.php ( 9.12 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/vendor/symfony/var-dumper/Resources/functions/dump.php ( 0.79 KB )</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-queue/src/common.php ( 1.01 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/fram
ework/src/think/App.php ( 14.17 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12
px">/server/vendor/topthink/framework/src/think/Container.php ( 15.38 KB )</li><li style="border-bottom:1px 
solid #EEE;font-size:14px;padding:0 12px">/server/vendor/psr/container/src/ContainerInterface.php ( 1.02 KB 
)</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/provider.php ( 0.19
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/fra
mework/src/think/Http.php ( 6.12 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 1
2px">/server/vendor/topthink/think-helper/src/helper/Str.php ( 7.28 KB )</li><li style="border-bottom:1px so
lid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Env.php ( 4.64 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/common.php ( 15.43 KB )</
li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/
src/helper.php ( 18.44 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serv
er/vendor/topthink/framework/src/think/Config.php ( 5.03 KB )</li><li style="border-bottom:1px solid #EEE;fo
nt-size:14px;padding:0 12px">/server/config/ai.php ( 37.72 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/config/app.php ( 1.02 KB )</li><li style="border-bottom:1px solid #EE
E;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/facade/Env.php ( 1.67 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/cache.php ( 1.25 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/console.php ( 1.76
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/cookie.php (
 0.56 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/databas
e.php ( 3.21 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/
filesystem.php ( 0.63 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/config/lang.php ( 0.81 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/config/log.php ( 1.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/
server/config/middleware.php ( 0.19 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/config/project.php ( 8.82 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/framework/src/think/facade/Request.php ( 8.92 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/Request.php ( 0.14 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Reque
st.php ( 54.04 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/c
ommon/enum/VoiceEnum.php ( 6.26 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12
px">/server/config/queue.php ( 1.23 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/config/route.php ( 1.54 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/config/session.php ( 0.57 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14p
x;padding:0 12px">/server/config/trace.php ( 0.34 KB )</li><li style="border-bottom:1px solid #EEE;font-size
:14px;padding:0 12px">/server/config/view.php ( 0.82 KB )</li><li style="border-bottom:1px solid #EEE;font-s
ize:14px;padding:0 12px">/server/app/event.php ( 0.35 KB )</li><li style="border-bottom:1px solid #EEE;font-
size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Event.php ( 6.96 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/service.php ( 0.13 KB )</li><li style
="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/AppService.php ( 1.00 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/th
ink/Service.php ( 1.67 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serv
er/vendor/topthink/framework/src/think/Lang.php ( 7.60 KB )</li><li style="border-bottom:1px solid #EEE;font
-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/lang/zh-cn.php ( 12.88 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/init
ializer/Error.php ( 3.27 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/vendor/topthink/framework/src/think/initializer/RegisterService.php ( 1.33 KB )</li><li style="border-b
ottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/services.php ( 0.20 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/servi
ce/PaginatorService.php ( 1.52 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/topthink/framework/src/think/service/ValidateService.php ( 0.99 KB )</li><li style="border
-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/service/Mo
delService.php ( 1.76 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/topthink/think-multi-app/src/Service.php ( 1.08 KB )</li><li style="border-bottom:1px solid #EEE;fo
nt-size:14px;padding:0 12px">/server/vendor/topthink/think-queue/src/queue/Service.php ( 1.24 KB )</li><li s
tyle="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-trace/src/Se
rvice.php ( 0.77 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/framework/src/think/Middleware.php ( 6.78 KB )</li><li style="border-bottom:1px solid #EEE;font
-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/initializer/BootService.php ( 0.77 KB
 )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-
orm/src/Paginator.php ( 11.80 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/framework/src/think/Validate.php ( 46.10 KB )</li><li style="border-bottom:1px sol
id #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/Model.php ( 25.28 KB )</li><li 
style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/src/
contract/Arrayable.php ( 0.09 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/think-helper/src/contract/Jsonable.php ( 0.13 KB )</li><li style="border-bottom:1p
x solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/Attribute.ph
p ( 17.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/top
think/think-orm/src/model/concern/RelationShip.php ( 26.12 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/ModelEvent.php ( 2.27 KB 
)</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-o
rm/src/model/concern/TimeStamp.php ( 5.70 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/Conversion.php ( 10.41 KB )</li><li style=
"border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Db.
php ( 2.87 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/to
pthink/think-orm/src/DbManager.php ( 8.49 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/framework/src/think/Log.php ( 8.50 KB )</li><li style="border-bottom:1
px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Manager.php ( 3.98 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/psr/log/Psr/L
og/LoggerInterface.php ( 3.04 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/framework/src/think/Cache.php ( 4.79 KB )</li><li style="border-bottom:1px solid #
EEE;font-size:14px;padding:0 12px">/server/vendor/psr/simple-cache/src/CacheInterface.php ( 4.50 KB )</li><l
i style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/sr
c/helper/Arr.php ( 15.54 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/vendor/topthink/framework/src/think/cache/driver/Redis.php ( 6.72 KB )</li><li style="border-bottom:1px
 solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/cache/Driver.php ( 8.
06 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/f
ramework/src/think/contract/CacheHandlerInterface.php ( 2.25 KB )</li><li style="border-bottom:1px solid #EE
E;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Console.php ( 22.65 KB )</li><l
i style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/middleware.php ( 0.21 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework
/src/think/Pipeline.php ( 2.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/topthink/think-trace/src/TraceDebug.php ( 2.94 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/app/common/http/middleware/LikeAdminAllowMiddleware.php ( 1.96 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/common/http/midd
leware/BaseMiddleware.php ( 1.08 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 1
2px">/server/vendor/topthink/think-multi-app/src/MultiApp.php ( 7.07 KB )</li><li style="border-bottom:1px s
olid #EEE;font-size:14px;padding:0 12px">/server/app/adminapi/config/route.php ( 1.35 KB )</li><li style="bo
rder-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/adminapi/event.php ( 0.95 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/th
ink/Route.php ( 23.96 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/topthink/framework/src/think/route/RuleName.php ( 5.33 KB )</li><li style="border-bottom:1px solid 
#EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/Domain.php ( 5.55 KB )
</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framewor
k/src/think/route/RuleGroup.php ( 13.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/vendor/topthink/framework/src/think/route/Rule.php ( 22.85 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/RuleIte
m.php ( 8.81 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/
topthink/framework/src/think/route/dispatch/Url.php ( 3.42 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/dispatch/Controller.php ( 6
.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/
framework/src/think/route/Dispatch.php ( 6.49 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14p
x;padding:0 12px">/server/app/adminapi/http/middleware/AdminIpMiddleware.php ( 5.26 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/facade/C
onfig.php ( 1.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/framework/src/think/exception/HttpException.php ( 1.20 KB )</li><li style="border-bottom:1px so
lid #EEE;font-size:14px;padding:0 12px">/server/app/ExceptionHandle.php ( 2.11 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/exception/Han
dle.php ( 10.02 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vend
or/topthink/framework/src/think/Response.php ( 8.60 KB )</li><li style="border-bottom:1px solid #EEE;font-si
ze:14px;padding:0 12px">/server/vendor/topthink/framework/src/tpl/think_exception.tpl ( 17.03 KB )</li><li s
tyle="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/thin
k/facade/App.php ( 2.73 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/ser
ver/vendor/topthink/framework/src/think/response/Html.php ( 0.98 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Cookie.php ( 6.28 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-trace/s
rc/Html.php ( 4.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/v
endor/topthink/framework/src/think/log/driver/File.php ( 6.17 KB )</li><li style="border-bottom:1px solid #E
EE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/contract/LogHandlerInterface.p
hp ( 0.86 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/top
think/framework/src/think/log/Channel.php ( 6.54 KB )</li>                </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                    </div>
    </div>
    <div id="think_page_trace_close" style="display:none;text-align:right;height:15px;position:absolute;top:
10px;right:12px;cursor:pointer;"><img style="vertical-align:top;" src="data:image/gif;base64,R0lGODlhDwAPAJE
AAAAAAAMDA////wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4
gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCA
yMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmR
mLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzE
uMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmN
vbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1
NOkluc3RhbmNlSUQ9InhtcC5paWQ6MUQxMjc1MUJCQUJDMTFFMTk0OUVGRjc3QzU4RURFNkEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ
6MUQxMjc1MUNCQUJDMTFFMTk0OUVGRjc3QzU4RURFNkEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo
xRDEyNzUxOUJBQkMxMUUxOTQ5RUZGNzdDNThFREU2QSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxRDEyNzUxQUJBQkMxMUUxOTQ5RUZ
GNzdDNThFREU2QSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgH//v3
8+/r5+Pf29fTz8vHw7+7t7Ovq6ejn5uXk4+Lh4N/e3dzb2tnY19bV1NPS0dDPzs3My8rJyMfGxcTDwsHAv769vLu6ubi3trW0s7KxsK+uray
rqqmop6alpKOioaCfnp2cm5qZmJeWlZSTkpGQj46NjIuKiYiHhoWEg4KBgH9+fXx7enl4d3Z1dHNycXBvbm1sa2ppaGdmZWRjYmFgX15dXFt
aWVhXVlVUU1JRUE9OTUxLSklIR0ZFRENCQUA/Pj08Ozo5ODc2NTQzMjEwLy4tLCsqKSgnJiUkIyIhIB8eHRwbGhkYFxYVFBMSERAPDg0MCwo
JCAcGBQQDAgEAACH5BAAAAAAALAAAAAAPAA8AAAIdjI6JZqotoJPR1fnsgRR3C2jZl3Ai9aWZZooV+RQAOw==" /></div>
</div>
<div id="think_page_trace_open" style="height:30px;float:right;text-align:right;overflow:hidden;position:fix
ed;bottom:0;right:0;color:#000;line-height:30px;cursor:pointer;">
    <div style="background:#232323;color:#FFF;padding:0 6px;float:right;line-height:30px;font-size:14px">0.0
49581s </div>
    <img width="30" style="" title="ShowPageTrace" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAA
AwCAYAAABXAvmHAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eH
BhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bW
V0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPH
JkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbi
ByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2
JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIH
htcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjVERDVENkZGQj
kyNDExRTE5REY3RDQ5RTQ2RTRDQUJCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjVERDVENzAwQjkyNDExRTE5REY3RDQ5RTQ2RTRDQU
JCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NURENUQ2RkRCOTI0MTFFMTlERjdENDlFNDZFNENBQk
IiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NURENUQ2RkVCOTI0MTFFMTlERjdENDlFNDZFNENBQkIiLz4gPC9yZGY6RGVzY3JpcHRpb2
4+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5fx6IRAAAMCElEQVR42sxae3BU1Rk/9+69+8xuNtkHJAFCSI
AkhMgjCCJQUi0GtEIVbP8Qq9LH2No6TmfaztjO2OnUdvqHFMfOVFTqIK0vUEEeqUBARCsEeYQkEPJoEvIiELLvvc9z+p27u2F3s5tsBB1OZi
ebu5dzf7/v/L7f952zMM8cWIwY+Mk2ulCp92Fnq3XvnzArr2NZnYNldDp0Gw+/OEQ4+obQn5D+4Ubb22+YOGsWi/Todh8AHglKEGkEsnHBQ1
62511GZFgW6ZCBM9/W4H3iNSQqIe09O196dLKX7d1O39OViP/wthtkND62if/wj/DbMpph8BY/m9xy8BoBmQk+mHqZQGNy4JYRwCoRbwa8l4
JXw6M+orJxpU0U6ToKy/5bQsAiTeokGKkTx46RRxxEUgrwGgF4MWNNEJCGgYTvpgnY1IJWg5RzfqLgvcIgktX0i8dmMlFA8qCQ5L0Z/WObPL
UxT1i4lWSYDISoEfBYGvM+LlMQQdkLHoWRRZ8zYQI62Thswe5WTORGwNXDcGjqeOA9AF7B8rhzsxMBEoJ8oJKaqPu4hblHMCMPwl9XeNWyb8
xkB/DDGYKfMAE6aFL7xesZ389JlgG3XHEMI6UPDOP6JHHu67T2pwNPI69mCP4rEaBDUAJaKc/AOuXiwH07VCS3w5+UQMAuF/WqGI+yFIwVNB
wemBD4r0wgQiKoFZa00sEYTwss32lA1tPwVxtc8jQ5/gWCwmGCyUD8vRT0sHBFW4GJDvZmrJFWRY1EkrGA6ZB8/10fOZSSj0E6F+BSP7xidi
IzhBmKB09lEwHPkG+UQIyEN44EBiT5vrv2uJXyPQqSqO930fxvcvwbR/+JAkD9EfASgI9EHlp6YiHO4W+cAB20SnrFqxBbNljiXf1Pl1K2S0
HCWfiog3YlAD5RGwwxK6oUjTweuVigLjyB0mX410mAFnMoVK1lvvUvgt8fUJH0JVyjuvcmg4dE5mUiFtD24AZ4qBVELxXKS+pMxN43kSdzNw
udJ+bQbLlmnxvPOQoCugSap1GnSRoG8KOiKbH+rIA0lEeSAg3y6eeQ6XI2nrYnrPM89bUTgI0Pdqvl50vlNbtZxDUBcLBK0kPd5jPziyLdoj
JIN0pq5/mdzwL4UVvVInV5ncQEPNOUxa9d0TU+CW5l+FoI0GSDKHVVSOs+0KOsZoxwOzSZNFGv0mQ9avyLCh2Hpm+70Y0YJoJVgmQv822wnD
C8Miq6VjJ5IFed0QD1YiAbT+nQE8v/RMZfmgmcCRHIIu7Bmcp39oM9fqEychcA747KxQ/AEyqQonl7hATtJmnhO2XYtgcia01aSbVMenAXrI
omPcLgEBA4liGBzFZAT8zBYqW6brI67wg8sFVhxBhwLwBP2+tqBQqqK7VJKGh/BRrfTr6nWL7nYBaZdBJHqrX3kPEPap56xwE/GvjJTRMADe
MCdcGpGXL1Xh4ZL8BDOlWkUpegfi0CeDzeA5YITzEnddv+IXL+UYCmqIvqC9UlUC/ki9FipwVjunL3yX7dOTLeXmVMAhbsGporPfyOBTm/BJ
23gTVehsvXRnSewagUfpBXF3p5pygKS7OceqTjb7h2vjr/XKm0ZofKSI2Q/J102wHzatZkJPYQ5JoKsuK+EoHJakVzubzuLQDepCKllTZi9A
G0DYg9ZLxhFaZsOu7bvlmVI5oPXJMQJcHxHClSln1apFTvAimeg48u0RWFeZW4lVcjbQWZuIQK1KozZfIDO6CSQmQQXdpBaiKZyEWThVK1uE
c6v7V7uK0ysduExPZx4vysDR+4SelhBYm0R6LBuR4PXts8MYMcJPsINo4YZCDLj0sgB0/vLpPXvA2Tn42Cv5rsLulGubzW0sEd3d4W/mJt2K
ck+DzDMijfPLOjyrDhXSh852B+OvflqAkoyXO1cYfujtc/i3jJSAwhgfFlp20laMLOku/bC7prgqW7lCn4auE5NhcXPd3M7x70+IceSgZvNl
jCd9k3fLjYsPElqLR14PXQZqD2ZNkkrAB79UeJUebFQmXpf8ZcAQt2XrMQdyNUVBqZoUzAFyp3V3xi/MubUA/mCT4Fhf038PC8XplhWnCmnK
/ZzyC2BSTRSqKVOuY2kB8Jia0lvvRIVoP+vVWJbYarf6p655E2/nANBMCWkgD49DA0VAMyI1OLFMYCXiU9bmzi9/y5i/vsaTpHPHidTofzLb
M65vMPva9HlovgXp0AvjtaqYMfDD0/4mAsYE92pxa+9k1QgCnRVObCpojpzsKTPvayPetTEgBdwnssjuc0kOBFX+q3HwRQxdrOLAqeYRjkMk
/trTSu2Z9Lik7CfF0AvjtqAhS4NHobGXUnB5DQs8hG8p/wMX1r4+8xkmyvQ50JVq72TVeXbz3HvpWaQJi57hJYTw4kGbtS+C2TigQUtZUX+X
27QQq2ePBZBru/0lxTm8fOOQ5yaZOZMAV+he4FqIMB+LQB0UgMSajANX29j+vbmly8ipRvHeSQoQOkM5iFXcPQCVwDMs5RBCQmaPOyvbNd6u
wvQJ183BZQG3Zc+Eiv7vQOKu8YeDmMcJlt2ckyftVeMIGLBCmdMHl/tFILYwGPjXWO3zOfSq/+om+oa7Mlh2fpSsRGLp7RAW3FUVjNHgiMhy
E6zBFjM2BdkdJGO7nP1kJXWAtBuBpPIAu7f+hhu7bFXIuC5xWrf0X2xreykOsUyKkF2gwadbrXDcXrfKxR43zGcSj4t/cCgr+a1iy6EjE5GY
ktUCl9fwfMeylyooGF48bN2IGLTw8x7StS7sj8TF9FmPGWQhm3rRR+o9lhvjJvSYAdfDUevI1M6bnX/OwWaDMOQ8RPgKRo0eulBTdT8AW2kl
8e9L7UHghHwMfLiZPNoSpx0yugpQZaFqKWqxVSM3a2pN1SAhC2jf94I7ybBI7EL5A2Wvu5ht3xsoEt4+Ay/abXgCQAxyOeDsDlTCQzy75ohc
Ggv9Tra9uiymRUYTLrswOLlCdfAQf7HPDQQ4ErAH5EDXB9cMxWYpjtXApRncojS0sbV/cCgHTHwGNBJy+1PQE2x56FpaVR7wfQGZ37V+V+19
EiHNvR6q1fRUjqvbjbMq1/qfHxbTrE10ePY2gPFk48D2CVMTf1AF4PXvyYR9dV6Wf7H413m3xTWQvYGhQ7mfYwA5mAX+18Vue05v/8jG/fZX
/IW5MKPKtjSYlt0ellxh+/BOCPAwYaeVr0QofZFxJWVWC8znG70au6llVmktsF0bfHF6k8fvZ5esZJbwHwwnjg59tXz6sL/P0NUZDuSNu1mn
J8Vab17+cy005A9wtOpp3i0bZdpJLUil00semAwN45LgEViZYe3amNye0B6A9chviSlzXVsFtyN5/1H3gaNmMpn8Fz0GpYFp6Zw615H/LpUu
RQQDMCL82n5DpBSawkvzIdN2ypiT8nSLth8Pk9jnjwdFzH3W4XW6KMBfwB569NdcGX93mC16tTflcArcYUc/mFuYbV+8zY0SAjAVoNErNgWj
twumJ3wbn/HlBFYdxHvSkJJEc+Ngal9opSwyo9YlITX2C/P/+gf8sxURSLR+mcZUmeqaS9wrh6vxW5zxFCOqFi90RbDWq/YwZmnu1+a6Ovdp
vRqkNxxe44lyl4OobEnpKA6Uox5EfH9xzPs/HRKrTPWdIQrK1VZDU7ETiD3Obpl+8wPPCRBbkbwNtpW9AbBe5L1SMlj3tdTxk/9W47JUmqS5
HU+JzYymUKXjtWVmT9RenIhgXc+nroWLyxXJhmL112OdB8GCsk4f8oZJucnvmmtR85mBn10GZ0EKSCMUSAR3ukcXd5s7LvLD3me61WkuTCpJ
zYAyRurMB44EdEJzTfU271lUJC03YjXJXzYOGZwN4D8eB5jlfLrdWfzGRW7icMPfiSO6Oe7s20bmhdgLX4Z23B+s3JgQESzUDiMboSzDMHFp
NMwccGePauhfwjzwnI2wu9zKGgEFg80jcZ7MHllk07s1H+5yojtUQTlH4nFdLKTGwDmPbIklOb1L1zO4T6N8NCuDLFLS/C63c0eNRimZ++s5
BMBHxU11jHchI9oFVUxRh/eMDzHEzGYu0Lg8gJ7oS/tFCwoic44fyUtix0n/46vP4bf+//BRgAYwDDar4ncHIAAAAASUVORK5CYII=">
</div>

<script type="text/javascript">
    (function(){
        var tab_tit  = document.getElementById('think_page_trace_tab_tit').getElementsByTagName('span');
        var tab_cont = document.getElementById('think_page_trace_tab_cont').getElementsByTagName('div');
        var open     = document.getElementById('think_page_trace_open');
        var close    = document.getElementById('think_page_trace_close').children[0];
        var trace    = document.getElementById('think_page_trace_tab');
        var cookie   = document.cookie.match(/thinkphp_show_page_trace=(\d\|\d)/);
        var history  = (cookie && typeof cookie[1] != 'undefined' && cookie[1].split('|')) || [0,0];
        open.onclick = function(){
            trace.style.display = 'block';
            this.style.display = 'none';
            close.parentNode.style.display = 'block';
            history[0] = 1;
            document.cookie = 'thinkphp_show_page_trace='+history.join('|')
        }
        close.onclick = function(){
            trace.style.display = 'none';
            this.parentNode.style.display = 'none';
            open.style.display = 'block';
            history[0] = 0;
            document.cookie = 'thinkphp_show_page_trace='+history.join('|')
        }
        for(var i = 0; i < tab_tit.length; i++){
            tab_tit[i].onclick = (function(i){
                return function(){
                    for(var j = 0; j < tab_cont.length; j++){
                        tab_cont[j].style.display = 'none';
                        tab_tit[j].style.color = '#999';
                    }
                    tab_cont[i].style.display = 'block';
                    tab_tit[i].style.color = '#000';
                    history[1] = i;
                    document.cookie = 'thinkphp_show_page_trace='+history.join('|')
                }
            })(i)
        }
        parseInt(history[0]) && open.click();
        tab_tit[history[1]].click();
    })();
</script>
</body>
</html>
* Connection #0 to host localhost left intact
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---

看起来有PHP错误。让我检查一下PHP错误日志：


---

```bash
cd /www/wwwroot/ai && docker logs chatmoney-php --tail 20
```

```
[root@iZ2ze776l6a0t8ydszrzbbZ ai]# cd /www/wwwroot/ai && docker logs chatmoney-php --tail 20
********** -  18/Jul/2025:11:02:00 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:01 +0800 "POST /index.php" 200
********** -  18/Jul/2025:11:02:01 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:01 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:02 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:02 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:02 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:02 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:02 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:02 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:02 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:02 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:03 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:02:22 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:26:22 +0800 "GET /index.php" 200
********** -  18/Jul/2025:11:26:35 +0800 "GET /index.php" 404
********** -  18/Jul/2025:11:26:40 +0800 "POST /index.php" 404
********** -  18/Jul/2025:12:44:11 +0800 "GET /index.php" 404
********** -  18/Jul/2025:12:49:31 +0800 "GET /index.php" 404
********** -  18/Jul/2025:12:52:24 +0800 "GET /index.php" 404
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---

我看到有404错误。让我检查一下ThinkPHP的路由配置，看看是否需要添加具体的路由规则。让我查看一下其他正常工作的API是如何配置的：


---

<details>
            <summary>Grep search for "adminapi.*setting" • **9** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README7.md` | L1639 | `4. `server/app/adminapi/controller/setting/ai/ModelsController.php` - 模型管理控制器` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README7.md` | L1640 | `5. `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` - 模型管理逻辑` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README7.md` | L1641 | `6. `server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` - 删除检查服务(新建)` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README7.md` | L1755 | `- `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` - 模型管理增强` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README7.md` | L1756 | `- `server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` - 新建删除检查服务` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README.md` | L170 | `- `server/app/adminapi/logic/setting/KeyPoolLogic.php` - 3处手动合并重排序支持` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README.md` | L171 | `- `server/app/adminapi/lists/setting/KeyPoolLists.php` - 1处手动合并重排序支持` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README.md` | L173 | `- `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` - 重排序模型管理功能` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README.md` | L946 | `- **修改** `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` - 修复变量引用错误` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/README.md` | L947 | `- **修改** `server/app/adminapi/validate/setting/ModelsValidate.php` - 支持重排模型类型验证` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/sensitive_words_function_analysis.md` | L204 | `**文件**: `server/app/adminapi/logic/setting/SensitiveWordLogic.php`` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/%E6%95%B0%E6%8D%AE%E5%BA%93%E5%88%86%E6%9E%90%E4%B8%8E%E5%8D%87%E7%BA%A7%E7%AE%A1%E7%90%86%E6%8C%87%E5%8D%97.md` | L1306 | `cp model_member_package_check_solution.php server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/%E6%95%B0%E6%8D%AE%E5%BA%93%E5%88%86%E6%9E%90%E4%B8%8E%E5%8D%87%E7%BA%A7%E7%AE%A1%E7%90%86%E6%8C%87%E5%8D%97.md` | L1311 | `// 在 server/app/adminapi/logic/setting/ai/AiModelsLogic.php 中修改 del 方法` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/%E6%95%B0%E6%8D%AE%E5%BA%93%E5%88%86%E6%9E%90%E4%B8%8E%E5%8D%87%E7%BA%A7%E7%AE%A1%E7%90%86%E6%8C%87%E5%8D%97.md` | L1481 | `- ✅ `server/app/adminapi/logic/setting/ContentCensorLogic.php` - 添加新审核配置项` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/new_version_update_analysis_report.md` | L55 | `- **文件路径**：`new/server/app/adminapi/logic/setting/KeyPoolLogic.php` (378行代码)` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/new_version_update_analysis_report.md` | L88 | `- **文件路径**：`new/server/app/adminapi/lists/setting/KeyPoolLists.php` (158行代码)` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/new_version_update_analysis_report.md` | L98 | `- **文件路径**：`new/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` (452行代码)` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/new_version_update_analysis_report.md` | L301 | `cp server/app/adminapi/logic/setting/KeyPoolLogic.php backup/logic/$(date +%Y%m%d_%H%M%S)/` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/new_version_update_analysis_report.md` | L304 | `cp new/server/app/adminapi/logic/setting/KeyPoolLogic.php server/app/adminapi/logic/setting/` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/new_version_update_analysis_report.md` | L305 | `cp new/server/app/adminapi/logic/setting/ai/AiModelsLogic.php server/app/adminapi/logic/setting/ai/` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/new_version_update_analysis_report.md` | L306 | `cp new/server/app/adminapi/lists/setting/KeyPoolLists.php server/app/adminapi/lists/setting/` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L165 | `| `server/app/adminapi/controller/setting/ai/ModelsController.php` | L1-26 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L166 | `| `server/app/adminapi/controller/setting/ai/ModelsController.php` | L26-100 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L167 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L153-194 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L169 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L90-153 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L172 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L1-29 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L173 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L29-90 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L175 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L295-333 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L176 | `| `server/app/adminapi/controller/setting/ai/DrawController.php` | L1-38 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L180 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L232-270 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L181 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L194-232 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L182 | `| `server/app/adminapi/controller/setting/ai/ModelsController.php` | L100-147 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L185 | `| `server/app/adminapi/controller/setting/ai/ChatController.php` | L1-47 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-11_08-13Z-%E5%AD%A6%E4%B9%A0cm-creation-model%E8%A1%A8%E7%9A%84%E5%AE%9E%E7%8E%B0%E9%80%BB%E8%BE%91.md` | L187 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L270-295 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-12_07-26Z-%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95%E4%B8%AD%E7%9A%84%E6%B5%8B%E8%AF%95%E5%92%8C%E8%B0%83%E8%AF%95%E6%96%87%E4%BB%B6.md` | L3148 | `| `server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` | L54-99 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-12_07-26Z-%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95%E4%B8%AD%E7%9A%84%E6%B5%8B%E8%AF%95%E5%92%8C%E8%B0%83%E8%AF%95%E6%96%87%E4%BB%B6.md` | L3199 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` | L48 | `->field('mpa.id,mpa.package_id,mpa.status,mpa.day_limit,mp.name as package_name,mpa.sub_model_id')` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-12_07-26Z-%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95%E4%B8%AD%E7%9A%84%E6%B5%8B%E8%AF%95%E5%92%8C%E8%B0%83%E8%AF%95%E6%96%87%E4%BB%B6.md` | L3200 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` | L57 | `->field('mpa.id,mpa.package_id,mpa.sub_model_id,mpa.status,mpa.day_limit,mp.name as package_name')` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-12_07-26Z-%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95%E4%B8%AD%E7%9A%84%E6%B5%8B%E8%AF%95%E5%92%8C%E8%B0%83%E8%AF%95%E6%96%87%E4%BB%B6.md` | L3201 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` | L125 | `->field('mpa.id,mpa.package_id,mpa.status,mpa.day_limit,mp.name as package_name')` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-12_07-26Z-%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95%E4%B8%AD%E7%9A%84%E6%B5%8B%E8%AF%95%E5%92%8C%E8%B0%83%E8%AF%95%E6%96%87%E4%BB%B6.md` | L3221 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` | L10 | `use app\common\model\member\MemberPackageApply;` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-12_07-26Z-%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95%E4%B8%AD%E7%9A%84%E6%B5%8B%E8%AF%95%E5%92%8C%E8%B0%83%E8%AF%95%E6%96%87%E4%BB%B6.md` | L3222 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` | L45 | `$packageApplies = MemberPackageApply::alias('mpa')` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-12_07-26Z-%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95%E4%B8%AD%E7%9A%84%E6%B5%8B%E8%AF%95%E5%92%8C%E8%B0%83%E8%AF%95%E6%96%87%E4%BB%B6.md` | L3223 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` | L53 | `$subModelApplies = MemberPackageApply::alias('mpa')` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-12_07-26Z-%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95%E4%B8%AD%E7%9A%84%E6%B5%8B%E8%AF%95%E5%92%8C%E8%B0%83%E8%AF%95%E6%96%87%E4%BB%B6.md` | L3224 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/ModelDeletionChecker.php` | L122 | `$directApplies = MemberPackageApply::alias('mpa')` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L217 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/newai/server/app/adminapi/logic/setting/ai/AiDrawLogic.php` | L11 | `class AiDrawLogic extends BaseLogic` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L218 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/newai/server/app/adminapi/logic/setting/ContentCensorLogic.php` | L19 | `class ContentCensorLogic extends BaseLogic` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L398 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/KeyPoolController.php` | L133 | `$result = (new KeyPoolLogic())->import($file,$post);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L399 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/MindmapController.php` | L37 | `$result = (new MindmapLogic())->getExampleConfig();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L400 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/MindmapController.php` | L50 | `(new MindmapLogic())->setExampleConfig($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L401 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/MindmapController.php` | L61 | `$result = (new MindmapLogic())->getConfig();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L402 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/MindmapController.php` | L74 | `(new MindmapLogic())->setConfig($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L403 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/pay/PayWayController.php` | L48 | `$result = (new PayWayLogic())->setPayWay($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L404 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/ShareSettingController.php` | L39 | `$result = (new ShareSettingLogic())->getConfig();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L405 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/ShareSettingController.php` | L53 | `(new ShareSettingLogic())->setConfig($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L406 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/UserController.php` | L33 | `$result = (new UserLogic())->getConfig();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L407 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/UserController.php` | L46 | `(new UserLogic())->setConfig($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L408 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/UserController.php` | L58 | `$result = (new UserLogic())->getRegisterConfig();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L409 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/setting/UserController.php` | L71 | `(new UserLogic())->setRegisterConfig($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L435 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L20 | `$result = (new ActivityRewardLogic())->getShareSetting();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L436 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L34 | `(new ActivityRewardLogic())->SetShareSetting($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L437 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L46 | `$result = (new ActivityRewardLogic())->getInviteSetting();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L438 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L59 | `(new ActivityRewardLogic())->setInviteSetting($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L439 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L71 | `$result = (new ActivityRewardLogic())->getSignSetting();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L440 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L85 | `(new ActivityRewardLogic())->setSignSetting($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L441 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L97 | `$result = (new ActivityRewardLogic())->getWorkSetting();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L442 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L110 | `(new ActivityRewardLogic())->setWorkSetting($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L443 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L122 | `$result = (new ActivityRewardLogic())->getRobotSetting();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L444 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L135 | `(new ActivityRewardLogic())->setRobotSetting($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L445 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L147 | `$result = (new ActivityRewardLogic())->getSquareSetting();` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-06-26_08-08Z-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98%E5%9B%9E%E6%94%B6%E6%9C%BA%E5%88%B6%E7%9A%84%E4%BB%A3%E7%A0%81%E6%96%B9%E6%B3%95.md` | L446 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/controller/market/ActivityRewardController.php` | L161 | `(new ActivityRewardLogic())->setSquareSetting($params);` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L224 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L29-66 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L225 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L172-205 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L226 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L249-298 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L227 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L66-117 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L228 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L117-172 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L229 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L1-29 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L230 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L205-249 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L232 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L323-361 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L233 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L361-422 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L234 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L298-323 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L541 | `| `server/app/adminapi/logic/setting/BulletinLogic.php` | L1-54 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L542 | `| `server/app/adminapi/controller/setting/BulletinController.php` | L1-50 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L562 | `| `server/app/adminapi/controller/setting/system/SystemController.php` | L1-39 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L564 | `| `server/app/adminapi/logic/setting/SensitiveWordLogic.php` | L103-147 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1696 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L249-298 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1697 | `| `new/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L249-298 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1702 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L422-452 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1704 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L361-422 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1705 | `| `new/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L422-452 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1707 | `| `new/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L361-422 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1712 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L205-249 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1714 | `| `server/app/adminapi/validate/setting/ModelsValidate.php` | L1-58 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1716 | `| `new/server/app/adminapi/validate/setting/ModelsValidate.php` | L1-58 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1717 | `| `new/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L205-249 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1750 | `从搜索结果中，我发现了后台重排模型编辑保存功能的问题所在！问题出现在`server/app/adminapi/logic/setting/ai/AiModelsLogic.php`文件中。` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1814 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/new/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L217 | `} else if (ChatEnum::MODEL_TYPE_EMB  || ChatEnum::MODEL_TYPE_RANKING) {` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1815 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L217 | `} else if (ChatEnum::MODEL_TYPE_EMB  || ChatEnum::MODEL_TYPE_RANKING) {` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1816 | `| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L217 | `} else if (ChatEnum::MODEL_TYPE_EMB  || ChatEnum::MODEL_TYPE_RANKING) {` |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1835 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L249-298 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1836 | `| `new/server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L249-298 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1837 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L422-452 |` |
| `vscode-remote://ssh-remote%2B*************/www/wwwroot/ai/.specstory/history/2025-07-17_12-09Z-fix-missing-component-error-in-javascript.md` | L1838 | `| `server/app/adminapi/logic/setting/ai/AiModelsLogic.php` | L361-422 |` |

</details>

---

让我检查一下其他正常工作的setting API是如何调用的：


---

Tool call timed out after 25s (grep)

---

让我直接查看一个正常工作的setting API：


---

Read file: admin/src/api/setting/website.ts

---

我发现了问题！其他API使用的是 `/setting.website/getWebsite` 这样的格式，而不是 `/setting.bulletin/detail`。让我修正API调用路径：


---



---



---

让我重新检查一下当前的API文件内容：


---

Read file: admin/src/api/setting/notice.ts

---

现在API路径看起来是正确的。让我测试一下这个API是否能正常工作：


---

```bash
curl -X GET "http://localhost:180/adminapi/setting.bulletin/detail" -H "Content-Type: application/json" -s
```

```
        .exception .message{
            padding: 12px;
            border: 1px solid #ddd;
            border-bottom: 0 none;
            line-height: 18px;
            font-size:16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .code{
            float: left;
            text-align: center;
            color: #fff;
            margin-right: 12px;
            padding: 16px;
            border-radius: 4px;
            background: #999;
        }
        .exception .source-code{
            padding: 6px;
            border: 1px solid #ddd;

            background: #f9f9f9;
            overflow-x: auto;

        }
        .exception .source-code pre{
            margin: 0;
        }
        .exception .source-code pre ol{
            margin: 0;
            color: #4288ce;
            display: inline-block;
            min-width: 100%;
            box-sizing: border-box;
            font-size:14px;
            font-family: "Century Gothic",Consolas,"Liberation Mono",Courier,Verdana,serif;
            padding-left: 40px;
        }
        .exception .source-code pre li{
            border-left: 1px solid #ddd;
            height: 18px;
            line-height: 18px;
        }
        .exception .source-code pre code{
            color: #333;
            height: 100%;
            display: inline-block;
            border-left: 1px solid #fff;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace{
            padding: 6px;
            border: 1px solid #ddd;
            border-top: 0 none;
            line-height: 16px;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace h2:hover {
            text-decoration: underline;
            cursor: pointer;
        }
        .exception .trace ol{
            margin: 12px;
        }
        .exception .trace ol li{
            padding: 2px 4px;
        }
        .exception div:last-child{
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        /* Exception Variables */
        .exception-var table{
            width: 100%;
            margin: 12px 0;
            box-sizing: border-box;
            table-layout:fixed;
            word-wrap:break-word;
        }
        .exception-var table caption{
            text-align: left;
            font-size: 16px;
            font-weight: bold;
            padding: 6px 0;
        }
        .exception-var table caption small{
            font-weight: 300;
            display: inline-block;
            margin-left: 10px;
            color: #ccc;
        }
        .exception-var table tbody{
            font-size: 13px;
            font-family: Consolas, "Liberation Mono", Courier, "微软雅黑",serif;
        }
        .exception-var table td{
            padding: 0 6px;
            vertical-align: top;
            word-break: break-all;
        }
        .exception-var table td:first-child{
            width: 28%;
            font-weight: bold;
            white-space: nowrap;
        }
        .exception-var table td pre{
            margin: 0;
        }

        /* Copyright Info */
        .copyright{
            margin-top: 24px;
            padding: 12px 0;
            border-top: 1px solid #eee;
        }

        /* SPAN elements with the classes below are added by prettyprint. */
        pre.prettyprint .pln { color: #000 }  /* plain text */
        pre.prettyprint .str { color: #080 }  /* string content */
        pre.prettyprint .kwd { color: #008 }  /* a keyword */
        pre.prettyprint .com { color: #800 }  /* a comment */
        pre.prettyprint .typ { color: #606 }  /* a type name */
        pre.prettyprint .lit { color: #066 }  /* a literal value */
        /* punctuation, lisp open bracket, lisp close bracket */
        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }
        pre.prettyprint .tag { color: #008 }  /* a markup tag name */
        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */
        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */
        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */
        pre.prettyprint .fun { color: red }  /* a function name */
    </style>
</head>
<body>
                <div class="exception">
        <div class="message">
            <div class="info">
                <div>
                    <h2>#0 [0]<abbr title="think\exception\HttpException">HttpException</abbr> in <a class="
toggle" title="/server/vendor/topthink/framework/src/helper.php line 53">helper.php line 53</a></h2>
                </div>
                <div><h1>页面不存在</h1></div>
            </div>
        </div>
                            <div class="source-code">
                    <pre class="prettyprint lang-php"><ol start="44"><li class="line-0-44"><code>     * @par
am integer|Response $code    状态码 或者 Response对象实例
</code></li><li class="line-0-45"><code>     * @param string           $message 错误信息
</code></li><li class="line-0-46"><code>     * @param array            $header  参数
</code></li><li class="line-0-47"><code>     */
</code></li><li class="line-0-48"><code>    function abort($code, string $message = '', array $header = [])
</code></li><li class="line-0-49"><code>    {
</code></li><li class="line-0-50"><code>        if ($code instanceof Response) {
</code></li><li class="line-0-51"><code>            throw new HttpResponseException($code);
</code></li><li class="line-0-52"><code>        } else {
</code></li><li class="line-0-53 line-error"><code>            throw new HttpException($code, $message, null
, $header);
</code></li><li class="line-0-54"><code>        }
</code></li><li class="line-0-55"><code>    }
</code></li><li class="line-0-56"><code>}
</code></li><li class="line-0-57"><code>
</code></li><li class="line-0-58"><code>if (!function_exists('app')) {
</code></li><li class="line-0-59"><code>    /**
</code></li><li class="line-0-60"><code>     * 快速获取容器中的实例 支持依赖注入
</code></li><li class="line-0-61"><code>     * @template T
</code></li><li class="line-0-62"><code>     * @param string|class-string&lt;T&gt; $name        类名或标识 
默认获取当前应用实例
</code></li></ol></pre>
                </div>
                    <div class="trace">
            <h2 data-expand="1">Call Stack</h2>
            <ol>
                <li>in <a class="toggle" title="/server/vendor/topthink/framework/src/helper.php line 53">he
lper.php line 53</a></li>
                                    <li>
                        at abort(404, '页面不存在') in <a class="toggle" title="/server/app/adminapi/http/mi
ddleware/AdminIpMiddleware.php line 55">AdminIpMiddleware.php line 55</a>                    </li>
                                    <li>
                        at <abbr title="app\adminapi\http\middleware\AdminIpMiddleware">AdminIpMiddleware</a
bbr>->handle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure
">Closure</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\adminapi\http\middleware\AdminIp
Middleware">AdminIpMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>),
 <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/
framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Route.php l
ine 794">Route.php line 794</a>                    </li>
                                    <li>
                        at <abbr title="think\Route">Route</abbr>->dispatch(<em>object</em>(<abbr title="app
\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title
="/server/vendor/topthink/framework/src/think/Http.php line 216">Http.php line 216</a>                    </
li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->dispatchToRoute(<em>object</em>(<abbr title
="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http
.php line 206">Http.php line 206</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->think\{closure}(<em>object</em>(<abbr title
="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Pipe
line.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-multi-app/
src/MultiApp.php line 51">MultiApp.php line 51</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->think\app\{closure}(<em>object<
/em>(<abbr title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framewor
k/src/think/Pipeline.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-multi-app/src/MultiApp.ph
p line 52">MultiApp.php line 52</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->handle(<em>object</em>(<abbr ti
tle="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>))                   
 </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="think\app\MultiApp">MultiApp</abbr>)
, 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure
">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Middleware.php li
ne 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/app/common/http/middleware/BaseM
iddleware.php line 29">BaseMiddleware.php line 29</a>                    </li>
                                    <li>
                        at <abbr title="app\common\http\middleware\BaseMiddleware">BaseMiddleware</abbr>->ha
ndle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closur
e</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\common\http\middleware\BaseMiddl
eware">BaseMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>obj
ect</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framewor
k/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/app/common/http/middleware/LikeA
dminAllowMiddleware.php line 47">LikeAdminAllowMiddleware.php line 47</a>                    </li>
                                    <li>
                        at <abbr title="app\common\http\middleware\LikeAdminAllowMiddleware">LikeAdminAllowM
iddleware</abbr>->handle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr ti
tle="Closure">Closure</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\common\http\middleware\LikeAdmin
AllowMiddleware">LikeAdminAllowMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Requ
est</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vend
or/topthink/framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li
>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-trace/src/
TraceDebug.php line 71">TraceDebug.php line 71</a>                    </li>
                                    <li>
                        at <abbr title="think\trace\TraceDebug">TraceDebug</abbr>->handle(<em>object</em>(<a
bbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>))             
       </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="think\trace\TraceDebug">TraceDebug</
abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Middleware.
php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http.php li
ne 207">Http.php line 207</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->runWithRequest(<em>object</em>(<abbr title=
"app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http.
php line 170">Http.php line 170</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->run() in <a class="toggle" title="/server/p
ublic/index.php line 29">index.php line 29</a>                    </li>
                            </ol>
        </div>
    </div>
                
    
        <div class="exception-var">
        <h2>Environment Variables</h2>
                <table>
                        <caption>GET Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>POST Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Files<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Cookies<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Session<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Server/Request Data</caption>
            <tbody>
                                <tr>
                    <td>HOSTNAME</td>
                    <td>dca4a834c126</td>
                </tr>
                                <tr>
                    <td>PHP_VERSION</td>
                    <td>8.0.30</td>
                </tr>
                                <tr>
                    <td>PHP_INI_DIR</td>
                    <td>/usr/local/etc/php</td>
                </tr>
                                <tr>
                    <td>GPG_KEYS</td>
                    <td>1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F 2C
16C765DBE54A088130F1BC4B9B5F600B55F3B4 39B641343D8C104B2B146DC3F9C39DC0B9698544</td>
                </tr>
                                <tr>
                    <td>PHP_LDFLAGS</td>
                    <td>-Wl,-O1 -pie</td>
                </tr>
                                <tr>
                    <td>PWD</td>
                    <td>/server</td>
                </tr>
                                <tr>
                    <td>TZ</td>
                    <td>Asia/Shanghai</td>
                </tr>
                                <tr>
                    <td>HOME</td>
                    <td>/</td>
                </tr>
                                <tr>
                    <td>PHP_SHA256</td>
                    <td>216ab305737a5d392107112d618a755dc5df42058226f1670e9db90e77d777d9</td>
                </tr>
                                <tr>
                    <td>PHPIZE_DEPS</td>
                    <td>autoconf                dpkg-dev                file            g++             gcc 
libc-dev                make            pkg-config              re2c</td>
                </tr>
                                <tr>
                    <td>PHP_URL</td>
                    <td>https://www.php.net/distributions/php-8.0.30.tar.xz</td>
                </tr>
                                <tr>
                    <td>SHLVL</td>
                    <td>0</td>
                </tr>
                                <tr>
                    <td>PHP_CFLAGS</td>
                    <td>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<
/td>
                </tr>
                                <tr>
                    <td>PATH</td>
                    <td>/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin</td>
                </tr>
                                <tr>
                    <td>PHP_ASC_URL</td>
                    <td>https://www.php.net/distributions/php-8.0.30.tar.xz.asc</td>
                </tr>
                                <tr>
                    <td>PHP_CPPFLAGS</td>
                    <td>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<
/td>
                </tr>
                                <tr>
                    <td>_</td>
                    <td>/usr/local/sbin/php-fpm</td>
                </tr>
                                <tr>
                    <td>HTTP_CONTENT_TYPE</td>
                    <td>application/json</td>
                </tr>
                                <tr>
                    <td>HTTP_ACCEPT</td>
                    <td>*/*</td>
                </tr>
                                <tr>
                    <td>HTTP_USER_AGENT</td>
                    <td>curl/7.61.1</td>
                </tr>
                                <tr>
                    <td>HTTP_HOST</td>
                    <td>localhost:180</td>
                </tr>
                                <tr>
                    <td>REDIRECT_STATUS</td>
                    <td>200</td>
                </tr>
                                <tr>
                    <td>SERVER_NAME</td>
                    <td>www.chatmoney.localhost</td>
                </tr>
                                <tr>
                    <td>SERVER_PORT</td>
                    <td>80</td>
                </tr>
                                <tr>
                    <td>SERVER_ADDR</td>
                    <td>**********</td>
                </tr>
                                <tr>
                    <td>REMOTE_PORT</td>
                    <td>48298</td>
                </tr>
                                <tr>
                    <td>REMOTE_ADDR</td>
                    <td>**********</td>
                </tr>
                                <tr>
                    <td>SERVER_SOFTWARE</td>
                    <td>nginx/1.23.1</td>
                </tr>
                                <tr>
                    <td>GATEWAY_INTERFACE</td>
                    <td>CGI/1.1</td>
                </tr>
                                <tr>
                    <td>REQUEST_SCHEME</td>
                    <td>http</td>
                </tr>
                                <tr>
                    <td>SERVER_PROTOCOL</td>
                    <td>HTTP/1.1</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_ROOT</td>
                    <td>/etc/nginx/html</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_URI</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_URI</td>
                    <td>/adminapi/setting.bulletin/detail</td>
                </tr>
                                <tr>
                    <td>SCRIPT_NAME</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>CONTENT_LENGTH</td>
                    <td></td>
                </tr>
                                <tr>
                    <td>CONTENT_TYPE</td>
                    <td>application/json</td>
                </tr>
                                <tr>
                    <td>REQUEST_METHOD</td>
                    <td>GET</td>
                </tr>
                                <tr>
                    <td>QUERY_STRING</td>
                    <td>s=adminapi/setting.bulletin/detail</td>
                </tr>
                                <tr>
                    <td>SCRIPT_FILENAME</td>
                    <td>/server/public/index.php</td>
                </tr>
                                <tr>
                    <td>FCGI_ROLE</td>
                    <td>RESPONDER</td>
                </tr>
                                <tr>
                    <td>PHP_SELF</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME_FLOAT</td>
                    <td>1752814447.3667</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME</td>
                    <td>1752814447</td>
                </tr>
                                <tr>
                    <td>argv</td>
                    <td>[
    &quot;s=adminapi\/setting.bulletin\/detail&quot;
]</td>
                </tr>
                                <tr>
                    <td>argc</td>
                    <td>1</td>
                </tr>
                            </tbody>
                    </table>
            </div>
    
    <div class="copyright">
        <a title="官方网站" href="http://www.thinkphp.cn">ThinkPHP</a> 
        <span>V6.1.4</span> 
        <span>{ 十年磨一剑-为API开发设计的高性能框架 }</span>
        <span>- <a title="官方手册" href="https://www.kancloud.cn/manual/thinkphp6_0/content">官方手册</a></
span>
    </div>
        <script>
        function $(selector, node){
            var elements;

            node = node || document;
            if(document.querySelectorAll){
                elements = node.querySelectorAll(selector);
            } else {
                switch(selector.substr(0, 1)){
                    case '#':
                        elements = [node.getElementById(selector.substr(1))];
                        break;
                    case '.':
                        if(document.getElementsByClassName){
                            elements = node.getElementsByClassName(selector.substr(1));
                        } else {
                            elements = get_elements_by_class(selector.substr(1), node);
                        }
                        break;
                    default:
                        elements = node.getElementsByTagName();
                }
            }
            return elements;

            function get_elements_by_class(search_class, node, tag) {
                var elements = [], eles, 
                    pattern  = new RegExp('(^|\\s)' + search_class + '(\\s|$)');

                node = node || document;
                tag  = tag  || '*';

                eles = node.getElementsByTagName(tag);
                for(var i = 0; i < eles.length; i++) {
                    if(pattern.test(eles[i].className)) {
                        elements.push(eles[i])
                    }
                }

                return elements;
            }
        }

        $.getScript = function(src, func){
            var script = document.createElement('script');
            
            script.async  = 'async';
            script.src    = src;
            script.onload = func || function(){};
            
            $('head')[0].appendChild(script);
        }

        ;(function(){
            var files = $('.toggle');
            var ol    = $('ol', $('.prettyprint')[0]);
            var li    = $('li', ol[0]);   

            // 短路径和长路径变换
            for(var i = 0; i < files.length; i++){
                files[i].ondblclick = function(){
                    var title = this.title;

                    this.title = this.innerHTML;
                    this.innerHTML = title;
                }
            }

            (function () {
                var expand = function (dom, expand) {
                    var ol = $('ol', dom.parentNode)[0];
                    expand = undefined === expand ? dom.attributes['data-expand'].value === '0' : undefined;
                    if (expand) {
                        dom.attributes['data-expand'].value = '1';
                        ol.style.display = 'none';
                        dom.innerText = 'Call Stack (展开)';
                    } else {
                        dom.attributes['data-expand'].value = '0';
                        ol.style.display = 'block';
                        dom.innerText = 'Call Stack (折叠)';
                    }
                };
                var traces = $('.trace');
                for (var i = 0; i < traces.length; i ++) {
                    var h2 = $('h2', traces[i])[0];
                    expand(h2);
                    h2.onclick = function () {
                        expand(this);
                    };
                }
            })();

            $.getScript('//cdn.bootcdn.net/ajax/libs/prettify/r298/prettify.min.js', function(){
                prettyPrint();
            });
        })();
    </script>
    <div id="think_page_trace" style="position: fixed;bottom:0;right:0;font-size:14px;width:100%;z-index: 99
9999;color: #000;text-align:left;font-family:'微软雅黑';">
    <div id="think_page_trace_tab" style="display: none;background:white;margin:0;height: 250px;">
        <div id="think_page_trace_tab_tit" style="height:30px;padding: 6px 12px 0;border-bottom:1px solid #e
cecec;border-top:1px solid #ececec;font-size:16px">
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">基本</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">文件</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">流程</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">错误</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">SQL</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">调试</span>
                    </div>
        <div id="think_page_trace_tab_cont" style="overflow:auto;height:212px;padding:0;line-height: 24px">
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                    <li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">请求信息 : 2025-0
7-18 12:54:07 HTTP/1.1 GET : http://localhost:180/adminapi/setting.bulletin/detail</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">运行时间 : 0.087304s [ 吞吐率：11.45req/s ] 内存消耗：2,61
5.48kb 文件加载：145</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">查询信息 : 0
 queries</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">缓存信息 : 0 reads,0 wri
tes</li>                </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                    <li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/public/in
dex.php ( 1.10 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendo
r/autoload.php ( 0.75 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/composer/autoload_real.php ( 1.63 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;p
adding:0 12px">/server/vendor/composer/platform_check.php ( 0.90 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/composer/ClassLoader.php ( 15.99 KB )</li><li style="bor
der-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/composer/autoload_static.php ( 38.34
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/depr
ecation-contracts/function.php ( 0.99 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;paddin
g:0 12px">/server/vendor/ralouphie/getallheaders/src/getallheaders.php ( 1.60 KB )</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzlehttp/psr7/src/functions_include.php (
 0.15 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzleh
ttp/psr7/src/functions.php ( 13.09 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0
 12px">/server/vendor/guzzlehttp/guzzle/src/functions_include.php ( 0.16 KB )</li><li style="border-bottom:1
px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzlehttp/guzzle/src/functions.php ( 5.55 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-m
bstring/bootstrap.php ( 7.33 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px"
>/server/vendor/symfony/polyfill-mbstring/bootstrap80.php ( 8.84 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/src/helper.php ( 7.35 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php80/bo
otstrap.php ( 1.50 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/v
endor/adbario/php-dot-notation/src/helpers.php ( 0.57 KB )</li><li style="border-bottom:1px solid #EEE;font-
size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php72/bootstrap.php ( 1.89 KB )</li><li style="bor
der-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-ctype/bootstrap.php
 ( 1.56 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfo
ny/polyfill-ctype/bootstrap80.php ( 1.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pad
ding:0 12px">/server/vendor/symfony/polyfill-intl-normalizer/bootstrap.php ( 0.71 KB )</li><li style="border
-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-intl-normalizer/bootst
rap80.php ( 0.70 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/think-orm/stubs/load_stubs.php ( 0.16 KB )</li><li style="border-bottom:1px solid #EEE;font-siz
e:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Exception.php ( 1.69 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Faca
de.php ( 2.71 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor
/symfony/polyfill-intl-idn/bootstrap.php ( 4.52 KB )</li><li style="border-bottom:1px solid #EEE;font-size:1
4px;padding:0 12px">/server/vendor/symfony/polyfill-php81/bootstrap.php ( 0.72 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php73/bootstrap.php ( 0.9
9 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/tra
nslation/Resources/functions.php ( 0.55 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/vendor/ezyang/htmlpurifier/library/HTMLPurifier.composer.php ( 0.10 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/mtdowling/jmespath.php/src/JmesPa
th.php ( 0.36 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor
/overtrue/socialite/src/Contracts/FactoryInterface.php ( 0.47 KB )</li><li style="border-bottom:1px solid #E
EE;font-size:14px;padding:0 12px">/server/vendor/overtrue/socialite/src/Contracts/UserInterface.php ( 1.04 K
B )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/overtrue/socia
lite/src/Contracts/ProviderInterface.php ( 2.89 KB )</li><li style="border-bottom:1px solid #EEE;font-size:1
4px;padding:0 12px">/server/vendor/alibabacloud/client/src/Functions.php ( 5.33 KB )</li><li style="border-b
ottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/qcloud/cos-sdk-v5/src/Common.php ( 2.40 K
B )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/qiniu/php-sdk/
src/Qiniu/functions.php ( 8.20 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/qiniu/php-sdk/src/Qiniu/Config.php ( 3.56 KB )</li><li style="border-bottom:1px solid #EEE
;font-size:14px;padding:0 12px">/server/vendor/ramsey/uuid/src/functions.php ( 5.03 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/rmccue/requests/library/Deprecated.ph
p ( 0.53 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/rmcc
ue/requests/src/Autoload.php ( 9.12 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/vendor/symfony/var-dumper/Resources/functions/dump.php ( 0.79 KB )</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-queue/src/common.php ( 1.01 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/fram
ework/src/think/App.php ( 14.17 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12
px">/server/vendor/topthink/framework/src/think/Container.php ( 15.38 KB )</li><li style="border-bottom:1px 
solid #EEE;font-size:14px;padding:0 12px">/server/vendor/psr/container/src/ContainerInterface.php ( 1.02 KB 
)</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/provider.php ( 0.19
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/fra
mework/src/think/Http.php ( 6.12 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 1
2px">/server/vendor/topthink/think-helper/src/helper/Str.php ( 7.28 KB )</li><li style="border-bottom:1px so
lid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Env.php ( 4.64 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/common.php ( 15.43 KB )</
li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/
src/helper.php ( 18.44 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serv
er/vendor/topthink/framework/src/think/Config.php ( 5.03 KB )</li><li style="border-bottom:1px solid #EEE;fo
nt-size:14px;padding:0 12px">/server/config/ai.php ( 37.72 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/config/app.php ( 1.02 KB )</li><li style="border-bottom:1px solid #EE
E;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/facade/Env.php ( 1.67 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/cache.php ( 1.25 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/console.php ( 1.76
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/cookie.php (
 0.56 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/databas
e.php ( 3.21 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/
filesystem.php ( 0.63 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/config/lang.php ( 0.81 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/config/log.php ( 1.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/
server/config/middleware.php ( 0.19 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/config/project.php ( 8.82 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/framework/src/think/facade/Request.php ( 8.92 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/Request.php ( 0.14 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Reque
st.php ( 54.04 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/c
ommon/enum/VoiceEnum.php ( 6.26 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12
px">/server/config/queue.php ( 1.23 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/config/route.php ( 1.54 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/config/session.php ( 0.57 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14p
x;padding:0 12px">/server/config/trace.php ( 0.34 KB )</li><li style="border-bottom:1px solid #EEE;font-size
:14px;padding:0 12px">/server/config/view.php ( 0.82 KB )</li><li style="border-bottom:1px solid #EEE;font-s
ize:14px;padding:0 12px">/server/app/event.php ( 0.35 KB )</li><li style="border-bottom:1px solid #EEE;font-
size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Event.php ( 6.96 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/service.php ( 0.13 KB )</li><li style
="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/AppService.php ( 1.00 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/th
ink/Service.php ( 1.67 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serv
er/vendor/topthink/framework/src/think/Lang.php ( 7.60 KB )</li><li style="border-bottom:1px solid #EEE;font
-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/lang/zh-cn.php ( 12.88 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/init
ializer/Error.php ( 3.27 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/vendor/topthink/framework/src/think/initializer/RegisterService.php ( 1.33 KB )</li><li style="border-b
ottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/services.php ( 0.20 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/servi
ce/PaginatorService.php ( 1.52 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/topthink/framework/src/think/service/ValidateService.php ( 0.99 KB )</li><li style="border
-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/service/Mo
delService.php ( 1.76 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/topthink/think-multi-app/src/Service.php ( 1.08 KB )</li><li style="border-bottom:1px solid #EEE;fo
nt-size:14px;padding:0 12px">/server/vendor/topthink/think-queue/src/queue/Service.php ( 1.24 KB )</li><li s
tyle="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-trace/src/Se
rvice.php ( 0.77 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/framework/src/think/Middleware.php ( 6.78 KB )</li><li style="border-bottom:1px solid #EEE;font
-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/initializer/BootService.php ( 0.77 KB
 )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-
orm/src/Paginator.php ( 11.80 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/framework/src/think/Validate.php ( 46.10 KB )</li><li style="border-bottom:1px sol
id #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/Model.php ( 25.28 KB )</li><li 
style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/src/
contract/Arrayable.php ( 0.09 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/think-helper/src/contract/Jsonable.php ( 0.13 KB )</li><li style="border-bottom:1p
x solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/Attribute.ph
p ( 17.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/top
think/think-orm/src/model/concern/RelationShip.php ( 26.12 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/ModelEvent.php ( 2.27 KB 
)</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-o
rm/src/model/concern/TimeStamp.php ( 5.70 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/Conversion.php ( 10.41 KB )</li><li style=
"border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Db.
php ( 2.87 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/to
pthink/think-orm/src/DbManager.php ( 8.49 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/framework/src/think/Log.php ( 8.50 KB )</li><li style="border-bottom:1
px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Manager.php ( 3.98 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/psr/log/Psr/L
og/LoggerInterface.php ( 3.04 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/framework/src/think/Cache.php ( 4.79 KB )</li><li style="border-bottom:1px solid #
EEE;font-size:14px;padding:0 12px">/server/vendor/psr/simple-cache/src/CacheInterface.php ( 4.50 KB )</li><l
i style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/sr
c/helper/Arr.php ( 15.54 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/vendor/topthink/framework/src/think/cache/driver/Redis.php ( 6.72 KB )</li><li style="border-bottom:1px
 solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/cache/Driver.php ( 8.
06 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/f
ramework/src/think/contract/CacheHandlerInterface.php ( 2.25 KB )</li><li style="border-bottom:1px solid #EE
E;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Console.php ( 22.65 KB )</li><l
i style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/middleware.php ( 0.21 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework
/src/think/Pipeline.php ( 2.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/topthink/think-trace/src/TraceDebug.php ( 2.94 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/app/common/http/middleware/LikeAdminAllowMiddleware.php ( 1.96 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/common/http/midd
leware/BaseMiddleware.php ( 1.08 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 1
2px">/server/vendor/topthink/think-multi-app/src/MultiApp.php ( 7.07 KB )</li><li style="border-bottom:1px s
olid #EEE;font-size:14px;padding:0 12px">/server/app/adminapi/config/route.php ( 1.35 KB )</li><li style="bo
rder-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/adminapi/event.php ( 0.95 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/th
ink/Route.php ( 23.96 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/topthink/framework/src/think/route/RuleName.php ( 5.33 KB )</li><li style="border-bottom:1px solid 
#EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/Domain.php ( 5.55 KB )
</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framewor
k/src/think/route/RuleGroup.php ( 13.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/vendor/topthink/framework/src/think/route/Rule.php ( 22.85 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/RuleIte
m.php ( 8.81 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/
topthink/framework/src/think/route/dispatch/Url.php ( 3.42 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/dispatch/Controller.php ( 6
.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/
framework/src/think/route/Dispatch.php ( 6.49 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14p
x;padding:0 12px">/server/app/adminapi/http/middleware/AdminIpMiddleware.php ( 5.26 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/facade/C
onfig.php ( 1.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/framework/src/think/exception/HttpException.php ( 1.20 KB )</li><li style="border-bottom:1px so
lid #EEE;font-size:14px;padding:0 12px">/server/app/ExceptionHandle.php ( 2.11 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/exception/Han
dle.php ( 10.02 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vend
or/topthink/framework/src/think/Response.php ( 8.60 KB )</li><li style="border-bottom:1px solid #EEE;font-si
ze:14px;padding:0 12px">/server/vendor/topthink/framework/src/tpl/think_exception.tpl ( 17.03 KB )</li><li s
tyle="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/thin
k/facade/App.php ( 2.73 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/ser
ver/vendor/topthink/framework/src/think/response/Html.php ( 0.98 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Cookie.php ( 6.28 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-trace/s
rc/Html.php ( 4.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/v
endor/topthink/framework/src/think/log/driver/File.php ( 6.17 KB )</li><li style="border-bottom:1px solid #E
EE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/contract/LogHandlerInterface.p
hp ( 0.86 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/top
think/framework/src/think/log/Channel.php ( 6.54 KB )</li>                </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                    </div>
    </div>
    <div id="think_page_trace_close" style="display:none;text-align:right;height:15px;position:absolute;top:
10px;right:12px;cursor:pointer;"><img style="vertical-align:top;" src="data:image/gif;base64,R0lGODlhDwAPAJE
AAAAAAAMDA////wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4
gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCA
yMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmR
mLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzE
uMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmN
vbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1
NOkluc3RhbmNlSUQ9InhtcC5paWQ6MUQxMjc1MUJCQUJDMTFFMTk0OUVGRjc3QzU4RURFNkEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ
6MUQxMjc1MUNCQUJDMTFFMTk0OUVGRjc3QzU4RURFNkEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo
xRDEyNzUxOUJBQkMxMUUxOTQ5RUZGNzdDNThFREU2QSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxRDEyNzUxQUJBQkMxMUUxOTQ5RUZ
GNzdDNThFREU2QSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgH//v3
8+/r5+Pf29fTz8vHw7+7t7Ovq6ejn5uXk4+Lh4N/e3dzb2tnY19bV1NPS0dDPzs3My8rJyMfGxcTDwsHAv769vLu6ubi3trW0s7KxsK+uray
rqqmop6alpKOioaCfnp2cm5qZmJeWlZSTkpGQj46NjIuKiYiHhoWEg4KBgH9+fXx7enl4d3Z1dHNycXBvbm1sa2ppaGdmZWRjYmFgX15dXFt
aWVhXVlVUU1JRUE9OTUxLSklIR0ZFRENCQUA/Pj08Ozo5ODc2NTQzMjEwLy4tLCsqKSgnJiUkIyIhIB8eHRwbGhkYFxYVFBMSERAPDg0MCwo
JCAcGBQQDAgEAACH5BAAAAAAALAAAAAAPAA8AAAIdjI6JZqotoJPR1fnsgRR3C2jZl3Ai9aWZZooV+RQAOw==" /></div>
</div>
<div id="think_page_trace_open" style="height:30px;float:right;text-align:right;overflow:hidden;position:fix
ed;bottom:0;right:0;color:#000;line-height:30px;cursor:pointer;">
    <div style="background:#232323;color:#FFF;padding:0 6px;float:right;line-height:30px;font-size:14px">0.0
93114s </div>
    <img width="30" style="" title="ShowPageTrace" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAA
AwCAYAAABXAvmHAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eH
BhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bW
V0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPH
JkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbi
ByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2
JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIH
htcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjVERDVENkZGQj
kyNDExRTE5REY3RDQ5RTQ2RTRDQUJCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjVERDVENzAwQjkyNDExRTE5REY3RDQ5RTQ2RTRDQU
JCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NURENUQ2RkRCOTI0MTFFMTlERjdENDlFNDZFNENBQk
IiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NURENUQ2RkVCOTI0MTFFMTlERjdENDlFNDZFNENBQkIiLz4gPC9yZGY6RGVzY3JpcHRpb2
4+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5fx6IRAAAMCElEQVR42sxae3BU1Rk/9+69+8xuNtkHJAFCSI
AkhMgjCCJQUi0GtEIVbP8Qq9LH2No6TmfaztjO2OnUdvqHFMfOVFTqIK0vUEEeqUBARCsEeYQkEPJoEvIiELLvvc9z+p27u2F3s5tsBB1OZi
ebu5dzf7/v/L7f952zMM8cWIwY+Mk2ulCp92Fnq3XvnzArr2NZnYNldDp0Gw+/OEQ4+obQn5D+4Ubb22+YOGsWi/Todh8AHglKEGkEsnHBQ1
62511GZFgW6ZCBM9/W4H3iNSQqIe09O196dLKX7d1O39OViP/wthtkND62if/wj/DbMpph8BY/m9xy8BoBmQk+mHqZQGNy4JYRwCoRbwa8l4
JXw6M+orJxpU0U6ToKy/5bQsAiTeokGKkTx46RRxxEUgrwGgF4MWNNEJCGgYTvpgnY1IJWg5RzfqLgvcIgktX0i8dmMlFA8qCQ5L0Z/WObPL
UxT1i4lWSYDISoEfBYGvM+LlMQQdkLHoWRRZ8zYQI62Thswe5WTORGwNXDcGjqeOA9AF7B8rhzsxMBEoJ8oJKaqPu4hblHMCMPwl9XeNWyb8
xkB/DDGYKfMAE6aFL7xesZ389JlgG3XHEMI6UPDOP6JHHu67T2pwNPI69mCP4rEaBDUAJaKc/AOuXiwH07VCS3w5+UQMAuF/WqGI+yFIwVNB
wemBD4r0wgQiKoFZa00sEYTwss32lA1tPwVxtc8jQ5/gWCwmGCyUD8vRT0sHBFW4GJDvZmrJFWRY1EkrGA6ZB8/10fOZSSj0E6F+BSP7xidi
IzhBmKB09lEwHPkG+UQIyEN44EBiT5vrv2uJXyPQqSqO930fxvcvwbR/+JAkD9EfASgI9EHlp6YiHO4W+cAB20SnrFqxBbNljiXf1Pl1K2S0
HCWfiog3YlAD5RGwwxK6oUjTweuVigLjyB0mX410mAFnMoVK1lvvUvgt8fUJH0JVyjuvcmg4dE5mUiFtD24AZ4qBVELxXKS+pMxN43kSdzNw
udJ+bQbLlmnxvPOQoCugSap1GnSRoG8KOiKbH+rIA0lEeSAg3y6eeQ6XI2nrYnrPM89bUTgI0Pdqvl50vlNbtZxDUBcLBK0kPd5jPziyLdoj
JIN0pq5/mdzwL4UVvVInV5ncQEPNOUxa9d0TU+CW5l+FoI0GSDKHVVSOs+0KOsZoxwOzSZNFGv0mQ9avyLCh2Hpm+70Y0YJoJVgmQv822wnD
C8Miq6VjJ5IFed0QD1YiAbT+nQE8v/RMZfmgmcCRHIIu7Bmcp39oM9fqEychcA747KxQ/AEyqQonl7hATtJmnhO2XYtgcia01aSbVMenAXrI
omPcLgEBA4liGBzFZAT8zBYqW6brI67wg8sFVhxBhwLwBP2+tqBQqqK7VJKGh/BRrfTr6nWL7nYBaZdBJHqrX3kPEPap56xwE/GvjJTRMADe
MCdcGpGXL1Xh4ZL8BDOlWkUpegfi0CeDzeA5YITzEnddv+IXL+UYCmqIvqC9UlUC/ki9FipwVjunL3yX7dOTLeXmVMAhbsGporPfyOBTm/BJ
23gTVehsvXRnSewagUfpBXF3p5pygKS7OceqTjb7h2vjr/XKm0ZofKSI2Q/J102wHzatZkJPYQ5JoKsuK+EoHJakVzubzuLQDepCKllTZi9A
G0DYg9ZLxhFaZsOu7bvlmVI5oPXJMQJcHxHClSln1apFTvAimeg48u0RWFeZW4lVcjbQWZuIQK1KozZfIDO6CSQmQQXdpBaiKZyEWThVK1uE
c6v7V7uK0ysduExPZx4vysDR+4SelhBYm0R6LBuR4PXts8MYMcJPsINo4YZCDLj0sgB0/vLpPXvA2Tn42Cv5rsLulGubzW0sEd3d4W/mJt2K
ck+DzDMijfPLOjyrDhXSh852B+OvflqAkoyXO1cYfujtc/i3jJSAwhgfFlp20laMLOku/bC7prgqW7lCn4auE5NhcXPd3M7x70+IceSgZvNl
jCd9k3fLjYsPElqLR14PXQZqD2ZNkkrAB79UeJUebFQmXpf8ZcAQt2XrMQdyNUVBqZoUzAFyp3V3xi/MubUA/mCT4Fhf038PC8XplhWnCmnK
/ZzyC2BSTRSqKVOuY2kB8Jia0lvvRIVoP+vVWJbYarf6p655E2/nANBMCWkgD49DA0VAMyI1OLFMYCXiU9bmzi9/y5i/vsaTpHPHidTofzLb
M65vMPva9HlovgXp0AvjtaqYMfDD0/4mAsYE92pxa+9k1QgCnRVObCpojpzsKTPvayPetTEgBdwnssjuc0kOBFX+q3HwRQxdrOLAqeYRjkMk
/trTSu2Z9Lik7CfF0AvjtqAhS4NHobGXUnB5DQs8hG8p/wMX1r4+8xkmyvQ50JVq72TVeXbz3HvpWaQJi57hJYTw4kGbtS+C2TigQUtZUX+X
27QQq2ePBZBru/0lxTm8fOOQ5yaZOZMAV+he4FqIMB+LQB0UgMSajANX29j+vbmly8ipRvHeSQoQOkM5iFXcPQCVwDMs5RBCQmaPOyvbNd6u
wvQJ183BZQG3Zc+Eiv7vQOKu8YeDmMcJlt2ckyftVeMIGLBCmdMHl/tFILYwGPjXWO3zOfSq/+om+oa7Mlh2fpSsRGLp7RAW3FUVjNHgiMhy
E6zBFjM2BdkdJGO7nP1kJXWAtBuBpPIAu7f+hhu7bFXIuC5xWrf0X2xreykOsUyKkF2gwadbrXDcXrfKxR43zGcSj4t/cCgr+a1iy6EjE5GY
ktUCl9fwfMeylyooGF48bN2IGLTw8x7StS7sj8TF9FmPGWQhm3rRR+o9lhvjJvSYAdfDUevI1M6bnX/OwWaDMOQ8RPgKRo0eulBTdT8AW2kl
8e9L7UHghHwMfLiZPNoSpx0yugpQZaFqKWqxVSM3a2pN1SAhC2jf94I7ybBI7EL5A2Wvu5ht3xsoEt4+Ay/abXgCQAxyOeDsDlTCQzy75ohc
Ggv9Tra9uiymRUYTLrswOLlCdfAQf7HPDQQ4ErAH5EDXB9cMxWYpjtXApRncojS0sbV/cCgHTHwGNBJy+1PQE2x56FpaVR7wfQGZ37V+V+19
EiHNvR6q1fRUjqvbjbMq1/qfHxbTrE10ePY2gPFk48D2CVMTf1AF4PXvyYR9dV6Wf7H413m3xTWQvYGhQ7mfYwA5mAX+18Vue05v/8jG/fZX
/IW5MKPKtjSYlt0ellxh+/BOCPAwYaeVr0QofZFxJWVWC8znG70au6llVmktsF0bfHF6k8fvZ5esZJbwHwwnjg59tXz6sL/P0NUZDuSNu1mn
J8Vab17+cy005A9wtOpp3i0bZdpJLUil00semAwN45LgEViZYe3amNye0B6A9chviSlzXVsFtyN5/1H3gaNmMpn8Fz0GpYFp6Zw615H/LpUu
RQQDMCL82n5DpBSawkvzIdN2ypiT8nSLth8Pk9jnjwdFzH3W4XW6KMBfwB569NdcGX93mC16tTflcArcYUc/mFuYbV+8zY0SAjAVoNErNgWj
twumJ3wbn/HlBFYdxHvSkJJEc+Ngal9opSwyo9YlITX2C/P/+gf8sxURSLR+mcZUmeqaS9wrh6vxW5zxFCOqFi90RbDWq/YwZmnu1+a6Ovdp
vRqkNxxe44lyl4OobEnpKA6Uox5EfH9xzPs/HRKrTPWdIQrK1VZDU7ETiD3Obpl+8wPPCRBbkbwNtpW9AbBe5L1SMlj3tdTxk/9W47JUmqS5
HU+JzYymUKXjtWVmT9RenIhgXc+nroWLyxXJhmL112OdB8GCsk4f8oZJucnvmmtR85mBn10GZ0EKSCMUSAR3ukcXd5s7LvLD3me61WkuTCpJ
zYAyRurMB44EdEJzTfU271lUJC03YjXJXzYOGZwN4D8eB5jlfLrdWfzGRW7icMPfiSO6Oe7s20bmhdgLX4Z23B+s3JgQESzUDiMboSzDMHFp
NMwccGePauhfwjzwnI2wu9zKGgEFg80jcZ7MHllk07s1H+5yojtUQTlH4nFdLKTGwDmPbIklOb1L1zO4T6N8NCuDLFLS/C63c0eNRimZ++s5
BMBHxU11jHchI9oFVUxRh/eMDzHEzGYu0Lg8gJ7oS/tFCwoic44fyUtix0n/46vP4bf+//BRgAYwDDar4ncHIAAAAASUVORK5CYII=">
</div>

<script type="text/javascript">
    (function(){
        var tab_tit  = document.getElementById('think_page_trace_tab_tit').getElementsByTagName('span');
        var tab_cont = document.getElementById('think_page_trace_tab_cont').getElementsByTagName('div');
        var open     = document.getElementById('think_page_trace_open');
        var close    = document.getElementById('think_page_trace_close').children[0];
        var trace    = document.getElementById('think_page_trace_tab');
        var cookie   = document.cookie.match(/thinkphp_show_page_trace=(\d\|\d)/);
        var history  = (cookie && typeof cookie[1] != 'undefined' && cookie[1].split('|')) || [0,0];
        open.onclick = function(){
            trace.style.display = 'block';
            this.style.display = 'none';
            close.parentNode.style.display = 'block';
            history[0] = 1;
            document.cookie = 'thinkphp_show_page_trace='+history.join('|')
        }
        close.onclick = function(){
            trace.style.display = 'none';
            this.parentNode.style.display = 'none';
            open.style.display = 'block';
            history[0] = 0;
            document.cookie = 'thinkphp_show_page_trace='+history.join('|')
        }
        for(var i = 0; i < tab_tit.length; i++){
            tab_tit[i].onclick = (function(i){
                return function(){
                    for(var j = 0; j < tab_cont.length; j++){
                        tab_cont[j].style.display = 'none';
                        tab_tit[j].style.color = '#999';
                    }
                    tab_cont[i].style.display = 'block';
                    tab_tit[i].style.color = '#000';
                    history[1] = i;
                    document.cookie = 'thinkphp_show_page_trace='+history.join('|')
                }
            })(i)
        }
        parseInt(history[0]) && open.click();
        tab_tit[history[1]].click();
    })();
</script>
</body>
</html>
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---



---

_**User**_

继续

---

_**Assistant**_

好的，我看到API文件已经更新了。现在让我继续解决404错误问题。从之前的测试结果来看，API调用仍然返回错误页面。让我检查一下ThinkPHP的路由配置和中间件设置：


---

Read file: server/config/app.php

---

让我检查一下中间件配置，看看是否有影响API访问的中间件：


---

Read file: server/config/middleware.php

---

让我检查一下adminapi应用的中间件配置：


---

Could not resolve URI: Error: 无法解析不存在的文件 'vscode-remote://ssh-remote+*************/www/wwwroot/ai/server/app/adminapi/middleware'

---

Could not resolve URI: Error: 无法解析不存在的文件 'vscode-remote://ssh-remote+*************/www/wwwroot/ai/server/middleware'

---

让我检查一下adminapi应用的配置文件：


---

<details>
            <summary>Listed directory server/app/adminapi/config • **1** results</summary>
        
| Name |
|-------|
| 📄 `route.php` |

</details>

---

Read file: server/app/adminapi/config/route.php

---

现在我明白了！adminapi应用有中间件配置，包括登录验证和权限认证。这意味着API调用需要登录认证。让我检查一下这些中间件：


---

Read file: server/app/adminapi/http/middleware/LoginMiddleware.php

---

现在我明白了问题所在！API调用需要登录认证，需要提供有效的token。让我检查一下BulletinController是否有免登录设置：


---

Read file: server/app/adminapi/controller/setting/BulletinController.php

---

让我检查一下BaseAdminController，看看是否有免登录设置：


---

Read file: server/app/adminapi/controller/BaseAdminController.php

---

让我检查一下BaseLikeAdminController：


---

Read file: server/app/common/controller/BaseLikeAdminController.php

---

现在我明白了！公告配置API需要登录认证，但是前端在调用时可能没有提供有效的token。让我检查一下前端的请求配置，看看token是如何处理的：


---

Read file: admin/src/utils/request/index.ts

---

很好！我看到前端请求配置中默认启用了token（`withToken: true`），并且会自动从`getToken()`获取token并添加到请求头中。这意味着问题可能不是token的问题。

让我检查一下前端的认证相关文件：


---

Could not find file 'admin/src/utils/auth/index.ts' in the workspace.

---

<details>
            <summary>Searched codebase "auth" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: admin/src/utils/auth.ts

---

现在让我检查一下缓存配置：


---

Read file: admin/src/enums/cacheEnums.ts

---

现在让我检查一下前端的配置文件，看看API的基础URL配置：


---

Read file: admin/src/config/index.ts

---

现在让我检查一下环境变量配置：


---

Could not find file 'admin/.env' in the workspace.

---

<details>
            <summary>Listed directory admin • **39** results</summary>
        
| Name |
|-------|
| 📄 `src_DPGNd.tar.gz` |
| 📄 `src_sCji5.tar.gz` |
| 📁 `node_modules` |
| 📄 `src.zip` |
| 📁 `src` |
| 📄 `src_Xp7aX.tar.gz` |
| 📄 `src_mRNZX.tar.gz` |
| 📄 `src_7Hyjb.tar.gz` |
| 📄 `src_3SEkW.tar.gz` |
| 📄 `src_7CiRa.tar.gz` |
| 📄 `src_F6MhT.tar.gz` |
| 📄 `src_TGKn8.tar.gz` |
| 📄 `src_FBde8.tar.gz` |
| 📄 `src_sNnJb.tar.gz` |
| 📄 `src_RMcSJ.tar.gz` |
| 📄 `src_8fxTh.tar.gz` |
| 📄 `components.d.ts` |
| 📄 `.eslintrc-auto-import.json` |
| 📄 `auto-imports.d.ts` |
| 📄 `src_63S64.tar.gz` |
| 📄 `src_MKz5N.tar.gz` |
| 📄 `src_8KTZT.tar.gz` |
| 📄 `src_peY8Y.tar.gz` |
| 📄 `yarn.lock` |
| 📄 `package-lock.json` |
| 📄 `tsconfig.json` |
| 📄 `package.json` |
| 📄 `.eslintrc.cjs` |
| 📄 `.gitignore` |
| 📁 `.vscode` |
| 📄 `README.md` |
| 📄 `global.d.ts` |
| 📄 `index.html` |
| 📄 `postcss.config.js` |
| 📁 `scripts` |
| 📄 `tailwind.config.js` |
| 📄 `tsconfig.config.json` |
| 📁 `typings` |
| 📄 `vite.config.ts` |

</details>

---

让我检查一下vite配置文件：


---

Read file: admin/vite.config.ts

---

现在让我检查一下前端是否已经构建并部署。让我查看一下public目录：


---

<details>
            <summary>Listed directory server/public/admin • **2** results</summary>
        
| Name |
|-------|
| 📁 `assets` |
| 📄 `index.html` |

</details>

---

很好！前端已经构建并部署了。现在让我检查一下nginx配置，看看是否正确代理了API请求：


---

Read file: docker/config/nginx/conf.d/default.conf

---

nginx配置看起来是正确的，它会将所有请求代理到PHP-FPM。现在让我重新测试一下API，但这次使用正确的URL格式。让我先检查一下前端实际访问的URL：


---

```bash
curl -X GET "http://localhost:180/adminapi/setting.bulletin/detail" -H "Content-Type: application/json" -H "token: test" -v
```

```
        .exception {
            margin-top: 20px;
        }
        .exception .message{
            padding: 12px;
            border: 1px solid #ddd;
            border-bottom: 0 none;
            line-height: 18px;
            font-size:16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .code{
            float: left;
            text-align: center;
            color: #fff;
            margin-right: 12px;
            padding: 16px;
            border-radius: 4px;
            background: #999;
        }
        .exception .source-code{
            padding: 6px;
            border: 1px solid #ddd;

            background: #f9f9f9;
            overflow-x: auto;

        }
        .exception .source-code pre{
            margin: 0;
        }
        .exception .source-code pre ol{
            margin: 0;
            color: #4288ce;
            display: inline-block;
            min-width: 100%;
            box-sizing: border-box;
            font-size:14px;
            font-family: "Century Gothic",Consolas,"Liberation Mono",Courier,Verdana,serif;
            padding-left: 40px;
        }
        .exception .source-code pre li{
            border-left: 1px solid #ddd;
            height: 18px;
            line-height: 18px;
        }
        .exception .source-code pre code{
            color: #333;
            height: 100%;
            display: inline-block;
            border-left: 1px solid #fff;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace{
            padding: 6px;
            border: 1px solid #ddd;
            border-top: 0 none;
            line-height: 16px;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace h2:hover {
            text-decoration: underline;
            cursor: pointer;
        }
        .exception .trace ol{
            margin: 12px;
        }
        .exception .trace ol li{
            padding: 2px 4px;
        }
        .exception div:last-child{
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        /* Exception Variables */
        .exception-var table{
            width: 100%;
            margin: 12px 0;
            box-sizing: border-box;
            table-layout:fixed;
            word-wrap:break-word;
        }
        .exception-var table caption{
            text-align: left;
            font-size: 16px;
            font-weight: bold;
            padding: 6px 0;
        }
        .exception-var table caption small{
            font-weight: 300;
            display: inline-block;
            margin-left: 10px;
            color: #ccc;
        }
        .exception-var table tbody{
            font-size: 13px;
            font-family: Consolas, "Liberation Mono", Courier, "微软雅黑",serif;
        }
        .exception-var table td{
            padding: 0 6px;
            vertical-align: top;
            word-break: break-all;
        }
        .exception-var table td:first-child{
            width: 28%;
            font-weight: bold;
            white-space: nowrap;
        }
        .exception-var table td pre{
            margin: 0;
        }

        /* Copyright Info */
        .copyright{
            margin-top: 24px;
            padding: 12px 0;
            border-top: 1px solid #eee;
        }

        /* SPAN elements with the classes below are added by prettyprint. */
        pre.prettyprint .pln { color: #000 }  /* plain text */
        pre.prettyprint .str { color: #080 }  /* string content */
        pre.prettyprint .kwd { color: #008 }  /* a keyword */
        pre.prettyprint .com { color: #800 }  /* a comment */
        pre.prettyprint .typ { color: #606 }  /* a type name */
        pre.prettyprint .lit { color: #066 }  /* a literal value */
        /* punctuation, lisp open bracket, lisp close bracket */
        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }
        pre.prettyprint .tag { color: #008 }  /* a markup tag name */
        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */
        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */
        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */
        pre.prettyprint .fun { color: red }  /* a function name */
    </style>
</head>
<body>
                <div class="exception">
        <div class="message">
            <div class="info">
                <div>
                    <h2>#0 [0]<abbr title="think\exception\HttpException">HttpException</abbr> in <a class="
toggle" title="/server/vendor/topthink/framework/src/helper.php line 53">helper.php line 53</a></h2>
                </div>
                <div><h1>页面不存在</h1></div>
            </div>
        </div>
                            <div class="source-code">
                    <pre class="prettyprint lang-php"><ol start="44"><li class="line-0-44"><code>     * @par
am integer|Response $code    状态码 或者 Response对象实例
</code></li><li class="line-0-45"><code>     * @param string           $message 错误信息
</code></li><li class="line-0-46"><code>     * @param array            $header  参数
</code></li><li class="line-0-47"><code>     */
</code></li><li class="line-0-48"><code>    function abort($code, string $message = '', array $header = [])
</code></li><li class="line-0-49"><code>    {
</code></li><li class="line-0-50"><code>        if ($code instanceof Response) {
</code></li><li class="line-0-51"><code>            throw new HttpResponseException($code);
</code></li><li class="line-0-52"><code>        } else {
</code></li><li class="line-0-53 line-error"><code>            throw new HttpException($code, $message, null
, $header);
</code></li><li class="line-0-54"><code>        }
</code></li><li class="line-0-55"><code>    }
</code></li><li class="line-0-56"><code>}
</code></li><li class="line-0-57"><code>
</code></li><li class="line-0-58"><code>if (!function_exists('app')) {
</code></li><li class="line-0-59"><code>    /**
</code></li><li class="line-0-60"><code>     * 快速获取容器中的实例 支持依赖注入
</code></li><li class="line-0-61"><code>     * @template T
</code></li><li class="line-0-62"><code>     * @param string|class-string&lt;T&gt; $name        类名或标识 
默认获取当前应用实例
</code></li></ol></pre>
                </div>
                    <div class="trace">
            <h2 data-expand="1">Call Stack</h2>
            <ol>
                <li>in <a class="toggle" title="/server/vendor/topthink/framework/src/helper.php line 53">he
lper.php line 53</a></li>
                                    <li>
                        at abort(404, '页面不存在') in <a class="toggle" title="/server/app/adminapi/http/mi
ddleware/AdminIpMiddleware.php line 55">AdminIpMiddleware.php line 55</a>                    </li>
                                    <li>
                        at <abbr title="app\adminapi\http\middleware\AdminIpMiddleware">AdminIpMiddleware</a
bbr>->handle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure
">Closure</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\adminapi\http\middleware\AdminIp
Middleware">AdminIpMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>),
 <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/
framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Route.php l
ine 794">Route.php line 794</a>                    </li>
                                    <li>
                        at <abbr title="think\Route">Route</abbr>->dispatch(<em>object</em>(<abbr title="app
\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title
="/server/vendor/topthink/framework/src/think/Http.php line 216">Http.php line 216</a>                    </
li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->dispatchToRoute(<em>object</em>(<abbr title
="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http
.php line 206">Http.php line 206</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->think\{closure}(<em>object</em>(<abbr title
="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Pipe
line.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-multi-app/
src/MultiApp.php line 51">MultiApp.php line 51</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->think\app\{closure}(<em>object<
/em>(<abbr title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framewor
k/src/think/Pipeline.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-multi-app/src/MultiApp.ph
p line 52">MultiApp.php line 52</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->handle(<em>object</em>(<abbr ti
tle="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>))                   
 </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="think\app\MultiApp">MultiApp</abbr>)
, 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure
">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Middleware.php li
ne 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/app/common/http/middleware/BaseM
iddleware.php line 29">BaseMiddleware.php line 29</a>                    </li>
                                    <li>
                        at <abbr title="app\common\http\middleware\BaseMiddleware">BaseMiddleware</abbr>->ha
ndle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closur
e</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\common\http\middleware\BaseMiddl
eware">BaseMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>obj
ect</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framewor
k/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/app/common/http/middleware/LikeA
dminAllowMiddleware.php line 47">LikeAdminAllowMiddleware.php line 47</a>                    </li>
                                    <li>
                        at <abbr title="app\common\http\middleware\LikeAdminAllowMiddleware">LikeAdminAllowM
iddleware</abbr>->handle(<em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr ti
tle="Closure">Closure</abbr>))                    </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="app\common\http\middleware\LikeAdmin
AllowMiddleware">LikeAdminAllowMiddleware</abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Requ
est</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a class="toggle" title="/server/vend
or/topthink/framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li
>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/think-trace/src/
TraceDebug.php line 71">TraceDebug.php line 71</a>                    </li>
                                    <li>
                        at <abbr title="think\trace\TraceDebug">TraceDebug</abbr>->handle(<em>object</em>(<a
bbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>))             
       </li>
                                    <li>
                        at call_user_func([<em>object</em>(<abbr title="think\trace\TraceDebug">TraceDebug</
abbr>), 'handle'], <em>object</em>(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Middleware.
php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}(<em>object</em>
(<abbr title="app\Request">Request</abbr>), <em>object</em>(<abbr title="Closure">Closure</abbr>)) in <a cla
ss="toggle" title="/server/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a
>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}(<em>object</em>(<ab
br title="app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/th
ink/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then(<em>object</em>(<abbr title="C
losure">Closure</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http.php li
ne 207">Http.php line 207</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->runWithRequest(<em>object</em>(<abbr title=
"app\Request">Request</abbr>)) in <a class="toggle" title="/server/vendor/topthink/framework/src/think/Http.
php line 170">Http.php line 170</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->run() in <a class="toggle" title="/server/p
ublic/index.php line 29">index.php line 29</a>                    </li>
                            </ol>
        </div>
    </div>
                
    
        <div class="exception-var">
        <h2>Environment Variables</h2>
                <table>
                        <caption>GET Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>POST Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Files<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Cookies<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Session<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Server/Request Data</caption>
            <tbody>
                                <tr>
                    <td>HOSTNAME</td>
                    <td>dca4a834c126</td>
                </tr>
                                <tr>
                    <td>PHP_VERSION</td>
                    <td>8.0.30</td>
                </tr>
                                <tr>
                    <td>PHP_INI_DIR</td>
                    <td>/usr/local/etc/php</td>
                </tr>
                                <tr>
                    <td>GPG_KEYS</td>
                    <td>1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F 2C
16C765DBE54A088130F1BC4B9B5F600B55F3B4 39B641343D8C104B2B146DC3F9C39DC0B9698544</td>
                </tr>
                                <tr>
                    <td>PHP_LDFLAGS</td>
                    <td>-Wl,-O1 -pie</td>
                </tr>
                                <tr>
                    <td>PWD</td>
                    <td>/server</td>
                </tr>
                                <tr>
                    <td>TZ</td>
                    <td>Asia/Shanghai</td>
                </tr>
                                <tr>
                    <td>HOME</td>
                    <td>/</td>
                </tr>
                                <tr>
                    <td>PHP_SHA256</td>
                    <td>216ab305737a5d392107112d618a755dc5df42058226f1670e9db90e77d777d9</td>
                </tr>
                                <tr>
                    <td>PHPIZE_DEPS</td>
                    <td>autoconf                dpkg-dev                file            g++             gcc 
libc-dev                make            pkg-config              re2c</td>
                </tr>
                                <tr>
                    <td>PHP_URL</td>
                    <td>https://www.php.net/distributions/php-8.0.30.tar.xz</td>
                </tr>
                                <tr>
                    <td>SHLVL</td>
                    <td>0</td>
                </tr>
                                <tr>
                    <td>PHP_CFLAGS</td>
                    <td>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<
/td>
                </tr>
                                <tr>
                    <td>PATH</td>
                    <td>/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin</td>
                </tr>
                                <tr>
                    <td>PHP_ASC_URL</td>
                    <td>https://www.php.net/distributions/php-8.0.30.tar.xz.asc</td>
                </tr>
                                <tr>
                    <td>PHP_CPPFLAGS</td>
                    <td>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<
/td>
                </tr>
                                <tr>
                    <td>_</td>
                    <td>/usr/local/sbin/php-fpm</td>
                </tr>
                                <tr>
                    <td>HTTP_TOKEN</td>
                    <td>test</td>
                </tr>
                                <tr>
                    <td>HTTP_CONTENT_TYPE</td>
                    <td>application/json</td>
                </tr>
                                <tr>
                    <td>HTTP_ACCEPT</td>
                    <td>*/*</td>
                </tr>
                                <tr>
                    <td>HTTP_USER_AGENT</td>
                    <td>curl/7.61.1</td>
                </tr>
                                <tr>
                    <td>HTTP_HOST</td>
                    <td>localhost:180</td>
                </tr>
                                <tr>
                    <td>REDIRECT_STATUS</td>
                    <td>200</td>
                </tr>
                                <tr>
                    <td>SERVER_NAME</td>
                    <td>www.chatmoney.localhost</td>
                </tr>
                                <tr>
                    <td>SERVER_PORT</td>
                    <td>80</td>
                </tr>
                                <tr>
                    <td>SERVER_ADDR</td>
                    <td>**********</td>
                </tr>
                                <tr>
                    <td>REMOTE_PORT</td>
                    <td>40986</td>
                </tr>
                                <tr>
                    <td>REMOTE_ADDR</td>
                    <td>**********</td>
                </tr>
                                <tr>
                    <td>SERVER_SOFTWARE</td>
                    <td>nginx/1.23.1</td>
                </tr>
                                <tr>
                    <td>GATEWAY_INTERFACE</td>
                    <td>CGI/1.1</td>
                </tr>
                                <tr>
                    <td>REQUEST_SCHEME</td>
                    <td>http</td>
                </tr>
                                <tr>
                    <td>SERVER_PROTOCOL</td>
                    <td>HTTP/1.1</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_ROOT</td>
                    <td>/etc/nginx/html</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_URI</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_URI</td>
                    <td>/adminapi/setting.bulletin/detail</td>
                </tr>
                                <tr>
                    <td>SCRIPT_NAME</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>CONTENT_LENGTH</td>
                    <td></td>
                </tr>
                                <tr>
                    <td>CONTENT_TYPE</td>
                    <td>application/json</td>
                </tr>
                                <tr>
                    <td>REQUEST_METHOD</td>
                    <td>GET</td>
                </tr>
                                <tr>
                    <td>QUERY_STRING</td>
                    <td>s=adminapi/setting.bulletin/detail</td>
                </tr>
                                <tr>
                    <td>SCRIPT_FILENAME</td>
                    <td>/server/public/index.php</td>
                </tr>
                                <tr>
                    <td>FCGI_ROLE</td>
                    <td>RESPONDER</td>
                </tr>
                                <tr>
                    <td>PHP_SELF</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME_FLOAT</td>
                    <td>1752816699.1128</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME</td>
                    <td>1752816699</td>
                </tr>
                                <tr>
                    <td>argv</td>
                    <td>[
    &quot;s=adminapi\/setting.bulletin\/detail&quot;
]</td>
                </tr>
                                <tr>
                    <td>argc</td>
                    <td>1</td>
                </tr>
                            </tbody>
                    </table>
            </div>
    
    <div class="copyright">
        <a title="官方网站" href="http://www.thinkphp.cn">ThinkPHP</a> 
        <span>V6.1.4</span> 
        <span>{ 十年磨一剑-为API开发设计的高性能框架 }</span>
        <span>- <a title="官方手册" href="https://www.kancloud.cn/manual/thinkphp6_0/content">官方手册</a></
span>
    </div>
        <script>
        function $(selector, node){
            var elements;

            node = node || document;
            if(document.querySelectorAll){
                elements = node.querySelectorAll(selector);
            } else {
                switch(selector.substr(0, 1)){
                    case '#':
                        elements = [node.getElementById(selector.substr(1))];
                        break;
                    case '.':
                        if(document.getElementsByClassName){
                            elements = node.getElementsByClassName(selector.substr(1));
                        } else {
                            elements = get_elements_by_class(selector.substr(1), node);
                        }
                        break;
                    default:
                        elements = node.getElementsByTagName();
                }
            }
            return elements;

            function get_elements_by_class(search_class, node, tag) {
                var elements = [], eles, 
                    pattern  = new RegExp('(^|\\s)' + search_class + '(\\s|$)');

                node = node || document;
                tag  = tag  || '*';

                eles = node.getElementsByTagName(tag);
                for(var i = 0; i < eles.length; i++) {
                    if(pattern.test(eles[i].className)) {
                        elements.push(eles[i])
                    }
                }

                return elements;
            }
        }

        $.getScript = function(src, func){
            var script = document.createElement('script');
            
            script.async  = 'async';
            script.src    = src;
            script.onload = func || function(){};
            
            $('head')[0].appendChild(script);
        }

        ;(function(){
            var files = $('.toggle');
            var ol    = $('ol', $('.prettyprint')[0]);
            var li    = $('li', ol[0]);   

            // 短路径和长路径变换
            for(var i = 0; i < files.length; i++){
                files[i].ondblclick = function(){
                    var title = this.title;

                    this.title = this.innerHTML;
                    this.innerHTML = title;
                }
            }

            (function () {
                var expand = function (dom, expand) {
                    var ol = $('ol', dom.parentNode)[0];
                    expand = undefined === expand ? dom.attributes['data-expand'].value === '0' : undefined;
                    if (expand) {
                        dom.attributes['data-expand'].value = '1';
                        ol.style.display = 'none';
                        dom.innerText = 'Call Stack (展开)';
                    } else {
                        dom.attributes['data-expand'].value = '0';
                        ol.style.display = 'block';
                        dom.innerText = 'Call Stack (折叠)';
                    }
                };
                var traces = $('.trace');
                for (var i = 0; i < traces.length; i ++) {
                    var h2 = $('h2', traces[i])[0];
                    expand(h2);
                    h2.onclick = function () {
                        expand(this);
                    };
                }
            })();

            $.getScript('//cdn.bootcdn.net/ajax/libs/prettify/r298/prettify.min.js', function(){
                prettyPrint();
            });
        })();
    </script>
    <div id="think_page_trace" style="position: fixed;bottom:0;right:0;font-size:14px;width:100%;z-index: 99
9999;color: #000;text-align:left;font-family:'微软雅黑';">
    <div id="think_page_trace_tab" style="display: none;background:white;margin:0;height: 250px;">
        <div id="think_page_trace_tab_tit" style="height:30px;padding: 6px 12px 0;border-bottom:1px solid #e
cecec;border-top:1px solid #ececec;font-size:16px">
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">基本</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">文件</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">流程</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">错误</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">SQL</span>
                        <span style="color:#000;padding-right:12px;height:30px;line-height:30px;display:inli
ne-block;margin-right:3px;cursor:pointer;font-weight:700">调试</span>
                    </div>
        <div id="think_page_trace_tab_cont" style="overflow:auto;height:212px;padding:0;line-height: 24px">
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                    <li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">请求信息 : 2025-0
7-18 13:31:39 HTTP/1.1 GET : http://localhost:180/adminapi/setting.bulletin/detail</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">运行时间 : 0.098550s [ 吞吐率：10.15req/s ] 内存消耗：2,61
5.54kb 文件加载：145</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">查询信息 : 0
 queries</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">缓存信息 : 0 reads,0 wri
tes</li>                </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                    <li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/public/in
dex.php ( 1.10 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendo
r/autoload.php ( 0.75 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/composer/autoload_real.php ( 1.63 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;p
adding:0 12px">/server/vendor/composer/platform_check.php ( 0.90 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/composer/ClassLoader.php ( 15.99 KB )</li><li style="bor
der-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/composer/autoload_static.php ( 38.34
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/depr
ecation-contracts/function.php ( 0.99 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;paddin
g:0 12px">/server/vendor/ralouphie/getallheaders/src/getallheaders.php ( 1.60 KB )</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzlehttp/psr7/src/functions_include.php (
 0.15 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzleh
ttp/psr7/src/functions.php ( 13.09 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0
 12px">/server/vendor/guzzlehttp/guzzle/src/functions_include.php ( 0.16 KB )</li><li style="border-bottom:1
px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/guzzlehttp/guzzle/src/functions.php ( 5.55 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-m
bstring/bootstrap.php ( 7.33 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px"
>/server/vendor/symfony/polyfill-mbstring/bootstrap80.php ( 8.84 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/src/helper.php ( 7.35 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php80/bo
otstrap.php ( 1.50 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/v
endor/adbario/php-dot-notation/src/helpers.php ( 0.57 KB )</li><li style="border-bottom:1px solid #EEE;font-
size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php72/bootstrap.php ( 1.89 KB )</li><li style="bor
der-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-ctype/bootstrap.php
 ( 1.56 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfo
ny/polyfill-ctype/bootstrap80.php ( 1.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pad
ding:0 12px">/server/vendor/symfony/polyfill-intl-normalizer/bootstrap.php ( 0.71 KB )</li><li style="border
-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-intl-normalizer/bootst
rap80.php ( 0.70 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/think-orm/stubs/load_stubs.php ( 0.16 KB )</li><li style="border-bottom:1px solid #EEE;font-siz
e:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Exception.php ( 1.69 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Faca
de.php ( 2.71 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor
/symfony/polyfill-intl-idn/bootstrap.php ( 4.52 KB )</li><li style="border-bottom:1px solid #EEE;font-size:1
4px;padding:0 12px">/server/vendor/symfony/polyfill-php81/bootstrap.php ( 0.72 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/polyfill-php73/bootstrap.php ( 0.9
9 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/symfony/tra
nslation/Resources/functions.php ( 0.55 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/vendor/ezyang/htmlpurifier/library/HTMLPurifier.composer.php ( 0.10 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/mtdowling/jmespath.php/src/JmesPa
th.php ( 0.36 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor
/overtrue/socialite/src/Contracts/FactoryInterface.php ( 0.47 KB )</li><li style="border-bottom:1px solid #E
EE;font-size:14px;padding:0 12px">/server/vendor/overtrue/socialite/src/Contracts/UserInterface.php ( 1.04 K
B )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/overtrue/socia
lite/src/Contracts/ProviderInterface.php ( 2.89 KB )</li><li style="border-bottom:1px solid #EEE;font-size:1
4px;padding:0 12px">/server/vendor/alibabacloud/client/src/Functions.php ( 5.33 KB )</li><li style="border-b
ottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/qcloud/cos-sdk-v5/src/Common.php ( 2.40 K
B )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/qiniu/php-sdk/
src/Qiniu/functions.php ( 8.20 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/qiniu/php-sdk/src/Qiniu/Config.php ( 3.56 KB )</li><li style="border-bottom:1px solid #EEE
;font-size:14px;padding:0 12px">/server/vendor/ramsey/uuid/src/functions.php ( 5.03 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/rmccue/requests/library/Deprecated.ph
p ( 0.53 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/rmcc
ue/requests/src/Autoload.php ( 9.12 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/vendor/symfony/var-dumper/Resources/functions/dump.php ( 0.79 KB )</li><li style="border-bot
tom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-queue/src/common.php ( 1.01 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/fram
ework/src/think/App.php ( 14.17 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12
px">/server/vendor/topthink/framework/src/think/Container.php ( 15.38 KB )</li><li style="border-bottom:1px 
solid #EEE;font-size:14px;padding:0 12px">/server/vendor/psr/container/src/ContainerInterface.php ( 1.02 KB 
)</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/provider.php ( 0.19
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/fra
mework/src/think/Http.php ( 6.12 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 1
2px">/server/vendor/topthink/think-helper/src/helper/Str.php ( 7.28 KB )</li><li style="border-bottom:1px so
lid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Env.php ( 4.64 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/common.php ( 15.43 KB )</
li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/
src/helper.php ( 18.44 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serv
er/vendor/topthink/framework/src/think/Config.php ( 5.03 KB )</li><li style="border-bottom:1px solid #EEE;fo
nt-size:14px;padding:0 12px">/server/config/ai.php ( 37.72 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/config/app.php ( 1.02 KB )</li><li style="border-bottom:1px solid #EE
E;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/facade/Env.php ( 1.67 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/cache.php ( 1.25 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/console.php ( 1.76
 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/cookie.php (
 0.56 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/databas
e.php ( 3.21 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/config/
filesystem.php ( 0.63 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/config/lang.php ( 0.81 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/config/log.php ( 1.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/
server/config/middleware.php ( 0.19 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/config/project.php ( 8.82 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/framework/src/think/facade/Request.php ( 8.92 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/Request.php ( 0.14 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Reque
st.php ( 54.04 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/c
ommon/enum/VoiceEnum.php ( 6.26 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12
px">/server/config/queue.php ( 1.23 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:
0 12px">/server/config/route.php ( 1.54 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/config/session.php ( 0.57 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14p
x;padding:0 12px">/server/config/trace.php ( 0.34 KB )</li><li style="border-bottom:1px solid #EEE;font-size
:14px;padding:0 12px">/server/config/view.php ( 0.82 KB )</li><li style="border-bottom:1px solid #EEE;font-s
ize:14px;padding:0 12px">/server/app/event.php ( 0.35 KB )</li><li style="border-bottom:1px solid #EEE;font-
size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Event.php ( 6.96 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/service.php ( 0.13 KB )</li><li style
="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/AppService.php ( 1.00 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/th
ink/Service.php ( 1.67 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serv
er/vendor/topthink/framework/src/think/Lang.php ( 7.60 KB )</li><li style="border-bottom:1px solid #EEE;font
-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/lang/zh-cn.php ( 12.88 KB )</li><li style="
border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/init
ializer/Error.php ( 3.27 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/vendor/topthink/framework/src/think/initializer/RegisterService.php ( 1.33 KB )</li><li style="border-b
ottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/services.php ( 0.20 KB )</li><li style="b
order-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/servi
ce/PaginatorService.php ( 1.52 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/topthink/framework/src/think/service/ValidateService.php ( 0.99 KB )</li><li style="border
-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/service/Mo
delService.php ( 1.76 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/topthink/think-multi-app/src/Service.php ( 1.08 KB )</li><li style="border-bottom:1px solid #EEE;fo
nt-size:14px;padding:0 12px">/server/vendor/topthink/think-queue/src/queue/Service.php ( 1.24 KB )</li><li s
tyle="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-trace/src/Se
rvice.php ( 0.77 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/framework/src/think/Middleware.php ( 6.78 KB )</li><li style="border-bottom:1px solid #EEE;font
-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/initializer/BootService.php ( 0.77 KB
 )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-
orm/src/Paginator.php ( 11.80 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/framework/src/think/Validate.php ( 46.10 KB )</li><li style="border-bottom:1px sol
id #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/Model.php ( 25.28 KB )</li><li 
style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/src/
contract/Arrayable.php ( 0.09 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/think-helper/src/contract/Jsonable.php ( 0.13 KB )</li><li style="border-bottom:1p
x solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/Attribute.ph
p ( 17.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/top
think/think-orm/src/model/concern/RelationShip.php ( 26.12 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/ModelEvent.php ( 2.27 KB 
)</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-o
rm/src/model/concern/TimeStamp.php ( 5.70 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/think-orm/src/model/concern/Conversion.php ( 10.41 KB )</li><li style=
"border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Db.
php ( 2.87 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/to
pthink/think-orm/src/DbManager.php ( 8.49 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;pa
dding:0 12px">/server/vendor/topthink/framework/src/think/Log.php ( 8.50 KB )</li><li style="border-bottom:1
px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Manager.php ( 3.98 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/psr/log/Psr/L
og/LoggerInterface.php ( 3.04 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px
">/server/vendor/topthink/framework/src/think/Cache.php ( 4.79 KB )</li><li style="border-bottom:1px solid #
EEE;font-size:14px;padding:0 12px">/server/vendor/psr/simple-cache/src/CacheInterface.php ( 4.50 KB )</li><l
i style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-helper/sr
c/helper/Arr.php ( 15.54 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/se
rver/vendor/topthink/framework/src/think/cache/driver/Redis.php ( 6.72 KB )</li><li style="border-bottom:1px
 solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/cache/Driver.php ( 8.
06 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/f
ramework/src/think/contract/CacheHandlerInterface.php ( 2.25 KB )</li><li style="border-bottom:1px solid #EE
E;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Console.php ( 22.65 KB )</li><l
i style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/middleware.php ( 0.21 KB )<
/li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework
/src/think/Pipeline.php ( 2.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12p
x">/server/vendor/topthink/think-trace/src/TraceDebug.php ( 2.94 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/app/common/http/middleware/LikeAdminAllowMiddleware.php ( 1.96 
KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/common/http/midd
leware/BaseMiddleware.php ( 1.08 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 1
2px">/server/vendor/topthink/think-multi-app/src/MultiApp.php ( 7.07 KB )</li><li style="border-bottom:1px s
olid #EEE;font-size:14px;padding:0 12px">/server/app/adminapi/config/route.php ( 1.35 KB )</li><li style="bo
rder-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/app/adminapi/event.php ( 0.95 KB )</li><li
 style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/th
ink/Route.php ( 23.96 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/serve
r/vendor/topthink/framework/src/think/route/RuleName.php ( 5.33 KB )</li><li style="border-bottom:1px solid 
#EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/Domain.php ( 5.55 KB )
</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framewor
k/src/think/route/RuleGroup.php ( 13.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padd
ing:0 12px">/server/vendor/topthink/framework/src/think/route/Rule.php ( 22.85 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/RuleIte
m.php ( 8.81 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/
topthink/framework/src/think/route/dispatch/Url.php ( 3.42 KB )</li><li style="border-bottom:1px solid #EEE;
font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/route/dispatch/Controller.php ( 6
.61 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/
framework/src/think/route/Dispatch.php ( 6.49 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14p
x;padding:0 12px">/server/app/adminapi/http/middleware/AdminIpMiddleware.php ( 5.26 KB )</li><li style="bord
er-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/facade/C
onfig.php ( 1.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/ven
dor/topthink/framework/src/think/exception/HttpException.php ( 1.20 KB )</li><li style="border-bottom:1px so
lid #EEE;font-size:14px;padding:0 12px">/server/app/ExceptionHandle.php ( 2.11 KB )</li><li style="border-bo
ttom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/exception/Han
dle.php ( 10.02 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vend
or/topthink/framework/src/think/Response.php ( 8.60 KB )</li><li style="border-bottom:1px solid #EEE;font-si
ze:14px;padding:0 12px">/server/vendor/topthink/framework/src/tpl/think_exception.tpl ( 17.03 KB )</li><li s
tyle="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/thin
k/facade/App.php ( 2.73 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/ser
ver/vendor/topthink/framework/src/think/response/Html.php ( 0.98 KB )</li><li style="border-bottom:1px solid
 #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/Cookie.php ( 6.28 KB )</li>
<li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/topthink/think-trace/s
rc/Html.php ( 4.37 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/v
endor/topthink/framework/src/think/log/driver/File.php ( 6.17 KB )</li><li style="border-bottom:1px solid #E
EE;font-size:14px;padding:0 12px">/server/vendor/topthink/framework/src/think/contract/LogHandlerInterface.p
hp ( 0.86 KB )</li><li style="border-bottom:1px solid #EEE;font-size:14px;padding:0 12px">/server/vendor/top
think/framework/src/think/log/Channel.php ( 6.54 KB )</li>                </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                        <div style="display:none;">
                <ol style="padding: 0; margin:0">
                                    </ol>
            </div>
                    </div>
    </div>
    <div id="think_page_trace_close" style="display:none;text-align:right;height:15px;position:absolute;top:
10px;right:12px;cursor:pointer;"><img style="vertical-align:top;" src="data:image/gif;base64,R0lGODlhDwAPAJE
AAAAAAAMDA////wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4
gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCA
yMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmR
mLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzE
uMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmN
vbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1
NOkluc3RhbmNlSUQ9InhtcC5paWQ6MUQxMjc1MUJCQUJDMTFFMTk0OUVGRjc3QzU4RURFNkEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ
6MUQxMjc1MUNCQUJDMTFFMTk0OUVGRjc3QzU4RURFNkEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo
xRDEyNzUxOUJBQkMxMUUxOTQ5RUZGNzdDNThFREU2QSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxRDEyNzUxQUJBQkMxMUUxOTQ5RUZ
GNzdDNThFREU2QSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgH//v3
8+/r5+Pf29fTz8vHw7+7t7Ovq6ejn5uXk4+Lh4N/e3dzb2tnY19bV1NPS0dDPzs3My8rJyMfGxcTDwsHAv769vLu6ubi3trW0s7KxsK+uray
rqqmop6alpKOioaCfnp2cm5qZmJeWlZSTkpGQj46NjIuKiYiHhoWEg4KBgH9+fXx7enl4d3Z1dHNycXBvbm1sa2ppaGdmZWRjYmFgX15dXFt
aWVhXVlVUU1JRUE9OTUxLSklIR0ZFRENCQUA/Pj08Ozo5ODc2NTQzMjEwLy4tLCsqKSgnJiUkIyIhIB8eHRwbGhkYFxYVFBMSERAPDg0MCwo
JCAcGBQQDAgEAACH5BAAAAAAALAAAAAAPAA8AAAIdjI6JZqotoJPR1fnsgRR3C2jZl3Ai9aWZZooV+RQAOw==" /></div>
</div>
<div id="think_page_trace_open" style="height:30px;float:right;text-align:right;overflow:hidden;position:fix
ed;bottom:0;right:0;color:#000;line-height:30px;cursor:pointer;">
    <div style="background:#232323;color:#FFF;padding:0 6px;float:right;line-height:30px;font-size:14px">0.1
02543s </div>
    <img width="30" style="" title="ShowPageTrace" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAA
AwCAYAAABXAvmHAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eH
BhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bW
V0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPH
JkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbi
ByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2
JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIH
htcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjVERDVENkZGQj
kyNDExRTE5REY3RDQ5RTQ2RTRDQUJCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjVERDVENzAwQjkyNDExRTE5REY3RDQ5RTQ2RTRDQU
JCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NURENUQ2RkRCOTI0MTFFMTlERjdENDlFNDZFNENBQk
IiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NURENUQ2RkVCOTI0MTFFMTlERjdENDlFNDZFNENBQkIiLz4gPC9yZGY6RGVzY3JpcHRpb2
4+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5fx6IRAAAMCElEQVR42sxae3BU1Rk/9+69+8xuNtkHJAFCSI
AkhMgjCCJQUi0GtEIVbP8Qq9LH2No6TmfaztjO2OnUdvqHFMfOVFTqIK0vUEEeqUBARCsEeYQkEPJoEvIiELLvvc9z+p27u2F3s5tsBB1OZi
ebu5dzf7/v/L7f952zMM8cWIwY+Mk2ulCp92Fnq3XvnzArr2NZnYNldDp0Gw+/OEQ4+obQn5D+4Ubb22+YOGsWi/Todh8AHglKEGkEsnHBQ1
62511GZFgW6ZCBM9/W4H3iNSQqIe09O196dLKX7d1O39OViP/wthtkND62if/wj/DbMpph8BY/m9xy8BoBmQk+mHqZQGNy4JYRwCoRbwa8l4
JXw6M+orJxpU0U6ToKy/5bQsAiTeokGKkTx46RRxxEUgrwGgF4MWNNEJCGgYTvpgnY1IJWg5RzfqLgvcIgktX0i8dmMlFA8qCQ5L0Z/WObPL
UxT1i4lWSYDISoEfBYGvM+LlMQQdkLHoWRRZ8zYQI62Thswe5WTORGwNXDcGjqeOA9AF7B8rhzsxMBEoJ8oJKaqPu4hblHMCMPwl9XeNWyb8
xkB/DDGYKfMAE6aFL7xesZ389JlgG3XHEMI6UPDOP6JHHu67T2pwNPI69mCP4rEaBDUAJaKc/AOuXiwH07VCS3w5+UQMAuF/WqGI+yFIwVNB
wemBD4r0wgQiKoFZa00sEYTwss32lA1tPwVxtc8jQ5/gWCwmGCyUD8vRT0sHBFW4GJDvZmrJFWRY1EkrGA6ZB8/10fOZSSj0E6F+BSP7xidi
IzhBmKB09lEwHPkG+UQIyEN44EBiT5vrv2uJXyPQqSqO930fxvcvwbR/+JAkD9EfASgI9EHlp6YiHO4W+cAB20SnrFqxBbNljiXf1Pl1K2S0
HCWfiog3YlAD5RGwwxK6oUjTweuVigLjyB0mX410mAFnMoVK1lvvUvgt8fUJH0JVyjuvcmg4dE5mUiFtD24AZ4qBVELxXKS+pMxN43kSdzNw
udJ+bQbLlmnxvPOQoCugSap1GnSRoG8KOiKbH+rIA0lEeSAg3y6eeQ6XI2nrYnrPM89bUTgI0Pdqvl50vlNbtZxDUBcLBK0kPd5jPziyLdoj
JIN0pq5/mdzwL4UVvVInV5ncQEPNOUxa9d0TU+CW5l+FoI0GSDKHVVSOs+0KOsZoxwOzSZNFGv0mQ9avyLCh2Hpm+70Y0YJoJVgmQv822wnD
C8Miq6VjJ5IFed0QD1YiAbT+nQE8v/RMZfmgmcCRHIIu7Bmcp39oM9fqEychcA747KxQ/AEyqQonl7hATtJmnhO2XYtgcia01aSbVMenAXrI
omPcLgEBA4liGBzFZAT8zBYqW6brI67wg8sFVhxBhwLwBP2+tqBQqqK7VJKGh/BRrfTr6nWL7nYBaZdBJHqrX3kPEPap56xwE/GvjJTRMADe
MCdcGpGXL1Xh4ZL8BDOlWkUpegfi0CeDzeA5YITzEnddv+IXL+UYCmqIvqC9UlUC/ki9FipwVjunL3yX7dOTLeXmVMAhbsGporPfyOBTm/BJ
23gTVehsvXRnSewagUfpBXF3p5pygKS7OceqTjb7h2vjr/XKm0ZofKSI2Q/J102wHzatZkJPYQ5JoKsuK+EoHJakVzubzuLQDepCKllTZi9A
G0DYg9ZLxhFaZsOu7bvlmVI5oPXJMQJcHxHClSln1apFTvAimeg48u0RWFeZW4lVcjbQWZuIQK1KozZfIDO6CSQmQQXdpBaiKZyEWThVK1uE
c6v7V7uK0ysduExPZx4vysDR+4SelhBYm0R6LBuR4PXts8MYMcJPsINo4YZCDLj0sgB0/vLpPXvA2Tn42Cv5rsLulGubzW0sEd3d4W/mJt2K
ck+DzDMijfPLOjyrDhXSh852B+OvflqAkoyXO1cYfujtc/i3jJSAwhgfFlp20laMLOku/bC7prgqW7lCn4auE5NhcXPd3M7x70+IceSgZvNl
jCd9k3fLjYsPElqLR14PXQZqD2ZNkkrAB79UeJUebFQmXpf8ZcAQt2XrMQdyNUVBqZoUzAFyp3V3xi/MubUA/mCT4Fhf038PC8XplhWnCmnK
/ZzyC2BSTRSqKVOuY2kB8Jia0lvvRIVoP+vVWJbYarf6p655E2/nANBMCWkgD49DA0VAMyI1OLFMYCXiU9bmzi9/y5i/vsaTpHPHidTofzLb
M65vMPva9HlovgXp0AvjtaqYMfDD0/4mAsYE92pxa+9k1QgCnRVObCpojpzsKTPvayPetTEgBdwnssjuc0kOBFX+q3HwRQxdrOLAqeYRjkMk
/trTSu2Z9Lik7CfF0AvjtqAhS4NHobGXUnB5DQs8hG8p/wMX1r4+8xkmyvQ50JVq72TVeXbz3HvpWaQJi57hJYTw4kGbtS+C2TigQUtZUX+X
27QQq2ePBZBru/0lxTm8fOOQ5yaZOZMAV+he4FqIMB+LQB0UgMSajANX29j+vbmly8ipRvHeSQoQOkM5iFXcPQCVwDMs5RBCQmaPOyvbNd6u
wvQJ183BZQG3Zc+Eiv7vQOKu8YeDmMcJlt2ckyftVeMIGLBCmdMHl/tFILYwGPjXWO3zOfSq/+om+oa7Mlh2fpSsRGLp7RAW3FUVjNHgiMhy
E6zBFjM2BdkdJGO7nP1kJXWAtBuBpPIAu7f+hhu7bFXIuC5xWrf0X2xreykOsUyKkF2gwadbrXDcXrfKxR43zGcSj4t/cCgr+a1iy6EjE5GY
ktUCl9fwfMeylyooGF48bN2IGLTw8x7StS7sj8TF9FmPGWQhm3rRR+o9lhvjJvSYAdfDUevI1M6bnX/OwWaDMOQ8RPgKRo0eulBTdT8AW2kl
8e9L7UHghHwMfLiZPNoSpx0yugpQZaFqKWqxVSM3a2pN1SAhC2jf94I7ybBI7EL5A2Wvu5ht3xsoEt4+Ay/abXgCQAxyOeDsDlTCQzy75ohc
Ggv9Tra9uiymRUYTLrswOLlCdfAQf7HPDQQ4ErAH5EDXB9cMxWYpjtXApRncojS0sbV/cCgHTHwGNBJy+1PQE2x56FpaVR7wfQGZ37V+V+19
EiHNvR6q1fRUjqvbjbMq1/qfHxbTrE10ePY2gPFk48D2CVMTf1AF4PXvyYR9dV6Wf7H413m3xTWQvYGhQ7mfYwA5mAX+18Vue05v/8jG/fZX
/IW5MKPKtjSYlt0ellxh+/BOCPAwYaeVr0QofZFxJWVWC8znG70au6llVmktsF0bfHF6k8fvZ5esZJbwHwwnjg59tXz6sL/P0NUZDuSNu1mn
J8Vab17+cy005A9wtOpp3i0bZdpJLUil00semAwN45LgEViZYe3amNye0B6A9chviSlzXVsFtyN5/1H3gaNmMpn8Fz0GpYFp6Zw615H/LpUu
RQQDMCL82n5DpBSawkvzIdN2ypiT8nSLth8Pk9jnjwdFzH3W4XW6KMBfwB569NdcGX93mC16tTflcArcYUc/mFuYbV+8zY0SAjAVoNErNgWj
twumJ3wbn/HlBFYdxHvSkJJEc+Ngal9opSwyo9YlITX2C/P/+gf8sxURSLR+mcZUmeqaS9wrh6vxW5zxFCOqFi90RbDWq/YwZmnu1+a6Ovdp
vRqkNxxe44lyl4OobEnpKA6Uox5EfH9xzPs/HRKrTPWdIQrK1VZDU7ETiD3Obpl+8wPPCRBbkbwNtpW9AbBe5L1SMlj3tdTxk/9W47JUmqS5
HU+JzYymUKXjtWVmT9RenIhgXc+nroWLyxXJhmL112OdB8GCsk4f8oZJucnvmmtR85mBn10GZ0EKSCMUSAR3ukcXd5s7LvLD3me61WkuTCpJ
zYAyRurMB44EdEJzTfU271lUJC03YjXJXzYOGZwN4D8eB5jlfLrdWfzGRW7icMPfiSO6Oe7s20bmhdgLX4Z23B+s3JgQESzUDiMboSzDMHFp
NMwccGePauhfwjzwnI2wu9zKGgEFg80jcZ7MHllk07s1H+5yojtUQTlH4nFdLKTGwDmPbIklOb1L1zO4T6N8NCuDLFLS/C63c0eNRimZ++s5
BMBHxU11jHchI9oFVUxRh/eMDzHEzGYu0Lg8gJ7oS/tFCwoic44fyUtix0n/46vP4bf+//BRgAYwDDar4ncHIAAAAASUVORK5CYII=">
</div>

<script type="text/javascript">
    (function(){
        var tab_tit  = document.getElementById('think_page_trace_tab_tit').getElementsByTagName('span');
        var tab_cont = document.getElementById('think_page_trace_tab_cont').getElementsByTagName('div');
        var open     = document.getElementById('think_page_trace_open');
        var close    = document.getElementById('think_page_trace_close').children[0];
        var trace    = document.getElementById('think_page_trace_tab');
        var cookie   = document.cookie.match(/thinkphp_show_page_trace=(\d\|\d)/);
        var history  = (cookie && typeof cookie[1] != 'undefined' && cookie[1].split('|')) || [0,0];
        open.onclick = function(){
            trace.style.display = 'block';
            this.style.display = 'none';
            close.parentNode.style.display = 'block';
            history[0] = 1;
            document.cookie = 'thinkphp_show_page_trace='+history.join('|')
        }
        close.onclick = function(){
            trace.style.display = 'none';
            this.parentNode.style.display = 'none';
            open.style.display = 'block';
            history[0] = 0;
            document.cookie = 'thinkphp_show_page_trace='+history.join('|')
        }
        for(var i = 0; i < tab_tit.length; i++){
            tab_tit[i].onclick = (function(i){
                return function(){
                    for(var j = 0; j < tab_cont.length; j++){
                        tab_cont[j].style.display = 'none';
                        tab_tit[j].style.color = '#999';
                    }
                    tab_cont[i].style.display = 'block';
                    tab_tit[i].style.color = '#000';
                    history[1] = i;
                    document.cookie = 'thinkphp_show_page_trace='+history.join('|')
                }
            })(i)
        }
        parseInt(history[0]) && open.click();
        tab_tit[history[1]].click();
    })();
</script>
</body>
</html>
* Connection #0 to host localhost left intact
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---

看起来仍然有错误。让我检查一下PHP错误日志，看看具体的错误信息：


---

```bash
cd /www/wwwroot/ai && docker exec chatmoney-php tail -f /var/log/php_errors.log
```

```
[root@iZ2ze776l6a0t8ydszrzbbZ ai]# cd /www/wwwroot/ai && docker exec chatmoney-php tail -f /var/log/php_erro
rs.log
tail: cannot open '/var/log/php_errors.log' for reading: No such file or directory
tail: no files remaining
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---

让我检查一下PHP的错误日志位置：


---

```bash
docker exec chatmoney-php find /var/log -name "*.log" 2>/dev/null
```

```
[root@iZ2ze776l6a0t8ydszrzbbZ ai]# docker exec chatmoney-php find /var/log -name "*.log" 2>/dev/null
/var/log/dalle3.out.log
/var/log/dalle3.err.log
/var/log/qa.out.log
/var/log/sd.err.log
/var/log/crontab.out.log
/var/log/em.out.log
/var/log/em.err.log
/var/log/crontab.err.log
/var/log/supervisord.log
/var/log/qa.err.log
/var/log/sd.out.log
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---

_**User**_

配置是没有问题的，注意是docker环境

---

_**Assistant**_

您说得对，配置没有问题。让我重新分析一下这个404错误的问题。

从错误信息来看，前端调用的是：
- `/adminapi/setting.setting.bulletin/detail` 
- `/adminapi/setting.setting.bulletin/save`

但是我们已经修正为：
- `/adminapi/setting.bulletin/detail`
- `/adminapi/setting.bulletin/save`

现在让我检查一下前端是否已经重新构建并部署了最新的代码。问题可能是前端还在使用旧的API路径。


---

```bash
cd /www/wwwroot/ai && docker exec chatmoney-nginx ls -la /server/public/admin/assets/
```

```
-rwxr-xr-x 1 <USER> <GROUP>    3424 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.91c5eb12.js
-rwxr-xr-x 1 <USER> <GROUP>    8031 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.927096c3.js
-rwxr-xr-x 1 <USER> <GROUP>   10755 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.93770673.js
-rwxr-xr-x 1 <USER> <GROUP>    2674 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.95938578.js
-rwxr-xr-x 1 <USER> <GROUP>    4220 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.99907ae5.js
-rwxr-xr-x 1 <USER> <GROUP>    3282 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.9a19ee37.js
-rwxr-xr-x 1 <USER> <GROUP>    5505 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.9ae33ce6.js
-rwxr-xr-x 1 <USER> <GROUP>    5570 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.9b967ae1.js
-rwxr-xr-x 1 <USER> <GROUP>    3855 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.9f02feec.js
-rwxr-xr-x 1 <USER> <GROUP>    4220 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.9f90441f.js
-rwxr-xr-x 1 <USER> <GROUP>    5673 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.a6d573a9.js
-rwxr-xr-x 1 <USER> <GROUP>    2575 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.a7b129a8.js
-rwxr-xr-x 1 <USER> <GROUP>    2571 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.a7ecf489.js
-rwxr-xr-x 1 <USER> <GROUP>    2262 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.aa18124d.js
-rwxr-xr-x 1 <USER> <GROUP>    4220 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.aafd845f.js
-rwxr-xr-x 1 <USER> <GROUP>    5673 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.abff0135.js
-rwxr-xr-x 1 <USER> <GROUP>    3161 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.ad2317b8.js
-rwxr-xr-x 1 <USER> <GROUP>    4298 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.ae3ff344.js
-rwxr-xr-x 1 <USER> <GROUP>    2715 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.ae548707.js
-rwxr-xr-x 1 <USER> <GROUP>    2262 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.b386a04b.js
-rwxr-xr-x 1 <USER> <GROUP>    2713 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.b3ec20c2.js
-rwxr-xr-x 1 <USER> <GROUP>    3879 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.b476c162.js
-rwxr-xr-x 1 <USER> <GROUP>    3867 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.bb3a9d0c.js
-rwxr-xr-x 1 <USER> <GROUP>    3879 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.bc30d405.js
-rwxr-xr-x 1 <USER> <GROUP>    4065 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.bc4e05dc.js
-rwxr-xr-x 1 <USER> <GROUP>    4614 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.bce90a11.js
-rwxr-xr-x 1 <USER> <GROUP>    4298 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.bdcfe82a.js
-rwxr-xr-x 1 <USER> <GROUP>    6010 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.be34373b.js
-rwxr-xr-x 1 <USER> <GROUP>    3180 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.bf433f75.js
-rwxr-xr-x 1 <USER> <GROUP>    2417 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.bf9e5cb8.js
-rwxr-xr-x 1 <USER> <GROUP>    2246 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.c2d7f66f.js
-rwxr-xr-x 1 <USER> <GROUP>    2713 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.c41ecaa2.js
-rwxr-xr-x 1 <USER> <GROUP>    2709 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.c475e9c1.js
-rwxr-xr-x 1 <USER> <GROUP>    2417 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.c4f1a78b.js
-rwxr-xr-x 1 <USER> <GROUP>    2015 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.c5e85d1e.js
-rwxr-xr-x 1 <USER> <GROUP>    3270 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.c6448def.js
-rwxr-xr-x 1 <USER> <GROUP>    2453 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.c646fa39.js
-rwxr-xr-x 1 <USER> <GROUP>    8031 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.c678a7c4.js
-rwxr-xr-x 1 <USER> <GROUP>    5240 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.d090cf88.js
-rwxr-xr-x 1 <USER> <GROUP>    3282 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.d0957f41.js
-rwxr-xr-x 1 <USER> <GROUP>    2709 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.d1dc7c9b.js
-rwxr-xr-x 1 <USER> <GROUP>    1948 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.d2f1fb3f.js
-rwxr-xr-x 1 <USER> <GROUP>    3828 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.d4ffc275.js
-rwxr-xr-x 1 <USER> <GROUP>    3879 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.d6a0da98.js
-rwxr-xr-x 1 <USER> <GROUP>    1937 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.d6fc4bf5.js
-rwxr-xr-x 1 <USER> <GROUP>    3879 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.da98ca5e.js
-rwxr-xr-x 1 <USER> <GROUP>   10755 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.daf048ac.js
-rwxr-xr-x 1 <USER> <GROUP>    2015 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.de57e079.js
-rwxr-xr-x 1 <USER> <GROUP>    1948 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.de5d80b5.js
-rwxr-xr-x 1 <USER> <GROUP>    4298 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.e10f6f4e.js
-rwxr-xr-x 1 <USER> <GROUP>    2471 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.e2ae786c.js
-rwxr-xr-x 1 <USER> <GROUP>    2571 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.e2e31933.js
-rwxr-xr-x 1 <USER> <GROUP>    2206 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.e4cfc1cb.js
-rwxr-xr-x 1 <USER> <GROUP>    8031 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.e539ea15.js
-rwxr-xr-x 1 <USER> <GROUP>    3119 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.e7157560.js
-rwxr-xr-x 1 <USER> <GROUP>    3282 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.e9cb3e4b.js
-rwxr-xr-x 1 <USER> <GROUP>    3119 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.ec410642.js
-rwxr-xr-x 1 <USER> <GROUP>    2015 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.ef584321.js
-rwxr-xr-x 1 <USER> <GROUP>    2471 Jul 17 10:53 edit.vue_vue_type_script_setup_true_lang.f133afcb.js
-rwxr-xr-x 1 <USER> <GROUP>    3161 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.f3c23fba.js
-rwxr-xr-x 1 <USER> <GROUP>    3867 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.f71186cd.js
-rwxr-xr-x 1 <USER> <GROUP>    3282 Jul 18 09:39 edit.vue_vue_type_script_setup_true_lang.f76b91de.js
-rwxr-xr-x 1 <USER> <GROUP>    3270 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.f82b2d88.js
-rwxr-xr-x 1 <USER> <GROUP>    4492 Jul 17 12:07 edit.vue_vue_type_script_setup_true_lang.ff9fe165.js
-rwxr-xr-x 1 <USER> <GROUP>  262099 Jul 18 09:39 element-plus.956244c7.css
-rwxr-xr-x 1 <USER> <GROUP>  620814 Jul 18 09:39 element-plus.e8677eae.js
-rwxr-xr-x 1 <USER> <GROUP>    1337 Jul 17 12:07 embedding.33670157.js
-rwxr-xr-x 1 <USER> <GROUP>    1337 Jul 18 09:39 embedding.62332d69.js
-rwxr-xr-x 1 <USER> <GROUP>    3649 Jul 17 12:07 embedding.vue_vue_type_script_setup_true_lang.1fc942d0.js
-rwxr-xr-x 1 <USER> <GROUP>    3649 Jul 18 09:39 embedding.vue_vue_type_script_setup_true_lang.b2f495f4.js
-rwxr-xr-x 1 <USER> <GROUP>    4771 Jul 18 09:39 empty.dce45c37.png
-rwxr-xr-x 1 <USER> <GROUP>    2933 Jul 17 12:07 environment.3665d5f7.js
-rwxr-xr-x 1 <USER> <GROUP>    2933 Jul 17 10:53 environment.9dc6d36e.js
-rwxr-xr-x 1 <USER> <GROUP>    2933 Jul 18 09:39 environment.dd0030a5.js
-rwxr-xr-x 1 <USER> <GROUP>    1432 Jul 18 09:39 error.0310298a.js
-rwxr-xr-x 1 <USER> <GROUP>     240 Jul 18 09:39 error.be93caf8.css
-rwxr-xr-x 1 <USER> <GROUP>     529 Jul 18 09:39 escape-html.e5dfadb9.js
-rwxr-xr-x 1 <USER> <GROUP>    2957 Jul 17 12:07 example.025e2106.js
-rwxr-xr-x 1 <USER> <GROUP>    2957 Jul 18 09:39 example.3c74bd55.js
-rwxr-xr-x 1 <USER> <GROUP>    2957 Jul 17 10:53 example.52e3d992.js
-rwxr-xr-x 1 <USER> <GROUP>   53243 Jul 18 09:39 example.5f35833a.png
-rwxr-xr-x 1 <USER> <GROUP>    3264 Jul 17 12:07 example.5f89a929.js
-rwxr-xr-x 1 <USER> <GROUP>    3264 Jul 18 09:39 example.6c2db485.js
-rwxr-xr-x 1 <USER> <GROUP>    2873 Jul 17 10:53 example.8b5fa2f6.js
-rwxr-xr-x 1 <USER> <GROUP>    2873 Jul 18 09:39 example.c54d2e99.js
-rwxr-xr-x 1 <USER> <GROUP>    2873 Jul 17 12:07 example.d4b1b7d3.js
-rwxr-xr-x 1 <USER> <GROUP>    3264 Jul 17 10:53 example.f331de16.js
-rwxr-xr-x 1 <USER> <GROUP>     521 Jul 17 10:53 example_category.5e05564d.js
-rwxr-xr-x 1 <USER> <GROUP>     521 Jul 17 12:07 example_category.bbd2ecd5.js
-rwxr-xr-x 1 <USER> <GROUP>     521 Jul 18 09:39 example_category.d8186681.js
-rwxr-xr-x 1 <USER> <GROUP>    4197 Jul 17 12:07 file.019c8ba7.js
-rwxr-xr-x 1 <USER> <GROUP>    4197 Jul 18 09:39 file.70004251.js
-rwxr-xr-x 1 <USER> <GROUP>     384 Jul 18 09:39 file.8578288e.css
-rwxr-xr-x 1 <USER> <GROUP>    4197 Jul 17 10:53 file.d15b2aa0.js
-rwxr-xr-x 1 <USER> <GROUP>     218 Jul 18 09:39 file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js
-rwxr-xr-x 1 <USER> <GROUP>    4597 Jul 18 09:39 filing.11c6b96b.js
-rwxr-xr-x 1 <USER> <GROUP>    4597 Jul 17 10:53 filing.767b43ce.js
-rwxr-xr-x 1 <USER> <GROUP>    4597 Jul 17 12:07 filing.b1246eec.js
-rwxr-xr-x 1 <USER> <GROUP>     680 Jul 18 09:39 finance.408586fa.js
-rwxr-xr-x 1 <USER> <GROUP>     680 Jul 17 12:07 finance.668c543b.js
-rwxr-xr-x 1 <USER> <GROUP>     680 Jul 17 10:53 finance.f59bed7b.js
-rwxr-xr-x 1 <USER> <GROUP>    4946 Jul 18 09:39 follow_reply.236b7e63.js
-rwxr-xr-x 1 <USER> <GROUP>    4946 Jul 17 12:07 follow_reply.9a0a2780.js
-rwxr-xr-x 1 <USER> <GROUP>    4946 Jul 17 10:53 follow_reply.bd920d12.js
-rwxr-xr-x 1 <USER> <GROUP>    1967 Jul 17 12:07 form.284ef5b3.js
-rwxr-xr-x 1 <USER> <GROUP>    1967 Jul 17 10:53 form.289d73d0.js
-rwxr-xr-x 1 <USER> <GROUP>    1967 Jul 18 09:39 form.96d3bcac.js
-rwxr-xr-x 1 <USER> <GROUP>   11969 Jul 17 10:53 form.vue_vue_type_script_setup_true_lang.361af602.js
-rwxr-xr-x 1 <USER> <GROUP>   11623 Jul 17 12:07 form.vue_vue_type_script_setup_true_lang.698af313.js
-rwxr-xr-x 1 <USER> <GROUP>   11623 Jul 18 09:39 form.vue_vue_type_script_setup_true_lang.ae882227.js
-rwxr-xr-x 1 <USER> <GROUP>     467 Jul 17 10:53 gift.3ebe6972.js
-rwxr-xr-x 1 <USER> <GROUP>     467 Jul 17 12:07 gift.a90b1a4b.js
-rwxr-xr-x 1 <USER> <GROUP>     467 Jul 18 09:39 gift.ba44de18.js
-rwxr-xr-x 1 <USER> <GROUP>   17902 Jul 18 09:39 github-markdown-css.6e1ea547.css
-rwxr-xr-x 1 <USER> <GROUP>     371 Jul 18 09:39 h5.125cd3a7.js
-rwxr-xr-x 1 <USER> <GROUP>    3602 Jul 17 12:07 h5.5f3721a2.js
-rwxr-xr-x 1 <USER> <GROUP>    3602 Jul 17 10:53 h5.621fe679.js
-rwxr-xr-x 1 <USER> <GROUP>    3602 Jul 18 09:39 h5.6a0761bb.js
-rwxr-xr-x 1 <USER> <GROUP>     371 Jul 17 12:07 h5.a9321a8e.js
-rwxr-xr-x 1 <USER> <GROUP>     371 Jul 17 10:53 h5.cdd7cb56.js
-rwxr-xr-x 1 <USER> <GROUP>     857 Jul 18 09:39 highlight.b334430f.css
-rwxr-xr-x 1 <USER> <GROUP> 1059889 Jul 18 09:39 highlight.js.4f6161a5.js
-rwxr-xr-x 1 <USER> <GROUP>    3474 Jul 18 09:39 icon.2f9aba10.js
-rwxr-xr-x 1 <USER> <GROUP>    3474 Jul 17 12:07 icon.c667eca9.js
-rwxr-xr-x 1 <USER> <GROUP>    3469 Jul 17 10:53 icon.fbb4217a.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 17 10:53 imports.081fff8b.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 18 09:39 imports.168c4478.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 18 09:39 imports.21326083.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 17 12:07 imports.27f62903.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 17 12:07 imports.53e06790.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 17 10:53 imports.59211402.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 17 12:07 imports.7536e5f0.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 18 09:39 imports.9b67a512.js
-rwxr-xr-x 1 <USER> <GROUP>    1361 Jul 17 10:53 imports.c3fb3d99.js
-rwxr-xr-x 1 <USER> <GROUP>    1883 Jul 17 12:07 imports.vue_vue_type_script_setup_true_lang.0454b886.js
-rwxr-xr-x 1 <USER> <GROUP>    1869 Jul 17 10:53 imports.vue_vue_type_script_setup_true_lang.0f8b0c7f.js
-rwxr-xr-x 1 <USER> <GROUP>    1892 Jul 17 12:07 imports.vue_vue_type_script_setup_true_lang.787ad093.js
-rwxr-xr-x 1 <USER> <GROUP>    1861 Jul 17 10:53 imports.vue_vue_type_script_setup_true_lang.8b6388b2.js
-rwxr-xr-x 1 <USER> <GROUP>    1861 Jul 17 12:07 imports.vue_vue_type_script_setup_true_lang.a1e7f964.js
-rwxr-xr-x 1 <USER> <GROUP>    1883 Jul 18 09:39 imports.vue_vue_type_script_setup_true_lang.a7b85217.js
-rwxr-xr-x 1 <USER> <GROUP>    1861 Jul 18 09:39 imports.vue_vue_type_script_setup_true_lang.c9cce214.js
-rwxr-xr-x 1 <USER> <GROUP>    1883 Jul 17 10:53 imports.vue_vue_type_script_setup_true_lang.d429c947.js
-rwxr-xr-x 1 <USER> <GROUP>    1892 Jul 18 09:39 imports.vue_vue_type_script_setup_true_lang.e768ae4d.js
-rwxr-xr-x 1 <USER> <GROUP>    5774 Jul 18 09:39 index.0011c79c.js
-rwxr-xr-x 1 <USER> <GROUP>    4710 Jul 17 12:07 index.0111c75a.js
-rwxr-xr-x 1 <USER> <GROUP>   10674 Jul 18 09:39 index.031a31e4.js
-rwxr-xr-x 1 <USER> <GROUP>    5048 Jul 17 10:53 index.03307200.js
-rwxr-xr-x 1 <USER> <GROUP>    5237 Jul 18 09:39 index.045a7030.js
-rwxr-xr-x 1 <USER> <GROUP>   11743 Jul 17 12:07 index.049b4131.js
-rwxr-xr-x 1 <USER> <GROUP>    6619 Jul 17 12:07 index.04bddbf2.js
-rwxr-xr-x 1 <USER> <GROUP>    4592 Jul 17 10:53 index.05adff41.js
-rwxr-xr-x 1 <USER> <GROUP>    4592 Jul 18 09:39 index.0627537a.js
-rwxr-xr-x 1 <USER> <GROUP>    6688 Jul 17 12:07 index.06f52bfe.js
-rwxr-xr-x 1 <USER> <GROUP>    7865 Jul 17 10:53 index.076d6c5d.js
-rwxr-xr-x 1 <USER> <GROUP>    3132 Jul 17 12:07 index.08b75b4d.js
-rwxr-xr-x 1 <USER> <GROUP>    4742 Jul 17 12:07 index.0b300457.js
-rwxr-xr-x 1 <USER> <GROUP>    7956 Jul 17 10:53 index.0bf98ae6.js
-rwxr-xr-x 1 <USER> <GROUP>    6738 Jul 17 12:07 index.0c121269.js
-rwxr-xr-x 1 <USER> <GROUP>    6578 Jul 18 09:39 index.0c9c2524.js
-rwxr-xr-x 1 <USER> <GROUP>    4592 Jul 17 12:07 index.0dca6adf.js
-rwxr-xr-x 1 <USER> <GROUP>    5969 Jul 18 09:39 index.0df56b4a.js
-rwxr-xr-x 1 <USER> <GROUP>     667 Jul 18 09:39 index.0e042cc4.css
-rwxr-xr-x 1 <USER> <GROUP>    3680 Jul 18 09:39 index.0f3938cd.js
-rwxr-xr-x 1 <USER> <GROUP>    5287 Jul 17 12:07 index.0f461cfc.js
-rwxr-xr-x 1 <USER> <GROUP>    5740 Jul 17 10:53 index.102968c7.js
-rwxr-xr-x 1 <USER> <GROUP>    3796 Jul 17 10:53 index.1221c9a5.js
-rwxr-xr-x 1 <USER> <GROUP>    2531 Jul 17 12:07 index.12b527f5.js
-rwxr-xr-x 1 <USER> <GROUP>   10591 Jul 17 10:53 index.12fb867a.js
-rwxr-xr-x 1 <USER> <GROUP>    5893 Jul 17 10:53 index.142bf05c.js
-rwxr-xr-x 1 <USER> <GROUP>    5013 Jul 17 10:53 index.15ef1016.js
-rwxr-xr-x 1 <USER> <GROUP>    7379 Jul 18 09:39 index.1730c318.js
-rwxr-xr-x 1 <USER> <GROUP>     121 Jul 18 09:39 index.17a2b733.css
-rwxr-xr-x 1 <USER> <GROUP>    6165 Jul 17 12:07 index.18b589e6.js
-rwxr-xr-x 1 <USER> <GROUP>    6704 Jul 18 09:39 index.1a6b0929.js
-rwxr-xr-x 1 <USER> <GROUP>    5013 Jul 17 12:07 index.1abd292d.js
-rwxr-xr-x 1 <USER> <GROUP>    6382 Jul 18 09:39 index.1c23494f.js
-rwxr-xr-x 1 <USER> <GROUP>    5811 Jul 17 10:53 index.1c5ac916.js
-rwxr-xr-x 1 <USER> <GROUP>    4700 Jul 17 12:07 index.1cd5bee8.js
-rwxr-xr-x 1 <USER> <GROUP>    7100 Jul 17 12:07 index.1da3678b.js
-rwxr-xr-x 1 <USER> <GROUP>    7984 Jul 17 10:53 index.1e07a8d4.js
-rwxr-xr-x 1 <USER> <GROUP>    6633 Jul 18 09:39 index.1e8368dd.js
-rwxr-xr-x 1 <USER> <GROUP>    5780 Jul 17 10:53 index.1e8e3fe6.js
-rwxr-xr-x 1 <USER> <GROUP>    4982 Jul 17 12:07 index.1f0f3e54.js
-rwxr-xr-x 1 <USER> <GROUP>    7443 Jul 17 12:07 index.1f18d4a5.js
-rwxr-xr-x 1 <USER> <GROUP>    4584 Jul 18 09:39 index.2032f285.js
-rwxr-xr-x 1 <USER> <GROUP>   15285 Jul 17 10:53 index.2060b5f8.js
-rwxr-xr-x 1 <USER> <GROUP>    7757 Jul 17 10:53 index.2089e747.js
-rwxr-xr-x 1 <USER> <GROUP>  893723 Jul 17 10:53 index.217d39ed.js
-rwxr-xr-x 1 <USER> <GROUP>    5776 Jul 17 10:53 index.21fddcc7.js
-rwxr-xr-x 1 <USER> <GROUP>    9658 Jul 17 10:53 index.225b9d7a.js
-rwxr-xr-x 1 <USER> <GROUP>    5902 Jul 17 12:07 index.227bc543.js
-rwxr-xr-x 1 <USER> <GROUP>    7865 Jul 17 12:07 index.228555da.js
-rwxr-xr-x 1 <USER> <GROUP>    4986 Jul 18 09:39 index.22f5998b.js
-rwxr-xr-x 1 <USER> <GROUP>   15285 Jul 17 12:07 index.23b28a1d.js
-rwxr-xr-x 1 <USER> <GROUP>    8975 Jul 17 10:53 index.23ec5695.js
-rwxr-xr-x 1 <USER> <GROUP>    4982 Jul 17 12:07 index.2437b0e4.js
-rwxr-xr-x 1 <USER> <GROUP>    7100 Jul 17 10:53 index.24a2dae9.js
-rwxr-xr-x 1 <USER> <GROUP>     115 Jul 18 09:39 index.24d8bdcd.css
-rwxr-xr-x 1 <USER> <GROUP>  895594 Jul 18 09:39 index.25c46c99.js
-rwxr-xr-x 1 <USER> <GROUP>   10921 Jul 17 10:53 index.2619ed2f.js
-rwxr-xr-x 1 <USER> <GROUP>   11743 Jul 17 10:53 index.26ea62f9.js
-rwxr-xr-x 1 <USER> <GROUP>    4924 Jul 18 09:39 index.297b2b3f.js
-rwxr-xr-x 1 <USER> <GROUP>    8213 Jul 17 10:53 index.29b47771.js
-rwxr-xr-x 1 <USER> <GROUP>    4849 Jul 17 12:07 index.29f20e1d.js
-rwxr-xr-x 1 <USER> <GROUP>    5811 Jul 17 12:07 index.2b6c2f9b.js
-rwxr-xr-x 1 <USER> <GROUP>    6704 Jul 17 10:53 index.2b84fa9b.js
-rwxr-xr-x 1 <USER> <GROUP>   10756 Jul 18 09:39 index.2bb992e3.js
-rwxr-xr-x 1 <USER> <GROUP>    5287 Jul 17 10:53 index.2c0b1c7d.js
-rwxr-xr-x 1 <USER> <GROUP>    3774 Jul 18 09:39 index.2c1774fa.css
-rwxr-xr-x 1 <USER> <GROUP>    4982 Jul 17 10:53 index.2ca82169.js
-rwxr-xr-x 1 <USER> <GROUP>    5653 Jul 17 10:53 index.2ced0093.js
-rwxr-xr-x 1 <USER> <GROUP>    2540 Jul 17 10:53 index.2d1bc5b6.js
-rwxr-xr-x 1 <USER> <GROUP>    5879 Jul 17 12:07 index.2f64b72f.js
-rwxr-xr-x 1 <USER> <GROUP>   11028 Jul 17 12:07 index.30f7a51e.js
-rwxr-xr-x 1 <USER> <GROUP>    7694 Jul 17 12:07 index.314c8359.js
-rwxr-xr-x 1 <USER> <GROUP>    5653 Jul 18 09:39 index.31a92df6.js
-rwxr-xr-x 1 <USER> <GROUP>    5828 Jul 17 12:07 index.3225d314.js
-rwxr-xr-x 1 <USER> <GROUP>   37613 Jul 18 09:39 index.3326db0a.css
-rwxr-xr-x 1 <USER> <GROUP>    4588 Jul 17 12:07 index.33339fbb.js
-rwxr-xr-x 1 <USER> <GROUP>    4633 Jul 17 10:53 index.34c9ae40.js
-rwxr-xr-x 1 <USER> <GROUP>    5780 Jul 18 09:39 index.366a5680.js
-rwxr-xr-x 1 <USER> <GROUP>    5801 Jul 17 12:07 index.373a9bfd.js
-rwxr-xr-x 1 <USER> <GROUP>    6704 Jul 18 09:39 index.38a228f8.js
-rwxr-xr-x 1 <USER> <GROUP>    5545 Jul 18 09:39 index.3a1208e7.js
-rwxr-xr-x 1 <USER> <GROUP>    9872 Jul 17 10:53 index.3a51ff80.js
-rwxr-xr-x 1 <USER> <GROUP>    4592 Jul 18 09:39 index.3a804ea0.js
-rwxr-xr-x 1 <USER> <GROUP>    2540 Jul 17 12:07 index.3ae73855.js
-rwxr-xr-x 1 <USER> <GROUP>    4569 Jul 17 10:53 index.3aec0164.js
-rwxr-xr-x 1 <USER> <GROUP>    7757 Jul 18 09:39 index.3bb11e24.js
-rwxr-xr-x 1 <USER> <GROUP>    6961 Jul 17 12:07 index.3c114a32.js
-rwxr-xr-x 1 <USER> <GROUP>    8565 Jul 17 12:07 index.3c2c6f05.js
-rwxr-xr-x 1 <USER> <GROUP>   11231 Jul 17 10:53 index.3c447b5b.js
-rwxr-xr-x 1 <USER> <GROUP>    3698 Jul 18 09:39 index.3cb0bef6.js
-rwxr-xr-x 1 <USER> <GROUP>    4059 Jul 17 10:53 index.3cec0a94.js
-rwxr-xr-x 1 <USER> <GROUP>    3109 Jul 18 09:39 index.3cef8d16.js
-rwxr-xr-x 1 <USER> <GROUP>    6281 Jul 17 10:53 index.3f2ab32f.js
-rwxr-xr-x 1 <USER> <GROUP>    5969 Jul 17 10:53 index.3fd2f801.js
-rwxr-xr-x 1 <USER> <GROUP>    6704 Jul 17 10:53 index.402607b6.js
-rwxr-xr-x 1 <USER> <GROUP>    5044 Jul 17 12:07 index.40940e1c.js
-rwxr-xr-x 1 <USER> <GROUP>    6961 Jul 17 10:53 index.41654de8.js
-rwxr-xr-x 1 <USER> <GROUP>    6239 Jul 17 10:53 index.42cc929a.js
-rwxr-xr-x 1 <USER> <GROUP>    5893 Jul 17 12:07 index.4412b73c.js
-rwxr-xr-x 1 <USER> <GROUP>    6578 Jul 17 12:07 index.44518841.js
-rwxr-xr-x 1 <USER> <GROUP>    3468 Jul 17 12:07 index.45365a97.js
-rwxr-xr-x 1 <USER> <GROUP>    2540 Jul 18 09:39 index.45713b8a.js
-rwxr-xr-x 1 <USER> <GROUP>    6239 Jul 18 09:39 index.465f6232.js
-rwxr-xr-x 1 <USER> <GROUP>    3109 Jul 17 10:53 index.470ebfe2.js
-rwxr-xr-x 1 <USER> <GROUP>    6619 Jul 18 09:39 index.489d79a8.js
-rwxr-xr-x 1 <USER> <GROUP>    3132 Jul 17 10:53 index.49f4e22e.js
-rwxr-xr-x 1 <USER> <GROUP>    5013 Jul 18 09:39 index.4a485ca6.js
-rwxr-xr-x 1 <USER> <GROUP>    7984 Jul 18 09:39 index.4a6b7392.js
-rwxr-xr-x 1 <USER> <GROUP>    3795 Jul 17 10:53 index.4bcbbce5.js
-rwxr-xr-x 1 <USER> <GROUP>    7336 Jul 17 12:07 index.4bef1368.js
-rwxr-xr-x 1 <USER> <GROUP>    4569 Jul 17 12:07 index.4cb2153d.js
-rwxr-xr-x 1 <USER> <GROUP>    4710 Jul 17 10:53 index.4d549a68.js
-rwxr-xr-x 1 <USER> <GROUP>    4849 Jul 18 09:39 index.4e005151.js
-rwxr-xr-x 1 <USER> <GROUP>    7336 Jul 18 09:39 index.52315012.js
-rwxr-xr-x 1 <USER> <GROUP>    6738 Jul 18 09:39 index.530458f5.js
-rwxr-xr-x 1 <USER> <GROUP>    6688 Jul 18 09:39 index.563d0820.js
-rwxr-xr-x 1 <USER> <GROUP>    5776 Jul 18 09:39 index.56885206.js
-rwxr-xr-x 1 <USER> <GROUP>    9737 Jul 17 12:07 index.5755408f.js
-rwxr-xr-x 1 <USER> <GROUP>    8214 Jul 18 09:39 index.5774b231.js
-rwxr-xr-x 1 <USER> <GROUP>    7379 Jul 17 10:53 index.5783330c.js
-rwxr-xr-x 1 <USER> <GROUP>    3796 Jul 17 12:07 index.5841c185.js
-rwxr-xr-x 1 <USER> <GROUP>    9737 Jul 17 10:53 index.58e3d374.js
-rwxr-xr-x 1 <USER> <GROUP>    8206 Jul 17 10:53 index.596af809.js
-rwxr-xr-x 1 <USER> <GROUP>    5774 Jul 17 10:53 index.5976a124.js
-rwxr-xr-x 1 <USER> <GROUP>    3698 Jul 17 10:53 index.5b167bf6.js
-rwxr-xr-x 1 <USER> <GROUP>    4633 Jul 17 12:07 index.5b32eb29.js
-rwxr-xr-x 1 <USER> <GROUP>    3468 Jul 17 10:53 index.5bb88000.js
-rwxr-xr-x 1 <USER> <GROUP>    7711 Jul 17 12:07 index.5c142e6e.js
-rwxr-xr-x 1 <USER> <GROUP>    2370 Jul 17 12:07 index.5d2553c5.js
-rwxr-xr-x 1 <USER> <GROUP>    5902 Jul 18 09:39 index.5d4c33db.js
-rwxr-xr-x 1 <USER> <GROUP>    5788 Jul 18 09:39 index.5f5ebd64.js
-rwxr-xr-x 1 <USER> <GROUP>    8565 Jul 17 10:53 index.6105f3e2.js
-rwxr-xr-x 1 <USER> <GROUP>    6672 Jul 17 10:53 index.62039ee9.js
-rwxr-xr-x 1 <USER> <GROUP>    2531 Jul 17 10:53 index.6268b85f.js
-rwxr-xr-x 1 <USER> <GROUP>    4633 Jul 18 09:39 index.62931593.js
-rwxr-xr-x 1 <USER> <GROUP>    6633 Jul 17 12:07 index.62e5e556.js
-rwxr-xr-x 1 <USER> <GROUP>    3522 Jul 17 10:53 index.648685b0.js
-rwxr-xr-x 1 <USER> <GROUP>    5788 Jul 17 12:07 index.651543fb.js
-rwxr-xr-x 1 <USER> <GROUP>    8975 Jul 18 09:39 index.671ba271.js
-rwxr-xr-x 1 <USER> <GROUP>    5237 Jul 17 12:07 index.675f4f8b.js
-rwxr-xr-x 1 <USER> <GROUP>    5384 Jul 17 12:07 index.67f0b3fa.js
-rwxr-xr-x 1 <USER> <GROUP>    5828 Jul 18 09:39 index.68e7239c.js
-rwxr-xr-x 1 <USER> <GROUP>    4586 Jul 17 10:53 index.690a9249.js
-rwxr-xr-x 1 <USER> <GROUP>    5740 Jul 17 12:07 index.6917de27.js
-rwxr-xr-x 1 <USER> <GROUP>    5043 Jul 17 10:53 index.69c96329.js
-rwxr-xr-x 1 <USER> <GROUP>    7956 Jul 17 12:07 index.6a218664.js
-rwxr-xr-x 1 <USER> <GROUP>      52 Jul 18 09:39 index.6a24a5e8.css
-rwxr-xr-x 1 <USER> <GROUP>    4595 Jul 17 12:07 index.6a3647e3.js
-rwxr-xr-x 1 <USER> <GROUP>   11473 Jul 18 09:39 index.6a89f087.js
-rwxr-xr-x 1 <USER> <GROUP>    6619 Jul 17 10:53 index.6b31c8f5.js
-rwxr-xr-x 1 <USER> <GROUP>    4849 Jul 17 10:53 index.6bf05199.js
-rwxr-xr-x 1 <USER> <GROUP>    4592 Jul 17 10:53 index.6c4fd376.js
-rwxr-xr-x 1 <USER> <GROUP>    5801 Jul 18 09:39 index.6cc3a647.js
-rwxr-xr-x 1 <USER> <GROUP>    5879 Jul 18 09:39 index.6e38589a.js
-rwxr-xr-x 1 <USER> <GROUP>    9931 Jul 18 09:39 index.6ebc4681.js
-rwxr-xr-x 1 <USER> <GROUP>    5740 Jul 18 09:39 index.7058c156.js
-rwxr-xr-x 1 <USER> <GROUP>   11473 Jul 17 10:53 index.72177a1c.js
-rwxr-xr-x 1 <USER> <GROUP>    4742 Jul 17 10:53 index.722fb93a.js
-rwxr-xr-x 1 <USER> <GROUP>    4992 Jul 18 09:39 index.7289f36b.js
-rwxr-xr-x 1 <USER> <GROUP>    5048 Jul 17 12:07 index.72b92f1e.js
-rwxr-xr-x 1 <USER> <GROUP>    3109 Jul 17 12:07 index.742ded89.js
-rwxr-xr-x 1 <USER> <GROUP>    6578 Jul 17 10:53 index.7480fc47.js
-rwxr-xr-x 1 <USER> <GROUP>    6688 Jul 17 10:53 index.74f99d22.js
-rwxr-xr-x 1 <USER> <GROUP>    5788 Jul 17 10:53 index.7508c3f8.js
-rwxr-xr-x 1 <USER> <GROUP>    9872 Jul 17 12:07 index.75b4d2d0.js
-rwxr-xr-x 1 <USER> <GROUP>    3522 Jul 17 12:07 index.75f32ecb.js
-rwxr-xr-x 1 <USER> <GROUP>    7379 Jul 17 12:07 index.75f51a73.js
-rwxr-xr-x 1 <USER> <GROUP>    3701 Jul 17 10:53 index.763960a7.js
-rwxr-xr-x 1 <USER> <GROUP>    7100 Jul 18 09:39 index.76613b4d.js
-rwxr-xr-x 1 <USER> <GROUP>    5893 Jul 18 09:39 index.77cf12a1.js
-rwxr-xr-x 1 <USER> <GROUP>    5653 Jul 17 12:07 index.780e72da.js
-rwxr-xr-x 1 <USER> <GROUP>    3023 Jul 17 10:53 index.78a29e61.js
-rwxr-xr-x 1 <USER> <GROUP>    4986 Jul 17 12:07 index.795309e6.js
-rwxr-xr-x 1 <USER> <GROUP>    5287 Jul 18 09:39 index.79dc4936.js
-rwxr-xr-x 1 <USER> <GROUP>    4079 Jul 17 10:53 index.7a510f1d.js
-rwxr-xr-x 1 <USER> <GROUP>    9931 Jul 17 10:53 index.7a99bde4.js
-rwxr-xr-x 1 <USER> <GROUP>    8213 Jul 18 09:39 index.7b0ed320.js
-rwxr-xr-x 1 <USER> <GROUP>     672 Jul 17 12:07 index.7c59dd39.js
-rwxr-xr-x 1 <USER> <GROUP>   37650 Jul 17 10:53 index.7ce754bf.css
-rwxr-xr-x 1 <USER> <GROUP>    6809 Jul 18 09:39 index.7df5936d.js
-rwxr-xr-x 1 <USER> <GROUP>    5774 Jul 17 12:07 index.7e2b8b22.js
-rwxr-xr-x 1 <USER> <GROUP>  891801 Jul 17 12:07 index.7e86d223.js
-rwxr-xr-x 1 <USER> <GROUP>    5237 Jul 17 10:53 index.7eedbdef.js
-rwxr-xr-x 1 <USER> <GROUP>   11231 Jul 18 09:39 index.7f112874.js
-rwxr-xr-x 1 <USER> <GROUP>    3903 Jul 17 10:53 index.80487dc2.js
-rwxr-xr-x 1 <USER> <GROUP>    4586 Jul 17 12:07 index.80d4c0fb.js
-rwxr-xr-x 1 <USER> <GROUP>    5926 Jul 18 09:39 index.81e56943.js
-rwxr-xr-x 1 <USER> <GROUP>    8213 Jul 18 09:39 index.823740ce.js
-rwxr-xr-x 1 <USER> <GROUP>    8214 Jul 17 10:53 index.842dc79b.js
-rwxr-xr-x 1 <USER> <GROUP>    5969 Jul 17 12:07 index.84513cd0.js
-rwxr-xr-x 1 <USER> <GROUP>    5833 Jul 17 10:53 index.8456565a.js
-rwxr-xr-x 1 <USER> <GROUP>    5780 Jul 17 12:07 index.8467773c.js
-rwxr-xr-x 1 <USER> <GROUP>    4700 Jul 17 10:53 index.86732984.js
-rwxr-xr-x 1 <USER> <GROUP>    3023 Jul 17 12:07 index.876dd647.js
-rwxr-xr-x 1 <USER> <GROUP>     438 Jul 18 09:39 index.88e852a7.js
-rwxr-xr-x 1 <USER> <GROUP>    3998 Jul 17 12:07 index.8a603a6a.js
-rwxr-xr-x 1 <USER> <GROUP>    5828 Jul 17 12:07 index.8afc9d68.js
-rwxr-xr-x 1 <USER> <GROUP>    6961 Jul 18 09:39 index.8ba24ec3.js
-rwxr-xr-x 1 <USER> <GROUP>    4982 Jul 18 09:39 index.8c9b0a1a.js
-rwxr-xr-x 1 <USER> <GROUP>   10591 Jul 17 12:07 index.8eb76ec6.js
-rwxr-xr-x 1 <USER> <GROUP>    2370 Jul 18 09:39 index.8ec20744.js
-rwxr-xr-x 1 <USER> <GROUP>    5879 Jul 17 10:53 index.8f367fba.js
-rwxr-xr-x 1 <USER> <GROUP>    6706 Jul 17 10:53 index.8f3f6a9b.js
-rwxr-xr-x 1 <USER> <GROUP>   10756 Jul 17 10:53 index.910377cd.js
-rwxr-xr-x 1 <USER> <GROUP>    9873 Jul 18 09:39 index.92b28e7a.js
-rwxr-xr-x 1 <USER> <GROUP>    7711 Jul 18 09:39 index.92c602bb.js
-rwxr-xr-x 1 <USER> <GROUP>    8214 Jul 17 12:07 index.93188e5b.js
-rwxr-xr-x 1 <USER> <GROUP>    6706 Jul 17 12:07 index.9344bc5c.js
-rwxr-xr-x 1 <USER> <GROUP>     409 Jul 18 09:39 index.93fc0f3d.css
-rwxr-xr-x 1 <USER> <GROUP>    6704 Jul 17 12:07 index.94ff7474.js
-rwxr-xr-x 1 <USER> <GROUP>    4079 Jul 18 09:39 index.967937b0.js
-rwxr-xr-x 1 <USER> <GROUP>    7694 Jul 18 09:39 index.96ae7afa.js
-rwxr-xr-x 1 <USER> <GROUP>    5148 Jul 17 10:53 index.96af20db.js
-rwxr-xr-x 1 <USER> <GROUP>    6382 Jul 17 10:53 index.9a032dde.js
-rwxr-xr-x 1 <USER> <GROUP>    8133 Jul 18 09:39 index.9acd3df6.js
-rwxr-xr-x 1 <USER> <GROUP>     330 Jul 18 09:39 index.9bce7fe7.css
-rwxr-xr-x 1 <USER> <GROUP>    1580 Jul 18 09:39 index.9c240d63.css
-rwxr-xr-x 1 <USER> <GROUP>    5384 Jul 17 10:53 index.9c249ddd.js
-rwxr-xr-x 1 <USER> <GROUP>    4992 Jul 17 10:53 index.9d98fd3f.js
-rwxr-xr-x 1 <USER> <GROUP>    8133 Jul 17 10:53 index.9e39c31f.js
-rwxr-xr-x 1 <USER> <GROUP>    7443 Jul 18 09:39 index.9e5c3aeb.js
-rwxr-xr-x 1 <USER> <GROUP>    8213 Jul 17 12:07 index.9ecbfec7.js
-rwxr-xr-x 1 <USER> <GROUP>    3998 Jul 18 09:39 index.9ed61a1f.js
-rwxr-xr-x 1 <USER> <GROUP>    9658 Jul 17 12:07 index.9ef9bceb.js
-rwxr-xr-x 1 <USER> <GROUP>    5833 Jul 17 12:07 index.9f251ca9.js
-rwxr-xr-x 1 <USER> <GROUP>    3796 Jul 18 09:39 index.a0bb2bac.js
-rwxr-xr-x 1 <USER> <GROUP>   11743 Jul 18 09:39 index.a0d1ed54.js
-rwxr-xr-x 1 <USER> <GROUP>    4586 Jul 18 09:39 index.a10e7482.js
-rwxr-xr-x 1 <USER> <GROUP>    5786 Jul 17 12:07 index.a1cee58f.js
-rwxr-xr-x 1 <USER> <GROUP>    6239 Jul 17 12:07 index.a319f39a.js
-rwxr-xr-x 1 <USER> <GROUP>    9658 Jul 18 09:39 index.a35d1703.js
-rwxr-xr-x 1 <USER> <GROUP>     672 Jul 17 10:53 index.a3d7c3e4.js
-rwxr-xr-x 1 <USER> <GROUP>     496 Jul 18 09:39 index.a57531c5.css
-rwxr-xr-x 1 <USER> <GROUP>   11231 Jul 17 12:07 index.a5c65fbe.js
-rwxr-xr-x 1 <USER> <GROUP>    8213 Jul 17 12:07 index.a606d7da.js
-rwxr-xr-x 1 <USER> <GROUP>    7956 Jul 18 09:39 index.a66053a6.js
-rwxr-xr-x 1 <USER> <GROUP>    6165 Jul 17 10:53 index.a6e047a9.js
-rwxr-xr-x 1 <USER> <GROUP>    2266 Jul 18 09:39 index.a781b177.js
-rwxr-xr-x 1 <USER> <GROUP>    5776 Jul 17 12:07 index.a81e3c08.js
-rwxr-xr-x 1 <USER> <GROUP>    6811 Jul 17 10:53 index.a886e29f.js
-rwxr-xr-x 1 <USER> <GROUP>    4700 Jul 18 09:39 index.a96728cb.js
-rwxr-xr-x 1 <USER> <GROUP>    5545 Jul 17 10:53 index.a989febc.js
-rwxr-xr-x 1 <USER> <GROUP>    5926 Jul 17 12:07 index.a9b5ee30.js
-rwxr-xr-x 1 <USER> <GROUP>    5148 Jul 18 09:39 index.aa17ab20.js
-rwxr-xr-x 1 <USER> <GROUP>     398 Jul 18 09:39 index.aa95a11f.css
-rwxr-xr-x 1 <USER> <GROUP>    5148 Jul 17 12:07 index.ab3e55f4.js
-rwxr-xr-x 1 <USER> <GROUP>    4595 Jul 18 09:39 index.ab47f103.js
-rwxr-xr-x 1 <USER> <GROUP>   10921 Jul 18 09:39 index.ab61370e.js
-rwxr-xr-x 1 <USER> <GROUP>    7984 Jul 17 12:07 index.ac1a3dae.js
-rwxr-xr-x 1 <USER> <GROUP>    8206 Jul 18 09:39 index.ad6f32a9.js
-rwxr-xr-x 1 <USER> <GROUP>    6281 Jul 18 09:39 index.aea3e4dc.js
-rwxr-xr-x 1 <USER> <GROUP>    2531 Jul 18 09:39 index.b2c5da33.js
-rwxr-xr-x 1 <USER> <GROUP>    2370 Jul 17 10:53 index.b397040e.js
-rwxr-xr-x 1 <USER> <GROUP>    4986 Jul 17 10:53 index.b807d2eb.js
-rwxr-xr-x 1 <USER> <GROUP>    8133 Jul 17 12:07 index.b94c8ab9.js
-rwxr-xr-x 1 <USER> <GROUP>    4595 Jul 17 10:53 index.b9d0c58b.js
-rwxr-xr-x 1 <USER> <GROUP>    8565 Jul 18 09:39 index.bb2b8249.js
-rwxr-xr-x 1 <USER> <GROUP>    7694 Jul 17 10:53 index.bba005ec.js
-rwxr-xr-x 1 <USER> <GROUP>    6704 Jul 17 12:07 index.bc150a8e.js
-rwxr-xr-x 1 <USER> <GROUP>    8213 Jul 17 10:53 index.bca0c44e.js
-rwxr-xr-x 1 <USER> <GROUP>    9647 Jul 17 12:07 index.bd1330c3.js
-rwxr-xr-x 1 <USER> <GROUP>    4924 Jul 17 10:53 index.bded4a28.js
-rwxr-xr-x 1 <USER> <GROUP>     800 Jul 18 09:39 index.be58216a.css
-rwxr-xr-x 1 <USER> <GROUP>   11473 Jul 17 12:07 index.be93bab8.js
-rwxr-xr-x 1 <USER> <GROUP>    5801 Jul 17 10:53 index.becadccb.js
-rwxr-xr-x 1 <USER> <GROUP>    8206 Jul 17 12:07 index.c02c22a0.js
-rwxr-xr-x 1 <USER> <GROUP>    7711 Jul 17 10:53 index.c129e1fd.js
-rwxr-xr-x 1 <USER> <GROUP>     140 Jul 18 09:39 index.c365a18f.css
-rwxr-xr-x 1 <USER> <GROUP>    5828 Jul 17 10:53 index.c39d4ae0.js
-rwxr-xr-x 1 <USER> <GROUP>    9873 Jul 17 10:53 index.c3e1e538.js
-rwxr-xr-x 1 <USER> <GROUP>    6382 Jul 17 12:07 index.c40263ae.js
-rwxr-xr-x 1 <USER> <GROUP>    3698 Jul 17 12:07 index.c68c2179.js
-rwxr-xr-x 1 <USER> <GROUP>    4924 Jul 17 12:07 index.c7f44b94.js
-rwxr-xr-x 1 <USER> <GROUP>    4059 Jul 18 09:39 index.c8a086bf.js
-rwxr-xr-x 1 <USER> <GROUP>    3903 Jul 18 09:39 index.c94d115c.js
-rwxr-xr-x 1 <USER> <GROUP>    3132 Jul 18 09:39 index.cac2b730.js
-rwxr-xr-x 1 <USER> <GROUP>    9647 Jul 17 10:53 index.cada4f9b.js
-rwxr-xr-x 1 <USER> <GROUP>    4059 Jul 17 12:07 index.cc455ba8.js
-rwxr-xr-x 1 <USER> <GROUP>    7757 Jul 17 12:07 index.ccb88d89.js
-rwxr-xr-x 1 <USER> <GROUP>    3701 Jul 18 09:39 index.cdc47d52.js
-rwxr-xr-x 1 <USER> <GROUP>    6165 Jul 18 09:39 index.d2341787.js
-rwxr-xr-x 1 <USER> <GROUP>    5833 Jul 18 09:39 index.d3a2fd35.js
-rwxr-xr-x 1 <USER> <GROUP>   11028 Jul 17 10:53 index.d4322f87.js
-rwxr-xr-x 1 <USER> <GROUP>    6207 Jul 17 10:53 index.d43ba8a1.js
-rwxr-xr-x 1 <USER> <GROUP>     338 Jul 18 09:39 index.d444b5a8.css
-rwxr-xr-x 1 <USER> <GROUP>    5044 Jul 17 10:53 index.d44d012c.js
-rwxr-xr-x 1 <USER> <GROUP>    4569 Jul 18 09:39 index.d5586e0b.js
-rwxr-xr-x 1 <USER> <GROUP>    7336 Jul 17 10:53 index.d5b5c54f.js
-rwxr-xr-x 1 <USER> <GROUP>    5044 Jul 18 09:39 index.d8af8686.js
-rwxr-xr-x 1 <USER> <GROUP>     140 Jul 18 09:39 index.d90d5c5f.css
-rwxr-xr-x 1 <USER> <GROUP>    4553 Jul 17 12:07 index.d9af4e43.js
-rwxr-xr-x 1 <USER> <GROUP>   10921 Jul 17 12:07 index.da49b04b.js
-rwxr-xr-x 1 <USER> <GROUP>    3023 Jul 18 09:39 index.db422412.js
-rwxr-xr-x 1 <USER> <GROUP>    5828 Jul 18 09:39 index.db5d4e82.js
-rwxr-xr-x 1 <USER> <GROUP>     672 Jul 18 09:39 index.db5d52d8.js
-rwxr-xr-x 1 <USER> <GROUP>    5545 Jul 17 12:07 index.dbf98e23.js
-rwxr-xr-x 1 <USER> <GROUP>    3701 Jul 17 12:07 index.dc3445a1.js
-rwxr-xr-x 1 <USER> <GROUP>    4588 Jul 18 09:39 index.dd03d847.js
-rwxr-xr-x 1 <USER> <GROUP>    5786 Jul 17 10:53 index.dd229116.js
-rwxr-xr-x 1 <USER> <GROUP>     117 Jul 18 09:39 index.dd41ece1.css
-rwxr-xr-x 1 <USER> <GROUP>    5048 Jul 18 09:39 index.dd604676.js
-rwxr-xr-x 1 <USER> <GROUP>    3680 Jul 17 10:53 index.de404c77.js
-rwxr-xr-x 1 <USER> <GROUP>    9737 Jul 18 09:39 index.de63a009.js
-rwxr-xr-x 1 <USER> <GROUP>    3680 Jul 17 12:07 index.df2bc846.js
-rwxr-xr-x 1 <USER> <GROUP>    5902 Jul 17 10:53 index.df65be24.js
-rwxr-xr-x 1 <USER> <GROUP>    7865 Jul 18 09:39 index.e0e225e4.js
-rwxr-xr-x 1 <USER> <GROUP>    4710 Jul 18 09:39 index.e2e52549.js
-rwxr-xr-x 1 <USER> <GROUP>    4588 Jul 17 10:53 index.e368e7f0.js
-rwxr-xr-x 1 <USER> <GROUP>    5811 Jul 18 09:39 index.e4b74bc1.js
-rwxr-xr-x 1 <USER> <GROUP>    4982 Jul 17 10:53 index.e4c4fef5.js
-rwxr-xr-x 1 <USER> <GROUP>    1862 Jul 18 09:39 index.e6005b4f.css
-rwxr-xr-x 1 <USER> <GROUP>    3903 Jul 17 12:07 index.e659e96e.js
-rwxr-xr-x 1 <USER> <GROUP>    6738 Jul 17 10:53 index.e87f102e.js
-rwxr-xr-x 1 <USER> <GROUP>    6706 Jul 18 09:39 index.e8db6a6b.js
-rwxr-xr-x 1 <USER> <GROUP>    6207 Jul 17 12:07 index.e9d693eb.js
-rwxr-xr-x 1 <USER> <GROUP>    9931 Jul 17 12:07 index.eb011995.js
-rwxr-xr-x 1 <USER> <GROUP>    3468 Jul 18 09:39 index.ec32818a.js
-rwxr-xr-x 1 <USER> <GROUP>    6207 Jul 18 09:39 index.ec992599.js
-rwxr-xr-x 1 <USER> <GROUP>    6281 Jul 17 12:07 index.ee789ca2.js
-rwxr-xr-x 1 <USER> <GROUP>   10591 Jul 18 09:39 index.f1ffc1f2.js
-rwxr-xr-x 1 <USER> <GROUP>    5384 Jul 18 09:39 index.f332adf8.js
-rwxr-xr-x 1 <USER> <GROUP>    3998 Jul 17 10:53 index.f3edf9af.js
-rwxr-xr-x 1 <USER> <GROUP>    4592 Jul 17 12:07 index.f3f0fc69.js
-rwxr-xr-x 1 <USER> <GROUP>    9872 Jul 18 09:39 index.f4789624.js
-rwxr-xr-x 1 <USER> <GROUP>    8975 Jul 17 12:07 index.f4b66ce2.js
-rwxr-xr-x 1 <USER> <GROUP>    4982 Jul 18 09:39 index.f4c0d8d6.js
-rwxr-xr-x 1 <USER> <GROUP>    4079 Jul 17 12:07 index.f53ac585.js
-rwxr-xr-x 1 <USER> <GROUP>    4742 Jul 18 09:39 index.f5bb60bd.js
-rwxr-xr-x 1 <USER> <GROUP>    6809 Jul 17 12:07 index.f5d25585.js
-rwxr-xr-x 1 <USER> <GROUP>    6633 Jul 17 10:53 index.f6fadd36.js
-rwxr-xr-x 1 <USER> <GROUP>   15285 Jul 18 09:39 index.f753912e.js
-rwxr-xr-x 1 <USER> <GROUP>    6672 Jul 17 12:07 index.f82683e1.js
-rwxr-xr-x 1 <USER> <GROUP>   11028 Jul 18 09:39 index.f8f3c80e.js
-rwxr-xr-x 1 <USER> <GROUP>    5926 Jul 17 10:53 index.f92c70eb.js
-rwxr-xr-x 1 <USER> <GROUP>    6672 Jul 18 09:39 index.fa4d9994.js
-rwxr-xr-x 1 <USER> <GROUP>    9647 Jul 18 09:39 index.fa9fa5ce.js
-rwxr-xr-x 1 <USER> <GROUP>    5828 Jul 17 10:53 index.faa056c2.js
-rwxr-xr-x 1 <USER> <GROUP>    7443 Jul 17 10:53 index.fad48e31.js
-rwxr-xr-x 1 <USER> <GROUP>    5786 Jul 18 09:39 index.fc2fbaf4.js
-rwxr-xr-x 1 <USER> <GROUP>    9873 Jul 17 12:07 index.fced4603.js
-rwxr-xr-x 1 <USER> <GROUP>   10756 Jul 17 12:07 index.fd4e0931.js
-rwxr-xr-x 1 <USER> <GROUP>    4992 Jul 17 12:07 index.fe5f68cd.js
-rwxr-xr-x 1 <USER> <GROUP>    3522 Jul 18 09:39 index.ffb9afdd.js
-rwxr-xr-x 1 <USER> <GROUP>     968 Jul 18 09:39 index.vue_vue_type_script_setup_true_lang.3c07c530.js
-rwxr-xr-x 1 <USER> <GROUP>    2033 Jul 17 12:07 index.vue_vue_type_script_setup_true_lang.6680770c.js
-rwxr-xr-x 1 <USER> <GROUP>    2033 Jul 17 10:53 index.vue_vue_type_script_setup_true_lang.6dafc9ca.js
-rwxr-xr-x 1 <USER> <GROUP>    3877 Jul 17 10:53 index.vue_vue_type_script_setup_true_lang.7b17a651.js
-rwxr-xr-x 1 <USER> <GROUP>    3877 Jul 17 12:07 index.vue_vue_type_script_setup_true_lang.89557ecf.js
-rwxr-xr-x 1 <USER> <GROUP>     920 Jul 18 09:39 index.vue_vue_type_script_setup_true_lang.8dbebfd3.js
-rwxr-xr-x 1 <USER> <GROUP>    3877 Jul 18 09:39 index.vue_vue_type_script_setup_true_lang.96c7f6c0.js
-rwxr-xr-x 1 <USER> <GROUP>    2754 Jul 17 12:07 index.vue_vue_type_script_setup_true_lang.98bb6cee.js
-rwxr-xr-x 1 <USER> <GROUP>    2754 Jul 18 09:39 index.vue_vue_type_script_setup_true_lang.a646d5a6.js
-rwxr-xr-x 1 <USER> <GROUP>    2033 Jul 18 09:39 index.vue_vue_type_script_setup_true_lang.b3a7c772.js
-rwxr-xr-x 1 <USER> <GROUP>    2286 Jul 18 09:39 index.vue_vue_type_script_setup_true_lang.b8ea78d1.js
-rwxr-xr-x 1 <USER> <GROUP>    2754 Jul 17 10:53 index.vue_vue_type_script_setup_true_lang.fe3ffbc3.js
-rwxr-xr-x 1 <USER> <GROUP>   11917 Jul 17 10:53 index.vue_vue_type_style_index_0_lang.07ff00d7.js
-rwxr-xr-x 1 <USER> <GROUP>   11917 Jul 18 09:39 index.vue_vue_type_style_index_0_lang.50778f43.js
-rwxr-xr-x 1 <USER> <GROUP>    4414 Jul 18 09:39 index.vue_vue_type_style_index_0_lang.612c3870.js
-rwxr-xr-x 1 <USER> <GROUP>   11917 Jul 17 12:07 index.vue_vue_type_style_index_0_lang.bed0a5b9.js
-rwxr-xr-x 1 <USER> <GROUP>     809 Jul 18 09:39 index_arrow-right02.3a32185e.js
-rwxr-xr-x 1 <USER> <GROUP>    9503 Jul 17 10:53 information.4ea51bed.js
-rwxr-xr-x 1 <USER> <GROUP>    9503 Jul 17 12:07 information.58bbe351.js
-rwxr-xr-x 1 <USER> <GROUP>    9503 Jul 18 09:39 information.967d541e.js
-rwxr-xr-x 1 <USER> <GROUP>    1482 Jul 17 10:53 interface-config.108f87a8.js
-rwxr-xr-x 1 <USER> <GROUP>    1482 Jul 18 09:39 interface-config.47f5bb2d.js
-rwxr-xr-x 1 <USER> <GROUP>    1482 Jul 17 12:07 interface-config.4bb3e228.js
-rwxr-xr-x 1 <USER> <GROUP>    3494 Jul 17 10:53 interface-config.vue_vue_type_script_setup_true_lang.05df967a.js
-rwxr-xr-x 1 <USER> <GROUP>    3494 Jul 18 09:39 interface-config.vue_vue_type_script_setup_true_lang.0c724f3d.js
-rwxr-xr-x 1 <USER> <GROUP>    3494 Jul 17 12:07 interface-config.vue_vue_type_script_setup_true_lang.789ab092.js
-rwxr-xr-x 1 <USER> <GROUP>     337 Jul 17 10:53 invite.27ab4e8a.js
-rwxr-xr-x 1 <USER> <GROUP>     337 Jul 18 09:39 invite.3ce69be1.js
-rwxr-xr-x 1 <USER> <GROUP>     337 Jul 17 12:07 invite.5311620f.js
-rwxr-xr-x 1 <USER> <GROUP>    4980 Jul 17 12:07 journal.12522181.js
-rwxr-xr-x 1 <USER> <GROUP>    4980 Jul 17 10:53 journal.dec2a4ff.js
-rwxr-xr-x 1 <USER> <GROUP>    4980 Jul 18 09:39 journal.feeecb85.js
-rwxr-xr-x 1 <USER> <GROUP>  252545 Jul 18 09:39 katex.2ad1a25a.js
-rwxr-xr-x 1 <USER> <GROUP>    5377 Jul 18 09:39 keyword_reply.0e16f1a6.js
-rwxr-xr-x 1 <USER> <GROUP>    5377 Jul 17 10:53 keyword_reply.79a0f588.js
-rwxr-xr-x 1 <USER> <GROUP>    5377 Jul 17 12:07 keyword_reply.b35c91f0.js
-rwxr-xr-x 1 <USER> <GROUP>   43923 Jul 18 09:39 layout_bg.de2be1d7.png
-rwxr-xr-x 1 <USER> <GROUP>    1404 Jul 17 10:53 leader-adjust.1a538072.js
-rwxr-xr-x 1 <USER> <GROUP>    1404 Jul 18 09:39 leader-adjust.4b56dfdf.js
-rwxr-xr-x 1 <USER> <GROUP>    1404 Jul 17 12:07 leader-adjust.9847c5fe.js
-rwxr-xr-x 1 <USER> <GROUP>    4774 Jul 17 12:07 leader-adjust.vue_vue_type_script_setup_true_lang.6f6f5f3e.js
-rwxr-xr-x 1 <USER> <GROUP>    4774 Jul 17 10:53 leader-adjust.vue_vue_type_script_setup_true_lang.c8ae9f58.js
-rwxr-xr-x 1 <USER> <GROUP>    4774 Jul 18 09:39 leader-adjust.vue_vue_type_script_setup_true_lang.df0d3eac.js
-rwxr-xr-x 1 <USER> <GROUP>    1867 Jul 17 12:07 link.26a47d33.js
-rwxr-xr-x 1 <USER> <GROUP>    1867 Jul 18 09:39 link.6fca575e.js
-rwxr-xr-x 1 <USER> <GROUP>    1867 Jul 17 10:53 link.88a74d65.js
-rwxr-xr-x 1 <USER> <GROUP>    9186 Jul 18 09:39 linkify-it.11d25e6c.js
-rwxr-xr-x 1 <USER> <GROUP>    7424 Jul 17 10:53 lists.02bedfea.js
-rwxr-xr-x 1 <USER> <GROUP>    7424 Jul 17 12:07 lists.03339071.js
-rwxr-xr-x 1 <USER> <GROUP>    5885 Jul 18 09:39 lists.149fad6d.js
-rwxr-xr-x 1 <USER> <GROUP>    6908 Jul 18 09:39 lists.2ca8a218.js
-rwxr-xr-x 1 <USER> <GROUP>    5885 Jul 17 12:07 lists.32fc3233.js
-rwxr-xr-x 1 <USER> <GROUP>    7099 Jul 17 10:53 lists.3891f8c6.js
-rwxr-xr-x 1 <USER> <GROUP>    7424 Jul 18 09:39 lists.38f0bbbc.js
-rwxr-xr-x 1 <USER> <GROUP>    5885 Jul 17 10:53 lists.44a00a4d.js
-rwxr-xr-x 1 <USER> <GROUP>    7099 Jul 17 12:07 lists.4ea7b398.js
-rwxr-xr-x 1 <USER> <GROUP>    7099 Jul 18 09:39 lists.4ec57ca0.js
-rwxr-xr-x 1 <USER> <GROUP>    7768 Jul 17 10:53 lists.7c87e859.js
-rwxr-xr-x 1 <USER> <GROUP>    8014 Jul 18 09:39 lists.a8fedee6.js
-rwxr-xr-x 1 <USER> <GROUP>    7768 Jul 17 12:07 lists.c12aaadc.js
-rwxr-xr-x 1 <USER> <GROUP>    8014 Jul 17 10:53 lists.c81c71f7.js
-rwxr-xr-x 1 <USER> <GROUP>    8014 Jul 17 12:07 lists.dae56ffb.js
-rwxr-xr-x 1 <USER> <GROUP>    6908 Jul 17 10:53 lists.dd55261e.js
-rwxr-xr-x 1 <USER> <GROUP>    6908 Jul 17 12:07 lists.e8e9250f.js
-rwxr-xr-x 1 <USER> <GROUP>    7768 Jul 18 09:39 lists.f8dceae9.js
-rwxr-xr-x 1 <USER> <GROUP>   23693 Jul 18 09:39 lodash-es.c9433054.js
-rwxr-xr-x 1 <USER> <GROUP>       1 Jul 18 09:39 lodash-unified.d120b703.js
-rwxr-xr-x 1 <USER> <GROUP>   72747 Jul 18 09:39 lodash.9ffd80b1.js
-rwxr-xr-x 1 <USER> <GROUP>    5066 Jul 17 10:53 login.0eff9f26.js
-rwxr-xr-x 1 <USER> <GROUP>    5066 Jul 18 09:39 login.63ede010.js
-rwxr-xr-x 1 <USER> <GROUP>    5066 Jul 17 12:07 login.895cc4f2.js
-rwxr-xr-x 1 <USER> <GROUP>     226 Jul 18 09:39 login.d230bb83.css
-rwxr-xr-x 1 <USER> <GROUP>   59273 Jul 18 09:39 login_bg.2f630cea.png
-rwxr-xr-x 1 <USER> <GROUP>    6764 Jul 18 09:39 login_register.27164d9b.js
-rwxr-xr-x 1 <USER> <GROUP>    6764 Jul 17 12:07 login_register.37dde024.js
-rwxr-xr-x 1 <USER> <GROUP>    6764 Jul 17 10:53 login_register.98d24b82.js
-rwxr-xr-x 1 <USER> <GROUP>    6859 Jul 17 10:53 lowdestributor.4c117382.js
-rwxr-xr-x 1 <USER> <GROUP>    6859 Jul 17 12:07 lowdestributor.8b5ed03e.js
-rwxr-xr-x 1 <USER> <GROUP>    6859 Jul 18 09:39 lowdestributor.e7a3fd80.js
-rwxr-xr-x 1 <USER> <GROUP>    6904 Jul 18 09:39 manage.024a77de.js
-rwxr-xr-x 1 <USER> <GROUP>     674 Jul 18 09:39 manage.1a3f5221.js
-rwxr-xr-x 1 <USER> <GROUP>     354 Jul 17 10:53 manage.23b63204.js
-rwxr-xr-x 1 <USER> <GROUP>    5450 Jul 17 12:07 manage.2f799310.js
-rwxr-xr-x 1 <USER> <GROUP>     499 Jul 17 10:53 manage.31e380e5.js
-rwxr-xr-x 1 <USER> <GROUP>    6901 Jul 17 12:07 manage.45bda660.js
-rwxr-xr-x 1 <USER> <GROUP>     354 Jul 17 12:07 manage.49e1d3fe.js
-rwxr-xr-x 1 <USER> <GROUP>    6904 Jul 17 10:53 manage.4a9e4415.js
-rwxr-xr-x 1 <USER> <GROUP>    6603 Jul 17 10:53 manage.7606f216.js
-rwxr-xr-x 1 <USER> <GROUP>    6904 Jul 17 12:07 manage.8aa332be.js
-rwxr-xr-x 1 <USER> <GROUP>    6901 Jul 17 10:53 manage.8dd6a1fc.js
-rwxr-xr-x 1 <USER> <GROUP>    6603 Jul 17 12:07 manage.9d0adacf.js
-rwxr-xr-x 1 <USER> <GROUP>    6901 Jul 18 09:39 manage.a3f7d69f.js
-rwxr-xr-x 1 <USER> <GROUP>     674 Jul 17 12:07 manage.a6ce37a9.js
-rwxr-xr-x 1 <USER> <GROUP>    6603 Jul 18 09:39 manage.a9e134bc.js
-rwxr-xr-x 1 <USER> <GROUP>     499 Jul 18 09:39 manage.b8febb1d.js
-rwxr-xr-x 1 <USER> <GROUP>    5450 Jul 18 09:39 manage.bce312c8.js
-rwxr-xr-x 1 <USER> <GROUP>     499 Jul 17 12:07 manage.cb2201de.js
-rwxr-xr-x 1 <USER> <GROUP>     674 Jul 17 10:53 manage.cf3da224.js
-rwxr-xr-x 1 <USER> <GROUP>    5450 Jul 17 10:53 manage.ea7039ae.js
-rwxr-xr-x 1 <USER> <GROUP>     354 Jul 18 09:39 manage.fb6980a5.js
-rwxr-xr-x 1 <USER> <GROUP>   96791 Jul 18 09:39 markdown-it.77c882ab.js
-rwxr-xr-x 1 <USER> <GROUP>    4708 Jul 18 09:39 mdurl.f884a969.js
-rwxr-xr-x 1 <USER> <GROUP>    4219 Jul 17 12:07 member-benefits.c1cbff65.js
-rwxr-xr-x 1 <USER> <GROUP>    4219 Jul 17 10:53 member-benefits.d2cbc70a.js
-rwxr-xr-x 1 <USER> <GROUP>    4219 Jul 18 09:39 member-benefits.f2151353.js
-rwxr-xr-x 1 <USER> <GROUP>     918 Jul 18 09:39 member.2f638220.js
-rwxr-xr-x 1 <USER> <GROUP>     918 Jul 17 12:07 member.46e05dce.js
-rwxr-xr-x 1 <USER> <GROUP>     918 Jul 17 10:53 member.b0392e58.js
-rwxr-xr-x 1 <USER> <GROUP>     522 Jul 18 09:39 memoize-one.4ee5c96d.js
-rwxr-xr-x 1 <USER> <GROUP>    2101 Jul 17 10:53 menu-set.28f403e7.js
-rwxr-xr-x 1 <USER> <GROUP>    2101 Jul 18 09:39 menu-set.51d7405b.js
-rwxr-xr-x 1 <USER> <GROUP>    2101 Jul 17 12:07 menu-set.b27f790e.js
-rwxr-xr-x 1 <USER> <GROUP>    3876 Jul 18 09:39 menu-set.vue_vue_type_script_setup_true_lang.05c3c103.js
-rwxr-xr-x 1 <USER> <GROUP>    3876 Jul 17 10:53 menu-set.vue_vue_type_script_setup_true_lang.827c61ec.js
-rwxr-xr-x 1 <USER> <GROUP>    3876 Jul 17 12:07 menu-set.vue_vue_type_script_setup_true_lang.92e41536.js
-rwxr-xr-x 1 <USER> <GROUP>    2851 Jul 17 12:07 menu.1570412d.js
-rwxr-xr-x 1 <USER> <GROUP>     463 Jul 17 12:07 menu.287b4214.js
-rwxr-xr-x 1 <USER> <GROUP>    1192 Jul 18 09:39 menu.28ff9e19.js
-rwxr-xr-x 1 <USER> <GROUP>     463 Jul 18 09:39 menu.672ddb3c.js
-rwxr-xr-x 1 <USER> <GROUP>    2851 Jul 17 10:53 menu.bbc3c01b.js
-rwxr-xr-x 1 <USER> <GROUP>     312 Jul 18 09:39 menu.d0d3fbb7.css
-rwxr-xr-x 1 <USER> <GROUP>     463 Jul 17 10:53 menu.db4422b3.js
-rwxr-xr-x 1 <USER> <GROUP>    2851 Jul 18 09:39 menu.fe876cb2.js
-rwxr-xr-x 1 <USER> <GROUP>     651 Jul 17 10:53 message.73a65137.js
-rwxr-xr-x 1 <USER> <GROUP>     651 Jul 18 09:39 message.8bf3f109.js
-rwxr-xr-x 1 <USER> <GROUP>     651 Jul 17 12:07 message.93de019f.js
-rwxr-xr-x 1 <USER> <GROUP>     356 Jul 18 09:39 mind_map.2544e663.js
-rwxr-xr-x 1 <USER> <GROUP>     356 Jul 17 12:07 mind_map.8a9508db.js
-rwxr-xr-x 1 <USER> <GROUP>     356 Jul 17 10:53 mind_map.9496d665.js
-rwxr-xr-x 1 <USER> <GROUP>  103016 Jul 18 09:39 mobile_example_app.2d20d9cc.png
-rwxr-xr-x 1 <USER> <GROUP>    2050 Jul 17 10:53 model-form.20a37ab4.js
-rwxr-xr-x 1 <USER> <GROUP>    2050 Jul 17 12:07 model-form.4bf12391.js
-rwxr-xr-x 1 <USER> <GROUP>    2050 Jul 18 09:39 model-form.5f039c1a.js
-rwxr-xr-x 1 <USER> <GROUP>   21654 Jul 18 09:39 model-form.vue_vue_type_script_setup_true_lang.3d870934.js
-rwxr-xr-x 1 <USER> <GROUP>   21654 Jul 17 12:07 model-form.vue_vue_type_script_setup_true_lang.97c0e936.js
-rwxr-xr-x 1 <USER> <GROUP>   21654 Jul 17 10:53 model-form.vue_vue_type_script_setup_true_lang.99f6a55f.js
-rwxr-xr-x 1 <USER> <GROUP>    4034 Jul 17 12:07 model-limits.8dac5c19.js
-rwxr-xr-x 1 <USER> <GROUP>    4034 Jul 17 10:53 model-limits.a8e4052b.js
-rwxr-xr-x 1 <USER> <GROUP>    4034 Jul 18 09:39 model-limits.fe578e1c.js
-rwxr-xr-x 1 <USER> <GROUP>    1402 Jul 17 10:53 model-lists.9d10a478.js
-rwxr-xr-x 1 <USER> <GROUP>    1402 Jul 17 12:07 model-lists.9e185bdb.js
-rwxr-xr-x 1 <USER> <GROUP>    1402 Jul 18 09:39 model-lists.ad274d2e.js
-rwxr-xr-x 1 <USER> <GROUP>    3598 Jul 17 12:07 model-lists.vue_vue_type_script_setup_true_lang.00d544cc.js
-rwxr-xr-x 1 <USER> <GROUP>    3598 Jul 18 09:39 model-lists.vue_vue_type_script_setup_true_lang.07a40bf2.js
-rwxr-xr-x 1 <USER> <GROUP>    3598 Jul 17 10:53 model-lists.vue_vue_type_script_setup_true_lang.3e5377bf.js
-rwxr-xr-x 1 <USER> <GROUP>     872 Jul 18 09:39 model.80694e31.js
-rwxr-xr-x 1 <USER> <GROUP>     570 Jul 17 10:53 model.a20b8776.js
-rwxr-xr-x 1 <USER> <GROUP>     872 Jul 17 12:07 model.db0d9d4f.js
-rwxr-xr-x 1 <USER> <GROUP>    1339 Jul 17 10:53 music-item.3e8b3f0b.js
-rwxr-xr-x 1 <USER> <GROUP>    1339 Jul 18 09:39 music-item.84552529.js
-rwxr-xr-x 1 <USER> <GROUP>    1339 Jul 17 12:07 music-item.c5e80e33.js
-rwxr-xr-x 1 <USER> <GROUP>    1536 Jul 18 09:39 music-item.vue_vue_type_script_setup_true_lang.78c4e05a.js
-rwxr-xr-x 1 <USER> <GROUP>    1536 Jul 17 12:07 music-item.vue_vue_type_script_setup_true_lang.df33504b.js
-rwxr-xr-x 1 <USER> <GROUP>    1536 Jul 17 10:53 music-item.vue_vue_type_script_setup_true_lang.f1c47737.js
-rwxr-xr-x 1 <USER> <GROUP>     811 Jul 18 09:39 music.03862596.js
-rwxr-xr-x 1 <USER> <GROUP>    1054 Jul 17 10:53 music.48859603.js
-rwxr-xr-x 1 <USER> <GROUP>    1054 Jul 18 09:39 music.4f0de84d.js
-rwxr-xr-x 1 <USER> <GROUP>     811 Jul 17 12:07 music.ced5bb13.js
-rwxr-xr-x 1 <USER> <GROUP>    1054 Jul 17 12:07 music.d22256da.js
-rwxr-xr-x 1 <USER> <GROUP>     811 Jul 17 10:53 music.f67f45ed.js
-rwxr-xr-x 1 <USER> <GROUP>   14619 Jul 18 09:39 no_perms.a56e95a5.png
-rwxr-xr-x 1 <USER> <GROUP>    3255 Jul 18 09:39 normalize-wheel-es.8aeb3683.js
-rwxr-xr-x 1 <USER> <GROUP>     424 Jul 18 09:39 notice.300c63f9.js
-rwxr-xr-x 1 <USER> <GROUP>     275 Jul 17 10:53 notice.82b940e8.js
-rwxr-xr-x 1 <USER> <GROUP>    4241 Jul 18 09:39 nprogress.28f32054.js
-rwxr-xr-x 1 <USER> <GROUP>     833 Jul 18 09:39 nprogress.f5128a35.css
-rwxr-xr-x 1 <USER> <GROUP>    1367 Jul 17 12:07 num-adjust.3bcf305e.js
-rwxr-xr-x 1 <USER> <GROUP>    1367 Jul 18 09:39 num-adjust.4cbc5a22.js
-rwxr-xr-x 1 <USER> <GROUP>    1367 Jul 17 10:53 num-adjust.f71d15ab.js
-rwxr-xr-x 1 <USER> <GROUP>    2444 Jul 17 12:07 num-adjust.vue_vue_type_script_setup_true_lang.39e5dfff.js
-rwxr-xr-x 1 <USER> <GROUP>    2444 Jul 18 09:39 num-adjust.vue_vue_type_script_setup_true_lang.3e61d7f8.js
-rwxr-xr-x 1 <USER> <GROUP>    2444 Jul 17 10:53 num-adjust.vue_vue_type_script_setup_true_lang.ed130768.js
-rwxr-xr-x 1 <USER> <GROUP>    4128 Jul 17 12:07 oa-attr.097080b9.js
-rwxr-xr-x 1 <USER> <GROUP>    4128 Jul 17 10:53 oa-attr.77c65540.js
-rwxr-xr-x 1 <USER> <GROUP>    4128 Jul 18 09:39 oa-attr.e0030f5a.js
-rwxr-xr-x 1 <USER> <GROUP>    1512 Jul 17 10:53 oa-menu-form-edit.1d511c6c.js
-rwxr-xr-x 1 <USER> <GROUP>    1512 Jul 18 09:39 oa-menu-form-edit.350ffb5c.js
-rwxr-xr-x 1 <USER> <GROUP>    1512 Jul 17 12:07 oa-menu-form-edit.e2a24c3b.js
-rwxr-xr-x 1 <USER> <GROUP>    1337 Jul 17 10:53 oa-menu-form-edit.vue_vue_type_script_setup_true_lang.17f22928.j
s
-rwxr-xr-x 1 <USER> <GROUP>    1337 Jul 17 12:07 oa-menu-form-edit.vue_vue_type_script_setup_true_lang.32fa0de9.j
s
-rwxr-xr-x 1 <USER> <GROUP>    1337 Jul 18 09:39 oa-menu-form-edit.vue_vue_type_script_setup_true_lang.e76704a3.j
s
-rwxr-xr-x 1 <USER> <GROUP>    1403 Jul 17 10:53 oa-menu-form.1d2b45a9.js
-rwxr-xr-x 1 <USER> <GROUP>    1403 Jul 17 12:07 oa-menu-form.bc382578.js
-rwxr-xr-x 1 <USER> <GROUP>    1403 Jul 18 09:39 oa-menu-form.eeeaae02.js
-rwxr-xr-x 1 <USER> <GROUP>    3309 Jul 17 10:53 oa-menu-form.vue_vue_type_script_setup_true_lang.11395e9f.js
-rwxr-xr-x 1 <USER> <GROUP>    3309 Jul 17 12:07 oa-menu-form.vue_vue_type_script_setup_true_lang.962ddb5f.js
-rwxr-xr-x 1 <USER> <GROUP>    3309 Jul 18 09:39 oa-menu-form.vue_vue_type_script_setup_true_lang.ae62ce9b.js
-rwxr-xr-x 1 <USER> <GROUP>     941 Jul 18 09:39 oa-phone.38906974.css
-rwxr-xr-x 1 <USER> <GROUP>    2655 Jul 17 10:53 oa-phone.750da7c4.js
-rwxr-xr-x 1 <USER> <GROUP>    2655 Jul 18 09:39 oa-phone.b387ae64.js
-rwxr-xr-x 1 <USER> <GROUP>    2655 Jul 17 12:07 oa-phone.ed9c6854.js
-rwxr-xr-x 1 <USER> <GROUP>    3412 Jul 18 09:39 open_setting.2bdc512c.js
-rwxr-xr-x 1 <USER> <GROUP>    3412 Jul 17 12:07 open_setting.4ac5fce4.js
-rwxr-xr-x 1 <USER> <GROUP>    3412 Jul 17 10:53 open_setting.6a62e57b.js
-rwxr-xr-x 1 <USER> <GROUP>    2978 Jul 18 09:39 order.663128e6.js
-rwxr-xr-x 1 <USER> <GROUP>    1727 Jul 17 12:07 overflow.17779923.js
-rwxr-xr-x 1 <USER> <GROUP>    1727 Jul 17 10:53 overflow.6573a604.js
-rwxr-xr-x 1 <USER> <GROUP>    1727 Jul 18 09:39 overflow.eee4664e.js
-rwxr-xr-x 1 <USER> <GROUP>     732 Jul 18 09:39 package-pop.68fa8dbb.js
-rwxr-xr-x 1 <USER> <GROUP>    4052 Jul 18 09:39 package-pop.vue_vue_type_script_setup_true_lang.c53b615c.js
-rwxr-xr-x 1 <USER> <GROUP>     451 Jul 18 09:39 pay.2a48e3c5.js
-rwxr-xr-x 1 <USER> <GROUP>     451 Jul 17 12:07 pay.3255d6fd.js
-rwxr-xr-x 1 <USER> <GROUP>     451 Jul 17 10:53 pay.e79876e0.js
-rwxr-xr-x 1 <USER> <GROUP>    3651 Jul 17 10:53 pc.00fa1583.js
-rwxr-xr-x 1 <USER> <GROUP>    4813 Jul 17 10:53 pc.1518629c.js
-rwxr-xr-x 1 <USER> <GROUP>      97 Jul 18 09:39 pc.40b69880.css
-rwxr-xr-x 1 <USER> <GROUP>    3651 Jul 18 09:39 pc.5a712562.js
-rwxr-xr-x 1 <USER> <GROUP>    3651 Jul 17 12:07 pc.7de4cd84.js
-rwxr-xr-x 1 <USER> <GROUP>    4813 Jul 17 12:07 pc.8e1a6c21.js
-rwxr-xr-x 1 <USER> <GROUP>    4813 Jul 18 09:39 pc.90f769fb.js
-rwxr-xr-x 1 <USER> <GROUP>  610552 Jul 18 09:39 pc_example_app.f79bcf6f.png
-rwxr-xr-x 1 <USER> <GROUP>     741 Jul 18 09:39 picker.5059d370.css
-rwxr-xr-x 1 <USER> <GROUP>    4672 Jul 17 10:53 picker.92c54273.js
-rwxr-xr-x 1 <USER> <GROUP>   23513 Jul 17 12:07 picker.cbed8c93.js
-rwxr-xr-x 1 <USER> <GROUP>   23513 Jul 18 09:39 picker.d2575025.js
-rwxr-xr-x 1 <USER> <GROUP>    4672 Jul 17 12:07 picker.d559b1b8.js
-rwxr-xr-x 1 <USER> <GROUP>    4672 Jul 18 09:39 picker.e5310ca1.js
-rwxr-xr-x 1 <USER> <GROUP>    1354 Jul 18 09:39 picker.e629b3b2.css
-rwxr-xr-x 1 <USER> <GROUP>   23513 Jul 17 10:53 picker.fd8af281.js
-rwxr-xr-x 1 <USER> <GROUP>    3163 Jul 17 12:07 picker.vue_vue_type_script_setup_true_lang.088f25ac.js
-rwxr-xr-x 1 <USER> <GROUP>    3163 Jul 18 09:39 picker.vue_vue_type_script_setup_true_lang.346330e7.js
-rwxr-xr-x 1 <USER> <GROUP>    3163 Jul 17 10:53 picker.vue_vue_type_script_setup_true_lang.ec5dd519.js
-rwxr-xr-x 1 <USER> <GROUP>    3662 Jul 18 09:39 pinia.0d658f08.js
-rwxr-xr-x 1 <USER> <GROUP>    1701 Jul 18 09:39 popover_input.80c91fbb.js
-rwxr-xr-x 1 <USER> <GROUP>    5354 Jul 17 10:53 popup.2b0b7a6e.js
-rwxr-xr-x 1 <USER> <GROUP>    5354 Jul 18 09:39 popup.8c7e5ff1.js
-rwxr-xr-x 1 <USER> <GROUP>    5354 Jul 17 12:07 popup.b9a8599f.js
-rwxr-xr-x 1 <USER> <GROUP>      40 Jul 18 09:39 popup.e809cfc7.css
-rwxr-xr-x 1 <USER> <GROUP>     486 Jul 18 09:39 post.8309aa99.js
-rwxr-xr-x 1 <USER> <GROUP>     486 Jul 17 10:53 post.cb49f87c.js
-rwxr-xr-x 1 <USER> <GROUP>     486 Jul 17 12:07 post.d38e20a0.js
-rwxr-xr-x 1 <USER> <GROUP>    2980 Jul 17 10:53 preview-pc.dd836d75.js
-rwxr-xr-x 1 <USER> <GROUP>    2980 Jul 18 09:39 preview-pc.e2df9206.js
-rwxr-xr-x 1 <USER> <GROUP>    2980 Jul 17 12:07 preview-pc.eca3c217.js
-rwxr-xr-x 1 <USER> <GROUP>    4332 Jul 18 09:39 preview-pc.vue_vue_type_script_setup_true_lang.18b6aec9.js
-rwxr-xr-x 1 <USER> <GROUP>    4332 Jul 17 10:53 preview-pc.vue_vue_type_script_setup_true_lang.ba619b60.js
-rwxr-xr-x 1 <USER> <GROUP>    4332 Jul 17 12:07 preview-pc.vue_vue_type_script_setup_true_lang.bfe89720.js
-rwxr-xr-x 1 <USER> <GROUP>    2963 Jul 17 10:53 preview-pop.0abc359c.js
-rwxr-xr-x 1 <USER> <GROUP>    2963 Jul 18 09:39 preview-pop.36a9f960.js
-rwxr-xr-x 1 <USER> <GROUP>    1163 Jul 18 09:39 preview-pop.4f24349c.css
-rwxr-xr-x 1 <USER> <GROUP>    2963 Jul 17 12:07 preview-pop.90c16661.js
-rwxr-xr-x 1 <USER> <GROUP>    2520 Jul 18 09:39 preview.0b03d824.js
-rwxr-xr-x 1 <USER> <GROUP>     948 Jul 18 09:39 preview.0f45adbb.css
-rwxr-xr-x 1 <USER> <GROUP>    6225 Jul 17 12:07 preview.849fd22c.js
-rwxr-xr-x 1 <USER> <GROUP>    6225 Jul 18 09:39 preview.a4792d10.js
-rwxr-xr-x 1 <USER> <GROUP>    6225 Jul 17 10:53 preview.c719fea6.js
-rwxr-xr-x 1 <USER> <GROUP>     252 Jul 18 09:39 preview.c758e1e7.css
-rwxr-xr-x 1 <USER> <GROUP>    2520 Jul 17 12:07 preview.e5fb9574.js
-rwxr-xr-x 1 <USER> <GROUP>    2520 Jul 17 10:53 preview.fd6e9f25.js
-rwxr-xr-x 1 <USER> <GROUP>    3254 Jul 17 12:07 preview.vue_vue_type_script_setup_true_lang.31ba0b90.js
-rwxr-xr-x 1 <USER> <GROUP>    3254 Jul 18 09:39 preview.vue_vue_type_script_setup_true_lang.c1b6a3fb.js
-rwxr-xr-x 1 <USER> <GROUP>    3254 Jul 17 10:53 preview.vue_vue_type_script_setup_true_lang.d334f244.js
-rwxr-xr-x 1 <USER> <GROUP>     439 Jul 18 09:39 problem_category.2a5a5464.js
-rwxr-xr-x 1 <USER> <GROUP>     439 Jul 17 12:07 problem_category.5a92864d.js
-rwxr-xr-x 1 <USER> <GROUP>     439 Jul 17 10:53 problem_category.8bf9355b.js
-rwxr-xr-x 1 <USER> <GROUP>    1138 Jul 18 09:39 prop-setting.44582a43.js
-rwxr-xr-x 1 <USER> <GROUP>     117 Jul 18 09:39 prop-setting.dcb5b78e.css
-rwxr-xr-x 1 <USER> <GROUP>    1939 Jul 17 12:07 prop.10a9ac67.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 17 12:07 prop.1be627ed.js
-rwxr-xr-x 1 <USER> <GROUP>    1939 Jul 17 10:53 prop.1cdb8f22.js
-rwxr-xr-x 1 <USER> <GROUP>    1727 Jul 17 10:53 prop.2160073f.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 17 10:53 prop.5a636986.js
-rwxr-xr-x 1 <USER> <GROUP>    1939 Jul 17 10:53 prop.5cfb22be.js
-rwxr-xr-x 1 <USER> <GROUP>    1939 Jul 18 09:39 prop.5ebd318c.js
-rwxr-xr-x 1 <USER> <GROUP>    1939 Jul 18 09:39 prop.6015932c.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 18 09:39 prop.6332636a.js
-rwxr-xr-x 1 <USER> <GROUP>    1727 Jul 18 09:39 prop.65658335.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 17 10:53 prop.68c01f44.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 18 09:39 prop.68d6727a.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 17 12:07 prop.68eda27c.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 17 10:53 prop.7d445f88.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 17 10:53 prop.82bae511.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 18 09:39 prop.980dccb2.js
-rwxr-xr-x 1 <USER> <GROUP>    1939 Jul 17 12:07 prop.9bc09bfd.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 18 09:39 prop.9e6adda2.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 17 12:07 prop.bc84722e.js
-rwxr-xr-x 1 <USER> <GROUP>    2093 Jul 17 12:07 prop.bfe7b98d.js
-rwxr-xr-x 1 <USER> <GROUP>    1727 Jul 17 12:07 prop.d6644f0c.js
-rwxr-xr-x 1 <USER> <GROUP>    1355 Jul 18 09:39 prop.vue_vue_type_script_setup_true_lang.04824bc4.js
-rwxr-xr-x 1 <USER> <GROUP>    3318 Jul 17 12:07 prop.vue_vue_type_script_setup_true_lang.0f3aad52.js
-rwxr-xr-x 1 <USER> <GROUP>    2565 Jul 17 10:53 prop.vue_vue_type_script_setup_true_lang.10213e96.js
-rwxr-xr-x 1 <USER> <GROUP>    3157 Jul 17 12:07 prop.vue_vue_type_script_setup_true_lang.2bc559a1.js
-rwxr-xr-x 1 <USER> <GROUP>    1355 Jul 17 12:07 prop.vue_vue_type_script_setup_true_lang.2c796037.js
-rwxr-xr-x 1 <USER> <GROUP>    2351 Jul 17 12:07 prop.vue_vue_type_script_setup_true_lang.374cd83f.js
-rwxr-xr-x 1 <USER> <GROUP>    2351 Jul 17 10:53 prop.vue_vue_type_script_setup_true_lang.4f23d5a1.js
-rwxr-xr-x 1 <USER> <GROUP>    2565 Jul 18 09:39 prop.vue_vue_type_script_setup_true_lang.63c291a8.js
-rwxr-xr-x 1 <USER> <GROUP>    2351 Jul 18 09:39 prop.vue_vue_type_script_setup_true_lang.6856d380.js
-rwxr-xr-x 1 <USER> <GROUP>    3318 Jul 17 10:53 prop.vue_vue_type_script_setup_true_lang.7580d6e4.js
-rwxr-xr-x 1 <USER> <GROUP>    1355 Jul 17 10:53 prop.vue_vue_type_script_setup_true_lang.9341c914.js
-rwxr-xr-x 1 <USER> <GROUP>    1355 Jul 17 12:07 prop.vue_vue_type_script_setup_true_lang.a68bc409.js
-rwxr-xr-x 1 <USER> <GROUP>    1355 Jul 17 10:53 prop.vue_vue_type_script_setup_true_lang.aa6159f3.js
-rwxr-xr-x 1 <USER> <GROUP>    7553 Jul 18 09:39 prop.vue_vue_type_script_setup_true_lang.b6638e52.js
-rwxr-xr-x 1 <USER> <GROUP>    7553 Jul 17 12:07 prop.vue_vue_type_script_setup_true_lang.be1f20dd.js
-rwxr-xr-x 1 <USER> <GROUP>    3318 Jul 18 09:39 prop.vue_vue_type_script_setup_true_lang.d4b65d44.js
-rwxr-xr-x 1 <USER> <GROUP>    7553 Jul 17 10:53 prop.vue_vue_type_script_setup_true_lang.d81155e7.js
-rwxr-xr-x 1 <USER> <GROUP>    2565 Jul 17 12:07 prop.vue_vue_type_script_setup_true_lang.dd3be087.js
-rwxr-xr-x 1 <USER> <GROUP>    3157 Jul 18 09:39 prop.vue_vue_type_script_setup_true_lang.e0a0533c.js
-rwxr-xr-x 1 <USER> <GROUP>    3157 Jul 17 10:53 prop.vue_vue_type_script_setup_true_lang.fd49de1c.js
-rwxr-xr-x 1 <USER> <GROUP>    1355 Jul 18 09:39 prop.vue_vue_type_script_setup_true_lang.fe434f3a.js
-rwxr-xr-x 1 <USER> <GROUP>    5025 Jul 18 09:39 protocol.a7425966.js
-rwxr-xr-x 1 <USER> <GROUP>    5025 Jul 17 10:53 protocol.b4d4a53c.js
-rwxr-xr-x 1 <USER> <GROUP>    5025 Jul 17 12:07 protocol.e809f2f6.js
-rwxr-xr-x 1 <USER> <GROUP>    2603 Jul 18 09:39 punycode.d2e2b7b3.js
-rwxr-xr-x 1 <USER> <GROUP>     728 Jul 17 10:53 recharge.1388b6c7.js
-rwxr-xr-x 1 <USER> <GROUP>     728 Jul 17 12:07 recharge.149640d1.js
-rwxr-xr-x 1 <USER> <GROUP>     728 Jul 18 09:39 recharge.725bdd89.js
-rwxr-xr-x 1 <USER> <GROUP>    5790 Jul 17 12:07 recharge_record.09823e3e.js
-rwxr-xr-x 1 <USER> <GROUP>    5790 Jul 18 09:39 recharge_record.7fdef626.js
-rwxr-xr-x 1 <USER> <GROUP>    5790 Jul 17 10:53 recharge_record.dcbd3435.js
-rwxr-xr-x 1 <USER> <GROUP>    5707 Jul 17 10:53 record.04643dea.js
-rwxr-xr-x 1 <USER> <GROUP>    4817 Jul 17 12:07 record.09eaf368.js
-rwxr-xr-x 1 <USER> <GROUP>    6553 Jul 17 12:07 record.0cabba14.js
-rwxr-xr-x 1 <USER> <GROUP>    6553 Jul 18 09:39 record.0cb560af.js
-rwxr-xr-x 1 <USER> <GROUP>    4817 Jul 17 10:53 record.12a26507.js
-rwxr-xr-x 1 <USER> <GROUP>    4675 Jul 17 12:07 record.1b9ea884.js
-rwxr-xr-x 1 <USER> <GROUP>    5111 Jul 17 12:07 record.2441545b.js
-rwxr-xr-x 1 <USER> <GROUP>    5111 Jul 17 10:53 record.26881eb8.js
-rwxr-xr-x 1 <USER> <GROUP>    4675 Jul 18 09:39 record.32c34092.js
-rwxr-xr-x 1 <USER> <GROUP>    5707 Jul 17 12:07 record.55e89df5.js
-rwxr-xr-x 1 <USER> <GROUP>    6542 Jul 18 09:39 record.5b21d6d9.js
-rwxr-xr-x 1 <USER> <GROUP>    4817 Jul 18 09:39 record.64ac0399.js
-rwxr-xr-x 1 <USER> <GROUP>    6542 Jul 17 12:07 record.76cb393f.js
-rwxr-xr-x 1 <USER> <GROUP>    6553 Jul 17 10:53 record.789c6d22.js
-rwxr-xr-x 1 <USER> <GROUP>    5707 Jul 18 09:39 record.8d8f8ce9.js
-rwxr-xr-x 1 <USER> <GROUP>    5111 Jul 18 09:39 record.bcd9eef8.js
-rwxr-xr-x 1 <USER> <GROUP>    6542 Jul 17 10:53 record.c1243f3e.js
-rwxr-xr-x 1 <USER> <GROUP>    4675 Jul 17 10:53 record.e5284b19.js
-rwxr-xr-x 1 <USER> <GROUP>     709 Jul 17 10:53 redeem_code.15ba7ebe.js
-rwxr-xr-x 1 <USER> <GROUP>     709 Jul 17 12:07 redeem_code.435c43a0.js
-rwxr-xr-x 1 <USER> <GROUP>     709 Jul 18 09:39 redeem_code.4c26c4d7.js
-rwxr-xr-x 1 <USER> <GROUP>    1369 Jul 17 12:07 refund-log.3b640e45.js
-rwxr-xr-x 1 <USER> <GROUP>    1369 Jul 18 09:39 refund-log.3e3acc49.js
-rwxr-xr-x 1 <USER> <GROUP>    1369 Jul 17 10:53 refund-log.bd5edced.js
-rwxr-xr-x 1 <USER> <GROUP>    1737 Jul 18 09:39 refund-log.vue_vue_type_script_setup_true_lang.0d2e04b6.js
-rwxr-xr-x 1 <USER> <GROUP>    1737 Jul 17 10:53 refund-log.vue_vue_type_script_setup_true_lang.cdf7bed2.js
-rwxr-xr-x 1 <USER> <GROUP>    1737 Jul 17 12:07 refund-log.vue_vue_type_script_setup_true_lang.db1b396b.js
-rwxr-xr-x 1 <USER> <GROUP>    7692 Jul 17 12:07 refund_record.5af2123d.js
-rwxr-xr-x 1 <USER> <GROUP>    7692 Jul 17 10:53 refund_record.b9d7a4d4.js
-rwxr-xr-x 1 <USER> <GROUP>    7692 Jul 18 09:39 refund_record.f0e573e9.js
-rwxr-xr-x 1 <USER> <GROUP>    3878 Jul 17 10:53 register_bonus.0b76adff.js
-rwxr-xr-x 1 <USER> <GROUP>    3878 Jul 18 09:39 register_bonus.12d40333.js
-rwxr-xr-x 1 <USER> <GROUP>    3878 Jul 17 12:07 register_bonus.7a9d87b1.js
-rwxr-xr-x 1 <USER> <GROUP>    1437 Jul 17 12:07 relations-add.2cac891c.js
-rwxr-xr-x 1 <USER> <GROUP>    1437 Jul 17 10:53 relations-add.5d517367.js
-rwxr-xr-x 1 <USER> <GROUP>    1437 Jul 18 09:39 relations-add.73f2b332.js
-rwxr-xr-x 1 <USER> <GROUP>    3363 Jul 17 12:07 relations-add.vue_vue_type_script_setup_true_lang.6d44e77f.js
-rwxr-xr-x 1 <USER> <GROUP>    3363 Jul 18 09:39 relations-add.vue_vue_type_script_setup_true_lang.a2cd5274.js
-rwxr-xr-x 1 <USER> <GROUP>    3363 Jul 17 10:53 relations-add.vue_vue_type_script_setup_true_lang.f6b6dd4c.js
-rwxr-xr-x 1 <USER> <GROUP>     732 Jul 18 09:39 reply-popup.d2e52da7.js
-rwxr-xr-x 1 <USER> <GROUP>     495 Jul 18 09:39 reply-popup.vue_vue_type_script_setup_true_lang.bd09f1b9.js
-rwxr-xr-x 1 <USER> <GROUP>    1083 Jul 18 09:39 reply.66bf5c2a.js
-rwxr-xr-x 1 <USER> <GROUP>     720 Jul 18 09:39 reply.b64fb019.js
-rwxr-xr-x 1 <USER> <GROUP>     720 Jul 18 09:39 reply.ece703f8.js
-rwxr-xr-x 1 <USER> <GROUP>     720 Jul 18 09:39 reply.f697592d.js
-rwxr-xr-x 1 <USER> <GROUP>     801 Jul 18 09:39 reply.vue_vue_type_script_setup_true_lang.1615e10e.js
-rwxr-xr-x 1 <USER> <GROUP>     801 Jul 18 09:39 reply.vue_vue_type_script_setup_true_lang.9f376018.js
-rwxr-xr-x 1 <USER> <GROUP>     689 Jul 18 09:39 reply.vue_vue_type_script_setup_true_lang.e9b7e5ed.js
-rwxr-xr-x 1 <USER> <GROUP>     801 Jul 18 09:39 reply.vue_vue_type_script_setup_true_lang.f2b8dd5b.js
-rwxr-xr-x 1 <USER> <GROUP>    1089 Jul 18 09:39 replyPop.17d78a69.js
-rwxr-xr-x 1 <USER> <GROUP>    1089 Jul 18 09:39 replyPop.33de7f29.js
-rwxr-xr-x 1 <USER> <GROUP>    1089 Jul 18 09:39 replyPop.3562bee9.js
-rwxr-xr-x 1 <USER> <GROUP>     569 Jul 18 09:39 replyPop.vue_vue_type_script_setup_true_lang.28d5a735.js
-rwxr-xr-x 1 <USER> <GROUP>     692 Jul 18 09:39 replyPop.vue_vue_type_script_setup_true_lang.b89881a0.js
-rwxr-xr-x 1 <USER> <GROUP>     692 Jul 18 09:39 replyPop.vue_vue_type_script_setup_true_lang.d27d98e8.js
-rwxr-xr-x 1 <USER> <GROUP>    1410 Jul 17 12:07 resetPasswordPop.47ad1a4a.js
-rwxr-xr-x 1 <USER> <GROUP>    1410 Jul 17 10:53 resetPasswordPop.a1e9ed2c.js
-rwxr-xr-x 1 <USER> <GROUP>    1410 Jul 18 09:39 resetPasswordPop.e2f02724.js
-rwxr-xr-x 1 <USER> <GROUP>     965 Jul 17 12:07 resetPasswordPop.vue_vue_type_script_setup_true_lang.4b231682.js
-rwxr-xr-x 1 <USER> <GROUP>     965 Jul 18 09:39 resetPasswordPop.vue_vue_type_script_setup_true_lang.6a653ff8.js
-rwxr-xr-x 1 <USER> <GROUP>     965 Jul 17 10:53 resetPasswordPop.vue_vue_type_script_setup_true_lang.ab60ca00.js
-rwxr-xr-x 1 <USER> <GROUP>    4523 Jul 18 09:39 resize-detector.4e96b72b.js
-rwxr-xr-x 1 <USER> <GROUP>    2560 Jul 17 12:07 rich_text.0f196bc5.js
-rwxr-xr-x 1 <USER> <GROUP>    2560 Jul 17 10:53 rich_text.5ad500d3.js
-rwxr-xr-x 1 <USER> <GROUP>    2560 Jul 18 09:39 rich_text.6c7dad49.js
-rwxr-xr-x 1 <USER> <GROUP>    1052 Jul 18 09:39 robot_square.3d1c55a7.js
-rwxr-xr-x 1 <USER> <GROUP>    1052 Jul 17 12:07 robot_square.a244265c.js
-rwxr-xr-x 1 <USER> <GROUP>    1052 Jul 17 10:53 robot_square.b78fda18.js
-rwxr-xr-x 1 <USER> <GROUP>     393 Jul 18 09:39 role.33b14dda.js
-rwxr-xr-x 1 <USER> <GROUP>     393 Jul 17 10:53 role.af81e76f.js
-rwxr-xr-x 1 <USER> <GROUP>     393 Jul 17 12:07 role.e952dfd1.js
-rwxr-xr-x 1 <USER> <GROUP>     670 Jul 18 09:39 search-config.4d4df2d8.js
-rwxr-xr-x 1 <USER> <GROUP>    2678 Jul 18 09:39 search-config.vue_vue_type_script_setup_true_lang.1bf47cc3.js
-rwxr-xr-x 1 <USER> <GROUP>     486 Jul 17 12:07 search.002c9ce3.js
-rwxr-xr-x 1 <USER> <GROUP>     486 Jul 17 10:53 search.ac75a52c.js
-rwxr-xr-x 1 <USER> <GROUP>     486 Jul 18 09:39 search.d8a4fd25.js
-rwxr-xr-x 1 <USER> <GROUP>     658 Jul 17 10:53 session.03662f92.js
-rwxr-xr-x 1 <USER> <GROUP>     658 Jul 18 09:39 session.4d8f2ece.js
-rwxr-xr-x 1 <USER> <GROUP>    9450 Jul 18 09:39 session.vue_vue_type_script_setup_true_lang.3250a87b.js
-rwxr-xr-x 1 <USER> <GROUP>    9451 Jul 17 10:53 session.vue_vue_type_script_setup_true_lang.4a90bf57.js
-rwxr-xr-x 1 <USER> <GROUP>    3247 Jul 18 09:39 setting.07a30d90.js
-rwxr-xr-x 1 <USER> <GROUP>    5660 Jul 18 09:39 setting.082952d4.js
-rwxr-xr-x 1 <USER> <GROUP>    6203 Jul 17 12:07 setting.1dc4072f.js
-rwxr-xr-x 1 <USER> <GROUP>    3355 Jul 17 10:53 setting.2efe8cd1.js
-rwxr-xr-x 1 <USER> <GROUP>    3247 Jul 17 10:53 setting.38ea107d.js
-rwxr-xr-x 1 <USER> <GROUP>    2859 Jul 18 09:39 setting.3e6f4ead.js
-rwxr-xr-x 1 <USER> <GROUP>    3355 Jul 18 09:39 setting.53bd3d6d.js
-rwxr-xr-x 1 <USER> <GROUP>    5281 Jul 17 12:07 setting.577fd4bd.js
-rwxr-xr-x 1 <USER> <GROUP>    5660 Jul 17 12:07 setting.5daf0527.js
-rwxr-xr-x 1 <USER> <GROUP>    5600 Jul 17 10:53 setting.74638f2e.js
-rwxr-xr-x 1 <USER> <GROUP>    3372 Jul 18 09:39 setting.7cdd53e7.js
-rwxr-xr-x 1 <USER> <GROUP>    7401 Jul 17 10:53 setting.86010018.js
-rwxr-xr-x 1 <USER> <GROUP>    3355 Jul 17 12:07 setting.9d5a4b8b.js
-rwxr-xr-x 1 <USER> <GROUP>    5281 Jul 18 09:39 setting.9d7ea3cb.js
-rwxr-xr-x 1 <USER> <GROUP>    3247 Jul 17 12:07 setting.a58e14d3.js
-rwxr-xr-x 1 <USER> <GROUP>    7401 Jul 17 12:07 setting.ad225274.js
-rwxr-xr-x 1 <USER> <GROUP>    6203 Jul 17 10:53 setting.ae56bdb0.js
-rwxr-xr-x 1 <USER> <GROUP>    6203 Jul 18 09:39 setting.af9168e7.js
-rwxr-xr-x 1 <USER> <GROUP>    2859 Jul 17 10:53 setting.b9fc4d9e.js
-rwxr-xr-x 1 <USER> <GROUP>    2859 Jul 17 12:07 setting.d55f1a23.js
-rwxr-xr-x 1 <USER> <GROUP>    5660 Jul 17 10:53 setting.ddf22447.js
-rwxr-xr-x 1 <USER> <GROUP>    5281 Jul 17 10:53 setting.e498890e.js
-rwxr-xr-x 1 <USER> <GROUP>    7401 Jul 18 09:39 setting.f2c35967.js
-rwxr-xr-x 1 <USER> <GROUP>    3372 Jul 17 12:07 setting.f755bbc1.js
-rwxr-xr-x 1 <USER> <GROUP>    5600 Jul 17 12:07 setting.fcc034c4.js
-rwxr-xr-x 1 <USER> <GROUP>    3372 Jul 17 10:53 setting.fdcbb8a0.js
-rwxr-xr-x 1 <USER> <GROUP>    5600 Jul 18 09:39 setting.ff4172ac.js
-rwxr-xr-x 1 <USER> <GROUP>    3680 Jul 17 10:53 setup.10b2c3d2.js
-rwxr-xr-x 1 <USER> <GROUP>    5068 Jul 18 09:39 setup.2e3485bc.js
-rwxr-xr-x 1 <USER> <GROUP>    5068 Jul 17 10:53 setup.56efc53b.js
-rwxr-xr-x 1 <USER> <GROUP>    5068 Jul 17 12:07 setup.5a7b98fb.js
-rwxr-xr-x 1 <USER> <GROUP>    3680 Jul 18 09:39 setup.5a9a513f.js
-rwxr-xr-x 1 <USER> <GROUP>    3680 Jul 17 12:07 setup.6a1d57d7.js
-rwxr-xr-x 1 <USER> <GROUP>     334 Jul 17 12:07 share.1ce7d91d.js
-rwxr-xr-x 1 <USER> <GROUP>     334 Jul 17 10:53 share.2b54abb0.js
-rwxr-xr-x 1 <USER> <GROUP>     334 Jul 18 09:39 share.df7d2e6d.js
-rwxr-xr-x 1 <USER> <GROUP>     331 Jul 18 09:39 sign.8eec08b8.js
-rwxr-xr-x 1 <USER> <GROUP>     331 Jul 17 10:53 sign.bdda4de6.js
-rwxr-xr-x 1 <USER> <GROUP>     331 Jul 17 12:07 sign.cd5e7a96.js
-rwxr-xr-x 1 <USER> <GROUP>   44912 Jul 18 09:39 sortablejs.98edf555.js
-rwxr-xr-x 1 <USER> <GROUP>    1066 Jul 17 10:53 sticker.4bb15cdb.js
-rwxr-xr-x 1 <USER> <GROUP>    1066 Jul 18 09:39 sticker.ce7b6cc0.js
-rwxr-xr-x 1 <USER> <GROUP>    1066 Jul 17 12:07 sticker.e3f6c5f1.js
-rwxr-xr-x 1 <USER> <GROUP>    4494 Jul 17 10:53 study_data.33b28000.js
-rwxr-xr-x 1 <USER> <GROUP>    4494 Jul 18 09:39 study_data.5027a088.js
-rwxr-xr-x 1 <USER> <GROUP>    4494 Jul 17 12:07 study_data.7163f2a9.js
-rwxr-xr-x 1 <USER> <GROUP>    4352 Jul 18 09:39 tabbar.196588f9.js
-rwxr-xr-x 1 <USER> <GROUP>    4352 Jul 17 12:07 tabbar.23f8de31.js
-rwxr-xr-x 1 <USER> <GROUP>    4352 Jul 17 10:53 tabbar.e3eafe08.js
-rwxr-xr-x 1 <USER> <GROUP>     693 Jul 18 09:39 tabbar.f8f8835c.css
-rwxr-xr-x 1 <USER> <GROUP>    1743 Jul 17 10:53 trainDataPop.259cddec.js
-rwxr-xr-x 1 <USER> <GROUP>    1743 Jul 18 09:39 trainDataPop.d981e1af.js
-rwxr-xr-x 1 <USER> <GROUP>    1743 Jul 17 12:07 trainDataPop.ff28d787.js
-rwxr-xr-x 1 <USER> <GROUP>    3683 Jul 17 12:07 trainDataPop.vue_vue_type_script_setup_true_lang.21a3967d.js
-rwxr-xr-x 1 <USER> <GROUP>    3683 Jul 17 10:53 trainDataPop.vue_vue_type_script_setup_true_lang.4159e283.js
-rwxr-xr-x 1 <USER> <GROUP>    3683 Jul 18 09:39 trainDataPop.vue_vue_type_script_setup_true_lang.9b8bda18.js
-rwxr-xr-x 1 <USER> <GROUP>    1405 Jul 17 10:53 transfer-cate.dfea0c32.js
-rwxr-xr-x 1 <USER> <GROUP>    1405 Jul 18 09:39 transfer-cate.f6fb86c8.js
-rwxr-xr-x 1 <USER> <GROUP>    1405 Jul 17 12:07 transfer-cate.fcc9bccd.js
-rwxr-xr-x 1 <USER> <GROUP>    1818 Jul 18 09:39 transfer-cate.vue_vue_type_script_setup_true_lang.3293db74.js
-rwxr-xr-x 1 <USER> <GROUP>    1818 Jul 17 12:07 transfer-cate.vue_vue_type_script_setup_true_lang.c521d2d2.js
-rwxr-xr-x 1 <USER> <GROUP>    1818 Jul 17 10:53 transfer-cate.vue_vue_type_script_setup_true_lang.ecf35807.js
-rwxr-xr-x 1 <USER> <GROUP>    1398 Jul 17 10:53 transfer.111c7f7f.js
-rwxr-xr-x 1 <USER> <GROUP>    1398 Jul 18 09:39 transfer.25af269e.js
-rwxr-xr-x 1 <USER> <GROUP>    1398 Jul 17 12:07 transfer.8603900c.js
-rwxr-xr-x 1 <USER> <GROUP>    1531 Jul 18 09:39 transfer.vue_vue_type_script_setup_true_lang.016ae390.js
-rwxr-xr-x 1 <USER> <GROUP>    1531 Jul 17 10:53 transfer.vue_vue_type_script_setup_true_lang.5baf80ef.js
-rwxr-xr-x 1 <USER> <GROUP>    1531 Jul 17 12:07 transfer.vue_vue_type_script_setup_true_lang.e2953fa0.js
-rwxr-xr-x 1 <USER> <GROUP>    1275 Jul 18 09:39 tslib.60310f1a.js
-rwxr-xr-x 1 <USER> <GROUP>     462 Jul 17 10:53 type.03be0ef4.js
-rwxr-xr-x 1 <USER> <GROUP>     462 Jul 18 09:39 type.11d29cfb.js
-rwxr-xr-x 1 <USER> <GROUP>     462 Jul 17 12:07 type.9dd2ec82.js
-rwxr-xr-x 1 <USER> <GROUP>    2614 Jul 18 09:39 uc.micro.5b97656f.js
-rwxr-xr-x 1 <USER> <GROUP>    2589 Jul 17 12:07 upload.db3d6074.js
-rwxr-xr-x 1 <USER> <GROUP>    2589 Jul 18 09:39 upload.ed156379.js
-rwxr-xr-x 1 <USER> <GROUP>    2589 Jul 17 10:53 upload.fd6757d8.js
-rwxr-xr-x 1 <USER> <GROUP>     413 Jul 17 10:53 useDictOptions.3465db6e.js
-rwxr-xr-x 1 <USER> <GROUP>     413 Jul 17 12:07 useDictOptions.3c465cd7.js
-rwxr-xr-x 1 <USER> <GROUP>     413 Jul 18 09:39 useDictOptions.5794546c.js
-rwxr-xr-x 1 <USER> <GROUP>     222 Jul 18 09:39 useLockFn.b2f69334.js
-rwxr-xr-x 1 <USER> <GROUP>     210 Jul 18 09:39 useMenu.4471f007.js
-rwxr-xr-x 1 <USER> <GROUP>    2126 Jul 17 12:07 useMenuOa.63299769.js
-rwxr-xr-x 1 <USER> <GROUP>    2126 Jul 17 10:53 useMenuOa.ec9eabbf.js
-rwxr-xr-x 1 <USER> <GROUP>    2126 Jul 18 09:39 useMenuOa.fb54baed.js
-rwxr-xr-x 1 <USER> <GROUP>    1154 Jul 18 09:39 usePaging.b48cb079.js
-rwxr-xr-x 1 <USER> <GROUP>     346 Jul 17 12:07 user.185314bf.js
-rwxr-xr-x 1 <USER> <GROUP>     346 Jul 17 10:53 user.ad4d354d.js
-rwxr-xr-x 1 <USER> <GROUP>     346 Jul 18 09:39 user.e66d06ee.js
-rwxr-xr-x 1 <USER> <GROUP>     816 Jul 17 12:07 video.173cbb12.js
-rwxr-xr-x 1 <USER> <GROUP>     816 Jul 17 10:53 video.3f161597.js
-rwxr-xr-x 1 <USER> <GROUP>     816 Jul 18 09:39 video.60bb6680.js
-rwxr-xr-x 1 <USER> <GROUP>    1390 Jul 17 12:07 vip-adjust.026e275c.js
-rwxr-xr-x 1 <USER> <GROUP>      54 Jul 18 09:39 vip-adjust.6ead1170.css
-rwxr-xr-x 1 <USER> <GROUP>    1390 Jul 18 09:39 vip-adjust.75e7a1fa.js
-rwxr-xr-x 1 <USER> <GROUP>    1390 Jul 17 10:53 vip-adjust.e4f4b650.js
-rwxr-xr-x 1 <USER> <GROUP>    2593 Jul 18 09:39 vip-adjust.vue_vue_type_style_index_0_lang.163ce50e.js
-rwxr-xr-x 1 <USER> <GROUP>    2593 Jul 17 10:53 vip-adjust.vue_vue_type_style_index_0_lang.25c9e3b8.js
-rwxr-xr-x 1 <USER> <GROUP>    2593 Jul 17 12:07 vip-adjust.vue_vue_type_style_index_0_lang.809306e0.js
-rwxr-xr-x 1 <USER> <GROUP>    1398 Jul 17 10:53 vip-record.5415fbc0.js
-rwxr-xr-x 1 <USER> <GROUP>    1398 Jul 17 12:07 vip-record.c4c25584.js
-rwxr-xr-x 1 <USER> <GROUP>    1398 Jul 18 09:39 vip-record.ca5dc033.js
-rwxr-xr-x 1 <USER> <GROUP>    1457 Jul 17 10:53 vip-record.vue_vue_type_script_setup_true_lang.00d726ee.js
-rwxr-xr-x 1 <USER> <GROUP>    1457 Jul 18 09:39 vip-record.vue_vue_type_script_setup_true_lang.176f8908.js
-rwxr-xr-x 1 <USER> <GROUP>    1457 Jul 17 12:07 vip-record.vue_vue_type_script_setup_true_lang.b6bb54d1.js
-rwxr-xr-x 1 <USER> <GROUP>     463 Jul 18 09:39 vue-clipboard3.ba321cef.js
-rwxr-xr-x 1 <USER> <GROUP>      25 Jul 18 09:39 vue-demi.b3a9cad9.js
-rwxr-xr-x 1 <USER> <GROUP>   11987 Jul 18 09:39 vue-drag-resize.527c6620.js
-rwxr-xr-x 1 <USER> <GROUP>     758 Jul 18 09:39 vue-drag-resize.ccfde60c.css
-rwxr-xr-x 1 <USER> <GROUP>    5235 Jul 18 09:39 vue-echarts.c133b66a.js
-rwxr-xr-x 1 <USER> <GROUP>   23002 Jul 18 09:39 vue-router.919c7bec.js
-rwxr-xr-x 1 <USER> <GROUP>    4122 Jul 18 09:39 vue.35cf47e9.js
-rwxr-xr-x 1 <USER> <GROUP>  383837 Jul 18 09:39 vue3-video-play.632beab5.js
-rwxr-xr-x 1 <USER> <GROUP>   27747 Jul 18 09:39 vue3-video-play.f54f0aa1.css
-rwxr-xr-x 1 <USER> <GROUP>   51104 Jul 18 09:39 vuedraggable.2019ddfd.js
-rwxr-xr-x 1 <USER> <GROUP>     305 Jul 17 10:53 weapp.107dc309.js
-rwxr-xr-x 1 <USER> <GROUP>     305 Jul 17 12:07 weapp.7d7897ea.js
-rwxr-xr-x 1 <USER> <GROUP>   10319 Jul 18 09:39 weapp.882c6308.js
-rwxr-xr-x 1 <USER> <GROUP>   10319 Jul 17 10:53 weapp.91f1b70f.js
-rwxr-xr-x 1 <USER> <GROUP>   10319 Jul 17 12:07 weapp.ae7d8f76.js
-rwxr-xr-x 1 <USER> <GROUP>     305 Jul 18 09:39 weapp.cf53eb37.js
-rwxr-xr-x 1 <USER> <GROUP>    4992 Jul 17 12:07 weapp_upload.1a5c2a42.js
-rwxr-xr-x 1 <USER> <GROUP>    4992 Jul 18 09:39 weapp_upload.24e1c3c9.js
-rwxr-xr-x 1 <USER> <GROUP>    4992 Jul 17 10:53 weapp_upload.e954e231.js
-rwxr-xr-x 1 <USER> <GROUP>     510 Jul 17 12:07 website.0454e8bc.js
-rwxr-xr-x 1 <USER> <GROUP>     510 Jul 18 09:39 website.ba6c3390.js
-rwxr-xr-x 1 <USER> <GROUP>     510 Jul 17 10:53 website.c5e7aad6.js
-rwxr-xr-x 1 <USER> <GROUP>    5100 Jul 18 09:39 withdraw.a1c55820.js
-rwxr-xr-x 1 <USER> <GROUP>    5100 Jul 17 10:53 withdraw.d9f52c26.js
-rwxr-xr-x 1 <USER> <GROUP>    5100 Jul 17 12:07 withdraw.eaf0f4f1.js
-rwxr-xr-x 1 <USER> <GROUP>     328 Jul 17 12:07 works.19f1b3dd.js
-rwxr-xr-x 1 <USER> <GROUP>     328 Jul 17 10:53 works.b3570680.js
-rwxr-xr-x 1 <USER> <GROUP>     328 Jul 18 09:39 works.e471e724.js
-rwxr-xr-x 1 <USER> <GROUP>    1044 Jul 18 09:39 wx_oa.2cc00d3a.js
-rwxr-xr-x 1 <USER> <GROUP>    1044 Jul 17 10:53 wx_oa.6c49a778.js
-rwxr-xr-x 1 <USER> <GROUP>    1044 Jul 17 12:07 wx_oa.c7b7c59e.js
-rwxr-xr-x 1 <USER> <GROUP>  192953 Jul 18 09:39 zrender.3eba8991.js
[root@iZ2ze776l6a0t8ydszrzbbZ ai]#
```

---



---

